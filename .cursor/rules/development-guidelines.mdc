---
description:
globs:
alwaysApply: true
---
# 项目开发规范指南

## 技术栈规范
- React 18 + TypeScript
- UI 框架：Ant Design
- Hooks 库：ahooks
- 组件测试：Storybook
- 单元测试：Jest

## 项目结构规范

### 1. 数据层 (packages/data)
- 存放工具函数和类型定义
- 所有方法必须编写测试用例，放在 `__test__` 目录
- 类型定义文件使用 `.d.ts` 后缀
- 工具函数使用 `.ts` 后缀

### 2. 请求层 (packages/request)
- 所有 API 请求定义
- 按模块组织请求文件
- 统一错误处理
- 请求拦截器配置

### 3. UI 组件 (packages/storm-web/src/components)
- 使用函数式组件
- 必须编写 Storybook 测试，放在 `packages/storm-web/src/stories`
- 组件命名使用 PascalCase
- 组件文件使用 `.tsx` 后缀
- 样式文件`styled-components`

### 4. 页面组件 (packages/storm-web/src/app/pages)
- 使用函数式组件
- 按功能模块组织
- 使用 Ant Design 组件库
- 复杂逻辑抽离到 hooks

## 开发规范

### 1. 组件开发
- 使用函数式组件
- 使用 TypeScript 类型定义
- 使用 ahooks 提供的 hooks
- 组件必须编写 Storybook 文档
- 复杂组件需要编写单元测试

### 2. 状态管理
- 使用 React Context 或 Redux
- 状态逻辑抽离到 hooks
- 避免过度使用全局状态

### 3. 样式管理
- 优先使用 Ant Design 组件
- 自定义样式使用 styled-components
- 遵循 BEM 命名规范
- 响应式设计使用 Ant Design 的栅格系统

### 4. 代码质量
- 使用 ESLint 和 Prettier
- 提交前进行代码格式化
- 遵循 TypeScript 严格模式
- 编写必要的注释和文档

### 5. 依赖管理
- 禁止使用未经批准的第三方库
- 新依赖需要团队审核
- 定期更新依赖版本
- 使用 package.json 锁定版本

### 6. 测试规范
- 组件测试使用 Storybook
- 工具函数测试使用 Jest
- 测试文件放在 `__test__` 目录
- 保持测试覆盖率

### 7. 性能优化
- 使用 React.memo 优化渲染
- 合理使用 useMemo 和 useCallback
- 避免不必要的重渲染
- 使用 React.lazy 进行代码分割

### 8. 错误处理
- 使用 ErrorBoundary 捕获错误
- 统一的错误提示
- 合理的错误日志
- 优雅的降级处理

## 文件命名规范
- 组件文件：pascal-case.tsx
- 工具函数：camel-case.ts
- 类型定义：pascal-case.d.ts
- 样式文件：pascal-case.ts
- 测试文件：pascal-case.test.ts
- Storybook 文件：pascal-case.stories.tsx

## 代码提交规范
- 使用 Conventional Commits
- 提交前进行代码检查
- 保持提交信息清晰
- 避免大文件提交
