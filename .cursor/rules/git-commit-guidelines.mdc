---
alwaysApply: true
---

# Git Commit Message 生成规则

## 📜 基于 Conventional Commits 规范

**本规则严格遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范**

### 标准格式

```
<type>[optional scope]: <description>
```

### 项目扩展格式（支持emoji和任务ID）

```
<type>(<scope>): <emoji> <description> #<taskId>
```

### 破坏性变更标识

```
<type>(<scope>)!: <emoji> <description> #<taskId>
```

或在footer中使用：

```
BREAKING CHANGE: <description>
```

## 🎯 Conventional Commits Type & Emoji 映射表

| Type       | Emoji | Conventional定义    | 使用场景                                 |
| ---------- | ----- | ------------------- | ---------------------------------------- |
| `feat`     | 🎉    | 新功能(feature)     | 添加新特性、新组件、新页面               |
| `fix`      | 🐛    | 修复(bug fix)       | Bug修复、问题解决                        |
| `docs`     | 📚    | 文档(documentation) | 仅文档更改、README更新、注释添加         |
| `style`    | 💄    | 格式化(formatting)  | 不影响代码含义的更改(空格、格式化、分号) |
| `refactor` | ♻️    | 重构                | 既不修复bug也不添加新功能的代码更改      |
| `perf`     | ⚡️   | 性能(performance)   | 提高性能的代码更改                       |
| `test`     | ✅    | 测试                | 添加缺失测试或修正现有测试               |
| `build`    | 👷    | 构建系统            | 影响构建系统或外部依赖的更改             |
| `ci`       | 🔧    | 持续集成            | CI配置文件和脚本的更改                   |
| `chore`    | 🔨    | 其他                | 其他不修改src或test文件的更改            |
| `revert`   | ⏪    | 回滚                | 回滚之前的提交                           |

## 🚨 破坏性变更 (BREAKING CHANGES)

**标识方法：**

1. **类型后加感叹号：** `feat!:`, `fix!:`
2. **Footer中使用：** `BREAKING CHANGE: description`

## 🏷️ 常用 Scope 建议

基于项目结构的scope建议：

- `dashboard` - 仪表板相关
- `components` - 组件相关
- `data` - 数据层相关
- `api` - 接口相关
- `types` - 类型定义相关
- `config` - 配置相关
- `auth` - 认证相关

## 📏 Description 规则

- **语言：** 使用中文
- **长度：** 不超过50个字符
- **格式：** 动词 + 对象 + 简要说明
- **动词：** 添加、修复、更新、优化、重构、调整

## 🔢 任务ID格式

如果提供分支信息，按以下规则解析：

从分支名中提取 `#任务ID`，优先匹配以下模式（按顺序）：

1. `xxx/#12345` → `/#(\d+)`
2. `xxx/#12345/xxx` → `/#(\d+)/`
3. `xxx-#12345-xxx` → `-#(\d+)-`
4. `xxx-12345-xxx`（带数字但不带#）→ `-(\d{4,6})-`
5. fallback: 无法匹配 → 无任务ID

## 📖 使用示例

### 基本示例

```bash
feat(dashboard): 🎉 添加智能调度仪表板组件
fix(auth): 🐛 修复登录验证失败问题
docs(readme): 📚 更新安装指南
style(components): 💄 格式化组件代码缩进
perf(api): ⚡️ 优化用户列表查询性能
```

### 包含任务ID

```bash
feat(dashboard): 🎉 添加智能调度仪表板组件 #1234
fix(auth): 🐛 修复登录验证失败问题 #5678
```

### 破坏性变更

```bash
feat(api)!: 🎉 重构用户认证API #1234

BREAKING CHANGE: 认证API从v1升级到v2版本，需要更新客户端代码
```

### 回滚提交

```bash
revert: ⏪ 回滚 feat(user): 添加用户头像功能

This reverts commit 1234567.
```

## 🚀 AI生成指南

### 用户请求格式建议

**基础请求：**

```
请生成commit message：添加新的仪表板功能
```

**指定类型：**

```
请生成feat类型的commit message：添加用户头像上传功能
```

**包含分支信息：**

```
请生成commit message：
- 意图：修复登录bug
- 分支：ycq-f-1234-v001
- 主要变更：修改auth组件
```

**破坏性变更：**

```
请生成commit message：这是一个破坏性变更，重构了API接口
```

### AI应该

1. 根据用户描述选择合适的Conventional Commits类型
2. 添加对应的emoji
3. 使用中文编写description
4. 如果用户提供分支信息，解析任务ID
5. 识别并标记破坏性变更
6. 遵循字符长度限制

### 生成格式

```
<type>(<scope>): <emoji> <description> [#taskId]
```

如果是破坏性变更，添加 `!` 或 `BREAKING CHANGE:` footer。
