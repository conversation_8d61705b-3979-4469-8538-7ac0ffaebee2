name: CI - Build & Test

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      data_package_changed: ${{ steps.filter.outputs.data_package }}
      request_package_changed: ${{ steps.filter.outputs.request_package }}
      components_package_changed: ${{ steps.filter.outputs.components_package }}
      storm_web_changed: ${{ steps.filter.outputs.storm_web }}
      storm_app_changed: ${{ steps.filter.outputs.storm_app }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check package changes
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            data_package:
              - 'packages/data/**'
              - 'packages/data/package.json'
            request_package:
              - 'packages/request/**'
              - 'packages/request/package.json'
            components_package:
              - 'packages/components/**'
              - 'packages/components/package.json'
            storm_web:
              - 'packages/storm-web/**'
            storm_app:
              - 'packages/storm-app/**'

  code-quality-and-test:
    needs: [changes]
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js with Yarn cache
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache yarn and node_modules
        uses: actions/cache@v4
        id: yarn-cache
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            **/node_modules
            **/.vite
            **/dist
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}-${{ hashFiles('**/vite.config.ts') }}
          restore-keys: |
            ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}-
            ${{ runner.os }}-yarn-

      - name: Install dependencies
        run: yarn install --frozen-lockfile --prefer-offline

      - name: Run code quality checks
        run: |
          echo "🔍 Running lint check..."
          yarn lint &
          LINT_PID=$!

          echo "🧹 Cleaning old TypeScript build cache..."
          find . -name "*.tsbuildinfo" -delete
          rm -rf **/dist

          echo "🔍 Running TypeScript type check (clean build)..."
          # 清缓存后再执行构建命令
          yarn build:tsc &
          TSC_PID=$!

          # Wait for both processes to complete
          wait $LINT_PID
          LINT_EXIT_CODE=$?

          wait $TSC_PID
          TSC_EXIT_CODE=$?

          # Check if both passed
          if [ $LINT_EXIT_CODE -ne 0 ]; then
            echo "❌ Lint check failed"
            exit $LINT_EXIT_CODE
          fi

          if [ $TSC_EXIT_CODE -ne 0 ]; then
            echo "❌ TypeScript type check failed"
            exit $TSC_EXIT_CODE
          fi

          echo "✅ All code quality checks passed"

      - name: Run tests for changed packages
        if: |
          needs.changes.outputs.data_package_changed == 'true' ||
          needs.changes.outputs.request_package_changed == 'true' ||
          needs.changes.outputs.components_package_changed == 'true'
        run: |
          echo "🧪 Starting package tests..."

          PIDS=()

          if [ "${{ needs.changes.outputs.data_package_changed }}" == "true" ]; then
            echo "🧪 Running tests for @waterdesk/data"
            yarn workspace @waterdesk/data run jest &
            PIDS+=($!)
          fi

          if [ "${{ needs.changes.outputs.request_package_changed }}" == "true" ]; then
            echo "🧪 Running tests for @waterdesk/request"
            yarn workspace @waterdesk/request run jest &
            PIDS+=($!)
          fi

          # Wait for all test processes to complete
          for PID in "${PIDS[@]}"; do
            wait $PID
            EXIT_CODE=$?
            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Package test failed"
              exit $EXIT_CODE
            fi
          done

          echo "✅ All package tests passed"

  build:
    needs: [changes, code-quality-and-test]
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'pull_request' &&
      (needs.changes.outputs.data_package_changed == 'true' ||
       needs.changes.outputs.request_package_changed == 'true' ||
       needs.changes.outputs.components_package_changed == 'true' ||
       needs.changes.outputs.storm_web_changed == 'true' ||
       needs.changes.outputs.storm_app_changed == 'true')
    strategy:
      matrix:
        include:
          - app: 'storm-web'
            build_cmd: 'build:scheduling'
          - app: 'storm-app'
            build_cmd: 'build-app'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js with Yarn cache
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache yarn and node_modules
        uses: actions/cache@v4
        id: yarn-cache
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            **/node_modules
            **/.vite
            **/dist
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}-${{ hashFiles('**/vite.config.ts') }}
          restore-keys: |
            ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}-
            ${{ runner.os }}-yarn-

      - name: Install dependencies
        run: yarn install --frozen-lockfile --prefer-offline

      - name: Build storm-web
        if: |
          matrix.app == 'storm-web' && (
            needs.changes.outputs.data_package_changed == 'true' ||
            needs.changes.outputs.request_package_changed == 'true' ||
            needs.changes.outputs.components_package_changed == 'true' ||
            needs.changes.outputs.storm_web_changed == 'true'
          )
        run: |
          export NODE_OPTIONS="--max-old-space-size=8192"
          yarn ${{ matrix.build_cmd }}

      - name: Build storm-app
        if: |
          matrix.app == 'storm-app' && (
            needs.changes.outputs.data_package_changed == 'true' ||
            needs.changes.outputs.request_package_changed == 'true' ||
            needs.changes.outputs.components_package_changed == 'true' ||
            needs.changes.outputs.storm_app_changed == 'true'
          )
        run: |
          export NODE_OPTIONS="--max-old-space-size=8192"
          yarn ${{ matrix.build_cmd }}
