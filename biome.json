{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"includes": ["./packages/*/src/**/*.{ts,tsx,js,jsx,json}", "./packages/*/*.{js,ts}", "./packages/*/.storybook/*.{ts,tsx,js,jsx,json}", "!**/dist/**", "!**/node_modules/**", "!**/storybook-static/**", "!./packages/*/src/assets/**", "!**/iconfont/**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "multiline", "bracketSameLine": false, "bracketSpacing": true, "expand": "auto", "useEditorconfig": true}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always", "arrowParentheses": "always"}, "globals": ["jest", "describe", "it", "test", "expect", "beforeEach", "after<PERSON>ach", "beforeAll", "afterAll"]}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true, "useValidAnchor": "off", "noAutofocus": "off", "useKeyWithClickEvents": "off", "useKeyWithMouseEvents": "off", "noStaticElementInteractions": "off", "noSvgWithoutTitle": "off", "useSemanticElements": "off", "noLabelWithoutControl": "off", "useFocusableInteractive": "off"}, "complexity": {"recommended": true, "noUselessFragments": "off", "noBannedTypes": "off", "noUselessSwitchCase": "off", "noStaticOnlyClass": "off"}, "correctness": {"recommended": true, "noUnusedVariables": "off", "noUndeclaredVariables": "off", "useExhaustiveDependencies": "off", "noUnusedFunctionParameters": "off"}, "security": {"recommended": true, "noDangerouslySetInnerHtml": "off", "noGlobalEval": "off"}, "style": {"recommended": true, "noDefaultExport": "off", "useBlockStatements": "off", "noNonNullAssertion": "off", "useLiteralEnumMembers": "off", "useImportType": "off", "useNodejsImportProtocol": "off"}, "suspicious": {"recommended": true, "noConsole": "off", "noEmptyBlockStatements": "off", "noExplicitAny": "off", "noImplicitAnyLet": "off", "noTemplateCurlyInString": "off", "noArrayIndexKey": "off"}, "performance": {"recommended": true, "noAccumulatingSpread": "off"}}}}