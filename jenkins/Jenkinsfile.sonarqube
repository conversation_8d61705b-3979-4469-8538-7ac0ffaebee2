node {
    stage('SCM') {
        checkout scm
    }

    stage('Install Dependencies') {
        nodejs(nodeJSInstallationName: '20.19.2') {
            sh 'yarn install --registry=https://registry.npmmirror.com'
        }
    }

    stage('Run Tests and Generate Coverage') {
        nodejs(nodeJSInstallationName: '20.19.2') {
            sh 'yarn coverage'
        }
    }

    stage('SonarQube Analysis') {
        // 获取 SonarQube Scanner 的路径
        def scannerHome = tool 'sonarqube-scanner-test'

        // 获取最新一次 Git 提交的日期，格式为 YYYYMMDD
        def commitDate = sh(script: "git log -1 --format=%cd --date=format:'%Y%m%d'", returnStdout: true).trim()

        // 获取当前 Git 分支名称
        def branchName = sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()

        // 获取当前 Git 提交的短哈希值
        def shortCommit = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()

        // 生成符合格式的版本号
        def version = "${commitDate}.${branchName}.${shortCommit}"

        // 使用 SonarQube 环境变量并执行扫描，同时设置项目版本号
        withSonarQubeEnv() {
            sh "${scannerHome}/bin/sonar-scanner -Dsonar.projectVersion=${version}"
        }
    }
}
