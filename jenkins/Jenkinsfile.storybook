pipeline {
    agent any

    environment {
        GIT_CREDENTIALS_ID = 'githubSSH'  // 你的凭证ID
        BRANCH = 'main'
        REPO_URL = '**************:WaterDesk/storm-web.git'
        TARGET_DIRS = '/var/frontend/storybook'
        IGNORE_FILES = 'global-config.js'
        BUILD_DIR = 'packages/storm-web/storybook-static'  // 构建输出目录
    }

    stages {
        stage('Pull') {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(credentialsId: GIT_CREDENTIALS_ID, keyFileVariable: 'SSH_KEY')]) {
                        if (fileExists('.git')) {
                            def remoteExists = sh(
                                script: "git remote -v | grep origin || echo 'no-remote'",
                                returnStdout: true
                            ).trim()

                            if (remoteExists == 'no-remote') {
                                echo 'Remote origin not configured. Adding remote...'
                                sh "git remote add origin ${REPO_URL}"
                            }
                            // 如果项目存在，进入目录并执行 git pull
                            echo 'Repository exists, pulling latest changes...'
                            sh "git checkout ${BRANCH}"
                            sh "git pull origin ${BRANCH}"
                        } else {
                            echo 'Cloning repository...'
                            // 如果项目不存在，执行 git clone
                            git url: REPO_URL, branch: BRANCH, credentialsId: GIT_CREDENTIALS_ID
                        }
                    }
                }
            }
        }

        stage('Install') {
            steps {
                nodejs(nodeJSInstallationName: '20.19.2') {
                    sh 'yarn install --registry=https://registry.npmmirror.com'
                }
            }
        }

        stage('Build') {
            steps {
                nodejs(nodeJSInstallationName: '20.19.2') {
                    sh 'yarn build-storybook'
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    TARGET_DIRS.split(' ').each { dir ->
                        sh """
                        if [ ! -d ${dir} ]; then
                            echo '目标目录 ${dir} 不存在，正在创建...'
                            mkdir -p ${dir}
                        fi

                        echo 'remove old files , and ignore ${IGNORE_FILES}...'
                        find ${dir} -type f ! -name '${IGNORE_FILES}' -exec rm -f {} \\;

                        echo 'Copying build files to ${dir}...'
                        cd ${BUILD_DIR}
                        find . -type f ! -name '${IGNORE_FILES}' -exec cp --parents {} ${dir} \\;
                        """
                    }
                }
            }
        }
    }

    post {
        success {
            echo 'Build completed successfully!'
        }
        failure {
            echo 'Build failed!'
        }
    }
}
