pipeline {
    agent any

    environment {
        GIT_CREDENTIALS_ID = 'githubSSH'  // 你的凭证ID
        BRANCH = 'main'
        REPO_URL = '**************:WaterDesk/storm-web.git'
    }

    stages {
        stage('Pull') {
            steps {
                script {
                    withCredentials([sshUserPrivateKey(credentialsId: GIT_CREDENTIALS_ID, keyFileVariable: 'SSH_KEY')]) {
                        if (fileExists('.git')) {
                            def remoteExists = sh(
                                script: "git remote -v | grep origin || echo 'no-remote'",
                                returnStdout: true
                            ).trim()

                            if (remoteExists == 'no-remote') {
                                echo 'Remote origin not configured. Adding remote...'
                                sh "git remote add origin ${REPO_URL}"
                            }
                            // 如果项目存在，进入目录并执行 git pull
                            echo 'Repository exists, pulling latest changes...'
                            sh "git checkout ${BRANCH}"
                            sh "git pull origin ${BRANCH}"
                        } else {
                            echo 'Cloning repository...'
                            // 如果项目不存在，执行 git clone
                            git url: REPO_URL, branch: BR<PERSON>CH, credentialsId: GIT_CREDENTIALS_ID
                        }
                    }
                }
            }
        }

        stage('Install') {
            steps {
                nodejs(nodeJSInstallationName: '20.19.2') {
                    sh 'yarn install --registry=https://registry.npmmirror.com'
                }
            }
        }

        stage('Test') {
            steps {
                nodejs(nodeJSInstallationName: '20.19.2') {
                    sh 'yarn jest'
                }
            }
        }
    }

    post {
        success {
            echo 'Test completed successfully!'
        }
        failure {
            echo 'Test failed!'
        }
    }
}
