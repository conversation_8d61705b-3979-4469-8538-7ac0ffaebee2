/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Preview } from '@storybook/react';
import { ConfigProvider } from 'antd';
import { ThemeProvider } from 'styled-components';
import { getAlgorithm, seedToken, type Theme } from '../src/theme';

/**
 * 根据主题类型生成完整的主题令牌
 * @param themeType - 主题类型 ('light' | 'dark')
 * @returns 完整的主题令牌对象
 */
const generateThemeToken = (themeType: Theme) => {
  const algorithm = getAlgorithm(themeType);
  // 如果是数组，取第一个算法；否则直接使用
  const alg = Array.isArray(algorithm) ? algorithm[0] : algorithm;
  return alg(seedToken);
};

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    docs: {
      toc: true,
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'dark',
          value: '#1f1f1f',
        },
        {
          name: 'blue',
          value: '#f0f8ff',
        },
      ],
    },
  },
  globalTypes: {
    theme: {
      description: 'Global theme for components',
      defaultValue: 'light',
      toolbar: {
        title: 'Theme',
        icon: 'paintbrush',
        items: [
          { value: 'light', title: 'Light' },
          { value: 'dark', title: 'Dark' },
        ],
        dynamicTitle: true,
      },
    },
    platform: {
      description: 'Platform for components',
      defaultValue: 'web',
      toolbar: {
        title: 'Platform',
        icon: 'mobile',
        items: [
          { value: 'web', title: 'Web' },
          { value: 'mobile', title: 'Mobile' },
        ],
        dynamicTitle: true,
      },
    },
  },
  decorators: [
    (Story, context) => {
      const { theme: selectedTheme } = context.globals;
      const isDark = selectedTheme === 'dark';
      const themeType: Theme = isDark ? 'dark' : 'light';

      // 使用我们的主题系统生成主题令牌
      const styledTheme = generateThemeToken(themeType);
      const antdAlgorithm = getAlgorithm(themeType);

      return (
        <ConfigProvider
          theme={{
            algorithm: antdAlgorithm,
            token: styledTheme,
          }}
        >
          <ThemeProvider theme={styledTheme}>
            <div
              style={{
                backgroundColor: styledTheme.colorBgContainer || '#ffffff',
                color: styledTheme.colorText || '#000000',
                minHeight: '100vh',
                padding: '20px',
              }}
            >
              <Story />
            </div>
          </ThemeProvider>
        </ConfigProvider>
      );
    },
  ],
};

export default preview;
