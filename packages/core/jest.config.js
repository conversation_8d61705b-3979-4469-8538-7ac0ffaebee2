/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'jsdom',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{test,spec}.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],

  // 忽略的文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/stories/',
    '/.storybook/',
  ],

  // 模块解析配置
  moduleDirectories: ['node_modules', '<rootDir>', '../../node_modules'],
  moduleNameMapper: {
    // 处理样式文件
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    // 处理图片文件
    '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
    // 处理字体文件
    '\\.(woff|woff2|eot|ttf|otf)$': '<rootDir>/__mocks__/fileMock.js',
    // 处理其他文件
    '\\.(docx|pdf|mp3|wav|mp4|webm)$': '<rootDir>/__mocks__/fileMock.js',
  },

  // 转换配置
  transform: {
    '^.+\\.tsx?$': ['ts-jest'],
    '^.+\\.jsx?$': 'babel-jest',
  },

  // 文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],

  // 覆盖率配置
  collectCoverage: false, // 默认不收集覆盖率，使用 test:coverage 命令收集
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/__tests__/**',
    '!src/**/stories/**',
    '!src/**/.storybook/**',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: '<rootDir>/coverage',
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },

  // 测试超时时间
  testTimeout: 10000,

  // 清除 Mock
  clearMocks: true,
  restoreMocks: true,

  // 详细输出
  verbose: true,

  // 根目录
  rootDir: '.',

  // 模块路径映射（用于处理 monorepo 中的包）
  modulePaths: ['<rootDir>/src'],

  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json',
      diagnostics: {
        warnOnly: true,
      },
    },
  },
};
