{"name": "@waterdesk/core", "version": "3.1.0", "description": "Waterdesk Core Library", "author": "wangchen <<EMAIL>>", "homepage": "https://github.com/WaterDesk/storm-web#readme", "license": "ISC", "directories": {"test": "__tests__"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./*": "./src/*"}, "publishConfig": {"registry": "https://registry.npmmirror.com/"}, "repository": {"type": "git", "url": "git+https://github.com/WaterDesk/storm-web.git"}, "scripts": {"build": "yarn build:tsc", "build:tsc": "tsc --build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "prettier:write": "find ./src ./stories ./.storybook -type f -name '*.ts' -o -name '*.tsx' 2>/dev/null | xargs prettier --write || prettier --write ./src", "lint:fix": "find ./src ./stories ./.storybook -type f -name '*.ts' -o -name '*.tsx' 2>/dev/null | xargs eslint --fix --max-warnings 0 || eslint --fix './src/**/*.{ts,tsx}' --max-warnings 0", "prettier": "find ./src ./stories ./.storybook -type f -name '*.ts' -o -name '*.tsx' 2>/dev/null | xargs prettier --check || prettier --check ./src", "lint": "find ./src ./stories ./.storybook -type f -name '*.ts' -o -name '*.tsx' 2>/dev/null | xargs eslint --max-warnings 0 || eslint './src/**/*.{ts,tsx}' --max-warnings 0", "clean": "rm -rf dist", "storybook": "storybook dev -p 6006 --ci", "build-storybook": "storybook build", "storybook:web": "storybook dev -p 6006 --ci --stories './stories/web/**/*.stories.@(js|jsx|ts|tsx|mdx)'", "storybook:mobile": "storybook dev -p 6007 --ci --stories './stories/mobile/**/*.stories.@(js|jsx|ts|tsx|mdx)'"}, "bugs": {"url": "https://github.com/WaterDesk/storm-web/issues"}, "dependencies": {"@waterdesk/data": "*", "@waterdesk/request": "*"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0", "antd": ">=5.0.0", "styled-components": ">=5.0.0", "@ant-design/icons": ">=5.0.0"}, "devDependencies": {}}