/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

describe('Jest 配置测试', () => {
  it('应该能够运行基本的测试', () => {
    expect(1 + 1).toBe(2);
  });

  it('应该能够使用 Jest Mock', () => {
    const mockFn = jest.fn();
    mockFn('test');
    expect(mockFn).toHaveBeenCalledWith('test');
  });

  it('应该能够使用 console Mock', () => {
    console.log('test');
    expect(console.log).toHaveBeenCalledWith('test');
  });
});
