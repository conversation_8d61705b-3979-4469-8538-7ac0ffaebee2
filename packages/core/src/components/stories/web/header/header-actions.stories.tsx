/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BellOutlined, SettingOutlined } from '@ant-design/icons';
import type { Meta, StoryObj } from '@storybook/react';
import {
  HeaderActions,
  HelpDropdown,
  UserDropdown,
} from '@waterdesk/core/components/web';
import { Badge, Button } from 'antd';

const meta: Meta<typeof HeaderActions> = {
  title: 'Web/Header/HeaderActions',
  component: HeaderActions,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'radio',
      options: ['small', 'middle', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <>
        <HelpDropdown />
        <UserDropdown
          user={{
            name: '张三',
            avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1',
          }}
        />
      </>
    ),
  },
};

export const WithCustomActions: Story = {
  args: {
    children: (
      <>
        <Badge
          count={5}
          size="small"
        >
          <Button
            type="link"
            size="large"
          >
            <BellOutlined style={{ fontSize: '20px' }} />
          </Button>
        </Badge>
        <Button
          type="link"
          size="large"
        >
          <SettingOutlined style={{ fontSize: '20px' }} />
        </Button>
        <HelpDropdown />
        <UserDropdown
          user={{
            name: '李四',
          }}
        />
      </>
    ),
  },
};

export const OnlyHelp: Story = {
  args: {
    children: <HelpDropdown />,
  },
};

export const OnlyUser: Story = {
  args: {
    children: (
      <UserDropdown
        user={{
          name: '王五',
          avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2',
        }}
      />
    ),
  },
};
