/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { HeaderLogo } from '@waterdesk/core/components/web';

const meta: Meta<typeof HeaderLogo> = {
  title: 'Web/Header/HeaderLogo',
  component: HeaderLogo,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    src: {
      control: 'text',
    },
    alt: {
      control: 'text',
    },
    height: {
      control: 'text',
    },
    title: {
      control: 'text',
    },
    showTitle: {
      control: 'boolean',
    },
    onClick: {
      action: 'clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 使用一个简单的 SVG logo 作为示例
const exampleLogo = `data:image/svg+xml;base64,${btoa(`
<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="50" fill="#1890ff" rx="8"/>
  <text x="100" y="30" fill="white" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">LOGO</text>
</svg>
`)}`;

export const Default: Story = {
  args: {
    src: exampleLogo,
    alt: 'System Logo',
    title: '慧水科技',
  },
};

export const OnlyImage: Story = {
  args: {
    src: exampleLogo,
    alt: 'System Logo',
    showTitle: false,
  },
};

export const OnlyTitle: Story = {
  args: {
    title: '水务调度系统',
    showTitle: true,
  },
};

export const CustomHeight: Story = {
  args: {
    src: exampleLogo,
    alt: 'System Logo',
    title: '慧水科技',
    height: '60px',
  },
};

export const LongTitle: Story = {
  args: {
    src: exampleLogo,
    alt: 'System Logo',
    title: '智能水务调度管理系统',
    height: '40px',
  },
};

export const WithClickHandler: Story = {
  args: {
    src: exampleLogo,
    alt: 'System Logo',
    title: '慧水科技',
    onClick: () => {
      alert('Logo clicked!');
    },
  },
};
