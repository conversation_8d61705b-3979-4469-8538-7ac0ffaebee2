/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { HeaderMenu } from '@waterdesk/core/components/web';

const meta: Meta<typeof HeaderMenu> = {
  title: 'Web/Header/HeaderMenu',
  component: HeaderMenu,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    mode: {
      control: 'radio',
      options: ['horizontal', 'vertical', 'inline'],
    },
    selectedKeys: {
      control: 'object',
    },
    items: {
      control: 'object',
    },
    onClick: {
      action: 'menu clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const menuItems = [
  {
    key: 'home',
    label: '首页',
  },
  {
    key: 'monitoring',
    label: '监控',
  },
  {
    key: 'alerts',
    label: '警告',
  },
  {
    key: 'analytics',
    label: '分析',
    children: [
      {
        key: 'reports',
        label: '报表',
      },
      {
        key: 'charts',
        label: '图表',
      },
    ],
  },
  {
    key: 'settings',
    label: '设置',
  },
];

export const Default: Story = {
  args: {
    items: menuItems,
    selectedKeys: ['home'],
  },
};

export const Vertical: Story = {
  args: {
    items: menuItems,
    selectedKeys: ['monitoring'],
    mode: 'vertical',
  },
};

export const WithSubMenu: Story = {
  args: {
    items: menuItems,
    selectedKeys: ['reports'],
  },
};

export const Empty: Story = {
  args: {
    items: [],
  },
};
