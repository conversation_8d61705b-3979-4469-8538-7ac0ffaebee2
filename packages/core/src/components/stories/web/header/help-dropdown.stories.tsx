/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InfoCircleOutlined } from '@ant-design/icons';
import type { Meta, StoryObj } from '@storybook/react';
import { HelpDropdown } from '@waterdesk/core/components/web';

const meta: Meta<typeof HelpDropdown> = {
  title: 'Web/Header/HelpDropdown',
  component: HelpDropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    buttonType: {
      control: 'radio',
      options: ['link', 'text', 'default', 'primary', 'dashed'],
    },
    buttonSize: {
      control: 'radio',
      options: ['small', 'middle', 'large'],
    },
    iconSize: {
      control: 'text',
    },
    onMenuClick: {
      action: 'menu clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const CustomItems: Story = {
  args: {
    items: [
      {
        key: 'quickStart',
        label: '快速开始',
      },
      {
        key: 'tutorial',
        label: '操作教程',
      },
      {
        key: 'api',
        label: 'API 文档',
      },
      {
        type: 'divider',
      },
      {
        key: 'feedback',
        label: '意见反馈',
      },
      {
        key: 'contact',
        label: '联系我们',
      },
    ],
  },
};

export const CustomIcon: Story = {
  args: {
    icon: <InfoCircleOutlined style={{ fontSize: '20px' }} />,
  },
};

export const LargeIcon: Story = {
  args: {
    iconSize: '24px',
  },
};

export const TextButton: Story = {
  args: {
    buttonType: 'text',
  },
};

export const PrimaryButton: Story = {
  args: {
    buttonType: 'primary',
    buttonSize: 'middle',
  },
};
