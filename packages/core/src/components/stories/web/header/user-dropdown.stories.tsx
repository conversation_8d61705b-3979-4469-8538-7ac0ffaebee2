/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { UserDropdown } from '@waterdesk/core/components/web';

const meta: Meta<typeof UserDropdown> = {
  title: 'Web/Header/UserDropdown',
  component: UserDropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    avatarSize: {
      control: 'radio',
      options: ['small', 'default', 'large'],
    },
    showName: {
      control: 'boolean',
    },
    user: {
      control: 'object',
    },
    items: {
      control: 'object',
    },
    onMenuClick: {
      action: 'menu clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    user: {
      name: '张三',
      avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1',
    },
    showName: true,
  },
};

export const WithoutAvatar: Story = {
  args: {
    user: {
      name: '李四',
    },
    showName: true,
  },
};

export const OnlyAvatar: Story = {
  args: {
    user: {
      avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2',
    },
  },
};

export const LongName: Story = {
  args: {
    user: {
      name: '这是一个很长的用户名称用来测试文本截断',
      avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=3',
    },
    showName: true,
  },
};

export const LargeAvatar: Story = {
  args: {
    user: {
      name: '王五',
      avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=4',
    },
    avatarSize: 'large',
    showName: true,
  },
};

export const CustomMenuItems: Story = {
  args: {
    user: {
      name: '赵六',
      avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=5',
      role: '系统管理员',
    },
    showName: true,
    items: [
      {
        key: 'profile',
        label: '个人资料',
      },
      {
        key: 'account',
        label: '账户设置',
      },
      {
        key: 'security',
        label: '安全设置',
      },
      {
        type: 'divider',
      },
      {
        key: 'help',
        label: '帮助中心',
      },
      {
        type: 'divider',
      },
      {
        key: 'logout',
        label: '退出登录',
        danger: true,
      },
    ],
  },
};

export const TextButton: Story = {
  args: {
    user: {
      name: '钱七',
    },
    showName: true,
  },
};
