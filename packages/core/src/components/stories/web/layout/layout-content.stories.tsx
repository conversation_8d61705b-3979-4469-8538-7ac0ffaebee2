/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { LayoutContent } from '../../../web';

const meta: Meta<typeof LayoutContent> = {
  title: 'Web/Layout/LayoutContent',
  component: LayoutContent,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    hiddenHeader: {
      control: 'boolean',
      description: '是否隐藏头部（影响高度计算）',
    },
    headerHeight: {
      control: 'text',
      description: '头部高度，用于计算内容区域高度',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    hiddenHeader: false,
    headerHeight: '46px',
    children: (
      <div style={{ padding: '20px', background: '#f5f5f5', height: '2000px' }}>
        <h2>布局内容区域</h2>
        <p>这是一个示例内容区域，高度会根据头部是否隐藏自动计算。</p>
        <p>内容区域具有垂直滚动条，可以滚动查看更多内容。</p>
        <div style={{ marginTop: '50px' }}>
          {Array.from({ length: 50 }, (_, i) => (
            <p key={i}>示例内容行 {i + 1}</p>
          ))}
        </div>
      </div>
    ),
  },
};

export const WithHeader: Story = {
  args: {
    hiddenHeader: false,
    headerHeight: '46px',
    children: (
      <div style={{ padding: '20px' }}>
        <h2>带头部的布局内容</h2>
        <p>内容区域高度 = 100vh - 头部高度(46px)</p>
      </div>
    ),
  },
};

export const WithoutHeader: Story = {
  args: {
    hiddenHeader: true,
    children: (
      <div style={{ padding: '20px' }}>
        <h2>无头部的布局内容</h2>
        <p>内容区域高度 = 100vh（全屏高度）</p>
      </div>
    ),
  },
};

export const CustomHeaderHeight: Story = {
  args: {
    hiddenHeader: false,
    headerHeight: '60px',
    children: (
      <div style={{ padding: '20px' }}>
        <h2>自定义头部高度</h2>
        <p>内容区域高度 = 100vh - 头部高度(60px)</p>
      </div>
    ),
  },
};

export const WithScrollableContent: Story = {
  args: {
    hiddenHeader: false,
    headerHeight: '46px',
    children: (
      <div style={{ padding: '20px' }}>
        <h2>可滚动内容示例</h2>
        <div
          style={{ background: '#fff', padding: '20px', borderRadius: '8px' }}
        >
          {Array.from({ length: 100 }, (_, i) => (
            <div
              key={i}
              style={{ padding: '10px 0', borderBottom: '1px solid #eee' }}
            >
              内容项 {i + 1} - 这是一个很长的内容项，用来演示滚动效果
            </div>
          ))}
        </div>
      </div>
    ),
  },
};
