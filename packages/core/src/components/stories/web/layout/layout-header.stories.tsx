/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BellOutlined } from '@ant-design/icons';
import type { Meta, StoryObj } from '@storybook/react';
import {
  HeaderActions,
  HeaderLogo,
  HeaderMenu,
  HelpDropdown,
  LayoutHeader,
  UserDropdown,
} from '@waterdesk/core/components/web';
import { Badge, Button } from 'antd';

const meta: Meta<typeof LayoutHeader> = {
  title: 'Web/Layout/LayoutHeader',
  component: LayoutHeader,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    hidden: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础菜单项配置
const menuItems = [
  {
    key: 'dashboard',
    label: '仪表板',
  },
  {
    key: 'monitoring',
    label: '监控',
  },
  {
    key: 'dispatch',
    label: '调度',
  },
  {
    key: 'analysis',
    label: '分析',
    children: [
      {
        key: 'reports',
        label: '报表',
      },
      {
        key: 'charts',
        label: '图表',
      },
    ],
  },
];

// 基础用户信息
const userInfo = {
  name: '张三',
  avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1',
  role: '系统管理员',
};

export const Default: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="慧水科技"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['dashboard']}
      />
    ),
    actions: (
      <HeaderActions>
        <Badge
          count={3}
          size="small"
        >
          <Button
            type="text"
            danger
            icon={<BellOutlined />}
          >
            告警
          </Button>
        </Badge>
        <HelpDropdown />
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const WithoutLogo: Story = {
  args: {
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['monitoring']}
      />
    ),
    actions: (
      <HeaderActions>
        <HelpDropdown />
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const Hidden: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="慧水科技"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['dispatch']}
      />
    ),
    actions: (
      <HeaderActions>
        <HelpDropdown />
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: true,
  },
};

export const MinimalActions: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="慧水科技"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['analysis']}
      />
    ),
    actions: (
      <HeaderActions>
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const WithCustomHelpMenu: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="水务调度系统"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['monitoring']}
      />
    ),
    actions: (
      <HeaderActions>
        <Badge
          count={5}
          size="small"
        >
          <Button
            type="text"
            danger
            icon={<BellOutlined />}
          >
            告警
          </Button>
        </Badge>
        <HelpDropdown
          items={[
            { key: 'manual', label: '用户手册' },
            { key: 'video', label: '视频教程' },
            { key: 'faq', label: '常见问题' },
            { key: 'contact', label: '联系我们' },
            { key: 'about', label: '关于系统' },
          ]}
        />
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const WithCustomUserMenu: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="智能水务平台"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['dashboard']}
      />
    ),
    actions: (
      <HeaderActions>
        <HelpDropdown />
        <UserDropdown
          user={userInfo}
          items={[
            { key: 'profile', label: '个人资料' },
            { key: 'preferences', label: '偏好设置' },
            { key: 'security', label: '安全设置' },
            { type: 'divider' },
            { key: 'switchAccount', label: '切换账号' },
            { key: 'logout', label: '退出登录' },
          ]}
        />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const LogoOnly: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="慧水科技"
        height="45px"
      />
    ),
    hidden: false,
  },
};

export const WithTextLogo: Story = {
  args: {
    logo: (
      <HeaderLogo
        title="慧水科技"
        height="45px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems}
        selectedKeys={['dashboard']}
      />
    ),
    actions: (
      <HeaderActions>
        <HelpDropdown />
        <UserDropdown user={userInfo} />
      </HeaderActions>
    ),
    hidden: false,
  },
};

export const CompactLayout: Story = {
  args: {
    logo: (
      <HeaderLogo
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="系统Logo"
        title="慧水科技"
        height="35px"
      />
    ),
    menu: (
      <HeaderMenu
        items={menuItems.slice(0, 3)} // 只显示前3个菜单项
        selectedKeys={['dashboard']}
      />
    ),
    actions: (
      <HeaderActions size="small">
        <Badge
          count={2}
          size="small"
        >
          <Button
            type="text"
            size="small"
            danger
            icon={<BellOutlined />}
          >
            告警
          </Button>
        </Badge>
        <HelpDropdown
          buttonSize="small"
          iconSize={16}
        />
        <UserDropdown
          user={userInfo}
          avatarSize="small"
        />
      </HeaderActions>
    ),
    hidden: false,
  },
};
