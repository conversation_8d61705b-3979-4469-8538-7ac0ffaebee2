/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { TimeLineProps, Timeline } from '@waterdesk/core/components/web';
import { WarnConfirmStatus, WarnPrimaryType } from '@waterdesk/data/warn';
import dayjs from 'dayjs';
import { useState } from 'react';

const TimeLineWrapper = (props: TimeLineProps) => {
  const [timeValue, setTimeValue] = useState(props.timeValue);
  const [dateValue, setDateValue] = useState(props.dateValue);
  const [autoPlay, setAutoPlay] = useState(props.autoPlay);

  return (
    <Timeline
      {...props}
      timeValue={timeValue}
      dateValue={dateValue}
      autoPlay={autoPlay}
      updateTimeValue={setTimeValue}
      updateDateValue={setDateValue}
      updateAutoPlay={setAutoPlay}
    />
  );
};

const meta: Meta<typeof Timeline> = {
  title: 'Web/Timeline',
  component: TimeLineWrapper,
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    timeValue: {
      control: { type: 'number', min: 0, max: 1440, step: 1 },
      description: '当前时间值（分钟数，从0点开始计算）',
    },
    dateValue: {
      control: false,
      description: '当前日期值',
    },
    showDate: {
      control: 'boolean',
      description: '是否显示日期选择器',
    },
    showRealtimeMark: {
      control: 'boolean',
      description: '是否显示实时时间标记',
    },
    autoPlay: {
      control: 'boolean',
      description: '是否自动播放',
    },
    startTime: {
      control: false,
      description: '开始时间（可选，用于限制时间范围）',
    },
    endTime: {
      control: false,
      description: '结束时间（可选，用于限制时间范围）',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * 默认时间线
 *
 * 基础的时间线组件，显示当前时间并支持基本交互。
 */
export const Default: Story = {
  args: {
    timeValue: 480, // 08:00
    dateValue: dayjs(),
    autoPlay: false,
    showDate: true,
    showRealtimeMark: true,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 实时时间线
 *
 * 开启自动播放，实时跟踪当前时间。
 */
export const RealTime: Story = {
  args: {
    timeValue: dayjs().hour() * 60 + dayjs().minute(),
    dateValue: dayjs(),
    autoPlay: true,
    showDate: true,
    showRealtimeMark: true,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 仅时间控制
 *
 * 隐藏日期选择器，仅显示时间控制功能。
 */
export const TimeOnly: Story = {
  args: {
    timeValue: 720, // 12:00
    dateValue: dayjs(),
    autoPlay: false,
    showDate: false,
    showRealtimeMark: true,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 限制时间范围
 *
 * 设置开始和结束时间，限制可选择的时间范围。
 */
export const TimeRange: Story = {
  args: {
    timeValue: 540, // 09:00
    dateValue: dayjs(),
    startTime: dayjs().hour(8).minute(0).second(0), // 08:00
    endTime: dayjs().hour(18).minute(0).second(0), // 18:00
    autoPlay: false,
    showDate: true,
    showRealtimeMark: false,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 历史日期
 *
 * 显示历史日期的时间线，不显示实时标记。
 */
export const HistoricalDate: Story = {
  args: {
    timeValue: 360, // 06:00
    dateValue: dayjs().subtract(1, 'day'),
    autoPlay: false,
    showDate: true,
    showRealtimeMark: false,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 带警告信息
 *
 * 展示如何在时间线上显示警告和事件信息。
 */
export const WithWarnings: Story = {
  args: {
    timeValue: 600, // 10:00
    dateValue: dayjs(),
    autoPlay: false,
    showDate: true,
    showRealtimeMark: true,
    currentWarnInfo: [
      {
        id: '1',
        primaryType: WarnPrimaryType.REALTIME,
        secondType: 'pressure',
        secondTypeName: '压力异常警告',
        rank: 2,
        startTime: dayjs().hour(9).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs().hour(9).minute(45).format('YYYY-MM-DD HH:mm:ss'),
        createTime: dayjs().hour(9).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        duration: [15, '15分钟'],
        description: '泵站A压力异常警告',
        workOrderStatus: 'PENDING',
        confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        details: [],
        eventId: '',
        eventName: '',
        remark: '',
      },
      {
        id: '2',
        primaryType: WarnPrimaryType.ASSESSMENT,
        secondType: 'equipment',
        secondTypeName: '设备故障',
        rank: 1,
        startTime: dayjs().hour(14).minute(15).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs().hour(14).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        createTime: dayjs().hour(14).minute(15).format('YYYY-MM-DD HH:mm:ss'),
        duration: [15, '15分钟'],
        description: '阀门B设备故障',
        workOrderStatus: 'PROCESSING',
        confirmStatus: WarnConfirmStatus.CONFIRM,
        details: [],
        eventId: '',
        eventName: '',
        remark: '',
      },
    ],
    currentEventInfo: [
      {
        eventId: '1',
        eventTitle: '例行巡检',
        eventStartTime: dayjs().hour(8).minute(0).format('YYYY-MM-DD HH:mm:ss'),
        eventEndTime: dayjs().hour(10).minute(0).format('YYYY-MM-DD HH:mm:ss'),
        eventType: 'maintenance',
        eventDescription: '每日例行设备巡检',
        eventStatus: 'ACTIVE',
        eventSubType: 'ROUTINE',
        eventAddress: '泵站区域',
        operator: '运维人员',
        operatorId: 'op001',
        createTime: dayjs().hour(7).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().hour(8).minute(0).format('YYYY-MM-DD HH:mm:ss'),
        remark: '例行设备巡检工作',
      },
      {
        eventId: '2',
        eventTitle: '设备调试',
        eventStartTime: dayjs()
          .hour(15)
          .minute(30)
          .format('YYYY-MM-DD HH:mm:ss'),
        eventEndTime: dayjs().hour(16).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        eventType: 'operation',
        eventDescription: '新设备安装调试',
        eventStatus: 'ACTIVE',
        eventSubType: 'MAINTENANCE',
        eventAddress: '阀门控制室',
        operator: '技术人员',
        operatorId: 'op002',
        createTime: dayjs().hour(15).minute(0).format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().hour(15).minute(30).format('YYYY-MM-DD HH:mm:ss'),
        remark: '新阀门设备调试',
      },
    ] as any,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
};

/**
 * 自定义主题
 *
 * 演示如何通过 Storybook 全局主题切换功能来查看不同主题效果。
 * 可以在 Storybook 工具栏中切换 "Light" 和 "Dark" 主题。
 */
export const ThemeVariation: Story = {
  args: {
    timeValue: 660, // 11:00
    dateValue: dayjs(),
    autoPlay: false,
    showDate: true,
    showRealtimeMark: true,
    updateTimeValue: () => {},
    updateDateValue: () => {},
  },
  parameters: {
    docs: {
      description: {
        story:
          '使用 Storybook 工具栏上的主题切换按钮可以查看组件在不同主题下的表现。',
      },
    },
  },
};
