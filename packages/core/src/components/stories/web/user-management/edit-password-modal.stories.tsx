/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import {
  type EditPasswordFormValues,
  EditPasswordModal,
} from '@waterdesk/core/components/web';
import { Button } from 'antd';
import { useState } from 'react';
import { action } from 'storybook/actions';

const meta: Meta<typeof EditPasswordModal> = {
  title: 'Web/UserManagement/EditPasswordModal',
  component: EditPasswordModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    open: {
      control: 'boolean',
      description: '是否显示模态框',
    },
    isNewPassword: {
      control: 'boolean',
      description: '是否为新密码设置模式（不需要输入旧密码）',
    },
    onClose: {
      action: 'closed',
      description: '关闭模态框的回调函数',
    },
    handleSave: {
      action: 'saved',
      description: '保存密码的回调函数',
    },
    onResetPassword: {
      action: 'resetPassword',
      description: '重置密码的回调函数（管理员功能）',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 带有交互的包装组件
const InteractiveWrapper = ({
  isNewPassword = false,
  showResetButton = false,
}: {
  isNewPassword?: boolean;
  showResetButton?: boolean;
}) => {
  const [open, setOpen] = useState(false);

  const handleSave = (values: EditPasswordFormValues) => {
    action('handleSave')(values);
    console.log('Password form values:', values);
    setOpen(false);
  };

  const handleClose = () => {
    action('onClose')();
    setOpen(false);
  };

  const handleResetPassword = showResetButton
    ? () => {
        action('onResetPassword')();
        console.log('Reset password clicked');
        setOpen(false);
      }
    : undefined;

  return (
    <div>
      <Button
        type="primary"
        onClick={() => setOpen(true)}
      >
        {isNewPassword ? '设置新密码' : '修改密码'}
      </Button>
      <EditPasswordModal
        open={open}
        onClose={handleClose}
        handleSave={handleSave}
        isNewPassword={isNewPassword}
        onResetPassword={handleResetPassword}
      />
    </div>
  );
};

export const Default: Story = {
  render: () => <InteractiveWrapper />,
  parameters: {
    docs: {
      description: {
        story: '默认的修改密码模态框，需要输入旧密码、新密码和确认密码。',
      },
    },
  },
};

export const NewPassword: Story = {
  render: () => <InteractiveWrapper isNewPassword={true} />,
  parameters: {
    docs: {
      description: {
        story:
          '新密码设置模式，不需要输入旧密码，通常用于首次设置密码或管理员重置后的密码设置。',
      },
    },
  },
};

export const WithResetButton: Story = {
  render: () => <InteractiveWrapper showResetButton={true} />,
  parameters: {
    docs: {
      description: {
        story: '管理员模式，包含重置密码按钮，可以直接重置用户密码。',
      },
    },
  },
};

export const AdminNewPasswordWithReset: Story = {
  render: () => (
    <InteractiveWrapper
      isNewPassword={true}
      showResetButton={true}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: '管理员为新用户设置密码的场景，既不需要旧密码，又提供重置功能。',
      },
    },
  },
};

// 静态展示版本（始终打开）
export const AlwaysOpen: Story = {
  args: {
    open: true,
    onClose: action('onClose'),
    handleSave: action('handleSave'),
    isNewPassword: false,
  },
  parameters: {
    docs: {
      description: {
        story: '始终显示的模态框，用于文档展示和样式调试。',
      },
    },
  },
};

export const AlwaysOpenNewPassword: Story = {
  args: {
    open: true,
    onClose: action('onClose'),
    handleSave: action('handleSave'),
    isNewPassword: true,
  },
  parameters: {
    docs: {
      description: {
        story: '始终显示的新密码设置模态框。',
      },
    },
  },
};

export const AlwaysOpenWithReset: Story = {
  args: {
    open: true,
    onClose: action('onClose'),
    handleSave: action('handleSave'),
    onResetPassword: action('onResetPassword'),
    isNewPassword: false,
  },
  parameters: {
    docs: {
      description: {
        story: '始终显示的带重置按钮的模态框。',
      },
    },
  },
};
