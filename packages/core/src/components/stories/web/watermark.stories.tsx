/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { Watermark } from '@waterdesk/core/components/web';

const meta: Meta<typeof Watermark> = {
  title: 'Web/Watermark',
  component: Watermark,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    visible: {
      control: 'boolean',
    },
  },
  decorators: [
    (Story) => (
      <div
        style={{ height: '500px', position: 'relative', background: '#f5f5f5' }}
      >
        <div style={{ padding: '20px' }}>
          <h2>页面内容示例</h2>
          <p>这是一个示例页面，用来展示水印组件的效果。</p>
        </div>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    src: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
    alt: '水印',
    visible: true,
  },
};

export const Hidden: Story = {
  args: {
    src: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
    alt: '水印',
    visible: false,
  },
};

export const CustomPosition: Story = {
  args: {
    src: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
    alt: '水印',
    visible: true,
    style: {
      bottom: '50px',
      left: '20px',
      right: 'auto',
    },
  },
};
