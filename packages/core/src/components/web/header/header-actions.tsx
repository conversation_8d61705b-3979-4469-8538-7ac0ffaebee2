/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Space } from 'antd';
import React, { memo } from 'react';
import styled from 'styled-components';

const HeaderActionsWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export interface HeaderActionsProps {
  /** 自定义操作组件列表 */
  children?: React.ReactNode;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** Space 组件的间距 */
  size?: 'small' | 'middle' | 'large' | number;
}

export const HeaderActions: React.FC<HeaderActionsProps> = memo(
  ({ children, style, className, size = 'small' }) => {
    return (
      <HeaderActionsWrapper
        style={style}
        className={className}
      >
        <Space size={size}>{children}</Space>
      </HeaderActionsWrapper>
    );
  },
);

HeaderActions.displayName = 'HeaderActions';
