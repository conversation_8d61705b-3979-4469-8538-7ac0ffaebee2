/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Flex, Typography } from 'antd';
import React from 'react';
import styled from 'styled-components';

const HeaderLogoWrapper = styled(Flex)`
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s;

  &:hover {
    opacity: 0.8;
  }
`;

const LogoImage = styled.img<{ $height: string }>`
  height: ${(props) => props.$height};
  object-fit: contain;
`;

const LogoText = styled(Typography.Text)`
  margin-left: 12px;
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colorText};
`;

export interface HeaderLogoProps {
  /** Logo 图片地址 */
  src?: string;
  /** Logo 替代文本 */
  alt?: string;
  /** Logo 高度 */
  height?: string;
  /** 系统名称 */
  title?: string;
  /** 是否显示标题文字 */
  showTitle?: boolean;
  /** 点击事件 */
  onClick?: () => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

export const HeaderLogo: React.FC<HeaderLogoProps> = ({
  src,
  alt = 'Logo',
  height = '45px',
  title,
  showTitle = true,
  onClick,
  style,
  className,
}) => {
  return (
    <HeaderLogoWrapper
      onClick={onClick}
      style={style}
      className={className}
    >
      {src && (
        <LogoImage
          src={src}
          alt={alt}
          $height={height}
        />
      )}
      {showTitle && title && <LogoText>{title}</LogoText>}
    </HeaderLogoWrapper>
  );
};

HeaderLogo.displayName = 'HeaderLogo';
