/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Menu as AntdMenu, MenuProps } from 'antd';
import React from 'react';
import styled from 'styled-components';

const HeaderMenuWrapper = styled.div`
  flex: 1;
  .ant-menu {
    border: none;
    background: transparent;
  }
`;

export interface HeaderMenuProps {
  /** 菜单项配置 */
  items?: MenuProps['items'];
  /** 当前选中的菜单项 */
  selectedKeys?: string[];
  /** 菜单点击事件 */
  onClick?: MenuProps['onClick'];
  /** 菜单模式，默认为水平 */
  mode?: 'horizontal' | 'vertical' | 'inline';
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

export const HeaderMenu: React.FC<HeaderMenuProps> = ({
  items,
  selectedKeys,
  onClick,
  mode = 'horizontal',
  style,
  className,
}) => {
  return (
    <HeaderMenuWrapper
      style={style}
      className={className}
    >
      <AntdMenu
        mode={mode}
        items={items}
        selectedKeys={selectedKeys}
        onClick={onClick}
      />
    </HeaderMenuWrapper>
  );
};

HeaderMenu.displayName = 'HeaderMenu';
