/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import React from 'react';

export interface HelpDropdownProps {
  /** 帮助菜单项 */
  items?: MenuProps['items'];
  /** 按钮类型 */
  buttonType?: 'link' | 'text' | 'default' | 'primary' | 'dashed';
  /** 按钮大小 */
  buttonSize?: 'small' | 'middle' | 'large';
  /** 图标大小 */
  iconSize?: string | number;
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 菜单点击事件 */
  onMenuClick?: MenuProps['onClick'];
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

export const HelpDropdown: React.FC<HelpDropdownProps> = ({
  items,
  buttonType = 'link',
  buttonSize = 'large',
  iconSize = '20px',
  icon,
  onMenuClick,
  style,
  className,
}) => {
  const defaultItems: MenuProps['items'] = [
    {
      key: 'manual',
      label: '使用手册',
    },
    {
      key: 'updateLog',
      label: '更新日志',
    },
    {
      key: 'about',
      label: '版本信息',
    },
  ];

  const menuItems = items || defaultItems;

  const iconElement = icon || (
    <QuestionCircleOutlined
      style={{
        fontSize: typeof iconSize === 'number' ? `${iconSize}px` : iconSize,
      }}
    />
  );

  return (
    <Dropdown
      menu={{
        items: menuItems,
        onClick: onMenuClick,
      }}
    >
      <Button
        type={buttonType}
        size={buttonSize}
        style={style}
        className={className}
      >
        {iconElement}
      </Button>
    </Dropdown>
  );
};

HelpDropdown.displayName = 'HelpDropdown';
