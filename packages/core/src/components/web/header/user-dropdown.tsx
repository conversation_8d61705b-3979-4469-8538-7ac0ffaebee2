/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown, MenuProps, Space, Typography } from 'antd';
import React from 'react';
import styled from 'styled-components';

const UserDropDownWrapper = styled(Dropdown)`
  cursor: pointer;
`;

const UserNameText = styled(Typography.Text)`
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export interface UserInfo {
  /** 用户名 */
  name?: string;
  /** 用户头像 */
  avatar?: string;
  /** 用户角色或部门 */
  role?: string;
  /** 用户ID */
  id?: string;
}

export interface UserDropdownProps {
  /** 用户信息 */
  user?: UserInfo;
  /** 用户菜单项 */
  items?: MenuProps['items'];
  /** 头像大小 */
  avatarSize?: 'small' | 'default' | 'large' | number;
  /** 是否显示用户名 */
  showName?: boolean;
  /** 菜单点击事件 */
  onMenuClick?: MenuProps['onClick'];
  /** 自定义类名 */
  className?: string;
}

export const UserDropdown: React.FC<UserDropdownProps> = ({
  user,
  items,
  avatarSize = 'small',
  showName = false,
  onMenuClick,
  className,
}) => {
  const defaultItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: '个人信息',
    },
    {
      key: 'settings',
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
    },
  ];

  const menuItems = items || defaultItems;

  const userContent = (
    <Space size="small">
      <Avatar
        size={avatarSize}
        src={user?.avatar}
        icon={<UserOutlined />}
      />
      {showName && user?.name && <UserNameText>{user.name}</UserNameText>}
    </Space>
  );

  return (
    <UserDropDownWrapper
      className={className}
      menu={{
        items: menuItems,
        onClick: onMenuClick,
      }}
    >
      {userContent}
    </UserDropDownWrapper>
  );
};

UserDropdown.displayName = 'UserDropdown';
