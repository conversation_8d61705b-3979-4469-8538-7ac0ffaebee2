/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { UserInfoType } from '@waterdesk/request/login';
import { Form, Input, Modal } from 'antd';
import { FC, useEffect } from 'react';

interface Props {
  showUserInfoModal: boolean;
  close: () => void;
  userInfo: UserInfoType;
}

export const UserInfoModal: FC<Props> = ({
  showUserInfoModal,
  close,
  userInfo,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (showUserInfoModal) {
      form.setFieldsValue(userInfo);
    }
  }, [showUserInfoModal, userInfo, form]);

  return (
    <Modal
      title="个人信息"
      open={showUserInfoModal}
      onCancel={close}
      footer={null}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="horizontal"
        name="userInfo"
        labelCol={{ span: 4 }}
      >
        <Form.Item
          label="昵称"
          name="userName"
        >
          <Input readOnly />
        </Form.Item>
        <Form.Item
          label="性别"
          name="userSex"
        >
          <Input readOnly />
        </Form.Item>
        <Form.Item
          label="电话"
          name="userPhone"
        >
          <Input readOnly />
        </Form.Item>
        <Form.Item
          label="邮箱"
          name="userEmail"
        >
          <Input readOnly />
        </Form.Item>
      </Form>
    </Modal>
  );
};

UserInfoModal.displayName = 'UserInfoModal';
