/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LockOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Theme } from '@waterdesk/core/theme';
import { Button, List, Modal, Select, Switch } from 'antd';
import { EditPasswordFormValues, EditPasswordModal } from '..';

export interface UserSettingModalProps {
  /** 是否显示用户设置窗口 */
  open: boolean;
  /** 关闭窗口的回调 */
  onClose: () => void;
  /** 当前主题 */
  theme: Theme;
  /** 主题变更回调 */
  onThemeChange: (theme: Theme) => void;
  /** 是否显示帮助 */
  showHelp: boolean;
  /** 帮助模式变更回调 */
  onHelpChange: (show: boolean) => void;
  /** 是否显示修改密码弹窗 */
  editPasswordOpen: boolean;
  /** 设置修改密码弹窗显示状态 */
  setEditPasswordOpen: (open: boolean) => void;
  /** 保存修改密码的回调 */
  onSavePassword: (formValues: EditPasswordFormValues) => void;
  /** 消息提示的contextHolder */
  messageContextHolder?: React.ReactNode;
  /** iconfont 组件 */
  iconfont?: React.ReactNode;
}

const UserSettingModal: React.FC<UserSettingModalProps> = ({
  open,
  onClose,
  theme,
  onThemeChange,
  showHelp,
  onHelpChange,
  editPasswordOpen,
  setEditPasswordOpen,
  onSavePassword,
  messageContextHolder,
  iconfont,
}) => {
  return (
    <>
      <Modal
        title="个人设置"
        open={open}
        footer={false}
        onCancel={onClose}
      >
        <List itemLayout="horizontal">
          <List.Item
            key="theme"
            actions={[
              <Select
                key="settingTheme"
                defaultValue={theme}
                style={{ width: 120 }}
                onChange={onThemeChange}
              >
                <Select.Option
                  key="light"
                  value="light"
                >
                  浅色
                </Select.Option>
                <Select.Option
                  key="dark"
                  value="dark"
                >
                  深色
                </Select.Option>
              </Select>,
            ]}
          >
            <List.Item.Meta
              avatar={iconfont}
              title="修改主题"
              description={
                <>
                  用于修改界面颜色主题
                  <br />
                  (切换后会自动刷新页面)
                </>
              }
            />
          </List.Item>

          <List.Item
            key="password"
            actions={[
              <Button
                key="settingPassword"
                style={{ width: 120 }}
                onClick={() => setEditPasswordOpen(true)}
              >
                修改密码
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<LockOutlined />}
              title="密码设置"
              description="用于修改用户密码"
            />
          </List.Item>

          <List.Item
            key="help"
            actions={[
              <Switch
                key="settingHelp"
                defaultChecked={showHelp}
                checkedChildren="开启"
                unCheckedChildren="关闭"
                onChange={onHelpChange}
              />,
            ]}
          >
            <List.Item.Meta
              avatar={<QuestionCircleOutlined style={{ fontSize: '16px' }} />}
              title="帮助模式"
              description="开启后，系统显示使用提示"
            />
          </List.Item>
        </List>
      </Modal>
      <EditPasswordModal
        open={editPasswordOpen}
        onClose={() => setEditPasswordOpen(false)}
        handleSave={onSavePassword}
      />
      {messageContextHolder}
    </>
  );
};

export default UserSettingModal;
