/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// 头部组件
export { HeaderActions } from './header/header-actions';
export { HeaderLogo } from './header/header-logo';
export { HeaderMenu } from './header/header-menu';
export { HelpDropdown } from './header/help-dropdown';
export type { UserDropdownProps, UserInfo } from './header/user-dropdown';
export { UserDropdown } from './header/user-dropdown';
export { UserInfoModal } from './header/user-info-modal';

// 布局组件
export { LayoutContent } from './layout/layout-content';
export { LayoutHeader } from './layout/layout-header';

// 时间线组件
export { Timeline } from './time-line';
export { SecondTimer } from './time-line/second-timer';
export { TimeLineDatePicker } from './time-line/time-line-datepicker';
export { TimeSlider } from './time-line/time-slider';
export type {
  MarkItem,
  SecondTimerProps,
  TimeLineDatePickerProps,
  TimeLineProps,
  TimeRange,
  TimeSliderProps,
} from './time-line/types';

// 用户管理组件
export type {
  EditPasswordFormValues,
  EditPasswordModalProps,
} from './user-management/edit-password-modal';
export { EditPasswordModal } from './user-management/edit-password-modal';

// 水印组件
export { Watermark } from './watermark';
