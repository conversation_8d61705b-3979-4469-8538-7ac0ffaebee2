/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React from 'react';
import styled from 'styled-components';

const LayoutContentWrapper = styled.div<{
  hiddenHeader: boolean;
  headerHeight?: string;
}>`
  height: ${(props) =>
    props.hiddenHeader
      ? '100vh'
      : `calc(100vh - ${props.headerHeight || '46px'})`};
  overflow-y: auto;
  overflow-x: hidden;
`;

export interface LayoutContentProps {
  /** 子组件 */
  children?: React.ReactNode;
  /** 是否隐藏头部（影响高度计算） */
  hiddenHeader?: boolean;
  /** 头部高度，用于计算内容区域高度 */
  headerHeight?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

export const LayoutContent: React.FC<LayoutContentProps> = ({
  children,
  hiddenHeader = false,
  headerHeight = '46px',
  style,
  className,
}) => {
  return (
    <LayoutContentWrapper
      hiddenHeader={hiddenHeader}
      headerHeight={headerHeight}
      style={style}
      className={className}
    >
      {children}
    </LayoutContentWrapper>
  );
};

LayoutContent.displayName = 'LayoutContent';
