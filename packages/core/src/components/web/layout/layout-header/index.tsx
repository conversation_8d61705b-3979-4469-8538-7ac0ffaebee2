/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Flex, Space } from 'antd';
import React from 'react';
import styled from 'styled-components';

export const LayoutMainHeaderWrapper = styled.div`
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid ${({ theme }) => theme.colorBorderSecondary};
`;

export interface LayoutHeaderProps {
  /** LOGO 组件 */
  logo?: React.ReactNode;
  /** 菜单组件 */
  menu?: React.ReactNode;
  /** 操作区域组件 */
  actions?: React.ReactNode;
  /** 是否隐藏头部 */
  hidden?: boolean;
}

export const LayoutHeader: React.FC<LayoutHeaderProps> = ({
  hidden = false,
  logo,
  menu,
  actions,
}) => {
  if (hidden) {
    return null;
  }

  return (
    <LayoutMainHeaderWrapper>
      {/* Logo 区域 */}
      {logo}

      {/* 菜单和操作区域 */}
      <Flex
        justify="space-between"
        flex={1}
      >
        {/* 菜单区域 */}
        <div style={{ width: 'calc(100% - 180px)' }}>{menu}</div>

        {/* 操作区域 */}
        <Space>{actions}</Space>
      </Flex>
    </LayoutMainHeaderWrapper>
  );
};

LayoutHeader.displayName = 'LayoutHeader';
