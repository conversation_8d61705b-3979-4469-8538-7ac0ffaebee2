/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React from 'react';

export interface LogoConfig {
  /** 图片地址 */
  src: string;
  /** alt 属性 */
  alt: string;
  /** 高度 */
  height?: string;
}

export interface LayoutHeaderProps {
  /** Logo 配置 */
  logo?: LogoConfig;
  /** 菜单组件 */
  menu?: React.ReactNode;
  /** 操作区域组件（告警、帮助、用户信息等） */
  actions?: React.ReactNode;
  /** 是否隐藏头部 */
  hidden?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义 CSS 类名 */
  className?: string;
}
