/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import {
  getDateTimeFromValue,
  getValueFromDateTime,
} from '@waterdesk/data/time-data';
import { DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import localCN from 'dayjs/locale/zh-cn';
import { memo, ReactNode, useCallback, useEffect, useState } from 'react';
import { SecondTimer } from './second-timer';
import { TimeLineWrapper } from './style';
import { TimeLineDatePicker } from './time-line-datepicker';
import { TimeSlider } from './time-slider';
import type { TimeLineProps } from './types';

/**
 * 默认时间刻度标记
 * 用于在时间滑块上显示关键时间点
 */
const defaultMarks = {
  240: getDateTimeFromValue(240).format('HH:mm'), // 04:00
  480: getDateTimeFromValue(480).format('HH:mm'), // 08:00
  720: getDateTimeFromValue(720).format('HH:mm'), // 12:00
  960: getDateTimeFromValue(960).format('HH:mm'), // 16:00
  1200: getDateTimeFromValue(1200).format('HH:mm'), // 20:00
};

/**
 * 时间线组件
 *
 * 提供时间控制功能，包括：
 * - 日期选择
 * - 时间滑块控制
 * - 实时时间显示
 * - 自动播放功能
 * - 警告和事件信息显示
 *
 * @param props - 组件属性
 * @returns 时间线组件JSX元素
 */
export const Timeline = memo<TimeLineProps>((props) => {
  const {
    autoPlay,
    showDate,
    timeValue,
    dateValue,
    startTime,
    endTime,
    warnInfo,
    eventInfo,
    currentWarnInfo,
    currentEventInfo,
    currentEventRelatedInfo,
    showRealtimeMark,
    updateDateValue,
    updateTimeValue,
    getAllList,
    updateAutoPlay,
    availableTimeRanges = [],
    pollutionTimeRanges = [],
  } = props;

  // 获取主题令牌
  const { token } = useToken();

  // 最新时间状态
  const [latestTime, setLatestTime] = useState<Dayjs>(dayjs());
  // 滑块当前值状态
  const [sliderValue, setSliderValue] = useState<number>(timeValue);

  /**
   * 处理日期选择器日期变化
   */
  const handleDatePickerDateChange = useCallback(
    (date: Dayjs | null) => {
      if (date !== null) {
        updateDateValue(date);
        // 如果选择的是今天，则设置为当前时间并开启自动播放
        if (dayjs().isSame(date, 'day')) {
          updateTimeValue(getValueFromDateTime(dayjs()));
          setLatestTime(dayjs());
          updateAutoPlay?.(true);
        } else {
          // 否则设置为0点并关闭自动播放
          updateTimeValue(0);
          updateAutoPlay?.(false);
        }
      }
    },
    [updateDateValue, updateTimeValue, updateAutoPlay],
  );

  /**
   * 处理日期选择器时间变化
   */
  const handleDatePickerTimeChange = (date: Dayjs | null) => {
    if (date !== null) {
      updateTimeValue(getValueFromDateTime(date));
      // 通过时间选择器手动设置时间时，关闭自动播放
      updateAutoPlay?.(false);
    }
  };

  /**
   * 处理滑块值变化（拖拽过程中）
   */
  const handleOnChange = (value: number) => {
    setSliderValue(value);
  };

  /**
   * 秒计时器完成回调
   * 用于更新实时时间和自动播放逻辑
   */
  const handleSecondTimerFinish = () => {
    const nowDate: Dayjs = dayjs();
    setLatestTime(nowDate);
    if (autoPlay) {
      const timeValue = getValueFromDateTime(nowDate);
      // 如果当前时间跟时间线日期在同一天，则跳过 updateDateValue，避免 chart time 同时变化
      if (!dayjs().isSame(dateValue, 'day')) {
        updateDateValue(nowDate);
      }
      updateTimeValue(timeValue);
    }
  };

  /**
   * 获取实时时间标记
   * 用于在滑块上显示当前实时时间位置
   */
  const getMarkRealTime = () => {
    // 如果明确设置不显示实时标记，返回空对象
    if (typeof showRealtimeMark === 'boolean' && showRealtimeMark === false)
      return {};

    // 只有在今天才显示实时时间标记
    if (latestTime && dayjs().isSame(dateValue, 'day')) {
      return {
        [getValueFromDateTime(latestTime)]: {
          style: {
            color: token.colorErrorActive || '#ff4d4f',
            top: '-42px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              最新:{latestTime.format('HH:mm:')}
              <SecondTimer
                style={{ fontSize: '1.2em' }}
                onFinish={handleSecondTimerFinish}
              />
            </strong>
          ),
        },
      };
    }
    return {};
  };

  const markRealTime = getMarkRealTime();

  /**
   * 处理滑块值变化完成（拖拽结束）
   */
  const handleAfterChange = (value: number) => {
    updateTimeValue(value);
    // 检查是否应该开启自动播放：当前值等于实时时间且是今天
    updateAutoPlay?.(
      value === getValueFromDateTime(dayjs()) &&
        dayjs().isSame(dateValue, 'day'),
    );
  };

  /**
   * 时间选择器内容
   */
  const timePickContent: ReactNode = (
    <div className="time-line-counttime">
      <DatePicker
        style={{ width: '48px', padding: '1px 5px' }}
        picker="time"
        format="HH:mm"
        variant="borderless"
        suffixIcon={null}
        allowClear={{
          clearIcon: null,
        }}
        inputReadOnly
        value={getDateTimeFromValue(sliderValue)}
        onChange={handleDatePickerTimeChange}
      />
    </div>
  );

  /**
   * 获取日期选择器内容
   * 根据showDate属性决定是否显示完整的日期选择器
   */
  const getDatePickContent = (): ReactNode => {
    if (typeof showDate === 'boolean' && showDate === false)
      return <div className="time-line-week">{timePickContent}</div>;

    return (
      <div className="time-line-datepicker">
        <TimeLineDatePicker
          value={dateValue}
          getAllList={getAllList}
          warnInfo={warnInfo}
          eventInfo={eventInfo}
          onChange={handleDatePickerDateChange}
        />
        <div className="time-line-week">
          <span>{dayjs(dateValue).locale(localCN).format('dddd')}</span>
          <div className="time-line-counttime">
            <DatePicker
              style={{ width: '48px', padding: '3px' }}
              picker="time"
              format="HH:mm"
              variant="borderless"
              suffixIcon={null}
              allowClear={{
                clearIcon: null,
              }}
              inputReadOnly
              value={getDateTimeFromValue(sliderValue)}
              onChange={handleDatePickerTimeChange}
            />
          </div>
        </div>
      </div>
    );
  };

  // 同步外部timeValue到内部sliderValue
  useEffect(() => {
    setSliderValue(timeValue);
  }, [timeValue]);

  // 自动播放逻辑：当时间值等于当前时间且是今天时，自动开启播放
  useEffect(() => {
    if (
      timeValue === getValueFromDateTime(dayjs()) &&
      dayjs().isSame(dateValue, 'day')
    ) {
      if (!autoPlay) {
        updateAutoPlay?.(true);
      }
    }
  }, [timeValue, dateValue, autoPlay, updateAutoPlay]);

  // 页面可见性变化处理：当页面重新可见时更新最新时间
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = dayjs();
        if (now.isAfter(dayjs(timeValue), 'second')) {
          setLatestTime(now);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [timeValue]);

  /**
   * 获取滑块标记
   * 包括默认时间标记和开始/结束时间标记
   */
  const getSliderMark = () => {
    if (startTime && endTime) {
      const startTimeMark = getValueFromDateTime(startTime);
      const endTimeMark = getValueFromDateTime(endTime);

      return {
        ...defaultMarks,
        [startTimeMark]: {
          style: {
            color: '#3589ff',
            top: '-40px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              {getDateTimeFromValue(startTimeMark).format('HH:mm')}
            </strong>
          ),
        },
        [endTimeMark]: {
          style: {
            color: '#3589ff',
            top: '-40px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              {getDateTimeFromValue(endTimeMark).format('HH:mm')}
            </strong>
          ),
        },
      };
    }
    return defaultMarks;
  };

  return (
    <TimeLineWrapper>
      <div className="time-line-timeslider">
        <TimeSlider
          date={startTime ?? dateValue}
          latestTime={latestTime}
          marks={{ ...getSliderMark(), ...markRealTime }}
          value={sliderValue}
          minSlider={startTime ? getValueFromDateTime(startTime) : undefined}
          maxSlider={endTime ? getValueFromDateTime(endTime) : undefined}
          onChange={handleOnChange}
          onAfterChange={handleAfterChange}
          warnList={currentWarnInfo ?? []}
          eventList={currentEventInfo ?? []}
          eventRelatedList={currentEventRelatedInfo ?? []}
          availableTimeRanges={availableTimeRanges}
          pollutantTimeRanges={pollutionTimeRanges}
        />
      </div>
      {getDatePickContent()}
    </TimeLineWrapper>
  );
});

Timeline.displayName = 'Timeline';
