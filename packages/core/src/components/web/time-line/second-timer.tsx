/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

/**
 * 秒计时器组件属性接口
 */
export interface SecondTimerProps {
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 每分钟完成时的回调函数 */
  onFinish?: () => void;
  /** 日期变化时的回调函数 */
  onDayChange?: () => void;
}

/**
 * 秒计时器组件
 * 用于显示实时的秒数，并在每分钟和日期变化时触发回调
 */
export const SecondTimer: React.FC<SecondTimerProps> = ({
  onFinish,
  style,
  onDayChange,
}) => {
  const [second, setSecond] = useState(dayjs().format('ss'));
  const timerRef = useRef<number | undefined>(undefined);
  const onFinishRef = useRef(onFinish);
  const onDayChangeRef = useRef(onDayChange);
  const lastDateRef = useRef(dayjs().format('YYYY-MM-DD'));

  const clearTimer = () => {
    if (timerRef.current !== undefined) {
      clearTimeout(timerRef.current);
      timerRef.current = undefined;
    }
  };

  const initTimer = () => {
    clearTimer();

    const update = () => {
      const now = dayjs();
      const nextSecond = now.second().toString().padStart(2, '0');
      const currentDate = now.format('YYYY-MM-DD');

      if (nextSecond === '00') {
        onFinishRef.current?.();
      }

      if (currentDate !== lastDateRef.current) {
        onDayChangeRef.current?.();
        lastDateRef.current = currentDate;
      }

      setSecond(nextSecond);

      const delay = 1000 - now.millisecond();
      timerRef.current = window.setTimeout(() => {
        update();
      }, delay);
    };

    update();
  };

  useEffect(() => {
    onFinishRef.current = onFinish;
  }, [onFinish]);

  useEffect(() => {
    onDayChangeRef.current = onDayChange;
  }, [onDayChange]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        initTimer();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    initTimer();

    return () => {
      clearTimer();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return <span style={style}>{second}</span>;
};

// SecondTimer 使用命名导出，无需默认导出
