/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { isTimeBetween } from '@waterdesk/data/utils';
import { WarnInfoList } from '@waterdesk/data/warn';
import { DatePickerProps, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { CSSProperties, useEffect, useState } from 'react';
import { DatePickerWrapper } from './style';

interface Props {
  value: Dayjs;
  warnInfo?: Map<string, WarnInfoList>;
  eventInfo?: Map<string, EventSchedulingBasicInfo[]>;
  style?: CSSProperties;
  onChange?: (date: Dayjs) => void;
  getAllList?: (date: Dayjs) => void;
}

function renderToolTipTitle(warnList: WarnInfoList, number?: number) {
  const list = number ? warnList.slice(0, number) : warnList;
  return warnList.length > 0 ? (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {list.map(({ secondTypeName, startTime, id }) => (
        <span key={id}>
          {dayjs(startTime).format('HH:mm')} {secondTypeName}
        </span>
      ))}
      {number && warnList.length > number ? <span>......</span> : null}
    </div>
  ) : null;
}

/**
 * 时间线日期选择器组件
 * 提供日期选择功能，并显示相关的警告和事件信息
 */
export const TimeLineDatePicker = (props: Props) => {
  const { value, style, warnInfo, eventInfo, onChange, getAllList } = props;
  const { token } = useToken();
  const [isCurrentDay, setIsCurrentDay] = useState<boolean>(true);
  const [isCurrentYear, setIsCurrentYear] = useState<boolean>(true);
  const [isOpened, setIsOpened] = useState<boolean>(false);

  const disabledDate = (currentDate: Dayjs): boolean =>
    currentDate > dayjs().endOf('day');

  const handleOnChange: DatePickerProps['onChange'] = (date) => {
    if (date !== null) {
      onChange?.(date);
    }
  };

  const handlePanelOnChange = (value: Dayjs, mode: string) => {
    if (mode === 'date') {
      getAllList?.(value);
    }
  };

  const onOpenChange = (opened: boolean) => {
    if (opened) {
      setIsOpened(opened);
    }
  };

  useEffect(() => {
    if (isOpened) {
      getAllList?.(value);
    }
  }, [isOpened]);

  const renderDateCell: DatePickerProps['cellRender'] = (current, info) => {
    const { type, originNode } = info;
    if (type === 'date') {
      const style: React.CSSProperties = {};
      const currentDayjs =
        typeof current === 'number' || typeof current === 'string'
          ? dayjs(current)
          : current;
      const warnListWithCurrent = warnInfo?.get(currentDayjs.format('YYYY-MM'));
      let currentDateWarnList: WarnInfoList = [];
      if (warnListWithCurrent && warnListWithCurrent.length > 0) {
        currentDateWarnList = warnListWithCurrent.filter((item) =>
          isTimeBetween(
            [
              dayjs(item.startTime).format('YYYY-MM-DD 00:00:00'),
              dayjs(item.endTime).format('YYYY-MM-DD 23:59:59'),
            ],
            currentDayjs,
          ),
        );
        if (currentDateWarnList.length > 0) {
          style.border = `1px dashed ${token.colorError}`;
          style.borderRadius = '50%';
        }
      }
      const eventListWithCurrent = eventInfo?.get(
        currentDayjs.format('YYYY-MM'),
      );

      if (eventListWithCurrent && eventListWithCurrent.length > 0) {
        const eventList = eventListWithCurrent.filter((item) =>
          isTimeBetween(
            [
              dayjs(item.eventStartTime).format('YYYY-MM-DD 00:00:00'),
              dayjs(item.eventStartTime).format('YYYY-MM-DD 23:59:59'),
            ],
            currentDayjs,
          ),
        );
        if (eventList.length > 0) {
          style.border = `1px dashed ${token['purple-5']}`;
          style.borderRadius = '50%';
        }
      }
      return (
        <Tooltip title={renderToolTipTitle(currentDateWarnList, 5)}>
          <div
            className="ant-picker-cell-inner"
            style={style}
          >
            {currentDayjs.date()}
          </div>
        </Tooltip>
      );
    }
    return originNode;
  };

  useEffect(() => {
    setIsCurrentDay(value.date() === dayjs().date());
    setIsCurrentYear(value.year() === dayjs().year());
  }, [value]);

  return (
    <DatePickerWrapper
      format={`${isCurrentYear ? '' : 'YYYY[年]'}MM[月]DD[日]`}
      style={{ ...style }}
      variant="borderless"
      suffixIcon={null}
      allowClear={{ clearIcon: null }}
      disabledDate={disabledDate}
      inputReadOnly
      onChange={handleOnChange}
      value={value}
      defaultValue={dayjs()}
      cellRender={renderDateCell}
      onPanelChange={handlePanelOnChange}
      isCurrentYear={isCurrentYear}
      isCurrentDay={isCurrentDay}
      onOpenChange={onOpenChange}
    />
  );
};

TimeLineDatePicker.displayName = 'TimeLineDatePicker';

// TimeLineDatePicker 使用命名导出，无需默认导出
