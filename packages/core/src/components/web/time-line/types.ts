/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from '@waterdesk/data/event-scheduling/related-info';
import { JunctionQualityChangeSetting } from '@waterdesk/data/solution-detail';
import { WarnInfoList } from '@waterdesk/data/warn';
import { Dayjs } from 'dayjs';

/**
 * 时间范围接口
 * 定义时间滑块上的可用时间范围
 */
export interface TimeRange {
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
}

/**
 * 标记项接口
 * 用于在时间轴上显示特殊标记
 */
export interface MarkItem {
  /** 标记位置（分钟数） */
  position: number;
  /** 标记类型 */
  type: 'warning' | 'event' | 'pollution' | 'info';
  /** 标记标题 */
  title?: string;
  /** 标记描述 */
  description?: string;
  /** 标记颜色 */
  color?: string;
  /** 是否为活跃状态 */
  active?: boolean;
}

/**
 * 时间线组件属性接口
 */
export interface TimeLineProps {
  /** 当前时间值（分钟数，从0点开始计算） */
  timeValue: number;

  /** 当前日期值 */
  dateValue: Dayjs;

  /** 开始时间（可选，用于限制时间范围） */
  startTime?: Dayjs;

  /** 结束时间（可选，用于限制时间范围） */
  endTime?: Dayjs;

  /** 更新日期值的回调函数 */
  updateDateValue: (value: Dayjs) => void;

  /** 更新时间值的回调函数 */
  updateTimeValue: (value: number) => void;

  /** 获取所有列表数据的回调函数（可选） */
  getAllList?: (date: Dayjs) => void;

  /** 警告信息映射表（可选） */
  warnInfo?: Map<string, WarnInfoList>;

  /** 事件信息映射表（可选） */
  eventInfo?: Map<string, EventSchedulingBasicInfo[]>;

  /** 当前警告信息（可选） */
  currentWarnInfo?: WarnInfoList;

  /** 当前事件信息（可选） */
  currentEventInfo?: EventSchedulingBasicInfo[];

  /** 当前事件相关信息（可选） */
  currentEventRelatedInfo?: EventSchedulingRelatedInfo[];

  /** 是否显示日期选择器（默认为true） */
  showDate?: boolean;

  /** 是否显示实时时间标记（默认为true） */
  showRealtimeMark?: boolean;

  /** 是否自动播放 */
  autoPlay: boolean;

  /** 更新自动播放状态的回调函数（可选） */
  updateAutoPlay?: (autoPlay: boolean) => void;

  /** 可用时间范围列表（可选） */
  availableTimeRanges?: TimeRange[];

  /** 污染时间范围列表（可选） */
  pollutionTimeRanges?: JunctionQualityChangeSetting[];
}

/**
 * 秒计时器组件属性接口
 */
export interface SecondTimerProps {
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 每分钟完成时的回调函数 */
  onFinish?: () => void;
  /** 日期变化时的回调函数 */
  onDayChange?: () => void;
}

/**
 * 时间线日期选择器组件属性接口
 */
export interface TimeLineDatePickerProps {
  /** 当前选中的日期值 */
  value: Dayjs;
  /** 自定义样式（可选） */
  style?: React.CSSProperties;
  /** 警告信息映射表（可选） */
  warnInfo?: Map<string, WarnInfoList>;
  /** 事件信息映射表（可选） */
  eventInfo?: Map<string, EventSchedulingBasicInfo[]>;
  /** 日期变化回调函数 */
  onChange: (date: Dayjs | null) => void;
  /** 获取所有列表数据的回调函数（可选） */
  getAllList?: (date: Dayjs) => void;
}

/**
 * 时间滑块组件属性接口
 */
export interface TimeSliderProps {
  /** 基准日期 */
  date: Dayjs;
  /** 最新时间 */
  latestTime: Dayjs;
  /** 滑块标记配置 */
  marks: Record<number, any>;
  /** 当前值 */
  value: number;
  /** 最小滑块值（可选） */
  minSlider?: number;
  /** 最大滑块值（可选） */
  maxSlider?: number;
  /** 值变化回调（拖拽过程中） */
  onChange: (value: number) => void;
  /** 值变化完成回调（拖拽结束） */
  onAfterChange: (value: number) => void;
  /** 警告列表 */
  warnList: WarnInfoList;
  /** 事件列表 */
  eventList: EventSchedulingBasicInfo[];
  /** 事件相关列表 */
  eventRelatedList: EventSchedulingRelatedInfo[];
  /** 可用时间范围列表 */
  availableTimeRanges: TimeRange[];
  /** 污染物时间范围列表 */
  pollutantTimeRanges: JunctionQualityChangeSetting[];
}
