/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ExclamationCircleFilled } from '@ant-design/icons';
import { passwordRegex } from '@waterdesk/data/regex';
import { Button, Form, Input, Modal, Space } from 'antd';
import { useCallback, useEffect } from 'react';

export interface EditPasswordFormValues {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface EditPasswordModalProps {
  open: boolean;
  onClose: () => void;
  handleSave: (params: EditPasswordFormValues) => void;
  isNewPassword?: boolean;
  onResetPassword?: () => void;
}

export const EditPasswordModal = ({
  open,
  onClose,
  handleSave,
  isNewPassword = false,
  onResetPassword,
}: EditPasswordModalProps) => {
  const [form] = Form.useForm<EditPasswordFormValues>();
  const { confirm } = Modal;

  const onConfirm = useCallback(async () => {
    try {
      const values = await form.validateFields();
      handleSave(values);
    } catch {}
  }, [form, handleSave]);

  const handleResetConfirm = useCallback(() => {
    confirm({
      icon: <ExclamationCircleFilled />,
      content: '是否重置该用户密码？',
      onOk: onResetPassword,
    });
  }, [confirm, onResetPassword]);

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open, form]);

  const renderFooter = () => (
    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
      {onResetPassword ? (
        <Button onClick={handleResetConfirm}>重置密码</Button>
      ) : null}
      <Space>
        <Button
          type="primary"
          onClick={onConfirm}
        >
          确定
        </Button>
        <Button onClick={onClose}>取消</Button>
      </Space>
    </Space>
  );

  return (
    <Modal
      title="修改密码"
      open={open}
      onCancel={onClose}
      footer={renderFooter()}
      destroyOnHidden
    >
      <Form
        form={form}
        name="editPasswordForm"
        labelCol={{ span: 4 }}
        autoComplete="off"
      >
        {!isNewPassword && (
          <Form.Item
            label="登录密码"
            name="oldPassword"
            rules={[{ required: true, message: '请输入登录密码' }]}
          >
            <Input.Password autoComplete="new-password" />
          </Form.Item>
        )}

        <Form.Item
          label="新密码"
          name="newPassword"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 8, message: '密码至少为8位' },
            { pattern: passwordRegex, message: '密码必须包含字母、数字和符号' },
          ]}
        >
          <Input.Password autoComplete="new-password" />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请输入确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || value === getFieldValue('newPassword')) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致!'));
              },
            }),
          ]}
        >
          <Input.Password autoComplete="new-password" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

EditPasswordModal.displayName = 'EditPasswordModal';
