/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React from 'react';
import styled from 'styled-components';

const WatermarkImage = styled.img`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  pointer-events: none;
  opacity: 0.8;
  max-width: 200px;
  max-height: 100px;
`;

export interface WatermarkProps {
  /** 水印图片地址 */
  src: string;
  /** 图片 alt 属性 */
  alt?: string;
  /** 是否显示水印 */
  visible?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义 CSS 类名 */
  className?: string;
}

export const Watermark: React.FC<WatermarkProps> = ({
  src,
  alt = 'watermark',
  visible = true,
  style,
  className = 'app-logo',
}) => {
  if (!visible) {
    return null;
  }

  return (
    <WatermarkImage
      className={className}
      src={src}
      alt={alt}
      style={{ pointerEvents: 'none', ...style }}
    />
  );
};

Watermark.displayName = 'Watermark';
