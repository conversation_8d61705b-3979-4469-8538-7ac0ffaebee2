/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { default as OlMap } from 'ol/Map';
import ContextMenu, { Item } from 'ol-contextmenu';
import { ContextMenuManagerConfig, OLContextMenuItem } from './types';

/**
 * OpenLayers 地图上下文菜单管理类
 *
 * 用于管理地图右键菜单的创建、更新和销毁。
 * 该类与业务逻辑解耦，只负责菜单的基础功能。
 *
 * @example
 * ```typescript
 * // 创建菜单
 * const menu = new ContextMenuManager(map);
 * menu.initialize([
 *   { text: '选项1', callback: () => console.log('clicked') },
 *   { text: '选项2', children: [{ text: '子选项', callback: () => {} }] }
 * ]);
 *
 * // 更新菜单项
 * menu.updateMenuItems(newItems);
 *
 * // 控制显示/隐藏
 * menu.setVisible(false);
 *
 * // 销毁菜单
 * menu.dispose();
 * ```
 */
export class ContextMenuManager {
  private _map?: OlMap;

  private _contextMenu?: ContextMenu;

  /**
   * 创建地图上下文菜单管理器
   * @param map OpenLayers 地图实例
   */
  constructor(map: OlMap) {
    this._map = map;
  }

  /**
   * 将用户配置的菜单项转换为 OpenLayers 支持的格式
   * @param menuItems 用户配置的菜单项
   * @returns OpenLayers 格式的菜单项
   */
  private convertToMenuItems(menuItems: ContextMenuManagerConfig[]): Item[] {
    const olMenuItems: Item[] = [];

    menuItems.forEach((item, index) => {
      const menuItem: OLContextMenuItem = {
        text: item.text,
        callback: item.callback ? () => item.callback!() : undefined,
      };

      if (item.children?.length) {
        menuItem.items = this.convertToMenuItems(
          item.children,
        ) as OLContextMenuItem[];
      }

      olMenuItems.push(menuItem as Item);

      if (item.separator && index !== menuItems.length - 1) {
        olMenuItems.push('-');
      }
    });

    if (olMenuItems.length > 0 && olMenuItems[olMenuItems.length - 1] === '-') {
      olMenuItems.pop();
    }

    return olMenuItems;
  }

  /**
   * 初始化上下文菜单
   * @param menuItems 菜单项配置
   * @param options 配置选项
   * @param options.visible 是否显示菜单，默认为 true
   */
  initialize(
    menuItems: ContextMenuManagerConfig[],
    options?: { visible?: boolean },
  ) {
    if (!this._map) return;

    const items = this.convertToMenuItems(menuItems);

    this._contextMenu = new ContextMenu({
      defaultItems: false,
      items,
    });

    if (options?.visible !== false && items.length > 0) {
      this._map.addControl(this._contextMenu);
    }
  }

  /**
   * 更新菜单项
   * @param menuItems 新的菜单项配置
   */
  updateMenuItems(menuItems: ContextMenuManagerConfig[]) {
    if (!this._contextMenu || !this._map) return;

    // 先移除旧的控件
    this._map.removeControl(this._contextMenu);
    this._contextMenu.dispose();

    // 创建新的控件
    const items = this.convertToMenuItems(menuItems);
    this._contextMenu = new ContextMenu({
      defaultItems: false,
      items,
    });

    // 重新添加到地图
    this._map.addControl(this._contextMenu);
  }

  /**
   * 设置菜单的显示状态
   * @param visible true 显示菜单，false 隐藏菜单
   */
  setVisible(visible: boolean) {
    if (!this._contextMenu || !this._map) return;

    if (visible) {
      this._map.addControl(this._contextMenu);
    } else {
      this._map.removeControl(this._contextMenu);
    }
  }

  /**
   * 销毁上下文菜单，释放资源
   * 在组件卸载时应调用此方法
   */
  dispose() {
    if (this._contextMenu && this._map) {
      this._map.removeControl(this._contextMenu);
      this._contextMenu.dispose();
      this._contextMenu = undefined;
    }
  }
}
