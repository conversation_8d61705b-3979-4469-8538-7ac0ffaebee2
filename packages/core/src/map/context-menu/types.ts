/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { default as OlMap } from 'ol/Map';

/**
 * 表示右键菜单的配置
 */
export interface ContextMenuManagerConfig {
  /** 菜单项显示的文本 */
  text: string;
  /** 子菜单项 */
  children?: ContextMenuManagerConfig[];
  /** 点击菜单项时的回调函数 */
  callback?: () => void;
  /** 是否在此菜单项后添加分隔符 */
  separator?: boolean;
}

/**
 * OpenLayers 上下文菜单项的内部类型
 */
export type OLContextMenuItem = {
  text: string;
  items?: OLContextMenuItem[];
  callback?: (
    obj: Pick<IObjectItem, 'otype' | 'oname' | 'shape'>,
    map: OlMap,
  ) => void;
};
