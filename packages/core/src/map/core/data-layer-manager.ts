/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import {
  EventSchedulingBasicInfo,
  LevelType,
} from '@waterdesk/data/event-scheduling/basic-info';
import {
  GisMapLayerData,
  ImageArcGisLayerData,
  TileLayerData,
  WmtsLayerData,
} from '@waterdesk/data/gis-layer-data';
import {
  CustomLayerData,
  EngineeringLayerData,
  FeatureLayerData,
  LayerData,
  NetworkLayerData,
} from '@waterdesk/data/layer-data';
import { default as OlMap } from 'ol/Map';
import Overlay from 'ol/Overlay';
import {
  IMapLayer,
  MapCustomLayer,
  MapFeatureLayer,
  MapGisGroupLayer,
  MapImageArcGISLayer,
  MapNetworkLayer,
  MapTileLayer,
  MapWmtsLayer,
  ServiceUrlGetter,
} from '../layers';

/**
 * 图层创建配置接口
 */
export interface LayerCreationConfig {
  /** Token 刷新器 */
  tokenRefresher?: (
    tokenKey: string,
    url: string,
    params: Record<string, string>,
    refreshCallback?: () => void,
  ) => void;
  /** 服务 URL 获取器 */
  serviceUrlGetter?: ServiceUrlGetter;
  /** 是否在开发环境中跳过某些图层 */
  skipInDevelopment?: boolean;
  /** 是否启用动画覆盖层 */
  enableAnimatedOverlay?: boolean;
  /** 创建动画覆盖层的函数 */
  createOverlay?: (
    id: string,
    position: number[],
    color: string,
    icon: string,
    eventData: EventSchedulingBasicInfo,
    levelTypeData: LevelType[],
  ) => Overlay;
  /** 更新动画覆盖层的函数 */
  updateOverlay?: (
    overlay: Overlay,
    color: string,
    icon: string,
    eventData: EventSchedulingBasicInfo,
    levelTypeData: LevelType[],
  ) => void;
  /** 销毁动画覆盖层的函数 */
  disposeOverlay?: (overlay: Overlay) => void;
  /** 工程图层相关的投影信息 */
  projection?: string;
}

/**
 * 特殊图层类型定义
 */
export interface SpecialLayerCreator {
  /** 创建工程图层的函数 */
  createEngineeringLayer?: (
    layerData: EngineeringLayerData,
    map: OlMap,
    projection?: string,
  ) => IMapLayer;
}

/**
 * 图层可见性更新策略
 */
export type LayerVisibilityStrategy =
  | 'show-all-not-invisible' // 显示所有非隐藏图层（storm-app 策略）
  | 'show-only-in-scene'; // 只显示场景中的非隐藏图层（storm-web 策略）

/**
 * 数据图层管理器配置
 */
export interface DataLayerManagerConfig {
  /** 图层创建配置 */
  creationConfig: LayerCreationConfig;
  /** 特殊图层创建器 */
  specialLayerCreator?: SpecialLayerCreator;
  /** 图层可见性更新策略 */
  visibilityStrategy: LayerVisibilityStrategy;
}

/**
 * 数据图层管理器
 *
 * 负责数据展示相关图层的创建、管理、可见性控制和主题设置等核心功能。
 * 管理的图层包括：FeatureLayer, TileLayer, NetworkLayer, CustomLayer 等数据驱动的图层。
 * 通过配置项实现不同系统间的差异化需求。
 */
export class DataLayerManager {
  private _map?: OlMap;
  private _config: DataLayerManagerConfig;
  private _mapLayers: Array<IMapLayer> = [];

  constructor(config: DataLayerManagerConfig) {
    this._config = config;
  }

  /**
   * 设置地图实例
   */
  setMap(map: OlMap): void {
    this._map = map;
  }

  /**
   * 创建图层
   */
  createLayer(layerData: LayerData, viewId?: string): IMapLayer | null {
    if (!this._map) {
      console.warn('Map instance not set');
      return null;
    }

    const { creationConfig, specialLayerCreator } = this._config;

    if (layerData instanceof ImageArcGisLayerData) {
      return new MapImageArcGISLayer(layerData, this._map, {
        tokenRefresher: creationConfig.tokenRefresher,
      });
    }

    if (layerData instanceof WmtsLayerData) {
      return new MapWmtsLayer(layerData);
    }

    if (layerData instanceof TileLayerData) {
      return new MapTileLayer(layerData, {
        tokenRefresher: creationConfig.tokenRefresher,
      });
    }

    if (layerData instanceof GisMapLayerData) {
      return new MapGisGroupLayer(layerData, this._map, {
        tokenRefresher: creationConfig.tokenRefresher,
        skipInDevelopment: creationConfig.skipInDevelopment ?? false,
      });
    }

    if (layerData instanceof FeatureLayerData) {
      return new MapFeatureLayer(layerData, this._map);
    }

    if (layerData instanceof CustomLayerData) {
      const customConfig: {
        enableAnimatedOverlay: boolean;
        createOverlay?: (
          id: string,
          position: number[],
          color: string,
          icon: string,
          eventData: EventSchedulingBasicInfo,
          levelTypeData: LevelType[],
        ) => Overlay;
        updateOverlay?: (
          overlay: Overlay,
          color: string,
          icon: string,
          eventData: EventSchedulingBasicInfo,
          levelTypeData: LevelType[],
        ) => void;
        disposeOverlay?: (overlay: Overlay) => void;
      } = {
        enableAnimatedOverlay: creationConfig.enableAnimatedOverlay ?? false,
      };

      // 如果启用动画覆盖层，添加相关函数
      if (creationConfig.enableAnimatedOverlay) {
        customConfig.createOverlay = creationConfig.createOverlay;
        customConfig.updateOverlay = creationConfig.updateOverlay;
        customConfig.disposeOverlay = creationConfig.disposeOverlay;
      }

      return new MapCustomLayer(layerData, this._map, customConfig);
    }

    if (
      layerData instanceof NetworkLayerData &&
      viewId &&
      creationConfig.serviceUrlGetter
    ) {
      return new MapNetworkLayer(layerData, viewId, this._map, {
        serviceUrlGetter: creationConfig.serviceUrlGetter,
      });
    }

    if (
      layerData instanceof EngineeringLayerData &&
      specialLayerCreator?.createEngineeringLayer
    ) {
      return specialLayerCreator.createEngineeringLayer(
        layerData,
        this._map,
        creationConfig.projection,
      );
    }

    return null;
  }

  /**
   * 添加图层到地图并管理
   */
  addLayer(
    layerData: LayerData,
    viewId?: string,
    defaultTheme?: Theme,
  ): IMapLayer | null {
    const mapLayer = this.createLayer(layerData, viewId);

    if (mapLayer?.layer && this._map) {
      if (defaultTheme) {
        mapLayer.setTheme(defaultTheme);
      }
      this._map.addLayer(mapLayer.layer);
      this._mapLayers.push(mapLayer);
    }

    return mapLayer;
  }

  /**
   * 移除图层
   */
  removeLayer(layerName: string): void {
    const index = this._mapLayers.findIndex(
      (layer) => layer.name === layerName,
    );
    if (index >= 0) {
      const layer = this._mapLayers[index];
      if (layer.layer && this._map) {
        this._map.removeLayer(layer.layer);
      }
      layer.dispose?.();
      this._mapLayers.splice(index, 1);
    }
  }

  /**
   * 更新图层可见性
   */
  updateLayersVisible(
    invisibleLayers: Set<string>,
    currentSceneAllLayers: string[],
  ): void {
    this._mapLayers.forEach((mapLayer) => {
      if (invisibleLayers.has(mapLayer.name)) {
        mapLayer.setVisible(false);
      } else {
        // 根据策略决定是否显示图层
        const shouldShow =
          this._config.visibilityStrategy === 'show-all-not-invisible'
            ? true
            : currentSceneAllLayers.includes(mapLayer.name);
        mapLayer.setVisible(shouldShow);
      }
    });
  }

  /**
   * 设置单个图层可见性
   */
  setLayerVisible(layerName: string, visible: boolean): void {
    const foundLayer = this._mapLayers.find(({ name }) => name === layerName);
    if (foundLayer) {
      foundLayer.layer?.setVisible(visible);
    }
  }

  /**
   * 设置所有图层主题
   */
  setTheme(theme: Theme): void {
    this._mapLayers.forEach((layer) => layer.setTheme(theme));
  }

  /**
   * 重绘所有图层
   */
  redraw(): void {
    this._mapLayers.forEach((layer) => layer.redraw());
  }

  /**
   * 获取所有图层
   */
  getLayers(): Array<IMapLayer> {
    return [...this._mapLayers];
  }

  /**
   * 根据名称查找图层
   */
  findLayer(layerName: string): IMapLayer | undefined {
    return this._mapLayers.find((layer) => layer.name === layerName);
  }

  /**
   * 根据类型查找图层
   */
  findLayersByType<T extends IMapLayer>(
    layerType: new (...args: any[]) => T,
  ): T[] {
    return this._mapLayers.filter((layer) => layer instanceof layerType) as T[];
  }

  /**
   * 清理所有图层
   */
  dispose(): void {
    this._mapLayers.forEach((layer) => {
      if (layer.layer && this._map) {
        this._map.removeLayer(layer.layer);
      }
      layer.dispose?.();
    });

    this._mapLayers = [];
    this._map = undefined;
  }
}
