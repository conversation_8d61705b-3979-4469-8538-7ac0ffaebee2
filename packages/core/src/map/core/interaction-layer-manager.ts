/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HighlightObject } from '@waterdesk/data/highlight-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import { HighlightStyle } from '@waterdesk/data/style-config';
import { default as OlMap } from 'ol/Map';
import { StyleLike } from 'ol/style/Style';
import { MapHighlightLayerConfig } from '../layers/map-highlight-layer';
import {
  MapHighlightLayerGroup,
  MapHighlightLayerGroupConfig,
} from '../layers/map-highlight-layer-group';
import { MapHoverLayer, MapHoverLayerConfig } from '../layers/map-hover-layer';

/**
 * 创建特征样式适配器的函数类型
 */
export type FeatureStyleAdapterCreator = (
  styleType: string,
  config: { [key: string]: unknown },
) => {
  generateObjectStyle: (
    item: HighlightObject | IObjectItem,
  ) => StyleLike | undefined;
};

/**
 * 交互图层管理器配置
 */
export interface InteractionLayerManagerConfig {
  /** 高亮样式配置 */
  highlightStyle: HighlightStyle;
  /** 悬停样式配置 */
  hoverStyle: HighlightStyle;
  /** 特征样式适配器创建函数 */
  featureStyleCreator: FeatureStyleAdapterCreator;
}

/**
 * 交互图层管理器
 *
 * 负责管理地图上的用户交互相关图层，包括：
 * - 悬停图层（HoverLayer）- 鼠标悬停效果
 * - 高亮图层（HighlightLayer）- 手动高亮效果
 * - 高亮图层组（HighlightLayerGroup）- 批量高亮管理
 * - 选择图层（SelectLayer）- 用户选中状态
 * - 编辑图层（EditLayer）- 地图编辑功能
 *
 * 这个类将交互图层的创建、管理和销毁逻辑抽取出来，
 * 避免在不同系统中重复实现相同的交互图层管理代码。
 */
export class InteractionLayerManager {
  private _map?: OlMap;
  private _config?: InteractionLayerManagerConfig;

  // 交互图层实例
  private _hoverLayer?: MapHoverLayer;
  private _highlightLayer?: MapHoverLayer;
  private _highlightLayerGroup?: MapHighlightLayerGroup;

  /**
   * 初始化图层管理器
   * @param map OpenLayers地图实例
   * @param config 图层管理器配置
   */
  initialize(map: OlMap, config: InteractionLayerManagerConfig) {
    this._map = map;
    this._config = config;

    this.createBusinessLayers();
  }

  /**
   * 创建业务图层
   */
  private createBusinessLayers() {
    if (!this._map || !this._config) return;

    // 配置高亮图层
    const highlightConfig: MapHighlightLayerConfig = {
      highlightStyle: this._config.highlightStyle,
      featureStyleCreator: this._config.featureStyleCreator,
    };

    // 配置悬停图层
    const hoverConfig: MapHoverLayerConfig = {
      hoverStyle: this._config.hoverStyle,
      featureStyleCreator: this._config.featureStyleCreator,
    };

    // 创建悬停和高亮图层
    this._hoverLayer = new MapHoverLayer(this._map, hoverConfig);
    this._highlightLayer = new MapHoverLayer(this._map, hoverConfig);

    // 配置高亮图层组
    const highlightGroupConfig: MapHighlightLayerGroupConfig = {
      highlightLayerConfig: highlightConfig,
    };

    this._highlightLayerGroup = new MapHighlightLayerGroup(
      this._map,
      highlightGroupConfig,
    );
  }

  /**
   * 将图层添加到地图
   */
  addLayersToMap() {
    if (!this._map) return;

    if (this._highlightLayerGroup?.layer) {
      this._map.addLayer(this._highlightLayerGroup.layer);
    }
    if (this._highlightLayer?.layer) {
      this._map.addLayer(this._highlightLayer.layer);
    }
    if (this._hoverLayer?.layer) {
      this._map.addLayer(this._hoverLayer.layer);
    }
  }

  /**
   * 获取悬停图层
   */
  get hoverLayer(): MapHoverLayer | undefined {
    return this._hoverLayer;
  }

  /**
   * 获取高亮图层
   */
  get highlightLayer(): MapHoverLayer | undefined {
    return this._highlightLayer;
  }

  /**
   * 获取高亮图层组
   */
  get highlightLayerGroup(): MapHighlightLayerGroup | undefined {
    return this._highlightLayerGroup;
  }

  /**
   * 销毁所有图层
   */
  dispose() {
    // 清理高亮图层组
    if (this._highlightLayerGroup) {
      this._highlightLayerGroup.dispose();
      this._highlightLayerGroup.layer?.dispose();
      this._highlightLayerGroup = undefined;
    }

    // 清理悬停图层
    if (this._hoverLayer) {
      this._hoverLayer.dispose();
      this._hoverLayer.layer?.dispose();
      this._hoverLayer = undefined;
    }

    // 清理高亮图层
    if (this._highlightLayer) {
      this._highlightLayer.dispose();
      this._highlightLayer.layer?.dispose();
      this._highlightLayer = undefined;
    }

    this._map = undefined;
    this._config = undefined;
  }
}
