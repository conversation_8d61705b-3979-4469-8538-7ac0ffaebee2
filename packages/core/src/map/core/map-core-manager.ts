/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { View } from 'ol';
import { MousePosition, ScaleLine } from 'ol/control';
import { Coordinate, createStringXY } from 'ol/coordinate';
import { Extent } from 'ol/extent';
import { defaults } from 'ol/interaction/defaults';
import { default as OlMap } from 'ol/Map';
import { Size } from 'ol/size';

export interface MapCoreConfig {
  target?: HTMLElement | string;
  projection?: string;
  mapZoomFactor?: number;
  enableDoubleClickZoom?: boolean;
  enableRotation?: boolean;
  showScaleLine?: boolean;
  showMousePosition?: boolean;
}

/**
 * 地图核心管理器
 *
 * 负责地图的基础初始化和控制功能，不包含任何业务逻辑。
 * 这个类可以被不同的业务系统复用。
 */
export class MapCoreManager {
  private _map?: OlMap;

  private _view?: View;

  private _extent: Array<number> = [0, 0, 0, 0];

  private _initialZoom: number = 1;

  /**
   * 初始化地图
   * @param config 地图配置
   */
  initialize(config: MapCoreConfig) {
    const {
      target,
      projection = 'EPSG:3857',
      mapZoomFactor,
      enableDoubleClickZoom = false,
      enableRotation = false,
      showScaleLine = true,
      showMousePosition = true,
    } = config;

    // 创建视图
    this._view = new View({
      center: [0, 0],
      zoom: 1,
      projection,
      zoomFactor: mapZoomFactor,
      enableRotation,
    });

    // 创建地图
    this._map = new OlMap({
      layers: [],
      target,
      view: this._view,
      interactions: defaults({
        doubleClickZoom: enableDoubleClickZoom,
      }),
    });

    // 添加基础控件
    if (showScaleLine) {
      this._map.addControl(
        new ScaleLine({
          units: 'metric',
        }),
      );
    }

    if (showMousePosition) {
      this._map.addControl(
        new MousePosition({
          coordinateFormat: createStringXY(4),
        }),
      );
    }
  }

  /**
   * 设置地图显示范围
   * @param extent 范围数组 [minX, minY, maxX, maxY]
   */
  setExtent(extent: Array<number>) {
    if (extent.length !== 0) {
      this._view?.fit(extent, {
        callback: () => {
          this._view?.setMinZoom((this._view?.getZoom() ?? 1) * 0.9);
        },
      });
    }
    this._extent = extent;
    this._initialZoom = this._view?.getZoom() ?? 1;
  }

  /**
   * 缩放到初始范围
   */
  fitToExtent() {
    this._view?.fit(this._extent);
  }

  /**
   * 设置地图目标容器
   * @param target 目标 DOM 元素或选择器
   */
  setTarget(target?: HTMLElement | string) {
    this._map?.setTarget(target);
  }

  /**
   * 动画方式移动到指定位置
   * @param center 中心点坐标
   * @param zoom 缩放级别
   */
  animateTo(center: number[], zoom: number) {
    this._view?.animate({ center, zoom });
  }

  /**
   * 根据形状中心点移动地图
   * @param center 中心点坐标
   */
  moveToCenter(center: number[]) {
    this.animateTo(center, this._initialZoom * 1.4);
  }

  /**
   * 缩放到指定范围
   * @param extent 范围
   */
  fitToGeometry(extent: Extent) {
    this._view?.fit(extent);
  }

  /**
   * 设置视图
   * @param view OpenLayers 视图实例
   */
  setView(view: View) {
    this._view = view;
    this._map?.setView(view);
  }

  /**
   * 获取当前缩放级别
   */
  getZoom(): number | undefined {
    return this._view?.getZoom();
  }

  /**
   * 设置缩放级别
   * @param zoom 缩放级别
   */
  setZoom(zoom: number) {
    this._view?.setZoom(zoom);
  }

  /**
   * 获取当前中心点
   */
  getCenter(): Coordinate | undefined {
    return this._view?.getCenter();
  }

  /**
   * 设置中心点
   * @param center 中心点坐标
   */
  setCenter(center: Coordinate) {
    this._view?.setCenter(center);
  }

  /**
   * 获取当前视图范围
   */
  getCurrentExtent(): Extent | undefined {
    return this._view?.calculateExtent();
  }

  /**
   * 获取地图尺寸
   */
  getSize(): Size | undefined {
    return this._map?.getSize();
  }

  /**
   * 获取地图投影
   */
  getProjection(): string | undefined {
    return this._view?.getProjection()?.getCode();
  }

  /**
   * 计算指定尺寸的视图范围
   * @param size 尺寸 [width, height]
   */
  calculateExtentBySize(size: Size): Extent | undefined {
    return this._view?.calculateExtent(size);
  }

  /**
   * 获取分辨率
   */
  getResolution(): number | undefined {
    return this._view?.getResolution();
  }

  /**
   * 设置分辨率
   * @param resolution 分辨率
   */
  setResolution(resolution: number) {
    this._view?.setResolution(resolution);
  }

  /**
   * 添加地图事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  addEventListener(type: string, listener: (event: unknown) => void) {
    this._map?.on(type as never, listener);
  }

  /**
   * 移除地图事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(type: string, listener: (event: unknown) => void) {
    this._map?.un(type as never, listener);
  }

  /**
   * 刷新地图
   */
  refresh() {
    this._map?.render();
  }

  /**
   * 设置最小缩放级别
   * @param minZoom 最小缩放级别
   */
  setMinZoom(minZoom: number) {
    this._view?.setMinZoom(minZoom);
  }

  /**
   * 设置最大缩放级别
   * @param maxZoom 最大缩放级别
   */
  setMaxZoom(maxZoom: number) {
    this._view?.setMaxZoom(maxZoom);
  }

  /**
   * 销毁地图，释放资源
   */
  dispose() {
    if (this._map) {
      this._map.dispose();
      this._map = undefined;
    }
    this._view = undefined;
    this._extent = [0, 0, 0, 0];
    this._initialZoom = 1;
  }

  /**
   * 获取地图实例
   */
  get map(): OlMap | undefined {
    return this._map;
  }

  /**
   * 获取视图实例
   */
  get view(): View | undefined {
    return this._view;
  }

  /**
   * 获取当前范围
   */
  get extent(): Array<number> {
    return this._extent;
  }

  /**
   * 获取初始缩放级别
   */
  get initialZoom(): number {
    return this._initialZoom;
  }
}
