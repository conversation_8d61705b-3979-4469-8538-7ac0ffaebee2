/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MOUSE_SELECT, MouseMode } from '@waterdesk/data/ui-types';
import lodash from 'lodash';
import { MapBrowserEvent } from 'ol';
import Feature, { FeatureLike } from 'ol/Feature';
import Layer from 'ol/layer/Layer';
import { default as OlMap } from 'ol/Map';

/**
 * 地图事件处理器配置
 */
export interface MapEventHandlerConfig {
  /**
   * 点击事件回调
   */
  onMapClick?: (event: MapBrowserEvent<PointerEvent>) => Promise<void>;

  /**
   * 指针移动事件回调
   */
  onPointerMove?: (event: MapBrowserEvent<PointerEvent>) => void;

  /**
   * 地图移动结束事件回调
   */
  onMoveEnd?: () => void;

  /**
   * 防抖延迟时间（毫秒）
   */
  debounceDelay?: number;
}

/**
 * 地图事件处理器接口
 */
export interface IMapEventHandler {
  /**
   * 初始化事件处理器
   */
  initialize(map: OlMap, config: MapEventHandlerConfig): void;

  /**
   * 销毁事件处理器
   */
  dispose(): void;

  /**
   * 设置鼠标模式
   */
  setMouseMode(mode: MouseMode): void;

  /**
   * 获取鼠标模式
   */
  getMouseMode(): MouseMode;

  /**
   * 设置鼠标是否在地图上
   */
  setMouseOnMap(onMap: boolean): void;

  /**
   * 获取鼠标是否在地图上
   */
  getMouseOnMap(): boolean;

  /**
   * 获取指定像素点的要素
   */
  getFeaturesAtPixel(
    pixel: number[],
    layerFilter?: (layer: Layer) => boolean,
  ): Feature[];
}

/**
 * 地图事件处理器实现
 */
export class MapEventHandler implements IMapEventHandler {
  private _map?: OlMap;
  private _config?: MapEventHandlerConfig;
  private _mouseMode: MouseMode = MOUSE_SELECT;
  private _mouseOnMap: boolean = true;
  private _debouncedPointerMove?: ReturnType<typeof lodash.debounce>;

  // 存储绑定的事件处理器引用
  private _boundClickHandler?: (event: any) => void;
  private _boundMoveEndHandler?: () => void;

  initialize(map: OlMap, config: MapEventHandlerConfig): void {
    this._map = map;
    this._config = config;

    // 创建防抖的指针移动处理函数
    this._debouncedPointerMove = lodash.debounce((event) => {
      if (
        event instanceof MapBrowserEvent &&
        event.originalEvent instanceof PointerEvent
      ) {
        this.handlePointerMove(event as MapBrowserEvent<PointerEvent>);
      }
    }, config.debounceDelay ?? 80);

    // 创建绑定的事件处理器引用
    this._boundClickHandler = (event) => {
      if (
        event instanceof MapBrowserEvent &&
        event.originalEvent instanceof PointerEvent
      ) {
        this.handleClick(event as MapBrowserEvent<PointerEvent>);
      }
    };

    this._boundMoveEndHandler = () => this.handleMoveEnd();

    // 绑定事件
    this._map.on('click', this._boundClickHandler);
    this._map.on('pointermove', this._debouncedPointerMove);
    this._map.on('moveend', this._boundMoveEndHandler);
  }

  dispose(): void {
    if (this._map) {
      // 移除事件监听器，使用存储的函数引用
      if (this._boundClickHandler) {
        this._map.un('click', this._boundClickHandler);
      }
      if (this._debouncedPointerMove) {
        this._map.un('pointermove', this._debouncedPointerMove);
      }
      if (this._boundMoveEndHandler) {
        this._map.un('moveend', this._boundMoveEndHandler);
      }
    }

    this._map = undefined;
    this._config = undefined;
    this._debouncedPointerMove = undefined;
    this._boundClickHandler = undefined;
    this._boundMoveEndHandler = undefined;
  }

  setMouseMode(mode: MouseMode): void {
    this._mouseMode = mode;
  }

  getMouseMode(): MouseMode {
    return this._mouseMode;
  }

  setMouseOnMap(onMap: boolean): void {
    this._mouseOnMap = onMap;
  }

  getMouseOnMap(): boolean {
    return this._mouseOnMap;
  }

  getFeaturesAtPixel(
    pixel: number[],
    layerFilter?: (layer: Layer) => boolean,
  ): Feature[] {
    if (!this._map) return [];

    const features: Feature[] = [];
    this._map.forEachFeatureAtPixel(
      pixel,
      (f: FeatureLike) => {
        if (f instanceof Feature) {
          features.push(f);
        }
      },
      {
        layerFilter:
          layerFilter ?? ((layer) => !layer.getProperties().undetectable),
      },
    );

    return features;
  }

  private handleClick = async (event: MapBrowserEvent<PointerEvent>) => {
    if (
      !this._mouseOnMap ||
      this._mouseMode !== MOUSE_SELECT ||
      !this._config?.onMapClick
    ) {
      return;
    }

    await this._config.onMapClick(event);
  };

  private handlePointerMove = (event: MapBrowserEvent<PointerEvent>) => {
    if (
      !this._map ||
      this._mouseMode !== MOUSE_SELECT ||
      !this._config?.onPointerMove
    ) {
      return;
    }

    this._config.onPointerMove(event);
  };

  private handleMoveEnd = () => {
    if (!this._config?.onMoveEnd) return;
    this._config.onMoveEnd();
  };
}
