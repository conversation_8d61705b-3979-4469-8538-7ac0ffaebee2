/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// 导出右键菜单管理器相关
export {
  ContextMenuManager,
  type ContextMenuManagerConfig,
} from './context-menu';
export type { OLContextMenuItem } from './context-menu/types';
// 导出数据图层管理器相关
export {
  DataLayerManager,
  type DataLayerManagerConfig,
  type LayerCreationConfig,
  type LayerVisibilityStrategy,
  type SpecialLayerCreator,
} from './core/data-layer-manager';
// 导出交互图层管理器相关
export {
  type FeatureStyleAdapterCreator,
  InteractionLayerManager,
  type InteractionLayerManagerConfig,
} from './core/interaction-layer-manager';
// 导出地图核心管理器相关
export {
  type MapCoreConfig,
  MapCoreManager,
} from './core/map-core-manager';
// 导出地图事件处理器相关
export {
  type IMapEventHandler,
  MapEventHandler,
  type MapEventHandlerConfig,
} from './core/map-event-handler';
// 导出图层相关
export * from './layers';
export {
  MapHighlightLayer,
  type MapHighlightLayerConfig,
} from './layers/map-highlight-layer';
export {
  MapHighlightLayerGroup,
  type MapHighlightLayerGroupConfig,
} from './layers/map-highlight-layer-group';
export {
  MapHoverLayer,
  type MapHoverLayerConfig,
} from './layers/map-hover-layer';
// 导出投影管理器相关
export {
  convertLegacyProjectionData,
  ProjectionManager,
  registerProjections,
} from './utils/projection-manager';
