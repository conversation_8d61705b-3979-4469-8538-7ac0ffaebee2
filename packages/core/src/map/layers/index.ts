/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// 导出具体图层实现
export { MapArcgisTileLayer } from './map-arcgis-tile-layer';
export {
  MapCustomLayer,
  type MapCustomLayerConfig,
} from './map-custom-layer';
export { MapFeatureLayer } from './map-feature-layer';
export {
  default as MapGisGroupLayer,
  type MapGisGroupLayerConfig,
} from './map-gis-group-layer';
export {
  MapImageArcGISLayer,
  type MapImageArcGISLayerConfig,
} from './map-image-arcgis-layer';
export {
  MapNetworkLayer,
  type MapNetworkLayerConfig,
} from './map-network-layer';
export { MapTileLayer, type MapTileLayerConfig } from './map-tile-layer';
export { MapWmtsLayer } from './map-wmts-layer';
// 导出图层类型和接口
export * from './types';
