/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { ArcgisTileLayerData } from '@waterdesk/data/gis-layer-data';
import { getMapLayerUrl, parseToNumberArray } from '@waterdesk/data/layer-data';
import BaseLayer from 'ol/layer/Base';
import TileLayer from 'ol/layer/Tile';
import TileArcGISRest from 'ol/source/TileArcGISRest';
import TileGrid from 'ol/tilegrid/TileGrid';
import { IMapLayer } from './types';

export class MapArcgisTileLayer implements IMapLayer {
  private _name: string;

  private _layer: TileLayer<TileArcGISRest> | null = null;

  private _layerData: ArcgisTileLayerData;

  constructor(layerData: ArcgisTileLayerData) {
    this._name = 'TileLayer';
    this._layerData = layerData;
    this.initialize();
  }

  private initialize() {
    const tileGrid = this._layerData.layerParams?.tileGrid
      ? new TileGrid({
          ...this._layerData.layerParams?.tileGrid,
          origin: this._layerData.layerParams?.tileGrid.origin,
          resolutions: parseToNumberArray(
            this._layerData.layerParams?.tileGrid.resolutions,
          ),
          extent: this._layerData.layerParams?.extent,
        })
      : undefined;

    const source = new TileArcGISRest({
      url: getMapLayerUrl(
        this._layerData.layerParams?.url,
        this._layerData.layerParams?.urlConfig,
      ),
      tileGrid,
      projection: this._layerData.layerParams?.projection,
    });

    this._layer = new TileLayer({
      source,
      opacity: this._layerData.layerParams?.opacity,
      minZoom: this._layerData.minZoom,
      maxZoom: this._layerData.maxZoom,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._layer) {
      // 移除图层的source
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }

      // 移除图层本身
      this._layer.dispose();
      this._layer = null;
    }
  }
}
