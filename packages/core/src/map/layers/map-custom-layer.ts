/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import {
  EventLayerName,
  EventSchedulingBasicInfo,
  getEventLevelColor,
  LevelType,
} from '@waterdesk/data/event-scheduling/basic-info';
import { CustomLayerData } from '@waterdesk/data/layer-data';
import { getCurrentDeviceColorData } from '@waterdesk/data/legend-data';
import { makeObjectId, validateShape } from '@waterdesk/data/object-item';
import { Feature, Map as OlMap } from 'ol';
import WKT from 'ol/format/WKT';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import Overlay from 'ol/Overlay';
import VectorSource from 'ol/source/Vector';
import { IMapLayer } from './types';

// 预定义的事件级别颜色，如果配置中没有颜色则使用这些默认值
const DEFAULT_EVENT_LEVEL_COLORS: Record<number, string> = {
  1: '#0000FF', // 蓝色 - 一级事件
  2: '#FFFF00', // 黄色 - 二级事件
  3: '#FFA500', // 橙色 - 三级事件
  4: '#FF0000', // 红色 - 四级事件
};

// 颜色名称到十六进制色值的映射
const COLOR_NAME_MAP: Record<string, string> = {
  red: '#FF0000',
  green: '#008000',
  blue: '#0000FF',
  yellow: '#FFFF00',
  orange: '#FFA500',
  purple: '#800080',
  black: '#000000',
  white: '#FFFFFF',
  gray: '#808080',
  brown: '#A52A2A',
  pink: '#FFC0CB',
  cyan: '#00FFFF',
  magenta: '#FF00FF',
  lime: '#00FF00',
  teal: '#008080',
  navy: '#000080',
  olive: '#808000',
  maroon: '#800000',
};

// 颜色缓存，避免重复转换
const colorCache = new Map<string, string>();

/**
 * 将颜色名称或其他格式转换为十六进制颜色
 * @param color 输入的颜色值
 * @returns 十六进制颜色值
 */
function convertToHexColor(color: string): string {
  if (!color || color === '') {
    return '#000000';
  }

  // 检查缓存
  if (colorCache.has(color)) {
    return colorCache.get(color)!;
  }

  let hexColor = color;

  // 如果已经是十六进制格式，直接返回
  if (/^#[0-9A-Fa-f]{6}$/.test(color)) {
    colorCache.set(color, hexColor);
    return hexColor;
  }

  // 尝试从颜色名称映射表获取
  const lowercaseColor = color.toLowerCase();
  if (COLOR_NAME_MAP[lowercaseColor]) {
    hexColor = COLOR_NAME_MAP[lowercaseColor];
  } else {
    // 如果无法识别，使用默认颜色
    hexColor = '#000000';
  }

  // 将结果存入缓存
  colorCache.set(color, hexColor);

  return hexColor;
}

/**
 * 动画覆盖层配置接口
 */
export interface MapCustomLayerConfig {
  /** 是否启用动画覆盖层 */
  enableAnimatedOverlay?: boolean;
  /** 自定义覆盖层创建函数 */
  createOverlay?: (
    id: string,
    position: number[],
    color: string,
    icon: string,
    eventData: EventSchedulingBasicInfo,
    levelTypeData: LevelType[],
  ) => Overlay;
  /** 自定义覆盖层更新函数 */
  updateOverlay?: (
    overlay: Overlay,
    color: string,
    icon: string,
    eventData: EventSchedulingBasicInfo,
    levelTypeData: LevelType[],
  ) => void;
  /** 自定义覆盖层销毁函数 */
  disposeOverlay?: (overlay: Overlay) => void;
}

export class MapCustomLayer implements IMapLayer {
  layerData: CustomLayerData;

  private _levelTypeData: LevelType[] = [];

  private _map: OlMap;

  private _name: string;

  private _source: VectorSource | null = null;

  private _layer: VectorLayer<VectorSource> | null = null;

  private _iconLayer: VectorLayer<VectorSource> | null = null;

  private _displayFeatures: Feature[] = [];

  private _featureLookup: Map<string, Feature> = new Map();

  private _overlayLookup: Map<string, Overlay> = new Map();

  private _WKT: WKT = new WKT();

  private _config?: MapCustomLayerConfig;

  constructor(
    layerData: CustomLayerData,
    map: OlMap,
    config?: MapCustomLayerConfig,
  ) {
    this.layerData = layerData;
    this._name = layerData.name;
    this._map = map;
    this._config = config;
    this.initialize();
  }

  private initialize() {
    this._source = new VectorSource({});
    this._layer = new VectorLayer({
      source: this._source,
      style: () => [], // 返回空样式数组，不渲染任何内容
    });

    this._iconLayer = new VectorLayer({
      source: new VectorSource({}),
      zIndex: 0,
      style: () => [], // 返回空样式数组，不渲染任何内容
    });

    this._map.addLayer(this._iconLayer);
  }

  get name(): string {
    return this._name;
  }

  get displayFeatures(): Feature[] {
    return this._displayFeatures;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  clearCustomData() {
    this._overlayLookup.forEach((overlay) => {
      this._map.removeOverlay(overlay);
      if (this._config?.disposeOverlay) {
        this._config.disposeOverlay(overlay);
      }
    });

    this._overlayLookup.clear();
    this._featureLookup.clear();

    if (this._source) {
      this._source.clear();
    }

    if (this._iconLayer?.getSource()) {
      this._iconLayer.getSource()?.clear();
    }
  }

  updateCustomData(data: EventSchedulingBasicInfo[]) {
    // 清空现有数据
    this.clearCustomData();
    // 添加新数据
    this.addCustomData(data, this._levelTypeData);
  }

  addCustomData(
    data: EventSchedulingBasicInfo[],
    levelTypeData: LevelType[] = [],
  ) {
    this._levelTypeData = levelTypeData;

    data.forEach((event) => {
      if (validateShape(event.shape)) {
        const feature = this._WKT.readFeature(event.shape);
        feature.set('eventId', event.eventId, true);
        feature.set('oname', event.oname, true);
        feature.set('otype', event.otype, true);
        feature.setProperties(event);

        let color = getEventLevelColor(levelTypeData, event.eventLevel);

        if (!color || color === '') {
          color =
            DEFAULT_EVENT_LEVEL_COLORS[event.eventLevel] ||
            this.layerData.color ||
            '#ff0000';
        }

        feature.set('_color', color, true);

        const isContingencyEvent = event.emergencyEvent;
        const layerMatchesEvent =
          (isContingencyEvent &&
            this.layerData.name === EventLayerName.MGT_CONTINGENCY_EVENT) ||
          (!isContingencyEvent &&
            this.layerData.name === EventLayerName.MGT_EVENT);

        if (layerMatchesEvent) {
          const objId = makeObjectId(this._name, event.eventId);
          this._featureLookup.set(objId, feature);

          if (this._iconLayer?.getSource()) {
            this._iconLayer.getSource()?.addFeature(feature);
          }

          if (
            this._layer?.getVisible() &&
            this._config?.enableAnimatedOverlay !== false
          ) {
            this.createOrUpdateOverlay(objId, feature, color);
          }
        }
      }
    });

    this.redraw();
  }

  private createOrUpdateOverlay(id: string, feature: Feature, color: string) {
    if (this._config?.enableAnimatedOverlay === false) {
      return;
    }

    const hexColor = convertToHexColor(color);

    const geometry = feature.getGeometry();
    const position = geometry ? geometry.getExtent() : undefined;

    if (!position) {
      return;
    }

    const eventData = feature.getProperties() as EventSchedulingBasicInfo;

    const centerX = (position[0] + position[2]) / 2;
    const centerY = (position[1] + position[3]) / 2;

    let overlay = this._overlayLookup.get(id);

    if (overlay) {
      overlay.setPosition([centerX, centerY]);
      if (this._config?.updateOverlay) {
        this._config.updateOverlay(
          overlay,
          hexColor,
          this.layerData.icon,
          eventData,
          this._levelTypeData,
        );
      }
    } else if (this._config?.createOverlay) {
      overlay = this._config.createOverlay(
        id,
        [centerX, centerY],
        hexColor,
        this.layerData.icon,
        eventData,
        this._levelTypeData,
      );

      const element = overlay.getElement();
      if (element) {
        element.addEventListener('mouseenter', () => {
          if (overlay) {
            this._map.removeOverlay(overlay);
            this._map.addOverlay(overlay);
          }
        });
      }

      this._map.addOverlay(overlay);
      this._overlayLookup.set(id, overlay);
    }
  }

  redraw(): void {
    if (!this._layer?.getVisible()) {
      return;
    }

    this._source?.clear();

    if (this._iconLayer?.getSource()) {
      this._iconLayer.getSource()?.clear();
    }

    const deviceColorData = getCurrentDeviceColorData();

    this._featureLookup.forEach((feature, id) => {
      let color = deviceColorData.get(id);

      if (!color || color === '') {
        color = feature.get('_color');
      }

      if (!color || color === '') {
        const eventLevel = feature.get('eventLevel');
        color =
          DEFAULT_EVENT_LEVEL_COLORS[eventLevel] ||
          this.layerData.color ||
          '#ff0000';
      }

      feature.set('_color', color, true);

      if (this._iconLayer?.getSource()) {
        this._iconLayer.getSource()?.addFeature(feature);
      }

      if (this._config?.enableAnimatedOverlay !== false) {
        this.createOrUpdateOverlay(id, feature, color);
      }
    });

    this._source?.addFeatures([...this._featureLookup.values()]);
  }

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
    this._iconLayer?.setVisible(visible);

    if (visible) {
      this._overlayLookup.forEach((overlay) => {
        this._map.removeOverlay(overlay);
        if (this._config?.disposeOverlay) {
          this._config.disposeOverlay(overlay);
        }
      });
      this._overlayLookup.clear();

      requestAnimationFrame(() => {
        if (this._config?.enableAnimatedOverlay !== false) {
          this._featureLookup.forEach((feature, id) => {
            const color = feature.get('_color');
            this.createOrUpdateOverlay(id, feature, color);
          });
        }

        this.redraw();
        this._map.updateSize();
      });
    } else {
      this._overlayLookup.forEach((overlay) => {
        this._map.removeOverlay(overlay);
      });
    }
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    this._overlayLookup.forEach((overlay) => {
      this._map.removeOverlay(overlay);
      if (this._config?.disposeOverlay) {
        this._config.disposeOverlay(overlay);
      }
    });

    this._overlayLookup.clear();

    colorCache.clear();

    if (this._layer) {
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }
      this._layer.dispose();
      this._layer = null;
    }

    if (this._iconLayer) {
      const source = this._iconLayer.getSource();
      if (source) {
        source.dispose();
      }
      this._iconLayer.dispose();
      this._iconLayer = null;
    }
  }
}
