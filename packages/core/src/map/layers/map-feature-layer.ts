/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { DeviceThemeData, getDeviceTheme } from '@waterdesk/data/device-theme';
import { FeatureLayerData, zoomFilter } from '@waterdesk/data/layer-data';
import { getCurrentDeviceColorData } from '@waterdesk/data/legend-data';
import { validateShape } from '@waterdesk/data/object-item';
import { Feature } from 'ol';
import WKT from 'ol/format/WKT';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import { toRadians } from 'ol/math';
import VectorSource from 'ol/source/Vector';
import { Circle, Fill, Style, Text } from 'ol/style';
import { IMapLayer } from './types';

export class MapFeatureLayer implements IMapLayer {
  private static generateStyle(iconName: string, rotation?: number): Style {
    return new Style({
      fill: new Fill({
        color: '#ffffff',
      }),
      image: new Circle({
        fill: new Fill({
          color: '#ffffff',
        }),
        radius: 6,
      }),
      text: new Text({
        font: '16px iconfont',
        text: eval(`'${iconName}'`),
        rotation: toRadians(rotation ?? 0),
      }),
    });
  }

  layerData: FeatureLayerData;

  private _map: OlMap;

  private _minZoom: number | undefined;

  private _maxZoom: number | undefined;

  private _name: string;

  private _source: VectorSource | null = null;

  private _layer: VectorLayer<VectorSource> | null = null;

  private _features: Feature[] = [];

  private _displayFeatures: Feature[] = [];

  private _featureLookup: Map<string, Feature> = new Map();

  private _WKT: WKT = new WKT();

  constructor(layerData: FeatureLayerData, map: OlMap) {
    this.layerData = layerData;
    this._name = layerData.name;
    this._map = map;
    this._minZoom = layerData.minZoom;
    this._maxZoom = layerData.maxZoom;
    this.initialize();
  }

  private initializeFeatures() {
    this.layerData.devices.forEach((device) => {
      // 如果存在shape,并且设备具有访问权限,则创建feature
      if (validateShape(device.shape) && device.dataAccess) {
        const feature = this._WKT.readFeature(device.shape);
        feature.set('id', device.id, true);
        feature.set('oname', device.oname, true);
        feature.set('otype', device.otype, true);
        feature.set('minViewRatio', device.minViewRatio, true);
        feature.set('maxViewRatio', device.maxViewRatio, true);
        feature.setStyle(
          MapFeatureLayer.generateStyle(device.icon, device.rotation),
        );

        this._featureLookup.set(device.id, feature);
        this._features.push(feature);
        if (zoomFilter(this._map, device.minViewRatio, device.maxViewRatio)) {
          this._displayFeatures.push(feature);
        }
      }
    });
  }

  private initialize() {
    this.initializeFeatures();

    this._source = new VectorSource({});
    this._layer = new VectorLayer({
      source: this._source,
      minZoom: this._minZoom,
      maxZoom: this._maxZoom,
    });
  }

  get name(): string {
    return this._name;
  }

  get displayFeatures(): Feature[] {
    return this._displayFeatures;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw(): void {
    if (!this._layer?.getVisible()) {
      return;
    }
    this._source?.clear();
    this._displayFeatures = [];
    const deviceColorData = getCurrentDeviceColorData();
    this._featureLookup.forEach((feature, deviceId) => {
      const properties = feature.getProperties();
      const deviceColor = deviceColorData.get(deviceId);
      if (
        zoomFilter(
          this._map,
          properties.minViewRatio,
          properties.maxViewRatio,
        ) &&
        deviceColor !== undefined
      ) {
        const deviceStyle = feature.getStyle();
        if (deviceStyle instanceof Style) {
          deviceStyle.getText()?.setFill(
            new Fill({
              color: deviceColor || '#000000',
            }),
          );
        }
        this._displayFeatures.push(feature);
      }
    });
    this._source?.addFeatures(this._displayFeatures);
  }

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  updateDeviceFeatureRatio(
    deviceTypeRatioData: Map<string, [number | undefined, number | undefined]>,
    deviceThemeData: DeviceThemeData,
  ) {
    this.layerData.devices.forEach((device) => {
      const deviceFeature = this._featureLookup.get(device.id);
      if (deviceFeature === undefined) return;
      const ratioConfigFromOtype = deviceTypeRatioData.get(device.otype);
      const deviceTheme = getDeviceTheme(device.id, deviceThemeData);
      let { minViewRatio, maxViewRatio } = device;
      const [minRatioFromOtype, maxRatioFromOtype] = ratioConfigFromOtype || [];
      const { minRatio, maxRatio } = deviceTheme || {};

      if (typeof minRatio === 'number') {
        minViewRatio = minRatio;
      } else if (typeof minRatioFromOtype === 'number') {
        minViewRatio = minRatioFromOtype;
      }
      if (typeof maxRatio === 'number') {
        maxViewRatio = maxRatio;
      } else if (typeof maxRatioFromOtype === 'number') {
        maxViewRatio = maxRatioFromOtype;
      }

      // reset if no new ratio setting
      deviceFeature.set('minViewRatio', minViewRatio, true);
      deviceFeature.set('maxViewRatio', maxViewRatio, true);
      device.setFeature(deviceFeature);
    });
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._source) {
      this._source.clear();
      this._source = null;
    }

    if (this._layer) {
      this._layer.dispose();
      this._layer = null;
    }

    this._features = [];
    this._displayFeatures = [];
    this._featureLookup.clear();
  }
}
