/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import {
  ArcgisTileLayerData,
  GisMapLayerData,
  ImageArcGisLayerData,
  TileLayerData,
  WmtsLayerData,
} from '@waterdesk/data/gis-layer-data';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import { default as OlMap } from 'ol/Map';
import { MapArcgisTileLayer } from './map-arcgis-tile-layer';
import { MapImageArcGISLayer } from './map-image-arcgis-layer';
import { MapTileLayer } from './map-tile-layer';
import { MapWmtsLayer } from './map-wmts-layer';
import { IMapLayer, TokenRefresher } from './types';

export interface MapGisGroupLayerConfig {
  /** Token 刷新器函数 */
  tokenRefresher?: TokenRefresher;
  /** 是否在开发环境中跳过某些图层类型 */
  skipInDevelopment?: boolean;
}

export default class MapGisGroupLayer implements IMapLayer {
  private _map: OlMap;
  private _name: string;
  private _layer: LayerGroup | null = null;
  private _layerData: GisMapLayerData;
  private _mapLayers: Map<string, IMapLayer> = new Map();
  private _config?: MapGisGroupLayerConfig;

  constructor(
    layerData: GisMapLayerData,
    map: OlMap,
    config?: MapGisGroupLayerConfig,
  ) {
    this._map = map;
    this._name = layerData.name;
    this._layerData = layerData;
    this._config = config;
    this.initialize();
  }

  private shouldSkipInDevelopment(): boolean {
    return (
      this._config?.skipInDevelopment === true &&
      process.env.NODE_ENV === 'development'
    );
  }

  private createMapLayer(
    layerData: unknown,
    themeName: string,
  ): IMapLayer | null {
    if (layerData instanceof ImageArcGisLayerData) {
      return new MapImageArcGISLayer(layerData, this._map, {
        tokenRefresher: this._config?.tokenRefresher,
      });
    }

    if (this.shouldSkipInDevelopment()) {
      return null;
    }

    if (layerData instanceof WmtsLayerData) {
      return new MapWmtsLayer(layerData);
    }

    if (layerData instanceof TileLayerData) {
      return new MapTileLayer(layerData, {
        tokenRefresher: this._config?.tokenRefresher,
      });
    }

    if (layerData instanceof ArcgisTileLayerData) {
      return new MapArcgisTileLayer(layerData);
    }

    return null;
  }

  private addLayerIfValid(
    mapLayer: IMapLayer | null,
    themeName: string,
    baseLayers: BaseLayer[],
  ): void {
    if (mapLayer?.layer) {
      this._mapLayers.set(themeName, mapLayer);
      baseLayers.push(mapLayer.layer);
    }
  }

  private initialize(): void {
    const baseLayers: BaseLayer[] = [];

    this._layerData.layerParams.forEach((layerData, themeName) => {
      const mapLayer = this.createMapLayer(layerData, themeName);
      this.addLayerIfValid(mapLayer, themeName, baseLayers);
    });

    this._layer = new LayerGroup({ layers: baseLayers });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw(): void {}

  setVisible(visible: boolean): void {
    this._layer?.setVisible(visible);
  }

  setTheme(theme: Theme): void {
    if (!this._map) return;

    if (this._mapLayers.has(theme)) {
      this._mapLayers.forEach((item, key) => {
        const visible = key === theme;
        item.setVisible(visible);
      });
    } else {
      this._mapLayers.forEach((item, key) => {
        const visible = key === 'default';
        item.setVisible(visible);
      });
    }
  }

  dispose(): void {
    this._mapLayers.forEach((layer) => {
      layer.dispose?.();
    });
    this._mapLayers.clear();

    if (this._layer) {
      this._layer.dispose?.();
      this._layer = null;
    }
  }
}
