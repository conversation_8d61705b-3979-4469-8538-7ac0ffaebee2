/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import LayerGroup from 'ol/layer/Group';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import {
  MapHighlightLayer,
  MapHighlightLayerConfig,
} from './map-highlight-layer';
import { IMapLayer } from './types';

export interface MapHighlightLayerGroupConfig {
  highlightLayerConfig: MapHighlightLayerConfig;
}

export class MapHighlightLayerGroup implements IMapLayer {
  private _map: OlMap;

  private _name: string;

  private _layer: LayerGroup = new LayerGroup();

  private _mapLayers: Map<string, MapHighlightLayer> = new Map();

  private _config: MapHighlightLayerGroupConfig;

  constructor(map: OlMap, config: MapHighlightLayerGroupConfig) {
    this._map = map;
    this._name = 'highlightGroup';
    this._config = config;
  }

  get name(): string {
    return this._name;
  }

  get layer(): LayerGroup {
    return this._layer;
  }

  dispose(): void {
    // 清除所有高亮对象
    this.clearAllHighlightObjects();
    // 清除所有图层
    this.clearHighlightObjects();
    // 销毁所有内部图层
    this._mapLayers.forEach((layer) => {
      layer.dispose?.();
    });
    // 清空引用
    this._mapLayers.clear();
  }

  setLayer(layerName: string, highlightLayer: MapHighlightLayer) {
    this._mapLayers.set(layerName, highlightLayer);
    // 使用新的 MapHighlightLayer 接口
    if (highlightLayer.layer) {
      this._layer.getLayers().push(highlightLayer.layer);
    }
  }

  clearAllHighlightObjects() {
    if (this._layer) {
      this._layer
        .getLayers()
        .getArray()
        .forEach((layer) => {
          if (layer instanceof LayerGroup) {
            // 对于 LayerGroup，需要遍历其子图层
            layer
              .getLayers()
              .getArray()
              .forEach((subLayer) => {
                if (subLayer instanceof VectorLayer) {
                  (subLayer as VectorLayer<VectorSource>).getSource()?.clear();
                }
              });
          } else if (layer instanceof VectorLayer) {
            (layer as VectorLayer<VectorSource>).getSource()?.clear();
          }
        });
    }
  }

  private generateHighlightLayer(
    layerName: string,
    highlightObjects: HighlightObject[],
  ): MapHighlightLayer {
    const highlightLayer = new MapHighlightLayer(
      this._map,
      this._config.highlightLayerConfig,
    );
    highlightLayer.addHighlightObject(highlightObjects);
    this.setLayer(layerName, highlightLayer);
    return highlightLayer;
  }

  addHighlightObject(highlightObjects: { [key: string]: HighlightObject[] }) {
    Object.keys(highlightObjects).forEach((item) => {
      let layer = this._mapLayers.get(item);
      if (layer) {
        layer.addHighlightObject(highlightObjects[item]);
      } else {
        layer = this.generateHighlightLayer(item, highlightObjects[item]);
      }
    });
  }

  clearHighlightObjects(layersName?: string) {
    if (layersName) {
      const removeLayer = this._mapLayers.get(layersName);
      if (removeLayer?.layer) {
        this._mapLayers.delete(layersName);
        this._layer.getLayers().remove(removeLayer.layer);
        removeLayer.dispose?.();
      }
    } else {
      // 清理所有图层
      this._mapLayers.forEach((layer) => {
        layer.dispose?.();
      });
      this._mapLayers.clear();
      this._layer.getLayers().clear();
    }
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme) {}
}
