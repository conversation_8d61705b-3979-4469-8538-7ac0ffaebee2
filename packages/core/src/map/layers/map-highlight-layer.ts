/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under trade secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import { validateShape } from '@waterdesk/data/object-item';
import { Feature } from 'ol';
import WKT from 'ol/format/WKT';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import Cluster from 'ol/source/Cluster';
import VectorSource from 'ol/source/Vector';
import { StyleLike } from 'ol/style/Style';
import { IMapLayer } from './types';

export interface HighlightStyleConfig {
  [key: string]: any;
}

export interface MapHighlightLayerConfig {
  highlightStyle: HighlightStyleConfig;
  featureStyleCreator: (
    styleType: string,
    config: HighlightStyleConfig,
  ) => {
    generateObjectStyle: (
      highlightObject: HighlightObject,
    ) => StyleLike | undefined;
  };
}

export class MapHighlightLayer implements IMapLayer {
  private _map: OlMap;

  private _name: string;

  private _layerGroup: LayerGroup;

  private _layers: VectorLayer<VectorSource | Cluster<Feature>>[];

  private _source: VectorSource;

  private _clusterSource: VectorSource;

  private _WKT: WKT = new WKT();

  private _featureStyle: {
    generateObjectStyle: (
      highlightObject: HighlightObject,
    ) => StyleLike | undefined;
  };
  private _featureSet: Set<string> = new Set();

  constructor(map: OlMap, config: MapHighlightLayerConfig) {
    this._name = 'highlight';
    this._map = map;
    this._source = new VectorSource();
    this._clusterSource = new VectorSource();

    // 使用配置中的样式创建器
    this._featureStyle = config.featureStyleCreator(
      'track',
      config.highlightStyle,
    );

    const clusterSource = new Cluster({
      distance: 26,
      source: this._clusterSource,
      createCluster(_, features) {
        return features[0];
      },
    });

    const clusterLayer = new VectorLayer({
      source: clusterSource,
    });

    const layer = new VectorLayer({
      source: this._source,
    });

    this._layers = [layer, clusterLayer];

    this._layers.forEach((layer) => {
      layer.setProperties({
        undetectable: true,
      });
    });

    // 创建图层组来包装多个图层
    this._layerGroup = new LayerGroup({
      layers: this._layers,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layerGroup;
  }

  /** 获取内部图层数组，用于需要直接操作个别图层的场景 */
  get layers(): VectorLayer<VectorSource | Cluster<Feature>>[] {
    return this._layers;
  }

  dispose(): void {
    this._source.clear();
    this._clusterSource.clear();
    this._featureSet.clear();
  }

  clearHighlightObjects() {
    this._featureSet = new Set();
    this._source.clear();
    this._clusterSource.clear();
  }

  addHighlightObject(highlightObjects: HighlightObject[]) {
    this.clearHighlightObjects();
    const features: Feature[] = [];
    const clusterFeatures: Feature[] = [];

    highlightObjects.forEach((item) => {
      if (item.shape) {
        if (!this._featureSet.has(item.id)) {
          this._featureSet.add(item.id);
          const feature = this.generateFeature(item);
          if (feature) {
            const featureType = feature?.getGeometry()?.getType();
            // add cluster layer when feature type is Point
            if (featureType === 'Point') {
              clusterFeatures.push(feature);
            } else {
              features.push(feature);
            }
          }
        }
      }
    });

    this._source.addFeatures(features);
    this._clusterSource.addFeatures(clusterFeatures);
  }

  private generateFeature(
    highlightObject: HighlightObject,
  ): Feature | undefined {
    if (validateShape(highlightObject.shape)) {
      const feature: Feature = this._WKT.readFeature(highlightObject.shape);
      feature.setProperties(highlightObject);
      const style: StyleLike | undefined =
        this._featureStyle.generateObjectStyle(highlightObject);
      if (style) feature.setStyle(style);
      return feature;
    }
    return undefined;
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layers?.forEach((layer) => layer.setVisible(visible));
  }

  setTheme(_theme: Theme) {}
}
