/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import Device from '@waterdesk/data/device';
import { IObjectItem, validateShape } from '@waterdesk/data/object-item';
import { Feature } from 'ol';
import WKT from 'ol/format/WKT';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import { Style } from 'ol/style';
import { StyleLike } from 'ol/style/Style';
import { IMapLayer } from './types';

export interface HoverStyleConfig {
  [key: string]: any;
}

export interface MapHoverLayerConfig {
  hoverStyle: HoverStyleConfig;
  featureStyleCreator: (
    styleType: string,
    config: HoverStyleConfig,
  ) => {
    generateObjectStyle: (objectItem: IObjectItem) => StyleLike | undefined;
  };
}

export class MapHoverLayer implements IMapLayer {
  private _map: OlMap;

  private _name: string;

  private _layer: VectorLayer<VectorSource>;

  private _source: VectorSource;

  private _hoverObject: IObjectItem | undefined = undefined;

  private _WKT: WKT = new WKT();

  private _featureStyle: {
    generateObjectStyle: (objectItem: IObjectItem) => StyleLike | undefined;
  };

  constructor(map: OlMap, config: MapHoverLayerConfig) {
    this._name = 'hover';
    this._map = map;
    this._source = new VectorSource();
    this._layer = new VectorLayer({ source: this._source });
    this._layer.setProperties({
      undetectable: true,
      unselectable: true,
    });

    // 使用配置中的样式创建器
    this._featureStyle = config.featureStyleCreator(
      'select',
      config.hoverStyle,
    );
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  dispose(): void {
    this.clearHoverObject();
    this._source?.clear();
  }

  clearHoverObject() {
    this._source.clear();
    this._hoverObject = undefined;
  }

  addHoverObject(hoverObject: IObjectItem) {
    this._hoverObject = hoverObject;

    if (hoverObject instanceof Device && hoverObject.feature !== undefined) {
      const feature: Feature = (hoverObject.feature as Feature).clone();
      MapHoverLayer.setHighlightStyle(feature);
      this._source.addFeature(feature);
    } else {
      this.addHoverObjectFeature(hoverObject);
    }
  }

  private addHoverObjectFeature(clickedObject: IObjectItem) {
    if (validateShape(clickedObject.shape)) {
      const feature: Feature = this._WKT.readFeature(clickedObject.shape);
      feature.set('id', clickedObject.id, true);
      feature.set('oname', clickedObject.oname, true);
      feature.set('otype', clickedObject.otype, true);
      const style: StyleLike | undefined =
        this._featureStyle.generateObjectStyle(clickedObject);
      if (style) feature.setStyle(style);
      this._source.addFeature(feature);
    }
  }

  private static setHighlightStyle(feature: Feature) {
    const style: Style = feature.getStyle() as Style;
    if (style) {
      const clonedStyle = style.clone();
      const originalColor = clonedStyle.getText()?.getFill()?.getColor();
      feature.set('color', originalColor);
      clonedStyle.getText()?.getFill()?.setColor('#ffa500');
      clonedStyle.getText()?.setFont('24px iconfont');
      clonedStyle.getImage()?.setScale(2);

      feature.setStyle(clonedStyle);
    }
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme) {}
}
