/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { GIS_TOKEN } from '@waterdesk/data/const/system-const';
import {
  GisLayerIdentifyArgs,
  ImageArcGisLayerData,
} from '@waterdesk/data/gis-layer-data';
import { getMapLayerUrl } from '@waterdesk/data/layer-data';
import BaseLayer from 'ol/layer/Base';
import ImageLayer from 'ol/layer/Image';
import { default as OlMap } from 'ol/Map';
import ImageArcGISRest from 'ol/source/ImageArcGISRest';
import { IMapLayer, TokenRefresher } from './types';

export interface MapImageArcGISLayerConfig {
  /** Token 刷新器函数，可选 */
  tokenRefresher?: TokenRefresher;
}

export class MapImageArcGISLayer implements IMapLayer {
  private _map: OlMap;

  private _minZoom: number | undefined;

  private _maxZoom: number | undefined;

  private _name: string;

  private _layer: ImageLayer<ImageArcGISRest> | null = null;

  private _layerData: ImageArcGisLayerData;

  private _config?: MapImageArcGISLayerConfig;

  constructor(
    layerData: ImageArcGisLayerData,
    map: OlMap,
    config?: MapImageArcGISLayerConfig,
  ) {
    this._name = layerData.name;
    this._map = map;
    this._minZoom = layerData.minZoom;
    this._maxZoom = layerData.maxZoom;
    this._layerData = layerData;
    this._config = config;
    this.initialize();
  }

  private initialize() {
    const source = new ImageArcGISRest({
      ratio: 1,
      params: this._layerData.layerParams?.params,
      projection: this._layerData.layerParams?.projection,
      url: getMapLayerUrl(
        this._layerData.layerParams?.url,
        this._layerData.layerParams?.urlConfig,
      ),
    });

    source.setImageLoadFunction((imageTile, src) => {
      let newSrc = src;
      if (this._layerData.layerParams?.lowerCase) {
        this._layerData.layerParams?.lowerCase?.forEach((word: string) => {
          newSrc = newSrc.replace(word, word.toLowerCase());
        });
      }

      if (this._layerData.layerParams?.tokenConfig) {
        const gisToken = localStorage.getItem(GIS_TOKEN);
        if (gisToken) {
          try {
            const tokenData = JSON.parse(gisToken);
            newSrc = newSrc.replace('{token}', `${tokenData.token}`);
          } catch (error) {
            console.error(
              'Failed to parse GIS token from localStorage:',
              error,
            );
          }
        }
      }

      const newImageTile = imageTile.getImage();
      (newImageTile as HTMLImageElement).src = newSrc;
    });

    this._layer = new ImageLayer({
      source,
      minZoom: this._minZoom,
      maxZoom: this._maxZoom,
      opacity: this._layerData.layerParams?.opacity,
    });

    // 使用注入的 token 刷新器
    const tokenConfig = this._layerData.layerParams?.tokenConfig ?? {};
    if (
      tokenConfig &&
      typeof tokenConfig === 'object' &&
      this._config?.tokenRefresher
    ) {
      this._config.tokenRefresher(
        GIS_TOKEN,
        tokenConfig.url as string,
        tokenConfig.params as Record<string, string>,
        this._layer?.getSource()?.refresh,
      );
    }
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  get minZoom(): number | undefined {
    return this._minZoom;
  }

  get maxZoom(): number | undefined {
    return this._maxZoom;
  }

  get identifyParams(): GisLayerIdentifyArgs | undefined {
    if (this._layerData.identifyParams.url) {
      return this._layerData.identifyParams;
    }

    return undefined;
  }

  get isVisible(): boolean | undefined {
    return this._layer?.isVisible();
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._layer) {
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }

      this._layer.dispose();
      this._layer = null;
    }
  }
}
