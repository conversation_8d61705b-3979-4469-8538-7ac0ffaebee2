/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { LayerData } from '@waterdesk/data/layer-data';
import BaseLayer from 'ol/layer/Base';
import ImageLayer from 'ol/layer/Image';
import { default as OlMap } from 'ol/Map';
import ImageArcGISRest from 'ol/source/ImageArcGISRest';
import { IMapLayer, ServiceUrlGetter } from './types';

export interface MapNetworkLayerConfig {
  /** 服务 URL 获取器函数 */
  serviceUrlGetter: ServiceUrlGetter;
}

export class MapNetworkLayer implements IMapLayer {
  layerData: LayerData;

  private _map: OlMap;

  private _minZoom: number | undefined;

  private _maxZoom: number | undefined;

  private _name: string;

  private _layer: ImageLayer<ImageArcGISRest> | null = null;

  private _rand: number = 0;

  private _config: MapNetworkLayerConfig;

  constructor(
    layerData: LayerData,
    viewId: string,
    map: OlMap,
    config: MapNetworkLayerConfig,
  ) {
    this.layerData = layerData;
    this._name = layerData.name;
    this._map = map;
    this._minZoom = layerData.minZoom;
    this._maxZoom = layerData.maxZoom;
    this._config = config;
    this.initialize(viewId);
  }

  private initialize(viewId: string) {
    const source = new ImageArcGISRest({
      ratio: 1,
      projection: this.layerData.projection,
      params: {
        otype: this.layerData.name,
        view_id: viewId,
      },
      imageLoadFunction: (image, src) => {
        if (this._rand) {
          const imageWrapper = image.getImage();
          const zoom = this._map.getView().getZoom();
          (imageWrapper as HTMLImageElement).src =
            `${src}&zoom=${zoom}&rand=${this._rand}`;
        }
      },
      url: this._config.serviceUrlGetter('watergis/MapServer'),
    });

    this._layer = new ImageLayer({
      source,
      // minZoom: this._minZoom,    // xuxi-todo, disable min/max zoom since it will be changed by different themes
      // maxZoom: this._maxZoom,
    });
  }

  setViewId(viewId: string) {
    this._layer?.getSource()?.updateParams({
      otype: this.layerData.name,
      view_id: viewId,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw() {
    if (this._layer?.getVisible()) {
      this._rand += 1;
      this._layer?.getSource()?.changed();
    }
  }

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._layer) {
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }

      this._layer.dispose();
      this._layer = null;
    }
  }
}
