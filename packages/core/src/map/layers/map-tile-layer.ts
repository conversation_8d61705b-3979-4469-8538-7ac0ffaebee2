/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { GIS_TOKEN } from '@waterdesk/data/const/system-const';
import { TileLayerData } from '@waterdesk/data/gis-layer-data';
import { getMapLayerUrl, parseToNumberArray } from '@waterdesk/data/layer-data';
import { ImageWrapper } from 'ol';
import BaseLayer from 'ol/layer/Base';
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import { LoadFunction } from 'ol/Tile';
import TileGrid from 'ol/tilegrid/TileGrid';
import { IMapLayer, TokenRefresher } from './types';

export interface MapTileLayerConfig {
  /** Token 刷新器函数，可选 */
  tokenRefresher?: TokenRefresher;
}

export class MapTileLayer implements IMapLayer {
  private _name: string;

  private _layer: TileLayer<XYZ> | null = null;

  private _layerData: TileLayerData;

  private _config?: MapTileLayerConfig;

  constructor(layerData: TileLayerData, config?: MapTileLayerConfig) {
    this._name = 'tile';
    this._layerData = layerData;
    this._config = config;
    this.initialize();
  }

  private initialize(): void {
    let tileGrid: TileGrid | undefined;
    if (this._layerData.layerParams?.tileGrid) {
      tileGrid = new TileGrid({
        ...this._layerData.layerParams?.tileGrid,
        origin: this._layerData.layerParams?.tileGrid.origin,
        resolutions: parseToNumberArray(
          this._layerData.layerParams?.tileGrid.resolutions,
        ),
        extent: this._layerData.layerParams?.extent,
      });
    }

    const tileLoadFunction: LoadFunction = (image) => {
      let z = image.tileCoord[0];
      const x = image.tileCoord[1];
      const y = image.tileCoord[2];
      let newImage = image.key;
      if (this._layerData.layerParams?.tileUrlConfig) {
        if (this._layerData.layerParams.tileUrlConfig.zStart) {
          z =
            image.tileCoord[0] +
            Number(this._layerData.layerParams.tileUrlConfig.zStart);
        }

        if (this._layerData.layerParams.tokenConfig) {
          const gisToken = localStorage.getItem(GIS_TOKEN);
          if (gisToken) {
            try {
              const tokenData = JSON.parse(gisToken);
              newImage = newImage.replace('{token}', `${tokenData.token}`);
            } catch (error) {
              console.error(
                'Failed to parse GIS token from localStorage:',
                error,
              );
            }
          }
        }
      }

      newImage = newImage
        .replace('{z}', `${z}`)
        .replace('{x}', `${x}`)
        .replace('{y}', `${y}`);
      const imageWrapper = (image as unknown as ImageWrapper).getImage();
      (imageWrapper as HTMLImageElement).src = newImage;
    };

    const source = new XYZ({
      url: getMapLayerUrl(
        this._layerData.layerParams?.url,
        this._layerData.layerParams?.urlConfig,
      ),
      tileLoadFunction: this._layerData.layerParams?.tileUrlConfig
        ? tileLoadFunction
        : undefined,
      tileGrid,
      tileSize: this._layerData.layerParams?.tileSize ?? 256,
      projection: this._layerData.layerParams?.projection,
    });

    this._layer = new TileLayer({
      extent: this._layerData.layerParams?.extent,
      source,
      opacity: this._layerData.layerParams?.opacity,
      minZoom: this._layerData.minZoom,
      maxZoom: this._layerData.maxZoom,
    });

    // 使用注入的 token 刷新器
    const tokenConfig = this._layerData.layerParams?.tokenConfig ?? {};
    if (
      tokenConfig &&
      typeof tokenConfig === 'object' &&
      this._config?.tokenRefresher
    ) {
      this._config.tokenRefresher(
        GIS_TOKEN,
        tokenConfig.url as string,
        tokenConfig.params as Record<string, string>,
        this._layer?.getSource()?.refresh,
      );
    }
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._layer) {
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }

      this._layer.dispose();
      this._layer = null;
    }
  }
}
