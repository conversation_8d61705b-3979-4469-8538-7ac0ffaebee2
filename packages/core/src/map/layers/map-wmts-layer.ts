/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { WmtsLayerData } from '@waterdesk/data/gis-layer-data';
import { getMapLayerUrl, parseToNumberArray } from '@waterdesk/data/layer-data';
import BaseLayer from 'ol/layer/Base';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { IMapLayer } from './types';

export class MapWmtsLayer implements IMapLayer {
  private _name: string;

  private _layer: TileLayer<WMTS> | null = null;

  private _layerData: WmtsLayerData;

  constructor(layerData: WmtsLayerData) {
    this._name = 'wmts';
    this._layerData = layerData;
    this.initialize();
  }

  private initialize(): void {
    const source = new WMTS({
      url: getMapLayerUrl(
        this._layerData.layerParams?.url,
        this._layerData.layerParams?.urlConfig,
      ),
      matrixSet: this._layerData.layerParams?.matrixSet || '',
      projection: this._layerData.layerParams?.projection,
      layer: this._layerData.layerParams?.layer || '',
      style: this._layerData.layerParams?.style || '',
      version: this._layerData.layerParams?.version,
      format: this._layerData.layerParams?.format,
      tileGrid: new WMTSTileGrid({
        origin: this._layerData.layerParams?.tileGrid.origin,
        resolutions: parseToNumberArray(
          this._layerData.layerParams?.tileGrid.resolutions,
        ),
        matrixIds: this._layerData.layerParams?.tileGrid.matrixIds || [],
      }),
    });

    this._layer = new TileLayer({
      extent: this._layerData.layerParams?.extent,
      source,
      opacity: this._layerData.layerParams?.opacity,
      minZoom: this._layerData.minZoom,
      maxZoom: this._layerData.maxZoom,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  setTheme(_theme: Theme): void {}

  dispose(): void {
    if (this._layer) {
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }

      this._layer.dispose();
      this._layer = null;
    }
  }
}
