/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import BaseLayer from 'ol/layer/Base';

/**
 * Token 刷新器配置
 */
export interface TokenRefresherConfig {
  tokenKey: string;
  url: string;
  params: Record<string, string>;
  refreshCallback?: () => void;
}

/**
 * Token 刷新器函数类型
 */
export type TokenRefresher = (
  tokenKey: string,
  url: string,
  params: Record<string, string>,
  refreshCallback?: () => void,
) => void;

/**
 * 服务 URL 获取器函数类型
 */
export type ServiceUrlGetter = (servicePath: string) => string;

/**
 * 通用地图图层接口
 */
export interface IMapLayer {
  /** 图层名称 */
  name: string;
  /** OpenLayers 图层实例 */
  layer: BaseLayer | null;
  /** 设置图层可见性 */
  setVisible(visible: boolean): void;
  /** 重绘图层 */
  redraw(): void;
  /** 设置主题 */
  setTheme(theme: Theme): void;
  /** 销毁图层 */
  dispose?(): void;
}

/**
 * 图层配置接口，支持外部依赖注入
 */
export interface LayerConfig {
  /** Token 刷新器函数 */
  tokenRefresher?: TokenRefresher;
  /** 服务 URL 获取器函数 */
  serviceUrlGetter?: ServiceUrlGetter;
  /** 其他配置参数 */
  [key: string]: unknown;
}
