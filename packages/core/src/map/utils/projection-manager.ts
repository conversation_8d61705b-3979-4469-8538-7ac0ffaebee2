/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Extent } from 'ol/extent';
import { get as getProjection } from 'ol/proj';
import { register } from 'ol/proj/proj4';
import proj4 from 'proj4';

/**
 * 投影配置项类型
 */
export interface ProjectionConfig {
  /** 投影名称，如 'EPSG:4326' */
  name: string;
  /** 投影定义字符串 */
  definition: string;
  /** 投影边界（可选） */
  projectedBounds?: Extent;
  /** WGS84边界（可选） */
  wgs84Bounds?: Extent;
}

/**
 * 旧格式投影数据类型（用于类型检查）
 */
type LegacyProjectionData = [string, string, string?, string?];

/**
 * 投影管理器
 *
 * 提供投影坐标系的注册和管理功能
 */
export class ProjectionManager {
  /**
   * 注册投影坐标系
   * @param projections 投影配置数组
   */
  static registerProjections(projections: ProjectionConfig[]): void {
    // 注册投影定义
    projections.forEach((config) => {
      proj4.defs(config.name, config.definition);
    });

    // 注册到 OpenLayers
    register(proj4);

    // 设置边界
    projections.forEach((config) => {
      const currentProjection = getProjection(config.name);
      if (currentProjection) {
        if (config.projectedBounds) {
          currentProjection.setExtent(config.projectedBounds);
        }
        if (config.wgs84Bounds) {
          currentProjection.setWorldExtent(config.wgs84Bounds);
        }
      }
    });
  }

  /**
   * 检查投影是否已注册
   * @param name 投影名称
   * @returns 是否已注册
   */
  static isProjectionRegistered(name: string): boolean {
    return getProjection(name) !== null;
  }

  /**
   * 获取已注册的投影
   * @param name 投影名称
   * @returns 投影对象或null
   */
  static getProjection(name: string) {
    return getProjection(name);
  }
}

/**
 * 将旧格式的投影数据转换为新格式
 * @param legacyData 旧格式数据：[name, definition, projectedBoundsJson?, wgs84BoundsJson?]
 * @returns 新格式的投影配置数组
 */
export function convertLegacyProjectionData(
  legacyData: unknown[],
): ProjectionConfig[] {
  return legacyData
    .filter(
      (element): element is LegacyProjectionData =>
        Array.isArray(element) && element.length >= 2,
    )
    .map((element) => {
      const [name, definition, projectedBoundsJson, wgs84BoundsJson] = element;

      if (typeof name !== 'string' || typeof definition !== 'string') {
        throw new Error(
          `Invalid projection config: name and definition must be strings`,
        );
      }

      const config: ProjectionConfig = { name, definition };

      // 解析边界JSON
      if (projectedBoundsJson) {
        try {
          config.projectedBounds = JSON.parse(projectedBoundsJson) as Extent;
        } catch (error) {
          console.warn(`Failed to parse projected bounds for ${name}:`, error);
        }
      }

      if (wgs84BoundsJson) {
        try {
          config.wgs84Bounds = JSON.parse(wgs84BoundsJson) as Extent;
        } catch (error) {
          console.warn(`Failed to parse WGS84 bounds for ${name}:`, error);
        }
      }

      return config;
    });
}

/**
 * 便捷的全局函数
 */
export function registerProjections(projections: ProjectionConfig[]): void {
  ProjectionManager.registerProjections(projections);
}
