/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { combineReducers, Reducer } from '@reduxjs/toolkit';
import { InjectedReducersType } from './injector-typings';

/**
 * 创建合并后的 reducer
 * @param injectedReducers 动态注入的 reducer 映射
 */
export function createReducer<TState>(
  injectedReducers?: InjectedReducersType<TState>,
): Reducer<TState> {
  // 如果没有 reducer 就返回 identity reducer
  if (!injectedReducers || Object.keys(injectedReducers).length === 0) {
    return (state: TState | undefined) => state as TState;
  }

  return combineReducers({ ...injectedReducers }) as unknown as Reducer<TState>;
}
