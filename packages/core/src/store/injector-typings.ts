/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Reducer, UnknownAction } from '@reduxjs/toolkit';
import { SagaInjectionModes } from 'redux-injectors';
import { Saga } from 'redux-saga';

/**
 * 注入的reducer类型定义
 */
export type InjectedReducersType<T> = {
  [K in keyof T]?: Reducer<Required<T>[K], UnknownAction>;
};

/**
 * 注入reducer的参数接口
 */
export interface InjectReducerParams<T, K extends keyof T = keyof T> {
  key: K & string;
  reducer: Reducer<Required<T>[K], UnknownAction>;
}

/**
 * 注入saga的参数接口
 */
export interface InjectSagaParams<K extends string = string> {
  key: K;
  saga: Saga;
  mode?: SagaInjectionModes;
}
