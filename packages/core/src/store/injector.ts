/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  useInjectReducer as baseUseReducer,
  useInjectSaga as baseUseSaga,
} from 'redux-injectors';
import type { InjectReducerParams, InjectSagaParams } from './injector-typings';

/**
 * 创建 useInjectReducer 的泛型封装（用于支持不同项目 RootState）
 */
export function createUseInjectReducer<T>() {
  return function useInjectReducer<K extends keyof T>(
    params: InjectReducerParams<T, K>,
  ) {
    return baseUseReducer(params);
  };
}

/**
 * 创建 useInjectSaga 的泛型封装（key 只需要是 string 类型）
 */
export function createUseInjectSaga() {
  return function useInjectSaga<K extends string = string>(
    params: InjectSagaParams<K>,
  ) {
    return baseUseSaga(params);
  };
}
