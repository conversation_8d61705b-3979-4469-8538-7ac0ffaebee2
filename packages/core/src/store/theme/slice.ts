/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import type {
  ComponentsConfig,
  DefaultSeedToken,
  Theme,
} from '@waterdesk/core/theme';
import type { ThemeState } from './types';

export const createThemeSlice = (initialState: ThemeState) => {
  return createSlice({
    name: 'theme',
    initialState,
    reducers: {
      updateTheme(state, action: PayloadAction<{ theme: Theme }>) {
        state.theme = action.payload.theme;
      },
      updateThemeToken(
        state,
        action: PayloadAction<{
          seedToken?: DefaultSeedToken;
          componentsConfig?: ComponentsConfig;
          darkModeSeedToken?: DefaultSeedToken;
          darkModeComponentsConfig?: ComponentsConfig;
        }>,
      ) {
        return { ...state, ...action.payload };
      },
      resetThemeState: () => initialState,
    },
  });
};
