import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import { DeviceWarningType, WarnInfoList } from '@waterdesk/data/warn';
import dayjs, { Dayjs } from 'dayjs';
import { TimelineState } from './types';

export const createTimelineSlice = (initialState: TimelineState) => {
  return createSlice({
    name: 'timeline',
    initialState,
    reducers: {
      updateAutoPlay(state, action: PayloadAction<{ autoPlay: boolean }>) {
        const { autoPlay } = action.payload;
        state.autoPlay = autoPlay;
      },
      updateTimelineDate(
        state,
        action: PayloadAction<{ timelineDate: string }>,
      ) {
        const { timelineDate } = action.payload;
        state.timelineDate = timelineDate;
      },
      updateTimelineTime(
        state,
        action: PayloadAction<{ timelineTime: number }>,
      ) {
        const { timelineTime } = action.payload;
        state.timelineTime = timelineTime;
      },
      updateTimelineWarn(state, action: PayloadAction<{ list: WarnInfoList }>) {
        const { list } = action.payload;
        state.timelineWarn = list;
      },
      updateTimelineEvent(
        state,
        action: PayloadAction<{ list: EventSchedulingBasicInfo[] }>,
      ) {
        const { list } = action.payload;
        state.timelineEvent = list;
      },
      updateDevicesWarns(
        state,
        action: PayloadAction<{ devicesWarnsList: DeviceWarningType }>,
      ) {
        const { devicesWarnsList } = action.payload;
        state.devicesWarns = devicesWarnsList;
      },
      updateTempTime(
        state,
        action: PayloadAction<{ tempTime: Dayjs | undefined }>,
      ) {
        const { tempTime } = action.payload;
        state.tempTime = tempTime;
      },
      setViewThemeTime() {},
      resetState() {
        return {
          ...initialState,
          timelineDate: dayjs().format('YYYY-MM-DD'),
          timelineTime: getValueFromDateTime(dayjs()),
        };
      },
    },
  });
};
