/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { DeviceWarningType, WarnInfoList } from '@waterdesk/data/warn';
import { Dayjs } from 'dayjs';

export interface TimelineState {
  /** 时间轴日期，格式为 YYYY-MM-DD */
  timelineDate: string;
  /** 时间轴时间值，标记时间滑块的值，转换为 HH:mm 格式的时间 */
  timelineTime: number;
  /** 时间轴是否自动播放 */
  autoPlay: boolean;
  /** 临时时间 */
  tempTime: Dayjs | undefined;
  /** 与时间轴date相关的警告信息 */
  timelineWarn: WarnInfoList;
  /** 时间轴事件列表 */
  timelineEvent: EventSchedulingBasicInfo[];
  /** 设备警告信息 */
  devicesWarns: DeviceWarningType;
}
