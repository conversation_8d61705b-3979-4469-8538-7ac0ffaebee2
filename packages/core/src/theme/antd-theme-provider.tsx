/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { Children, useLayoutEffect, useMemo } from 'react';
import {
  ComponentsConfig,
  DefaultSeedToken,
  getAlgorithm,
  seedToken,
} from './theme';

type WithChildren<T = {}> = T & { children?: React.ReactNode };

export interface AntdThemeProviderProps extends WithChildren {
  /**
   * 当前主题类型
   */
  theme: 'light' | 'dark';

  /**
   * 系统级别的种子令牌配置
   */
  systemSeedToken?: DefaultSeedToken;

  /**
   * 组件级别的配置
   */
  componentsConfig?: ComponentsConfig;

  /**
   * 暗色模式下的种子令牌配置
   */
  darkModeSeedToken?: DefaultSeedToken;

  /**
   * 暗色模式下的组件配置
   */
  darkModeComponentsConfig?: ComponentsConfig;

  /**
   * 是否在 document 元素上设置 data-prefers-color-scheme 属性
   * 默认为 false
   */
  setDataAttribute?: boolean;
}

/**
 * Ant Design 主题提供者组件
 *
 * 提供统一的 Ant Design 主题配置，支持亮色/暗色主题切换
 * 以及自定义的种子令牌和组件配置
 */
export const AntdThemeProvider = (props: AntdThemeProviderProps) => {
  const {
    theme: themeType,
    systemSeedToken = {},
    componentsConfig = {},
    darkModeSeedToken = {},
    darkModeComponentsConfig = {},
    setDataAttribute = false,
    children,
  } = props;

  // 可选的 DOM 属性设置
  useLayoutEffect(() => {
    if (setDataAttribute) {
      document.documentElement.setAttribute(
        'data-prefers-color-scheme',
        themeType === 'dark' ? 'dark' : 'light',
      );
    }
  }, [themeType, setDataAttribute]);

  // 构建主题配置
  const theme = useMemo(
    () => ({
      token: {
        ...seedToken,
        ...systemSeedToken,
        ...(themeType === 'dark' ? darkModeSeedToken : {}),
      },
      components: {
        ...componentsConfig,
        ...(themeType === 'dark' ? darkModeComponentsConfig : {}),
      },
      algorithm: getAlgorithm(themeType),
    }),
    [
      themeType,
      systemSeedToken,
      componentsConfig,
      darkModeSeedToken,
      darkModeComponentsConfig,
    ],
  );

  return (
    <ConfigProvider
      theme={theme}
      locale={zhCN}
    >
      {Children.only(children)}
    </ConfigProvider>
  );
};
