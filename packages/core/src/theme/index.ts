/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// 主题提供者组件
export type { AntdThemeProviderProps } from './antd-theme-provider';
export { AntdThemeProvider } from './antd-theme-provider';
export type { StyledThemeProviderProps } from './styled-theme-provider';
export { StyledThemeProvider } from './styled-theme-provider';
// 主题类型和配置
export type {
  ComponentsConfig,
  DefaultMapToken,
  DefaultSeedToken,
  Theme,
  ThemeToken,
} from './theme';
export {
  defaultSeed,
  defaultToken,
  getAlgorithm,
  seedToken,
} from './theme';
// 主题钩子
export { useToken } from './use-token';
