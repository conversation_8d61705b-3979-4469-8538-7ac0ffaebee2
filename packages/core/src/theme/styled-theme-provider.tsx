/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { theme as antdTheme } from 'antd';
import { ThemeProvider as OriginalThemeProvider } from 'styled-components';

export interface StyledThemeProviderProps {
  children?: React.ReactNode;
}

/**
 * Styled Components 主题提供者组件
 *
 * 将 Ant Design 的主题令牌传递给 styled-components，
 * 使得在 styled-components 中可以访问 Ant Design 的设计令牌
 */
export const StyledThemeProvider = (props: StyledThemeProviderProps) => {
  const { useToken: useAntdToken } = antdTheme;
  const { token: antdToken } = useAntdToken();
  const { children } = props;

  return (
    <OriginalThemeProvider theme={antdToken}>{children}</OriginalThemeProvider>
  );
};
