/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ThemeConfig, theme } from 'antd';
import { MappingAlgorithm } from 'antd/es/theme';
import {
  AliasToken,
  SeedToken as AntdSeedToken,
  MapToken,
} from 'antd/es/theme/interface';

/**
 * 默认种子令牌类型
 * 用于自定义 Ant Design 的基础设计令牌
 */
export type DefaultSeedToken = Partial<AntdSeedToken>;

/**
 * 组件配置类型
 * 用于自定义特定组件的主题配置
 */
export type ComponentsConfig = ThemeConfig['components'];

/**
 * 默认映射令牌类型
 * 从种子令牌派生的映射令牌
 */
export type DefaultMapToken = Partial<MapToken>;

/**
 * 主题类型
 * 支持亮色和暗色主题
 */
export type Theme = 'light' | 'dark';

/**
 * 主题令牌接口
 * 继承自 Ant Design 的别名令牌，提供完整的设计令牌
 */
export interface ThemeToken extends Partial<AliasToken> {}

const { defaultAlgorithm, defaultSeed: antdDefaultSeed, darkAlgorithm } = theme;

/**
 * 默认种子令牌
 * 可以在这里覆盖 Ant Design 的默认值
 */
export const defaultSeed: DefaultSeedToken = {};

/**
 * 完整的种子令牌
 * 合并 Ant Design 默认值和自定义值
 */
export const seedToken: AntdSeedToken = { ...antdDefaultSeed, ...defaultSeed };

/**
 * 默认令牌
 * 通过算法从种子令牌生成的完整令牌集
 */
export const defaultToken: MapToken = defaultAlgorithm(seedToken);

/**
 * 获取主题算法
 * 根据主题类型返回相应的算法
 *
 * @param theme - 主题类型
 * @returns 对应的算法函数
 */
export const getAlgorithm = (
  theme: Theme,
): MappingAlgorithm | MappingAlgorithm[] => {
  if (theme === 'dark') return darkAlgorithm;
  return defaultAlgorithm;
};
