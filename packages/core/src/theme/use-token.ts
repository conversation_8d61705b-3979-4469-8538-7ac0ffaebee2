/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useTheme } from 'styled-components';
import { ThemeToken } from './theme';

/**
 * 主题令牌钩子
 * 提供访问当前主题令牌的方式
 *
 * @returns 包含主题令牌的对象
 *
 * @example
 * ```tsx
 * const { token } = useToken();
 * console.log(token.colorPrimary); // 获取主色
 * ```
 */
export const useToken = (): { token: ThemeToken } => {
  const theme = useTheme();
  return { token: theme };
};

export default useToken;
