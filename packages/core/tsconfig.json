{"extends": "./tsconfig.paths.json", "compilerOptions": {"outDir": "./dist/", "sourceMap": true, "strictNullChecks": true, "target": "es6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "exclude": ["./node_modules"], "include": ["./src/", "./stories/", "./.storybook/", "typed.d.ts", "./index.html"]}