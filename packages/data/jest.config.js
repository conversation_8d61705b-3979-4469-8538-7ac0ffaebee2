/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */

module.exports = {
  preset: 'ts-jest',

  testEnvironment: 'jsdom',
  moduleDirectories: ['node_modules', '__dirname'],
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|webp|svg|docx)$': '<rootDir>/__mocks__/fileMock.js',
  },
  transform: {
    '^.+\\.ts?$': 'ts-jest',
  },
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],
  testPathIgnorePatterns: ['/node_modules/', '/dist/'],
};
