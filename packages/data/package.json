{"name": "@waterdesk/data", "version": "3.1.0", "description": "> TODO: description", "author": "yechangqing <<EMAIL>>", "homepage": "https://github.com/WaterDesk/storm-web#readme", "license": "ISC", "directories": {"test": "__tests__"}, "main": "src/index.js", "types": "src/index.d.ts", "exports": {"./*": "./src/*"}, "publishConfig": {"registry": "https://registry.npmmirror.com/"}, "repository": {"type": "git", "url": "git+https://github.com/WaterDesk/storm-web.git"}, "scripts": {"prettier:write": "prettier --write ./src", "lint:fix": "eslint --fix \"./src/**/*.{ts,js}\" --max-warnings 0", "prettier": "prettier --check ./src", "lint": "eslint \"./src/**/*.{ts,js}\" --max-warnings 0", "jest": "jest", "coverage": "jest --coverage", "build:tsc": "tsc --build"}, "bugs": {"url": "https://github.com/WaterDesk/storm-web/issues"}, "dependencies": {"docxtemplater": "^3.49.1", "file-saver": "^2.0.5", "pizzip": "^3.1.7"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "babel-jest": "^30.0.4"}}