/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { parseTimePeriods, TimePeriod } from '../app-config';

describe('App config', () => {
  it('parseTimePeriods', () => {
    const timePeriodsObject = [
      {
        label: '早高峰',
        period: ['7:00', '9:00'],
      },
      {
        label: '晚高峰',
        period: ['18:00', '21:00'],
      },
      {
        label: '平均时',
        period: ['12:00', '15:00'],
      },
      {
        label: '夜间',
        period: ['1:00', '4:00'],
      },
      {
        label: '自定义',
        period: ['0:00', '24:00'],
      },
    ];

    const timePeriods: TimePeriod[] | undefined =
      parseTimePeriods(timePeriodsObject);
    expect(timePeriods?.length).toEqual(5);
    if (timePeriods !== undefined) {
      expect(timePeriods[0].label).toEqual('早高峰');
      expect(timePeriods[0].startTime).toEqual('7:00');
      expect(timePeriods[0].endTime).toEqual('9:00');
      expect(timePeriods[4].label).toEqual('自定义');
      expect(timePeriods[4].startTime).toEqual('0:00');
      expect(timePeriods[4].endTime).toEqual('24:00');
    }
  });
});
