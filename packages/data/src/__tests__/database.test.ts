/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import Device, { getChartProperties, getValueQueryParameters } from '../device';
import { DeviceTimeData, ScadaModelTimeData } from '../device-time-data';
import { FeatureLayerData } from '../layer-data';
import mockDatabase from '../mock/mock-database';
import {
  deviceFlowJson2,
  iconDataJson,
  mockGroupPropValuesParams,
} from '../mock/mock-device';
import BasicPropertyCategory from '../property/basic-property-category';
import DmaRelatedDeviceCategory from '../property/dma-related-devices-category';
import GeneralPropertyCategory from '../property/general-property-category';
import { PropertyInfo } from '../property/property-info';

describe('empty database', () => {
  it('empty db', () => {
    const db: Database = new Database();
    expect(db.layerCollection.layerDatas.length).toBe(0);
  });
});

describe('database', () => {
  it('intialize database', () => {
    const db = mockDatabase();
    expect(db.viewExtent.length).toBe(4);
    expect(db.getDeviceById('unknown')).toBe(undefined);
    const device: Device | undefined = db.getDeviceById(
      'DEV_FLOW@C0220010007156',
    );
    const deviceByOName: Device | undefined = db.getDevice(
      'DEV_FLOW',
      'C0220010007156',
    );
    expect(device?.id).toEqual(deviceByOName?.id);
    const devicePropertyInfo: PropertyInfo | undefined =
      db.getPropertyInfo('DEV_FLOW');
    expect(device?.id).toBe('DEV_FLOW@C0220010007156');
    expect(devicePropertyInfo).toBeDefined();
    if (device) {
      const params = getValueQueryParameters(db, device);
      expect(JSON.stringify(params)).toEqual(mockGroupPropValuesParams);
      if (device) expect(getChartProperties(db, device).length).toBe(17);
    }
    expect(db.icons.size).toBe(5);
    expect(db.propertyInfos.size).toBe(6);

    const layerRain = db.layerCollection.getLayer('SDFOLD_NETWORK_RAIN');
    expect(layerRain?.type).toBe('LayerFeature');
    const featureLayerRain = layerRain as FeatureLayerData;
    expect(featureLayerRain?.devices.get('DEV_FLOW@C0220010007156')?.icon).toBe(
      '\\ue69b',
    );

    const flowIndicator = db.getIndicator('SDVAL_FLOW_W', 'C022001000715601');
    expect(flowIndicator?.id).toBe('SDVAL_FLOW_W@C022001000715601');
    expect(flowIndicator?.pname).toBe('C0220010007156');
    expect(flowIndicator?.ptype).toBe('DEV_FLOW');
    expect(flowIndicator?.title).toBeUndefined();
    expect(flowIndicator?.maxLimitation).toEqual(3.5);

    expect(db.getIndicator(undefined, undefined)).toBeUndefined();

    const liquidInfo = db.getPropertyInfo('DEV_LIQUID');
    expect(liquidInfo?.title).toBe('液位计');

    const generalCategory = liquidInfo
      ?.categories[0] as GeneralPropertyCategory;
    const objectProperty0 = generalCategory.propertyItems[0];
    expect(objectProperty0.otype).toBe('SDVAL_LIQ');
    expect(objectProperty0.name).toBe('SDVAL');
    expect(objectProperty0.title).toBe('黄海高程');
    expect(objectProperty0.unit).toBe('LEVEL');
    expect(objectProperty0.unitFormat?.unitName).toBe('LEVEL');
    expect(objectProperty0.unitFormat?.valueTitle).toBe('m');

    const basicCategory = liquidInfo?.categories[5] as BasicPropertyCategory;
    const objectProperty1 = basicCategory.propertyItems[1];
    expect(objectProperty1.title).toBe('名称');
    expect(objectProperty1.unit).toBe('');

    expect(liquidInfo?.quickProperties.length).toBe(6);
    const quickPropertyTitle = liquidInfo?.quickProperties[0];
    const quickPropertyGroundLevel = liquidInfo?.quickProperties[3];
    const quickPropertyLiqSdval = liquidInfo?.quickProperties[5];
    expect(quickPropertyTitle?.unit).toBe('');
    expect(quickPropertyGroundLevel?.unitFormat?.unitTitle).toBe('标高');
    expect(quickPropertyGroundLevel?.unitFormat?.valueTitle).toBe('m');
    expect(quickPropertyLiqSdval?.unitFormat?.unitTitle).toBe('液位');
    expect(quickPropertyLiqSdval?.unitFormat?.valueTitle).toBe('m');

    const unitFormat = db.getUnitFormat('SDVAL_LIQ', 'SDVAL');
    expect(unitFormat?.unitName).toBe('LEVEL');

    const invalidFormat = db.getUnitFormat('invalidFormat', 'SDVAL');
    expect(invalidFormat).toBeUndefined();

    const invalidProp = db.getUnitFormat('SDVAL_LIQ', 'SDVAL1');
    expect(invalidProp).toBeUndefined();

    const dmaPropertyInfo = db.getPropertyInfo('NRW_DMA3');
    const dmaCategory = dmaPropertyInfo
      ?.categories[2] as DmaRelatedDeviceCategory;
    expect(dmaCategory.title).toBe('分区与流量计关系');
    expect(dmaCategory.getValueQueryParameters([])).toEqual(new Map());
    expect(dmaCategory.getChartProperties([])).toEqual([]);
  });

  it('device time data', () => {
    const db = mockDatabase();

    expect(
      db.currentDeviceTimeData.getIndicatorValueById(
        'SDVAL_FLOW_W@C022001000715601',
      ),
    ).toBeUndefined();

    const deviceTimeData: DeviceTimeData = {
      oname: 'C022001000715601',
      otype: 'SDVAL_FLOW_W',
      vprop: 'SDVAL',
      time: '2022-12-01 00:00:00',
      value: 1200.4,
      unit: 'FLOW',
      originalValue: 1200,
      latestDeviceState: false,
    };

    const scadaModelTimeData: ScadaModelTimeData = {
      id: 'SDVAL_FLOW_W@C022001000715601',
      oname: 'SDVAL_FLOW_W',
      otype: 'SDVAL_FLOW_W',
      scadaData: deviceTimeData,
      simulationData: undefined,
    };

    const values: Map<string, ScadaModelTimeData> = new Map();
    values.set('SDVAL_FLOW_W@C022001000715601', scadaModelTimeData);
    db.currentDeviceTimeData.updateIndicatorData(values);
    expect(
      db.currentDeviceTimeData.getIndicatorValueById(
        'SDVAL_FLOW_W@C022001000715601',
      )?.scadaData?.value,
    ).toBe(1200.4);
  });
});

describe('getIconByShape function', () => {
  const db = mockDatabase();
  it('should return the icon when shape is included and not excluded', () => {
    const result = db.getIconByShape(
      deviceFlowJson2.OTYPE,
      deviceFlowJson2.SHAPE,
      ['POINT'],
      undefined,
    );
    expect(result).toBe(iconDataJson.DEV_FLOW);
  });

  it('should return undefined when shape is not included', () => {
    const result = db.getIconByShape(
      deviceFlowJson2.OTYPE,
      deviceFlowJson2.SHAPE,
      [],
    );
    expect(result).toBeUndefined();
  });

  it('should return undefined when shape is excluded', () => {
    const result = db.getIconByShape(
      deviceFlowJson2.OTYPE,
      deviceFlowJson2.SHAPE,
      undefined,
      ['POINT'],
    );
    expect(result).toBeUndefined();
  });

  it('should return undefined when otype is not found', () => {
    const result = db.getIconByShape(
      'xxxx',
      deviceFlowJson2.SHAPE,
      ['POINT'],
      undefined,
    );
    expect(result).toBeUndefined();
  });
});
