/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentInfo } from '../department';
import { buildDepartmentTree } from '../department-user';

describe('buildDepartmentTree', () => {
  it('should build a tree structure from a flat array', () => {
    const departments: DepartmentInfo[] = [
      { id: '1', name: 'Root', note: '', parentId: '', order: 1 },
      { id: '2', name: 'Child1', note: '', parentId: '1', order: 2 },
      { id: '3', name: 'Child2', note: '', parentId: '1', order: 3 },
      { id: '4', name: '<PERSON>Chil<PERSON>', note: '', parentId: '2', order: 4 },
    ];

    const expected = [
      {
        title: 'Root',
        label: 'Root',
        key: '1',
        value: '1',
        checkable: false,
        disabled: false,
        order: 1,
        children: [
          {
            title: 'Child1',
            key: '2',
            label: 'Child1',
            value: '2',
            checkable: false,
            disabled: false,
            order: 2,
            children: [
              {
                title: 'GrandChild',
                key: '4',
                label: 'GrandChild',
                value: '4',
                checkable: false,
                disabled: false,
                order: 4,
              },
            ],
          },
          {
            title: 'Child2',
            key: '3',
            label: 'Child2',
            value: '3',
            checkable: false,
            disabled: false,
            order: 3,
          },
        ],
      },
    ];

    const result = buildDepartmentTree(departments);
    expect(result).toEqual(expected);
  });

  it('should handle empty input', () => {
    const result = buildDepartmentTree([]);
    expect(result).toEqual([]);
  });

  it('should correctly handle disabled departments', () => {
    const departments: DepartmentInfo[] = [
      { id: '1', name: '总部', note: '', parentId: '', order: 1 },
      { id: '2', name: '研发部', note: '', parentId: '1', order: 2 },
      { id: '3', name: '市场部', note: '', parentId: '1', order: 3 },
      { id: '4', name: '前端组', note: '', parentId: '2', order: 4 },
    ];

    const disabledDepartments = ['2', '3']; // 禁用研发部和市场部
    const result = buildDepartmentTree(departments, true, disabledDepartments);
    const expected = [
      {
        title: '总部',
        key: '1',
        label: '总部',
        value: '1',
        checkable: true,
        disabled: false,
        order: 1,
        children: [
          {
            title: '研发部',
            key: '2',
            label: '研发部',
            value: '2',
            checkable: true,
            disabled: true,
            order: 2,
            children: [
              {
                title: '前端组',
                key: '4',
                label: '前端组',
                value: '4',
                checkable: true,
                disabled: false,
                order: 4,
              },
            ],
          },
          {
            title: '市场部',
            key: '3',
            label: '市场部',
            value: '3',
            checkable: true,
            disabled: true,
            order: 3,
          },
        ],
      },
    ];

    expect(result).toEqual(expected);
  });
});
