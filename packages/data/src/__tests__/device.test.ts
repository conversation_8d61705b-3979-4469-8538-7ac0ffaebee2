/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Device, {
  DeviceCollection,
  PlantsAndPumpStationsCollection,
} from '../device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '../indicator';
import {
  deviceFlowJson1,
  deviceFlowJson2,
  deviceIndicatorsJson,
  deviceJsonArray,
  devicePondJson1,
  factoryJson1,
  factoryJson2,
  iconDataJson,
  indicatorsData,
  indicatorTypesJson,
} from '../mock/mock-device';
import {
  devFlowPropsData,
  devFlowPropViewData,
} from '../mock/mock-property-info';
import GeneralPropertyCategory from '../property/general-property-category';
import IndicatorPropertyCategory from '../property/indicator-property-category';
import { PropertyInfo } from '../property/property-info';

describe('device', () => {
  it('add device', () => {
    const deviceFlow1 = new Device(deviceFlowJson1, []);
    expect(deviceFlow1.id).toEqual('DEV_FLOW@C0220010007156');
    expect(deviceFlow1.oname).toEqual('C0220010007156');
    expect(deviceFlow1.otype).toEqual('DEV_FLOW');
    expect(deviceFlow1.pname).toEqual('NETWORK');
    expect(deviceFlow1.layerName).toEqual('SDFOLD_NETWORK_RAIN');
    expect(deviceFlow1.shape).toEqual('POINT(506542.3232 2491334.7526)');
    expect(deviceFlow1.shapeType).toEqual('POINT');
    expect(deviceFlow1.title).toEqual('(雨水)皇岗路与国花路交叉口');
    expect(deviceFlow1.dataSource).toEqual('福田水务局');
    expect(deviceFlow1.displayLevel).toEqual(9999);
    expect(deviceFlow1.minViewRatio).toBeNull();
    expect(deviceFlow1.maxViewRatio).toBeNull();
    expect(deviceFlow1.summarize).toBe(true);

    const deviceFlow2 = new Device(deviceFlowJson2, []);
    expect(deviceFlow2.title).toEqual('C0220010007129');
    expect(deviceFlow2.summarize).toBe(false);

    const devicePond = new Device(devicePondJson1, []);
    expect(devicePond.otype).toEqual('DEV_POND');
    expect(devicePond.displayLevel).toEqual(0);
    expect(devicePond.minViewRatio).toBe(2);
    expect(devicePond.maxViewRatio).toBe(4.5);
    expect(devicePond.title).toEqual('岗厦北高架桥旁');
    expect(devicePond.summarize).toBe(true);

    const deviceCollection = new DeviceCollection();
    deviceCollection.addDevice(deviceFlow1);
    deviceCollection.addDevice(deviceFlow2);
    deviceCollection.addDevice(devicePond);
    expect(deviceCollection.getAllDeviceObjects().length).toBe(3);
    expect(
      deviceCollection.getDevice('DEV_FLOW', 'C0220010007156')?.oname,
    ).toBe('C0220010007156');
  });

  it('initialize DeviceCollection', () => {
    const deviceCollection: DeviceCollection = new DeviceCollection();
    deviceCollection.initializeDevices(deviceJsonArray, []);
    expect(deviceCollection.getAllDeviceObjects().length).toBe(5);
    const flow1 = deviceCollection.getDevice('DEV_FLOW', 'C0220010007156');
    expect(flow1?.oname).toBe('C0220010007156');
    const flow2 = deviceCollection.getDevice('DEV_FLOW', 'C0220010007129');

    const indicatorTypeCollection: IndicatorTypeCollection =
      new IndicatorTypeCollection();
    indicatorTypeCollection.initialize(indicatorTypesJson);

    const indicatorObjects: IndicatorObjectCollection =
      new IndicatorObjectCollection();
    indicatorObjects.initializeIndicators(indicatorsData, deviceCollection);

    deviceCollection.initializeOverlayIndicators(
      deviceIndicatorsJson,
      indicatorTypeCollection,
      indicatorObjects,
    );

    const iconMap: Map<string, string> = new Map();
    Object.entries(iconDataJson).forEach((item) => {
      iconMap.set(item[0], item[1]);
    });

    deviceCollection.initializeIcons(iconMap);

    expect(flow1?.indicators.length).toBe(1);
    expect(flow1?.indicators[0].oname).toBe('C022001000715601');
    expect(flow1?.indicators[0].otype).toBe('SDVAL_FLOW_W');
    expect(flow1?.icon).toBe('\\ue69b');
    expect(flow1?.highlightIcon).toBe('\\ue69b');

    expect(
      flow1?.getIndicatorTypeById('SDVAL_FLOW_W@C022001000715601')?.otype,
    ).toBe('SDVAL_FLOW_W');
    expect(flow1?.getIndicatorTypeById('unknown')).toBeUndefined();

    expect(flow2?.indicators.length).toBe(0);
    expect(flow2?.feature).toBeUndefined();
    if (flow2) flow2.setFeature('mokeFeature');
    expect(flow2?.feature).toBe('mokeFeature');

    const pond = deviceCollection.getDevice('DEV_POND', 'C0220010002003');
    expect(pond?.icon).toBe('\\ue6a8');

    const rainfall = deviceCollection.getDevice('DEV_RAINFALL', 'G3531');
    expect(rainfall?.icon).toBe('');

    const propertyInfo = new PropertyInfo('DEV_FLOW', '液位');

    propertyInfo.initialize(
      devFlowPropViewData,
      devFlowPropsData,
      [],
      [],
      true,
    );

    const category0 = propertyInfo.categories[0] as GeneralPropertyCategory;
    expect(category0.title).toBe('监测指标');
    expect(category0.propertyItems.length).toBe(13);
    expect(category0.getChartProperties([]).length).toEqual(0);
    expect(
      category0.getChartProperties(flow1?.indicators || []).length,
    ).toEqual(1);

    const category2 = propertyInfo.categories[2] as IndicatorPropertyCategory;
    expect(category2.title).toBe('指标模拟精度');
    expect(
      category2.getChartProperties(flow1?.indicators || []).length,
    ).toEqual(8);
  });
});

describe('PlantsAndPumpStationsCollection', () => {
  const mockData = [factoryJson1, factoryJson2];

  it('should initialize pumps map correctly', () => {
    const collection = new PlantsAndPumpStationsCollection(mockData);
    const { pumpsMap } = collection;

    expect(pumpsMap.size).toBe(2);
    const pumpList1 = pumpsMap.get('SDFOLD_FACT@BQ-SC');
    expect(pumpList1).toBeDefined();
    expect(pumpList1?.length).toBe(1);
    expect(pumpList1?.[0]?.title).toBe('2#');
    expect(pumpList1?.[0]?.indicators.length).toBe(2);
    expect(pumpList1?.[0]?.onOffIndicator?.oname).toBe('BQSC86');

    const pumpList2 = pumpsMap.get('SDFOLD_FACT@XQ-SC');
    expect(pumpList2).toBeDefined();
    expect(pumpList2?.length).toBe(2);
    expect(pumpList2?.[1].title).toBe('2#');
    expect(pumpList2?.[1]?.indicators.length).toBe(2);
    expect(pumpList2?.[1]?.onOffIndicator?.oname).toBe(
      'lt.lt.FacO6.EBF.PUMP2.FREQ',
    );
  });
});
