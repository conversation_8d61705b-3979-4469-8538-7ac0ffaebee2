/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  GisMapLayerData,
  ImageArcGisLayerData,
  WmtsLayerData,
} from '../gis-layer-data';
import { mockGisMapDataArgs } from '../mock/mock-layers';

describe('gis layers', () => {
  const gisLayerData: GisMapLayerData = new GisMapLayerData(
    'gis_map',
    'GisMapLayerMap',
    'map',
    '',
    undefined,
    undefined,
    mockGisMapDataArgs.layers,
  );

  it('GisMapLayerData layerParams', () => {
    const { layerParams } = gisLayerData;
    expect(layerParams.size).toBe(2);
    const defaultLayerData = layerParams.get('default') as ImageArcGisLayerData;
    expect(defaultLayerData.layerParams?.projection).toBe('EPSG:4549');
    const darkLayerData = layerParams.get('dark') as WmtsLayerData;
    expect(darkLayerData.layerParams?.layer).toBe('FZDT_FZ_Basemap_cache6');
  });
});
