/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  mockGisDMA,
  mockGisNoShape,
  mockGisPipe,
  mockGisValve,
} from '../mock/mock-gis-object';
import { mockModelPipe, mockModelValve } from '../mock/mock-model-object';

describe('gis-object', () => {
  it('gis pipe object', () => {
    expect(mockGisPipe.id).toEqual('62@121059');
    expect(mockGisPipe.layerName).toEqual('管线');
    expect(mockGisPipe.oname).toEqual('121059');
    expect(mockGisPipe.otype).toEqual('62');
    expect(mockGisPipe.title).toEqual('121059');
    expect(mockGisPipe.indicators).toEqual([]);
    expect(mockGisPipe.shape).toEqual(
      'LINESTRING(436881.78500000015 2881081.6500000004,436721.5580000002 2881230.0441999994)',
    );
    expect(mockGisPipe.shapeType).toEqual('LINE');
    expect(mockGisPipe.attributes.length).toBe(34);
    expect(mockGisPipe.highlightIcon).toBeUndefined();
    expect(mockGisPipe.refModelObject).toBeUndefined();
    mockGisPipe.refModelObject = mockModelPipe;
    expect(mockGisPipe.refModelObject.id).toEqual(mockModelPipe.id);
  });

  it('gis valve object', () => {
    expect(mockGisValve.id).toEqual('13@37765');
    expect(mockGisValve.layerName).toEqual('阀门');
    expect(mockGisValve.oname).toEqual('37765');
    expect(mockGisValve.otype).toEqual('13');
    expect(mockGisValve.title).toEqual('37765');
    expect(mockGisValve.indicators).toEqual([]);
    expect(mockGisValve.shape).toEqual(
      'POINT(436842.0645000003 2881127.3817999996)',
    );
    expect(mockGisValve.shapeType).toEqual('POINT');
    expect(mockGisValve.attributes.length).toBe(42);
    expect(mockGisValve.highlightIcon).toBeUndefined();
    expect(mockGisValve.refModelObject).toBeUndefined();
    mockGisValve.refModelObject = mockModelValve;
    expect(mockGisValve.refModelObject.id).toEqual(mockModelValve.id);
  });

  it('gis DMA object', () => {
    expect(mockGisDMA.shapeType).toEqual('POLYGON');
  });

  it('gis object no shape', () => {
    expect(mockGisNoShape.shape).toEqual('');
    expect(mockGisNoShape.shapeType).toEqual('UNKNOWN');
  });
});
