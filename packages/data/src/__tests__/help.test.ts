/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getHelpDescription, HELP, HelpKeysType } from '../const/help';

describe('getHelpContext', () => {
  it('should return the correct help context for a valid key', () => {
    const key = 'totalFlow';
    const helpContext = getHelpDescription(key);

    expect(helpContext).toBe(HELP[key]);
  });

  it('should return "无" for an invalid key', () => {
    const invalidKey = 'invalidKey';
    const helpContext = getHelpDescription(invalidKey as HelpKeysType);

    expect(helpContext).toBe('无');
  });

  it('should replace the placeholder with the argument more', () => {
    const key = 'plantFlowStandard';
    const helpContext = getHelpDescription(key, '10%', '80');

    expect(helpContext).toBe(
      '出厂流量多时段相对误差均值(δ)控制在 10% 范围内的个数占总数的 80 % 以上',
    );
  });

  it('should not replace the placeholder if no argument is provided', () => {
    const key = 'networkFlowStandard';
    const helpContext = getHelpDescription(key);

    expect(helpContext).toBe(HELP[key]);
  });
});
