/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeviceCollection } from '../device';
import {
  formatNumberValue,
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '../indicator';
import { deviceJsonArray, indicatorTypesJson } from '../mock/mock-device';

describe('indicator type', () => {
  it('initialize indicator types', () => {
    const indicatorTypes: IndicatorTypeCollection =
      new IndicatorTypeCollection();
    indicatorTypes.initialize(indicatorTypesJson);
    expect(indicatorTypes.getIndicatorType('SDVAL_FLOW_W')?.title).toBe(
      '瞬时流量',
    );
  });

  it('invalid indicator objects', () => {
    const indicatorFlow1 = {
      LIMIT_MAX: '3.5',
      // OTYPE: 'SDVAL_FLOW_W', // undefined
      ONAME: 'C022001000715601',
      PNAME: 'C0220010007156',
      PTYPE: 'DEV_FLOW',
      TITLE: null,
    };

    const indicatorFlow2 = {
      LIMIT_MAX: '3.5',
      OTYPE: 'DEV_PF', // not start with SDVAL
      ONAME: 'C022001000715601',
      PNAME: 'C0220010007156',
      PTYPE: 'DEV_FLOW',
      TITLE: null,
    };

    const indicatorFlow3 = {
      LIMIT_MAX: null,
      ONAME: 2356,
      OTYPE: 'SDVAL_FLOW_W',
      PNAME: 'STA42',
      PTYPE: 'DEV_PF',
      TITLE: 'XX流量计',
    };

    const invalidData = [indicatorFlow1, indicatorFlow2, indicatorFlow3];

    const deviceCollection: DeviceCollection = new DeviceCollection();
    deviceCollection.initializeDevices(deviceJsonArray, []);
    const indicatorObjects: IndicatorObjectCollection =
      new IndicatorObjectCollection();
    indicatorObjects.initializeIndicators(invalidData, deviceCollection);

    expect(
      indicatorObjects.getIndicator('SDVAL_FLOW_W', 'C022001000715601'),
    ).toBeUndefined();
    expect(
      indicatorObjects.getIndicator('DEV_PF', 'C022001000715601'),
    ).toBeUndefined();
    expect(
      indicatorObjects.getIndicator('SDVAL_FLOW_W', '2356'),
    ).toBeUndefined();
  });
});

describe('formatNumberValue', () => {
  it('should return undefined for null or undefined input', () => {
    expect(formatNumberValue(null)).toBeUndefined();
    expect(formatNumberValue(undefined)).toBeUndefined();
  });

  it('should return a number for a valid numeric string', () => {
    expect(formatNumberValue('123')).toBe(123);
    expect(formatNumberValue('-456')).toBe(-456);
    expect(formatNumberValue('0')).toBe(0);
  });

  it('should return undefined for non-numeric strings', () => {
    expect(formatNumberValue('abc')).toBeUndefined();
    expect(formatNumberValue('12.34.56')).toBeUndefined();
    expect(formatNumberValue(' 1 0 0 ')).toBeUndefined();
  });

  it('should handle whitespace in the input string', () => {
    expect(formatNumberValue('   789   ')).toBe(789);
    expect(formatNumberValue('')).toBe(0);
  });

  it('should return undefined for NaN values', () => {
    expect(formatNumberValue('NaN')).toBeUndefined();
    expect(formatNumberValue('Infinity')).toBeUndefined();
  });
});
