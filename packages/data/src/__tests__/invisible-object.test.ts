/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MODEL_SUMMARY_OBJECT,
  SCADA_SUMMARY_OBJECT,
} from '../const/system-const';

describe('test invisible-object', () => {
  it('sdava summary', () => {
    expect(SCADA_SUMMARY_OBJECT.id).toBe('SCADA_SUMMARY@SUMMARY');
    expect(SCADA_SUMMARY_OBJECT.otype).toBe('SCADA_SUMMARY');
    expect(SCADA_SUMMARY_OBJECT.oname).toBe('SUMMARY');
    expect(SCADA_SUMMARY_OBJECT.title).toBe('SCADA汇总');
    expect(SCADA_SUMMARY_OBJECT.shape).toBeUndefined();
    expect(SCADA_SUMMARY_OBJECT.shapeType).toBe('UNKNOWN');
    expect(SCADA_SUMMARY_OBJECT.indicators.length).toBe(0);
  });

  it('model summary', () => {
    expect(MODEL_SUMMARY_OBJECT.otype).toBe('SPY_SUMMARY');
    expect(MODEL_SUMMARY_OBJECT.oname).toBe('SUMMARY');
    expect(MODEL_SUMMARY_OBJECT.title).toBe('模型汇总');
  });
});
