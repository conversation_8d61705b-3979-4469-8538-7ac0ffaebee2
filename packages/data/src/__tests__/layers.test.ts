/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeviceCollection } from '../device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '../indicator';
import { FeatureLayerData, LayerDataCollection } from '../layer-data';
import {
  deviceIndicatorsJson,
  deviceJsonArray,
  indicatorsData,
  indicatorTypesJson,
} from '../mock/mock-device';
import { displayLayers, layerDetails } from '../mock/mock-layers';

describe('layers', () => {
  const layerCollection = new LayerDataCollection();
  layerCollection.initialize(displayLayers, layerDetails);
  const deviceCollection: DeviceCollection = new DeviceCollection();
  deviceCollection.initializeDevices(deviceJsonArray, []);
  const indicatorCollection: IndicatorTypeCollection =
    new IndicatorTypeCollection();
  indicatorCollection.initialize(indicatorTypesJson);
  const indicatorObjects: IndicatorObjectCollection =
    new IndicatorObjectCollection();
  indicatorObjects.initializeIndicators(indicatorsData, deviceCollection);

  deviceCollection.initializeOverlayIndicators(
    deviceIndicatorsJson,
    indicatorCollection,
    indicatorObjects,
  );
  layerCollection.initializeDevices(deviceCollection);

  it('initialize LayerCollection', () => {
    expect(layerCollection.layerDatas.length).toBe(6);
    expect(layerCollection.layerDatas[0].name).toBe('BAIDU_MAP');
    expect(layerCollection.layerDatas[1].name).toBe('GIS_SERVICE');
    expect(layerCollection.layerDatas[2].name).toBe('WDM_PIPES');
    expect(layerCollection.layerDatas[3].name).toBe('SDFOLD_DEV_PUMP_ROOT');
    expect(layerCollection.layerDatas[4].name).toBe('SDFOLD_NETWORK_RAIN');
    expect(layerCollection.layerDatas[5].name).toBe('SDFOLD_NETWORK_RAIN2');
    const layer = layerCollection.getLayer('SDFOLD_NETWORK_RAIN');
    expect(layer?.name).toBe('SDFOLD_NETWORK_RAIN');
    expect(layer?.title).toBe('水厂');
    expect(layer?.icon).toBe('');
    expect(
      layerCollection.getLayer('SDFOLD_STATION_WATER_ROOT'),
    ).toBeUndefined();
  });

  it('devices in layer', () => {
    const layer = layerCollection.getLayer('SDFOLD_NETWORK_RAIN');
    expect(layer?.type).toBe('LayerFeature');
    const featureLayer = layer as FeatureLayerData;
    expect(featureLayer.devices.size).toBe(2);
    const deviceFlow1 = featureLayer.devices.get('DEV_FLOW@C0220010007156');
    expect(deviceFlow1?.indicators.length).toBe(1);
  });
});
