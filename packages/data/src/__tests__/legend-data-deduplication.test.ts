/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupData, LegendGroupDataCollection } from '../legend-data';

describe('Legend Data Deduplication', () => {
  let legendCollection: LegendGroupDataCollection;

  beforeEach(() => {
    legendCollection = new LegendGroupDataCollection();
  });

  test('应该对重复的图例组进行去重', () => {
    const duplicatedLegendData: LegendGroupData[] = [
      {
        name: 'test-legend',
        title: '测试图例',
        type: 'checkbox',
        items: [
          {
            id: 1,
            color: '#ff0000',
            count: 10,
            title: '项目1',
            value: 'value1',
            checked: true,
          },
          {
            id: 2,
            color: '#00ff00',
            count: 20,
            title: '项目2',
            value: 'value2',
            checked: true,
          },
        ],
      },
      {
        name: 'test-legend',
        title: '测试图例',
        type: 'checkbox',
        items: [
          {
            id: 3,
            color: '#0000ff',
            count: 30,
            title: '项目3',
            value: 'value3',
            checked: true,
          },
        ],
      },
      {
        name: 'another-legend',
        title: '另一个图例',
        type: 'text',
        items: [
          {
            id: 4,
            color: '#ffff00',
            count: 40,
            title: '项目4',
            value: 'value4',
            checked: false,
          },
        ],
      },
    ];

    legendCollection.updateLegendData(duplicatedLegendData);
    const result = legendCollection.legendDataCollection;

    // 应该只有2个不同的图例组
    expect(result).toHaveLength(2);

    // 第一个组应该包含合并后的所有项目
    const firstGroup = result.find((group) => group.name === 'test-legend');
    expect(firstGroup).toBeDefined();
    expect(firstGroup!.items).toHaveLength(3);

    // 第二个组保持不变
    const secondGroup = result.find((group) => group.name === 'another-legend');
    expect(secondGroup).toBeDefined();
    expect(secondGroup!.items).toHaveLength(1);
  });

  test('应该对重复的图例项进行去重', () => {
    const legendDataWithDuplicateItems: LegendGroupData[] = [
      {
        name: 'test-legend',
        title: '测试图例',
        type: 'checkbox',
        items: [
          {
            id: 1,
            color: '#ff0000',
            count: 10,
            title: '项目1',
            value: 'value1',
            checked: true,
          },
          {
            id: 1,
            color: '#ff0000',
            count: 15,
            title: '项目1重复',
            value: 'value1-dup',
            checked: false,
          },
          {
            id: 2,
            color: '#00ff00',
            count: 20,
            title: '项目2',
            value: 'value2',
            checked: true,
          },
        ],
      },
    ];

    legendCollection.updateLegendData(legendDataWithDuplicateItems);
    const result = legendCollection.legendDataCollection;

    expect(result).toHaveLength(1);
    const group = result[0];

    // 应该只保留第一个 id=1 的项目，去掉重复的
    expect(group.items).toHaveLength(2);
    expect(group.items.filter((item) => item.id === 1)).toHaveLength(1);
    expect(group.items.filter((item) => item.id === 2)).toHaveLength(1);
  });

  test('应该处理空数据', () => {
    legendCollection.updateLegendData([]);
    const result = legendCollection.legendDataCollection;
    expect(result).toHaveLength(0);
  });

  test('initialize方法应该自动去重', () => {
    const mockLegendData = {
      category1: [
        {
          legend: 'test-legend',
          title: '测试图例',
          values: [
            {
              legend_id: 1,
              color: 'ff0000ff',
              title: '项目1',
              value: 'value1',
              legend_hidden: false,
            },
            {
              legend_id: 1,
              color: 'ff0000ff',
              title: '项目1重复',
              value: 'value1-dup',
              legend_hidden: true,
            },
            {
              legend_id: 2,
              color: '00ff00ff',
              title: '项目2',
              value: 'value2',
              legend_hidden: false,
            },
          ],
        },
        {
          legend: 'test-legend',
          title: '测试图例',
          values: [
            {
              legend_id: 3,
              color: '0000ffff',
              title: '项目3',
              value: 'value3',
              legend_hidden: false,
            },
          ],
        },
      ],
    };

    legendCollection.initialize(mockLegendData, {});
    const result = legendCollection.legendDataCollection;

    // 应该合并成一个图例组
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('test-legend');

    // 应该有3个不同的项目（id=1的重复项被去除）
    expect(result[0].items).toHaveLength(3);
    expect(result[0].items.filter((item) => item.id === 1)).toHaveLength(1);
    expect(result[0].items.filter((item) => item.id === 2)).toHaveLength(1);
    expect(result[0].items.filter((item) => item.id === 3)).toHaveLength(1);
  });
});
