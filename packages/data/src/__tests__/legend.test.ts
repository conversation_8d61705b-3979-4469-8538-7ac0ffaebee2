/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupDataCollection } from '../legend-data';
import {
  mockInvalidLegendGroupData,
  mockLegendGroupData,
} from '../mock/mock-legend';

describe('legend', () => {
  it('if legend data is null', () => {
    const legend: LegendGroupDataCollection = new LegendGroupDataCollection();
    legend.initialize(null, null);
    expect(legend.legendDataCollection.length).toBe(0);
  });

  it('if legend data is undefined', () => {
    const legend: LegendGroupDataCollection = new LegendGroupDataCollection();
    legend.initialize(undefined, undefined);
    expect(legend.legendDataCollection.length).toBe(0);
  });

  it('if legend data is empty object', () => {
    const legend: LegendGroupDataCollection = new LegendGroupDataCollection();
    legend.initialize({}, {});
    expect(legend.legendDataCollection.length).toBe(0);
  });

  it('initialize legend', () => {
    const legendDataCollection = mockLegendGroupData();
    expect(legendDataCollection.length).toBe(5);
    expect(legendDataCollection[0].name).toBe('DEV_TIMELY#DEV_TIMELY#AA');
    expect(legendDataCollection[0].title).toBe('当前数据时效');
    expect(legendDataCollection[0].icon).toBe('\\ue68f');
    expect(legendDataCollection[0].items.length).toBe(5);
    expect(legendDataCollection[0].items[0].color).toBe('#1492CCFF');
    expect(legendDataCollection[0].items[0].value).toBe('5');
    expect(legendDataCollection[0].items[0].title).toBe('<5分钟');
    expect(legendDataCollection[0].items[0].size).toBe(undefined);
    expect(legendDataCollection[0].items[0].id).toBe(9);
    expect(legendDataCollection[0].items[0].checked).toBe(false);
    expect(legendDataCollection[0].items[1].checked).toBe(true);
  });

  it('invalid data', () => {
    const legendDataCollection = mockInvalidLegendGroupData();
    expect(legendDataCollection[3].icon).toBeUndefined();
  });
  it('if legendDataItem.legend is null', () => {
    const legendDataCollection = mockInvalidLegendGroupData();
    expect(legendDataCollection[0].type).toBe('text');
    expect(legendDataCollection[0].items[1].value).toBe('');
    expect(legendDataCollection[0].items[2].title).toBe('未定义');
  });

  it('if legendDataItem is empty', () => {
    const legendDataCollection = mockInvalidLegendGroupData();
    expect(legendDataCollection[1].title).toBe('未定义');
  });

  it('if ledendDataItem.values is null', () => {
    const legendDataCollection = mockInvalidLegendGroupData();
    expect(legendDataCollection[2].items.length).toBe(0);
  });

  it('if legendDataValue.color not is string', () => {
    const legendDataCollection = mockInvalidLegendGroupData();
    expect(legendDataCollection[3].items[0].color).toBe('');
    expect(legendDataCollection[3].items[1].color).toBe('');
  });
});
