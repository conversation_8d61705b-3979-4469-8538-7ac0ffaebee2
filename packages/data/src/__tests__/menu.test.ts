/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { injectBadgeToMenu } from '../sidebar-menu-data';

describe('injectBadgeToMenu', () => {
  it('should correctly inject badges into menu items', () => {
    const menuData = [
      {
        key: 'dashboard',
        title: 'Dashboard',
        badge: 2,
        children: [
          {
            key: 'sub1',
            title: 'Submenu 1',
            badge: 1,
          },
          {
            key: 'sub2',
            title: 'Submenu 2',
            badge: 1,
            children: [
              {
                key: 'sub2-1',
                title: 'Subsubmenu 2-1',
              },
            ],
          },
        ],
      },
      {
        key: 'reports',
        title: 'Reports',
      },
    ];

    const badgeUpdates = {
      dashboard: 5, // Update the badge number for dashboard
      'sub2-1': 3, // Add a badge number to sub2-1 which did not previously have one
    };

    const updatedMenu = injectBadgeToMenu(menuData, badgeUpdates);

    expect(updatedMenu).toEqual([
      {
        key: 'dashboard',
        title: 'Dashboard',
        badge: 5,
        children: [
          {
            key: 'sub1',
            title: 'Submenu 1',
            badge: 1, // Should remain unchanged
          },
          {
            key: 'sub2',
            title: 'Submenu 2',
            badge: 1, // Should remain unchanged
            children: [
              {
                key: 'sub2-1',
                title: 'Subsubmenu 2-1',
                badge: 3, // Should be updated
              },
            ],
          },
        ],
      },
      {
        key: 'reports',
        title: 'Reports',
        badge: undefined, // Should remain unchanged
      },
    ]);
  });

  it('should handle zero badges correctly', () => {
    const menuData = [
      {
        key: 'dashboard',
        title: 'Dashboard',
        badge: 5,
      },
    ];

    const badgeUpdates = {
      dashboard: 0, // Setting badge to 0 should remove the badge
    };

    const updatedMenu = injectBadgeToMenu(menuData, badgeUpdates);

    expect(updatedMenu[0].badge).toBeUndefined();
  });
});
