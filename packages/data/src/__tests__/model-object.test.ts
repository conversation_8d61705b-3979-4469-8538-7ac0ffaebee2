/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  mockModelDMA,
  mockModelJunction,
  mockModelPipe,
  mockModelUnknownShape,
} from '../mock/mock-model-object';

describe('model-object', () => {
  it('model pipe object', () => {
    expect(mockModelPipe.id).toEqual('WDM_PIPES@P00001');
    expect(mockModelPipe.oname).toEqual('P00001');
    expect(mockModelPipe.otype).toEqual('WDM_PIPES');
    expect(mockModelPipe.title).toEqual('P00001');
    expect(mockModelPipe.indicators).toEqual([]);
    expect(mockModelPipe.shape).toEqual(
      'LINESTRING(436881.78500000015 2881081.6500000004,436721.5580000002 2881230.0441999994)',
    );
    expect(mockModelPipe.shapeType).toEqual('LINE');
    expect(mockModelPipe.highlightIcon).toBeUndefined();
  });

  it('model junction object', () => {
    expect(mockModelJunction.id).toEqual('WDM_JUNCTIONS@J00001');
    expect(mockModelJunction.oname).toEqual('J00001');
    expect(mockModelJunction.otype).toEqual('WDM_JUNCTIONS');
    expect(mockModelJunction.title).toEqual('J00001');
    expect(mockModelJunction.indicators).toEqual([]);
    expect(mockModelJunction.shape).toEqual(
      'POINT(436881.78500000015 2881081.6500000004)',
    );
    expect(mockModelJunction.shapeType).toEqual('POINT');
    expect(mockModelJunction.highlightIcon).toBeUndefined();
  });

  it('model DMA object', () => {
    expect(mockModelDMA.shapeType).toEqual('POLYGON');
  });

  it('model unknown object', () => {
    expect(mockModelUnknownShape.shapeType).toEqual('UNKNOWN');
  });
});
