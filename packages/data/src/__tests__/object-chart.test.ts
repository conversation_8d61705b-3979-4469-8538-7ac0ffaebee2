/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { convertChainBaseData } from '../object-chart';
import { TimeData } from '../time-data';

const exampleTimeData: TimeData[] = [
  { time: '2023-01-01 12:00:00', value: 10 },
  { time: '2023-01-02 12:00:00', value: 20 },
];
describe('object-char: convertChainBaseData', () => {
  it('should return the same timeData if the input array is empty', () => {
    const chainBaseDate = '2022-12-31 12:00:00';
    const result = convertChainBaseData(chainBaseDate, []);
    expect(result).toEqual([]);
  });

  it('should convert timeData based on the chainBaseDate', () => {
    const result1 = convertChainBaseData(
      '2022-12-31 12:00:00',
      exampleTimeData,
    );
    const result2 = convertChainBaseData(
      '2023-01-04 12:00:00',
      exampleTimeData,
    );

    // Expected converted time data
    const expectedTimeData1: TimeData[] = [
      { time: '2022-12-31 12:00:00', value: 10 },
      { time: '2023-01-01 12:00:00', value: 20 },
    ];

    const expectedTimeData2: TimeData[] = [
      { time: '2023-01-04 12:00:00', value: 10 },
      { time: '2023-01-05 12:00:00', value: 20 },
    ];

    expect(result1).toEqual(expectedTimeData1);
    expect(result2).toEqual(expectedTimeData2);
  });

  it('should convert timeData when diff time is not more than 1 day', () => {
    const result = convertChainBaseData('2024-01-01 00:00:00', exampleTimeData);

    const expectedTimeData: TimeData[] = [
      { time: '2024-01-01 12:00:00', value: 10 },
      { time: '2024-01-02 12:00:00', value: 20 },
    ];
    expect(result).toEqual(expectedTimeData);
  });
});
