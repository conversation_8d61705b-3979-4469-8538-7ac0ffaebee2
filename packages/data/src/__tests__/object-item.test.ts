/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { mockModelJunction, mockModelPipe } from '../mock/mock-model-object';
import {
  getIObjectIds,
  getShapeCenter,
  getShapeExtent,
  getShapesCenter,
  getShapesExtent,
  getShapeType,
  makeId,
  makeObjectId,
  splitId,
  splitObjectId,
  validateShape,
} from '../object-item';

describe('objectID', () => {
  it('id', () => {
    expect(makeObjectId('WDM_JUNCTIONS', 'J12345')).toEqual(
      'WDM_JUNCTIONS@J12345',
    );
    expect(splitObjectId('WDM_JUNCTIONS@J12345')).toEqual([
      'WDM_JUNCTIONS',
      'J12345',
    ]);
    expect(makeId('WDM_JUNCTIONS', 'J12345')).toEqual('WDM_JUNCTIONS@J12345');
    expect(splitId('WDM_JUNCTIONS@J12345')).toEqual([
      'WDM_JUNCTIONS',
      'J12345',
    ]);
    expect(getIObjectIds([mockModelPipe, mockModelJunction])).toEqual([
      'WDM_PIPES@P00001',
      'WDM_JUNCTIONS@J00001',
    ]);
  });
});

describe('getShapeType', () => {
  it('getShapeType', () => {
    expect(getShapeType('POINT(1.234 2.345)')).toEqual('POINT');
    expect(getShapeType('LINESTRING(1.234 2.345, 1.236 2.347)')).toEqual(
      'LINE',
    );
    expect(
      getShapeType('POLYGON((1.234 2.345, 1.236 2.347, 1.238 2.349))'),
    ).toEqual('POLYGON');
    expect(
      getShapeType(
        'MULTIPOLYGON(((1.234 2.345, 1.236 2.347, 1.238 2.349)),((1.1 1, 2 2, 3 3)))',
      ),
    ).toEqual('MULTIPOLYGON');
    expect(getShapeType('')).toEqual('UNKNOWN');
    expect(getShapeType('unknown')).toEqual('UNKNOWN');
    expect(getShapeType(undefined)).toEqual('UNKNOWN');
  });
});

describe('getShapeCenter', () => {
  it('invalid cases', () => {
    expect(getShapeCenter('POINT(1.234 a)')).toEqual([]);
    expect(getShapeCenter('POINT(1.234 2.34 5)')).toEqual([]);
    expect(getShapeCenter('LINESTRING(1.234 a)')).toEqual([]);
    expect(getShapeCenter('POLYGON((1.234 a))')).toEqual([]);
    expect(getShapesCenter(['POLYGON((1.234 a))'])).toEqual([]);
  });

  it('valid cases', () => {
    expect(getShapeCenter('POINT(1.234 2.345)')).toEqual([1.234, 2.345]);
    expect(getShapeCenter('LINESTRING(1.234 2.345, 1.236 2.347)')).toEqual([
      1.2349999999999999, 2.346,
    ]);
    expect(
      getShapeCenter('POLYGON((1.234 2.345, 1.236 2.347, 1.238 2.349))'),
    ).toEqual([1.236, 2.347]);
    expect(
      getShapeCenter(
        'MULTIPOLYGON(((1.234 2.345, 1.236 2.347, 1.238 2.349)),((1.1 1, 2 2, 3 3)))',
      ),
    ).toEqual([1.236, 2.347]);
    expect(
      getShapesCenter([
        'LINESTRING(1.234 2.345, 1.236 2.347)',
        'LINESTRING(1.236 2.347, 1.238 2.349)',
      ]),
    ).toEqual([1.236, 2.347]);
  });
});

describe('validateShape', () => {
  it('invalid cases', () => {
    expect(validateShape('POINT(1.234 a)')).toEqual(false);
    expect(validateShape('POINT(NaN NaN)')).toEqual(false);
    expect(validateShape('POINT EMPTY')).toEqual(false);
    expect(validateShape('LINESTRING(1.234 a)')).toEqual(false);
    expect(validateShape('POLYGON((1.234 a))')).toEqual(false);
    expect(
      validateShape(
        'MULTIPOLYGON(((1.234 NaN, 1.236 b, 1.238 a)),((1.1 1, 2 2, 3 3)))',
      ),
    ).toEqual(false);
  });

  it('valid cases', () => {
    expect(validateShape('POINT(1.234 2.345)')).toEqual(true);
    expect(validateShape('LINESTRING(1.234 2.345, 1.236 2.347)')).toEqual(true);
    expect(
      validateShape('POLYGON((1.234 2.345, 1.236 2.347, 1.238 2.349))'),
    ).toEqual(true);
    expect(
      validateShape(
        'MULTIPOLYGON(((1.234 2.345, 1.236 2.347, 1.238 2.349)),((1.1 1, 2 2, 3 3)))',
      ),
    ).toEqual(true);
  });
});

describe('getShapeExtent', () => {
  const empty: number[] = [Infinity, Infinity, -Infinity, -Infinity];
  it('invalid cases', () => {
    expect(getShapeExtent('POINT(1.234 a)')).toEqual(empty);
    expect(getShapeExtent('POINT(1.234 2.34 5)')).toEqual(empty);
    expect(getShapeExtent('LINESTRING(1.234 a)')).toEqual(empty);
    expect(getShapeExtent('POLYGON((1.234 a))')).toEqual(empty);
  });

  it('valid cases', () => {
    const extPoint = getShapeExtent('POINT(1.234 2.345)');
    expect(extPoint).toEqual([1.234, 2.345, 1.234, 2.345]);

    const extLine = getShapeExtent('LINESTRING(1.234 2.345, 1.236 2.347)');
    expect(extLine).toEqual([1.234, 2.345, 1.236, 2.347]);

    const extPolygon = getShapeExtent(
      'POLYGON((1.234 2.345, 1.236 2.347, 1.238 2.349))',
    );
    expect(extPolygon).toEqual([1.234, 2.345, 1.238, 2.349]);

    const extMultiPolygon = getShapeExtent(
      'MULTIPOLYGON(((1.234 2.345, 1.236 2.347, 1.238 2.349)),((1.1 1, 2 2, 3 3)))',
    ); // only consider the first polygon
    expect(extMultiPolygon).toEqual([1.234, 2.345, 1.238, 2.349]);
  });
});

describe('getShapesExtent', () => {
  const empty: number[] = [Infinity, Infinity, -Infinity, -Infinity];
  it('invalid cases', () => {
    expect(getShapesExtent([], 1)).toEqual(empty);
  });

  it('valid cases', () => {
    expect(
      getShapesExtent(['POINT(1.234 2.345)', 'POINT(3.234 4.345)'], 1),
    ).toEqual([1.234, 2.345, 3.234, 4.345]);
    expect(
      getShapesExtent(['POINT(1.234 2.345)', 'POINT(3.234 4.345)'], 2),
    ).toEqual([0.23399999999999999, 1.3450000000000004, 4.234, 5.345]);
  });
});
