/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getValueGroupParams,
  isBetweenRange,
  ObservationScadaBaseItem,
  ObservationType,
} from '../observation-scada-data';

describe('getValueGroupParams', () => {
  it('should return an empty object when the input list is empty', () => {
    const list: ObservationScadaBaseItem[] = [];
    const params = getValueGroupParams(list);
    expect(params).toEqual({});
  });

  it('should generate the correct params object for SCADA type items', () => {
    const list = [
      {
        id: '1',
        oname: 'STA1691',
        otype: 'DEV_PF',
        vprop: 'VOLTAGE',
        type: ObservationType.SCADA,
      },
      {
        id: '2',
        oname: 'STA1945',
        otype: 'DEV_PF',
        vprop: 'CURRENT',
        type: ObservationType.SCADA,
      },
    ] as ObservationScadaBaseItem[];
    const params = getValueGroupParams(list);
    expect(params).toEqual({
      'DEV_PF@STA1691@VOLTAGE@CALCULATION': {
        vprop: 'VOLTAGE',
        otype: 'DEV_PF',
        oname: 'STA1691',
        rmode: '@CALCULATION',
      },
      'DEV_PF@STA1691@VOLTAGE': {
        vprop: 'VOLTAGE',
        otype: 'DEV_PF',
        oname: 'STA1691',
      },
      'DEV_PF@STA1945@CURRENT': {
        vprop: 'CURRENT',
        otype: 'DEV_PF',
        oname: 'STA1945',
      },
      'DEV_PF@STA1945@CURRENT@CALCULATION': {
        vprop: 'CURRENT',
        otype: 'DEV_PF',
        oname: 'STA1945',
        rmode: '@CALCULATION',
      },
    });
  });

  it('should generate the correct params object for non-SCADA type items', () => {
    const list = [
      {
        id: '3',
        oname: 'MODEL123',
        otype: 'MODEL',
        vprop: 'TEMPERATURE',
        type: ObservationType.MODEL,
      },
    ] as ObservationScadaBaseItem[];
    const params = getValueGroupParams(list);
    expect(params).toEqual({
      'MODEL@MODEL123@TEMPERATURE@CALCULATION': {
        vprop: 'TEMPERATURE',
        otype: 'MODEL',
        oname: 'MODEL123',
      },
    });
  });
});

describe('isBetweenRange', () => {
  it('should return true when value is within the specified range', () => {
    expect(isBetweenRange(5, 0, 10)).toBe(true); // value: 5, min: 0, max: 10
    expect(isBetweenRange(-5, -10, 0)).toBe(true); // value: -5, min: -10, max: 0
    expect(isBetweenRange(0, undefined, 100)).toBe(true); // value: 0, max: 100 (min is undefined)
    expect(isBetweenRange(100, 0, undefined)).toBe(true); // value: 100, min: 0 (max is undefined)
  });

  it('should return false when value is outside the specified range', () => {
    expect(isBetweenRange(15, 0, 10)).toBe(false); // value: 15, min: 0, max: 10
    expect(isBetweenRange(-15, -10, 0)).toBe(false); // value: -15, min: -10, max: 0
    expect(isBetweenRange(-5, undefined, -10)).toBe(false); // value: -5, max: -10 (min is undefined)
    expect(isBetweenRange(100, 0, 50)).toBe(false); // value: 100, min: 0, max: 50
  });

  it('should return true when value is not a number', () => {
    expect(isBetweenRange('abc' as unknown as number, 0, 10)).toBe(true); // value: 'abc', min: 0, max: 10
    expect(isBetweenRange(undefined, 0, 10)).toBe(true); // value: undefined, min: 0, max: 10
    expect(isBetweenRange(null as unknown as number, 0, 10)).toBe(true); // value: null, min: 0, max: 10
  });

  it('should return false when value is not number and the range is not found', () => {
    expect(isBetweenRange(undefined, undefined, undefined)).toBe(true);
  });
});
