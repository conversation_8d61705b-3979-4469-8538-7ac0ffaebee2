/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import mockDatabase from '../mock/mock-database';
import {
  mockDEVPFFormListBase,
  mockDEVPFFormListHaveOrder,
  mockDEVPFFormListInvalidData,
} from '../mock/mock-scada-manager-form';
import { makeObjectId } from '../object-item';
import { formatFormSchema } from '../otype-vprop-config-data';

describe('getFormSchemaTitle', () => {
  const formList = mockDEVPFFormListBase;

  const otype = 'DEV_FLOW';
  const oname = 'dev1';
  const db = mockDatabase();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return an array with updated titles and initial values', () => {
    const result = formatFormSchema(formList, otype, oname, 'device', db);

    formList.forEach((item, index) => {
      const { dataIndex } = item;
      const title = db.getPropertyInfo(otype)?.getPropertyTitle(dataIndex);
      const id = makeObjectId(otype, oname);
      const value = db.getDeviceById(id)?.getPropertyValue(dataIndex);

      expect(result[index].title).toBe(title || dataIndex);
      expect(result[index].initialValue).toBe(value);
      expect(result[0].dataIndex).toBe('TITLE');
    });
  });

  it('should return an empty array when list is invalid data', () => {
    const list = mockDEVPFFormListInvalidData;
    const result = formatFormSchema(list, otype, oname, 'device', db);

    expect(result.length).toBe(0);
  });

  it('should return an sorted array when have order', () => {
    const list = mockDEVPFFormListHaveOrder;
    const result = formatFormSchema(list, otype, oname, 'device', db);

    expect(result[0].dataIndex).toBe('EQUIPMENT_MODEL');
  });
});
