/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  devLiquidPropsData,
  devLiquidPropViewData,
  invalidPropView,
} from '../mock/mock-property-info';
import BasicPropertyCategory from '../property/basic-property-category';
import CorrelationPropertyCategory from '../property/correlation-property-category';
import DiagramPropertyCategory from '../property/diagram-property-category';
import GeneralPropertyCategory from '../property/general-property-category';
import IndicatorPropertyCategory from '../property/indicator-property-category';
import NotePropertyCategory from '../property/note-property-category';
import PicturePropertyCategory from '../property/picture-property-category';
import { PropertyInfo } from '../property/property-info';
import WarnPropertyCategory from '../property/warn-property-category';

describe('property-info', () => {
  const propertyInfo = new PropertyInfo('DEV_LIQUID', '液位');
  propertyInfo.initialize(
    devLiquidPropViewData,
    devLiquidPropsData,
    [],
    [],
    true,
  );

  it('property info', () => {
    expect(propertyInfo.otype).toBe('DEV_LIQUID');
    expect(propertyInfo.categories.length).toBe(10);
  });

  it('property buttons', () => {
    expect(propertyInfo.buttons.length).toBe(3);
    expect(propertyInfo.buttons[0].type).toBe('operations');
    expect(propertyInfo.buttons[0].title).toBe('操作记录');
  });

  it('property category 0', () => {
    const category0 = propertyInfo.categories[0] as GeneralPropertyCategory;
    expect(category0.title).toBe('监测指标');
    expect(category0.propertyItems.length).toBe(5);

    const { name, otype, type, dataMode, editors } = category0.propertyItems[0];
    expect(name).toBe('SDVAL');
    expect(otype).toBe('SDVAL_LIQ');
    expect(type).toBe('Indicator');
    expect(dataMode).toBe('realAndForecast');
    editors?.forEach((item) => {
      if (item.type === 'chart') {
        expect(item.charts).toBe('modelChart');
        expect(item.proCharts).toBe('TIME_MAX_MIN');
        expect(item.type).toBe('chart');
        expect(item.dateType).toBeUndefined();
      }
    });
    expect(category0.getChartProperties([]).length).toEqual(3);
  });

  it('property category 1 - DiagramPropertyCategory', () => {
    const category1 = propertyInfo.categories[1] as DiagramPropertyCategory;
    expect(category1.title).toBe('示意图');
    expect(category1.propertyItems.length).toBe(1);

    const {
      name,
      otype,
      type,
      dataMode,
      editors: editor,
    } = category1.propertyItems[0];
    expect(name).toBe('WARNING_VALUE');
    expect(otype).toBe('SDVAL_WATER_DEPTH');
    expect(type).toBe('Indicator');
    expect(dataMode).toBeUndefined();
    expect(editor).toBeUndefined();
  });

  it('property category 4 - IndicatorPropertyCategory', () => {
    const category4 = propertyInfo.categories[4] as IndicatorPropertyCategory;
    expect(category4.title).toBe('指标评估(昨日)');
    expect(category4.indicatorPropertyItems.length).toBe(2);
    expect(category4.indicatorPropertyItems[0][0]).toBe('SDVAL_LIQ');
    expect(category4.indicatorPropertyItems[0][1].length).toBe(3);
    expect(category4.indicatorPropertyItems[0][1][0].name).toBe('RELIABILITY');
    expect(category4.indicatorPropertyItems[1][0]).toBe('SDVAL_WATER_DEPTH');
    expect(category4.indicatorPropertyItems[1][1].length).toBe(3);
  });

  it('property category 5 - BasicPropertyCategory', () => {
    const category5 = propertyInfo.categories[5] as BasicPropertyCategory;
    expect(category5.title).toBe('基本属性');
    expect(category5.propertyItems.length).toBe(16);

    const { name, otype, type, dataMode, editors } = category5.propertyItems[0];
    expect(name).toBe('ONAME');
    expect(otype).toBeUndefined();
    expect(type).toBe('Property');
    expect(dataMode).toBeUndefined();
    expect(editors).toBeUndefined();

    expect(category5.getChartProperties([])).toEqual([]);
  });

  it('property category 6 - PicturePropertyCategory', () => {
    const category6 = propertyInfo.categories[6] as PicturePropertyCategory;
    expect(category6.title).toBe('设备图片');
    expect(category6.getValueQueryParameters([])).toEqual(new Map());
    expect(category6.getChartProperties([])).toEqual([]);
  });

  it('property category 7 - NotePropertyCategory', () => {
    const category7 = propertyInfo.categories[7] as NotePropertyCategory;
    expect(category7.title).toBe('备注');
    expect(category7.propertyItems.length).toBe(1);

    const { name, otype, type, dataMode, editors } = category7.propertyItems[0];
    expect(name).toBe('REMARK');
    expect(otype).toBeUndefined();
    expect(type).toBe('Property');
    expect(dataMode).toBeUndefined();
    expect(editors).toBeUndefined();
  });

  it('property category 8 - CorrelationPropertyCategory', () => {
    const category8 = propertyInfo.categories[8] as CorrelationPropertyCategory;
    expect(category8.title).toBe('设备相关');
    expect(category8.indicatorPropertyItems.length).toBe(2);
    expect(category8.indicatorPropertyItems[0][0]).toBe('SDVAL_LIQ');
    expect(category8.indicatorPropertyItems[1][0]).toBe('SDVAL_FLOW_W');
    expect(category8.getValueQueryParameters([])).toEqual(new Map());
    expect(category8.getChartProperties([])).toEqual([]);
  });

  it('property category 9 - WarnPropertyCategory', () => {
    const category9 = propertyInfo.categories[9] as WarnPropertyCategory;
    expect(category9.title).toBe('警告列表');
    expect(category9.getValueQueryParameters([])).toEqual(new Map());
    expect(category9.getChartProperties([])).toEqual([]);
  });
});

describe('property-info - invalid property type', () => {
  const propertyInfo = new PropertyInfo('DEV_LIQUID', '液位');
  propertyInfo.initialize(invalidPropView, [], [], [], true);

  it('property info', () => {
    expect(propertyInfo.categories.length).toBe(1);
    const category0 = propertyInfo.categories[0] as GeneralPropertyCategory;
    expect(category0.title).toBe('监测指标');
    expect(category0.propertyItems.length).toBe(1);
    const { name, otype, type, dataMode } = category0.propertyItems[0];
    expect(name).toBe('SDVAL');
    expect(otype).toBe('SDVAL_LIQ');
    expect(type).toBe('Unknown');
    expect(dataMode).toBe('realAndForecast');
  });
});

describe('property-info - no property view', () => {
  it('property info - undefined', () => {
    const propertyInfo = new PropertyInfo('DEV_LIQUID', '液位');
    propertyInfo.initialize(undefined, undefined, undefined, [], true);
    expect(propertyInfo.categories.length).toBe(0);
  });
  it('property info - empty', () => {
    const propertyInfo = new PropertyInfo('DEV_LIQUID', '');
    propertyInfo.initialize(undefined, [{}], undefined, [], true);
    expect(propertyInfo.categories.length).toBe(0);
  });
});
