/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  mockFixedPumpStatus1,
  mockVariablePumpStatus1,
} from '../mock/mock-pump-status';
import {
  formatPumpData,
  getPumpStatusChangeFlags,
  getPumpStatusChangeTimeData,
  PumpStatusChangeFlag,
  PumpStatusChanges,
  PumpTimeData,
  timeDataToPumpStatus,
} from '../pump-status';

describe('Pump Status', () => {
  it('status data length is 0', () => {
    expect(timeDataToPumpStatus([], true)).toEqual([]);
  });
  it('status data of Fixed pump', () => {
    const pumpStatusData = timeDataToPumpStatus(mockFixedPumpStatus1, false);
    expect(pumpStatusData).toStrictEqual([
      { startTimeIndex: 0, endTimeIndex: 720, value: 0 },
      { startTimeIndex: 720, endTimeIndex: 1000, value: 1 },
      { startTimeIndex: 1000, endTimeIndex: 1439, value: 0 },
    ]);
  });

  it('status data of Variable pump', () => {
    const pumpStatusData = timeDataToPumpStatus(mockVariablePumpStatus1, true);
    expect(pumpStatusData).toStrictEqual([
      { startTimeIndex: 0, endTimeIndex: 480, value: 1 },
      { startTimeIndex: 480, endTimeIndex: 960, value: 0 },
      { startTimeIndex: 960, endTimeIndex: 1439, value: 1 },
    ]);
  });
});

describe('formatPumpData', () => {
  it('should return 0 when value is less than min', () => {
    expect(formatPumpData(10, 20, 30)).toBe(0);
  });

  it('should return 50 when value is greater than max', () => {
    expect(formatPumpData(40, 20, 30)).toBe(50);
  });

  it('should return the original value when value is within the range', () => {
    expect(formatPumpData(25, 20, 30)).toBe(25);
  });

  it('should return 0 when value is not a number', () => {
    expect(formatPumpData('invalid', 20, 30)).toBe(0);
  });
});

describe('pump status change', () => {
  const pumpTimeData1: PumpTimeData = {
    name: 'pump1',
    timeData: mockFixedPumpStatus1,
    variable: false,
  };
  const pumpTimeData2: PumpTimeData = {
    name: 'pump2',
    timeData: mockVariablePumpStatus1,
    variable: false,
  };
  const expectResult1: PumpStatusChangeFlag[] = [
    { time: '2023-04-01 12:00:00', change: 1 },
    { time: '2023-04-01 16:40:00', change: -1 },
  ];
  const expectResult2: PumpStatusChangeFlag[] = [
    { time: '2023-04-01 08:00:00', change: -1 },
    { time: '2023-04-01 16:00:00', change: 1 },
  ];

  it('pump status change for fixed pump', () => {
    const pumpChanges: PumpStatusChanges =
      getPumpStatusChangeTimeData(pumpTimeData1);

    expect(pumpChanges.changeFlags).toEqual(expectResult1);
  });

  it('pump status change for variable pump', () => {
    const pumpChanges: PumpStatusChanges =
      getPumpStatusChangeTimeData(pumpTimeData2);

    expect(pumpChanges.changeFlags).toEqual(expectResult2);
  });

  it('pump status change for two pumps', () => {
    const pumpTimeData: Map<string, PumpTimeData[]> = new Map();
    pumpTimeData.set('plant_1', [pumpTimeData1, pumpTimeData2]);
    const pumpChanges: PumpStatusChanges[] =
      getPumpStatusChangeFlags(pumpTimeData);

    expect(pumpChanges[0].plantName).toEqual('plant_1');
    expect(pumpChanges[0].pumpName).toEqual('pump1');
    expect(pumpChanges[0].changeFlags).toEqual(expectResult1);
    expect(pumpChanges[1].changeFlags).toEqual(expectResult2);
  });
});
