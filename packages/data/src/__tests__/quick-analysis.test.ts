/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getAllSelectedByMergeRoadName,
  getFlowVelocity,
  mergeSameRoadNameAndDiameter,
} from '../quick-analysis/quick-analysis-data';

describe('quick-analysis', () => {
  it('mergeSameRoadNameAndDiameter', () => {
    const arr = [
      { ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
      { ROAD_NAME: 'A', DIAMETER: 100, LENGTH: 20, WIDTH: 10 },
    ] as any;
    expect(mergeSameRoadNameAndDiameter(arr)).toEqual([
      { ROAD_NAME: 'A', DIAMETER: 10, LENGTH: `5~50`, WIDTH: `1~20` },
      { ROAD_NAME: 'A', DIAMETER: 100, LENGTH: 20, WIDTH: 10 },
    ]);
  });

  it('getAllSelectedRowData', () => {
    const arr = [
      { oname: '1', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { oname: '2', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { oname: '3', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
      { oname: '4', ROAD_NAME: 'A', DIAMETER: 100, LENGTH: 20, WIDTH: 10 },
    ] as any;
    expect(getAllSelectedByMergeRoadName(['1', '4'], arr)).toEqual([
      { oname: '1', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { oname: '2', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { oname: '3', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
      { oname: '4', ROAD_NAME: 'A', DIAMETER: 100, LENGTH: 20, WIDTH: 10 },
    ]);
    expect(getAllSelectedByMergeRoadName(['1'], arr)).toEqual([
      { oname: '1', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { oname: '2', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { oname: '3', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
    ]);
    expect(getAllSelectedByMergeRoadName(['2'], arr)).toEqual([
      { oname: '1', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { oname: '2', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { oname: '3', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
    ]);
    expect(getAllSelectedByMergeRoadName(['3'], arr)).toEqual([
      { oname: '1', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 50, WIDTH: 20 },
      { oname: '2', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 5, WIDTH: 1 },
      { oname: '3', ROAD_NAME: 'A', DIAMETER: 10, LENGTH: 20, WIDTH: 10 },
    ]);
    expect(getAllSelectedByMergeRoadName(['4'], arr)).toEqual([
      { oname: '4', ROAD_NAME: 'A', DIAMETER: 100, LENGTH: 20, WIDTH: 10 },
    ]);
  });

  it('getFlowVelocity', () => {
    const flow = 1000; // 1,000 cubic meters per hour
    const diameter = 2000; // 2 meters
    expect(getFlowVelocity(flow, diameter)).toBe(0.1); // Adjust the precision as needed
  });
});
