/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { emailRegex, passwordRegex, phoneNumberRegex } from '../regex';

describe('phoneNumberRegex', () => {
  it('should validate correct phone numbers', () => {
    expect(phoneNumberRegex.test('13800138000')).toBeTruthy();
    expect(phoneNumberRegex.test('19912345678')).toBeTruthy();
  });

  it('should invalidate incorrect phone numbers', () => {
    expect(phoneNumberRegex.test('23800138000')).toBeFalsy(); // 不以1开头
    expect(phoneNumberRegex.test('1380013800')).toBeFalsy(); // 少于11位
    expect(phoneNumberRegex.test('138001380000')).toBeFalsy(); // 超过11位
    expect(phoneNumberRegex.test('1380013800a')).toBeFalsy(); // 包含字母
  });
});

describe('emailRegex', () => {
  it('should validate correct email addresses', () => {
    expect(emailRegex.test('<EMAIL>')).toBeTruthy();
    expect(emailRegex.test('<EMAIL>')).toBeTruthy();
  });

  it('should invalidate incorrect email addresses', () => {
    expect(emailRegex.test('example.com')).toBeFalsy(); // 缺少@
    expect(emailRegex.test('example@')).toBeFalsy(); // 缺少域名
    expect(emailRegex.test('example.name+tag@')).toBeFalsy(); // 缺少域名
    expect(emailRegex.test('example.name+tag@example')).toBeFalsy(); // 缺少顶级域
  });
});

describe('Password Regular Expression', () => {
  it('should match valid passwords', () => {
    const validPasswords = ['Abc123!$', 'P@ssw0rd', 'S3cure!'];

    validPasswords.forEach((password) => {
      expect(passwordRegex.test(password)).toBe(true);
    });
  });

  it('should not match invalid passwords', () => {
    const invalidPasswords = ['abc123', 'Password123', '123456'];

    invalidPasswords.forEach((password) => {
      expect(passwordRegex.test(password)).toBe(false);
    });
  });
});
