/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import mockDatabase from '../mock/mock-database';
import { findSameUnitFormatByKey } from '../scada-analysis-data';

describe('findSameUnitFormatByKey', () => {
  const db = mockDatabase();
  const sourceKeys = [
    'DEV_FLOW@C0220010007156@SDVAL_FLOW_W@C022001000715601@SDVAL',
    'DEV_PF@STA42@SDVAL_FLOW_W@D_GW_2356@SDVAL',
  ];

  it("should return false if don't find same unit format", () => {
    const targetKey =
      'DEV_POND@C0220010002003@SDVAL_PONDING@C0220010002003_NL@SDVAL';
    const flag = findSameUnitFormatByKey(targetKey, sourceKeys, db);
    expect(flag).toBe(false);
  });

  it('should return false if unit symbol is undefined', () => {
    const targetKey = 'DEV_POND@C0220010002003@SDVAL_POND@C0220@SDVAL';
    const flag = findSameUnitFormatByKey(targetKey, sourceKeys, db);
    expect(flag).toBe(false);
  });
});
