/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { mockTestScadaDeviceData } from '../mock/mock-scada-tree-data';
import {
  makeTreeDeviceId,
  makeTreeIndicatorId,
  ScadaDeviceCollection,
  splitTreeIndicatorId,
} from '../scada-tree-data';

describe('scadaDeviceCollection', () => {
  const collection = new ScadaDeviceCollection(mockTestScadaDeviceData);
  describe('getDeviceIdsById', () => {
    it('should return an empty array when groupId or parentId does not exist', () => {
      const deviceIds = collection.getDeviceIdsById(
        'nonexistentGroup',
        'nonexistentParent',
      );
      expect(deviceIds).toEqual([]);
    });

    it('should return the correct device IDs when groupId and parentId exist', () => {
      const deviceIds = collection.getDeviceIdsById(
        'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      );
      expect(deviceIds).toEqual([
        'DEV_PF@STA1691',
        'DEV_PF@D_BMBYQLK_500',
        'DEV_PF@STA1945',
        'DEV_PF@D_BYQJT_400',
      ]);
    });
  });
});

describe('makeTreeDeviceId', () => {
  it('should return the correct tree device ID', () => {
    const groupId = '001';
    const parentId = '002';
    const deviceId = 'DEV_FLOW_W@STA1376';

    const treeDeviceId = makeTreeDeviceId(groupId, parentId, deviceId);

    expect(treeDeviceId).toBe('001&002&DEV_FLOW_W@STA1376');
  });
});

describe('makeTreeIndicatorId', () => {
  it('should return the correct tree indicator ID', () => {
    const treeDeviceId = '001&002&DEV_FLOW_W@STA1376';
    const indicatorId = 'SDVAL_FULL_FLOW@D_GW_2632';

    const treeIndicatorId = makeTreeIndicatorId(treeDeviceId, indicatorId);

    expect(treeIndicatorId).toBe(
      '001&002&DEV_FLOW_W@STA1376&SDVAL_FULL_FLOW@D_GW_2632',
    );
  });
});

describe('splitTreeIndicatorId', () => {
  it('should return the split components of tree indicator ID', () => {
    const treeIndicatorId =
      '001&002&DEV_FLOW_W@STA1376&SDVAL_FULL_FLOW@D_GW_2632&SDVAL';

    const splitResult = splitTreeIndicatorId(treeIndicatorId);

    expect(splitResult).toEqual([
      '001',
      '002',
      'DEV_FLOW_W@STA1376',
      'SDVAL_FULL_FLOW@D_GW_2632',
      'SDVAL',
    ]);
  });

  it('should return undefined for invalid input', () => {
    const invalidInput = 123; // Not a string

    const splitResult = splitTreeIndicatorId(invalidInput as unknown as string);

    expect(splitResult).toBeUndefined();
  });

  it('should return undefined when the number of components is not 4', () => {
    const invalidInput = '001&002&DEV_FLOW_W@STA1376'; // Missing indicator ID

    const splitResult = splitTreeIndicatorId(invalidInput);

    expect(splitResult).toBeUndefined();
  });
});
