/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  CompleteScadaWarnConfig,
  convertToCustomScadaWarnName,
} from '../scada-warn-config';

describe('convertToCustomScadaWarnName', () => {
  it('should correctly extract custom warning names from the config array', () => {
    const mockData = [
      {
        ONAME: 'Test1',
        OTYPE: 'Type1',
        PNAME: 'PName1',
        PTYPE: 'PType1',
        TITLE: 'Title1',
        KET_WARN: true,
        WARN_DYNAMIC_BOUNDS: {
          title: '越动态上下限报警',
          type: 'SCADA_OUT_DYNAMIC_BOUND_ABNORMAL',
          value: {
            state: 0,
            align_minutes: {
              title: '数据对齐步长(分钟)',
              value: 5,
            },
          },
        },
        WARN_DATA_SAME: {
          title: '数据不变报警',
          type: 'SCADA_DATA_SAME_ABNORMAL',
          value: {
            state: 0,
          },
        },
      },
    ] as unknown as CompleteScadaWarnConfig[];

    const expectedResult = [
      {
        title: '越动态上下限报警',
        type: 'SCADA_OUT_DYNAMIC_BOUND_ABNORMAL',
        key: 'WARN_DYNAMIC_BOUNDS',
      },
      {
        title: '数据不变报警',
        type: 'SCADA_DATA_SAME_ABNORMAL',
        key: 'WARN_DATA_SAME',
      },
    ];

    expect(convertToCustomScadaWarnName(mockData)).toEqual(expectedResult);
  });

  it('should return an empty array when there are no warning configurations', () => {
    const mockData = [
      {
        ONAME: 'Test1',
        OTYPE: 'Type1',
        PNAME: 'PName1',
        PTYPE: 'PType1',
        TITLE: 'Title1',
        KET_WARN: true,
      },
    ] as unknown as CompleteScadaWarnConfig[];

    expect(convertToCustomScadaWarnName(mockData)).toEqual([]);
  });

  // 可以根据需要添加更多测试用例
});
