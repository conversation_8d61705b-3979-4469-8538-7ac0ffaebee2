/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getSimpleThemeAndLayerList,
  getThemeSectionTypes,
  Scene,
  ThemeSection,
} from '../scene';

describe('getSimpleThemeAndLayerList', () => {
  const simpleThemeSections: ThemeSection[] = [
    {
      type: 'type1',
      title: 'Title 1',
      layerStates: [
        { name: 'Layer 1', title: 'Layer Title 1', visible: true },
        { name: 'Layer 2', title: 'Layer Title 2', visible: false },
      ],
      themeItems: [],
      currentThemeItem: undefined,
    },
  ];

  const themeSections: ThemeSection[] = [
    {
      type: 'type1',
      title: 'Title 1',
      layerStates: [
        { name: 'Layer 1', title: 'Layer Title 1', visible: true },
        { name: 'Layer 2', title: 'Layer Title 2', visible: true },
      ],
      themeItems: [],
      currentThemeItem: { name: 'Theme 1', title: 'Theme Title 1' },
    },
    {
      type: 'type2',
      title: 'Title 2',
      layerStates: [
        { name: 'Layer 3', title: 'Layer Title 3', visible: true },
        { name: 'Layer 4', title: 'Layer Title 4', visible: true },
      ],
      themeItems: [],
      currentThemeItem: { name: 'Theme 2', title: 'Theme Title 2' },
    },
  ];

  it('should return the filtered and updated theme sections', () => {
    const result = getSimpleThemeAndLayerList(
      simpleThemeSections,
      themeSections,
    );

    expect(result).toEqual([
      {
        type: 'type1',
        title: 'Title 1',
        layerStates: [
          { name: 'Layer 1', title: 'Layer Title 1', visible: true },
          { name: 'Layer 2', title: 'Layer Title 2', visible: true },
        ],
        themeItems: [],
        currentThemeItem: { name: 'Theme 1', title: 'Theme Title 1' },
      },
    ]);
  });
});

describe('getThemeSectionTypes', () => {
  const sceneWithThemeSections: Scene = {
    id: 'RUN',
    title: 'Scene 1',
    dashboard: 'Dashboard 1',
    default: true,
    themeSections: [
      {
        type: 'type1',
        title: 'Title 1',
        layerStates: [],
        themeItems: [],
        currentThemeItem: undefined,
      },
      {
        type: 'type2',
        title: 'Title 2',
        layerStates: [],
        themeItems: [],
        currentThemeItem: undefined,
      },
      {
        type: 'type3',
        title: 'Title 3',
        layerStates: [],
        themeItems: [],
        currentThemeItem: undefined,
      },
    ],
    simpleThemeSections: [],
  };

  const sceneWithoutThemeSections: Scene = {
    id: 'SCADA',
    title: 'Scene 2',
    dashboard: 'Dashboard 2',
    default: false,
    themeSections: [],
    simpleThemeSections: [],
  };

  const sceneUndefined: Scene | undefined = undefined;

  it('should return an array of theme section types when scene has theme sections', () => {
    const result = getThemeSectionTypes(sceneWithThemeSections);
    expect(result).toEqual(['type1', 'type2', 'type3']);
  });

  it('should return an empty array when scene has no theme sections', () => {
    const result = getThemeSectionTypes(sceneWithoutThemeSections);
    expect(result).toEqual([]);
  });

  it('should return an empty array when scene is undefined', () => {
    const result = getThemeSectionTypes(sceneUndefined);
    expect(result).toEqual([]);
  });
});
