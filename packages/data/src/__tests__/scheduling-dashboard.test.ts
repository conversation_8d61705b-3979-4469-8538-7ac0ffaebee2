/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import {
  getPredictionTimeRange,
  getRealTimeRange,
  getTimeRange,
} from '../mini-dashboard/scheduling-dashboard-data';

describe('getTimeRange', () => {
  it('should return the correct time range when the hour is less than 12', () => {
    const dateTime = '2023-09-04 10:00:00';
    const expectedStart = '2023-09-03 12:00:00';
    const expectedEnd = '2023-09-05 00:00:00';
    const [start, end] = getTimeRange(dateTime);
    expect(start).toBe(expectedStart);
    expect(end).toBe(expectedEnd);
  });

  it('should return the correct time range when the hour is 12 or greater', () => {
    const dateTime = '2023-09-04 13:00:00';
    const expectedStart = '2023-09-04 00:00:00';
    const expectedEnd = '2023-09-05 12:00:00';
    const [start, end] = getTimeRange(dateTime);
    expect(start).toBe(expectedStart);
    expect(end).toBe(expectedEnd);
  });
});

describe('getRealTimeRange', () => {
  it('should return the current time range when currentTime is between start and end', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-02 09:00:00';
    const expectedStart = '2023-01-01 12:00:00';

    // 使用字符串形式的currentTime
    const result = getRealTimeRange(dateTime, currentTime);
    expect(result).toEqual([expectedStart, currentTime]);

    // 使用Dayjs对象形式的currentTime
    const dayjsResult = getRealTimeRange(dateTime, dayjs(currentTime));
    expect(dayjsResult).toEqual([expectedStart, currentTime]);
  });

  it('should return the full time range when currentTime is after end', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-03 09:00:00';
    const expectedStart = '2023-01-01 12:00:00';
    const expectedEnd = '2023-01-03 00:00:00';

    const result = getRealTimeRange(dateTime, currentTime);
    expect(result).toEqual([expectedStart, expectedEnd]);
  });

  it('should return null when currentTime is before start', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-01 09:00:00';

    const result = getRealTimeRange(dateTime, currentTime);
    expect(result).toBeNull();
  });
});

describe('getPredictionTimeRange', () => {
  it('returns the correct time range when currentTime is between start and end', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-02 09:00:00';
    const expectedEnd = '2023-01-03 00:00:00';

    const result = getPredictionTimeRange(dateTime, currentTime);

    expect(result).toEqual([currentTime, expectedEnd]);
  });

  it('returns null when currentTime is after the end of the range', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-03 09:00:00';

    const result = getPredictionTimeRange(dateTime, currentTime);

    expect(result).toBe(null);
  });

  it('returns the start and end of the range when currentTime is before the start', () => {
    const dateTime = '2023-01-02 10:00:00';
    const currentTime = '2023-01-01 09:00:00';
    const expectedStart = '2023-01-01 12:00:00';
    const expectedEnd = '2023-01-03 00:00:00';

    const result = getPredictionTimeRange(dateTime, currentTime);

    expect(result).toEqual([expectedStart, expectedEnd]);
  });
});
