/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { isAllStartTimeWithinOneHour } from '../smart-scheduling-data';

describe('isAllStartTimeWithinOneHour', () => {
  it('should return true for empty array', () => {
    expect(isAllStartTimeWithinOneHour([])).toBe(true);
  });

  it('should return true if all startTime are within one hour', () => {
    const now = dayjs();
    const data = [
      { startTime: now.add(10, 'minute').toISOString() },
      { startTime: now.add(59, 'minute').toISOString() },
    ];
    expect(isAllStartTimeWithinOneHour(data)).toBe(true);
  });

  it('should return false if any startTime is more than one hour later', () => {
    const now = dayjs();
    const data = [
      { startTime: now.add(10, 'minute').toISOString() },
      { startTime: now.add(61, 'minute').toISOString() },
    ];
    expect(isAllStartTimeWithinOneHour(data)).toBe(false);
  });

  it('should return true if startTime is exactly one hour later (not included)', () => {
    const now = dayjs();
    const data = [
      { startTime: now.add(59, 'minute').toISOString() },
      { startTime: now.add(60, 'minute').subtract(1, 'second').toISOString() },
    ];
    expect(isAllStartTimeWithinOneHour(data)).toBe(true);
  });

  it('should return true if startTime is exactly one hour later (included)', () => {
    const now = dayjs();
    const data = [{ startTime: now.add(60, 'minute').toISOString() }];
    expect(isAllStartTimeWithinOneHour(data)).toBe(true);
  });

  it('should return false if all startTime are more than one hour later', () => {
    const now = dayjs();
    const data = [
      { startTime: now.add(61, 'minute').toISOString() },
      { startTime: now.add(120, 'minute').toISOString() },
    ];
    expect(isAllStartTimeWithinOneHour(data)).toBe(false);
  });
});
