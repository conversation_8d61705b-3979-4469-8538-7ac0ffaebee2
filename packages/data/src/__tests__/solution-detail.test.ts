/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  defaultSolutionObject,
  demandValueChange1,
  demandValueChange2,
  demandValueChange3,
  getMockDefaultSolutionDetail,
  getMockSampleSolutionDetail,
  mockFlowTimeData,
  sampleSolutionObject,
} from '../mock/mock-create-solution';
import {
  flowToTotalFlow,
  getBaseSolutionInfoData,
  getChangedTimeData,
  getModifyTypeText,
  getSolutionDetail,
  getSumFlow,
  getSumFlowText,
  getUpdatedPumpStationChanges,
  PumpStationValueChangeSetting,
  SolutionModifyType,
  timeToMinutes,
  toRequestJson,
} from '../solution-detail';

describe('Solution Detail - Default', () => {
  const defaultSolution = getMockDefaultSolutionDetail();

  it('default solution', () => {
    expect(defaultSolution.name).toBe(undefined);
    expect(toRequestJson(defaultSolution)).toStrictEqual(defaultSolutionObject);
    expect(defaultSolution.flowInfo.data.length).toBe(3);
    expect(defaultSolution.flowInfo.data[0].time).toEqual(
      '2023-04-13 00:00:00',
    );
    expect(defaultSolution.flowInfo.data[0].value).toEqual(33268.747973);
  });
});

describe('Solution Detail - Sample', () => {
  const sampleSolution = getMockSampleSolutionDetail();

  it('default solution', () => {
    expect(sampleSolution.name).toEqual(sampleSolutionObject.name);
    expect(sampleSolution.modelId).toEqual(sampleSolutionObject.model_id);
    expect(sampleSolution.note).toEqual(sampleSolutionObject.note);
    expect(sampleSolution.simulationStartTime).toEqual(
      sampleSolutionObject.simulation_start_time,
    );
    expect(sampleSolution.simulationEndTime).toEqual(
      sampleSolutionObject.simulation_end_time,
    );
    expect(sampleSolution.calculateStartTime).toEqual(
      sampleSolutionObject.calculate_start_time,
    );
    expect(sampleSolution.calculateEndTime).toEqual(
      sampleSolutionObject.calculate_end_time,
    );

    expect(toRequestJson(sampleSolution).base_day).toStrictEqual(
      sampleSolutionObject.base_day,
    );
    expect(toRequestJson(sampleSolution).demand).toStrictEqual(
      sampleSolutionObject.demand,
    );
    expect(toRequestJson(sampleSolution).plants).toStrictEqual(
      sampleSolutionObject.plants,
    );
    expect(toRequestJson(sampleSolution).pump_stations).toStrictEqual(
      sampleSolutionObject.pump_stations,
    );
    expect(toRequestJson(sampleSolution).link_state).toStrictEqual(
      sampleSolutionObject.link_state,
    );
    expect(toRequestJson(sampleSolution).options).toStrictEqual(
      sampleSolutionObject.options,
    );
    expect(toRequestJson(sampleSolution).times).toStrictEqual(
      sampleSolutionObject.times,
    );
  });
});

describe('Solution initialize - Sample', () => {
  const sampleSolution = getSolutionDetail(sampleSolutionObject);

  expect(sampleSolution.name).toBe(sampleSolutionObject.name);
  expect(sampleSolution.modelId).toBe(sampleSolutionObject.model_id);
  expect(sampleSolution.note).toBe(sampleSolutionObject.note);
  expect(sampleSolution.simulationStartTime).toBe(
    sampleSolutionObject.simulation_start_time,
  );
  expect(sampleSolution.simulationEndTime).toBe(
    sampleSolutionObject.simulation_end_time,
  );
  expect(toRequestJson(sampleSolution).base_day).toEqual(
    sampleSolutionObject.base_day,
  );
  expect(toRequestJson(sampleSolution).demand).toEqual(
    sampleSolutionObject.demand,
  );
  expect(toRequestJson(sampleSolution).plants).toEqual(
    sampleSolutionObject.plants,
  );

  expect(sampleSolution.options.DemandModel).toBe(
    sampleSolutionObject.options.DEMAND_MODEL,
  );
  expect(sampleSolution.options.HydraulicTimestep).toBe(
    sampleSolutionObject.times['Hydraulic Timestep'],
  );
  expect(sampleSolution.options.QualityTimestep).toBe(
    sampleSolutionObject.times['Quality Timestep'],
  );
  expect(toRequestJson(sampleSolution).options).toEqual(
    sampleSolutionObject.options,
  );
});

describe('Flow - change', () => {
  const originalTimeData = mockFlowTimeData;
  it('ValueChangeSetting - add, minus, assign', () => {
    const newTimeData = getChangedTimeData(originalTimeData, [
      demandValueChange1,
      demandValueChange2,
      demandValueChange3,
    ]);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 08:00:00')?.value,
    ).toBe(60342.44315205291);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 10:00:00')?.value,
    ).toBe(47086.48483565262);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 13:55:00')?.value,
    ).toBe(42600.02825372836);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 14:00:00')?.value,
    ).toBe(32371.30426991178);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 16:00:00')?.value,
    ).toBe(40010.7040932236);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 18:00:00')?.value,
    ).toBe(30000);
    expect(
      newTimeData.find((item) => item.time === '2023-03-19 21:00:00')?.value,
    ).toBe(52017.28789230684);
  });
});

describe('pump station - changes', () => {
  const changes: PumpStationValueChangeSetting[] = [
    {
      id: 'XD-BZ',
      pattern: 'XDBZ-OP',
      method: 'add',
      value: 2,
      startTime: '7:00',
      endTime: '8:00',
    },
    {
      id: 'XD-BZ',
      pattern: 'XDBZ-OP',
      method: 'minus',
      value: 1,
      startTime: '18:00',
      endTime: '19:00',
    },
    {
      id: 'XD-BZ',
      pattern: 'XDBZ-IF',
      method: 'add',
      value: 5000,
      startTime: '7:00',
      endTime: '9:00',
    },
    {
      id: 'HML-BZ',
      pattern: 'HMLBZ-OP',
      method: 'add',
      value: 1,
      startTime: '0:00',
      endTime: '24:00',
    },
  ];

  it('getUpdatedPumpStationChanges', () => {
    const pumpStationChanges = getUpdatedPumpStationChanges(changes);
    expect(
      pumpStationChanges.get('XD-BZ')?.patterns.get('XDBZ-OP')?.length,
    ).toEqual(2);
    expect(
      pumpStationChanges.get('HML-BZ')?.patterns.get('HMLBZ-OP')?.length,
    ).toEqual(1);
    expect(pumpStationChanges.get('"YX-BZ"')).toBeUndefined();
  });
});

describe('functions', () => {
  it('getSumFlow', () => {
    const sum = getSumFlow(mockFlowTimeData);
    expect(sum).toBe(953125.087407443);
    const sumText = getSumFlowText(mockFlowTimeData);
    expect(sumText).toBe('95.31万吨');

    const sum2 = getSumFlow([{ time: '2023-04-09 11:00:00', value: 10000 }]);
    expect(sum2).toBe(240000);
    const sumText2 = getSumFlowText([
      { time: '2023-04-09 11:00:00', value: 10000 },
    ]);
    expect(sumText2).toBe('24.00万吨');
  });

  it('timeToMinutes', () => {
    expect(timeToMinutes('12:00')).toBe(720);
    expect(timeToMinutes('')).toBe(0);
  });

  it('getBaseSolutionInfoData', () => {
    const infoData = {
      solution_title: '2023-04-03方案-test',
      solution_status: 'IDLE',
      start_time: '2023-04-03 00:00:00',
      end_time: '2023-04-03 23:00:00',
      solution_error: null,
      solution_note: '',
      solution_share: '1',
      solution_status_msg: '完成计算',
    };
    const baseInfo = getBaseSolutionInfoData(infoData);
    expect(baseInfo.name).toEqual('2023-04-03方案-test');
    expect(baseInfo.status).toEqual('IDLE');
    expect(baseInfo.startTime).toEqual('2023-04-03 00:00:00');
    expect(baseInfo.endTime).toEqual('2023-04-03 23:00:00');
    expect(baseInfo.note).toEqual('');
    expect(baseInfo.isShared).toEqual(true);
    expect(baseInfo.statusMessage).toEqual('完成计算');
    expect(baseInfo.errorMessage).toBeNull();
  });

  it('getModifyTypeText', () => {
    expect(getModifyTypeText(SolutionModifyType.SPLIT)).toBe('打断');
    expect(getModifyTypeText(SolutionModifyType.CREATE)).toBe('增加');
    expect(getModifyTypeText(SolutionModifyType.DELETE)).toBe('删除');
    expect(getModifyTypeText(SolutionModifyType.VPROP)).toBe('修改');
    expect(getModifyTypeText('REMOVE' as any)).toBe('REMOVE');
  });
});

describe('flowToTotalFlow', () => {
  it('should calculate total flow correctly for a positive value', () => {
    const startTime = '08:00';
    const endTime = '12:30';
    const value = 500;
    const result = flowToTotalFlow(startTime, endTime, value);
    expect(result).toEqual(0.225);
  });

  it('should handle zero value', () => {
    const startTime = '08:00';
    const endTime = '12:30';
    const value = 0;
    const result = flowToTotalFlow(startTime, endTime, value);
    expect(result).toEqual(0);
  });

  it('should handle negative value', () => {
    const startTime = '08:00';
    const endTime = '12:30';
    const value = -500;

    const result = flowToTotalFlow(startTime, endTime, value);

    expect(result).toEqual(-0.225);
  });

  it('should handle edge case when startTime and endTime are the same', () => {
    const startTime = '08:00';
    const endTime = '08:00';
    const value = 500;

    const result = flowToTotalFlow(startTime, endTime, value);

    expect(result).toEqual(0);
  });

  it('should handle decimal places correctly', () => {
    const startTime = '08:00';
    const endTime = '12:30';
    const value = 500.0;

    const result = flowToTotalFlow(startTime, endTime, value);
    expect(result).toEqual(0.225);
  });
});
