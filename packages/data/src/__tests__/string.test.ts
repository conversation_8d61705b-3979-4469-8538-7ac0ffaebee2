/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import {
  camelToSnakeCase,
  convertObjectKeysToSnakeCase,
  convertSnakeCaseKeysToCamelCase,
  formatDayjsInObject,
  isNotNullOrEmpty,
  removeWhitespaceFromParams,
  snakeToCamelCase,
} from '../string';

describe('camelToSnakeCase', () => {
  it('should convert camelCase to snake_case', () => {
    expect(camelToSnakeCase('camelCase')).toBe('camel_case');
  });

  it('should convert camelCase to SNAKE_CASE when toUpperCase is true', () => {
    expect(camelToSnakeCase('camelCase', true)).toBe('CAMEL_CASE');
  });

  it('should not change lowercase strings', () => {
    expect(camelToSnakeCase('lowercase')).toBe('lowercase');
  });
});

describe('snakeToCamelCase', () => {
  it('should convert snake_case to camelCase', () => {
    expect(snakeToCamelCase('snake_case')).toBe('snakeCase');
  });

  it('should not change camelCase strings', () => {
    expect(snakeToCamelCase('alreadyCamelCase')).toBe('alreadyCamelCase');
  });

  it('should handle strings with multiple underscores', () => {
    expect(snakeToCamelCase('snake_case_with_multiple_words')).toBe(
      'snakeCaseWithMultipleWords',
    );
  });
});

describe('convertObjectKeysToSnakeCase', () => {
  it('should convert object keys from camelCase to snake_case', () => {
    const obj = {
      camelCase: 'value',
      anotherCamelCase: 'another value',
    };
    expect(convertObjectKeysToSnakeCase(obj)).toEqual({
      camel_case: 'value',
      another_camel_case: 'another value',
    });
  });

  it('should convert object keys from camelCase to SNAKE_CASE when toUpperCase is true', () => {
    const obj = {
      camelCase: 'value',
      anotherCamelCase: 'another value',
    };
    expect(convertObjectKeysToSnakeCase(obj, true)).toEqual({
      CAMEL_CASE: 'value',
      ANOTHER_CAMEL_CASE: 'another value',
    });
  });

  it('should not change object keys if they are already in snake_case', () => {
    const obj = {
      snake_case: 'value',
      another_snake_case: 'another value',
    };
    expect(convertObjectKeysToSnakeCase(obj)).toEqual(obj);
  });

  it('should return an empty object when input is not an object', () => {
    expect(convertObjectKeysToSnakeCase()).toEqual({});
  });
});

describe('convertSnakeCaseKeysToCamelCase', () => {
  it('should convert object keys from snake_case to camelCase', () => {
    const obj = {
      snake_case: 'value',
      another_snake_case: 'another value',
    };
    expect(convertSnakeCaseKeysToCamelCase(obj)).toEqual({
      snakeCase: 'value',
      anotherSnakeCase: 'another value',
    });
  });

  it('should not change object keys if they are already in camelCase', () => {
    const obj = {
      alreadyCamelCase: 'value',
      anotherAlreadyCamelCase: 'another value',
    };
    expect(convertSnakeCaseKeysToCamelCase(obj)).toEqual(obj);
  });

  it('should handle nested objects and arrays of objects', () => {
    const obj = {
      snake_case: 'value',
      another_snake_case: 'another value',
      nested_object: {
        nested_snake_case: 'nested value',
      },
      array_of_objects: [
        {
          array_snake_case: 'array value',
        },
      ],
    };
    expect(convertSnakeCaseKeysToCamelCase(obj)).toEqual({
      snakeCase: 'value',
      anotherSnakeCase: 'another value',
      nestedObject: {
        nestedSnakeCase: 'nested value',
      },
      arrayOfObjects: [
        {
          arraySnakeCase: 'array value',
        },
      ],
    });
  });

  it('should return an empty object when input is not an object', () => {
    expect(convertSnakeCaseKeysToCamelCase()).toEqual({});
  });
});

describe('formatDayjsInObject', () => {
  test('should not modify non-date properties', () => {
    const obj = { name: 'John Doe', age: 30 };
    expect(formatDayjsInObject(obj)).toEqual(obj);
  });

  test('should format all date properties', () => {
    const date = dayjs('2023-01-01');
    const obj = { created: date, updated: date };
    const expected = {
      created: date.format('YYYY-MM-DD HH:mm:ss'),
      updated: date.format('YYYY-MM-DD HH:mm:ss'),
    };
    expect(formatDayjsInObject(obj)).toEqual(expected);
  });

  test('should normally format dates when key does not match special keys', () => {
    const date = dayjs('2023-01-01');
    const obj = { creationDate: date };
    const expected = { creationDate: date.format('YYYY-MM-DD HH:mm:ss') };
    expect(formatDayjsInObject(obj)).toEqual(expected);
  });

  test('should normally format dates for similar but non-matching keys', () => {
    const date = dayjs('2023-01-01');
    const obj = { start_Date: date, end_Date: date };
    const expected = {
      start_Date: date.format('YYYY-MM-DD HH:mm:ss'),
      end_Date: date.format('YYYY-MM-DD HH:mm:ss'),
    };
    expect(formatDayjsInObject(obj)).toEqual(expected);
  });

  test('should not apply special formatting when specialTimeFormat is not provided', () => {
    const startDate = dayjs('2023-01-01 12:30');
    const endDate = dayjs('2023-01-01 18:45');
    const obj = { startTime: startDate, endTime: endDate };
    const expected = {
      startTime: startDate.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endDate.format('YYYY-MM-DD HH:mm:ss'),
    };
    expect(formatDayjsInObject(obj)).toEqual(expected);
  });

  test('should apply special formatting for matching keys', () => {
    const startDate = dayjs('2023-01-01 12:30');
    const endDate = dayjs('2023-01-01 18:45');
    const obj = { startTime: startDate, endTime: endDate };
    const expected = {
      startTime: '2023-01-01 00:00:00',
      endTime: '2023-01-01 23:59:59',
    };
    expect(formatDayjsInObject(obj, true)).toEqual(expected);
  });
});

describe('isNotNullOrEmpty function', () => {
  test('should return false for undefined', () => {
    expect(isNotNullOrEmpty(undefined)).toBe(false);
  });

  test('should return false for null', () => {
    expect(isNotNullOrEmpty(null)).toBe(false);
  });

  test('should return false for an empty string', () => {
    expect(isNotNullOrEmpty('')).toBe(false);
  });

  test('should return true for a non-empty string', () => {
    expect(isNotNullOrEmpty('hello')).toBe(true);
  });

  test('should return true for a number including 0', () => {
    expect(isNotNullOrEmpty(0)).toBe(true);
    expect(isNotNullOrEmpty(42)).toBe(true);
  });

  test('should return true for boolean values', () => {
    expect(isNotNullOrEmpty(true)).toBe(true);
    expect(isNotNullOrEmpty(false)).toBe(true);
  });

  test('should return true for objects and arrays', () => {
    expect(isNotNullOrEmpty({})).toBe(true);
    expect(isNotNullOrEmpty([])).toBe(true);
  });
});

describe('removeWhitespaceFromParams', () => {
  it('should trim leading and trailing whitespace from a string', () => {
    const result = removeWhitespaceFromParams('  hello world  ');
    expect(result).toBe('hello world');
  });

  it('should recursively trim whitespace from strings in an array', () => {
    const result = removeWhitespaceFromParams(['  hello ', ' world  ']);
    expect(result).toEqual(['hello', 'world']);
  });

  it('should recursively trim whitespace from strings in an object', () => {
    const params = {
      name: '  Alice  ',
      greeting: '  hello world  ',
    };
    const result = removeWhitespaceFromParams(params);
    expect(result).toEqual({
      name: 'Alice',
      greeting: 'hello world',
    });
  });

  it('should handle nested objects and arrays', () => {
    const params = {
      user: {
        name: '  Alice  ',
        hobbies: ['  reading  ', '  cooking  '],
      },
      greeting: '  hello world  ',
    };
    const result = removeWhitespaceFromParams(params);
    expect(result).toEqual({
      user: {
        name: 'Alice',
        hobbies: ['reading', 'cooking'],
      },
      greeting: 'hello world',
    });
  });

  it('should skip trimming for dayjs objects', () => {
    const now = dayjs();
    const params = {
      date: now,
      greeting: '  hello world  ',
    };
    const result = removeWhitespaceFromParams(params);
    expect(result).toEqual({
      date: now, // should remain unchanged
      greeting: 'hello world',
    });
  });

  it('should return the original value if it is neither string, array, nor object', () => {
    const result = removeWhitespaceFromParams(42);
    expect(result).toBe(42);

    const resultBoolean = removeWhitespaceFromParams(true);
    expect(resultBoolean).toBe(true);
  });

  it('should return null or undefined as is', () => {
    const resultNull = removeWhitespaceFromParams(null);
    expect(resultNull).toBeNull();

    const resultUndefined = removeWhitespaceFromParams(undefined);
    expect(resultUndefined).toBeUndefined();
  });
});
