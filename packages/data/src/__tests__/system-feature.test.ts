/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MenuInfo } from '../menu-data';
import {
  FeatureCode,
  featureEnabled,
  generateSystemPage,
  PageCode,
  pageEnabled,
  registerSystemFeatures,
} from '../system-feature';

describe('system-feature', () => {
  it('initialize SystemFeatureManagement', () => {
    const menuConfig: MenuInfo[] = [
      {
        functionUrl: '/main/online',
        code: 'PAGE_ONLINE',
        functionKey: 'PAGE_ONLINE',
        homepage: true,
        icon: '',
        id: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
        name: '供水在线',
        parentId: '',
        showType: '',
        type: 'PAGE',
        typeName: '页面',
        url: '/main/online/Supply',
        dataMode: 'Supply',
        children: [
          {
            functionUrl: '',
            code: 'FEATURE_VALVE_ADD_RECORD',
            functionKey: 'FEATURE_VALVE_ADD_RECORD',
            homepage: false,
            icon: '',
            id: 'cbf5dc52d84e40a2865d4ef0c7ff7d29',
            name: '添加阀门记录',
            parentId: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
            showType: '',
            type: 'FUNC',
            typeName: '功能',
            url: '',
            dataMode: '',
          },
          {
            functionUrl: '',
            code: 'FEATURE_VALVE_ADD_SCADA_RELATION',
            functionKey: 'FEATURE_VALVE_ADD_SCADA_RELATION',
            homepage: false,
            icon: '',
            id: 'c68843d8dc4f4de5be90f42db6c73ae1',
            name: '添加智慧阀门',
            parentId: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
            showType: '',
            type: 'FUNC',
            typeName: '功能',
            url: '',
            dataMode: '',
          },
        ],
      },
      {
        functionUrl: '',
        code: 'f131c574-cd73-4f44-b792-3a88e24a88cf',
        functionKey: 'f131c574-cd73-4f44-b792-3a88e24a88cf',
        homepage: false,
        icon: '',
        id: 'd6873ef4-b2c3-4718-9271-a25dd9ae70ba',
        name: '系统',
        parentId: '',
        showType: '',
        type: 'MENU',
        typeName: '导航',
        url: '',
        dataMode: 'default',
        children: [
          {
            functionUrl: '/permission/kettleLog',
            code: 'PAGE_KETTLE_LOG',
            functionKey: 'PAGE_KETTLE_LOG',
            homepage: false,
            icon: '',
            id: '02c21a8dc79a44b3bb13229f25510096',
            name: 'kettle日志',
            parentId: 'd6873ef4-b2c3-4718-9271-a25dd9ae70ba',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/kettleLog',
            dataMode: '',
          },
          {
            functionUrl: '/permission/system',
            code: 'PAGE_SYSTEM_MONITORS',
            functionKey: 'PAGE_SYSTEM_MONITORS',
            homepage: false,
            icon: '',
            id: '5af7468182024da6a3dcd57d599d58d6',
            name: '系统监控',
            parentId: 'd6873ef4-b2c3-4718-9271-a25dd9ae70ba',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/system',
            dataMode: '',
          },
        ],
      },
      {
        functionUrl: '',
        code: 'd4250eaa-22aa-41dd-b5a8-627d11109380',
        functionKey: 'd4250eaa-22aa-41dd-b5a8-627d11109380',
        homepage: false,
        icon: '',
        id: '9975c41e-96db-48f5-b168-aab0d3f6a232',
        name: '用户',
        parentId: '',
        showType: '',
        type: 'MENU',
        typeName: '导航',
        url: '',
        dataMode: 'default',
        children: [
          {
            functionUrl: '/permission/user',
            code: 'PAGE_USER',
            functionKey: 'PAGE_USER',
            homepage: false,
            icon: '',
            id: '4998e3b3-9f1c-4c28-b3f2-16be73dbe7e1',
            name: '用户管理',
            parentId: '9975c41e-96db-48f5-b168-aab0d3f6a232',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/user',
            dataMode: '',
          },
          {
            functionUrl: '/permission/department',
            code: 'PAGE_DEPARTMENT',
            functionKey: 'PAGE_DEPARTMENT',
            homepage: false,
            icon: '',
            id: '6fd2e78a-1b22-4956-87e6-4e40f521dbdc',
            name: '部门管理',
            parentId: '9975c41e-96db-48f5-b168-aab0d3f6a232',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/department',
            dataMode: '',
          },
          {
            functionUrl: '/permission/systemLog',
            code: 'PAGE_SYSTEM_LOG',
            functionKey: 'PAGE_SYSTEM_LOG',
            homepage: false,
            icon: '',
            id: '59d779ab-3339-4d4e-b503-5a4c40a73d90',
            name: '系统日志',
            parentId: '9975c41e-96db-48f5-b168-aab0d3f6a232',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/systemLog',
            dataMode: '',
          },
        ],
      },
      {
        functionUrl: '',
        code: 'fbd9e2da-abff-4234-b8af-865634df6893',
        functionKey: 'fbd9e2da-abff-4234-b8af-865634df6893',
        homepage: false,
        icon: '',
        id: 'fbd9e2da-abff-4234-b8af-865634df6893',
        name: '慧水内部用',
        parentId: '',
        showType: '',
        type: 'MENU',
        typeName: '导航',
        url: '',
        dataMode: 'default',
        children: [
          {
            functionUrl: '/permission/menu',
            code: 'PAGE_MENU',
            functionKey: 'PAGE_MENU',
            homepage: false,
            icon: '',
            id: '1a8b84fe-2b2b-4e9b-9147-787de44d821a',
            name: '菜单',
            parentId: 'fbd9e2da-abff-4234-b8af-865634df6893',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/menu',
            dataMode: '',
          },
          {
            functionUrl: '/permission/role',
            code: 'PAGE_ROLE',
            functionKey: 'PAGE_ROLE',
            homepage: false,
            icon: '',
            id: 'ac4abcc8-53a8-4d2c-947f-75fe5c7412b8',
            name: '角色',
            parentId: 'fbd9e2da-abff-4234-b8af-865634df6893',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/role',
            dataMode: '',
          },
          {
            functionUrl: '/permission/icon',
            code: 'PAGE_ICON',
            functionKey: 'PAGE_ICON',
            homepage: false,
            icon: '',
            id: '790265c5-603f-4be2-ac81-d559351d7b9b',
            name: '图标',
            parentId: 'fbd9e2da-abff-4234-b8af-865634df6893',
            showType: '',
            type: 'PAGE',
            typeName: '页面',
            url: '/permission/icon',
            dataMode: '',
          },
        ],
      },
    ];
    registerSystemFeatures(menuConfig);
    expect(pageEnabled('PAGE_ONLINE' as PageCode)).toBe(true);
    expect(featureEnabled('/main/online', FeatureCode.VALVE_ADD_RECORD)).toBe(
      true,
    );
  });

  it('create system-page', () => {
    const childrenEmptyCase: MenuInfo = {
      functionUrl: '/main/online',
      code: 'PAGE_ONLINE',
      functionKey: 'PAGE_ONLINE',
      homepage: true,
      icon: '',
      id: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
      name: '供水在线',
      parentId: '',
      showType: '',
      type: 'PAGE',
      typeName: '页面',
      url: '/main/online/Supply',
      dataMode: 'Supply',
    };
    const systemPage = generateSystemPage(childrenEmptyCase);

    expect(systemPage.info.id).toBe('bc6bf46e-7aae-4331-b49b-1a675960f5ad');
    expect(systemPage.info.code).toBe('PAGE_ONLINE');
    expect(systemPage.hasFeatureInfo(FeatureCode.VALVE_ADD_RECORD)).toBe(false);
  });

  it('system-page have function', () => {
    const childrenNotEmptyCase: MenuInfo = {
      functionUrl: '/main/online',
      code: 'PAGE_ONLINE',
      functionKey: 'PAGE_ONLINE',
      homepage: true,
      icon: '',
      id: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
      name: '供水在线',
      parentId: '',
      showType: '',
      type: 'PAGE',
      typeName: '页面',
      url: '/main/online/Supply',
      dataMode: 'Supply',
      children: [
        {
          functionUrl: '',
          code: 'FEATURE_VALVE_ADD_RECORD',
          functionKey: 'FEATURE_VALVE_ADD_RECORD',
          homepage: false,
          icon: '',
          id: 'cbf5dc52d84e40a2865d4ef0c7ff7d29',
          name: '添加阀门记录',
          parentId: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
          showType: '',
          type: 'FUNC',
          typeName: '功能',
          url: '',
          dataMode: '',
        },
        {
          functionUrl: '',
          code: 'FEATURE_VALVE_ADD_SCADA_RELATION',
          functionKey: 'FEATURE_VALVE_ADD_SCADA_RELATION',
          homepage: false,
          icon: '',
          id: 'c68843d8dc4f4de5be90f42db6c73ae1',
          name: '添加智慧阀门',
          parentId: 'bc6bf46e-7aae-4331-b49b-1a675960f5ad',
          showType: '',
          type: 'FUNC',
          typeName: '功能',
          url: '',
          dataMode: '',
        },
      ],
    };

    const systemPage = generateSystemPage(childrenNotEmptyCase);
    expect(systemPage.hasFeatureInfo(FeatureCode.VALVE_ADD_RECORD)).toBe(true);
  });
});
