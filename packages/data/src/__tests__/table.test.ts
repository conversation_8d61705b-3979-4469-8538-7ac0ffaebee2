/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getConvertedSort } from '../table';

describe('getConvertedSort', () => {
  it('should convert "ascend" to "asc" and "descend" to "desc"', async () => {
    const sort: Record<string, 'ascend' | 'descend' | null> = {
      name: 'ascend',
      age: 'descend',
    };
    const result = await getConvertedSort(sort);
    expect(result).toEqual({
      name: 'asc',
      age: 'desc',
    });
  });

  it('should ignore fields with null values', async () => {
    const sort: Record<string, 'ascend' | 'descend' | null> = {
      name: 'ascend',
      age: null,
    };
    const result = await getConvertedSort(sort);
    expect(result).toEqual({
      name: 'asc',
    });
  });

  it('should return an empty object when all values are null', async () => {
    const sort: Record<string, 'ascend' | 'descend' | null> = {
      name: null,
      age: null,
    };
    const result = await getConvertedSort(sort);
    expect(result).toEqual({});
  });

  it('should handle an empty input object', async () => {
    const sort = {};
    const result = await getConvertedSort(sort);
    expect(result).toEqual({});
  });
});
