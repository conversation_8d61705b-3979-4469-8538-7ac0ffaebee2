/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { mockFlowTimeData } from '../mock/mock-create-solution';
import {
  changeDateOfTimeData,
  convertDatesToRange,
  TimeData,
} from '../time-data';

describe('time-data', () => {
  it('changeDateOfTimeData', () => {
    const timeData: TimeData[] = mockFlowTimeData;
    const newTimeData = changeDateOfTimeData(timeData, '2023-10-01');
    expect(newTimeData[0].time.startsWith('2023-10-01')).toBeTruthy();
  });
});

describe('convertDatesToRange', () => {
  it('should return the nearest three days if the input array is empty', () => {
    const [startDate, endDate] = convertDatesToRange([]);
    expect(startDate.format('YYYY-MM-DD')).toBe(
      dayjs().add(-2, 'day').format('YYYY-MM-DD'),
    );
    expect(endDate.format('YYYY-MM-DD')).toBe(dayjs().format('YYYY-MM-DD'));
  });

  it('should return a three day range if the date difference is less than or equal to 2 days', () => {
    const dates = ['2023-10-10 08:00:00', '2023-10-11 09:00:00'];
    const [startDate, endDate] = convertDatesToRange(dates);
    expect(startDate.format('YYYY-MM-DD')).toBe('2023-10-09');
    expect(endDate.format('YYYY-MM-DD')).toBe('2023-10-12');
  });

  it('should return the exact date range if the date difference is more than 2 days', () => {
    const dates = ['2023-10-10 08:00:00', '2023-10-14 09:00:00'];
    const [startDate, endDate] = convertDatesToRange(dates);
    expect(startDate.format('YYYY-MM-DD')).toBe('2023-10-10');
    expect(endDate.format('YYYY-MM-DD')).toBe('2023-10-14');
  });
});
