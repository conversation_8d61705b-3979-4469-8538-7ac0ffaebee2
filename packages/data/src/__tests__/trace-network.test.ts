/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  mockDownTraceNetwork,
  mockDownTraceNetwork2,
  mockEmptyShapePipe,
  mockNegativeFlowPipe,
  mockNoFlowPipe,
  mockNonLineShapePipe,
  mockNormalPipe,
  mockNoShapePipe,
  mockNullFlowPipe,
  mockUpTraceNetwork,
  mockUpTraceNetwork2,
  mockUpTraceNetwork3,
  mockUpTraceNetworkWithPump,
  mockUpTraceNetworkWithPump2,
} from '../mock/mock-trace-network';
import TraceNetwork, { Pipe } from '../trace/trace-network';

describe('pipes', () => {
  it('normal pipe', () => {
    const pipe: Pipe = new Pipe(mockNormalPipe);
    expect(pipe.diameter).toEqual(1000);
    expect(pipe.flow).toEqual(2566.737061);
    expect(pipe.startNodeId).toEqual('437127.631760494 2875529.47036416');
    expect(pipe.endNodeId).toEqual('437117.787148931 2875548.55714236');
  });

  it('negative flow pipe', () => {
    const pipe: Pipe = new Pipe(mockNegativeFlowPipe);
    expect(pipe.diameter).toEqual(1200);
    expect(pipe.flow).toEqual(-4369.154785);
    expect(pipe.startNodeId).toEqual('437777.814880215 2874650.18585754');
    expect(pipe.endNodeId).toEqual('437743.540438279 2874704.24192384');
  });

  it('null flow pipe', () => {
    const pipe: Pipe = new Pipe(mockNullFlowPipe);
    expect(pipe.isValid()).toBeFalsy();
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toBeUndefined();
    expect(pipe.endNodeId).toBeUndefined();
  });

  it('no flow pipe', () => {
    const pipe: Pipe = new Pipe(mockNoFlowPipe);
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toEqual('422324.070719478 2887364.67472469');
    expect(pipe.endNodeId).toEqual('422334.188686975 2887374.54704962');
  });

  it('zero flow pipe', () => {
    const pipe: Pipe = new Pipe(mockNoFlowPipe);
    expect(pipe.isValid()).toBeFalsy();
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toEqual('422324.070719478 2887364.67472469');
    expect(pipe.endNodeId).toEqual('422334.188686975 2887374.54704962');
  });

  it('no shape pipe', () => {
    const pipe: Pipe = new Pipe(mockNoShapePipe);
    expect(pipe.isValid()).toBeFalsy();
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toBeUndefined();
    expect(pipe.endNodeId).toBeUndefined();
  });

  it('empty shape pipe', () => {
    const pipe: Pipe = new Pipe(mockEmptyShapePipe);
    expect(pipe.isValid()).toBeFalsy();
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toBeUndefined();
    expect(pipe.endNodeId).toBeUndefined();
  });

  it('no shape pipe', () => {
    const pipe: Pipe = new Pipe(mockNonLineShapePipe);
    expect(pipe.isValid()).toBeFalsy();
    expect(pipe.diameter).toEqual(0);
    expect(pipe.flow).toEqual(0);
    expect(pipe.startNodeId).toBeUndefined();
    expect(pipe.endNodeId).toBeUndefined();
  });
});

describe('trace network', () => {
  it('up-trace-network', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockUpTraceNetwork);
    expect(mockUpTraceNetwork.length).toEqual(110);
    expect(network.validPipeCount()).toEqual(86);
    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(13);
    expect(network.arrayTracePath[0].flow).toEqual(2162.320068);
    expect(network.arrayTracePath[0].diameter).toEqual(1200);
    expect(network.arrayTracePath[0].pipes.length).toEqual(1);
    const path1Shape = [
      '437673.182283964 2874252.12301975',
      '437673.2636066 2874261.91859613',
    ];
    expect(network.arrayTracePath[0].shape).toEqual(
      `LINESTRING(${path1Shape.join(',')})`,
    );
  });

  it('up-trace-network2', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockUpTraceNetwork2);
    expect(mockUpTraceNetwork2.length).toEqual(53);
    expect(network.validPipeCount()).toEqual(33);
    expect(network.hasPipe('WDM_MODELPIPES@P-115788')).toBeFalsy();
    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(5);
    expect(network.arrayTracePath[0].diameter).toEqual(1000);
    expect(network.arrayTracePath[0].flow).toEqual(3824.917236);

    expect(network.arrayTracePath[0].pipes.length).toEqual(8);
    expect(network.arrayTracePath[0].pipes[0].id).toEqual('WDM_PIPES@D_FFS_50');
    expect(network.arrayTracePath[0].pipes[0].diameter).toEqual(1000);
    expect(network.arrayTracePath[0].pipes[0].flow).toEqual(3824.917236);
    expect(network.arrayTracePath[0].pipes[0].startNodeId).toEqual(
      '425832.109977698 2879049.28125206',
    );
    const path1Shape = [
      '425832.109977698 2879049.28125206',
      '425829.83257786656 2879047.2366440473',
      '425826.773281129 2879044.49006326',
      '425825.749185673 2879043.56703487',
      '425816.192972018 2879034.24672822',
      '425811.448818434 2879029.61957598',
      '425807.551272551 2879020.47213521',
      '425816.086439164 2879008.75622567',
      '425822.840553522 2878998.94547406',
      '425848.534978453 2878961.3105963',
      '425853.612075799 2878954.28705205',
      '425875.093438948 2878923.06666054',
      '425876.669468229 2878920.85748989',
      '425901.164898089 2878885.74279153',
      '425927.194351366 2878848.3209178',
      '425944.628628258 2878822.43194434',
      '425957.972890068 2878804.15552473',
      '425957.312736465 2878800.71933866',
      '425956.596490475 2878794.5429922',
      '425962.967586768 2878784.93126211',
      '425972.393145008 2878783.54193967',
      '425992.156481455 2878754.89074369',
      '426002.524665893 2878740.10460609',
      '426012.566845346 2878725.80850575',
      '426046.623434859 2878676.73673934',
      '426063.893734766 2878651.88183113',
      '426077.641983057 2878632.39032946',
      '426103.143447166 2878596.34555052',
      '426124.555802075 2878565.0071538',
      '426143.398133146 2878538.00907893',
      '426144.8443532556 2878535.883252318',
      '426146.799285103 2878535.98087241',
      '426187.12067038263 2878541.561742396',
      '426276.0159069534 2878417.203358177',
      '426290.80211962963 2878396.518453248',
    ];
    expect(network.arrayTracePath[0].shape).toEqual(
      `LINESTRING(${path1Shape.join(',')})`,
    );
  });

  it('up-trace-network3', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockUpTraceNetwork3);
    expect(mockUpTraceNetwork3.length).toEqual(61);
    expect(network.validPipeCount()).toEqual(37);
    expect(network.hasPipe('WDM_MODELPIPES@P-115740')).toBeFalsy();
    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(9);
    expect(network.arrayTracePath[0].diameter).toEqual(1200);
    expect(network.arrayTracePath[0].flow).toEqual(1536.114258);
    expect(network.arrayTracePath[0].pipes.length).toEqual(9);
    expect(network.arrayTracePath[0].pipes[0].id).toEqual(
      'WDM_MODELPIPES@XZ-P120439',
    );
    expect(network.arrayTracePath[0].pipes[0].diameter).toEqual(1200);
    expect(network.arrayTracePath[0].pipes[0].flow).toEqual(1536.114258);
    expect(network.arrayTracePath[0].pipes[0].startNodeId).toEqual(
      '437663.00863834 2874252.29328929',
    );
  });

  it('up-trace-network-pump', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockUpTraceNetworkWithPump);
    expect(mockUpTraceNetworkWithPump.length).toEqual(9);
    expect(network.validPipeCount()).toEqual(9);
    expect(network.hasPipe('WDM_PUMPS@FFS-P5')).toBeTruthy();
    network.tryMergePath();

    expect(network.arrayTracePath.length).toEqual(6);
    expect(network.arrayTracePath[0].diameter).toEqual(1500);
    expect(network.arrayTracePath[0].flow).toEqual(8633.65332);
    expect(network.arrayTracePath[0].pipes.length).toEqual(1);
    expect(network.arrayTracePath[0].pipes[0].id).toEqual(
      'WDM_MODELPIPES@P-115785',
    );
    expect(network.arrayTracePath[0].pipes[0].diameter).toEqual(1500);
    expect(network.arrayTracePath[0].pipes[0].flow).toEqual(8633.65332);
    expect(network.arrayTracePath[0].pipes[0].startNodeId).toEqual(
      '425826.973557841 2879083.49236132',
    );

    // network.arrayTracePath.forEach((item) => console.log(item));

    expect(network.arrayTracePath[3].pipes.length).toEqual(2);
    expect(network.arrayTracePath[3].pipes[1].id).toEqual('WDM_PUMPS@FFS-P5');
  });

  it('up-trace-network-pump2', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockUpTraceNetworkWithPump2);
    expect(mockUpTraceNetworkWithPump2.length).toEqual(7);
    expect(network.validPipeCount()).toEqual(7);
    expect(network.hasPipe('WDM_PUMPS@FFS-P4')).toBeTruthy();
    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(4);
    expect(network.arrayTracePath[3].diameter).toEqual(1000);
    expect(network.arrayTracePath[3].flow).toEqual(4396.383789);
    expect(network.arrayTracePath[3].pipes.length).toEqual(4);
    expect(network.arrayTracePath[3].pipes[1].id).toEqual('WDM_PUMPS@FFS-P4');
    expect(network.arrayTracePath[3].pipes[1].diameter).toEqual(0);
  });

  it('down-trace-network', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockDownTraceNetwork);
    expect(mockDownTraceNetwork.length).toEqual(84);
    expect(network.validPipeCount()).toEqual(35);

    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(15);
    expect(network.arrayTracePath[0].flow).toEqual(162.597198);
    expect(network.arrayTracePath[0].diameter).toEqual(400);
    expect(network.arrayTracePath[0].pipes.length).toEqual(4);
    expect(network.arrayTracePath[0].pipes[0].diameter).toEqual(400);
    expect(network.arrayTracePath[0].pipes[0].flow).toEqual(162.597198);
    expect(network.arrayTracePath[0].pipes[0].startNodeId).toEqual(
      '422644.331220421 2886590.22292339',
    );
    const path1Shape = [
      '422644.331220421 2886590.22292339',
      '422627.493108495 2886620.02511273',
      '422619.270107307 2886636.22927935',
      '422613.174105102 2886648.20114179',
      '422602.081130199 2886670.88476423',
      '422583.878263686 2886710.95559477',
      '422571.287164511 2886732.76320369',
      '422568.328168765 2886738.73863202',
      '422552.46721161 2886771.38696451',
      '422532.130288074 2886813.91299446',
      '422512.452347636 2886854.61589994',
      '422493.750422414 2886893.86069442',
      '422471.829497153 2886939.46194637',
      '422461.027565312 2886962.89960592',
      '422440.987695335 2887006.49169124',
      '422432.976067269 2887033.79250775',
      '422423.764208807 2887056.35407501',
      '422416.180234842 2887072.13620041',
      '422410.302255665 2887084.38807383',
      '422402.312232248 2887099.44616683',
      '422394.678322657 2887117.31341663',
      '422392.524436028 2887125.06592937',
      '422393.418969184 2887139.75177378',
      '422406.80264143 2887164.92191878',
      '422413.699167185 2887170.61097883',
      '422414.066697989 2887170.83568273',
      '422422.89803817 2887176.2344763',
      '422426.68085522 2887178.54701637',
      '422431.91069355 2887181.74417179',
    ];
    expect(network.arrayTracePath[0].shape).toEqual(
      `LINESTRING(${path1Shape.join(',')})`,
    );
  });

  it('down-trace-network2', () => {
    const network: TraceNetwork = new TraceNetwork();
    network.initialize(mockDownTraceNetwork2);
    expect(mockDownTraceNetwork2.length).toEqual(54);
    expect(network.validPipeCount()).toEqual(40);

    network.tryMergePath();
    expect(network.arrayTracePath.length).toEqual(21);
    expect(network.arrayTracePath[0].flow).toEqual(1.15136);
    expect(network.arrayTracePath[0].diameter).toEqual(400);
    expect(network.arrayTracePath[0].pipes.length).toEqual(8);
    expect(network.arrayTracePath[0].pipes[0].diameter).toEqual(400);
    expect(network.arrayTracePath[0].pipes[0].flow).toEqual(1.15136);
    expect(network.arrayTracePath[0].pipes[0].startNodeId).toEqual(
      '423310.84779999964 2887578.4071999993',
    );
    const path1Shape = [
      '423310.84779999964 2887578.4071999993',
      '423326.1579 2887583.2040999997',
      '423353.4779000003 2887591.2608000003',
      '423378.5728000002 2887598.3905999996',
      '423394.5022 2887604.7524999995',
      '423396.10140000004 2887606.608100001',
      '423436.47250000015 2887623.6626999993',
      '423473.8487999998 2887647.1974',
      '423492.01240000036 2887658.6345000006',
      '423496.8883999996 2887662.3915999997',
      '423500.2485999996 2887664.9498999994',
      '423523.49149999954 2887680.2249999996',
      '423535.10720000044 2887685.0548',
      '423545.9023000002 2887688.7854999993',
      '423559.87229999993 2887692.5161000006',
      '423571.9941999996 2887694.5387999993',
      '423584.82650000043 2887696.4571',
      '423598.99060000014 2887697.0656000003',
      '423626.8618000001 2887694.1446',
      '423643.3666000003 2887691.4855000004',
      '423668.2131000003 2887686.9749999996',
      '423697.38900000043 2887679.6217',
      '423718.06620000023 2887679.3307000007',
      '423739.12710000016 2887682.2939999998',
    ];
    expect(network.arrayTracePath[0].shape).toEqual(
      `LINESTRING(${path1Shape.join(',')})`,
    );
  });
});
