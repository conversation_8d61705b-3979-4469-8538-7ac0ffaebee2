/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  convertMapToSerializable,
  convertSerializableToMap,
} from '../track-data';

describe('convertMapToSerializable 函数', () => {
  test('处理基本类型值', () => {
    expect(convertMapToSerializable(null)).toBeNull();
    expect(convertMapToSerializable(undefined)).toBeUndefined();
    expect(convertMapToSerializable(123)).toBe(123);
    expect(convertMapToSerializable('abc')).toBe('abc');
    expect(convertMapToSerializable(true)).toBe(true);
  });

  test('处理普通对象', () => {
    const obj = { a: 1, b: 'test', c: true };
    expect(convertMapToSerializable(obj)).toEqual(obj);
  });

  test('处理数组', () => {
    const arr = [1, 'test', true];
    expect(convertMapToSerializable(arr)).toEqual(arr);
  });

  test('处理简单Map', () => {
    const map = new Map<string, number>();
    map.set('a', 1);
    map.set('b', 2);

    const result = convertMapToSerializable(map);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([
      { key: 'a', value: 1 },
      { key: 'b', value: 2 },
    ]);
  });

  test('处理嵌套Map', () => {
    const innerMap = new Map<string, number>();
    innerMap.set('c', 3);
    innerMap.set('d', 4);

    const outerMap = new Map<string, Map<string, number>>();
    outerMap.set('inner', innerMap);

    const result = convertMapToSerializable(outerMap);
    expect(result).toEqual([
      {
        key: 'inner',
        value: [
          { key: 'c', value: 3 },
          { key: 'd', value: 4 },
        ],
      },
    ]);
  });

  test('处理包含Map的对象', () => {
    const map = new Map<string, number>();
    map.set('a', 1);
    map.set('b', 2);

    const obj = { map, regular: 'value' };
    const result = convertMapToSerializable(obj);

    expect(result).toEqual({
      map: [
        { key: 'a', value: 1 },
        { key: 'b', value: 2 },
      ],
      regular: 'value',
    });
  });

  test('处理包含Map的数组', () => {
    const map = new Map<string, number>();
    map.set('a', 1);

    const arr = [map, 'test'];
    const result = convertMapToSerializable(arr);

    expect(result).toEqual([[{ key: 'a', value: 1 }], 'test']);
  });

  test('处理复杂嵌套结构', () => {
    const innerMap = new Map<string, any>();
    innerMap.set('array', [1, 2, 3]);
    innerMap.set('obj', { x: 'y' });

    const outerMap = new Map<string, any>();
    outerMap.set('innerMap', innerMap);
    outerMap.set('primitive', 42);

    const complexObj = {
      maps: outerMap,
      array: [1, innerMap],
      nested: {
        map: innerMap,
      },
    };

    const result = convertMapToSerializable(complexObj);

    // 验证结构是否正确
    expect(result).toEqual({
      maps: [
        {
          key: 'innerMap',
          value: [
            { key: 'array', value: [1, 2, 3] },
            { key: 'obj', value: { x: 'y' } },
          ],
        },
        { key: 'primitive', value: 42 },
      ],
      array: [
        1,
        [
          { key: 'array', value: [1, 2, 3] },
          { key: 'obj', value: { x: 'y' } },
        ],
      ],
      nested: {
        map: [
          { key: 'array', value: [1, 2, 3] },
          { key: 'obj', value: { x: 'y' } },
        ],
      },
    });
  });
});

describe('convertSerializableToMap 函数', () => {
  test('处理基本类型值', () => {
    expect(convertSerializableToMap(null)).toBeNull();
    expect(convertSerializableToMap(undefined)).toBeUndefined();
    expect(convertSerializableToMap(123)).toBe(123);
    expect(convertSerializableToMap('abc')).toBe('abc');
    expect(convertSerializableToMap(true)).toBe(true);
  });

  test('处理普通对象', () => {
    const obj = { a: 1, b: 'test', c: true };
    expect(convertSerializableToMap(obj)).toEqual(obj);
  });

  test('处理数组', () => {
    const arr = [1, 'test', true];
    expect(convertSerializableToMap(arr)).toEqual(arr);
  });

  test('处理序列化的简单Map', () => {
    const serialized = [
      { key: 'a', value: 1 },
      { key: 'b', value: 2 },
    ];

    const result = convertSerializableToMap(serialized);
    expect(result instanceof Map).toBe(true);

    const map = result as Map<string, number>;
    expect(map.get('a')).toBe(1);
    expect(map.get('b')).toBe(2);
    expect(map.size).toBe(2);
  });

  test('处理序列化的嵌套Map', () => {
    const serialized = [
      {
        key: 'inner',
        value: [
          { key: 'c', value: 3 },
          { key: 'd', value: 4 },
        ],
      },
    ];

    const result = convertSerializableToMap(serialized);
    expect(result instanceof Map).toBe(true);

    const outerMap = result as Map<string, Map<string, number>>;
    expect(outerMap.size).toBe(1);

    const innerMap = outerMap.get('inner');
    expect(innerMap instanceof Map).toBe(true);
    expect(innerMap?.get('c')).toBe(3);
    expect(innerMap?.get('d')).toBe(4);
    expect(innerMap?.size).toBe(2);
  });

  test('处理包含序列化Map的对象', () => {
    const serialized = {
      map: [
        { key: 'a', value: 1 },
        { key: 'b', value: 2 },
      ],
      regular: 'value',
    };

    const result = convertSerializableToMap(serialized);
    expect(typeof result).toBe('object');
    expect(result).not.toBeNull();

    const obj = result as { map: Map<string, number>; regular: string };
    expect(obj.map instanceof Map).toBe(true);
    expect(obj.map.get('a')).toBe(1);
    expect(obj.map.get('b')).toBe(2);
    expect(obj.regular).toBe('value');
  });

  test('处理包含序列化Map的数组', () => {
    const serialized = [[{ key: 'a', value: 1 }], 'test'];

    const result = convertSerializableToMap(serialized);
    expect(Array.isArray(result)).toBe(true);

    const arr = result as [Map<string, number>, string];
    expect(arr[0] instanceof Map).toBe(true);
    expect(arr[0].get('a')).toBe(1);
    expect(arr[1]).toBe('test');
  });

  test('处理复杂嵌套结构', () => {
    const serialized = {
      maps: [
        {
          key: 'innerMap',
          value: [
            { key: 'array', value: [1, 2, 3] },
            { key: 'obj', value: { x: 'y' } },
          ],
        },
        { key: 'primitive', value: 42 },
      ],
      array: [
        1,
        [
          { key: 'array', value: [1, 2, 3] },
          { key: 'obj', value: { x: 'y' } },
        ],
      ],
      nested: {
        map: [
          { key: 'array', value: [1, 2, 3] },
          { key: 'obj', value: { x: 'y' } },
        ],
      },
    };

    const result = convertSerializableToMap(serialized);

    // 验证结构
    expect(result).toEqual({
      maps: expect.any(Map),
      array: expect.arrayContaining([1, expect.any(Map)]),
      nested: {
        map: expect.any(Map),
      },
    });

    // 直接验证具体值而不是结构
    const resultObj = result as any;
    expect(resultObj.maps instanceof Map).toBe(true);
    expect(resultObj.maps.get('primitive')).toBe(42);

    const resultInnerMap = resultObj.maps.get('innerMap');
    expect(resultInnerMap instanceof Map).toBe(true);
    expect(resultInnerMap.get('array')).toEqual([1, 2, 3]);
    expect(resultInnerMap.get('obj')).toEqual({ x: 'y' });
  });

  test('处理循环往复的转换', () => {
    // 创建一个复杂结构
    const innerMap = new Map<string, any>();
    innerMap.set('array', [1, 2, 3]);
    innerMap.set('obj', { x: 'y' });

    const outerMap = new Map<string, any>();
    outerMap.set('innerMap', innerMap);
    outerMap.set('primitive', 42);

    const complexObj = {
      maps: outerMap,
      regular: 'value',
    };

    // 先序列化
    const serialized = convertMapToSerializable(complexObj);
    // 再反序列化
    const deserialized = convertSerializableToMap(serialized);

    // 验证结构
    expect(deserialized).toEqual({
      maps: expect.any(Map),
      regular: 'value',
    });

    // 直接验证具体值而不是结构
    const resultObj = deserialized as any;
    expect(resultObj.maps instanceof Map).toBe(true);
    expect(resultObj.maps.get('primitive')).toBe(42);

    const resultInnerMap = resultObj.maps.get('innerMap');
    expect(resultInnerMap instanceof Map).toBe(true);
    expect(resultInnerMap.get('array')).toEqual([1, 2, 3]);
    expect(resultInnerMap.get('obj')).toEqual({ x: 'y' });
    expect(resultObj.regular).toBe('value');
  });
});
