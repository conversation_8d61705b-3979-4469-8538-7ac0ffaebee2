/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getChildrenFromTreeData,
  getParentFromTreeData,
  getTreeDataNode,
  loopTreeList,
  transListToTree,
  transTreeToList,
} from '../tree';

interface TreeNode {
  id: number;
  name: string;
  parentId: number | null;
  order: number;
  children?: TreeNode[];
}

const list: TreeNode[] = [
  { id: 1, name: 'Node 1', parentId: null, order: 2 },
  { id: 2, name: 'Node 2', parentId: 1, order: 1 },
  { id: 3, name: 'Node 3', parentId: 1, order: 3 },
  { id: 4, name: 'Node 4', parentId: 2, order: 2 },
  { id: 5, name: 'Node 5', parentId: 2, order: 1 },
  { id: 6, name: 'Node 6', parentId: 3, order: 1 },
  { id: 7, name: 'Node 7', parentId: null, order: 1 },
];

const tree: TreeNode[] = [
  {
    id: 1,
    name: 'Node 1',
    parentId: null,
    order: 2,
    children: [
      {
        id: 2,
        name: 'Node 2',
        parentId: 1,
        order: 1,
        children: [
          { id: 4, name: 'Node 4', parentId: 2, order: 2 },
          { id: 5, name: 'Node 5', parentId: 2, order: 1 },
        ],
      },
      {
        id: 3,
        name: 'Node 3',
        parentId: 1,
        order: 3,
        children: [{ id: 6, name: 'Node 6', parentId: 3, order: 1 }],
      },
    ],
  },
  { id: 7, name: 'Node 7', parentId: null, order: 1 },
];

describe('transListToTree', () => {
  it('should transform flat list to tree structure', () => {
    const result = transListToTree(list, 'id', 'parentId');
    expect(result).toEqual(tree);
  });

  it('should handle empty list', () => {
    const result = transListToTree([], 'id', 'parentId');
    expect(result).toEqual([]);
  });

  it('should handle list with single root node', () => {
    const result = transListToTree(
      [{ id: 1, name: 'Node 1', parentId: null, order: 1 }],
      'id',
      'parentId',
    );
    expect(result).toEqual([
      { id: 1, name: 'Node 1', parentId: null, order: 1 },
    ]);
  });

  it('should handle list with multiple root nodes', () => {
    const result = transListToTree(
      [
        { id: 1, name: 'Node 1', parentId: null, order: 2 },
        { id: 2, name: 'Node 2', parentId: null, order: 1 },
      ],
      'id',
      'parentId',
    );
    expect(result).toEqual([
      { id: 1, name: 'Node 1', parentId: null, order: 2 },
      { id: 2, name: 'Node 2', parentId: null, order: 1 },
    ]);
  });

  it('should sort tree nodes in ascending order', () => {
    const result = transListToTree(list, 'id', 'parentId', {
      sorted: 'asc',
      sortedKey: 'order',
    });

    // 验证根节点排序
    expect(result[0].id).toBe(7); // order: 1
    expect(result[1].id).toBe(1); // order: 2

    // 验证第一层子节点排序
    const firstNodeChildren = result[1].children!;
    expect(firstNodeChildren[0].id).toBe(2); // order: 1
    expect(firstNodeChildren[1].id).toBe(3); // order: 3

    // 验证第二层子节点排序
    const secondNodeChildren = firstNodeChildren[0].children!;
    expect(secondNodeChildren[0].id).toBe(5); // order: 1
    expect(secondNodeChildren[1].id).toBe(4); // order: 2
  });

  it('should sort tree nodes in descending order', () => {
    const result = transListToTree(list, 'id', 'parentId', {
      sorted: 'desc',
      sortedKey: 'order',
    });

    // 验证根节点排序
    expect(result[0].id).toBe(1); // order: 2
    expect(result[1].id).toBe(7); // order: 1

    // 验证第一层子节点排序
    const firstNodeChildren = result[0].children!;
    expect(firstNodeChildren[0].id).toBe(3); // order: 3
    expect(firstNodeChildren[1].id).toBe(2); // order: 1

    // 验证第二层子节点排序
    const secondNodeChildren = firstNodeChildren[1].children!;
    expect(secondNodeChildren[0].id).toBe(4); // order: 2
    expect(secondNodeChildren[1].id).toBe(5); // order: 1
  });

  it('should handle nodes without order value', () => {
    const listWithoutOrder: TreeNode[] = [
      { id: 1, name: 'Node 1', parentId: null, order: 0 },
      { id: 2, name: 'Node 2', parentId: 1, order: 1 },
      { id: 3, name: 'Node 3', parentId: 1, order: 0 },
    ];

    const result = transListToTree(listWithoutOrder, 'id', 'parentId', {
      sorted: 'asc',
      sortedKey: 'order',
    });

    // 验证根节点
    expect(result[0].id).toBe(1); // 没有order值，默认为0

    // 验证子节点排序
    const children = result[0].children!;
    expect(children[0].id).toBe(3); // 没有order值，默认为0
    expect(children[1].id).toBe(2); // order: 1
  });

  describe('convertItem functionality', () => {
    interface InputNode {
      id: number;
      name: string;
      parentId: number | null;
      order: number;
    }

    interface OutputNode {
      id: number;
      name: string;
      parentId: number | null;
      order: number;
      displayName: string;
    }

    const inputList: InputNode[] = [
      { id: 1, name: 'Node 1', parentId: null, order: 2 },
      { id: 2, name: 'Node 2', parentId: 1, order: 1 },
      { id: 3, name: 'Node 3', parentId: 1, order: 3 },
    ];

    const convertItem = (item: InputNode): OutputNode => ({
      ...item,
      displayName: `Display: ${item.name}`,
    });

    it('should convert items using convertItem function', () => {
      const result = transListToTree<InputNode, OutputNode>(
        inputList,
        'id',
        'parentId',
        {
          convertItem,
        },
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        name: 'Node 1',
        displayName: 'Display: Node 1',
        children: [
          {
            id: 2,
            name: 'Node 2',
            displayName: 'Display: Node 2',
          },
          {
            id: 3,
            name: 'Node 3',
            displayName: 'Display: Node 3',
          },
        ],
      });
    });

    it('should maintain tree structure after conversion', () => {
      const result = transListToTree<InputNode, OutputNode>(
        inputList,
        'id',
        'parentId',
        {
          convertItem,
        },
      );

      // 验证根节点
      expect(result[0].id).toBe(1);
      expect(result[0].displayName).toBe('Display: Node 1');

      // 验证子节点
      const children = result[0].children!;
      expect(children).toHaveLength(2);
      expect(children[0].id).toBe(2);
      expect(children[0].displayName).toBe('Display: Node 2');
      expect(children[1].id).toBe(3);
      expect(children[1].displayName).toBe('Display: Node 3');
    });

    it('should work with sorting after conversion', () => {
      const result = transListToTree<InputNode, OutputNode>(
        inputList,
        'id',
        'parentId',
        {
          convertItem,
          sorted: 'asc',
          sortedKey: 'order',
        },
      );

      // 验证根节点
      expect(result[0].id).toBe(1);
      expect(result[0].displayName).toBe('Display: Node 1');

      // 验证子节点排序
      const children = result[0].children!;
      expect(children[0].id).toBe(2); // order: 1
      expect(children[1].id).toBe(3); // order: 3
    });

    it('should handle empty list with conversion', () => {
      const result = transListToTree<InputNode, OutputNode>(
        [],
        'id',
        'parentId',
        {
          convertItem,
        },
      );
      expect(result).toEqual([]);
    });
  });
});

describe('transTreeToList', () => {
  it('should transform tree structure to flat list', () => {
    const result = transTreeToList(tree);
    expect(result).toHaveLength(7);
    expect(result.map((i) => i.id)).toEqual([1, 2, 4, 5, 3, 6, 7]);
  });
});

describe('getTreeDataNode', () => {
  it('should return the tree node with the specified key', () => {
    const result = getTreeDataNode(tree, 'id', 2);
    expect(result?.id).toBe(2);
    expect(result?.children?.map((i) => i.id)).toEqual([4, 5]);
  });

  it('should return undefined if the tree node with the specified key is not found', () => {
    const result = getTreeDataNode(tree, 'id', 8);
    expect(result).toBeUndefined();
  });
});

describe('loopTreeList', () => {
  it('should call the callback function for each tree item', () => {
    const callback = jest.fn();
    loopTreeList(tree, callback);
    expect(callback).toHaveBeenCalledTimes(7);
    const calledIds = callback.mock.calls.map((call) => call[0].id);
    expect(calledIds.sort()).toEqual([1, 2, 3, 4, 5, 6, 7]);
  });

  it('should not call the callback function if it is not provided', () => {
    const callback = jest.fn();
    loopTreeList(tree);
    expect(callback).not.toHaveBeenCalled();
  });
});

describe('getLeafsFromTreeList', () => {
  it('should return an array of leaf nodes for the given id', () => {
    const leafs = getChildrenFromTreeData(tree, 2);
    expect(leafs).toHaveLength(2);
    expect(leafs.map((i) => i.id)).toEqual([4, 5]);
  });

  it('should return an empty array if the id is not found', () => {
    const leafs = getChildrenFromTreeData(tree, 8);
    expect(leafs).toHaveLength(0);
  });
});

describe('getParentFromTreeData', () => {
  it('should return undefined if the id is not found', () => {
    expect(getParentFromTreeData(tree, 'id', 42)).toBeUndefined();
  });

  it('should return undefined if the node is no parent', () => {
    expect(getParentFromTreeData(tree, 'id', 1)).toBeUndefined();
    expect(getParentFromTreeData(tree, 'id', 7)).toBeUndefined();
  });

  it('should return the tree node with the specified key', () => {
    const parent = getParentFromTreeData(tree, 'id', 2);
    expect(parent?.id).toBe(1);
    expect(parent?.children?.map((i) => i.id)).toEqual([2, 3]);
  });

  it('returns parent node for second-level children', () => {
    const parent = getParentFromTreeData(tree, 'id', 4);
    expect(parent?.id).toBe(2);
    expect(parent?.children?.map((i) => i.id)).toEqual([4, 5]);
  });
});
