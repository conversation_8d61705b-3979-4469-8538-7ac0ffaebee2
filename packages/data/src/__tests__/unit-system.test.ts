/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DoubleUnitFormat,
  generateUnitFormat,
  getUnitFormat,
  getUnitValue,
  getUnitValues,
  getUnitValueWithSymbol,
  registerSystemUnits,
  UnitFormat,
} from '../unit-system';

describe('unit-system-invalid-cases', () => {
  it('invalid unit unit_type with wrong value', () => {
    const invalidCase = {
      unit_title: '面积',
      unit_type: 'X',
      value_add: 0,
      value_muli: 1,
      value_precision: 1,
      value_title: 'm²',
    };
    const unit: UnitFormat | null = generateUnitFormat('AREA', invalidCase);
    expect(unit).toBeNull();
  });

  it('invalid unit unit_type with wrong type', () => {
    const invalidCase = {
      unit_title: '面积',
      unit_type: 1,
      value_add: 0,
      value_muli: 1,
      value_precision: 1,
      value_title: 'm²',
    };
    const unit: UnitFormat | null = generateUnitFormat('AREA', invalidCase);
    expect(unit).toBeNull();
  });

  it('invalid double unit value_add', () => {
    const doubleUnitCase1 = {
      unit_title: '面积',
      unit_type: 'D',
      value_add: 't',
      value_muli: 1,
      value_precision: 1,
      value_title: 'm²',
    };
    const unit: UnitFormat | null = generateUnitFormat('AREA', doubleUnitCase1);
    expect(unit?.isValid).toBe(false);
  });

  it('invalid double unit value_muli is 0', () => {
    const doubleUnitCase1 = {
      unit_title: '面积',
      unit_type: 'D',
      value_add: 0,
      value_muli: 0,
      value_precision: 1,
      value_title: 'm²',
    };
    const unit: UnitFormat | null = generateUnitFormat('AREA', doubleUnitCase1);
    expect(unit?.isValid).toBe(true);
    if (unit instanceof DoubleUnitFormat)
      expect(unit.getOriginalValue(1)).toBe(1);
  });

  it('invalid enum unit', () => {
    const enumCase1 = {
      unit_title: '可靠度',
      unit_type: 'E',
      value_add: 0,
      value_muli: 1,
      value_precision: 0,
      value_title: '',
    };
    const unit1: UnitFormat | null = generateUnitFormat(
      'RELIABILITY',
      enumCase1,
    );
    expect(unit1?.isValid).toBe(false);

    const enumCase2 = {
      unit_title: '可靠度',
      unit_type: 'E',
      value_add: 0,
      value_muli: 1,
      value_precision: 0,
      value_title: null,
    };
    const unit2: UnitFormat | null = generateUnitFormat(
      'RELIABILITY',
      enumCase2,
    );
    expect(unit2?.isValid).toBe(false);
  });

  it('invalid time unit', () => {
    const timeUnitCase1 = {
      unit_title: null,
      unit_type: 'T',
      value_add: 0,
      value_muli: 1,
      value_precision: 0,
      value_title: '',
    };
    const unit: UnitFormat | null = generateUnitFormat('DATE', timeUnitCase1);
    expect(unit?.isValid).toBe(false);
  });

  it('invalid range unit - empty string', () => {
    const rangeUnitCase = {
      unit_title: '内涝风险',
      unit_type: 'R',
      value_add: 0,
      value_muli: 1,
      value_precision: 2,
      value_title: '',
    };
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    expect(unit?.isValid).toBe(false);
  });

  it('invalid range unit - empty array', () => {
    const rangeUnitCase = {
      unit_title: '内涝风险',
      unit_type: 'R',
      value_add: 0,
      value_muli: 1,
      value_precision: 2,
      value_title: '[]',
    };
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    expect(unit?.isValid).toBe(false);
  });

  it('invalid range unit - invalid range 1', () => {
    const rangeUnitCase = {
      unit_title: '内涝风险',
      unit_type: 'R',
      value_add: 0,
      value_muli: 1,
      value_precision: 2,
      value_title:
        '[\n{"无": [10]},\n{"低": [10]},\n{"中": [40,60]},\n{"高": [60,80]},\n{"很高": [80]}\n]',
    };
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    expect(unit?.isValid).toBe(false);
  });

  it('invalid range unit - invalid range 2', () => {
    const rangeUnitCase = {
      unit_title: '内涝风险',
      unit_type: 'R',
      value_add: 0,
      value_muli: 1,
      value_precision: 2,
      value_title:
        '[\n{"无": [0,10]},\n{"低": [10,40]},\n{"中": [40,60]},\n{"高": [60,80]},\n{"很高": [80]}\n]',
    };
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    expect(unit?.isValid).toBe(false);
  });

  it('invalid range unit - invalid range json', () => {
    const rangeUnitCase = {
      unit_title: '内涝风险',
      unit_type: 'R',
      value_add: 0,
      value_muli: 1,
      value_precision: 2,
      value_title:
        '[\n{"无": },\n{"低": [10,40]},\n{"中": [40,60]},\n{"高": [60,80]},\n{"很高": [80]}\n',
    };
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    expect(unit?.isValid).toBe(false);
  });
});

describe('unit-system-valid-cases', () => {
  const areaUnitCase = {
    unit_title: '面积',
    unit_type: 'D',
    value_add: 0,
    value_muli: 1,
    value_precision: 1,
    value_title: 'm²',
  };

  it('valid double unit with 0 add, 1 multiply, 1 precision', () => {
    const unit: UnitFormat | null = generateUnitFormat('AREA', areaUnitCase);
    if (unit != null) {
      expect(unit.unitType).toBe('D');
      expect(unit.getValue('')).toBe(undefined);
      expect(unit.getValue(12.34)).toBe(12.3);
      expect(unit.getValue(12.501)).toBe(12.5);
      expect(unit.getValue('12.501')).toBe(12.5);
      expect(unit.getValue('12.501 m')).toBeUndefined();
      expect(unit.getValueWithSymbol(12.34)).toEqual('12.3 m²');
      expect(unit.getValueWithSymbol(12.5)).toEqual('12.5 m²');
      expect(unit.getYAxisValues()).toEqual([]);
    }
  });

  const flowUnitCase = {
    unit_title: '流量',
    unit_type: 'D',
    value_add: 0,
    value_muli: 3600,
    value_precision: 1,
    value_title: 'm³/h',
  };

  it('valid double unit with 0 add, 3600 multiply, 1 precision', () => {
    const unit1: UnitFormat | null = generateUnitFormat('FLOW', flowUnitCase);
    if (unit1 != null) {
      expect(unit1.unitType).toBe('D');
      expect(unit1.getValue('')).toBe(undefined);
      expect(unit1.getValue(0.1)).toBe(360);
      expect(unit1.getValue(0.101)).toBe(363.6);
      expect(unit1.getValueWithSymbol(0.1)).toEqual('360 m³/h');
      expect(unit1.getValueWithSymbol(0.101)).toEqual('363.6 m³/h');
      expect(unit1 instanceof DoubleUnitFormat).toBeTruthy();
      if (unit1 instanceof DoubleUnitFormat) {
        expect(unit1.getExactValue(0.1)).toBe(360);
        expect(unit1.getExactValue('0.1')).toBe(360);
        expect(unit1.getExactValue('undefined')).toBe(undefined);
        expect(unit1.getOriginalValue(360)).toBe(0.1);
      }
    }
  });

  const phUnitCase = {
    unit_title: 'PH',
    unit_type: 'D',
    value_add: 0,
    value_muli: 1,
    value_precision: 0,
    value_title: null,
  };

  it('valid double unit with value_title is null', () => {
    const unit1: UnitFormat | null = generateUnitFormat('FLOW', phUnitCase);
    if (unit1 != null) {
      expect(unit1.unitType).toBe('D');
      expect(unit1.getValue(7.12)).toBe(7);
      expect(unit1.getValues().length).toBe(0);
      expect(unit1.getValueWithSymbol(7.12)).toEqual('7');
    }
  });

  const reliabilityUnitCase = {
    unit_title: '可靠度',
    unit_type: 'E',
    value_add: 0,
    value_muli: 1,
    value_precision: 0,
    value_title:
      '{\r\n"0":"未评估",\r\n"1":"坏",\r\n"2":"差",\r\n"3":"良",\r\n"4":"优"\r\n}',
  };

  it('valid enum unit', () => {
    const unit: UnitFormat | null = generateUnitFormat(
      'RELIABILITY',
      reliabilityUnitCase,
    );
    if (unit != null) {
      expect(unit.unitType).toBe('E');
      expect(unit.getValue('0')).toBe('未评估');
      expect(unit.getValue('1')).toBe('坏');
      expect(unit.getValue('2')).toBe('差');
      expect(unit.getValue('3')).toBe('良');
      expect(unit.getValue('4')).toBe('优');
      expect(unit.getValue('5')).toBeUndefined();
      expect(unit.getValue(4)).toBe('优');
      expect(unit.getValues().length).toBe(0);
      expect(unit.getValueWithSymbol('1')).toEqual('坏');
      expect(unit.getValueWithSymbol('5')).toEqual('-');
      expect(unit.getYAxisValues()).toEqual(['0', '1', '2', '3', '4']);
    }
  });

  const dateUnitCase = {
    unit_title: null,
    unit_type: 'T',
    value_add: 0,
    value_muli: 1,
    value_precision: 0,
    value_title: 'YYYY-MM-DD',
  };

  it('valid time unit', () => {
    const unit: UnitFormat | null = generateUnitFormat('DATE', dateUnitCase);
    if (unit != null) {
      expect(unit.unitType).toBe('T');
      expect(unit.getValue(20220801)).toBeUndefined();
      expect(unit.getValue('2022-08-01 08:00:00')).toBe('2022-08-01');
      expect(unit.getValues().length).toBe(0);
      expect(unit.getValueWithSymbol(20220801)).toBe('-');
      expect(unit.getValueWithSymbol('2022-08-01 08:00:00')).toBe('2022-08-01');
      expect(unit.getYAxisValues()).toEqual([]);
    }
  });

  const rangeUnitCase = {
    unit_title: '内涝风险',
    unit_type: 'R',
    value_add: 0,
    value_muli: 1,
    value_precision: 2,
    value_title:
      '[\n{"无": [10]},\n{"低": [10,40]},\n{"中": [40,60]},\n{"高": [60,80]},\n{"很高": [80]}\n]',
  };

  it('valid range unit', () => {
    const unit: UnitFormat | null = generateUnitFormat(
      'INUNDATION',
      rangeUnitCase,
    );
    if (unit != null) {
      expect(unit.unitType).toBe('R');
      expect(unit.getValue(5.18)).toBe('无');
      expect(unit.getValue(10)).toBe('低');
      expect(unit.getValue(25.18)).toBe('低');
      expect(unit.getValue(60)).toBe('高');
      expect(unit.getValue(65.18)).toBe('高');
      expect(unit.getValue(100.18)).toBe('很高');
      expect(unit.getValue('100.18')).toBe('很高');
      expect(unit.getValue('abc')).toBeUndefined();
      expect(unit.getValue('')).toBe(undefined);
      expect(unit.getValues().length).toBe(5);
      expect(unit.getValueWithSymbol(5.18)).toBe('无');
      expect(unit.getValueWithSymbol('100.18')).toBe('很高');
      expect(unit.getValueWithSymbol('abc')).toBe('-');
      expect(unit.getYAxisValues()).toEqual([10, 40, 60, 80]);
    }
  });

  it('SystemUnits getUnitFormat', () => {
    const unitsJson = {
      AREA: areaUnitCase,
      FLOW: flowUnitCase,
      DATE: dateUnitCase,
      RELIABILITY: reliabilityUnitCase,
      INUNDATION: rangeUnitCase,
    };

    registerSystemUnits(unitsJson);

    const notExistingUnit = getUnitFormat('NOT_EXISTING');
    expect(notExistingUnit).toBeUndefined();
    expect(notExistingUnit?.unitType).toBeUndefined();

    const areaUnit = getUnitFormat('AREA');
    expect(areaUnit?.unitType).toBe('D');
    expect(areaUnit?.getValue(12.34)).toBe(12.3);

    const dateUnit = getUnitFormat('DATE');
    expect(dateUnit?.unitType).toBe('T');
    expect(dateUnit?.getValue('2022-08-01 08:00:00')).toBe('2022-08-01');

    const reliabilityUnit = getUnitFormat('RELIABILITY');
    expect(reliabilityUnit?.unitType).toBe('E');
    expect(reliabilityUnit?.getValue('4')).toBe('优');

    const inundationUnit = getUnitFormat('INUNDATION');
    expect(inundationUnit?.unitType).toBe('R');
    expect(inundationUnit?.getValue(60)).toBe('高');

    expect(getUnitValue('NOT_EXISTING', 12.34)).toBe(12.34);
    expect(getUnitValue('NOT_EXISTING', '12.34')).toBe('12.34');
    expect(getUnitValue('AREA', 12.34)).toBe(12.3);
    expect(getUnitValue('AREA', '12.34')).toBe(12.3);
    expect(getUnitValue('AREA', '12.34 m')).toBeUndefined();
    expect(getUnitValue('DATE', '2022-08-01 08:00:00')).toBe('2022-08-01');
    expect(getUnitValue('RELIABILITY', '4')).toBe('优');
    expect(getUnitValue('INUNDATION', 60)).toBe('高');
    expect(getUnitValues('INUNDATION')?.length).toBe(5);
    expect(getUnitValues('')).toBeUndefined();
  });

  it('SystemUnits getUnitValueWithSymbol', () => {
    const unitsJson = {
      AREA: areaUnitCase,
    };

    registerSystemUnits(unitsJson);

    expect(getUnitValueWithSymbol('AREA', 123)).toBe('123 m²');
    expect(getUnitValueWithSymbol('AREA', '123')).toBe('123 m²');
    expect(getUnitValueWithSymbol('AREA', undefined)).toBe('-');
    expect(getUnitValueWithSymbol('AREA', null)).toBe('-');
    expect(getUnitValueWithSymbol('unknown', 123)).toBe('123');
  });
});
