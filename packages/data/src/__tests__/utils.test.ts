/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import toObject, {
  compareNumbers,
  convertToSpecificDate,
  dateSorter,
  formatNumber,
  getMinMax,
  getTimeRangesOverlap,
  isBetweenRange,
  isTimeBetween,
  isTimeRangesOverlap,
} from '../utils';

describe('utils - toObject', () => {
  it('toObject - not a Map', () => {
    const dataResult = {};
    expect(toObject(undefined)).toEqual(dataResult);
  });

  it('toObject - Map<string, string>', () => {
    const data: Map<string, string> = new Map();
    data.set('key', 'value');
    const dataResult = { key: 'value' };
    expect(toObject(data)).toEqual(dataResult);
  });

  it('toObject - Map<string, Map<string, string>>', () => {
    const data: Map<string, Map<string, string>> = new Map();
    const dataValue: Map<string, string> = new Map();
    dataValue.set('key2', 'value');
    data.set('key1', dataValue);
    const dataResult = { key1: { key2: 'value' } };
    expect(toObject(data)).toEqual(dataResult);
  });

  it('toObject - Map<string, Array<string>>', () => {
    const data: Map<string, Array<string>> = new Map();
    const dataValue: Array<string> = ['value1', 'value2'];
    data.set('key1', dataValue);
    const dataResult = { key1: ['value1', 'value2'] };
    expect(toObject(data)).toEqual(dataResult);
  });
});

describe('formatNumber', () => {
  it('should return rounded number when valid number is provided', () => {
    const result = formatNumber(123.4567, 2);
    expect(result).toBe(123.46);
  });

  it('should return 0 when NaN is provided', () => {
    const result = formatNumber(NaN, 2);
    expect(result).toBe(0);
  });

  it('should return 0 when null is provided', () => {
    const result = formatNumber(null, 2);
    expect(result).toBe(0);
  });

  it('should return 0 when undefined is provided', () => {
    const result = formatNumber(undefined, 2);
    expect(result).toBe(0);
  });

  it('should return 0 when Infinity is provided', () => {
    const result = formatNumber(Infinity, 2);
    expect(result).toBe(0);
  });

  it('should return 0 when -Infinity is provided', () => {
    const result = formatNumber(-Infinity, 2);
    expect(result).toBe(0);
  });

  it('should return rounded number when string number is provided', () => {
    const result = formatNumber('123.4567', 2);
    expect(result).toBe(123.46);
  });

  it('should return 0 when non-number string is provided', () => {
    const result = formatNumber('abc', 2);
    expect(result).toBe(0);
  });

  it('should return 0 when number is decimal', () => {
    const result = formatNumber(0.003, 2);
    expect(result).toBe(0);
  });

  it('should return rounded number when number is decimal', () => {
    const result = formatNumber(0.005, 2);
    expect(result).toBe(0.01);
  });

  it('should return rounded number when number is decimal', () => {
    const result = formatNumber(-0.005, 2);
    expect(result).toBe(-0);
  });

  it('should return rounded number when number is negative', () => {
    const result = formatNumber(-100.333, 2);
    expect(result).toBe(-100.33);
  });
});

describe('isTimeBetween', () => {
  it('should return true if time between the range', () => {
    expect(
      isTimeBetween(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        '2023-05-08 11:00:00',
      ),
    ).toBe(true);
  });

  it('should return false if range not between the range', () => {
    expect(
      isTimeBetween(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        '2023-05-08 09:00:00',
      ),
    ).toBe(false);
  });

  it('should return true if time equals the start time of range', () => {
    expect(
      isTimeBetween(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        '2023-05-08 10:00:00',
      ),
    ).toBe(true);
  });

  it('should return true if time equals the end time of the range', () => {
    expect(
      isTimeBetween(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        '2023-05-08 12:00:00',
      ),
    ).toBe(true);
  });

  it('should return false if time is invalid', () => {
    expect(
      isTimeBetween(['2023-05-08 10:00:00', '2023-05-10 12:00:00'], '1243'),
    ).toBe(false);
  });
});

describe('isTimeRangesOverlap', () => {
  it('should return true if two time ranges overlap', () => {
    expect(
      isTimeRangesOverlap(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        ['2023-05-08 11:00:00', '2023-05-08 13:00:00'],
      ),
    ).toBe(true);
  });

  it('should return false if two time ranges do not overlap', () => {
    expect(
      isTimeRangesOverlap(
        ['2023-05-08 10:00:00', '2023-05-08 12:00:00'],
        ['2023-05-08 13:00:00', '2023-05-08 14:00:00'],
      ),
    ).toBe(false);
  });

  it('should return true if range1 includes range2', () => {
    expect(
      isTimeRangesOverlap(
        ['2023-05-08 10:00:00', '2023-05-08 14:00:00'],
        ['2023-05-08 11:00:00', '2023-05-08 13:00:00'],
      ),
    ).toBe(true);
  });

  it('should return true if range2 includes range1', () => {
    expect(
      isTimeRangesOverlap(
        ['2023-05-08 11:00:00', '2023-05-08 13:00:00'],
        ['2023-05-08 10:00:00', '2023-05-08 14:00:00'],
      ),
    ).toBe(true);
  });

  it('should return true if the end time of the range1 is empty string, but the start time of the range1 is between range2', () => {
    expect(
      isTimeRangesOverlap(
        ['2023-05-08 11:00:00', ''],
        ['2023-05-08 10:00:00', '2023-05-08 14:00:00'],
      ),
    ).toBe(true);
  });

  it('should return true if the range is equal the other', () => {
    const range1: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 08:00:00'),
      dayjs('2022-01-01 08:00:00'),
    ];
    const range2: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 08:00:00'),
      dayjs('2022-01-01 08:00:00'),
    ];

    expect(isTimeRangesOverlap(range1, range2)).toBe(true);
  });
});

describe('compareNumbers function', () => {
  it('should correctly compare two numbers', () => {
    // Test cases for comparing numbers
    expect(compareNumbers(3, 1)).toBe(2); // 3 - 1 = 2
    expect(compareNumbers(1, 3)).toBe(-2); // 1 - 3 = -2
    expect(compareNumbers(5, 5)).toBe(0); // 5 - 5 = 0
  });

  it('should handle undefined or null values', () => {
    // Test cases for handling undefined or null values
    expect(compareNumbers(3, null)).toBe(4); // 3 - (-1) = 4
    expect(compareNumbers(null, 1)).toBe(-2); // (-1) - 1 = -2
    expect(compareNumbers(undefined, undefined)).toBe(0); // (-1) - (-1) = 0
  });

  it('should treat undefined and null as equivalent', () => {
    // Test cases for treating undefined and null as equivalent
    expect(compareNumbers(null, undefined)).toBe(0); // (-1) - (-1) = 0
    expect(compareNumbers(undefined, null)).toBe(0); // (-1) - (-1) = 0
  });
});

describe('isBetweenRange function', () => {
  test('should return true for values within the range', () => {
    expect(isBetweenRange(5, 1, 10)).toBe(true);
  });

  test('should return true for values equal to the minimum', () => {
    expect(isBetweenRange(1, 1, 10)).toBe(true);
  });

  test('should return true for values equal to the maximum', () => {
    expect(isBetweenRange(10, 1, 10)).toBe(true);
  });

  test('should return false for values below the range', () => {
    expect(isBetweenRange(0, 1, 10)).toBe(false);
  });

  test('should return false for values above the range', () => {
    expect(isBetweenRange(15, 1, 10)).toBe(false);
  });

  test('should use default minimum and maximum if not provided', () => {
    expect(isBetweenRange(5)).toBe(true);
    expect(isBetweenRange(-1000)).toBe(true);
    expect(isBetweenRange(1000)).toBe(true);
    expect(isBetweenRange(1000, 999)).toBe(true);
    expect(isBetweenRange(1000, 1001)).toBe(false);
    expect(isBetweenRange(1000, undefined, 999)).toBe(false);
    expect(isBetweenRange(1000, undefined, 1001)).toBe(true);
  });
});

describe('getTimeRangesOverlap', () => {
  it('should return undefined if there is no overlap', () => {
    const range1: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 00:00:00'),
      dayjs('2022-01-01 02:00:00'),
    ];
    const range2: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-03 00:00:00'),
      dayjs('2022-01-03 02:00:00'),
    ];

    const result = getTimeRangesOverlap(range1, range2);

    expect(result).toBeUndefined();
  });

  it('should return the overlapping time range', () => {
    const range1: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 01:30:00'),
      dayjs('2022-01-02 06:59:00'),
    ];
    const range2: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 02:31:00'),
      dayjs('2022-01-03 00:00:00'),
    ];

    const result = getTimeRangesOverlap(range1, range2);

    expect(result).toEqual([
      dayjs('2022-01-01 02:31:00'),
      dayjs('2022-01-02 06:59:00'),
    ]);
  });

  it('should return the full range if one range is completely within the other', () => {
    const range1: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 10:00:00'),
      dayjs('2022-01-02 00:00:00'),
    ];
    const range2: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 00:00:00'),
      dayjs('2022-01-03 00:00:00'),
    ];

    const result = getTimeRangesOverlap(range1, range2);

    expect(result).toEqual([
      dayjs('2022-01-01 10:00:00'),
      dayjs('2022-01-02 00:00:00'),
    ]);
  });

  it('should return the range if range is equal the other', () => {
    const range1: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 08:00:00'),
      dayjs('2022-01-01 08:00:00'),
    ];
    const range2: [string | Dayjs, string | Dayjs] = [
      dayjs('2022-01-01 08:00:00'),
      dayjs('2022-01-01 08:00:00'),
    ];

    const result = getTimeRangesOverlap(range1, range2);

    expect(result).toEqual([
      dayjs('2022-01-01 08:00:00'),
      dayjs('2022-01-01 08:00:00'),
    ]);
  });
});

describe('convertToSpecificDate', () => {
  it('should convert a string time to a specific date string', () => {
    const time = '2023-03-30 10:30:00';
    const specificDate = dayjs('2023-04-01');
    const result = convertToSpecificDate(time, specificDate);
    expect(result).toBe('2023-04-01 10:30:00');
  });

  it('should convert a Dayjs time to a specific date', () => {
    const time = dayjs('2023-03-30 10:30:00');
    const specificDate = '2023-04-01';
    const result = convertToSpecificDate(time, specificDate);
    expect(result).toEqual(dayjs('2023-04-01 10:30:00'));
  });
});

describe('dateSorter', () => {
  it('should return 0 when both dates are null', () => {
    expect(dateSorter(null, null)).toBe(0);
  });

  it('should return 0 when both dates are undefined', () => {
    expect(dateSorter(undefined, undefined)).toBe(0);
  });

  it('should return 1 when first date is not null and second date is null', () => {
    expect(dateSorter('2023-01-01 12:00:00', null)).toBe(1);
  });

  it('should return 1 when first date is not undefined and second date is undefined', () => {
    expect(dateSorter('2023-01-01 12:00:00', undefined)).toBe(1);
  });

  it('should return -1 when first date is null and second date is not null', () => {
    expect(dateSorter(null, '2023-01-01 12:00:00')).toBe(-1);
  });

  it('should return -1 when first date is undefined and second date is not undefined', () => {
    expect(dateSorter(undefined, '2023-01-01 12:00:00')).toBe(-1);
  });

  it('should return -1 when first date is before second date', () => {
    expect(dateSorter('2023-01-01 12:00:00', '2023-01-02 12:00:00')).toBe(-1);
  });

  it('should return 1 when first date is after second date', () => {
    expect(dateSorter('2023-01-02 12:00:00', '2023-01-01 12:00:00')).toBe(1);
  });

  it('should return 0 when both dates are the same', () => {
    expect(dateSorter('2023-01-01 12:00:00', '2023-01-01 12:00:00')).toBe(0);
  });
});

describe('getMinMax', () => {
  it('should return correct min and max for positive numbers', () => {
    const data = [1, 5, 3, 9, 2];
    const result = getMinMax(data);
    expect(result).toEqual({ min: 1, max: 9 });
  });

  it('should return correct min and max for negative numbers', () => {
    const data = [-5, -1, -10, -3];
    const result = getMinMax(data);
    expect(result).toEqual({ min: -10, max: -1 });
  });

  it('should return correct min and max for mixed positive and negative numbers', () => {
    const data = [-5, 10, -2, 8, 0];
    const result = getMinMax(data);
    expect(result).toEqual({ min: -5, max: 10 });
  });

  it('should handle array with single element', () => {
    const data = [42];
    const result = getMinMax(data);
    expect(result).toEqual({ min: 42, max: 42 });
  });

  it('should handle array with duplicate values', () => {
    const data = [5, 5, 5, 5];
    const result = getMinMax(data);
    expect(result).toEqual({ min: 5, max: 5 });
  });

  it('should handle decimal numbers', () => {
    const data = [1.5, 2.3, 0.8, 3.7];
    const result = getMinMax(data);
    expect(result).toEqual({ min: 0.8, max: 3.7 });
  });

  it('should handle large arrays efficiently', () => {
    // 创建一个大数组来测试性能
    const data = Array.from({ length: 100000 }, () => Math.random() * 1000);
    const expectedMin = -100000;
    const expectedMax = 100000;
    data.push(expectedMin);
    data.push(expectedMax);
    const result = getMinMax(data);

    expect(result.min).toBe(expectedMin);
    expect(result.max).toBe(expectedMax);
  });

  it('should handle zero values', () => {
    const data = [0, -1, 1, 0];
    const result = getMinMax(data);
    expect(result).toEqual({ min: -1, max: 1 });
  });

  it('should handle very large numbers', () => {
    const data = [Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, 0];
    const result = getMinMax(data);
    expect(result).toEqual({
      min: Number.MIN_SAFE_INTEGER,
      max: Number.MAX_SAFE_INTEGER,
    });
  });
});
