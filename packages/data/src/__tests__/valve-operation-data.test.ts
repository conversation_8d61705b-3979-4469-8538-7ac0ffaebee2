/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  mockValveOpenCurveData,
  mockValveOperationGroupData1,
  mockValveOperationGroupData2,
} from '../mock/mock-valve';
import {
  containsShapeInValveGroup,
  displayDuration,
  formatSdval,
  getValveLoseCoeff,
  getValveOperationGroupTitle,
  parseValveOperationGroups,
  VALVE_CLOSED,
  VALVE_OPEN,
  ValveOperationGroup,
} from '../valve-manager/valve-manager-data';

describe('parse valve operation groups', () => {
  it('parse data', () => {
    const groups: ValveOperationGroup[] = parseValveOperationGroups(
      mockValveOperationGroupData1,
    );
    expect(groups.length).toBe(3);
  });
});

describe('valve - formatSdval', () => {
  it('parse data', () => {
    const open = formatSdval(VALVE_OPEN);
    const closed = formatSdval(VALVE_CLOSED);
    const setting = formatSdval(80);
    const empty = formatSdval('');
    expect(open).toBe('打开');
    expect(closed).toBe('关闭');
    expect(setting).toBe(80);
    expect(empty).toBe('');
  });
});

describe('valve - displayDuration', () => {
  it('displayDuration', () => {
    expect(displayDuration(0)).toBe('单时刻');
    expect(displayDuration(1440)).toBe('1天');
    expect(displayDuration(1502)).toBe('1天1小时2分钟');
    expect(displayDuration(18)).toBe('18分钟');
  });
});

describe('valve - getValveOperationGroupTitle', () => {
  it('getValveOperationGroupTitle', () => {
    const groups: ValveOperationGroup[] = parseValveOperationGroups(
      mockValveOperationGroupData2,
    );
    expect(getValveOperationGroupTitle(groups[0])).toBe(
      '鳌峄大桥10号灯杆附近机动车道上; 闭水试验; 闭水试验结束',
    );
    expect(getValveOperationGroupTitle(groups[4])).toBe(
      '操作阀门 F33010512, F330105121',
    );
    expect(getValveOperationGroupTitle(groups[5])).toBe('打开阀门 F334158');
  });
});

describe('valve - containsShapeInValveGroup', () => {
  it('containsShapeInValveGroup', () => {
    const groups: ValveOperationGroup[] = parseValveOperationGroups(
      mockValveOperationGroupData2,
    );
    expect(containsShapeInValveGroup(groups[0])).toBe(true);
    expect(containsShapeInValveGroup(groups[1])).toBe(true);
    expect(containsShapeInValveGroup(groups[4])).toBe(false);
  });
});

describe('getValveLoseCoeff function', () => {
  test('should return exact values for known x', () => {
    expect(getValveLoseCoeff(0, mockValveOpenCurveData)).toBe(12000);
    expect(getValveLoseCoeff(5, mockValveOpenCurveData)).toBe(9000);
    expect(getValveLoseCoeff(10, mockValveOpenCurveData)).toBe(6000);
    expect(getValveLoseCoeff(100, mockValveOpenCurveData)).toBe(0);
  });

  test('should correctly interpolate values between known x', () => {
    expect(getValveLoseCoeff(2.5, mockValveOpenCurveData)).toBeCloseTo(10500); // (12000+9000)/2
    expect(getValveLoseCoeff(7.5, mockValveOpenCurveData)).toBeCloseTo(7500); // (9000+6000)/2
    expect(getValveLoseCoeff(12, mockValveOpenCurveData)).toBeCloseTo(5200); // Between x=10 and x=15
    expect(getValveLoseCoeff(85, mockValveOpenCurveData)).toBeCloseTo(30); // Between x=80 and x=90
  });

  test('should return boundary values for out-of-range x', () => {
    expect(getValveLoseCoeff(-10, mockValveOpenCurveData)).toBe(12000);
    expect(getValveLoseCoeff(110, mockValveOpenCurveData)).toBe(0);
  });

  test('should return null for empty data', () => {
    expect(getValveLoseCoeff(0, [])).toBeNull();
  });
});
