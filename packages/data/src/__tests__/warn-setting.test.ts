/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  convertDataTypeToObject,
  convertObjectToDataType,
} from '../warn-setting';

// 正常数据
const mockWarnSettingOriginDataType = {
  key1: { title: 'Title1', value: 'Value1' },
  key2: { title: 'Title2', value: 'Value2' },
};

const mockDataTypeArray = [
  { key: 'key1', id: 'key1', name: 'Title1', value: 'Value1' },
  { key: 'key2', id: 'key2', name: 'Title2', value: 'Value2' },
];

// 包含数值的数据
const mockWarnSettingOriginDataTypeWithNumbers = {
  key1: { title: 'Title1', value: 42 },
  key2: { title: 'Title2', value: 3.14 },
};

const mockDataTypeArrayWithNumbers = [
  { key: 'key1', id: 'key1', name: 'Title1', value: 42 },
  { key: 'key2', id: 'key2', name: 'Title2', value: 3.14 },
];

// 空数据
const mockEmptyWarnSettingOriginDataType = {};
const mockEmptyDataTypeArray: [] = [];

describe('convertObjectToDataType', () => {
  it('should correctly convert WarnSettingOriginDataType to DataType array', () => {
    const result = convertObjectToDataType(mockWarnSettingOriginDataType);
    expect(result).toEqual(mockDataTypeArray);
  });

  it('should handle numbers correctly', () => {
    const result = convertObjectToDataType(
      mockWarnSettingOriginDataTypeWithNumbers,
    );
    expect(result).toEqual(mockDataTypeArrayWithNumbers);
  });

  it('should return an empty array when given an empty object', () => {
    const result = convertObjectToDataType(mockEmptyWarnSettingOriginDataType);
    expect(result).toEqual(mockEmptyDataTypeArray);
  });
});

describe('convertDataTypeToObject', () => {
  it('should correctly convert DataType array to WarnSettingOriginDataType', () => {
    const result = convertDataTypeToObject(mockDataTypeArray);
    expect(result).toEqual(mockWarnSettingOriginDataType);
  });

  it('should handle numbers correctly', () => {
    const result = convertDataTypeToObject(mockDataTypeArrayWithNumbers);
    expect(result).toEqual(mockWarnSettingOriginDataTypeWithNumbers);
  });

  it('should return an empty object when given an empty array', () => {
    const result = convertDataTypeToObject(mockEmptyDataTypeArray);
    expect(result).toEqual(mockEmptyWarnSettingOriginDataType);
  });
});
