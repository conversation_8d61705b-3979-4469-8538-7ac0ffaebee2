/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeviceWarningType,
  formatDevicesWarns,
  getDurationTime,
  getShelveValue,
  reorderGroupIndexes,
  WarnConfirmStatus,
  WarnDetail,
} from '../warn';

describe('getShelveValue', () => {
  it('should return undefined when dealWithTime is not provided', () => {
    expect(getShelveValue()).toBeUndefined();
  });

  it('should return the difference in minutes between shelveTime and dealWithTime', () => {
    const dealWithTime = '2023-09-04 12:00:00';
    const shelveTime = '2023-09-04 13:30:00';
    expect(getShelveValue(dealWithTime, shelveTime)).toBe(90);
  });
});

describe('formatDevicesWarns', () => {
  it('should format devices warns', () => {
    const originalData: DeviceWarningType = {
      device1: [
        {
          id: 'warn1',
          warnId: 'w1',
          deviceId: 'device1',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        } as WarnDetail,
      ],
    };

    const warnDetailList: WarnDetail[] = [
      {
        id: 'warn2',
        warnId: 'w2',
        deviceId: 'device1',
        confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
      } as WarnDetail,
      {
        id: 'warn3',
        warnId: 'w3',
        deviceId: 'device2',
        confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
      } as WarnDetail,
    ];

    const result = formatDevicesWarns(originalData, warnDetailList);

    expect(result).toEqual({
      device1: [
        {
          id: 'warn1',
          warnId: 'w1',
          deviceId: 'device1',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        },
        {
          id: 'warn2',
          warnId: 'w2',
          deviceId: 'device1',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        },
      ],
      device2: [
        {
          id: 'warn3',
          warnId: 'w3',
          deviceId: 'device2',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        },
      ],
    });
  });

  it('should handle empty original data', () => {
    const originalData: DeviceWarningType = {};
    const warnDetailList: WarnDetail[] = [
      {
        id: 'warn1',
        warnId: 'w1',
        deviceId: 'device1',
        confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
      } as WarnDetail,
    ];

    const result = formatDevicesWarns(originalData, warnDetailList);

    expect(result).toEqual({
      device1: [
        {
          id: 'warn1',
          warnId: 'w1',
          deviceId: 'device1',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        },
      ],
    });
  });

  it('should handle empty warn detail list', () => {
    const originalData: DeviceWarningType = {
      device1: [
        {
          id: 'warn1',
          warnId: 'w1',
          deviceId: 'device1',
          confirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        } as WarnDetail,
      ],
    };
    const warnDetailList: WarnDetail[] = [];

    const result = formatDevicesWarns(originalData, warnDetailList);

    expect(result).toEqual(originalData);
  });
});

describe('reorderGroupIndexes', () => {
  it('cases', () => {
    const originalArray1: number[] = [
      3, 1, 1, 1, 1, 2, 3, 3, 3, 4, 4, 5, 6, 6, 6, 6, 7, 7, 8, 8, 8, 8, 8, 9,
    ];
    const transformedArray1: number[] = reorderGroupIndexes(originalArray1);
    expect(transformedArray1).toEqual([
      0, 1, 1, 1, 1, 0, 1, 1, 1, 2, 2, 0, 1, 1, 1, 1, 2, 2, 3, 3, 3, 3, 3, 0,
    ]);

    const originalArray2: number[] = [
      1, 1, 1, 1, 2, 3, 3, 3, 4, 4, 5, 6, 6, 6, 6, 7, 7, 8, 8, 8, 8, 8, 9, 9,
    ];
    const transformedArray2: number[] = reorderGroupIndexes(originalArray2);
    expect(transformedArray2).toEqual([
      1, 1, 1, 1, 0, 1, 1, 1, 2, 2, 0, 1, 1, 1, 1, 2, 2, 3, 3, 3, 3, 3, 4, 4,
    ]);

    const originalArray3: number[] = [3];
    const transformedArray3: number[] = reorderGroupIndexes(originalArray3);
    expect(transformedArray3).toEqual([0]);

    const originalArray4: number[] = [3, 1];
    const transformedArray4: number[] = reorderGroupIndexes(originalArray4);
    expect(transformedArray4).toEqual([0, 0]);

    const originalArray5: number[] = [1, 1];
    const transformedArray5: number[] = reorderGroupIndexes(originalArray5);
    expect(transformedArray5).toEqual([1, 1]);

    const originalArray6: number[] = [];
    const transformedArray6: number[] = reorderGroupIndexes(originalArray6);
    expect(transformedArray6).toEqual([]);
  });
});

describe('getDurationTime', () => {
  it('getDurationTime', () => {
    expect(
      getDurationTime('2024-01-01 01:00:00', '2024-01-01 01:00:00'),
    ).toEqual([0, '']);
    expect(
      getDurationTime('2024-01-01 01:00:00', '2024-01-01 01:00:18'),
    ).toEqual([18000, '18秒钟']);
    expect(
      getDurationTime('2024-01-01 01:00:00', '2024-01-01 01:05:18'),
    ).toEqual([318000, '5分钟']);
    expect(
      getDurationTime('2024-01-01 01:00:00', '2024-01-01 02:05:18'),
    ).toEqual([3918000, '1小时5分钟']);
    expect(
      getDurationTime('2024-01-01 01:00:00', '2024-01-02 02:05:18'),
    ).toEqual([90318000, '25小时5分钟']);

    expect(getDurationTime(undefined, '2024-01-01 02:05:18')).toEqual([
      null,
      '-',
    ]);
    expect(getDurationTime('2024-01-01 01:00:00', undefined)).toEqual([
      null,
      '-',
    ]);
  });
});
