/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getWeekdayName } from '../weather-data';

describe('getWeekdayName', () => {
  it('should return "今天" for today\'s date', () => {
    const today = new Date().toISOString().split('T')[0];
    expect(getWeekdayName(today)).toBe('今天');
  });

  it('should return the correct weekday name for a given date', () => {
    const testDates = ['20230605', '2023-06-06 00:00:00', '2023-06-07'];

    const expectedWeekdayNames = ['周一', '周二', '周三'];

    testDates.forEach((date, index) => {
      expect(getWeekdayName(date)).toBe(expectedWeekdayNames[index]);
    });
  });

  it('should return "" for Invalid Date', () => {
    expect(getWeekdayName('Invalid Date')).toBe('');
  });
});
