/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface AlertLevelParameter {
  [key: string]: {
    title: string; // 参数标题
    value: string | number; // 参数值
  };
}

export interface AlertLevel {
  level: number; // 级别
  icon: string; // 图标
  color: string; // 级别颜色
  signal: string; // 级别名称
  description: string; // 级别描述
  parameter: AlertLevelParameter; // 级别参数
}

export interface AlertDefinition {
  id: string; // 定义ID
  title: string; // 预警名称
  description: string; // 预警描述
  category: string; // 预警类别
  status: number; // 预警状态
  levels: AlertLevel[]; // 预警级别列表
  source?: string; // 预警来源
  remark?: string; // 预警备注
}

export interface AlertInstance {
  id: string; // 实例ID
  definitionId: string; // 定义ID
  definitionTitle: string; // 定义名称
  definitionCategory: string; // 定义类别
  level: number; // 级别
  content: string; // 内容
  startTime: string; // 开始时间
  endTime: string; // 结束时间
  createTime: string; // 创建时间
  ptype: string; // 设备otype
  pname: string; // 设备oname
  ptitle: string; // 设备名称
  stime: string; // 入库时间
  remark?: string; // 备注
  source?: string; // 来源
}

export type AlertInstanceList = AlertInstance[];

export const getAlertLevelParam = (
  alert: AlertInstance,
  definitions: AlertDefinition[],
) => {
  const alertDefinition = definitions.find(
    (def) => def.id === alert.definitionId,
  );
  return alertDefinition?.levels.find((level) => level.level === alert.level);
};
