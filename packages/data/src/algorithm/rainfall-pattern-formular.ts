/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export default class RainfallPatternFormula {
  consts_A: number = 2989.3; // 常数，重现期1年的设计降雨雨量

  consts_C: number = 0.671; // – 雨量变动参数

  consts_b: number = 13.3; // 常数, 降水历时修正参数

  consts_n: number = 0.8; // 常数, 暴雨衰竭指数

  consts_P: number = 2.0; // 常数, 重现期(年)

  consts_r: number = 0.4; // 雨峰系数

  static convert_Rainfall_To_q(rainfall: number, minutes: number): number {
    // 把一定时间内(单位：分钟）的降雨量（单位：毫米）转换成 暴雨强度 （单位：升 / 秒·公顷）
    return (1000.0 * rainfall) / (minutes * 6.0);
  }

  calculate_P_by_q(q: number, t: number): number {
    // – 重现期，年
    // P = 10 ^ ((q * (t + b)^n / A - 1) / C)
    const tbn: number = (t + this.consts_b) ** this.consts_n;
    const power: number =
      (q * tbn - this.consts_A) / (this.consts_A * this.consts_C);
    return 10 ** power;
  }

  calculate_P_by_rainfall(rainfall: number, minutes: number): number {
    const q: number = RainfallPatternFormula.convert_Rainfall_To_q(
      rainfall,
      minutes,
    );
    return this.calculate_P_by_q(q, minutes);
  }

  static convert_q_to_Rainfall(q: number): number {
    // 把暴雨强度 （单位：升 / 秒·公顷）转换成 单位分钟内的降雨量（单位：毫米）
    return q * 0.006;
  }

  calculateRainfall(
    rainfall: number,
    minutes: number,
    step: number,
  ): Array<number> {
    const P: number = this.calculate_P_by_rainfall(rainfall, minutes);
    const A = this.consts_A * (1 + this.consts_C * Math.log10(P));
    const listRainfalls: Array<number> = [];
    const peakTime: number = this.consts_r * minutes;
    let ti: number = step;
    while (ti <= minutes) {
      if (ti <= peakTime) {
        const tempRainfall: number =
          RainfallPatternFormula.calculateRainfallBeforePeak(
            A,
            minutes,
            this.consts_b,
            this.consts_n,
            this.consts_r,
            ti,
          );
        listRainfalls.push(
          RainfallPatternFormula.convert_q_to_Rainfall(tempRainfall),
        );
      } else {
        const tempRainfall: number =
          RainfallPatternFormula.calculateRainfallAfterPeak(
            A,
            minutes,
            this.consts_b,
            this.consts_n,
            this.consts_r,
            ti,
          );
        listRainfalls.push(
          RainfallPatternFormula.convert_q_to_Rainfall(tempRainfall),
        );
      }

      ti += step;
    }

    return listRainfalls;
  }

  static calculateRainfallBeforePeak(
    A: number,
    t0: number,
    b: number,
    n: number,
    r: number,
    ti: number,
  ): number {
    const numerator: number = ((1 - n) * (r * t0 - ti)) / r + b;
    const denominator: number = ((t0 * r - ti) / r + b) ** (1 + n);
    return (A * numerator) / denominator;
  }

  static calculateRainfallAfterPeak(
    A: number,
    t0: number,
    b: number,
    n: number,
    r: number,
    ti: number,
  ): number {
    const numerator: number = ((1 - n) * (-r * t0 + ti)) / (1 - r) + b;
    const denominator: number = ((-t0 * r + ti) / (1 - r) + b) ** (1 + n);
    return (A * numerator) / denominator;
  }
}
