/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { makeObjectId } from './object-item';
import { getUnitFormat, UnitFormat } from './unit-system';

interface ObjectItem {
  otype: string;
  /** 如果不配置默认取 otype title */
  title?: string;
  /** 配置对象的查询条件: vprop */
  vprops: string[];
  /** 期望的返回结果的有哪些字段的配置 */
  resultVprops: string[];
}

export interface BatchQuery {
  /** 查询对象 */
  objects: ObjectItem[];
}

export type BatchQueryVpropValue = number | string | boolean | null;
export type BatchQueryVpropValues = {
  [index: string]: BatchQueryVpropValue;
};

export interface BatchQueryListItem {
  otype: string;
  oname: string;
  shape: string;
  values: BatchQueryVpropValues;
}

export enum WhereTypeNumber {
  /** 大于 */
  GreaterThan = '>',
  /** 大于等于 */
  GreaterThanOrEqual = '>=',
  /** 小于 */
  LessThan = '<',
  /** 小于等于 */
  LessThanOrEqual = '<=',
  /** 等于 */
  Equal = '=',
}

export enum WhereTypeString {
  /** 包含 */
  Contains = 'contains',
}

export type WhereType = WhereTypeNumber | WhereTypeString;

export interface BatchQueryWhere {
  vprop: string;
  /** default: WhereType.Equal */
  type: WhereType;
  value: string | number;
}

export interface BatchQueryParams {
  /** 不传代表全局查询 */
  wkt?: string;
  /** 查询对象otype */
  otype: string;
  /** 查询条件 */
  where: BatchQueryWhere[];
  /** 返回结果 */
  resultVprops: string[];
  /** 分页信息 */
  current: number;
  pageSize: number;
}

export const getBatchQueryWhereTypeName = (type: WhereType): string => {
  switch (type) {
    case WhereTypeString.Contains:
      return '包含';
    case WhereTypeNumber.GreaterThan:
      return '大于';
    case WhereTypeNumber.GreaterThanOrEqual:
      return '大于等于';
    case WhereTypeNumber.LessThan:
      return '小于';
    case WhereTypeNumber.LessThanOrEqual:
      return '小于等于';
    case WhereTypeNumber.Equal:
      return '等于';
    default:
      return type;
  }
};

export const whereTypeNumberOptions: {
  label: string;
  value: WhereTypeNumber;
}[] = Object.values(WhereTypeNumber).map((key) => ({
  label: getBatchQueryWhereTypeName(key),
  value: key,
}));

export const whereTypeStringOptions: { label: string; value: WhereType }[] =
  Object.values(WhereTypeString).map((key) => ({
    label: getBatchQueryWhereTypeName(key),
    value: key,
  }));

export class BatchQueryCollection {
  private static _instance: BatchQueryCollection;

  static getInstance(): BatchQueryCollection {
    if (!BatchQueryCollection._instance) {
      BatchQueryCollection._instance = new BatchQueryCollection();
    }
    return BatchQueryCollection._instance;
  }

  // Map<otype, { label: string; value: string }[]>
  private _objectsOptions: { label: string; value: string }[] = [];

  // Map<otype, typeof this._objectsOptions>
  private _vpropsOptionsMap: Map<string, typeof this._objectsOptions> =
    new Map();

  // Map<otype, { title: string; key: string }[]>
  private _resultVpropsColumnsMap: Map<
    string,
    { title: string; key: string }[]
  > = new Map();

  // Map<otype@vprop, UnitFormat>
  private _unitInfo: Map<string, UnitFormat> = new Map();

  initializeData(batchQuery: BatchQuery, db: Database) {
    const { objects = [] } = batchQuery;

    this._objectsOptions = [];
    this._vpropsOptionsMap = new Map();
    this._resultVpropsColumnsMap = new Map();
    this._unitInfo = new Map();

    objects.forEach(({ otype, title, vprops, resultVprops }) => {
      const label = title ?? db.getPropertyInfo(otype)?.title;
      this._objectsOptions.push({
        value: otype,
        label: label ?? otype,
      });

      const vpropsOptions: typeof this._objectsOptions = [];
      vprops.forEach((vprop) => {
        // initialize unitInfo
        const unitKey = db.getPropertyInfo(otype)?.getPropertyUnit(vprop);
        const unit = getUnitFormat(unitKey as string);
        if (unit) this._unitInfo.set(makeObjectId(otype, vprop), unit);

        // get vprops option
        vpropsOptions.push({
          label: db.getPropertyInfo(otype)?.getPropertyTitle(vprop) ?? vprop,
          value: vprop,
        });
      });

      this._vpropsOptionsMap.set(otype, vpropsOptions);

      const resultColumns = resultVprops.map((vprop) => ({
        title: db.getPropertyInfo(otype)?.getPropertyTitle(vprop) ?? vprop,
        key: vprop,
      }));
      this._resultVpropsColumnsMap.set(otype, resultColumns);
    });
  }

  getVpropsOptions(
    otype: string,
  ): { label: string; value: string }[] | undefined {
    return this._vpropsOptionsMap.get(otype);
  }

  getResultColumns(
    otype: string,
  ): { title: string; key: string }[] | undefined {
    return this._resultVpropsColumnsMap.get(otype);
  }

  getVpropUnit(otype: string, vprop: string): UnitFormat | undefined {
    return this._unitInfo.get(makeObjectId(otype, vprop));
  }

  get objectsOptions() {
    return this._objectsOptions;
  }
}

export const getObjectsOptions = (): { label: string; value: string }[] =>
  BatchQueryCollection.getInstance().objectsOptions;

export const getVpropsOptions = (
  otype: string,
): { label: string; value: string }[] | undefined =>
  BatchQueryCollection.getInstance().getVpropsOptions(otype);

export const getResultColumns = (
  otype: string,
): { title: string; key: string }[] | undefined =>
  BatchQueryCollection.getInstance().getResultColumns(otype);

export const getVpropUnit = (
  otype: string,
  vprop: string,
): UnitFormat | undefined =>
  BatchQueryCollection.getInstance().getVpropUnit(otype, vprop);

export const getOptionsByValueTitle = (
  valueTile: string,
): { label: string; value: string | number }[] => {
  const options: { label: string; value: string | number }[] = [];
  try {
    const values =
      typeof valueTile === 'string' && valueTile ? JSON.parse(valueTile) : {};
    Object.entries(values).forEach((item) => {
      const [key, value] = item as [string | number, string];
      options.push({
        label: value,
        value: key,
      });
    });
  } catch (err) {
    console.log(err, 'getOptionsByValueTitle');
  }
  return options;
};
