/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Dayjs } from 'dayjs';
import { SchemeType } from './scheme-config';

export interface MarkInfoItem {
  markId: string;
  eventId?: string;
  eventName?: string;
  ptype: string;
  pname: string;
  otype: string;
  oname: string;
  vprop: string;
  startTime: string;
  endTime: string;
  type: SchemeType;
  description: string;
  user: string;
  createTime: string;
  deviceName?: string;
}

export type MarkInfoList = MarkInfoItem[];

export interface SaveChartMarkCallbackParams {
  startTime: string;
  endTime: string;
  otype: string;
  oname: string;
  vprop: string;
}

export interface ScadaChartMarkFormValues {
  markId?: string;
  dateTimeType: boolean;
  type: SchemeType;
  description: string;
  timeRange: [Dayjs, Dayjs];
  timePicker: Dayjs;
  markObjects: string[];
}

export enum ScadaMarkFormType {
  date = 'DATE',
  dateRange = 'DATERANGE',
}

export interface ScadaMarkConfig {
  otype: string;
  oname: string;
  vprop: string;
  pname: string;
  ptype: string;
}

export interface ScadaChartMark {
  markId: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
  description: string;
  type: SchemeType;
  otype: string;
  oname: string;
  vprop: string;
  pname: string;
  ptype: string;
}

export interface ScadaListChartMark {
  startTime: string | undefined;
  endTime: string | undefined;
  description: string;
  type: SchemeType;
  scadaList: {
    otype: string;
    oname: string;
    vprop: string;
    pname: string;
    ptype: string;
  }[];
}
