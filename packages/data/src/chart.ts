/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { EventSchedulingBasicInfo } from './event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from './event-scheduling/related-info';

export const generateEventTimeSeries = (
  start: string,
  end: string,
  eventList: EventSchedulingBasicInfo[],
) => {
  const format = 'YYYY-MM-DD HH:mm:ss';
  let currentTime = dayjs(start);
  const endTime = dayjs(end);

  const timeSeriesMap = new Map<
    string,
    { count: number; events: EventSchedulingBasicInfo[] }
  >();

  while (currentTime <= endTime) {
    timeSeriesMap.set(currentTime.format(format), { count: 0, events: [] });
    currentTime = currentTime.add(1, 'hour');
  }

  eventList.forEach((event) => {
    const eventTime = dayjs(event.eventStartTime).format(format);
    if (timeSeriesMap.has(eventTime)) {
      const currentEntry = timeSeriesMap.get(eventTime);
      currentEntry!.count += 1;
      currentEntry!.events.push(event);
    } else {
      timeSeriesMap.set(eventTime, { count: 1, events: [event] });
    }
  });

  const timeSeries = Array.from(timeSeriesMap, ([time, { count, events }]) => ({
    name: time,
    value: [time, count],
    events,
  }));

  return timeSeries;
};

export const generateRelatedEventTimeSeries = (
  start: string,
  end: string,
  eventList: EventSchedulingRelatedInfo[],
) => {
  const format = 'YYYY-MM-DD HH:mm:ss';
  let currentTime = dayjs(start);
  const endTime = dayjs(end);

  const timeSeriesMap = new Map<
    string,
    { count: number; events: EventSchedulingRelatedInfo[] }
  >();

  while (currentTime <= endTime) {
    timeSeriesMap.set(currentTime.format(format), { count: 0, events: [] });
    currentTime = currentTime.add(1, 'hour');
  }

  eventList.forEach((event) => {
    const eventTime = dayjs(event.relatedTime).format(format);
    if (timeSeriesMap.has(eventTime)) {
      const currentEntry = timeSeriesMap.get(eventTime);
      currentEntry!.count += 1;
      currentEntry!.events.push(event);
    } else {
      timeSeriesMap.set(eventTime, { count: 1, events: [event] });
    }
  });

  const timeSeries = Array.from(timeSeriesMap, ([time, { count, events }]) => ({
    name: time,
    value: [time, count],
    events,
  }));

  return timeSeries;
};
