/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import InvisibleObject from '../invisible-object';

export interface InvisibleObjectInfo {
  oname: string;
  otype: string;
  title: string;
}

/** InvisibleObject - SCADA汇总 */
export const SCADA_SUMMARY_OBJECT = new InvisibleObject({
  otype: 'SCADA_SUMMARY',
  oname: 'SUMMARY',
  title: 'SCADA汇总',
});

/** InvisibleObject - SCADA指标汇总 */
export const INDICATOR_SUMMARY_OBJECT = new InvisibleObject({
  otype: 'SCADA_SUMMARY',
  oname: 'INDICATOR_SUMMARY',
  title: 'SCADA汇总',
});

/** 分质水汇总 */
export const SCADA_SUMMARY_FZS_OBJECT = new InvisibleObject({
  otype: 'SCADA_SUMMARY',
  oname: 'SUMMARY_FZS',
  title: '分质水汇总',
});

/** InvisibleObject - 模型汇总 */
export const MODEL_SUMMARY_OBJECT = new InvisibleObject({
  otype: 'SPY_SUMMARY',
  oname: 'SUMMARY',
  title: '模型汇总',
});

/** 水厂类型 */
export const PLANT_TYPE = 'SDFOLD_FACT';

/** 泵站类型 */
export const PUMPSTATION_TYPE = 'SDFOLD_STATION_WATER';

export const WDM_PUMPS = 'WDM_PUMPS';

/** 阀门类型 */
export const WDM_VALVES = 'WDM_VALVES';

const APP_MODE_SCHEDULING = 'Scheduling';

const APP_MODE_SUPPLY = 'Supply';

export enum APP_MODE {
  SCHEDULING = APP_MODE_SCHEDULING,
  SUPPLY = APP_MODE_SUPPLY,
}

export const GIS_TOKEN = 'GIS_TOKEN';
