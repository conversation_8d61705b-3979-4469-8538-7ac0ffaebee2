/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Options } from '../types';
import { UploadParams } from '../upload';

export enum ContingencyPlanAttachmentType {
  // 附件
  Default = '1',
  // 流程图
  FLOW = '2',
}

export interface UploadContingencyPlanAttachmentParam extends UploadParams {
  contingencyPlanAttachmentType: ContingencyPlanAttachmentType;
}

export enum ContingencyPlanUserRole {
  GROUP_LEADER = 'group_leader',
  DEPUTY_LEADER = 'deputy_leader',
  MEMBER = 'member',
}

export interface ContingencyPlan {
  planId: string;
  planName: string;
  planType: string;
  planLevel: number;
  createTime: string;
  planNote: string;
  planContent: string;
  attachments: {
    uuid: string;
    fileName: string;
    fileType: ContingencyPlanAttachmentType;
  }[];
}

export interface ContingencyPlanFormValues {
  planId?: string;
  planName: string;
  planType: string;
  planLevel: number;
  planContent: string | undefined;
  planNote?: string;
  attachments?: {
    uuid: string;
    fileName: string;
    fileType: ContingencyPlanAttachmentType;
  }[];
}

export enum ContingencyPlanLevel {
  Level1 = 1,
  Level2 = 2,
  Level3 = 3,
  Level4 = 4,
}

export enum ContingencyPlanType {
  SCHEDULE = 'SCHEDULE',
  REPAIR = 'REPAIR',
  OTHER = 'OTHER',
}

export const getContingencyPlanLevelName = (level: number): string => {
  switch (level) {
    case ContingencyPlanLevel.Level1:
      return '一级';
    case ContingencyPlanLevel.Level2:
      return '二级';
    case ContingencyPlanLevel.Level3:
      return '三级';
    case ContingencyPlanLevel.Level4:
      return '四级';
    default:
      return '未知';
  }
};

export const getContingencyPlanTypeName = (type: string): string => {
  switch (type) {
    case ContingencyPlanType.SCHEDULE:
      return '应急调度';
    case ContingencyPlanType.REPAIR:
      return '应急抢修';
    case ContingencyPlanType.OTHER:
      return '其他';
    default:
      return `未知: ${type}`;
  }
};

export const contingencyPlanLevelOptions: Options<string, number> = [
  ContingencyPlanLevel.Level1,
  ContingencyPlanLevel.Level2,
  ContingencyPlanLevel.Level3,
  ContingencyPlanLevel.Level4,
].map((m) => ({
  label: getContingencyPlanLevelName(m),
  value: m,
}));

export const contingencyPlanTypeOptions: Options<string, string> = [
  ContingencyPlanType.SCHEDULE,
  ContingencyPlanType.REPAIR,
  ContingencyPlanType.OTHER,
].map((m) => ({
  label: getContingencyPlanTypeName(m),
  value: m,
}));
