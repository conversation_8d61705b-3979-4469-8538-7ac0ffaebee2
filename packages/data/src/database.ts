/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Device, { DeviceCollection, StationDevice, StationType } from './device';
import CurrentDeviceTimeData from './device-time-data';
import { IndicatorObject, IndicatorObjectCollection } from './indicator';
import { LayerDataCollection } from './layer-data';
import { getShapeType, SHAPE_TYPE } from './object-item';
import { ChartConfig, PropertyInfo } from './property/property-info';
import { getUnitFormat, UnitFormat } from './unit-system';

export default class Database {
  constructor() {
    this._layerCollection = new LayerDataCollection();
    this._devices = new DeviceCollection();
    this._indicators = new IndicatorObjectCollection();
    this._currentDeviceTimeData = new CurrentDeviceTimeData();
    this._icons = new Map();
    this._propertyInfos = new Map();
    this._viewExtent = [];
    this._fieldEnumMap = new Map();
  }

  initializeLayer(layerData: LayerDataCollection, extent: Array<number>) {
    this._layerCollection = layerData;
    this._viewExtent = extent;
  }

  private updatePropertyTitleUnit() {
    this.propertyInfos.forEach((item) => item.updatePropertyTitleUnit(this));
  }

  private _viewExtent: Array<number>;

  get viewExtent(): Array<number> {
    return this._viewExtent;
  }

  private _layerCollection: LayerDataCollection;

  get layerCollection(): LayerDataCollection {
    return this._layerCollection;
  }

  private _devices: DeviceCollection;

  getDeviceCollection(): DeviceCollection {
    return this._devices;
  }

  getAllDevices(): Device[] {
    return this._devices.getAllDevices();
  }

  /** 获取所有设备和厂站 */
  getAllDevicesAndStations(useDataAccess: boolean = false): Device[] {
    return this._devices.getAllDevicesAndStations(useDataAccess);
  }

  /** 获取所有水厂和泵站 */
  getAllStations(): StationDevice[] {
    return this._devices.getStationDeviceList();
  }

  /** 获取所有水厂 */
  getAllPlants(): StationDevice[] {
    return this._devices
      .getStationDeviceList()
      .filter(
        (f) => f instanceof StationDevice && f.type === StationType.FACTORY,
      );
  }

  /** 获取所有泵站 */
  getAllPumpStation(): StationDevice[] {
    return this._devices
      .getStationDeviceList()
      .filter(
        (f) =>
          f instanceof StationDevice && f.type === StationType.PUMP_STATION,
      );
  }

  getDeviceById(id: string): Device | undefined {
    return this._devices.getDeviceById(id);
  }

  getDeviceListByName(keyword: string): Device[] {
    return this._devices.getDeviceListByName(keyword);
  }

  getDevice(otype: string, oname: string): Device | undefined {
    return this._devices.getDevice(otype, oname);
  }

  private _indicators: IndicatorObjectCollection;

  getIndicators(): IndicatorObjectCollection {
    return this._indicators;
  }

  getIndicator(
    otype: string | undefined,
    oname: string | undefined,
  ): IndicatorObject | undefined {
    if (otype === undefined || oname === undefined) return undefined;
    return this._indicators.getIndicator(otype, oname);
  }

  getIndicatorGroup(
    ptype: string | undefined,
    pname: string | undefined,
  ): IndicatorObject[] | undefined {
    if (ptype === undefined || pname === undefined) return undefined;
    return this._indicators.getIndicatorGroup(ptype, pname);
  }

  private _currentDeviceTimeData: CurrentDeviceTimeData;

  get currentDeviceTimeData(): CurrentDeviceTimeData {
    return this._currentDeviceTimeData;
  }

  private _icons: Map<string, string>;

  get icons(): Map<string, string> {
    return this._icons;
  }

  private _propertyInfos: Map<string, PropertyInfo>;

  get propertyInfos(): Map<string, PropertyInfo> {
    return this._propertyInfos;
  }

  getPropertyInfo(otype: string): PropertyInfo | undefined {
    return this._propertyInfos.get(otype);
  }

  getPropertyTitleUnit(
    otype: string,
    vprop: string,
  ): [string | undefined, string | undefined] {
    const propertyInfo = this._propertyInfos.get(otype);
    if (propertyInfo === undefined) return [undefined, undefined];
    const title = propertyInfo.getPropertyTitle(vprop);
    const unit = propertyInfo.getPropertyUnit(vprop);
    return [title, unit];
  }

  getUnitFormat(otype: string, vprop: string): UnitFormat | undefined {
    const propertyInfo = this._propertyInfos.get(otype);
    const unit = propertyInfo?.getPropertyUnit(vprop);
    if (unit === undefined) return undefined;
    return getUnitFormat(unit);
  }

  getChartConfig(otype: string, vprop: string): ChartConfig | undefined {
    const propertyInfo = this._propertyInfos.get(otype);
    return propertyInfo?.getPropertyChart(vprop);
  }

  initializeDevice(
    devices: DeviceCollection,
    property: Map<string, PropertyInfo> | undefined,
  ) {
    devices.getAllDeviceObjects().forEach((item) => {
      const device = item;
      const propertyInfo = property?.get(device.otype);
      if (propertyInfo?.isDevice) {
        device.isDevice = propertyInfo.isDevice;
      }
    });
    this._devices = devices;
  }

  initializeIndicator(indicators: IndicatorObjectCollection) {
    this._indicators = indicators;
  }

  initializeIcon(icons: Map<string, string>) {
    this._icons = icons;
  }

  initializePropertyInfos(propertyInfos: Map<string, PropertyInfo>) {
    this._propertyInfos = propertyInfos;
    this.updatePropertyTitleUnit();
  }

  updateDeviceInfoById(deviceId: string, info: { [index: string]: any }) {
    this._devices.updateDeviceInfoById(deviceId, info);
  }

  updateIndicatorInfoById(indicatorId: string, info: { [key: string]: any }) {
    this._indicators.updateIndicatorInfoById(indicatorId, info);
  }

  getIcon(otype: string): string | undefined {
    return this.icons.get(otype);
  }

  getIconByShape(
    otype: string,
    shape: string,
    includeShapeType?: SHAPE_TYPE[],
    excludeShapeType?: SHAPE_TYPE[],
  ): string | undefined {
    const shapeType = getShapeType(shape);
    if (
      Array.isArray(includeShapeType) &&
      !includeShapeType.includes(shapeType)
    ) {
      return undefined;
    }

    if (
      Array.isArray(excludeShapeType) &&
      excludeShapeType.includes(shapeType)
    ) {
      return undefined;
    }

    return this.icons.get(otype);
  }

  private _fieldEnumMap: Map<string, Map<string, string>>;

  initializeFieldEnumMap(
    fieldEnumMap:
      | {
          [category: string]: { [key: string]: string };
        }
      | undefined,
  ) {
    Object.entries(fieldEnumMap ?? {}).forEach(([category, values]) => {
      this._fieldEnumMap.set(category, new Map(Object.entries(values)));
    });
  }

  updateFieldEnumMap(fieldEnumMap: Map<string, Map<string, string>>) {
    this._fieldEnumMap = fieldEnumMap;
  }

  get fieldEnumMap(): Map<string, Map<string, string>> {
    return this._fieldEnumMap;
  }

  getEnumObject(businessVariable: string): Map<string, string> | undefined {
    return this._fieldEnumMap.get(businessVariable);
  }

  getAllDevicesByUserAccess(access: boolean): Map<string, Device> {
    return this._devices.getAllDevicesByUserAccess(access);
  }

  getStationByPump(otype: string, oname: string): StationDevice | undefined {
    return this.getAllStations().find((f) =>
      f.pumpList.find((pf) => pf.otype === otype && pf.oname === oname),
    );
  }

  getStationById(id: string): StationDevice | undefined {
    return this.getAllStations().find((f) => f.id === id);
  }
}
