/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface DemandSummary {
  totalUserCount?: number;
  dmaUserCount?: number;
  otherUserCount?: number;
  totalDemand?: number;
}

export interface DMAInfo {
  otype: string;
  oname: string;
  shape: string;
  title: string;
  data: Map<string, any>;
}

export interface MeterInfo {
  otype: string;
  oname: string;
  shape: string;
  name: string;
  data: Map<string, any>;
}
