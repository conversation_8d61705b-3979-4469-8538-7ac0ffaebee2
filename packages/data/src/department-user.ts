/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentInfo } from './department';
import { isNotNullOrEmpty } from './string';
import { UserInfo } from './system-user';
import { transListToTree } from './tree';

export type ShareSolutionType = 'department' | 'user';
export type ShareSolutionTableType = {
  id: string;
  type: ShareSolutionType;
  name: string;
};

export type DepartmentUserNode = {
  title: string;
  /**
   * 用于tree select 组件时需要这个属性
   * label & value
   */
  label?: string;
  value?: string;
  key: string;
  checkable?: boolean;
  disabled?: boolean;
  children?: DepartmentUserNode[];
};

interface TreeNode {
  key: string | number;
  children?: TreeNode[];
}

export function buildDepartmentTree(
  departments: DepartmentInfo[],
  checkable: boolean = false,
  disabledDepartments: string[] = [],
): DepartmentUserNode[] {
  return transListToTree(departments, 'id', 'parentId', {
    sorted: 'asc',
    sortedKey: 'order',
    convertItem: (item) => ({
      title: item.name,
      label: item.name,
      key: item.id,
      value: item.id,
      checkable,
      disabled: disabledDepartments?.includes(item.id),
      order: item.order,
    }),
  });
}

function addUsersToDepartmentTree(
  departmentTree: DepartmentUserNode[],
  users: UserInfo[],
  disabledNoMobile: boolean = false,
): DepartmentUserNode[] {
  return departmentTree.map((node) => {
    const matchingUsers = users.filter(
      (user) =>
        user.departmentName === node.title && user.department === node.key,
    );
    const userNodes: DepartmentUserNode[] = matchingUsers.map((user) => ({
      title: user.name,
      label: user.name,
      key: user.id,
      value: user.id,
      department: user.departmentName,
      mobile: user.phone,
      type: 'user',
      disabled: disabledNoMobile && !isNotNullOrEmpty(user.phone),
    }));
    const children = node.children
      ? addUsersToDepartmentTree(node.children, users, disabledNoMobile)
      : [];
    return {
      ...node,
      disabled: disabledNoMobile && userNodes.every((user) => user.disabled),
      children: [...userNodes, ...children],
    };
  });
}

export function createDepartmentUserTree(
  departments: DepartmentInfo[],
  users: UserInfo[],
  allowCheckDepartment: boolean = false,
  disabledNoMobile: boolean = false,
): DepartmentUserNode[] {
  const departmentTree = buildDepartmentTree(departments, allowCheckDepartment);
  const departmentUserTree = addUsersToDepartmentTree(
    departmentTree,
    users,
    disabledNoMobile,
  );
  const noDepartmentUsers = users.filter(
    (user) => !departments.some((dept) => dept.name === user.departmentName),
  );
  if (noDepartmentUsers.length > 0) {
    departmentUserTree.push({
      title: '未设置部门',
      label: '未设置部门',
      key: 'no-department',
      value: 'no-department',
      checkable: false,
      children: noDepartmentUsers.map((user) => ({
        title: user.name,
        label: user.name,
        key: user.id,
        value: user.id,
        type: 'user',
        department: '未设置部门',
        mobile: user.phone,
        disabled: disabledNoMobile && !isNotNullOrEmpty(user.phone),
      })),
    });
  }
  return departmentUserTree;
}

// 递归地获取所有子节点的 key
export const getAllChildKeys = (node: TreeNode): string[] => {
  if (!node.children || node.children.length === 0) {
    return [];
  }
  const childKeys = node.children.map((child) => child.key.toString());
  const grandChildKeys = node.children.flatMap(getAllChildKeys);
  return [...childKeys, ...grandChildKeys];
};
