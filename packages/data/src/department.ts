/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface DepartmentInfo {
  id: string;
  parentId: string;
  name: string;
  note: string;
  children?: DepartmentInfo[];
  order?: number;
}

export type DepartmentList = DepartmentInfo[];

export interface DepartmentTreeData {
  title: string;
  key: string;
  value: string;
  children?: DepartmentTreeData[];
}

type GetValueFunc = (item: DepartmentInfo) => string;

export const transDepartmentToTreeData = (
  list: DepartmentList,
  getValue: GetValueFunc = (item: DepartmentInfo) => item.id,
): DepartmentTreeData[] =>
  list.map(
    (item): DepartmentTreeData => ({
      title: item.name,
      key: getValue(item),
      value: getValue(item),
      children: item.children
        ? transDepartmentToTreeData(item.children, getValue)
        : undefined,
    }),
  );
