/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AssessmentParams } from './app-config';
import Database from './database';
import Device from './device';
import { makeObjectId } from './object-item';
import {
  getUnitFormat,
  getUnitValueWithSymbol,
  UnitFormat,
} from './unit-system';

export type InfoValue = string | number | null | undefined;

export type RateData = { value: number; name: string };

export interface DeviceAssessmentInfo {
  id: string;
  oname: string;
  otype: string;
  [index: string]: InfoValue;
}

export function generateDeviceAssessmentInfo(
  db: Database,
  oname: string,
  otype: string,
  deviceInfo: Device | undefined,
  dataValue: { [index: string]: InfoValue } | null,
): DeviceAssessmentInfo {
  const info: DeviceAssessmentInfo = {
    id: makeObjectId(otype, oname),
    oname,
    otype,
    deviceName: deviceInfo?.title,
  };
  const propertyInfo = db.getPropertyInfo(otype);
  if (dataValue) {
    Object.entries(dataValue).forEach((item) => {
      const [key, value] = item;
      const fieldUnitKey = propertyInfo?.getPropertyUnit(key);
      info[key] =
        (fieldUnitKey ? getUnitValueWithSymbol(fieldUnitKey, value) : value) ||
        '-';
    });
  }
  return info;
}

export function columnsSort(
  a: DeviceAssessmentInfo,
  b: DeviceAssessmentInfo,
  key: string,
): number {
  const valueA = a[key];
  const valueB = b[key];
  if (typeof valueA === 'number' && typeof valueB === 'number') {
    return valueA - valueB;
  }
  if (typeof valueA === 'string' && typeof valueB === 'string')
    return valueA?.localeCompare(valueB);
  return -1;
}

export function getFieldTitle(
  db: Database,
  vprop: string,
  deviceAssessmentParams: AssessmentParams,
): string | undefined {
  const { otypeList = [] } = deviceAssessmentParams;
  for (let i = 0; i < otypeList.length; i += 1) {
    const propertyInfo = db.getPropertyInfo(otypeList[i]);
    const fieldTitle = propertyInfo?.getPropertyTitle(vprop);
    if (fieldTitle) return fieldTitle;
  }
  return undefined;
}

export function getReliabilityUnit(
  db: Database,
  otype: string,
): UnitFormat | undefined {
  const propertyInfo = db.getPropertyInfo(otype);
  const fieldUnitKey = propertyInfo?.getPropertyUnit('RELIABILITY_S');
  if (fieldUnitKey) {
    const unitFormat = getUnitFormat(fieldUnitKey);
    if (unitFormat) return unitFormat;
  }
  return undefined;
}

function getReliabilityUnitForDeviceList(
  db: Database,
  deviceList: DeviceAssessmentInfo[],
): UnitFormat | undefined {
  for (let i = 0; i < deviceList.length; i += 1) {
    const { otype } = deviceList[i];
    const unitFormat = getReliabilityUnit(db, otype);

    if (unitFormat) return unitFormat;
  }
  return undefined;
}

export function getDeviceReliabilityRate(
  db: Database,
  deviceList: DeviceAssessmentInfo[],
): RateData[] {
  const reliabilityUnitFormat = getReliabilityUnitForDeviceList(db, deviceList);
  const rateData: Map<string | number, RateData> = new Map();

  if (reliabilityUnitFormat) {
    const reliabilityUnitValues = [
      ...reliabilityUnitFormat.getYAxisValues(),
    ].reverse();

    reliabilityUnitValues.forEach((reliabilityValue) => {
      const reliabilityName = reliabilityUnitFormat.getValue(reliabilityValue);
      const filterList = deviceList.filter(
        (item) => item.RELIABILITY_S === reliabilityName,
      );
      rateData.set(reliabilityValue, {
        value: filterList.length,
        name: reliabilityName as string,
      });
    });
    // RELIABILITY_S已经装换为中文， 对比中文
    const reliabilityUnitNames = reliabilityUnitValues.map((reliabilityValue) =>
      reliabilityUnitFormat.getValue(reliabilityValue),
    );
    const filterOtherList = deviceList.filter(
      (item) => !reliabilityUnitNames.includes(item.RELIABILITY_S as string),
    );

    const dataItem = rateData.get('0');
    if (filterOtherList.length && dataItem) {
      // 写死 其它统计在 reliabilityValue “0” 下面
      rateData.set('0', {
        ...dataItem,
        value: dataItem.value + filterOtherList.length,
      });
    }
  }
  return [...rateData.values()];
}
