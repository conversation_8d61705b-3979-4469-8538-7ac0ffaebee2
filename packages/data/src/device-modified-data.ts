/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface GetDeviceModifiedParams {
  current: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  operationType?: string;
  modifyType?: string;
  deviceCode?: string;
}

export interface DeviceModifiedLog {
  id: string;
  updateTime: string | undefined;
  otype: string;
  oname: string;
  operationType: string;
  modifyType: string;
  description: string;
  source: string;
  operator: string;
  remark: string;
}

export interface GetDeviceModifiedSummaryParams {
  current: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  operationType?: string;
  modifyType?: string;
}

export interface DeviceModifiedLogSummary {
  id: string;
  updatedTime: string;
  operationType: string;
  modifyType: string;
  count: string;
  description: string;
}

export const OPERATION_TYPE = [
  {
    label: '设备更新',
    value: 'DEVICE',
  },
  {
    label: '监测量更新',
    value: 'SCADA',
  },
  {
    label: '分区更新',
    value: 'DMA',
  },
  {
    label: '监控表更新',
    value: 'MONITORING',
  },
  {
    label: '用户表更新',
    value: 'WATERMETER',
  },
];

export const MODIFY_TYPE = [
  {
    label: '增加',
    value: 'ADD',
  },
  {
    label: '修改',
    value: 'UPDATE',
  },
  {
    label: '删除',
    value: 'DELETE',
  },
  {
    label: '错误',
    value: 'ERROR',
  },
];
