/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeObjectId } from './object-item';

export interface DeviceTheme {
  maxRatio: number | null;
  minRatio: number | null;
}

export type DeviceThemeData = Map<string, DeviceTheme>;

export function getDeviceThemeData(deviceThemeStyles: any): DeviceThemeData {
  const deviceThemeData: DeviceThemeData = new Map();
  if (typeof deviceThemeStyles === 'object' && deviceThemeStyles !== null) {
    Object.entries(deviceThemeStyles).forEach((item) => {
      const [otype, otypeValues] = item;
      if (otypeValues) {
        Object.entries(otypeValues).forEach((v) => {
          const [oname, theme] = v;
          const { max_ratio: maxRatio, min_ratio: minRatio } = theme || {};
          const deviceId = makeObjectId(otype, oname);
          deviceThemeData.set(deviceId, {
            maxRatio,
            minRatio,
          });
        });
      }
    });
  }
  return deviceThemeData;
}

export function getDeviceTheme(
  deviceId: string,
  data: DeviceThemeData,
): DeviceTheme | undefined {
  return data.get(deviceId);
}
