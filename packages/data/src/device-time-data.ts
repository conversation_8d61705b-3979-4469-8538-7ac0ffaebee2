/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getUnitValue } from './unit-system';

export enum DeviceStateRecordSource {
  // todo: 目前数据库字段没有sourceType 使用中文
  ORDER = '工单',
  ARTIFICIAL = '人工',
  DEVICE_ASSESSMENT = '设备评估',
}

export interface DeviceStateData {
  id: string;
  key: string;
  title: string;
  typeTitle: string;
  oname: string;
  otype: string;
  otime: string;
  state: boolean;
  source: string;
  note: string;
  creator: string;
  stime: string;
  shape?: string;
  dataType?: 'device' | 'indicator';
  ptype?: string;
  pname?: string;
}

export interface DeviceTimeData {
  oname: string;
  otype: string;
  vprop: string;
  time: string;
  originalValue: number | string | undefined;
  value: number | string | undefined;
  unit: string | undefined;
  latestDeviceState: boolean;
}

export interface ScadaModelTimeData {
  id: string;
  oname: string;
  otype: string;
  scadaData: DeviceTimeData | undefined;
  simulationData: DeviceTimeData | undefined;
}

export interface ScadaModelWsData {
  OID: string;
  TIME: string;
  VALUE: number;
}

export default class CurrentDeviceTimeData {
  private _indicatorData: Map<string, ScadaModelTimeData> = new Map();

  updateIndicatorData(values: Map<string, ScadaModelTimeData>) {
    this._indicatorData = values;
  }

  updateIndicatorDataByIncremental(
    values: ScadaModelWsData[],
    type: 'scada' | 'model' = 'scada',
  ) {
    values.forEach((wsData) => {
      const id = wsData.OID;

      const existingData = this._indicatorData.get(id);

      if (existingData) {
        if (type === 'scada') {
          if (existingData.scadaData) {
            existingData.scadaData.time = wsData.TIME;
            existingData.scadaData.originalValue = wsData.VALUE;
            existingData.scadaData.value = getUnitValue(
              existingData.scadaData?.unit ?? '',
              wsData.VALUE,
            );
            existingData.scadaData.latestDeviceState = true;
          }
        } else if (existingData.simulationData) {
          existingData.simulationData.time = wsData.TIME;
          existingData.simulationData.originalValue = wsData.VALUE;
          existingData.simulationData.value = getUnitValue(
            existingData.simulationData?.unit ?? '',
            wsData.VALUE,
          );
        }
      }
    });
  }

  getIndicatorValueById(id: string): ScadaModelTimeData | undefined {
    return this._indicatorData.get(id);
  }
}
