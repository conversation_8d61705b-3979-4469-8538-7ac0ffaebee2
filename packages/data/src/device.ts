/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PLANT_TYPE, PUMPSTATION_TYPE } from './const/system-const';
import Database from './database';
import GisObject from './gis-object';
import {
  IndicatorObject,
  IndicatorObjectCollection,
  IndicatorType,
  IndicatorTypeCollection,
} from './indicator';
import ModelObject from './model-object';
import {
  getShapeType,
  IObjectItem,
  makeObjectId,
  SHAPE_TYPE,
} from './object-item';
import {
  ChartProperties,
  ObjectChartProperty,
  PropertyInfo,
} from './property/property-info';

type Positioning =
  | 'bottom-center'
  | 'bottom-left'
  | 'bottom-right'
  | 'center-left'
  | 'center-center'
  | 'center-right'
  | 'top-left'
  | 'top-center'
  | 'top-right';

export interface PropertyValue {
  oname: string;
  otype: string;
  vprop: string;
  value: string | undefined;
  otime: string | undefined;
}

export function getDataAccess(
  deviceDataAccess: string | null | undefined,
  userDataAccess: string[],
): boolean {
  // 当设备权限为空时， 不受限制，所有人可以访问
  if (
    deviceDataAccess === null ||
    deviceDataAccess === undefined ||
    deviceDataAccess.trim() === ''
  )
    return true;
  const deviceDataAccessList = deviceDataAccess.trim().split(',');
  const mergeSet = new Set([...deviceDataAccessList, ...userDataAccess]);
  // 如果合并后的集合大小小于用户权限集合，则表示有交集，即有权限访问
  return mergeSet.size < userDataAccess.length + deviceDataAccessList.length;
}

export default class Device implements IObjectItem {
  private _data: Map<string, any> = new Map();

  constructor(data: any, userDataAccess: string[]) {
    this.updateDeviceData(data, userDataAccess);
  }

  private _dataAccess: boolean = true;

  private _parentArea: string | null = null;

  get parentArea(): string | null {
    return this._parentArea;
  }

  private _monitorObject: string | null = null;

  get monitorObject(): string | null {
    return this._monitorObject;
  }

  private _id: string = '';

  get id(): string {
    return this._id;
  }

  private _oname: string = '';

  get oname(): string {
    return this._oname;
  }

  private _otype: string = '';

  get otype(): string {
    return this._otype;
  }

  private _pname: string = '';

  get pname(): string {
    return this._pname;
  }

  private _layerName: string = '';

  get layerName(): string {
    return this._layerName;
  }

  private _shape: string = '';

  get shape(): string | undefined {
    return this._shape;
  }

  private _shapeType: SHAPE_TYPE = 'UNKNOWN';

  get shapeType(): SHAPE_TYPE {
    return this._shapeType;
  }

  private _title: string = '';

  get title(): string {
    return this._title;
  }

  private _shortTitle: string = '';

  get shortTitle(): string {
    return this._shortTitle;
  }

  private _dataSource: string = '';

  get dataSource(): string {
    return this._dataSource;
  }

  private _minViewRatio: number = 0;

  get minViewRatio(): number {
    return this._minViewRatio;
  }

  private _maxViewRatio: number = 0;

  get maxViewRatio(): number {
    return this._maxViewRatio;
  }

  private _displayLevel: number = 0;

  get displayLevel(): number {
    return this._displayLevel;
  }

  private _icon: string = '';

  get icon(): string {
    return this._icon;
  }

  set icon(iconName: string) {
    this._icon = iconName;
    this._highlightIcon = iconName;
  }

  private _highlightIcon: string | undefined;

  get highlightIcon(): string | undefined {
    return this._highlightIcon;
  }

  private _indicators: Array<IndicatorObject> = [];

  get indicators(): Array<IndicatorObject> {
    return this._indicators;
  }

  private _overlayIndicators: Array<IndicatorObject> = [];

  get overlayIndicators(): Array<IndicatorObject> {
    return this._overlayIndicators;
  }

  private _caliber: number | undefined;

  get caliber(): number | undefined {
    return this._caliber;
  }

  private _organization: string | undefined;

  get organization(): string | undefined {
    return this._organization;
  }

  private _summarize: boolean = true;

  get summarize(): boolean {
    return this._summarize;
  }

  // icon rotation
  private _rotation: number = 0;

  get rotation(): number {
    return this._rotation;
  }

  set rotation(angle: number) {
    this._rotation = angle;
  }

  // overlayPositioning
  private _overlayPositioning: Positioning = 'bottom-center';

  get overlayPositioning(): Positioning {
    return this._overlayPositioning;
  }

  set overlayPositioning(positioning: Positioning) {
    this._overlayPositioning = positioning;
  }

  private _isDevice: boolean = false;

  set isDevice(flag: boolean) {
    this._isDevice = flag;
  }

  get isDevice(): boolean {
    return this._isDevice;
  }

  private _samplingTimeStep: string = '';

  set samplingTimeStep(timeStep: string) {
    this._samplingTimeStep = timeStep;
  }

  get samplingTimeStep(): string {
    return this._samplingTimeStep;
  }

  updateDeviceData(data: any, userDataAccess: string[]) {
    const dataMap = new Map(Object.entries(data));
    [...dataMap].forEach(([key, value]) => {
      this._data.set(key, value);
    });

    this._oname = this._data.get('ONAME');
    this._otype = this._data.get('OTYPE');
    this._id = makeObjectId(this._otype, this._oname);
    this._pname = this._data.get('PNAME');
    this._layerName = this._data.get('PTYPE');
    this._shape = this._data.get('SHAPE');
    this._shapeType = getShapeType(this._shape);
    this._dataSource = this._data.get('DATASOURCE');
    this._maxViewRatio = this._data.get('MAX_DRAW_RATIO');
    this._minViewRatio = this._data.get('MIN_DRAW_RATIO');
    this._caliber = this._data.get('CALIBER');
    this._rotation = this._data.get('ANGLE') ?? 0;
    this._overlayPositioning =
      this._data.get('OVERLAY_POSITIONING') ?? 'bottom-center';
    this._samplingTimeStep = this._data.get('SAMPLING_TIMESTEP');
    this._dataAccess = getDataAccess(
      this._data.get('DATA_ACCESS'),
      userDataAccess,
    );
    const summarize = this._data.get('SUMMARIZE');
    if (summarize === 'false') {
      this._summarize = false;
    }

    const caliber = this._data.get('CALIBER');
    if (caliber === null || typeof caliber === 'undefined') {
      this._caliber = undefined;
    } else {
      this._caliber = Number(caliber);
    }

    const displayLevel = this._data.get('DISPLAYLEVEL');
    if (displayLevel == null) {
      this._displayLevel = 9999;
    } else {
      this._displayLevel = Number(displayLevel);
    }

    const shortTitle = this._data.get('SHORT_TITLE');
    const fullTitle = this._data.get('TITLE');
    this._shortTitle = shortTitle ?? fullTitle ?? this.oname;
    this._title = fullTitle ?? shortTitle ?? this.oname;
    this._title = this._title?.trim();
    this._shortTitle = this._shortTitle?.trim();
    this._parentArea = this._data.get('PARENT_AREA');
    this._monitorObject = this._data.get('MONITOR_OBJECT');
    this._organization = this._data.get('ORANIZATION');
  }

  getIndicatorTypeById(id: string): IndicatorType | undefined {
    for (let i = 0; i < this._indicators.length; i += 1) {
      if (this._indicators[i].id === id)
        return this._indicators[i].indicatorType;
    }

    return undefined;
  }

  getIndicatorType(otype: string, oname: string): IndicatorType | undefined {
    const id: string = makeObjectId(otype, oname);
    return this.getIndicatorTypeById(id);
  }

  getIndicatorById(id: string): IndicatorObject | undefined {
    for (let i = 0; i < this._indicators.length; i += 1) {
      if (this._indicators[i].id === id) return this._indicators[i];
    }

    return undefined;
  }

  addIndicator(indicator: IndicatorObject) {
    this._indicators.push(indicator);
  }

  addOverlayIndicator(indicator: IndicatorObject) {
    this._overlayIndicators.push(indicator);
  }

  private _feature: any;

  /**
   * get cached feature object which is added to layer
   * the type is expected to be 'Feature', use 'any' to avoid dependency
   * if value is undefined, means that the device has not renderd as a Feature
   */
  get feature(): any {
    return this._feature;
  }

  setFeature(feature: any) {
    this._feature = feature;
  }

  getPropertyValue(vprop: string): any {
    return this._data.get(vprop);
  }

  get dataAccess(): boolean {
    return this._dataAccess;
  }
}

export enum StationType {
  FACTORY = 'FACTORY',
  PUMP_STATION = 'PUMP_STATION',
}

function getStationTypeByOType(otype: string): StationType | undefined {
  switch (otype) {
    case 'SDFOLD_FACT':
      return StationType.FACTORY;
    case 'SDFOLD_STATION_WATER':
      return StationType.PUMP_STATION;
    default:
      return undefined;
  }
}

function getStationTypeName(type: StationType): string {
  switch (type) {
    case StationType.FACTORY:
      return '水厂';
    case StationType.PUMP_STATION:
      return '泵站';
    default:
      return '未知';
  }
}

export function getStationTypeNameByOType(otype: string): string {
  return getStationTypeName(getStationTypeByOType(otype) as StationType);
}

export class PumpInfo {
  private _title = '';

  private _oname = '';

  private _otype = '';

  private _variable: boolean = false;

  private _maxFrequency = 0;

  private _minFrequency = 0;

  private _indicators: Array<IndicatorObject> = [];

  private _onOffIndicator: IndicatorObject | undefined;

  constructor(data: any) {
    this._title = data.title ?? data.description ?? data.oname;
    this._oname = data.oname;
    this._otype = data.otype;
    this._variable = data.frequencymode === 'Variable';
    data.scada.forEach((item: any) => {
      const originData: any = {
        OTYPE: item.otype,
        ONAME: item.oname,
        PNAME: data.otype,
        PTYPE: data.oname,
        LIMIT_MAX: undefined,
        TITLE: undefined,
      };
      this._indicators.push(new IndicatorObject(originData));
    });
    if (this._variable) {
      this._minFrequency = Number(data.minfrequency);
      this._maxFrequency = Number(data.maxfrequency);
      this._onOffIndicator = this._indicators.find(
        (item) => item.otype === 'SDVAL_FREQUENCY',
      );
    } else {
      this._onOffIndicator = this._indicators.find(
        (item) => item.otype === 'SDVAL_PUMPRUN',
      );
    }
  }

  get title(): string {
    return this._title;
  }

  get oname(): string {
    return this._oname;
  }

  get otype(): string {
    return this._otype;
  }

  get variable(): boolean {
    return this._variable;
  }

  get indicators(): IndicatorObject[] {
    return this._indicators;
  }

  get onOffIndicator(): IndicatorObject | undefined {
    return this._onOffIndicator;
  }

  get minFrequency(): number {
    return this._minFrequency;
  }

  get maxFrequency(): number {
    return this._maxFrequency;
  }
}

export class StationDevice extends Device {
  private _pumpList: PumpInfo[] = [];

  private _type: StationType | undefined = undefined;

  constructor(data: any, userDataAccess: string[]) {
    super(data, userDataAccess);
    this._type = getStationTypeByOType(this.otype);
  }

  initializePumpList(pumpsData: PumpInfo[]) {
    // 对 pumpsData 进行排序
    this._pumpList = pumpsData.sort((a, b) => {
      // 从 title 中提取数字
      const getNumber = (title: string) => {
        const match = title.match(/^(\d+)#/);
        return match ? parseInt(match[1], 10) : 0;
      };

      const numA = getNumber(a.title);
      const numB = getNumber(b.title);

      // 如果两个标题都符合 "1# 9#" 格式,则按数字大小排序
      if (numA !== -1 && numB !== -1) {
        return numA - numB;
      }

      // 如果不符合格式,保持原有顺序
      return 0;
    });
  }

  get pumpList(): PumpInfo[] {
    return this._pumpList;
  }

  get type(): StationType | undefined {
    return this._type;
  }

  get typeName(): string {
    return getStationTypeName(this._type as StationType);
  }
}

export class PlantsAndPumpStationsCollection {
  private _pumpsMap: Map<string, PumpInfo[]> = new Map();

  constructor(data: any) {
    data.forEach((item: any) => {
      const id = makeObjectId(item.otype, item.oname);
      const pumpsList: PumpInfo[] = [];
      item.pumps.forEach((item: any) => {
        pumpsList.push(new PumpInfo(item));
      });
      this._pumpsMap.set(id, pumpsList);
    });
  }

  get pumpsMap(): Map<string, PumpInfo[]> {
    return this._pumpsMap;
  }

  getPumpList(id: string): PumpInfo[] | undefined {
    return this._pumpsMap.get(id);
  }
}

export class DeviceCollection {
  private _userDeviceAccess: string[] = [];

  private _devices: Map<string, Device>;

  private _devicesByOType: Map<string, Map<string, Device>>;

  private _devicesByLayer: Map<string, Map<string, Device>>;

  constructor() {
    this._devices = new Map<string, Device>();
    this._devicesByOType = new Map<string, Map<string, Device>>();
    this._devicesByLayer = new Map<string, Map<string, Device>>();
  }

  addDevice(device: Device) {
    this._devices.set(device.id, device);

    const oTypeDevices: Map<string, Device> | undefined =
      this._devicesByOType.get(device.otype);
    if (oTypeDevices !== undefined) {
      oTypeDevices.set(device.id, device);
    } else {
      const devices: Map<string, Device> = new Map<string, Device>();
      devices.set(device.id, device);
      this._devicesByOType.set(device.otype, devices);
    }

    const layerDevices: Map<string, Device> | undefined =
      this._devicesByLayer.get(device.layerName);
    if (layerDevices !== undefined) {
      layerDevices.set(device.id, device);
    } else {
      const devices: Map<string, Device> = new Map<string, Device>();
      devices.set(device.id, device);
      this._devicesByLayer.set(device.layerName, devices);
    }
  }

  getDeviceById(id: string): Device | undefined {
    return this._devices.get(id);
  }

  getDeviceListByName(keyword: string): Device[] {
    if (keyword === '') return [];
    const allDevices = this.getAllDeviceObjects();
    const matchDevices = allDevices.filter((device) => {
      if (device.title) {
        return device.title.match(keyword) !== null;
      }
      return false;
    });
    return matchDevices;
  }

  getDevice(otype: string, oname: string): Device | undefined {
    const id = makeObjectId(otype, oname);
    return this._devices.get(id);
  }

  getAllDeviceObjects(): Device[] {
    return Array.from(this._devices.values());
  }

  getAllDevices(useDataAccess: boolean = false): Device[] {
    const devices = Array.from(this._devices.values());
    const allDevices: Device[] = [];
    devices.forEach((device) => {
      if (device.isDevice) {
        // 如果使用数据访问权限，则只返回当前用户有访问权限的设备
        if (useDataAccess) {
          if (device.dataAccess) allDevices.push(device);
        } else {
          allDevices.push(device);
        }
      }
    });
    return allDevices;
  }

  getStationDeviceList(useDataAccess: boolean = false): StationDevice[] {
    const devices = Array.from(this._devices.values());
    const allDevices: StationDevice[] = [];
    devices.forEach((device) => {
      if (device instanceof StationDevice) {
        if (useDataAccess) {
          if (device.dataAccess) allDevices.push(device);
        } else {
          allDevices.push(device);
        }
      }
    });
    return allDevices;
  }

  getAllDevicesAndStations(useDataAccess: boolean = false): Device[] {
    return [
      ...this.getAllDevices(useDataAccess),
      ...this.getStationDeviceList(useDataAccess),
    ];
  }

  getAllDevicesByLayer(layerName: string): Map<string, Device> | undefined {
    return this._devicesByLayer.get(layerName);
  }

  initializeDevices(
    data: { [key: string]: any }[],
    userDeviceAccess: string[],
  ) {
    this._devices.clear();
    this._userDeviceAccess = userDeviceAccess;
    data.forEach((element) => {
      let device;
      switch (element.OTYPE) {
        case PLANT_TYPE:
        case PUMPSTATION_TYPE:
          device = new StationDevice(element, this._userDeviceAccess);
          break;
        default:
          device = new Device(element, this._userDeviceAccess);
          break;
      }

      if (!device.otype?.startsWith('SDVAL_')) this.addDevice(device);
    });
  }

  initializeOverlayIndicators(
    data: {},
    indicatorTypes: IndicatorTypeCollection,
    indicatorObjects: IndicatorObjectCollection,
  ) {
    Object.entries(data).forEach((item) => {
      const deviceOtype = item[0];
      const devices: {} = item[1] as {};
      Object.entries(devices).forEach((deviceData) => {
        const deviceOname = deviceData[0];
        const indicatorType: {}[] = deviceData[1] as {}[];
        const deviceObject: Device | undefined = this.getDevice(
          deviceOtype,
          deviceOname,
        );
        if (deviceObject === undefined) return;

        indicatorType.forEach((indicatorItem: any) => {
          const indicatorOname = indicatorItem.ONAME;
          const indicatorOtype = indicatorItem.OTYPE;
          const id = makeObjectId(indicatorOtype, indicatorOname);
          const indicatorType: IndicatorType | undefined =
            indicatorTypes.getIndicatorType(indicatorOtype);
          const indicatorObject: IndicatorObject | undefined =
            indicatorObjects.getIndicatorById(id);
          if (indicatorObject !== undefined && indicatorType !== undefined) {
            indicatorObject.setVisible = true;
            indicatorObject.indicatorType = indicatorType;
            deviceObject.addOverlayIndicator(indicatorObject);
          }
        });
      });
    });
  }

  initializeIcons(icons: Map<string, string>) {
    this._devices.forEach((device) => {
      let icon = icons.get(device.otype);
      if (icon === undefined) {
        icon = icons.get(device.layerName);
        if (icon === undefined) icon = '';
      }

      device.icon = icon;
    });
  }

  initializePlantsAndPumpStations(
    plantsAndPumpStations: PlantsAndPumpStationsCollection,
  ) {
    Array.from(plantsAndPumpStations.pumpsMap.entries()).forEach((item) => {
      const [id, list]: [string, PumpInfo[]] = item;
      const plantDevice = this._devices.get(id);
      if (plantDevice && plantDevice instanceof StationDevice) {
        plantDevice.initializePumpList(list);
      }
    });
  }

  updateDeviceInfoById(id: string, info: { [key: string]: any }) {
    const deviceInfo = this._devices.get(id);
    if (deviceInfo) {
      deviceInfo.updateDeviceData(info, this._userDeviceAccess);
    }
  }

  getAllDevicesByUserAccess(access: boolean): Map<string, Device> {
    const accessMap: Map<string, Device> = new Map();
    this._devices.forEach((device, key) => {
      if (device.dataAccess === access) {
        accessMap.set(key, device);
      }
    });
    return accessMap;
  }

  set userDeviceAccess(access: string[]) {
    this._userDeviceAccess = access;
  }
}

export function getValueQueryParameters(
  db: Database,
  object: Device | ModelObject,
): any {
  const propertyInfo: PropertyInfo | undefined = db.getPropertyInfo(
    object.otype,
  );

  if (propertyInfo) {
    return propertyInfo.getValueQueryParameters(object.indicators);
  }
  return {};
}

export function getChartProperties(
  db: Database,
  object: IObjectItem,
): ObjectChartProperty[] {
  let { otype } = object;
  if (object instanceof GisObject) {
    if (object.refModelObject === undefined) return [];
    otype = object.refModelObject.otype;
  }

  const propertyInfo: PropertyInfo | undefined = db.getPropertyInfo(otype);
  if (propertyInfo) {
    return propertyInfo.getChartProperties(object.indicators);
  }

  return [];
}

export function getChartPropertiesByIndicator(
  db: Database,
  indicatorType: string,
  indicatorName: string,
  vprop: string,
): ObjectChartProperty | undefined {
  const propertyInfo: PropertyInfo | undefined =
    db.getPropertyInfo(indicatorType);
  if (propertyInfo) {
    return propertyInfo.getChartPropertyByIndicator(
      indicatorType,
      indicatorName,
      vprop,
    );
  }
  return undefined;
}

export function getChartPropertiesByConfig(
  db: Database,
  object: IObjectItem | Device,
  configName: 'treeConfig' | 'chartConfig' = 'treeConfig',
): ChartProperties[] {
  let chartObject = object;
  if (object instanceof GisObject) {
    if (object.refModelObject === undefined) return [];
    // 当object为gis对象时, 使用对应的模型对象
    chartObject = object.refModelObject;
  }
  const { otype } = chartObject;

  const propertyInfo: PropertyInfo | undefined = db.getPropertyInfo(otype);
  if (propertyInfo) {
    return propertyInfo.getChartPropertiesByConfig(chartObject, db, configName);
  }

  return [];
}
