/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CommandType } from './create-command';

export enum DispatchCommandSendOrganization {
  COMMAND_CENTER = 'COMMAND_CENTER',
}

export enum DispatchCommandCommandObject {
  WATER_PUMP = 'WATER_PUMP',
  FLOW_ADJUSTMENT = 'FLOW_ADJUSTMENT',
  PRESSURE_ADJUSTMENT = 'PRESSURE_ADJUSTMENT',
  OTHER_COMMAND = 'OTHER_COMMAND',
  VALVE_CONTROL = 'VALVE_CONTROL',
}

export enum DispatchCommandState {
  SENT = 'SENT',
  UNREAD = 'UNREAD',
  READ_ACCEPTED = 'READ_ACCEPTED',
  NOT_REPLIED = 'NOT_REPLIED',
  REPLIED = 'REPLIED',
  REJECTED = 'REJECTED',
}

export const dispatchCommandTypeNameMap: {
  [key in CommandType]: string;
} = {
  [CommandType.WATER_PLANT_CONTROL]: '水厂调控',
  [CommandType.PUMP_STATION_CONTROL]: '泵站调控',
  [CommandType.VALVE_CONTROL]: '阀门调控',
};

export const dispatchCommandSendOrganizationNameMap: {
  [key in DispatchCommandSendOrganization]: string;
} = {
  [DispatchCommandSendOrganization.COMMAND_CENTER]: '指挥中心',
};

export const dispatchCommandObjectNameMap: {
  [key in DispatchCommandCommandObject]: string;
} = {
  [DispatchCommandCommandObject.WATER_PUMP]: '水泵开关',
  [DispatchCommandCommandObject.FLOW_ADJUSTMENT]: '流量调整',
  [DispatchCommandCommandObject.PRESSURE_ADJUSTMENT]: '压力调整',
  [DispatchCommandCommandObject.OTHER_COMMAND]: '其他指令',
  [DispatchCommandCommandObject.VALVE_CONTROL]: '阀门开关',
};

export const dispatchCommandStateNameMap: {
  [key in DispatchCommandState]: string;
} = {
  [DispatchCommandState.SENT]: '已发送',
  [DispatchCommandState.UNREAD]: '未读',
  [DispatchCommandState.READ_ACCEPTED]: '指令接收',
  [DispatchCommandState.NOT_REPLIED]: '未回复',
  [DispatchCommandState.REPLIED]: '指令回复',
  [DispatchCommandState.REJECTED]: '指令拒绝',
};

export const dispatchCommandStateColorMap: {
  [key in DispatchCommandState]?: string;
} = {
  [DispatchCommandState.SENT]: 'processing',
  [DispatchCommandState.READ_ACCEPTED]: 'warning',
  [DispatchCommandState.REPLIED]: 'success',
  [DispatchCommandState.REJECTED]: 'error',
};

export const dispatchCommandTypeOptions = Object.values(CommandType).map(
  (value) => ({
    label: dispatchCommandTypeNameMap[value],
    value,
  }),
);

export const dispatchCommandSendOrganizationOptions = Object.values(
  DispatchCommandSendOrganization,
).map((value) => ({
  label: dispatchCommandSendOrganizationNameMap[value],
  value,
}));

export const dispatchCommandObjectOptions = Object.values(
  DispatchCommandCommandObject,
).map((value) => ({
  label: dispatchCommandObjectNameMap[value],
  value,
}));

export const dispatchCommandStateOptions = Object.values(
  DispatchCommandState,
).map((value) => ({
  label: dispatchCommandStateNameMap[value],
  value,
}));

export interface CreateDispatchCommandParams {
  /** 指令类型 */
  type?: CommandType;
  /** 指令发送组织 */
  sendOrganization?: string;
  /** 指令执行组织 */
  receiveOrganization?: string;
  /** 指令对象 */
  object?: DispatchCommandCommandObject;
  /** 指令状态 */
  state?: DispatchCommandState;
  /** 指令内容 */
  content?: string;
  /** 指令计划执行时间 */
  planTime?: string;
  /** 指令备注 */
  note?: string;
}

export interface DeleteDispatchCommandParams {
  /** 指令id列表 */
  idList?: string[];
}

export interface DispatchCommandListParams {
  /** 指令id */
  id?: string;
  /** 指令类型 */
  type?: CommandType;
  /** 指令执行组织 */
  receiveOrganization?: string | string[];
  /** 指令发送开始时间 */
  sendStartTime?: string;
  /** 指令发送结束时间 */
  sendEndTime?: string;
  /** 指令接收开始时间 */
  receiveStartTime?: string;
  /** 指令接收结束时间 */
  receiveEndTime?: string;
  /** 指令状态 */
  state?: DispatchCommandState[];
  /** 过滤仅当前用户指令 */
  filterCurrentUser?: boolean;
}

export interface DispatchCommandList {
  /** 指令id */
  id?: string;
  /** 指令类型 */
  type?: CommandType;
  /** 指令来源 */
  sourceType?: number;
  /** 指令发送组织 */
  sendOrganization?: DispatchCommandSendOrganization;
  /** 指令执行组织 */
  receiveOrganization?: string;
  /** 指令对象 */
  object?: DispatchCommandCommandObject;
  /** 指令状态 */
  state?: DispatchCommandState;
  /** 指令内容 */
  content?: string;
  /** 指令内容渲染 */
  contentRender?: string;
  /** 指令计划执行时间 */
  planTime?: string;
  /** 指令备注 */
  note?: string;
  /** 指令发送时间 */
  sendTime?: string;
  /** 指令发送人 */
  sender?: string;
  /** 指令接收时间 */
  receiveTime?: string;
  /** 指令接收人 */
  receiver?: string;
  /** 指令回复时间 */
  replyTime?: string;
  /** 指令回复内容 */
  replyContent?: string;
  /** 指令回复人 */
  replyPeople?: string;
  /** 执行及时性 */
  timelyExecute?: string;
  /** 执行时间 */
  executeTime?: string;
  /** 执行备注 */
  executeNote?: string;
  /** 关联事件 ID */
  eventName?: string;
  /** 关联事件名称 */
  eventTitle?: string;
  /** 回复及时性 */
  timelyReply?: number;
}

export interface UpdateCommandReceiveInfo {
  /** 指令id */
  id?: string;
  /** 指令状态 */
  state?: DispatchCommandState;
}

export interface UpdateCommandReplyInfo {
  /** 指令id */
  id?: string;
  /** 指令状态 */
  state?: DispatchCommandState;
  /** 指令回复内容 */
  replyContent?: string;
}

export interface UpdateCommandTimelyExecuteInfo {
  /** 指令id */
  id?: string;
  /** 执行及时性 */
  timelyExecute?: string;
  /** 执行时间 */
  executeTime?: string;
  /** 执行备注 */
  executeNote?: string;
}

export const getTimelyExecuteText = (timelyExecute?: string) => {
  if (timelyExecute === null || timelyExecute === undefined) return '---';
  return Number(timelyExecute) === 1 ? '及时执行' : '未及时执行';
};

export const getTimelyReplyText = (timelyReply?: number) => {
  if (timelyReply === null || timelyReply === undefined) return '---';
  return timelyReply === 0 ? '未及时回复' : '及时回复';
};

export const getCommandSourceTypeText = (
  sourceType: number,
): string | number => {
  switch (sourceType) {
    case 1:
      return '人工指令';
    case 2:
      return '智能调度';
    case 3:
      return '智能调度调整';
    default:
      return sourceType ?? '';
  }
};
