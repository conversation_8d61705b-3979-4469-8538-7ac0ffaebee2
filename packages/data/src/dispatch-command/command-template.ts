/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from '../database';
import { DispatchCommandList } from './command-list';
import {
  CommandContent,
  CreateCommandList,
  DeviceAction,
  deviceActionNameMap,
  getContentDescriptions,
} from './create-command';

/**
 * 模板元素类型
 */
export enum TemplateElementType {
  /** 固定文字 */
  TEXT = 'TEXT',
  /** 系统变量 */
  VARIABLE = 'VARIABLE',
  /** 指令描述 */
  DESCRIPTION = 'DESCRIPTION',
}

/**
 * 系统支持的变量类型
 */
export enum SystemVariableType {
  /** 接收站点 */
  DEVICE_TITLE = 'DEVICE_TITLE',
  /** 计划时间 */
  PLAN_TIME = 'PLAN_TIME',
  /** 备注 */
  NOTE = 'NOTE',
}

/**
 * 系统变量定义
 */
export interface SystemVariable {
  /** 变量类型 */
  type: SystemVariableType;
  /** 显示名称 */
  label: string;
  /** 变量描述 */
  description: string;
  /** 是否必填 */
  required: boolean;
  /** 格式化函数 */
  formatter?: (value: any, data?: any) => string;
}

/**
 * 模板元素
 */
export interface TemplateElement {
  /** 元素ID */
  id: string;
  /** 元素类型 */
  type: TemplateElementType;
  /** 元素内容 */
  content: string;
  /** 变量类型（当type为VARIABLE时使用） */
  variableType?: SystemVariableType;
  /** 元素排序 */
  order: number;
}

/**
 * 调度指令模板配置
 */
export interface CommandTemplateConfig {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description?: string;
  /** 模板元素列表 */
  elements: TemplateElement[];
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 模板配置管理 - 每种指令类型对应一个模板配置
 */
export type CommandTemplateManagerConfig = Partial<
  Record<DeviceAction, CommandTemplateConfig>
>;

/**
 * 系统变量定义映射
 */
export const SYSTEM_VARIABLES: Record<SystemVariableType, SystemVariable> = {
  [SystemVariableType.DEVICE_TITLE]: {
    type: SystemVariableType.DEVICE_TITLE,
    label: '接收站点',
    description: '指令接收站点名称',
    required: true,
    formatter: (value: string) => value || '',
  },
  [SystemVariableType.PLAN_TIME]: {
    type: SystemVariableType.PLAN_TIME,
    label: '计划时间',
    description: '指令计划执行时间',
    required: true,
    formatter: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm'),
  },
  [SystemVariableType.NOTE]: {
    type: SystemVariableType.NOTE,
    label: '备注',
    description: '指令备注信息',
    required: false,
    formatter: (value: string) => value || '',
  },
};

/**
 * 获取系统变量选项
 */
export const getSystemVariableOptions = () =>
  Object.values(SYSTEM_VARIABLES).map((variable) => ({
    label: variable.label,
    value: variable.type,
    description: variable.description,
    required: variable.required,
  }));

/**
 * 获取指令类型的默认模板
 * @param deviceAction 指令类型
 * @returns 默认模板配置
 */
export const getDefaultTemplateForDeviceAction = (
  deviceAction: DeviceAction,
): CommandTemplateConfig => ({
  id: `default-template-${deviceAction}`,
  name: `${deviceActionNameMap[deviceAction]}模板`,
  description: `${deviceActionNameMap[deviceAction]}的指令模板`,
  elements: [
    {
      id: 'element-1',
      type: TemplateElementType.TEXT,
      content: '请',
      order: 1,
    },
    {
      id: 'element-2',
      type: TemplateElementType.VARIABLE,
      content: '',
      variableType: SystemVariableType.DEVICE_TITLE,
      order: 2,
    },
    {
      id: 'element-3',
      type: TemplateElementType.TEXT,
      content: '在',
      order: 3,
    },
    {
      id: 'element-4',
      type: TemplateElementType.VARIABLE,
      content: '',
      variableType: SystemVariableType.PLAN_TIME,
      order: 4,
    },
    {
      id: 'element-5',
      type: TemplateElementType.TEXT,
      content: '将',
      order: 5,
    },
    {
      id: 'element-6',
      type: TemplateElementType.DESCRIPTION,
      content: '',
      order: 6,
    },
  ],
  createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
});

/**
 * 渲染模板数据
 */
export interface TemplateRenderData {
  /** 接收站点 */
  deviceTitle: string;
  /** 计划时间 */
  planTime: string;
  /** 备注 */
  note?: string;
  /** 指令描述数组 */
  descriptionArr: string[];
}

/**
 * 获取模板渲染数据
 * @param data 指令数据
 * @param curDb 当前数据库
 * @param commandDeviceTitle 指令接收站点名称
 * @returns 模板渲染数据
 */
export const getTemplateRenderData = (
  data: CreateCommandList | DispatchCommandList | undefined,
  curDb: Database,
  commandDeviceTitle: string,
): TemplateRenderData => {
  const content: CommandContent =
    typeof data?.content === 'string'
      ? JSON.parse(data.content)
      : data?.content;

  const contentArr = Array.isArray(content) ? content : [content];

  return {
    deviceTitle: commandDeviceTitle,
    planTime: data?.planTime || '',
    note: data?.note,
    descriptionArr: getContentDescriptions(data?.object, contentArr, curDb),
  };
};

/**
 * 渲染调度指令模板
 * @param template 模板配置
 * @param data 渲染数据
 * @returns 渲染后的指令描述
 */
export const renderCommandTemplate = (
  template: CommandTemplateConfig | undefined,
  data: TemplateRenderData,
): string => {
  if (!template) {
    // 如果没有传入模板，使用默认模板
    const defaultTemplate = getDefaultTemplateForDeviceAction(
      DeviceAction.OTHER_COMMAND,
    );
    return renderCommandTemplate(defaultTemplate, data);
  }

  if (!template.elements || template.elements.length === 0) return '-';

  const sortedElements = [...template.elements].sort(
    (a, b) => a.order - b.order,
  );

  return sortedElements
    .map((element) => {
      switch (element.type) {
        case TemplateElementType.TEXT:
          return element.content;

        case TemplateElementType.VARIABLE: {
          if (!element.variableType) return '';
          const variable = SYSTEM_VARIABLES[element.variableType];
          if (!variable) return '';

          switch (element.variableType) {
            case SystemVariableType.DEVICE_TITLE:
              return variable.formatter
                ? variable.formatter(data.deviceTitle)
                : data.deviceTitle;
            case SystemVariableType.PLAN_TIME:
              return variable.formatter
                ? variable.formatter(data.planTime)
                : data.planTime;
            case SystemVariableType.NOTE:
              return variable.formatter
                ? variable.formatter(data.note)
                : data.note || '';
            default:
              return '';
          }
        }

        case TemplateElementType.DESCRIPTION:
          return data.descriptionArr.join(';');

        default:
          return '';
      }
    })
    .filter(Boolean)
    .join('');
};

export const getCommandContentRenderText = (
  data: DispatchCommandList,
  db: Database,
  template: CommandTemplateManagerConfig | undefined,
): string => {
  const renderData = getTemplateRenderData(
    data,
    db,
    data.receiveOrganization
      ? (db.getDeviceById(data.receiveOrganization)?.title ?? '')
      : '',
  );

  return renderCommandTemplate(
    template?.[data.object as unknown as DeviceAction],
    renderData,
  );
};

/**
 * 使用模板格式化指令描述
 * @param data 指令数据
 * @param curDb 当前数据库
 * @param commandDeviceTitle 指令接收站点
 * @param template 模板配置（可选，不传则使用默认模板）
 * @returns 指令描述
 */
export const formatCommandDescriptionWithTemplate = (
  data: CreateCommandList | DispatchCommandList | undefined,
  curDb: Database,
  commandDeviceTitle: string,
  template?: CommandTemplateConfig,
): string => {
  if (!data) return '';

  const content: CommandContent =
    typeof data?.content === 'string'
      ? JSON.parse(data.content)
      : data?.content;

  if (!content) return '';

  const contentArr = Array.isArray(content) ? content : [content];
  const descriptionStrArr = getContentDescriptions(
    data?.object,
    contentArr,
    curDb,
  );

  const renderData: TemplateRenderData = {
    deviceTitle: commandDeviceTitle,
    planTime: data.planTime || '',
    note: data.note,
    descriptionArr: descriptionStrArr,
  };

  const templateToUse =
    template ||
    getDefaultTemplateForDeviceAction(
      (data.object as DeviceAction) || DeviceAction.OTHER_COMMAND,
    );
  let result = renderCommandTemplate(templateToUse, renderData);

  // 如果有备注，按照原逻辑添加备注
  if (data.note && !template) {
    result = `${result}(备注: ${data.note})`;
  }

  return result;
};

/**
 * 创建新的模板元素
 * @param type 元素类型
 * @param content 元素内容
 * @param variableType 变量类型（可选）
 * @returns 新的模板元素
 */
export const createTemplateElement = (
  type: TemplateElementType,
  content: string,
  variableType?: SystemVariableType,
): Omit<TemplateElement, 'id' | 'order'> => ({
  type,
  content,
  variableType,
});

/**
 * 验证模板配置
 * @param template 模板配置
 * @returns 验证结果
 */
export const validateTemplate = (
  template: CommandTemplateConfig,
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!template.name?.trim()) {
    errors.push('模板名称不能为空');
  }

  if (!template.elements || template.elements.length === 0) {
    errors.push('模板元素不能为空');
  }

  // 检查是否包含指令描述元素
  const hasDescriptionElement = template.elements.some(
    (element) => element.type === TemplateElementType.DESCRIPTION,
  );
  if (!hasDescriptionElement) {
    errors.push('模板必须包含指令描述元素');
  }

  // 检查变量元素是否有有效的变量类型
  template.elements.forEach((element, index) => {
    if (
      element.type === TemplateElementType.VARIABLE &&
      !element.variableType
    ) {
      errors.push(`第${index + 1}个变量元素缺少变量类型`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};
