/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from '../database';
import { DepartmentInfo } from '../department';
import { PropertyValue } from '../device';
import { makeId, splitId } from '../object-item';
import { PumpData } from '../scheduling-data';
import {
  DispatchCommandCommandObject,
  DispatchCommandList,
} from './command-list';

export enum CommandType {
  WATER_PLANT_CONTROL = 'WATER_PLANT_CONTROL',
  PUMP_STATION_CONTROL = 'PUMP_STATION_CONTROL',
  VALVE_CONTROL = 'VALVE_CONTROL',
}

export enum CommandEventType {
  WATER_PLANT = 'waterPlant',
  PUMP_STATION = 'pumpStation',
  VALVE = 'valve',
}

export enum DeviceAction {
  WATER_PUMP = 'WATER_PUMP',
  FLOW_ADJUSTMENT = 'FLOW_ADJUSTMENT',
  PRESSURE_ADJUSTMENT = 'PRESSURE_ADJUSTMENT',
  OTHER_COMMAND = 'OTHER_COMMAND',
  VALVE_CONTROL = 'VALVE_CONTROL',
}

export enum CommandState {
  SENT = 'SENT',
  UNREAD = 'UNREAD',
  READ_ACCEPTED = 'READ_ACCEPTED',
  NOT_REPLIED = 'NOT_REPLIED',
  REPLIED = 'REPLIED',
  REJECTED = 'REJECTED',
}

export enum CommandSendOrganization {
  COMMAND_CENTER = 'COMMAND_CENTER',
}

export const commandTypeNameMap: {
  [key in CommandType]: string;
} = {
  [CommandType.WATER_PLANT_CONTROL]: '水厂调控',
  [CommandType.PUMP_STATION_CONTROL]: '泵站调控',
  [CommandType.VALVE_CONTROL]: '阀门调控',
};

export const deviceActionNameMap: {
  [key in DeviceAction]: string;
} = {
  [DeviceAction.WATER_PUMP]: '水泵开关',
  [DeviceAction.FLOW_ADJUSTMENT]: '流量调整',
  [DeviceAction.PRESSURE_ADJUSTMENT]: '压力调整',
  [DeviceAction.OTHER_COMMAND]: '其他指令',
  [DeviceAction.VALVE_CONTROL]: '阀门开关',
};

export const commandTypeOptions = Object.values(CommandType).map((value) => ({
  label: commandTypeNameMap[value],
  value,
}));

export const deviceActionOptions = Object.values(DeviceAction).map((value) => ({
  label: deviceActionNameMap[value],
  value,
}));

export enum CommandMode {
  MIN_MAX = 'min_max',
  THRESHOLD = 'threshold',
}

export type CommandPumpData = {
  otype: string;
  oname: string;
  precision: number;
  symbol: string;
} & PumpData;

export type CommandPropertyValue = {
  description: string | undefined;
  value: string | number | undefined;
  mode: CommandMode;
  minValue: number | undefined;
  maxValue: number | undefined;
  symbol: string;
  defaultRange: number;
} & PropertyValue;

export type CommandValvePropertyValue = {
  valueType: 'gatevalve' | 'controlvalve';
  description: string;
  otype: string;
  oname: string;
  diameter: string;
};

interface CommandContentItem {
  title: string;
  value?: number | string;
  minValue?: number | string;
  maxValue?: number | string;
  mode?: number | string;
  otype: string;
  oname: string;
  range?: number | string;
  pumprun?: number;
  variable?: boolean;
  status?: number;
  index?: number;
}

export type CommandContent = CommandContentItem | CommandContentItem[];

export interface WaterPump {
  title: string;
  value: number;
  otype: string;
  oname: string;
  mode: CommandMode;
  minValue: number | undefined;
  maxValue: number | undefined;
  range?: number;
  pumprun?: number;
  variable?: boolean;
  status?: number;
}

export interface GroupValueProps {
  [key: string]: {
    vprop: string;
    otype: string;
    oname: string;
  };
}

export interface WaterPumpData {
  key: string;
  name: string;
  currentValue?: string | number;
  newValue?: number;
  minFrequency?: number | null;
  maxFrequency?: number | null;
  mode?: CommandMode;
  minValue?: number;
  maxValue?: number;
  variable?: boolean;
  rangeRate?: number;
  unit: string;
  precision?: number;
  selected: boolean;
  otype: string;
  oname: string;
  isChanged: boolean;
  pumprun?: number;
  status?: number;
}

export interface FlowPressureRateData {
  key: string;
  name: string;
  currentRate?: string | number;
  mode?: CommandMode;
  minValue?: number;
  maxValue?: number;
  newRate?: number;
  rangeRate?: number;
  unit: string;
  selected: boolean;
  isChanged: boolean;
}

export interface ValveOperationValue {
  id: string;
  key: string;
  name: string;
  description: string;
  currentState?: string;
  mode?: CommandMode;
  minValue?: number;
  maxValue?: number;
  newRate?: number;
  rangeRate?: number;
  selected: boolean;
  isChanged: boolean;
  valueType: 'gatevalve' | 'controlvalve';
  diameter: string;
}

export interface ConvertCreateCommandForm {
  type: CommandType;
  receiveOrganization: string;
  deviceAction?: DeviceAction;
  waterPump?: WaterPumpData[];
  flow?: FlowPressureRateData[];
  pressure?: FlowPressureRateData[];
  valve?: ValveOperationValue[];
  other?: string;
  planTime: string;
  note?: string;
  notification?: boolean;
  customValve?: string;
  diameter?: number;
}

export interface CreateCommandForm {
  type: CommandType;
  receiveOrganization: string;
  deviceAction?: DeviceAction;
  waterPump?: WaterPump[];
  flow?: Record<string, Record<string, number | CommandMode> | undefined>;
  pressure?: Record<string, Record<string, number | CommandMode> | undefined>;
  valve?: Record<string, Record<string, number | CommandMode> | undefined>;
  other?: string;
  planTime: string;
  note?: string;
  notificationDepartment?: string[];
  notificationUser?: string[];
  notificationType?: string[];
}

export interface CreateCommandList {
  id: string;
  type: CommandType;
  sendOrganization: CommandSendOrganization;
  receiveOrganization: string;
  object?: DeviceAction;
  content?: CommandContent;
  state?: CommandState;
  planTime: string;
  note?: string;
  sendTime?: string;
  notificationDepartment?: string[];
  notificationUser?: string[];
  notificationType?: string[];
  dispatchSuggestId?: string;
  sourceType?: number;
}

export interface CommandList extends CreateCommandList {
  sender?: string;
  receiveTime?: string;
  receiver?: string;
  replyTime?: string;
  replyContent?: string;
  replyPeople?: string;
  eventName?: string;
  eventTitle?: string;
}

export interface SDVALItem {
  otype: string;
  oname: string;
  description: string;
  frequencyMode: 'Fixed' | 'Variable';
  maxFrequency: number | null;
  minFrequency: number | null;
  defaultRange: number;
}

export interface ValvePropertyValue {
  valueType: 'gatevalve' | 'controlvalve';
  description: string;
  otype: string;
  oname: string;
  diameter: string;
}

export interface ScheduleMode {
  factType: string;
  factName: string;
  factTitle: string;
  [DeviceAction.WATER_PUMP]: SDVALItem[];
  [DeviceAction.FLOW_ADJUSTMENT]: CommandPropertyValue[];
  [DeviceAction.PRESSURE_ADJUSTMENT]: CommandPropertyValue[];
  [DeviceAction.VALVE_CONTROL]: ValvePropertyValue[];
}

export interface DeviceOption {
  label: string;
  value: string;
  data: ScheduleMode | null;
  disabled: boolean;
}

export interface CommandDevice {
  ptype: string;
  pname: string;
  otype: string;
  oname: string;
  title: string;
  scheduleMode: ScheduleMode | null;
}

export interface CommandValveList {
  receiver: string;
  valveOperation: ValvePropertyValue[];
  allowCustom: boolean;
}

export interface DispatchCommandConfiguration {
  waterPlants: CommandDevice[];
  pumpStations: CommandDevice[];
  valveList: CommandValveList[];
}

export interface CommandReceiveUser {
  departmentId: string;
  departmentName: string;
  finalDepartmentId?: string;
}

export const convertCommandValve = (item: any[]): ValvePropertyValue[] =>
  (item ?? [])?.map((item: any) => ({
    ...item,
    valueType: item.valvetype,
  })) || [];

export const convertScheduleMode = (item: any): ScheduleMode | null => {
  if (!item.SCHEDULE_MODE) return null;

  let scheduleModeObj: any = item.SCHEDULE_MODE;

  if (typeof item.SCHEDULE_MODE === 'string') {
    try {
      scheduleModeObj = JSON.parse(item.SCHEDULE_MODE);
    } catch (error) {
      console.error('JSON 解析错误:', error);
      console.log('原始 SCHEDULE_MODE 字符串:', item.SCHEDULE_MODE);
      return null;
    }
  }

  return {
    factType: item.OTYPE,
    factName: item.ONAME,
    factTitle: item.TITLE,
    [DeviceAction.WATER_PUMP]:
      scheduleModeObj.WATER_PUMP?.map((item: any) => ({
        ...item,
        frequencyMode: item.frequencymode,
        maxFrequency: item.maxfrequency ?? null,
        minFrequency: item.minfrequency ?? null,
        defaultRange: item.defaultrange ?? 0,
      })) || [],
    [DeviceAction.FLOW_ADJUSTMENT]:
      scheduleModeObj.FLOW_ADJUSTMENT?.map((item: any) => ({
        ...item,
        defaultRange: item.defaultrange ?? 0,
      })) || [],
    [DeviceAction.PRESSURE_ADJUSTMENT]:
      scheduleModeObj.PRESSURE_ADJUSTMENT?.map((item: any) => ({
        ...item,
        defaultRange: item.defaultrange ?? 0,
      })) || [],
    [DeviceAction.VALVE_CONTROL]: convertCommandValve(
      scheduleModeObj.VALVE_OPERATION,
    ),
  };
};

export const convertCommandDevice = (item: any): CommandDevice => ({
  ptype: item.PTYPE,
  pname: item.PNAME,
  otype: item.OTYPE,
  oname: item.ONAME,
  title: item.TITLE,
  scheduleMode: convertScheduleMode(item),
});

export const formatCommandUnit = (otype: string, curDb: Database) =>
  curDb.getUnitFormat(otype, 'SDVAL');

export const generateDeviceActionOptions = (data: ScheduleMode) => {
  const actions = new Set<DeviceAction>();
  Object.keys(data).forEach((actionKey) => {
    if (actionKey in DeviceAction) {
      actions.add(actionKey as DeviceAction);
    }
  });

  if (actions.size > 0) {
    actions.add(DeviceAction.OTHER_COMMAND);
  }

  return Array.from(actions).map((value) => ({
    label: deviceActionNameMap[value],
    value,
  }));
};

export const convertPumpData = (
  originalData: SDVALItem[],
  latestValues: Map<string, PropertyValue> | undefined,
  curDb: Database,
): CommandPumpData[] =>
  originalData.map((item) => {
    const latestValue = latestValues?.get(
      makeId(item.otype, item.oname, 'SDVAL'),
    );
    return {
      oname: item.oname,
      otype: item.otype,
      pumpName: item.description,
      pumpPattern: item.oname,
      defaultValue:
        (formatCommandUnit(item.otype, curDb)?.getValue(
          Number(latestValue?.value ?? 0),
        ) as number) ?? 0,
      value:
        (formatCommandUnit(item.otype, curDb)?.getValue(
          Number(latestValue?.value ?? 0),
        ) as number) ?? 0,
      minFrequency: item.minFrequency,
      maxFrequency: item.maxFrequency,
      defaultRange: item.defaultRange,
      variable: item.frequencyMode === 'Variable',
      precision: formatCommandUnit(item.otype, curDb)?.valuePrecision ?? 2,
      symbol: formatCommandUnit(item.otype, curDb)?.unitSymbol ?? '',
    };
  });

export const convertDeviceIndicators = (
  originalData: CommandPropertyValue[],
  latestValues: Map<string, PropertyValue> | undefined,
  curDb: Database,
): CommandPropertyValue[] =>
  originalData
    .map((device) => {
      const key = makeId(device.otype, device.oname, 'SDVAL');
      const latestValue = latestValues?.get(key);

      if (latestValue) {
        return {
          ...device,
          value:
            formatCommandUnit(device.otype, curDb)?.getValue(
              latestValue?.value ?? '',
            ) ?? undefined,
          symbol: formatCommandUnit(device.otype, curDb)?.unitSymbol ?? '',
          defaultRange: device.defaultRange,
        };
      }
      return null;
    })
    .filter(Boolean) as CommandPropertyValue[];

export const convertValveData = (
  originalData: ValvePropertyValue[],
): CommandValvePropertyValue[] =>
  originalData.map((item) => ({
    valueType: item.valueType,
    description: item.description,
    otype: item.otype,
    oname: item.oname,
    diameter: item.diameter,
    value: null,
  }));

export const generateCommandContent = (
  commandForm: CreateCommandForm,
  curDb: Database,
): CommandContentItem[] | null => {
  const mapEntriesToCommandContent = (
    entries: [string, Record<string, number | CommandMode> | undefined][] | [],
  ) =>
    entries.map(([key, value]) => {
      if (value?.mode === CommandMode.MIN_MAX) {
        return {
          title: splitId(key)[2],
          mode: CommandMode.MIN_MAX,
          maxValue:
            formatCommandUnit(splitId(key)[0], curDb)?.getOriginalValue(
              value?.maxValue ?? 0,
            ) ?? 0,
          minValue:
            formatCommandUnit(splitId(key)[0], curDb)?.getOriginalValue(
              value?.minValue ?? 0,
            ) ?? 0,
          otype: splitId(key)[0],
          oname: splitId(key)[1],
        };
      }
      return {
        title: splitId(key)[2],
        value:
          formatCommandUnit(splitId(key)[0], curDb)?.getOriginalValue(
            value?.value ?? 0,
          ) ?? 0,
        range:
          formatCommandUnit(splitId(key)[0], curDb)?.getOriginalValue(
            value?.range ?? 0,
          ) ?? 0,
        mode: CommandMode.THRESHOLD,
        otype: splitId(key)[0],
        oname: splitId(key)[1],
      };
    });

  const mapEntriesToValveCommandContent = (
    entries: [string, Record<string, number | CommandMode> | undefined][] | [],
  ) =>
    entries.map(([key, value]) => ({
      title: splitId(key)[2],
      value: value?.value ?? 0,
      range: value?.range ?? 0,
      otype: splitId(key)[0],
      oname: splitId(key)[1],
    }));

  switch (commandForm.deviceAction) {
    case DeviceAction.WATER_PUMP:
      return (
        commandForm?.waterPump?.map((item) => {
          if (item?.mode === CommandMode.MIN_MAX) {
            return {
              title: item.title,
              otype: item.otype,
              oname: item.oname,
              pumprun: item.pumprun,
              status: item.status,
              variable: item.variable,
              mode: CommandMode.MIN_MAX,
              maxValue: item.maxValue,
              minValue: item.minValue,
            };
          }
          return {
            title: item.title,
            mode: CommandMode.THRESHOLD,
            value: item.value ?? 0,
            range: item.range ?? 0,
            otype: item.otype,
            oname: item.oname,
            variable: item.variable,
            pumprun: item.pumprun,
            status: item.status,
          };
        }) ?? null
      );
    case DeviceAction.FLOW_ADJUSTMENT: {
      const entries = Object.entries(commandForm.flow ?? {});
      return mapEntriesToCommandContent(entries ?? []);
    }
    case DeviceAction.PRESSURE_ADJUSTMENT: {
      const entries = Object.entries(commandForm.pressure ?? {});
      return mapEntriesToCommandContent(entries ?? []);
    }
    case DeviceAction.VALVE_CONTROL: {
      const entries = Object.entries(commandForm.valve ?? {});
      return mapEntriesToValveCommandContent(entries ?? []);
    }
    case DeviceAction.OTHER_COMMAND: {
      const organization = Array.isArray(commandForm.receiveOrganization)
        ? commandForm.receiveOrganization
        : [commandForm.receiveOrganization];
      return (
        organization.map(() => ({
          title: '',
          value: commandForm.other ?? '',
          otype: '',
          oname: '',
        })) ?? null
      );
    }
    default:
      return null;
  }
};

function formatContent(
  content: CommandContentItem | CommandContentItem[] | null,
): CommandContentItem[] {
  if (!content) return [];
  return Array.isArray(content) ? content : [content];
}

export const createNewDataSource = (
  formValues: CreateCommandForm,
  content: CommandContentItem | CommandContentItem[] | null,
  index?: number,
): CreateCommandList => ({
  id: Math.random().toString(),
  type: formValues.type,
  sendOrganization: CommandSendOrganization.COMMAND_CENTER,
  receiveOrganization: Array.isArray(formValues.receiveOrganization)
    ? formValues.receiveOrganization[index ?? 0]
    : formValues.receiveOrganization,
  object: formValues.deviceAction,
  state: CommandState.SENT,
  planTime: dayjs(formValues.planTime).format('YYYY-MM-DD HH:mm'),
  note: formValues.note,
  content: formatContent(content),
  notificationDepartment: formValues.notificationDepartment,
  notificationUser: formValues.notificationUser,
  notificationType: formValues.notificationType,
});

function getWaterPumpContentDesc(
  data: CommandContentItem[],
  db: Database,
): string[] {
  const descriptionStr: string[] = [];
  data.forEach((content) => {
    const value = content?.value;
    const otype = content?.otype;
    const range = content?.range ?? 0;
    const pumprun = content?.pumprun;
    const status = content?.status;
    const minValue = content?.minValue;
    const maxValue = content?.maxValue;
    const variable = content?.variable;
    const mode = content?.mode ?? CommandMode.THRESHOLD;
    const unit = formatCommandUnit(otype, db)?.unitSymbol ?? '';
    let descStr = '';
    if (value === 0 || status === 0) {
      descStr = '关闭';
    } else if (variable && mode === CommandMode.MIN_MAX) {
      const convertMinValue = formatCommandUnit(otype, db)?.getValue(
        minValue ?? 0,
      );
      const convertMaxValue = formatCommandUnit(otype, db)?.getValue(
        maxValue ?? 0,
      );
      descStr += `开启, 频率调整为${convertMinValue}~${convertMaxValue}${unit}`;
    } else if (typeof value === 'number' && value > 1) {
      const convertValue = formatCommandUnit(otype, db)?.getValue(value);
      const convertRange = formatCommandUnit(otype, db)?.getValue(range);
      if (pumprun === 1) {
        descStr += '开启, 频率调整为';
      } else {
        descStr += '频率调整为';
      }
      descStr += `${convertValue}${range ? `±${convertRange}` : ''}${unit}`;
    } else {
      descStr = '开启';
    }
    descriptionStr.push(`水泵${content.title}${descStr}`);
  });
  return descriptionStr;
}
function getFlowContentDesc(
  data: CommandContentItem[],
  db: Database,
): string[] {
  const descriptionStr: string[] = [];
  data.forEach((content) => {
    const value = content?.value;
    const minValue = content?.minValue;
    const maxValue = content?.maxValue;
    const mode = content?.mode;
    const otype = content?.otype;
    const range = content?.range ?? 0;
    const unit = formatCommandUnit(otype, db)?.unitSymbol ?? '';
    const convertMinValue = formatCommandUnit(otype, db)?.getValue(
      minValue ?? 0,
    );
    const convertMaxValue = formatCommandUnit(otype, db)?.getValue(
      maxValue ?? 0,
    );
    const convertValue = formatCommandUnit(otype, db)?.getValue(value ?? 0);
    const convertRange = formatCommandUnit(otype, db)?.getValue(range);
    if (mode === CommandMode.MIN_MAX) {
      descriptionStr.push(
        `${content.title}的流量调整为${convertMinValue}~${convertMaxValue}${unit}`,
      );
    } else {
      descriptionStr.push(
        `${content.title}的流量调整为${convertValue}${range ? `±${convertRange}` : ''}${unit}`,
      );
    }
  });

  return descriptionStr;
}
function getPressureContentDesc(
  data: CommandContentItem[],
  db: Database,
): string[] {
  const descriptionStr: string[] = [];
  data.forEach((content) => {
    const value = content?.value;
    const minValue = content?.minValue;
    const maxValue = content?.maxValue;
    const otype = content?.otype;
    const range = content?.range ?? 0;
    const mode = content?.mode;
    const unit = formatCommandUnit(otype, db)?.unitSymbol ?? '';
    const convertMinValue = formatCommandUnit(otype, db)?.getValue(
      minValue ?? 0,
    );
    const convertMaxValue = formatCommandUnit(otype, db)?.getValue(
      maxValue ?? 0,
    );
    const convertValue = formatCommandUnit(otype, db)?.getValue(value ?? 0);
    const convertRange = formatCommandUnit(otype, db)?.getValue(range);
    if (mode === CommandMode.MIN_MAX) {
      descriptionStr.push(
        `${content.title}的压力调整为${convertMinValue}~${convertMaxValue}${unit}`,
      );
    } else {
      descriptionStr.push(
        `${content.title}的压力调整为${convertValue}${range ? `±${convertRange}` : ''}${unit}`,
      );
    }
  });

  return descriptionStr;
}
function getValveContentDesc(data: CommandContentItem[]) {
  const descriptionStr: string[] = [];
  data.forEach((content) => {
    const value = content?.value;
    const range = content?.range;
    let operationStr = value ? '开启' : '关闭';
    if (range) {
      operationStr = value ? '开启至' : '关闭至';
    }
    descriptionStr.push(
      `阀门${content.title}${operationStr}${range ? `(${range})` : ''}`,
    );
  });
  return descriptionStr;
}
function getOtherContentDesc(data: CommandContentItem[]): string[] {
  const descriptionStr: string[] = [];
  data.forEach((content) => {
    descriptionStr.push(content?.value?.toString() ?? '');
  });
  return descriptionStr;
}

export function getContentDescriptions(
  commandObject: DeviceAction | DispatchCommandCommandObject | undefined,
  content: CommandContentItem[],
  db: Database,
): string[] {
  switch (commandObject) {
    case DeviceAction.WATER_PUMP:
      return getWaterPumpContentDesc(content, db);
    case DeviceAction.FLOW_ADJUSTMENT:
      return getFlowContentDesc(content, db);
    case DeviceAction.PRESSURE_ADJUSTMENT:
      return getPressureContentDesc(content, db);
    case DeviceAction.VALVE_CONTROL:
      return getValveContentDesc(content);
    case DeviceAction.OTHER_COMMAND:
      return getOtherContentDesc(content);
    default:
      return [];
  }
}

/**
 * 格式化指令描述
 * @param data 指令数据
 * @param curDb 当前数据库
 * @param commandDeviceTitle 指令设备标题
 * @returns 指令描述
 */
export const formatCommandDescription = (
  data: CreateCommandList | DispatchCommandList | undefined,
  curDb: Database,
  commandDeviceTitle: string,
): string => {
  if (!data) return '';

  const content: CommandContent =
    typeof data?.content === 'string'
      ? JSON.parse(data.content)
      : data?.content;

  if (!content) return '';

  const contentArr = Array.isArray(content) ? content : [content];
  const descriptionStrArr = getContentDescriptions(
    data?.object,
    contentArr,
    curDb,
  );

  const commandDesc = `请${commandDeviceTitle}在${dayjs(data.planTime).format('YYYY-MM-DD HH:mm')}将${descriptionStrArr.join(';')}`;

  return data.note ? `${commandDesc}(备注: ${data.note})` : commandDesc;
};

export interface ActionConfig {
  convertFunction?: (
    originalData: any[],
    latestValues: Map<string, PropertyValue> | undefined,
    curDb: Database,
  ) => any[];
  fieldName?: string;
}

export function filterDevicesByDepartmentNames(
  departments: DepartmentInfo[],
  devices: CommandDevice[],
): CommandDevice[] {
  const departmentNames = new Set(departments.map((dept) => dept.name));
  return devices.filter((device) => departmentNames.has(device.title));
}

export function filterValveListByDepartmentNames(
  departments: DepartmentInfo[],
  valveList: CommandValveList[],
): CommandValveList[] {
  const departmentNames = new Set(departments.map((dept) => dept.name));
  return valveList.filter((valve) => departmentNames.has(valve.receiver));
}

export enum NotificationType {
  SYSTEM = 'system',
  SMS = 'sms',
  WECOM = 'wecom',
}

export const notificationTypeOptions = [
  { label: '系统弹窗', value: NotificationType.SYSTEM },
  { label: '短信', value: NotificationType.SMS },
  { label: '企业微信', value: NotificationType.WECOM },
];

export const getNotificationOptions = (options: string[]) =>
  options.map((item) => {
    switch (item) {
      case NotificationType.SMS:
        return { label: '短信', value: NotificationType.SMS };
      case NotificationType.WECOM:
        return { label: '企业微信', value: NotificationType.WECOM };
      case NotificationType.SYSTEM:
      default:
        return { label: '系统弹窗', value: NotificationType.SYSTEM };
    }
  });

export const getNotificationTypeText = (type?: string): string | undefined => {
  if (!type) return '---';
  if (Number(type) === 1) {
    return notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SYSTEM,
    )?.label;
  }

  if (Number(type) === 2) {
    return notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SMS,
    )?.label;
  }

  if (Number(type) === 3) {
    const systemLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SYSTEM,
    )?.label;
    const smsLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SMS,
    )?.label;
    return `${systemLabel},${smsLabel}`;
  }

  if (Number(type) === 4) {
    return notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.WECOM,
    )?.label;
  }

  if (Number(type) === 5) {
    const systemLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SYSTEM,
    )?.label;
    const wecomLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.WECOM,
    )?.label;
    return `${systemLabel},${wecomLabel}`;
  }

  if (Number(type) === 6) {
    const smsLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.SMS,
    )?.label;
    const wecomLabel = notificationTypeOptions.find(
      (opt) => opt.value === NotificationType.WECOM,
    )?.label;
    return `${smsLabel},${wecomLabel}`;
  }

  return notificationTypeOptions.map((opt) => opt.label).join(',');
};

/**
 * 获取水泵运行状态
 * @param currentValue 当前值
 * @param newValue 新值
 * @param minFrequency 最小频率
 * @returns 1: 开启, 0: 关闭, undefined: 不变
 */
export const getPumpRunStatus = (
  currentValue: number,
  newValue: number,
  minFrequency: number,
): number | undefined => {
  if (currentValue < minFrequency && newValue > minFrequency) {
    return 1;
  }
  if (currentValue > minFrequency && newValue < minFrequency) {
    return 0;
  }
  return undefined;
};
