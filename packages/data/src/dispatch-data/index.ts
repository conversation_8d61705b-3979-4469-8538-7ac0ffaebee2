/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Dayjs } from 'dayjs';

export type DispatchEventType = {
  /** 调度事件类型 */
  type: string;
  /** 调度事件类型名称 */
  title: string;
  /** 子事件类型 */
  children: {
    /** 子事件类型 */
    type: string;
    /** 子事件类型名称 */
    title: string;
  }[];
}[];

export const convertDispatchTypeDataToMap = (
  data: DispatchEventType,
): Record<string, string> =>
  data.reduce(
    (acc, item) => ({ ...acc, [item.type]: item.title }),
    {} as Record<string, string>,
  );

export const createOptionsArrayFromObject = (
  obj: Record<string, string>,
): Array<{ label: string; value: string }> =>
  Object.entries(obj).map(([key, value]) => ({
    label: value,
    value: key,
  }));

export enum DispatchTypeEnum {
  /** 管道工程 */
  PIPE_WORK = 'PIPE_WORK',
  /** 闭水试验 */
  CLOSED_WATER_TEST = 'CLOSED_WATER_TEST',
  /** 电源情况 */
  POWER_SITUATION = 'POWER_SITUATION',
  /** 水厂工程/设备 */
  WATERWORKS_PROJECT = 'WATERWORKS_PROJECT',
  /** 生产调控阀门 */
  PRODUCTION_CONTROL_VALVE = 'PRODUCTION_CONTROL_VALVE',
  /** 水质事件 */
  WATER_QUALITY_EVENT = 'WATER_QUALITY_EVENT',
  /** 预案启动 */
  PLAN_STARTS = 'PLAN_STARTS',
  /** 主控点调控 */
  MASTER_CONTROL_POINT_ADJUSTMENT = 'MASTER_CONTROL_POINT_ADJUSTMENT',
  /** 在线监测设备 */
  ONLINE_DEVICE = 'ONLINE_DEVICE',
  /** 调度指令 */
  DISPATCH_INSTRUCTION = 'DISPATCH_INSTRUCTION',
}

export type DispatchChildrenType = {
  type: string;
  title: string;
};

export interface DispatchType {
  type: DispatchTypeEnum;
  title: string;
  children?: DispatchChildrenType[];
}

export enum EventStatusType {
  DONE = 'DONE',
  DOING = 'DOING',
  PLANNING = 'PLANNING',
}

export const getEventStateTypeName = (eventStateType: string) => {
  switch (eventStateType) {
    case EventStatusType.DONE:
      return '已完成';
    case EventStatusType.DOING:
      return '进行中';
    case EventStatusType.PLANNING:
      return '计划中';
    default:
      return eventStateType;
  }
};

export const getEventStateTypeColor = (eventStateType: string): string => {
  switch (eventStateType) {
    case EventStatusType.DONE:
      return 'success';
    case EventStatusType.DOING:
      return 'processing';
    case EventStatusType.PLANNING:
      return 'warning';
    default:
      return 'error';
  }
};

export const getEventStateTypeOptions = Object.keys(EventStatusType).map(
  (key) => ({
    label: getEventStateTypeName(key),
    value: key,
  }),
);

export const getDispatchTypeTitle = (
  dispatchTypeData: DispatchEventType,
  type: string,
): string => {
  const data = dispatchTypeData.find((item) => item.type === type);
  return data ? data.title : type;
};

export const getDispatchTypeChildren = (
  dispatchTypeData: DispatchEventType,
  type: string | string[],
): DispatchChildrenType[] => {
  if (!type) {
    return [];
  }

  const types = typeof type === 'string' ? [type] : type;
  const allChildren = types.map(
    (t) => dispatchTypeData.find((item) => item.type === t)?.children ?? [],
  );
  const intersection = allChildren.reduce<DispatchChildrenType[]>(
    (acc, children, index) => {
      if (index === 0) {
        return children;
      }
      return acc.filter((item) =>
        children.some((child) => child.type === item.type),
      );
    },
    [],
  );
  const uniqueChildren = Array.from(
    new Set(intersection.map((item) => item.type)),
  ).map((id) => intersection.find((item) => item.type === id)!);

  return uniqueChildren;
};

export const getDispatchTypeOptions = (
  dispatchTypeData: DispatchEventType,
): {
  label: string;
  value: string;
}[] =>
  dispatchTypeData.map((data) => ({
    label: data.title,
    value: data.type,
  }));

export const getDispatchEventTypeOptions = (
  dispatchTypeData: DispatchEventType,
  type: string | string[] | undefined,
): {
  label: string;
  value: string;
}[] => {
  if (!type || type.length === 0) {
    return [];
  }
  return getDispatchTypeChildren(dispatchTypeData, type).map((data) => ({
    label: data.title,
    value: data.type,
  }));
};

export const getDispatchEventTypeTitle = (
  dispatchTypeData: DispatchEventType,
  type: string,
  eventType: string,
): string => {
  const data = getDispatchTypeChildren(dispatchTypeData, type).find(
    (item) => item.type === eventType,
  );
  return data ? data.title : eventType;
};

export interface DispatchData {
  eventId: string;
  oname: string;
  otype: string;
  shape: string;
  address: string;
  description: string;
  dispatchName: string;
  dispatchType: DispatchTypeEnum;
  dispatchEventState: EventStatusType;
  dispatchEventType: string;
  done: boolean;
  startTime: string;
  endTime: string;
  creator: string;
  content: string;
}

export interface DispatchFormValues {
  connectEventId?: string;
  startTime?: Dayjs;
  endTime?: Dayjs;
  dispatchEventState?: string[];
  dispatchName?: string;
  dispatchType?: string[];
  dispatchEventType?: string[];
  eventSubType?: string;
  eventEndTime?: string;
  emergencyEvent?: number;
}
