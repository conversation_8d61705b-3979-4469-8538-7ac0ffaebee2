/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { Shift } from './shift';

/** 根据班次时间和偏移量获取班次的实际开始和结束时间 */
export const getActualShiftTimes = (
  shift: Shift | undefined,
  shiftOffsetMinutes: number,
  dateKey: string,
) => {
  const shiftStart = dayjs(`${dateKey} ${shift?.startTime}`).add(
    shiftOffsetMinutes,
    'minute',
  );
  const shiftEnd = dayjs(`${dateKey} ${shift?.endTime}`).add(
    shiftOffsetMinutes,
    'minute',
  );
  return { shiftStart, shiftEnd };
};

/** 根据班次时间和偏移量获取班次的实际开始和结束时间 */
export const getActualShiftTimesStr = (
  shift: Shift | undefined,
  shiftOffsetMinutes: number,
  dateKey: string,
) => {
  const shiftStart = dayjs(`${dateKey} ${shift?.startTime}`).add(
    shiftOffsetMinutes,
    'minute',
  );
  const shiftEnd = dayjs(`${dateKey} ${shift?.endTime}`).add(
    shiftOffsetMinutes,
    'minute',
  );
  let shiftStartSuffix = '';
  let shiftEndSuffix = '';

  if (shiftStart.format('YYYY-MM-DD') !== dateKey) {
    shiftStartSuffix = shiftStart.isBefore(dateKey) ? ' (前一天)' : ' (后一天)';
  }

  if (shiftEnd.format('YYYY-MM-DD') !== dateKey) {
    shiftEndSuffix = shiftEnd.isBefore(dateKey) ? ' (前一天)' : ' (后一天)';
  }

  const shiftStartStr = shiftStart.format('HH:mm') + shiftStartSuffix;
  const shiftEndStr = shiftEnd.format('HH:mm') + shiftEndSuffix;

  return `${shiftStartStr} - ${shiftEndStr}`;
};
