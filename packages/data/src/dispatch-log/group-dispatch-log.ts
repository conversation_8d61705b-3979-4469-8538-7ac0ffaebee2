/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DispatchData } from '../dispatch-data';
import { HandoverInfoList } from './handover';
import { Shift } from './shift';
import { Weather } from './weather';

export interface GroupedDispatchLog {
  date: string;
  shifts: {
    shift: Shift;
    logs: DispatchData[];
    handovers?: HandoverInfoList[];
  }[];
  weather: Weather;
}

export type MatchingShiftAndLogs = {
  date: string;
  shift: Shift;
  logs: DispatchData[];
  handovers: HandoverInfoList[];
};
