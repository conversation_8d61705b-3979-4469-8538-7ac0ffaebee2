/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface HandoverInfoList {
  /** 交接班id */
  handoverId?: string;
  /** 交接班时间 */
  handoverDate?: string;
  /** 班次 */
  handoverClasses?: string;
  /** 交班人id */
  handoverFromOperator?: string;
  /** 交班人姓名 */
  handoverFromUserName?: string;
  /** 接班人id */
  handoverToOperator?: string;
  /** 接班人姓名 */
  handoverToUserName?: string;
  /** 交接工作内容 */
  handoverContent?: string;
  /** 计划工作内容 */
  planContent?: string;
  /** 注意事项 */
  noteContent?: string;
  /** 备注 */
  remark?: string;
  /** 创建人 */
  userName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 交接状态: 0为待接 1为已接 */
  handoverState?: number;
}

export interface HandoverUserInfo {
  currentClasses: {
    userId: string;
    userName: string;
  }[];
  nextClasses: {
    userId: string;
    userName: string;
  }[];
}
