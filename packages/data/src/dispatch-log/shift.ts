/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import { getActualShiftTimes } from './data';

/** 基本班次信息 */
export interface Shift {
  id?: string;
  /** 班次 ID */
  key: string;
  /** 班次名称 */
  name: string;
  /** 班次开始时间 */
  startTime: string;
  /** 班次结束时间 */
  endTime: string;
  /** 班次所属日期 */
  date: string;
  nextId?: string;
}

/** 综合班次信息 */
export interface ShiftInfo {
  id?: string;
  /** 班次 ID */
  key: string;
  /** 班次名称 */
  name: string;
  /** 班次所属日期 */
  date: string;
  /** 班次开始时间 */
  startTime: string;
  /** 班次结束时间 */
  endTime: string;
  /** 班次所属索引 */
  index: number;
  /** 上一班次开始时间 */
  prevShiftStartTime?: string;
  /** 下一班次开始时间 */
  nextShiftStartTime?: string;
  nextId?: string;
}

/** 通过配置的基本班次信息及可指定的日期获取调整后的全天综合班次信息 */
export const getAllShiftsInfoByDate = (
  scheduleData: {
    shifts: Shift[];
    shiftOffsetMinutes: number;
  },
  date?: Dayjs,
): ShiftInfo[] | null => {
  if (!scheduleData.shifts || scheduleData.shifts.length === 0) return null;

  const targetDate = date ?? dayjs();

  return scheduleData.shifts.map((shift, index) => {
    const { shiftStart, shiftEnd } = getActualShiftTimes(
      shift,
      scheduleData.shiftOffsetMinutes,
      targetDate.format('YYYY-MM-DD'),
    );

    let prevShiftStartTime = null;
    let nextShiftStartTime = null;

    if (index > 0) {
      // 如果不是第一个班次，获取上一个班次的开始时间
      const prevShift = scheduleData.shifts[index - 1];
      prevShiftStartTime = getActualShiftTimes(
        prevShift,
        scheduleData.shiftOffsetMinutes,
        targetDate.format('YYYY-MM-DD'),
      ).shiftStart.format('YYYY-MM-DD HH:mm:ss');
    } else {
      // 如果是第一个班次，获取前一天的最后一个班次的开始时间
      const prevDayLastShift =
        scheduleData.shifts[scheduleData.shifts.length - 1];
      prevShiftStartTime = getActualShiftTimes(
        prevDayLastShift,
        scheduleData.shiftOffsetMinutes,
        targetDate.subtract(1, 'day').format('YYYY-MM-DD'),
      ).shiftStart.format('YYYY-MM-DD HH:mm:ss');
    }

    if (index < scheduleData.shifts.length - 1) {
      // 如果不是最后一个班次，获取下一个班次的开始时间
      const nextShift = scheduleData.shifts[index + 1];
      nextShiftStartTime = getActualShiftTimes(
        nextShift,
        scheduleData.shiftOffsetMinutes,
        targetDate.format('YYYY-MM-DD'),
      ).shiftStart.format('YYYY-MM-DD HH:mm:ss');
    } else {
      // 如果是最后一个班次，获取第二天的第一个班次的开始时间
      const nextDayFirstShift = scheduleData.shifts[0];
      nextShiftStartTime = getActualShiftTimes(
        nextDayFirstShift,
        scheduleData.shiftOffsetMinutes,
        targetDate.add(1, 'day').format('YYYY-MM-DD'),
      ).shiftStart.format('YYYY-MM-DD HH:mm:ss');
    }

    return {
      key: shift.key,
      name: shift.name,
      date: targetDate.format('YYYY-MM-DD'),
      startTime: shiftStart.format('YYYY-MM-DD HH:mm:ss'),
      endTime: shiftEnd.format('YYYY-MM-DD HH:mm:ss'),
      index,
      prevShiftStartTime,
      nextShiftStartTime,
    };
  });
};
