/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';

import { GroupTimeData, TimeData } from '../time-data';

export interface Weather {
  dayWeather: string;
  temperatureMax: string;
  temperatureMin: string;
}

export interface ConvertedWeatherData {
  /** key as date YYYY-MM-DD */
  [key: string]: Weather;
}

export function convertWeatherData(
  rawData?: Map<string, GroupTimeData>,
): ConvertedWeatherData | {} {
  if (!rawData) return {};
  const convertedData: ConvertedWeatherData = {};

  Object.values(rawData).forEach((data) => {
    data.values.forEach((value: TimeData) => {
      const date = dayjs(value.time).format('YYYY-MM-DD');
      if (!convertedData[date]) {
        convertedData[date] = {
          dayWeather: '',
          temperatureMax: '',
          temperatureMin: '',
        };
      }

      switch (data.VPROP) {
        case 'DAY_WEATHER':
          convertedData[date].dayWeather = String(value.value);
          break;
        case 'TEMPERATURE_MAX':
          convertedData[date].temperatureMax = String(value.value);
          break;
        case 'TEMPERATURE_MIN':
          convertedData[date].temperatureMin = String(value.value);
          break;
        default:
          break;
      }
    });
  });

  return convertedData;
}
