/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ContingencyPlanUserRole } from '../contingency-plan/contingency-plan-data';
import { EventRelatedType } from './related-info';

export enum EventStatusType {
  DONE = 'DONE',
  DOING = 'DOING',
  PLANNING = 'PLANNING',
}

export enum EventLayerName {
  MGT_EVENT = 'MGT_EVENT',
  MGT_CONTINGENCY_EVENT = 'MGT_CONTINGENCY_EVENT',
}

export interface EventType {
  /** 事件大类id */
  type: string;
  /** 事件大类名称 */
  title: string;
  /** 是否是默认值 */
  isDefault?: boolean;
  /** 事件小类 */
  children: {
    /** 事件小类id */
    type: string;
    /** 事件小类名称 */
    title: string;
    /** 是否是默认值 */
    isDefault?: boolean;
  }[];
}

export interface LevelType {
  /** 事件级别 */
  type: number;
  /** 事件级别名称 */
  title: string;
  /** 是否是默认值 */
  isDefault?: boolean;
  /** 日志页面级别颜色 */
  color?: string;
}

export interface EventSchedulingBasicInfo {
  /** 事件id */
  eventId: string;
  /** 事件名称 */
  eventTitle: string;
  /** 事件状态 */
  eventStatus: EventStatusType;
  /** 事件大类 */
  eventType: string;
  /** 事件小类 */
  eventSubType: string;
  /** 事件发生地址 */
  eventAddress: string;
  /** 事件描述 */
  eventDescription: string;
  /** 事件发生时间 */
  eventStartTime: string;
  /** 事件结束事件 */
  eventEndTime: string;
  /** 操作人 */
  operator: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 事件上轴状态 */
  isOnTimeline: boolean;
  /** 事件上曲线状态 */
  isOnChart: boolean;
  oname: string;
  otype: string;
  shape: string;
  /** 事件级别 */
  eventLevel: number;
  /** 事件影响人数 */
  affectUserCount?: number;
  /** 标签 */
  label?: string;
  eventSourceType?: EventRelatedType;
  eventSourceId?: string;
  emergencyEvent?: number;
  groupLeader?: string[];
  deputyLeader?: string[];
  members?: string[];
  emergencyUserList?: {
    userName: string;
    roleType: ContingencyPlanUserRole;
    userId: string;
  }[];
}

export const getEventStatusTypeName = (eventStatus: string): string => {
  switch (eventStatus) {
    case EventStatusType.DONE:
      return '已完成';
    case EventStatusType.DOING:
      return '进行中';
    case EventStatusType.PLANNING:
      return '计划中';
    default:
      return eventStatus;
  }
};

export const getEventStatusTypeColor = (eventStatus: string): string => {
  switch (eventStatus) {
    case EventStatusType.DONE:
      return 'success';
    case EventStatusType.DOING:
      return 'processing';
    case EventStatusType.PLANNING:
      return 'warning';
    default:
      return 'error';
  }
};

export interface EventFormOptions {
  label: string;
  value: string | number;
  isDefault?: boolean;
  dependentOptions?: string[];
}
export interface EventFormConfig {
  type: string; // 组件类型
  name: keyof EventSchedulingBasicInfo; // 字段值
  label?: string; // 展示名称
  colSpan?: number; // 栅格占位
  rules?: any[]; // 校验规则
  options?: EventFormOptions[]; // 下拉选项
  dependsOn?: string; // 依赖字段
  dependencies?: string[]; // 依赖字段值
  filePath?: string; // 文件根目录
}

export const getEventStatusOptions: Array<{ label: string; value: string }> =
  Object.keys(EventStatusType).map((key) => ({
    label: getEventStatusTypeName(key),
    value: key,
  }));

export const getEventTypeOptions = (
  data: EventType[],
): Array<{ label: string; value: string }> =>
  data?.map((item) => ({
    label: item.title,
    value: item.type,
  })) ?? [];

export const getEventSubTypeOptions = (
  data: EventType[],
  type: string,
): Array<{ label: string; value: string }> => {
  const event = data?.find((item) => item.type === type);
  return event?.children
    ? event.children.map((item) => ({
        label: item.title,
        value: item.type,
      }))
    : [];
};

export const getEventLevelOptions = (
  data: LevelType[],
): Array<{ label: string; value: number }> =>
  data?.map((item) => ({
    label: item.title,
    value: item.type,
  })) ?? [];

export const getEventTypeName = (type: string, data: EventType[]): string => {
  const event = data?.find((item) => item.type === type);
  return event?.title ?? type;
};

export const getEventLevelName = (
  level: string | number,
  data: LevelType[],
): string => {
  const event = data?.find((item) => item.type === Number(level));
  return event?.title ?? level?.toString();
};

export const getEventSubTypeName = (
  type: string,
  data: EventType[],
): string => {
  const event = data.find((item) =>
    item.children?.some((child) => child.type === type),
  );
  return event?.children?.find((child) => child.type === type)?.title ?? type;
};

export const getEventLevelColor = (
  data: LevelType[],
  level: number,
): string => {
  const event = data?.find((item) => item.type === level);
  return event?.color ?? '';
};
