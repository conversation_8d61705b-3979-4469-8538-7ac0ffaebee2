/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import {
  EventSchedulingBasicInfo,
  EventType,
  getEventLevelName,
  getEventStatusTypeName,
  getEventSubTypeName,
  getEventTypeName,
  LevelType,
} from './basic-info';

export enum EventSchedulingContentType {
  ATTRIBUTE = 'ATTRIBUTE',
  CONTENT = 'CONTENT',
  ASSOCIATION = 'ASSOCIATION',
}

export const changeTypeInAttributeMap: Record<string, string> = {
  ADD: '创建',
  UPDATE: '修改',
  DELETE: '删除',
};

export const changeTypeInContentMap: Record<string, string> = {
  ADD: '增加',
  UPDATE: '修改',
  DELETE: '删除',
};

export const changeChartTypeInContentMap: Record<string, string> = {
  ON_CHART: '上事件时间轴',
  ON_TIMELINE: '上时间轴',
  CREATE_TIME: '时间',
};

export const changeTypeInAssociationMap: Record<string, string> = {
  ADD: '关联',
  DELETE: '取消关联',
};

export const changeTypeInUploadImageMap: Record<string, string> = {
  ADD: '上传',
  DELETE: '删除',
};

export const fieldMapping: Record<string, keyof EventSchedulingBasicInfo> = {
  oname: 'eventId',
  title: 'eventTitle',
  state: 'eventStatus',
  event_type1: 'eventType',
  event_type2: 'eventSubType',
  address: 'eventAddress',
  association_content: 'eventDescription',
  start_time: 'eventStartTime',
  end_time: 'eventEndTime',
  user_name: 'operator',
  create_time: 'createTime',
  event_on_timeline: 'isOnTimeline',
  event_on_chart: 'isOnChart',
  otype: 'otype',
  shape: 'shape',
  event_level: 'eventLevel',
  affect_user_count: 'affectUserCount',
  label: 'label',
  emergency_event: 'emergencyEvent',
  update_time: 'updateTime',
  event_source_id: 'eventSourceId',
  event_source_type: 'eventSourceType',
};

export const fieldNameMap: Record<string, string> = {
  eventTitle: '事件名称',
  eventStatus: '事件状态',
  eventType: '事件大类',
  eventSubType: '事件小类',
  eventAddress: '事件地址',
  shape: '坐标',
  eventLevel: '事件级别',
  affectUserCount: '影响用户',
  label: '标签',
  eventStartTime: '事件开始时间',
  eventEndTime: '事件完成时间',
  isOnTimeline: '时间轴',
  isOnChart: '事件时间轴',
  emergencyEvent: '应急事件',
  groupLeader: '组长',
  deputyLeader: '副组长',
  members: '组员',
};

export const changeTypeStatusMap: Record<
  string,
  'success' | 'processing' | 'error'
> = {
  ADD: 'success',
  UPDATE: 'processing',
  DELETE: 'error',
  ON_CHART: 'success',
};

export interface LogTranslations {
  eventType: EventType[];
  eventLevel: LevelType[];
}

const fieldTranslationMap: Record<
  string,
  (value: string, translations?: any) => string
> = {
  eventStatus: getEventStatusTypeName,
  eventType: (value: string, translations: LogTranslations) =>
    getEventTypeName(value, translations.eventType),
  eventSubType: (value: string, translations: LogTranslations) =>
    getEventSubTypeName(value, translations.eventType),
  eventLevel: (value: string, translations: LogTranslations) =>
    getEventLevelName(value, translations.eventLevel),
  eventStartTime: (value: string) =>
    value ? dayjs(value).format('YYYY-MM-DD HH:mm') : value,
  eventEndTime: (value: string) =>
    value ? dayjs(value).format('YYYY-MM-DD HH:mm') : value,
  isOnTimeline: (value: string) =>
    Number(value) ? '显示在时间轴' : '取消显示在时间轴',
  isOnChart: (value: string) =>
    Number(value) ? '显示在事件时间轴' : '取消显示在事件时间轴',
  emergencyEvent: (value: string) => (Number(value) ? '是' : '否'),
};

export interface EventSchedulingLog {
  id: string;
  /** 事件 ID */
  eventId?: string;
  /** 事件关联 ID */
  eventRelatedId?: string;
  /** 日志时间 */
  logTime?: string;
  /** 修改类型 */
  contentType?: EventSchedulingContentType;
  /** 操作类型 */
  changeType?: string;
  /** 修改字段 */
  changedField?: string;
  /** 修改前内容 */
  beforeContent?: string;
  /** 修改后内容 */
  afterContent?: string;
  /** 操作人 */
  operator?: string;
}

export function translateFieldValue(
  fieldName: string,
  value: string,
  logTranslations: LogTranslations,
): string {
  const translator = fieldTranslationMap[fieldName];
  return translator ? translator(value, logTranslations) : value;
}
