/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ContingencyPlan } from '../contingency-plan/contingency-plan-data';
import { DispatchCommandList } from '../dispatch-command/command-list';
import { ObservationScadaItem } from '../observation-scada-data';
import { PlanProjectList } from '../plan-project';
import { ChartProperties } from '../property/property-info';
import { SolutionComparisonItem, SolutionListItem } from '../solution';
import { SystemIconData } from '../system-icon';
import {
  ValveOperationGroup,
  ValveOperationValue,
} from '../valve-manager/valve-manager-data';
import { WarnInfoItem } from '../warn';
import { WorkOrder } from '../work-order';

export interface EventSchedulingRelatedDetailInfo {
  /** 关联警告 */
  warning: WarnInfoItem[];
  /** 关联指令 */
  command: DispatchCommandList[];
  /** 关联工程 */
  planning: PlanProjectList[];
  /** 关联阀门 */
  valve: ValveOperationValue[];
  /** 关联阀门归集 */
  valveGroup: ValveOperationGroup[];
  /** 关联标注 */
  remark: ChartProperties[];
  /** 关联工单 */
  workOrder: WorkOrder[];
  /** 关联图片 */
  images: SystemIconData[];
  /** 关联方案 */
  solution: SolutionListItem[];
  /** 关联方案对比 */
  solutionComparison: SolutionComparisonItem[];
  contingencyPlan: ContingencyPlan[];
  observations: ObservationScadaItem[];
}
