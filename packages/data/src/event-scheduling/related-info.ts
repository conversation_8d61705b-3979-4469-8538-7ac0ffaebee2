/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export enum EventRelatedType {
  /** 警告 */
  WARNING = 'WARNING',
  /** 指令 */
  COMMAND = 'COMMAND',
  /** 工程 */
  PLANNING = 'PLANNING',
  /** 阀门 */
  VALVE = 'VALVE',
  /** 阀门归集 */
  VALVE_GROUP = 'VALVE_GROUP',
  /** 标注 */
  REMARK = 'REMARK',
  /** 工单 */
  WORK_ORDER = 'WORK_ORDER',
  /** 短信 */
  SMS = 'SMS',
  /** 自定义内容 */
  CUSTOM = 'CUSTOM',
  /** 事件开始 */
  EVENT_START = 'EVENT_START',
  /** 事件结束 */
  EVENT_END = 'EVENT_END',
  /** 上传附件 */
  UPLOAD_IMAGE = 'UPLOAD_IMAGE',
  /** 方案 */
  SOLUTION = 'SOLUTION',
  /** 方案快速对比 */
  SOLUTION_COMPARISON = 'SOLUTION_COMPARISON',
  /** 应急事件 */
  EMERGENCY_EVENT = 'EMERGENCY_EVENT',
  /** 应急预案 */
  CONTINGENCY_PLAN = 'CONTINGENCY_PLAN',
  /** 观测点 */
  OBSERVATION = 'OBSERVATION',
}

export enum EventRelatedSubType {
  OTHER = 'OTHER',
}

export enum RelatedType {
  CREATE = 'CREATE',
  RELATED = 'RELATED',
  NONE = 'NONE',
}

export interface EventSchedulingSelectedForm {
  eventId: string;
  relatedType: RelatedType;
}

export interface EventSchedulingRelatedInfo {
  /** 关联id */
  relatedId: string;
  /** 关联大类 */
  relatedType: EventRelatedType;
  /** 关联小类 */
  relatedSubType: string | EventRelatedSubType;
  /** 关联描述 */
  relatedDescription?: string;
  /** 关联时间 */
  relatedTime: string;
  /** 关联人 */
  relatedOperator?: string;
  /** 关联上轴状态 */
  relatedOnTimeLine?: boolean;
  /** 关联上曲线状态 */
  relatedOnChart?: boolean;
  /** 关联事件 id */
  eventId?: string;
  /** 关联事件名称 */
  eventName?: string;
}

export const getEventRelatedTypeName = (eventRelatedType: string): string => {
  switch (eventRelatedType) {
    case EventRelatedType.WARNING:
      return '警告';
    case EventRelatedType.COMMAND:
      return '指令';
    case EventRelatedType.PLANNING:
      return '工程';
    case EventRelatedType.VALVE:
      return '阀门';
    case EventRelatedType.VALVE_GROUP:
      return '阀门归集';
    case EventRelatedType.WORK_ORDER:
      return '工单';
    case EventRelatedType.REMARK:
      return '标注';
    case EventRelatedType.SMS:
      return '短信';
    case EventRelatedType.CUSTOM:
      return '事件内容';
    case EventRelatedType.EVENT_START:
      return '事件开始';
    case EventRelatedType.EVENT_END:
      return '事件结束';
    case EventRelatedType.UPLOAD_IMAGE:
      return '附件';
    case EventRelatedType.SOLUTION:
      return '方案';
    case EventRelatedType.SOLUTION_COMPARISON:
      return '方案对比';
    case EventRelatedType.EMERGENCY_EVENT:
      return '应急事件';
    case EventRelatedType.CONTINGENCY_PLAN:
      return '应急预案';
    default:
      return eventRelatedType;
  }
};

export const getEventRelatedTypeTagColor = (
  eventRelatedType: string,
): string => {
  switch (eventRelatedType) {
    case EventRelatedType.WARNING:
      return '#FF4D4F';
    case EventRelatedType.COMMAND:
      return '#f59300';
    case EventRelatedType.PLANNING:
      return '#41ccff';
    case EventRelatedType.VALVE:
      return '#8dd46e';
    case EventRelatedType.VALVE_GROUP:
      return '#8dd46e';
    case EventRelatedType.WORK_ORDER:
      return '#9254de';
    case EventRelatedType.REMARK:
      return '#52C41A';
    case EventRelatedType.SMS:
      return '#6f00e8';
    case EventRelatedType.EVENT_START:
    case EventRelatedType.EVENT_END:
      return '#C0C0C0';
    case EventRelatedType.UPLOAD_IMAGE:
      return '#007AFF';
    case EventRelatedType.SOLUTION:
      return '#0066CC';
    case EventRelatedType.SOLUTION_COMPARISON:
      return '#36A3FF';
    case EventRelatedType.CONTINGENCY_PLAN:
      return '#722ED1';
    default:
      return '#D9D9D9';
  }
};
