/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import * as XLSX from 'xlsx';
import { ObjectChartProperty } from './property/property-info';
import { MultipleAnalysisReport } from './quick-analysis/solution-analysis-data';

// 将 h:mm 格式转换为秒
const convertHydraulicTimeStepToSeconds = (timeStep: string): number => {
  try {
    const parts = timeStep.split(':');
    if (parts.length === 2) {
      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);
      return hours * 60 * 60 + minutes * 60;
    }
    if (parts.length === 1) {
      // 如果只有一个数字，假设是分钟
      return parseInt(parts[0], 10) * 60;
    }
  } catch (error) {
    console.error('Error parsing hydraulic time step:', timeStep, error);
  }

  // 默认返回60秒
  return 60;
};

/**
 * 计算时间步长
 * @param reportData 多方案分析报告数据
 * @returns 时间步长（秒）
 */
export const calculateTimeStep = (
  reportData: MultipleAnalysisReport,
): number => {
  // 默认步长（如果无法计算，则使用默认值，例如60秒）
  let minTimeStepSeconds = 60;

  // 获取基准方案的步长
  if (reportData.solution.options?.hydraulicTimestep) {
    const baseTimeStep = convertHydraulicTimeStepToSeconds(
      reportData.solution.options.hydraulicTimestep,
    );
    minTimeStepSeconds = baseTimeStep;
  }

  // 比较所有对比方案的步长，取最小值
  if (reportData.compare && reportData.compare.length > 0) {
    reportData.compare.forEach((compareSolution) => {
      if (compareSolution.options?.hydraulicTimestep) {
        const compareTimeStep = convertHydraulicTimeStepToSeconds(
          compareSolution.options.hydraulicTimestep,
        );
        minTimeStepSeconds = Math.min(minTimeStepSeconds, compareTimeStep);
      }
    });
  }

  return minTimeStepSeconds;
};

/**
 * 根据对象类型获取导出关注点的 excel 指标
 * @param otype 对象类型
 * @param newDevice 新设备
 * @returns 指标
 */
export const getIndicatorByOType = (
  otype: string,
  newDevice: ObjectChartProperty[],
) => {
  switch (otype) {
    case 'WDM_PIPES':
      return newDevice.filter((m) => ['FLOW', 'VELOCITY'].includes(m.vprop));
    case 'WDM_JUNCTIONS':
      return newDevice.filter((m) =>
        ['PRESSURE', 'TOTALHEAD'].includes(m.vprop),
      );
    case 'DEV_PRESSURE_W':
      return newDevice.filter((m) =>
        ['SDVAL_PRESS_W'].includes(m.indicatorOType ?? ''),
      );
    case 'DEV_FLOW_W':
      return newDevice.filter((m) =>
        ['SDVAL_FLOW_W'].includes(m.indicatorOType ?? ''),
      );
    default:
      return [];
  }
};

interface TimeSeriesData {
  time: string; // 时间
  model?: string; // 模型值
  value?: string; // 监测值
}

interface TimeSeriesDataWithUnit {
  unit: string;
  data: TimeSeriesData[];
}

interface PipelineData {
  velocity: TimeSeriesDataWithUnit; // 流速
  flow: TimeSeriesDataWithUnit; // 流量
}

interface NodeData {
  pressure: TimeSeriesDataWithUnit; // 压力
  totalhead: TimeSeriesDataWithUnit; // 水头
}

interface FlowMeterData {
  flow: TimeSeriesDataWithUnit; // 流量
}

interface PressurePointData {
  pressure: TimeSeriesDataWithUnit; // 压力
}

// 定义观测点类型枚举
export enum ObservationType {
  PIPELINE = 'pipeline',
  NODE = 'node',
  FLOW_METER = 'flowMeter',
  PRESSURE_POINT = 'pressurePoint',
}

export interface ComparisonResult {
  [key: string]: {
    id: string;
    name: string;
    calculateStartTime: string;
    calculateEndTime: string;
    timeStep: string;
    observations: {
      id: string;
      type: ObservationType;
      name?: string;
      roadName?: string;
      diameter?: number;
      pipeline?: PipelineData;
      node?: NodeData;
      flowMeter?: FlowMeterData;
      pressurePoint?: PressurePointData;
    }[];
  };
}

// 定义常量文本，便于统一管理和修改
const TEXT = {
  // 通用
  TIME: '时间',
  MONITORED: '(监测)',

  // 观测点类型
  [ObservationType.PIPELINE]: '管道',
  [ObservationType.NODE]: '节点',
  [ObservationType.FLOW_METER]: '流量计',
  [ObservationType.PRESSURE_POINT]: '压力点',

  // 数据类型
  VELOCITY: '流速',
  FLOW: '流量',
  PRESSURE: '压力',
  TOTAL_HEAD: '水头',
  FLOW_METER_VALUE: '流量',
  PRESSURE_POINT_VALUE: '压力',

  // 关注点页面
  ID: 'ID',
  TYPE: '类型',
  ROAD_NAME: '路名',
  NAME: '名称',
  DIAMETER: '口径(mm)',

  // 文件名
  EXPORT_RESULT: '附录_关注点计算结果_',

  // 工作表名
  SHEET_FOCUS: '关注点',
  SHEET_PIPELINE: '管道',
  SHEET_NODE: '节点',
  SHEET_FLOW_METER: '流量计',
  SHEET_PRESSURE_POINT: '压力点',
};

// 格式化时间为时:分
function formatTimeToHourMinute(timeStr: string): string {
  try {
    // 使用 dayjs 解析时间字符串
    const time = dayjs(timeStr);

    // 检查是否是有效日期
    if (!time.isValid()) {
      // 如果不是有效日期，可能已经是时:分格式，直接返回
      return timeStr;
    }

    // 使用 dayjs 格式化为时:分
    return time.format('HH:mm');
  } catch {
    // 如果解析出错，返回原始字符串
    return timeStr;
  }
}

// 创建通用函数处理不同类型的观测点数据
function createObservationSheet(
  wb: XLSX.WorkBook,
  solutions: ComparisonResult[string][],
  observationType: ObservationType,
  sheetName: string,
) {
  // 收集所有指定类型的观测点
  const allObservations = solutions.flatMap((solution) =>
    solution.observations
      .filter((obs) => {
        // 根据观测点类型筛选并确保有数据
        switch (observationType) {
          case ObservationType.PIPELINE:
            return obs.type === observationType && obs.pipeline;
          case ObservationType.NODE:
            return obs.type === observationType && obs.node;
          case ObservationType.FLOW_METER:
            return obs.type === observationType && obs.flowMeter;
          case ObservationType.PRESSURE_POINT:
            return obs.type === observationType && obs.pressurePoint;
          default:
            return false;
        }
      })
      .map((obs) => ({
        solutionId: solution.id,
        solutionName: solution.name,
        ...obs,
      })),
  );

  // 如果没有数据，直接返回
  if (allObservations.length === 0) return;

  // 根据所有方案的计算时间范围和步长生成时间点
  const allTimePoints = new Set<string>();

  // 找出最小的时间步长（以分钟为单位）
  const minTimeStep = Math.min(
    ...solutions.map((s) => {
      // 处理 "h:mm" 格式 (如 "0:30")
      const timeFormatMatch = s.timeStep.match(/^(\d+):(\d+)$/);
      if (timeFormatMatch) {
        const hours = parseInt(timeFormatMatch[1], 10);
        const minutes = parseInt(timeFormatMatch[2], 10);
        return hours * 60 + minutes; // 转换为分钟
      }

      // 处理 "数字+单位" 格式 (如 "30m", "1h")
      const unitFormatMatch = s.timeStep.match(/(\d+)([hms])/);
      if (unitFormatMatch) {
        const value = parseInt(unitFormatMatch[1], 10);
        const unit = unitFormatMatch[2];

        // 转换为分钟
        switch (unit) {
          case 'h':
            return value * 60;
          case 'm':
            return value;
          case 's':
            return value / 60;
          default:
            return 60;
        }
      }

      // 默认返回60分钟
      return 60;
    }),
  );

  // 为每个方案生成时间点
  solutions.forEach((solution) => {
    if (!solution.calculateStartTime || !solution.calculateEndTime) return;

    let currentTime = dayjs(solution.calculateStartTime);
    const endTime = dayjs(solution.calculateEndTime);

    // 检查日期是否有效
    if (!currentTime.isValid() || !endTime.isValid()) return;

    // 使用最小步长生成时间点
    while (currentTime.isBefore(endTime) || currentTime.isSame(endTime)) {
      allTimePoints.add(currentTime.toISOString());
      // 增加最小步长（分钟）
      currentTime = currentTime.add(minTimeStep, 'minute');
    }
  });

  // 从实际数据中也收集时间点
  allObservations.forEach((observation) => {
    switch (observationType) {
      case ObservationType.PIPELINE:
        if (observation.pipeline) {
          observation.pipeline.velocity.data.forEach((v) => {
            if (v.time) allTimePoints.add(v.time);
          });
          observation.pipeline.flow.data.forEach((f) => {
            if (f.time) allTimePoints.add(f.time);
          });
        }
        break;
      case ObservationType.NODE:
        if (observation.node) {
          observation.node.pressure.data.forEach((p) => {
            if (p.time) allTimePoints.add(p.time);
          });
          observation.node.totalhead.data.forEach((t) => {
            if (t.time) allTimePoints.add(t.time);
          });
        }
        break;
      case ObservationType.FLOW_METER:
        if (observation.flowMeter) {
          observation.flowMeter.flow.data.forEach((fm) => {
            if (fm.time) allTimePoints.add(fm.time);
          });
        }
        break;
      case ObservationType.PRESSURE_POINT:
        if (observation.pressurePoint) {
          observation.pressurePoint.pressure.data.forEach((pp) => {
            if (pp.time) allTimePoints.add(pp.time);
          });
        }
        break;
      default:
        break;
    }
  });

  // 将所有时间点格式化为时:分并排序
  const formattedTimePoints = Array.from(allTimePoints)
    .map((time) => formatTimeToHourMinute(time))
    .filter((value, index, self) => self.indexOf(value) === index) // 去重
    .sort();

  // 创建表头
  // 第一行：A1空着，然后是每个观测点的信息
  const headerRow1: any = [null];

  // 获取所有观测点ID
  const observationIds = [...new Set(allObservations.map((o) => o.id))];

  // 获取所有方案ID和名称
  const allSolutions = solutions.map((s) => ({
    id: s.id,
    name: s.name,
  }));

  // 第二行：A2写"时间"，然后是各个方案的数据列
  const headerRow2 = [TEXT.TIME];

  // 记录每个观测点占用的列数
  const observationColumnCounts: number[] = [];

  // 为每个观测点添加所有方案的列标题，并计算列数
  observationIds.forEach((observationId) => {
    let observationColumnCount = 0;

    // 为所有方案添加数据列标题
    allSolutions.forEach((solution) => {
      // 查找该方案下该观测点的数据
      const observationData = allObservations.find(
        (o) => o.id === observationId && o.solutionId === solution.id,
      );

      switch (observationType) {
        case ObservationType.PIPELINE: {
          // 检查该管道在该方案下是否有监测值
          const hasVelocityMonitored =
            observationData?.pipeline?.velocity.data.some((v) => v.value);
          const hasFlowMonitored = observationData?.pipeline?.flow.data.some(
            (f) => f.value,
          );

          // 获取单位
          const velocityUnit = observationData?.pipeline?.velocity.unit || '';
          const flowUnit = observationData?.pipeline?.flow.unit || '';

          // 添加流速列标题（带单位）
          headerRow2.push(`${solution.name}${TEXT.VELOCITY}(${velocityUnit})`);
          observationColumnCount += 1;

          if (hasVelocityMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.VELOCITY}${TEXT.MONITORED}(${velocityUnit})`,
            );
            observationColumnCount += 1;
          }

          // 添加流量列标题（带单位）
          headerRow2.push(`${solution.name}${TEXT.FLOW}(${flowUnit})`);
          observationColumnCount += 1;

          if (hasFlowMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.FLOW}${TEXT.MONITORED}(${flowUnit})`,
            );
            observationColumnCount += 1;
          }
          break;
        }

        case ObservationType.NODE: {
          // 检查该节点在该方案下是否有监测值
          const hasPressureMonitored =
            observationData?.node?.pressure.data.some((p) => p.value);
          const hasTotalHeadMonitored =
            observationData?.node?.totalhead.data.some((t) => t.value);

          // 获取单位
          const pressureUnit = observationData?.node?.pressure.unit || '';
          const totalheadUnit = observationData?.node?.totalhead.unit || '';

          // 添加压力列标题（带单位）
          headerRow2.push(`${solution.name}${TEXT.PRESSURE}(${pressureUnit})`);
          observationColumnCount += 1;

          if (hasPressureMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.PRESSURE}${TEXT.MONITORED}(${pressureUnit})`,
            );
            observationColumnCount += 1;
          }

          // 添加水头列标题（带单位）
          headerRow2.push(
            `${solution.name}${TEXT.TOTAL_HEAD}(${totalheadUnit})`,
          );
          observationColumnCount += 1;

          if (hasTotalHeadMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.TOTAL_HEAD}${TEXT.MONITORED}(${totalheadUnit})`,
            );
            observationColumnCount += 1;
          }
          break;
        }

        case ObservationType.FLOW_METER: {
          // 检查该流量计在该方案下是否有监测值
          const hasFlowMeterMonitored =
            observationData?.flowMeter?.flow.data.some((fm) => fm.value);

          // 获取单位
          const flowUnit = observationData?.flowMeter?.flow.unit || '';

          // 添加流量计列标题（带单位）
          headerRow2.push(
            `${solution.name}${TEXT.FLOW_METER_VALUE}(${flowUnit})`,
          );
          observationColumnCount += 1;

          if (hasFlowMeterMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.FLOW_METER_VALUE}${TEXT.MONITORED}(${flowUnit})`,
            );
            observationColumnCount += 1;
          }
          break;
        }

        case ObservationType.PRESSURE_POINT: {
          // 检查该压力点在该方案下是否有监测值
          const hasPressurePointMonitored =
            observationData?.pressurePoint?.pressure.data.some(
              (pp) => pp.value,
            );

          // 获取单位
          const pressureUnit =
            observationData?.pressurePoint?.pressure.unit || '';

          // 添加压力点列标题（带单位）
          headerRow2.push(
            `${solution.name}${TEXT.PRESSURE_POINT_VALUE}(${pressureUnit})`,
          );
          observationColumnCount += 1;

          if (hasPressurePointMonitored) {
            headerRow2.push(
              `${solution.name}${TEXT.PRESSURE_POINT_VALUE}${TEXT.MONITORED}(${pressureUnit})`,
            );
            observationColumnCount += 1;
          }
          break;
        }
        default:
          break;
      }
    });

    // 记录该观测点占用的列数
    observationColumnCounts.push(observationColumnCount);
  });

  // 添加观测点名称，并为后续列添加null（用于合并）
  observationIds.forEach((observationId, index) => {
    // 找到第一个匹配的观测点来获取名称
    const observation = allObservations.find((o) => o.id === observationId);

    // 格式化观测点名称：如果有名称则显示"名称 (ID)"，否则只显示ID
    const observationDisplay = observation?.name
      ? `${observation.name} (${observationId})`
      : observationId;

    // 添加观测点名称，并为后续列添加null（用于合并）
    headerRow1.push(observationDisplay);

    // 添加null用于合并单元格（列数-1，因为已经添加了一个名称）
    const nullCount = observationColumnCounts[index] - 1;
    headerRow1.push(...Array(nullCount).fill(null));
  });

  // 数据行：每行代表一个时间点的所有观测点数据
  const dataRows = formattedTimePoints.map((formattedTime) => {
    // 第一列是格式化后的时间
    const row = [formattedTime];

    // 按观测点ID顺序添加数据 - 所有观测点在同一时间点的数据都在同一行
    observationIds.forEach((observationId) => {
      // 为所有方案添加数据
      allSolutions.forEach((solution) => {
        // 查找该方案下该观测点的数据
        const observationData = allObservations.find(
          (o) => o.id === observationId && o.solutionId === solution.id,
        );

        switch (observationType) {
          case ObservationType.PIPELINE:
            {
              // 检查是否有监测值
              const hasVelocityMonitored =
                observationData?.pipeline?.velocity.data.some((v) => v.value);
              const hasFlowMonitored =
                observationData?.pipeline?.flow.data.some((f) => f.value);

              if (!observationData || !observationData.pipeline) {
                // 添加流速数据（模拟值）
                row.push('');
                if (hasVelocityMonitored) row.push('');

                // 添加流量数据（模拟值）
                row.push('');
                if (hasFlowMonitored) row.push('');

                return;
              }

              const { pipeline } = observationData;

              // 查找匹配的数据，使用格式化后的时间进行比较
              const velocityItem = pipeline.velocity.data.find(
                (v) => formatTimeToHourMinute(v.time) === formattedTime,
              );
              const flowItem = pipeline.flow.data.find(
                (f) => formatTimeToHourMinute(f.time) === formattedTime,
              );

              // 添加流速数据
              row.push(velocityItem?.model || '');
              if (hasVelocityMonitored) {
                row.push(velocityItem?.value || '');
              }

              // 添加流量数据
              row.push(flowItem?.model || '');
              if (hasFlowMonitored) {
                row.push(flowItem?.value || '');
              }
            }
            break;

          case ObservationType.NODE:
            {
              // 检查是否有监测值
              const hasPressureMonitored =
                observationData?.node?.pressure.data.some((p) => p.value);
              const hasTotalHeadMonitored =
                observationData?.node?.totalhead.data.some((t) => t.value);

              if (!observationData || !observationData.node) {
                // 添加压力数据（模拟值）
                row.push('');
                if (hasPressureMonitored) row.push('');

                // 添加水头数据（模拟值）
                row.push('');
                if (hasTotalHeadMonitored) row.push('');

                return;
              }

              const { node } = observationData;

              // 查找匹配的数据，使用格式化后的时间进行比较
              const pressureItem = node.pressure.data.find(
                (p) => formatTimeToHourMinute(p.time) === formattedTime,
              );
              const totalHeadItem = node.totalhead.data.find(
                (t) => formatTimeToHourMinute(t.time) === formattedTime,
              );

              // 添加压力数据
              row.push(pressureItem?.model || '');
              if (hasPressureMonitored) {
                row.push(pressureItem?.value || '');
              }

              // 添加水头数据
              row.push(totalHeadItem?.model || '');
              if (hasTotalHeadMonitored) {
                row.push(totalHeadItem?.value || '');
              }
            }
            break;

          case ObservationType.FLOW_METER:
            {
              // 检查是否有监测值
              const hasFlowMeterMonitored =
                observationData?.flowMeter?.flow.data.some((fm) => fm.value);

              if (!observationData || !observationData.flowMeter) {
                // 添加流量计数据（模拟值）
                row.push('');
                if (hasFlowMeterMonitored) row.push('');
                return;
              }

              // 查找匹配的数据，使用格式化后的时间进行比较
              const flowMeterItem = observationData.flowMeter.flow.data.find(
                (fm) => formatTimeToHourMinute(fm.time) === formattedTime,
              );

              // 添加流量计数据
              row.push(flowMeterItem?.model || '');
              if (hasFlowMeterMonitored) {
                row.push(flowMeterItem?.value || '');
              }
            }
            break;

          case ObservationType.PRESSURE_POINT:
            {
              // 检查是否有监测值
              const hasPressurePointMonitored =
                observationData?.pressurePoint?.pressure.data.some(
                  (pp) => pp.value,
                );

              if (!observationData || !observationData.pressurePoint) {
                // 添加压力点数据（模拟值）
                row.push('');
                if (hasPressurePointMonitored) row.push('');
                return;
              }

              // 查找匹配的数据，使用格式化后的时间进行比较
              const pressurePointItem =
                observationData.pressurePoint.pressure.data.find(
                  (pp) => formatTimeToHourMinute(pp.time) === formattedTime,
                );

              // 添加压力点数据
              row.push(pressurePointItem?.model || '');
              if (hasPressurePointMonitored) {
                row.push(pressurePointItem?.value || '');
              }
            }
            break;
          default:
            break;
        }
      });
    });

    return row;
  });

  // 合并所有行
  const aoaSheet = [headerRow1, headerRow2, ...dataRows];
  const ws = XLSX.utils.aoa_to_sheet(aoaSheet);

  // 设置合并单元格：每个观测点名称合并其所有方案的列
  let currentColumn = 1; // 从第2列开始（第1列是时间）
  ws['!merges'] = observationIds.map((_, index) => {
    const columnCount = observationColumnCounts[index];
    const merge = {
      s: { r: 0, c: currentColumn },
      e: { r: 0, c: currentColumn + columnCount - 1 },
    };
    currentColumn += columnCount;
    return merge;
  });

  XLSX.utils.book_append_sheet(wb, ws, sheetName);
}

/**
 * 自动调整工作表列宽
 * @param worksheet 工作表
 * @returns 列宽配置
 */
function autoFitColumns(worksheet: XLSX.WorkSheet): { wch: number }[] {
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

  // 创建列索引数组
  const columnIndices = Array.from(
    { length: range.e.c - range.s.c + 1 },
    (_, i) => range.s.c + i,
  );

  // 计算每列的最佳宽度
  return columnIndices.map((col) => {
    // 创建行索引数组
    const rowIndices = Array.from(
      { length: range.e.r - range.s.r + 1 },
      (_, i) => range.s.r + i,
    );

    // 获取该列所有单元格的内容长度
    const contentLengths = rowIndices
      .map((row) => {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (!cell || !cell.v) return 0;

        // 计算内容长度
        const cellValue = cell.v.toString();

        // 基础长度是字符串的长度
        let contentLength: number = cellValue.length;

        // 特殊字符宽度调整
        // 1. 中文字符占用更多宽度
        const chineseCharCount = (cellValue.match(/[\u4e00-\u9fa5]/g) || [])
          .length;
        contentLength += chineseCharCount * 0.5;

        // 2. 宽标点符号需要额外空间
        const wideSymbolCount = (cellValue.match(/[{}()[\]<>]/g) || []).length;
        contentLength += wideSymbolCount * 0.3;

        // 3. 其他特殊符号可能也需要额外空间
        const specialSymbolCount = (
          cellValue.match(/[@#$%^&*+=|\\/:;"',.?!~`]/g) || []
        ).length;
        contentLength += specialSymbolCount * 0.1;

        // 4. 如果内容包含换行符，考虑每行的最大长度
        if (cellValue.includes('\n')) {
          const lines = cellValue.split('\n');
          const maxLineLength = Math.max(
            ...lines.map((line: string) => line.length),
          );
          // 使用行中最长的一行作为基础长度
          contentLength = Math.max(contentLength, maxLineLength);
          // 为多行内容添加额外空间
          contentLength += lines.length * 0.5;
        }

        return contentLength + 2; // 加2作为内边距
      })
      .filter((length) => length > 0);

    // 取最大宽度，如果没有内容则使用默认宽度
    const maxWidth =
      contentLengths.length > 0
        ? Math.max(...contentLengths, 10) // 最小宽度为10
        : 10;

    // 限制最大宽度，避免过宽
    return { wch: Math.min(maxWidth, 60) }; // 增加最大宽度限制到60
  });
}

/**
 * 导出关注点计算结果
 * @param comparisonData 对比结果
 */
export function exportComparisonResultToExcel(
  comparisonData: ComparisonResult,
) {
  // 创建工作簿
  const wb = XLSX.utils.book_new();

  // 获取所有方案
  const solutions = Object.values(comparisonData);

  // 如果没有方案，直接返回
  if (solutions.length === 0) {
    XLSX.writeFile(
      wb,
      `${TEXT.EXPORT_RESULT}-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`,
    );
    return;
  }

  // ===== 关注点页 =====
  const subHeaders = [
    TEXT.ID,
    TEXT.TYPE,
    TEXT.ROAD_NAME,
    TEXT.NAME,
    TEXT.DIAMETER,
  ];

  const headerRow1 = solutions.flatMap((solution) => [
    solution.name,
    ...Array(subHeaders.length - 1).fill(null),
  ]);

  const headerRow2 = solutions.flatMap(() => subHeaders);

  // 获取所有方案中最大的观测点数量
  const maxPointsCount = Math.max(
    ...solutions.map((s) => s.observations.length),
  );

  // 创建数据行，每行代表一个关注点在不同方案中的数据
  const dataRows = Array.from({ length: maxPointsCount }, (_, i) =>
    solutions.flatMap((solution) => {
      // 如果该索引位置没有数据，则返回空值数组
      if (!solution.observations[i]) {
        return Array(subHeaders.length).fill('');
      }

      const point = solution.observations[i];

      // 将英文类型转换为中文显示
      let typeDisplay = '';
      switch (point.type) {
        case ObservationType.PIPELINE:
          typeDisplay = TEXT[ObservationType.PIPELINE];
          break;
        case ObservationType.NODE:
          typeDisplay = TEXT[ObservationType.NODE];
          break;
        case ObservationType.FLOW_METER:
          typeDisplay = TEXT[ObservationType.FLOW_METER];
          break;
        case ObservationType.PRESSURE_POINT:
          typeDisplay = TEXT[ObservationType.PRESSURE_POINT];
          break;
        default:
          typeDisplay = point.type;
          break;
      }

      return [
        point.id,
        typeDisplay,
        point.roadName || '',
        point.name || '',
        point.diameter ?? '',
      ];
    }),
  );

  const aoaFocus = [headerRow1, headerRow2, ...dataRows];
  const wsFocus = XLSX.utils.aoa_to_sheet(aoaFocus);

  // 设置合并：每组 5 列的第一行合并
  wsFocus['!merges'] = solutions.map((_, index) => {
    const start = index * subHeaders.length;
    return {
      s: { r: 0, c: start },
      e: { r: 0, c: start + subHeaders.length - 1 },
    };
  });

  XLSX.utils.book_append_sheet(wb, wsFocus, TEXT.SHEET_FOCUS);

  // 创建各类型观测点的工作表
  createObservationSheet(
    wb,
    solutions,
    ObservationType.PIPELINE,
    TEXT.SHEET_PIPELINE,
  );
  createObservationSheet(wb, solutions, ObservationType.NODE, TEXT.SHEET_NODE);
  createObservationSheet(
    wb,
    solutions,
    ObservationType.FLOW_METER,
    TEXT.SHEET_FLOW_METER,
  );
  createObservationSheet(
    wb,
    solutions,
    ObservationType.PRESSURE_POINT,
    TEXT.SHEET_PRESSURE_POINT,
  );

  // 为所有工作表设置自适应列宽
  wb.SheetNames.forEach((sheetName) => {
    const worksheet = wb.Sheets[sheetName];
    const cols = autoFitColumns(worksheet);
    worksheet['!cols'] = cols;
  });

  // 导出 Excel 文件
  XLSX.writeFile(
    wb,
    `${TEXT.EXPORT_RESULT}${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`,
  );
}
