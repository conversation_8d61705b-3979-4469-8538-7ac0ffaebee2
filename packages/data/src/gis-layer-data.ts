/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DisplayType, LayerData } from './layer-data';

export interface GisMapLayerArgs {
  type: 'ImageArcGISRest' | 'WMTS' | 'TILE';
}

export interface ImageArcGisLayerArgs extends GisMapLayerArgs {
  projection: string;
  url: string;
  urlConfig: { [key: string]: string };
  params: Map<string, any>;
  opacity?: number;
  gisType?: string;
  lowerCase?: string[];
  tokenConfig?: Record<string, string | Record<string, string>>;
}

export interface GisLayerIdentifyArgs {
  url?: string;
  urlConfig?: { [key: string]: string };
  method?: string;
  timeout?: number;
  params?: Map<string, any>;
}

export class ImageArcGisLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    layerArgs?: any,
    identifyArgs?: any,
  ) {
    this.name = name;
    this.type = 'LayerImageArcGISRest';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this._layerParams = ImageArcGisLayerData.getLayerParams(layerArgs);
    this._identifyParams = ImageArcGisLayerData.getIdentifyParams(identifyArgs);
  }

  static getLayerParams(args?: any): ImageArcGisLayerArgs {
    return {
      type: 'ImageArcGISRest',
      projection: args?.projection,
      url: args?.url,
      urlConfig: args?.urlConfig,
      params: args?.params,
      opacity: args?.opacity,
      lowerCase: args?.lowerCase,
      tokenConfig: args?.tokenConfig,
    };
  }

  private static getIdentifyParams(args?: any): GisLayerIdentifyArgs {
    let timeout: number | undefined;
    if (!Number.isNaN(args?.timeout)) timeout = args?.timeout;
    return {
      url: args?.url,
      urlConfig: args?.urlConfig,
      timeout,
      params: args?.params,
      method: args?.method,
    };
  }

  private _layerParams?: ImageArcGisLayerArgs;

  get layerParams(): ImageArcGisLayerArgs | undefined {
    return this._layerParams;
  }

  private _identifyParams: GisLayerIdentifyArgs;

  get identifyParams(): GisLayerIdentifyArgs {
    return this._identifyParams;
  }
}

export interface TileGridArgs {
  origin: number[];
  matrixIds: string[];
  resolutions: string;
  tileSize?: number[] | number;
}

export type UrlFunction = (tile: any) => string;

export interface WmtsLayerArgs extends GisMapLayerArgs {
  projection: string;
  url: string;
  urlConfig: { [key: string]: string };
  matrixSet: string;
  layer: string;
  style: string;
  version: string;
  format: string;
  tileGrid: TileGridArgs;
  extent: number[];
  opacity?: number;
}

export class WmtsLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    layerArgs?: any,
  ) {
    this.name = name;
    this.type = 'LayerWmts';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this._layerParams = WmtsLayerData.getLayerParams(layerArgs);
  }

  static getLayerParams(args?: any): WmtsLayerArgs {
    const tileGrid: TileGridArgs = {
      origin: args?.tileGrid?.origin,
      matrixIds: args?.tileGrid?.matrixIds,
      resolutions: args?.tileGrid?.resolutions,
      tileSize: args?.tileGrid?.tileSize,
    };
    return {
      type: 'WMTS',
      projection: args?.projection,
      url: args?.url,
      urlConfig: args?.urlConfig,
      matrixSet: args?.matrixSet,
      layer: args?.layer,
      style: args?.style,
      version: args?.version,
      format: args?.format,
      tileGrid,
      extent: args?.extent,
      opacity: args?.opacity,
    };
  }

  private _layerParams?: WmtsLayerArgs;

  get layerParams(): WmtsLayerArgs | undefined {
    return this._layerParams;
  }
}

export interface TileLayerArgs extends GisMapLayerArgs {
  projection: string;
  url: string;
  urlConfig: { [key: string]: string };
  tileGrid: TileGridArgs | undefined;
  extent: number[];
  tileSize?: number;
  opacity?: number;
  tileUrlConfig?: {
    [key: string]: string | number;
  };
  tokenConfig?: Record<string, string | Record<string, string>>;
}

export class TileLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    layerArgs?: any,
  ) {
    this.name = name;
    this.type = 'LayerTile';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this._layerParams = TileLayerData.getLayerParams(layerArgs);
  }

  static getLayerParams(args?: any): TileLayerArgs | undefined {
    const tileGrid: TileGridArgs = {
      origin: args?.tileGrid?.origin,
      matrixIds: args?.tileGrid?.matrixIds,
      resolutions: args?.tileGrid?.resolutions,
      tileSize: args?.tileGrid?.tileSize,
    };

    return {
      type: 'TILE',
      projection: args?.projection,
      url: args?.url,
      urlConfig: args?.urlConfig,
      tileGrid: args?.tileGrid ? tileGrid : undefined,
      extent: args?.extent,
      opacity: args?.opacity,
      tileUrlConfig: args?.tileUrlConfig ? args?.tileUrlConfig : undefined,
    };
  }

  private _layerParams?: TileLayerArgs;

  get layerParams(): TileLayerArgs | undefined {
    return this._layerParams;
  }
}

export class ArcgisTileLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    layerArgs?: any,
  ) {
    this.name = name;
    this.type = 'LayerTile';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this._layerParams = TileLayerData.getLayerParams(layerArgs);
  }

  static getLayerParams(args?: any): TileLayerArgs {
    const tileGrid: TileGridArgs = {
      origin: args?.tileGrid?.origin,
      matrixIds: args?.tileGrid?.matrixIds,
      resolutions: args?.tileGrid?.resolutions,
      tileSize: args?.tileGrid?.tileSize,
    };

    return {
      type: 'TILE',
      projection: args?.projection,
      url: args?.url,
      urlConfig: args?.urlConfig,
      tileGrid,
      extent: args?.extent,
      opacity: args?.opacity,
    };
  }

  private _layerParams?: TileLayerArgs;

  get layerParams(): TileLayerArgs | undefined {
    return this._layerParams;
  }
}

export class GisMapLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    layersArgs?: any,
  ) {
    this.name = name;
    this.type = 'LayerGroupMap';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this._layerData = new Map<string, LayerData>();
    this.initialize(layersArgs);
  }

  private initialize(layersData: any) {
    layersData.forEach((item: any) => {
      const { theme, type } = item;
      if (theme === undefined || type === undefined) return;

      if (type === 'ImageArcGISRest') {
        const layerData: ImageArcGisLayerData = new ImageArcGisLayerData(
          this.name,
          this.className,
          this.title,
          this.icon,
          this.minZoom,
          this.maxZoom,
          item,
        );
        this._layerData.set(theme, layerData);
      } else if (type === 'WMTS') {
        const layerData: WmtsLayerData = new WmtsLayerData(
          this.name,
          this.className,
          this.title,
          this.icon,
          this.minZoom,
          this.maxZoom,
          item,
        );
        this._layerData.set(theme, layerData);
      } else if (type === 'TILE') {
        const layerData: TileLayerData = new TileLayerData(
          this.name,
          this.className,
          this.title,
          this.icon,
          this.minZoom,
          this.maxZoom,
          item,
        );
        this._layerData.set(theme, layerData);
      } else if (type === 'ArcgisTILE') {
        const layerData: ArcgisTileLayerData = new ArcgisTileLayerData(
          this.name,
          this.className,
          this.title,
          this.icon,
          this.minZoom,
          this.maxZoom,
          item,
        );
        this._layerData.set(theme, layerData);
      }
    });
  }

  private _layerData: Map<string, LayerData>;

  get layerParams(): Map<string, LayerData> {
    return this._layerData;
  }
}
