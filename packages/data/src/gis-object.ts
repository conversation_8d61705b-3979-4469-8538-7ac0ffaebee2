/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IndicatorObject } from './indicator';
import ModelObject from './model-object';
import {
  getShapeType,
  IObjectItem,
  makeObjectId,
  SHAPE_TYPE,
} from './object-item';

export default class GisObject implements IObjectItem {
  constructor(
    otype: string,
    layerName: string,
    oname: string,
    shape: string,
    attributes: Array<[string, any]>,
  ) {
    this._id = makeObjectId(otype, oname);
    this._otype = otype;
    this._layerName = layerName;
    this._oname = oname;
    this._title = oname;
    this._shape = shape;
    this._shapeType = getShapeType(shape);
    this._attributes = attributes;
  }

  private _id: string;

  get id(): string {
    return this._id;
  }

  private _oname: string;

  get oname(): string {
    return this._oname;
  }

  private _otype: string;

  get otype(): string {
    return this._otype;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  // eslint-disable-next-line class-methods-use-this
  get indicators(): Array<IndicatorObject> {
    return [];
  }

  private _layerName: string;

  get layerName(): string {
    return this._layerName;
  }

  private _shape: string;

  get shape(): string {
    return this._shape;
  }

  private _shapeType: SHAPE_TYPE;

  get shapeType(): SHAPE_TYPE {
    return this._shapeType;
  }

  private _attributes: Array<[string, any]> = [];

  get attributes(): Array<[string, any]> {
    return this._attributes;
  }

  private _highlightIcon: string | undefined;

  get highlightIcon(): string | undefined {
    return this._highlightIcon;
  }

  private _refModelObject: ModelObject | undefined;

  get refModelObject(): ModelObject | undefined {
    return this._refModelObject;
  }

  set refModelObject(modelObject: ModelObject | undefined) {
    this._refModelObject = modelObject;
  }
}

export type GeometryType =
  | 'esriGeometryPoint'
  | 'civGeometryPoint'
  | 'esriGeometryMultipoint'
  | 'civGeometryPolyline'
  | 'esriGeometryPolyline'
  | 'esriGeometryPolygon'
  | 'esriGeometryEnvelope';

export function convertToPoint(geometry: any): string | undefined {
  if (typeof geometry.x === 'number' && typeof geometry.y === 'number') {
    return `POINT(${geometry.x} ${geometry.y})`;
  }

  return undefined;
}

function convertToMultiPoint(geometry: any): string | undefined {
  if (Array.isArray(geometry.points) && geometry.points.length > 0) {
    if (Array.isArray(geometry.points[0]) && geometry.points[0].length >= 2)
      return `POINT(${geometry.points[0][0]} ${geometry.points[0][1]})`;
  }

  return undefined;
}

function convertToLine(geometry: any): string | undefined {
  if (
    Array.isArray(geometry.paths) &&
    geometry.paths.length > 0 &&
    Array.isArray(geometry.paths[0])
  ) {
    let paths = '';
    geometry.paths[0].forEach((item) => {
      if (Array.isArray(item) && item.length === 2) {
        if (paths.length !== 0) paths += ',';
        paths += `${item[0]} ${item[1]}`;
      }
    });
    return `LINESTRING(${paths})`;
  }

  return undefined;
}

function convertToPolygon(geometry: any): string | undefined {
  if (Array.isArray(geometry.rings)) {
    let polygons = '';
    geometry.rings.forEach((ring: any) => {
      let polygon = '';
      if (Array.isArray(ring)) {
        ring.forEach((item) => {
          if (Array.isArray(item) && item.length === 2) {
            if (polygon.length !== 0) polygon += ',';
            polygon += `${item[0]} ${item[1]}`;
          }
        });
      }
      if (polygons.length !== 0) polygons += ',';
      polygons += `(${polygon})`;
    });

    return `POLYGON(${polygons})`;
  }

  return undefined;
}

export function convertGeometryToShape(
  geometryType: GeometryType,
  geometry: any,
): string | undefined {
  switch (geometryType) {
    case 'esriGeometryPoint':
    case 'civGeometryPoint':
      return convertToPoint(geometry);
    case 'esriGeometryMultipoint':
      return convertToMultiPoint(geometry);
    case 'esriGeometryPolyline':
    case 'civGeometryPolyline':
      return convertToLine(geometry);
    case 'esriGeometryPolygon':
      return convertToPolygon(geometry);
    case 'esriGeometryEnvelope':
      break;
    default:
      break;
  }

  return undefined;
}
