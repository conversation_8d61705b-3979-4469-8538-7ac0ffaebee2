/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HighlightStyleType } from './style-config';

type Coordinate = number[];

export interface HighlightObject {
  oname: string;
  otype: string;
  id: string;
  shape: string;
  shapeType: string;
  length?: number;
  diameter?: number;
  highlightLineWidth?: number;
  highlightSize?: number;
  highlightIcon?: string;
  highlightText?: string;
  highlightType?: HighlightStyleType;
  highlightColor?: string;
  highlightTextBgColor?: string;
  coordinate?: Coordinate[];
  /**
   * @deprecated 计划删除，以后改为根据是否有highlightText判断
   */
  highlightShowMark?: boolean;
  value?: number;
  highlightSecondColor?: string;
  lineType?: string;
}

export interface HighlightObjectItem {
  /** index: highlight layer name */
  [index: string]: Array<HighlightObject>;
}

export interface HighlightObjects {
  /** index: highlight map name */
  [index: string]: HighlightObjectItem | undefined;
}

export type HighlightDataItem = Omit<HighlightObject, 'id' | 'shapeType'> & {
  id?: string;
  shapeType?: string;
};

export interface ImpactedLink {
  id: string;
  roadName: string;
  diameter: number;
  length: number;
  highlightObjects: HighlightObject[];
  minEffectRatio: number;
  maxEffectRatio: number;
}

export interface ImpactedDma extends HighlightObject {
  title: string;
  pressureDiff?: number;
  currentPressure?: number;
}

export interface TrackDownLink extends HighlightObject {
  id: string;
  ratio: string;
  length: number;
  highlightObjects: HighlightObject[];

  USAGE: number | string;
  FLOW: number | string;
  FLOW_ABS: number | string;
  VELOCITY: number | string;
  UNITHEADLOSS: number | string;
  AGE: number | string;
  QUALITY: number | string;
  CUMULATIVE_TIME_V: number | string;
  LAST_CUMULATIVE_TIME_V: number | string;
  VELOCITY_Q: number | string;
  LAST_VELOCITY_Q: number | string;
  DIRECT_FLOW: number | string;
  CUMULATIVE_TIME_F: number | string;
  LAST_CUMULATIVE_TIME_F: number | string;
  LAST_DIRECT_FLOW: number | string;
  ROAD_NAME: string;
  LENGTH: number | string;
  DIAMETER: number | string;
}

export interface TrackDownDma extends HighlightObject {
  TITLE?: number | string | null;
  POPULATION?: number | string | null;
  AREA?: number | string | null;
  QUALITY?: number | string | null;
  AGE?: number | string | null;
  PRESSURE?: number | string | null;
  TOTALHEAD?: number | string | null;
  WATERSOURCE?: number | string | null;
  USER_METER_COUNT?: number | string | null;
  MAX_FLOW_TIME?: number | string | null;
  MIN_NMF_TIME?: number | string | null;
  MIN_NMF?: number | string | null;
  AVERAGE_NMF?: number | string | null;
  IN_FLOW_DAY?: number | string | null;
  OUT_FLOW_DAY?: number | string | null;
  MAX_FLOW?: number | string | null;
  AVERAGE_FLOW?: number | string | null;
  AVERAGE_PRESSURE?: number | string | null;
  DAY_FLOW?: number | string | null;
  TOTAL_FLOW?: number | string | null;
  ABNORMAL_STATUS?: number | string | null;
  IN_FLOW?: number | string | null;
  OUT_FLOW?: number | string | null;
  WATERMETER_NUM?: number | string | null;
  REALFLOW?: number | string | null;
  SOURCE_PERCENTAGE?: number | string | null;
}

export interface DownTrackWaterMeterData extends HighlightObject {
  NAME?: number | string | null;
  ADDRESS?: number | string | null;
  BOOK_ID?: number | string | null;
  FIRST_DMA_ID?: number | string | null;
  CARD_STAT?: number | string | null;
  USE_MODE?: number | string | null;
  CUST_TYPE?: number | string | null;
  DIAMETER?: number | string | null;
  PAY_TYPE?: number | string | null;
  SECOND_DMA_ID?: number | string | null;
  THREE_DMA_ID?: number | string | null;
  USER_METER_TYPE?: number | string | null;
  TOTAL_FLOW?: number | string | null;
  QUALITY?: number | string | null;
  AGE?: number | string | null;
  TOTALHEAD?: number | string | null;
  PRESSURE?: number | string | null;
  WATERSOURCE?: number | string | null;
  SOURCE_PERCENTAGE?: number | string | null;
}

export interface TrackUpLink extends HighlightObject {
  length: number;
  highlightObjects: HighlightObject[];

  FLOW?: number | string | null;
  FLOW_ABS?: number | string | null;
  VELOCITY?: number | string | null;
  UNITHEADLOSS?: number | string | null;
  AGE?: number | string | null;
  QUALITY?: number | string | null;
  CUMULATIVE_TIME_V?: number | string | null;
  LAST_CUMULATIVE_TIME_V?: number | string | null;
  VELOCITY_Q?: number | string | null;
  LAST_VELOCITY_Q?: number | string | null;
  DIRECT_FLOW?: number | string | null;
  CUMULATIVE_TIME_F?: number | string | null;
  LAST_CUMULATIVE_TIME_F?: number | string | null;
  LAST_DIRECT_FLOW?: number | string | null;
  ROAD_NAME?: number | string | null;
  LENGTH?: number | string | null;
  DIAMETER?: number | string | null;
  WATERSOURCE1?: number | string | null;
  WATERSOURCE2?: number | string | null;
  WATERSOURCE3?: number | string | null;
  WATERSOURCE4?: number | string | null;
  WATERSOURCE5?: number | string | null;
  WATERSOURCE6?: number | string | null;
  WATERSOURCE7?: number | string | null;
  WATERSOURCE8?: number | string | null;
  WATERSOURCE9?: number | string | null;
  WATERSOURCE10?: number | string | null;
}

export interface TrackScada extends HighlightObject {
  id: string;
  title: string;
  indicatorType?: string;
  indicatorName?: string;
  indicatorId?: string;
  scadaValue?: number | string | null;
  usage?: number | string | null;
  modelValue?: number | string | null;
  percent?: number | string | null;
}

export function getRangeValue(data: any, field: keyof TrackDownLink): string {
  let minValue = data[0][field] || 0;
  let maxValue = data[0][field] || 0;
  data.forEach((item: { [x: string]: number }) => {
    if (item[field] && minValue > item[field]) {
      minValue = item[field];
    }
    if (item[field] && maxValue < item[field]) {
      maxValue = item[field];
    }
  });
  return `${minValue}~${maxValue}`;
}
