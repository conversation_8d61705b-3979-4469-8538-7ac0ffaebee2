/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeviceCollection } from './device';
import { HighlightObject } from './highlight-object';
import { makeObjectId } from './object-item';

export interface IndicatorType {
  otype: string;
  icon: string;
  title: string;
}

export function formatNumberValue(
  value: string | null | undefined,
): number | undefined {
  if (value === undefined || value === null) return undefined;
  const number = Number(value);
  if (Number.isNaN(number) || !Number.isFinite(number)) return undefined;
  return number;
}

export class IndicatorTypeCollection {
  private _indicators: Map<string, IndicatorType>; // key is otype

  constructor() {
    this._indicators = new Map<string, IndicatorType>();
  }

  getIndicatorType(otype: string): IndicatorType | undefined {
    return this._indicators.get(otype);
  }

  initialize(data: {}[]) {
    this._indicators.clear();
    data.forEach((element: any) => {
      const indicator: IndicatorType = {
        otype: element.otype,
        icon: element.icon,
        title: element.title,
      };
      this._indicators.set(indicator.otype, indicator);
    });
  }
}

export class IndicatorObject {
  private _data: Map<string, any> = new Map();

  constructor(data: {}) {
    this.updateIndicatorData(data);
    this._visible = false;
  }

  private _indicatorType: IndicatorType | undefined;

  get indicatorType(): IndicatorType | undefined {
    return this._indicatorType;
  }

  set indicatorType(type: IndicatorType | undefined) {
    this._indicatorType = type;
  }

  private _id: string = '';

  get id(): string {
    return this._id;
  }

  private _oname: string = '';

  get oname(): string {
    return this._oname;
  }

  private _otype: string = '';

  get otype(): string {
    return this._otype;
  }

  private _pname: string | undefined;

  get pname(): string | undefined {
    return this._pname;
  }

  private _ptype: string | undefined;

  get ptype(): string | undefined {
    return this._ptype;
  }

  private _title: string | undefined;

  get title(): string | undefined {
    return this._title;
  }

  private _shortTitle: string | undefined;

  get shortTitle(): string | undefined {
    return this._shortTitle;
  }

  private _maxLimitation: number | undefined;

  private _minLimitation: number | undefined;

  get maxLimitation(): number | undefined {
    return this._maxLimitation;
  }

  get minLimitation(): number | undefined {
    return this._minLimitation;
  }

  private _visible: boolean | undefined;

  set setVisible(visible: boolean | undefined) {
    this._visible = visible;
  }

  get visible(): boolean | undefined {
    return this._visible;
  }

  updateIndicatorData(data: any) {
    const dataMap = new Map(Object.entries(data));
    [...dataMap].forEach(([key, value]) => {
      this._data.set(key, value);
    });

    const otype = this._data.get('OTYPE');
    if (typeof otype !== 'string') return;
    if (!otype.startsWith('SDVAL_')) return;

    const oname = this._data.get('ONAME');
    if (typeof oname !== 'string') return;
    const pname = this._data.get('PNAME') as string;
    const ptype = this._data.get('PTYPE') as string;
    let maxLimitation: number | undefined;
    let minLimitation: number | undefined;
    // fix #10675;  maxLimitation 不能为NaN
    if (this._data.has('LIMIT_MAX')) {
      maxLimitation = formatNumberValue(this._data.get('LIMIT_MAX'));
    }
    if (this._data.has('LIMIT_MIN')) {
      minLimitation = formatNumberValue(this._data.get('LIMIT_MIN'));
    }
    const title = this._data.get('TITLE') ?? undefined;
    const shortTitle = this._data.get('SHORT_TITLE') ?? undefined;
    this._otype = otype;
    this._oname = oname;
    this._ptype = ptype;
    this._pname = pname;
    this._maxLimitation = maxLimitation;
    this._minLimitation = minLimitation;
    this._title = title;
    this._shortTitle = shortTitle;
    this._id = makeObjectId(this._otype, this._oname);
  }

  getPropertyValue(vprop: string): any {
    return this._data.get(vprop);
  }
}

export class IndicatorObjectCollection {
  private _indicatorObjects: Map<string, IndicatorObject>;

  private _indicatorGroup: Map<string, IndicatorObject[]>;

  constructor() {
    this._indicatorObjects = new Map();
    this._indicatorGroup = new Map();
  }

  addIndicator(indicator: IndicatorObject) {
    this._indicatorObjects.set(indicator.id, indicator);
  }

  addIndicatorGroup(parentId: string, indicator: IndicatorObject) {
    const indicatorGroup = this._indicatorGroup.get(parentId);
    if (indicatorGroup) {
      indicatorGroup.push(indicator);
    } else {
      this._indicatorGroup.set(parentId, [indicator]);
    }
  }

  getIndicator(otype: string, oname: string): IndicatorObject | undefined {
    const id = makeObjectId(otype, oname);
    return this.getIndicatorById(id);
  }

  getIndicatorById(id: string): IndicatorObject | undefined {
    return this._indicatorObjects.get(id);
  }

  getIndicatorGroup(
    ptype: string,
    pname: string,
  ): IndicatorObject[] | undefined {
    const pid = makeObjectId(ptype, pname);
    return this.getIndicatorGroupByPId(pid);
  }

  getIndicatorGroupByPId(pid: string): IndicatorObject[] | undefined {
    return this._indicatorGroup.get(pid);
  }

  initializeIndicators(data: {}[], deviceCollection: DeviceCollection) {
    this._indicatorObjects.clear();
    data.forEach((element: {}) => {
      const indicator = new IndicatorObject(element);
      if (
        indicator.oname &&
        indicator.otype &&
        indicator.otype.startsWith('SDVAL_')
      ) {
        const device = deviceCollection.getDevice(
          indicator.ptype as string,
          indicator.pname as string,
        );
        if (device) {
          device.addIndicator(indicator);
        }
        this.addIndicator(indicator);
        this.addIndicatorGroup(
          makeObjectId(indicator.ptype as string, indicator.pname as string),
          indicator,
        );
      }
    });
  }

  updateIndicatorInfoById(id: string, info: { [key: string]: any }) {
    const indicatorInfo = this._indicatorObjects.get(id);
    if (indicatorInfo) {
      indicatorInfo.updateIndicatorData(info);
    }
  }
}

export interface PressureRelativeIndicatorValue extends HighlightObject {
  key: string;
  id: string;
  otype: string;
  oname: string;
  indicatorType: string;
  indicatorName: string;
  title: string;
  coeff: number;
  scadaValue: number | undefined;
  coeffWithSymbol: string;
  scadaValueUnit: string | undefined;
  modelCoeff: number | null;
  modelCoeffWithSymbol: string;
}

export interface FlowRelativeIndicatorValue extends HighlightObject {
  key: string;
  id: string;
  otype: string;
  oname: string;
  indicatorType: string;
  indicatorName: string;
  title: string;
  relation: string;
  scadaValue: number | undefined;
  scadaValueUnit: string | undefined;
}

export function isIndicator(id: string): boolean {
  if (typeof id === 'string' && id.startsWith('SDVAL_')) return true;
  return false;
}
