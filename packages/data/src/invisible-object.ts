/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InvisibleObjectInfo } from './const/system-const';
import { IndicatorObject } from './indicator';
import {
  getShapeType,
  IObjectItem,
  makeObjectId,
  SHAPE_TYPE,
} from './object-item';

export default class InvisibleObject implements IObjectItem {
  constructor(data: InvisibleObjectInfo) {
    const { oname, otype, title } = data;
    this._id = makeObjectId(otype, oname);
    this._otype = otype;
    this._oname = oname;
    this._title = title;
    this._shape = undefined;
    this._shapeType = getShapeType(undefined);
    this._indicators = [];
  }

  private _id: string;

  get id(): string {
    return this._id;
  }

  private _oname: string;

  get oname(): string {
    return this._oname;
  }

  private _otype: string;

  get otype(): string {
    return this._otype;
  }

  private _shape: string | undefined;

  get shape(): string | undefined {
    return this._shape;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _shapeType: SHAPE_TYPE;

  get shapeType(): SHAPE_TYPE {
    return this._shapeType;
  }

  private _indicators: Array<IndicatorObject> = [];

  get indicators(): Array<IndicatorObject> {
    return this._indicators;
  }
}
