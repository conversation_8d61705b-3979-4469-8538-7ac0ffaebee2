/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export enum IssueReportProcessState {
  UNPROCESSED = -1,
  PROCESSED = 1,
}

export enum QuestionType {
  /** 监测设备 */
  MONITORING_DEVICE = 'MONITORING_DEVICE',
  /** 管网设备 */
  PIPE_NETWORK_DEVICE = 'PIPE_NETWORK_DEVICE',
  /** 水厂泵站 */
  PUMP_STATION = 'PUMP_STATION',
  /** 水质监测 */
  WATER_QUALITY = 'WATER_QUALITY',
  /** 工程施工 */
  CONSTRUCTION = 'CONSTRUCTION',
  /** 模型精度 */
  MODEL_ACCURACY = 'MODEL_ACCURACY',
}

export const issueReportProcessStateNameMap: {
  [key in IssueReportProcessState]: string;
} = {
  [IssueReportProcessState.UNPROCESSED]: '未处理',
  [IssueReportProcessState.PROCESSED]: '已处理',
};

export const issueReportProcessStateOptions = Object.values(
  IssueReportProcessState,
)
  .filter((value) => typeof value === 'number')
  .map((value) => ({
    label: issueReportProcessStateNameMap[value as IssueReportProcessState],
    value,
  }));

export interface IssueReportListParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  userName?: string; // 创建人(模糊查询)
  isProcess?: IssueReportProcessState; // 是否处理
}

export interface IssueReportList {
  otype: string; // 问题上报类型
  oname: string; // 问题上报编号
  title: string; // 问题上报名称
  shape: string; // shape
  questionType: QuestionType; // 问题类型
  content: string; // 事件内容
  createUser: string; // 创建人
  createTime: string; // 创建时间
  questionState: IssueReportProcessState; // 事件状态
  processContent: string; // 处理描述
  updateUser: string; // 更新人
  updateTime: string; // 更新时间
}

export interface UpdateIssueReportParams {
  otype?: string; // 问题上报类型
  oname?: string; // 问题上报编号
  title?: string; // 问题上报名称
  shape?: string; // shape
  questionType?: QuestionType; // 问题类型
  content?: string; // 事件内容
  questionState?: IssueReportProcessState; // 事件状态
  processContent?: string; // 处理描述
}
