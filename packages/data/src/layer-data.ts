/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Device, { DeviceCollection } from './device';
import {
  ArcgisTileLayerData,
  GisMapLayerData,
  ImageArcGisLayerData,
  TileLayerData,
  WmtsLayerData,
} from './gis-layer-data';
import { CoordinateSystem } from './third-party-data';

export type DisplayType =
  | 'LayerBaidu'
  | 'LayerGroupMap'
  | 'LayerImageArcGISRest'
  | 'LayerWmts'
  | 'LayerTile'
  | 'LayerNetwork'
  | 'LayerFeature'
  | 'MainPipe'
  | 'LayerEngineering';

export interface LayerData {
  name: string;
  type: DisplayType;
  title: string;
  icon: string;
  className: string; // class_name
  minZoom: number | undefined;
  maxZoom: number | undefined;
  projection?: string;
}

export class BaiduLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
  ) {
    this.name = name;
    this.type = 'LayerBaidu';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
  }
}

export class NetworkLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
  ) {
    this.name = name;
    this.type = 'LayerNetwork';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
  }
}

export class FeatureLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  private _devices: Map<string, Device> = new Map<string, Device>();

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
  ) {
    this.name = name;
    this.type = 'LayerFeature';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
  }

  setDevices(devices: Map<string, Device>) {
    this._devices = devices;
  }

  get devices(): Map<string, Device> {
    return this._devices;
  }
}

export class CustomLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  color: string | undefined;

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    color: string | undefined,
  ) {
    this.name = name;
    this.type = 'LayerFeature';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this.color = color;
  }
}

type EngineeringLayerName = 'PERSONNEL_MAP_LAYER' | 'VEHICLE_MAP_LAYER';

export class EngineeringLayerData implements LayerData {
  name: EngineeringLayerName;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  config: {
    personnelIcon?: string;
    vehicleIcon?: string;
    /** 人员图标大小 */
    personnelIconSize?: number;
    /** 车辆图标大小 */
    vehicleIconSize?: number;
    /** 人员数据坐标系 */
    personnelCoordinateSystem?: CoordinateSystem;
    /** 车辆数据坐标系 */
    vehicleCoordinateSystem?: CoordinateSystem;
    /** 人员图标颜色 */
    personnelIconColor?: string;
    /** 车辆图标颜色 */
    vehicleIconColor?: string;
    /** 数据请求延时时间：ms */
    requestDelay?: number;
    /** 是否显示所有弹窗 */
    showAllPopups?: boolean;
  } = {};

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    args: {
      iconSize?: number;
      coordinateSystem?: CoordinateSystem;
      iconColor?: string;
      requestDelay?: number;
      showAllPopups?: boolean;
    },
  ) {
    this.name = name as EngineeringLayerName;
    this.type = 'LayerEngineering';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;

    this.config.requestDelay = args.requestDelay ?? 30000;
    this.config.showAllPopups = args.showAllPopups ?? false;
    if (this.name === 'PERSONNEL_MAP_LAYER') {
      this.config.personnelIcon = icon;
      this.config.personnelIconSize = args.iconSize;
      this.config.personnelIconColor = args.iconColor ?? '#EDA71B';
      this.config.personnelCoordinateSystem = args.coordinateSystem;
    }

    if (this.name === 'VEHICLE_MAP_LAYER') {
      this.config.vehicleIcon = icon;
      this.config.vehicleIconSize = args.iconSize;
      this.config.vehicleIconColor = args.iconColor ?? '#EDA71B';
      this.config.vehicleCoordinateSystem = args.coordinateSystem;
    }
  }
}

export class MainPipeLayerData implements LayerData {
  name: string;

  type: DisplayType;

  title: string;

  icon: string;

  className: string;

  minZoom: number | undefined;

  maxZoom: number | undefined;

  mainPipeConfig: {
    pipeColor: string;
    waterFlowColor: string;
    symbolSize: number;
    zoomDiameter: {
      zoom: number;
      diameter: number;
    }[];
    otypes?: string[];
  };

  constructor(
    name: string,
    className: string,
    title: string,
    icon: string,
    minZoom: number | undefined,
    maxZoom: number | undefined,
    mainPipeConfig: {
      pipeColor: string;
      waterFlowColor: string;
      symbolSize: number;
      zoomDiameter: {
        zoom: number;
        diameter: number;
      }[];
      otypes?: string[];
    },
  ) {
    this.name = name;
    this.type = 'MainPipe';
    this.title = title;
    this.icon = icon;
    this.className = className;
    this.minZoom = minZoom;
    this.maxZoom = maxZoom;
    this.mainPipeConfig = mainPipeConfig;
  }
}

function generateLayerData(
  name: string,
  className: string,
  title: string,
  icon: string,
  minZoom: number | undefined,
  maxZoom: number | undefined,
  args: any,
): LayerData | null {
  switch (className) {
    case 'GisMapLayerNetwork':
      return new NetworkLayerData(
        name,
        className,
        title,
        icon,
        minZoom,
        maxZoom,
      );
    case 'GisMapLayerMap':
      if (args?.type === 'group')
        return new GisMapLayerData(
          name,
          className,
          title,
          icon,
          minZoom,
          maxZoom,
          args.layers,
        );
      if (args?.type === 'ImageArcGISRest')
        return new ImageArcGisLayerData(
          name,
          className,
          title,
          icon,
          minZoom,
          maxZoom,
          args.layer,
          args.identify,
        );
      if (args?.type === 'WMTS')
        return new WmtsLayerData(
          name,
          className,
          title,
          icon,
          minZoom,
          maxZoom,
          args.layer,
        );
      if (args?.type === 'TILE')
        return new TileLayerData(
          name,
          className,
          title,
          icon,
          minZoom,
          maxZoom,
          args.layer,
        );
      if (args?.type === 'ArcgisTILE')
        return new ArcgisTileLayerData(
          name,
          className,
          title,
          icon,
          minZoom,
          maxZoom,
          args.layer,
        );

      return null;
    case 'GisMapLayerBaidu':
      return new BaiduLayerData(name, className, title, icon, minZoom, maxZoom);
    case 'GisMapLayerFeatureMapSDSTATION':
    case 'GisMapLayerFeatureMapFACTORY':
      return new FeatureLayerData(
        name,
        className,
        title,
        icon,
        minZoom,
        maxZoom,
      );
    case 'MapLayerCustom':
      return new CustomLayerData(
        name,
        className,
        title,
        icon,
        minZoom,
        maxZoom,
        args.color,
      );
    case 'MapMainPipe':
      return new MainPipeLayerData(
        name,
        className,
        title,
        icon,
        minZoom,
        maxZoom,
        args.mainPipeConfig,
      );
    case 'MapThirdPartyEngineeringLayer':
      return new EngineeringLayerData(
        name,
        className,
        title,
        icon,
        minZoom,
        maxZoom,
        args,
      );
    default:
      return null;
  }
}

export class LayerDataCollection {
  private _layerDatas: Array<LayerData>;

  private _layerLookup: Map<string, LayerData>;

  constructor() {
    this._layerDatas = [];
    this._layerLookup = new Map<string, LayerData>();
  }

  private addLayer(layer: LayerData) {
    if (this._layerLookup.has(layer.name)) {
      console.warn(`duplicated layer: ${layer.name}`);
      return;
    }
    this._layerLookup.set(layer.name, layer);
    this._layerDatas.push(layer);
  }

  getLayer(name: string): LayerData | undefined {
    return this._layerLookup.get(name);
  }

  get layerDatas(): Array<LayerData> {
    return this._layerDatas;
  }

  initialize(displayLayers: {}[], layerDetails: {}[]) {
    const sortedLayers: Array<[string, string]> = []; // <layer_type_name, class_name>
    const layerInformation: Map<string, any> = new Map<string, any>();
    displayLayers.forEach((element: any) => {
      sortedLayers.push([element.args.layer_name, element.class_name]);
      layerInformation.set(element.args.layer_name, element);
    });

    const allLayersByType: Map<string, LayerData[]> = new Map<
      string,
      LayerData[]
    >();
    layerDetails.forEach((element: any) => {
      const layerInfo = layerInformation.get(element.layer_name);
      const className = layerInfo?.class_name;
      if (className === undefined) {
        console.warn(`unknown layer ${JSON.stringify(element)}`);
        return;
      }

      const {
        name,
        title,
        icon,
        min_draw_ratio: minZoom,
        max_draw_ratio: maxZoom,
      } = element;
      const layer = generateLayerData(
        name,
        className,
        title ?? '',
        icon ?? '',
        minZoom ?? undefined,
        maxZoom ?? undefined,
        layerInfo?.args,
      );
      if (layer === null) return;

      const layerArray = allLayersByType.get(element.layer_name);
      if (layerArray !== undefined) {
        layerArray.push(layer);
      } else {
        const layers: LayerData[] = [layer];
        allLayersByType.set(element.layer_name, layers);
      }
    });

    sortedLayers.forEach((element) => {
      const layerType = element[0];
      const layers: Array<LayerData> | undefined =
        allLayersByType.get(layerType);
      layers?.forEach((layer) => {
        this.addLayer(layer);
      });
    });
  }

  initializeDevices(devicesCollection: DeviceCollection) {
    this._layerDatas.forEach((layer) => {
      if (layer.type === 'LayerFeature') {
        const featureLayer = layer as FeatureLayerData;
        const devices = devicesCollection.getAllDevicesByLayer(
          featureLayer.name,
        );
        if (devices !== undefined) {
          featureLayer.setDevices(devices);
        }
      }
    });
  }
}

export function zoomFilter(
  // todo: map as OLMap
  map: any,
  minZoom: number | undefined,
  maxZoom: number | undefined,
): boolean {
  const currentMapZoom = map.getView().getZoom();
  if (minZoom && currentMapZoom && currentMapZoom < minZoom) {
    return false;
  }
  if (maxZoom && currentMapZoom && currentMapZoom > maxZoom) {
    return false;
  }
  return true;
}

export function parseToNumberArray(config: string | undefined): number[] {
  return config ? JSON.parse(config) : [];
}

export function getMapLayerUrl(
  url: string | undefined,
  urlConfig?: { [key: string]: string },
): string | undefined {
  const { origin } = window.location;
  if (urlConfig) {
    const matchUrl = urlConfig[origin];
    if (matchUrl) {
      return matchUrl.replace('{ORIGIN}', origin);
    }
  }
  if (url) {
    return url.replace('{ORIGIN}', origin);
  }
  return undefined;
}
