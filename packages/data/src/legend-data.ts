/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeObjectId } from './object-item';
import { UnitFormat } from './unit-system';

type LegendItemDataType = 'checkbox' | 'text';
export interface AnimateConfig {
  pipeColor: string;
  waterFlowColor: string;
  symbolSize: number;
}
export interface LegendItemData {
  color: string;
  count: number | undefined; // legend count
  id: number | string; // id is legend_id
  title: string;
  value: string;
  checked: boolean;
  size?: string;
}

export interface LegendGroupData {
  key?: string;
  icon?: string;
  name: string; // legend
  title: string;
  /**
   * @deprecated 即将废弃，使用unit属性替代
   */
  unitSymbol?: string;
  unit?: UnitFormat | undefined;
  type: LegendItemDataType;
  items: LegendItemData[];
  animateConfig?: AnimateConfig;
}

export class LegendGroupDataCollection {
  // eslint-disable-next-line no-use-before-define
  private static _instance: LegendGroupDataCollection;

  static getInstance(): LegendGroupDataCollection {
    if (LegendGroupDataCollection._instance == null) {
      LegendGroupDataCollection._instance = new LegendGroupDataCollection();
    }

    return LegendGroupDataCollection._instance;
  }

  private _legendDataCollection: LegendGroupData[];

  private _deviceColorData: Map<string, string>;

  private static convertColor(color: string): string | 'hidden' {
    if (typeof color === 'string' && color.trim().length === 8) {
      return `#${color.trim().substring(2, 8)}${color.trim().substring(0, 2)}`;
    }
    if (typeof color === 'string' && color.trim().length === 6) {
      return `#${color}`;
    }
    return '';
  }

  private static formatCount(
    count?: number | string | null,
  ): number | undefined {
    if (count === null) return undefined;
    if (!Object.is(Number(count), NaN)) {
      return Number(count);
    }
    return undefined;
  }

  private static generateLegendItemData(dataValue: {
    [index: string]: any;
  }): LegendItemData {
    return {
      color: LegendGroupDataCollection.convertColor(dataValue.color || ''),
      count: LegendGroupDataCollection.formatCount(dataValue.legend_count),
      id: dataValue.legend_id,
      value: dataValue.value || '',
      title: dataValue.title || '未定义',
      checked: !dataValue.legend_hidden,
      size: dataValue.size,
    };
  }

  private static generateLegendGroupData(
    _key: string,
    dataItem: { [index: string]: any },
  ): LegendGroupData {
    const legendItems: LegendItemData[] = [];
    if (Array.isArray(dataItem.values)) {
      dataItem.values.forEach((item) => {
        const legendItem =
          LegendGroupDataCollection.generateLegendItemData(item);
        legendItems.push(legendItem);
      });
    }
    return {
      icon: dataItem.icon,
      name: dataItem.legend,
      title: dataItem.title || '未定义',
      items: legendItems,
      type: dataItem.legend ? 'checkbox' : 'text',
    };
  }

  /**
   * 对图例项进行去重，根据 id 进行去重
   * @param items 图例项数组
   * @returns 去重后的图例项数组
   */
  private static deduplicateLegendItems(
    items: LegendItemData[],
  ): LegendItemData[] {
    const seen = new Set<string | number>();
    return items.filter((item) => {
      // 不对 null、undefined 或空字符串的 id 进行去重
      if (item.id == null || item.id === '') {
        return true;
      }

      if (seen.has(item.id)) {
        return false;
      }
      seen.add(item.id);
      return true;
    });
  }

  /**
   * 对图例组进行去重，根据 name 和 title 的组合进行去重
   * @param legendGroups 图例组数组
   * @returns 去重后的图例组数组
   */
  private static deduplicateLegendGroups(
    legendGroups: LegendGroupData[],
  ): LegendGroupData[] {
    const groupMap = new Map<string, LegendGroupData>();
    let uniqueCounter = 0;

    legendGroups.forEach((group) => {
      // 只对有效的 name 和 title 进行去重
      const shouldDeduplicate =
        group.name != null &&
        group.name !== '' &&
        group.name !== 'undefined' &&
        group.title != null &&
        group.title !== '' &&
        group.title !== '未定义';

      if (!shouldDeduplicate) {
        // 对于无效数据，不去重，每个都创建唯一键
        const uniqueKey = `invalid_${uniqueCounter}`;
        uniqueCounter += 1;
        groupMap.set(uniqueKey, {
          ...group,
          items: LegendGroupDataCollection.deduplicateLegendItems(group.items),
        });
      } else {
        const key = `${group.name}_${group.title}`;

        if (!groupMap.has(key)) {
          groupMap.set(key, {
            ...group,
            items: LegendGroupDataCollection.deduplicateLegendItems(
              group.items,
            ),
          });
        } else {
          const existingGroup = groupMap.get(key)!;
          const mergedItems = [...existingGroup.items, ...group.items];
          existingGroup.items =
            LegendGroupDataCollection.deduplicateLegendItems(mergedItems);
        }
      }
    });

    return Array.from(groupMap.values());
  }

  constructor() {
    this._legendDataCollection = [];
    this._deviceColorData = new Map();
  }

  updateLegendData(legendData: LegendGroupData[]) {
    this._legendDataCollection =
      LegendGroupDataCollection.deduplicateLegendGroups(legendData);
  }

  updateDeviceColorData(colorData: Map<string, string>) {
    this._deviceColorData = colorData;
  }

  initialize(legendData: any, colorData: any) {
    this._legendDataCollection = [];
    if (legendData) {
      const keys = Object.keys(legendData).sort((a, b) => a.localeCompare(b));
      keys.forEach((key) => {
        const values = legendData[key];
        if (values?.length) {
          values.forEach((dataItem: any, index: number) => {
            this._legendDataCollection.push(
              LegendGroupDataCollection.generateLegendGroupData(
                `${key}-${index}`,
                dataItem,
              ),
            );
          });
        }
      });
      this._legendDataCollection =
        LegendGroupDataCollection.deduplicateLegendGroups(
          this._legendDataCollection,
        );
    }

    this._deviceColorData = new Map();
    if (colorData) {
      Object.keys(colorData).forEach((otype) => {
        Object.keys(colorData[otype]).forEach((oname) => {
          const dataItem = colorData[otype][oname];
          const deviceId = makeObjectId(otype, oname);
          this._deviceColorData.set(
            deviceId,
            LegendGroupDataCollection.convertColor(dataItem.color),
          );
        });
      });
    }
  }

  get deviceColorData(): Map<string, string> {
    return this._deviceColorData;
  }

  get legendDataCollection(): LegendGroupData[] {
    return this._legendDataCollection;
  }
}

export function getCurrentLegendData(): LegendGroupData[] {
  return LegendGroupDataCollection.getInstance().legendDataCollection;
}

export function getCurrentDeviceColorData(): Map<string, string> {
  return LegendGroupDataCollection.getInstance().deviceColorData;
}

export function getCurrentDeviceColor(deviceId: string): string | undefined {
  return getCurrentDeviceColorData().get(deviceId);
}

export function updateCurrentLegendData(legendData: LegendGroupData[]) {
  return LegendGroupDataCollection.getInstance().updateLegendData(legendData);
}

export function updateCurrentDeviceColorData(colorData: Map<string, string>) {
  return LegendGroupDataCollection.getInstance().updateDeviceColorData(
    colorData,
  );
}
