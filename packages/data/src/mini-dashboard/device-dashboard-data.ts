/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { AssessmentFormItem, AssessmentScoreItem } from '../app-config';
import Database from '../database';
import Device, { PropertyValue } from '../device';
import { makeObjectId } from '../object-item';
import { DefaultOptionType, Key } from '../types';
import { getUnitValue } from '../unit-system';
import { formatNumber } from '../utils';

type DeviceOType = string;

export interface DashboardFormConfig {
  [index: string]: string | string[];
}
export interface DeviceAssessmentInfo {
  id: string;
  oname: string;
  otype: string;
  /** 可信度 */
  reliability: string | null;
  durationAbs: number | null;
  unit?: Key;
  deviceName?: string;
  shape?: string;
  shapeType?: string;
  mainCase?: string;
  parentTypeName?: string;
  summarize: boolean;
}

export interface AssessmentInfo {
  key: string;
  status: string | null;
  statusName: string;
  devices: DeviceAssessmentInfo[];
  rate: number;
}

export interface AssessmentDataScoreItem {
  score: string | number | undefined;
  time: string;
  title: string;
  otype: string;
  oname: string;
  vprop: string;
  helpDescription?: string;
}

export interface InitialDeviceInfo {
  [index: string]: string | number | null;
}

/** Map<string as otype,Map<string as oname, InitialDeviceInfo>> */
export type InitialDeviceInfoValues = Map<
  string,
  Map<string, InitialDeviceInfo>
>;

export interface FormConfigValue extends AssessmentFormItem {
  options: string[];
}

export interface SearchForm extends AssessmentFormItem {
  options: DefaultOptionType[];
}

const generateOptions = (
  db: Database,
  options: string[],
): DefaultOptionType[] =>
  // 去重
  [...new Set([...options])].map((value) => {
    const label = db.getPropertyInfo(value)?.title || value;
    return {
      label: label || value,
      value,
    };
  });

/**
 * 根据已有信息生成设备计算信息
 * @param oname
 * @param otype
 * @param deviceInfo
 * @param dataValue: InitialDeviceInfo
 * @returns DeviceAssessmentInfo
 */
const generateDeviceAssessmentInfo = (
  db: Database,
  oname: string,
  otype: string,
  deviceInfo: Device | undefined,
  dataValue: InitialDeviceInfo,
): DeviceAssessmentInfo => {
  const unit = getUnitValue('RELIABILITY', dataValue.reliability || '');
  const bottleneckUnit = getUnitValue(
    'Bottleneck',
    dataValue.BOTTLENECK_S || '',
  ) as string;
  const layer = db.layerCollection.getLayer(deviceInfo?.layerName || '');
  return {
    id: makeObjectId(otype, oname),
    oname,
    otype,
    reliability: dataValue.RELIABILITY_S
      ? dataValue.RELIABILITY_S.toString()
      : null,
    durationAbs: dataValue.STATE_DAYS
      ? Math.abs(Number(dataValue.STATE_DAYS))
      : 0,
    unit,
    deviceName: deviceInfo?.title,
    shape: deviceInfo?.shape,
    mainCase: bottleneckUnit || dataValue.BOTTLENECK_S?.toString(),
    shapeType: deviceInfo?.shapeType,
    parentTypeName: layer?.title,
    summarize: deviceInfo?.summarize ?? true,
  };
};

const getSameReliabilityDevices = (
  reliability: string | null,
  deviceInfoList: DeviceAssessmentInfo[],
): DeviceAssessmentInfo[] =>
  deviceInfoList.filter((item) => item.reliability === reliability);

const getOthersReliabilityDevices = (
  excludes: Array<string | null>,
  deviceInfoList: DeviceAssessmentInfo[],
): DeviceAssessmentInfo[] =>
  deviceInfoList.filter((item) => !excludes.includes(item.reliability));

const reliabilityUnit = new Map([
  ['4', '优'],
  ['3', '良'],
  ['2', '差'],
  ['1', '坏'],
  ['0', '未评估'],
]);

export class DeviceDashboardCollection {
  _formConfig: Map<string, FormConfigValue>;

  _ptypeOptions: Map<
    string,
    {
      ptype: string;
      pname: string;
      ptitle: string;
    }
  >;

  _catchDevice: Map<string, Device>;

  _deviceAssessmentInfoList: Map<DeviceOType, DeviceAssessmentInfo[]>;

  constructor() {
    this._formConfig = new Map();
    this._ptypeOptions = new Map();
    this._catchDevice = new Map();
    this._deviceAssessmentInfoList = new Map();
  }

  /**
   * 选择符合筛选条件的device
   * @param formValue
   * @returns
   */
  selectDevices(formValue: DashboardFormConfig): DeviceAssessmentInfo[] {
    const selectDevicesInfoValues: DeviceAssessmentInfo[] = [];
    this._deviceAssessmentInfoList.forEach(
      (mapValues: DeviceAssessmentInfo[]) => {
        if (!mapValues?.length) return;
        mapValues.forEach((deviceInfoValue: DeviceAssessmentInfo) => {
          const deviceId = makeObjectId(
            deviceInfoValue.otype,
            deviceInfoValue.oname,
          );
          const deviceInfo = this._catchDevice.get(deviceId);
          let flag = true;
          Object.keys(formValue).forEach((key) => {
            const value = formValue[key];
            // 多选时的匹配，空数据不做过滤，默认通过
            if (Array.isArray(value)) {
              if (
                value.length > 0 &&
                !value.includes(deviceInfo?.[key as keyof Device] as string)
              )
                flag = false;
            } else if (
              value !== '' &&
              value !== (deviceInfo?.[key as keyof Device] as string)
            ) {
              flag = false;
            }
          });
          if (flag) {
            selectDevicesInfoValues.push(deviceInfoValue);
          }
        });
      },
    );

    return selectDevicesInfoValues;
  }

  generateAssessmentData(formValue: DashboardFormConfig): AssessmentInfo[] {
    const filterDevices: DeviceAssessmentInfo[] = this.selectDevices(formValue);
    if (!filterDevices.length) return [];

    const list: AssessmentInfo[] = [];
    reliabilityUnit.forEach((reliabilityName, reliability) => {
      // 统计相同可信度的一组设备
      let sameReliabilityDeviceList = getSameReliabilityDevices(
        reliability,
        filterDevices,
      );

      // 将reliabilityUnit之外的全部统计到未评估之中
      if (reliability === '0') {
        const other = getOthersReliabilityDevices(
          [...reliabilityUnit.keys()],
          filterDevices,
        );
        sameReliabilityDeviceList = [...sameReliabilityDeviceList, ...other];
      }
      // 统计占比
      const rate: number = formatNumber(
        (sameReliabilityDeviceList.length / filterDevices.length) * 100,
        2,
      );

      list.push({
        key: reliabilityName,
        status: reliability,
        statusName: reliabilityName,
        devices: sameReliabilityDeviceList,
        rate,
      });
    });
    return list;
  }

  generateSearchFormList(db: Database): Array<SearchForm> {
    const formList: Array<SearchForm> = [];
    this._formConfig.forEach((value) => {
      if (value.enable) {
        const options = generateOptions(db, value.options);
        formList.push({ ...value, options });
      }
    });
    return formList.sort((a, b) => a.index - b.index);
  }

  initialize(
    db: Database,
    data: InitialDeviceInfoValues,
    formConfig: Map<string, AssessmentFormItem>,
  ) {
    this._catchDevice.clear();
    this._deviceAssessmentInfoList.clear();

    const selectDevice: Map<string, Device> = new Map();
    const selectFormConfig: Map<string, FormConfigValue> = new Map();

    data.forEach((deviceClueMap, otype) => {
      const selectDeviceAssessmentInfo: DeviceAssessmentInfo[] = [];
      deviceClueMap.forEach((deviceClue, oname) => {
        const deviceId = makeObjectId(otype, oname);
        const deviceInfo = db.getDeviceById(deviceId);
        const deviceAssessmentInfo = generateDeviceAssessmentInfo(
          db,
          oname,
          otype,
          deviceInfo,
          deviceClue,
        );
        selectDeviceAssessmentInfo.push(deviceAssessmentInfo);

        if (deviceInfo?.layerName) {
          const title =
            db.getPropertyInfo(deviceInfo.layerName)?.title ||
            deviceInfo.layerName;
          this._ptypeOptions.set(deviceInfo?.layerName, {
            pname: deviceInfo?.pname,
            ptype: deviceInfo?.layerName,
            ptitle: title,
          });
        }

        // 获取设备信息和一些下拉选项（设备区域、设备类型、设备来源）
        if (typeof deviceInfo !== 'undefined') {
          selectDevice.set(deviceId, deviceInfo);
        }
        formConfig.forEach((value, key) => {
          const config: FormConfigValue = selectFormConfig.get(key) ?? {
            ...value,
            options: [],
          };
          if (
            deviceInfo?.[key as keyof Device] &&
            deviceInfo?.[key as keyof Device] !== 'null'
          ) {
            config.options.push(deviceInfo[key as keyof Device]);
          }
          // 将初始值合并到选项中
          config.options = [...config.options, ...config.initialValues];
          selectFormConfig.set(key, config);
        });
      });
      this._deviceAssessmentInfoList.set(otype, selectDeviceAssessmentInfo);
    });
    this._formConfig = selectFormConfig;
    this._catchDevice = selectDevice;
  }
}

export const getScoreData = (
  data: Map<string, PropertyValue> | undefined,
  scoreConfig: AssessmentScoreItem[],
  time: string,
  db: Database,
): AssessmentDataScoreItem[] => {
  const scoreInfo: AssessmentDataScoreItem[] = [];
  if (data) {
    scoreConfig.forEach(
      ({ helpDescription, vprop, title, dateformat = 'YYYY-MM-DD' }) => {
        const dataValue = data.get(vprop);
        if (dataValue) {
          const { otype, oname, value } = dataValue;
          db.getUnitFormat(otype, vprop)?.getValueWithSymbol(
            value as unknown as number,
          );
          const scoreSymbol = db
            .getUnitFormat(otype, vprop)
            ?.getValueWithSymbol(value as unknown as number);
          scoreInfo.push({
            score: scoreSymbol,
            time: dayjs(time).format(dateformat),
            title:
              title ??
              db.getPropertyInfo(otype)?.getPropertyTitle(vprop) ??
              vprop,
            otype,
            oname,
            vprop,
            helpDescription,
          });
        }
      },
    );
  }

  return scoreInfo;
};

export const generateSearchInfo = (
  formValues: DashboardFormConfig | undefined,
  formList: SearchForm[],
): string => {
  let searchInfo = '';
  if (typeof formValues === 'undefined') return searchInfo;
  Object.entries(formValues).forEach((item) => {
    const [key, options]: [string, string | string[]] = item;
    const formListItem = formList.find((item) => item.field === key);
    if (formListItem) {
      const { fieldName, options: formListItemOptions } = formListItem;
      const optionsStr = Array.isArray(options)
        ? options
            .map(
              (key) =>
                formListItemOptions.find((item) => item.value === key)?.label,
            )
            .join(',') || '无'
        : formListItemOptions.find((item) => item.value === options)?.label;
      searchInfo += `${fieldName}:${optionsStr};`;
    }
  });

  if (searchInfo !== '') {
    searchInfo = `(${searchInfo})`;
  }

  return searchInfo;
};
