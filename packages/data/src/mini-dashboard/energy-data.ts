/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from '../database';
import { PropertyValue, PumpInfo, StationDevice, StationType } from '../device';
import { makeId, makeObjectId } from '../object-item';
import { UnitFormat } from '../unit-system';

export const PLANT_COLOR = '#FD6A37';
export const PUMP_COLOR = '#F0CB69';
export const EFFICIENCY_COLOR = '#5FB7E5';

type ChartCategoryVprop =
  | 'TOTAL_ENERGY'
  | 'TOTAL_EFFICIENCY'
  | 'TOTAL_POWER_CONSUMPTION';

export interface EnergyDashboardConfig {
  /** 汇总图标配置 */
  charts: [ChartCategoryVprop, ChartCategoryVprop];
  /** 显示的属性字段 */
  stationProps: Array<{
    /** 厂站 */
    station: string;
    /** 泵 */
    pump?: string;
  }>;
  /** 是否显示泵站 */
  showPumpStation: boolean;
  excludeFactory?: string[];
  excludePumpStation?: string[];
  /** 是否显示模拟值 */
  showModel: boolean;
  energyThreshold: number;
  powerConsumption: number;
}

export interface ScadaChartInfo {
  otype?: string;
  oname?: string;
  vprop?: string;
}

export type StationVpropValue = {
  unitFormat: UnitFormat | undefined;
  scada: {
    otime?: string;
    value: number | string | undefined;
  } & ScadaChartInfo;
  model: {
    otime?: string;
    value: number | string | undefined;
  } & ScadaChartInfo;
};

export type StationDataItem = {
  id: string;
  otype: string;
  oname: string;
  title: string;
  /** 厂站属性 */
} & {
  [vprop: string]: StationVpropValue | undefined;
};

export function formatTime(time: string | undefined): string {
  return time ? dayjs(time).format('MM-DD HH:mm') : '-';
}

export type ValueGroupParams = {
  [index: string]: {
    otype: string;
    oname: string;
    vprop: string;
    rmode?: string;
  };
};

export const REVERSE_MODE = 'CALCULATION';

export function getMapKey(
  otype: string,
  oname: string,
  vprop: string,
  isModel?: boolean,
): string {
  return isModel
    ? makeId(otype, oname, vprop, 'MODEL')
    : makeId(otype, oname, vprop);
}

export function getValueGroupParams(
  stationList: StationDevice[] | PumpInfo[],
  stationProps: EnergyDashboardConfig['stationProps'],
): ValueGroupParams {
  const params: ValueGroupParams = {};
  stationList.forEach((item) => {
    const { otype, oname } = item;
    const stationKey = makeId(otype, oname);
    stationProps.forEach((propItem) => {
      const vprop =
        item instanceof StationDevice ? propItem.station : propItem.pump;
      if (!vprop) return;
      const key = makeId(stationKey, vprop);
      params[key] = {
        otype,
        oname,
        vprop,
        rmode: REVERSE_MODE,
      };
      const modelKey = makeId(key, 'MODEL');
      params[modelKey] = {
        otype,
        oname,
        vprop,
      };
    });
  });
  return params;
}

function getStationDataItem(
  info: StationDevice | PumpInfo,
  originData: Map<string, PropertyValue>,
  stationProps: EnergyDashboardConfig['stationProps'],
  db: Database,
): StationDataItem {
  const { otype, oname, title } = info;
  const stationDataItem: StationDataItem = {
    id: makeObjectId(otype, oname),
    otype,
    oname,
    title,
  } as StationDataItem;
  stationProps.forEach((stationItem) => {
    const vprop =
      info instanceof StationDevice ? stationItem.station : stationItem.pump;
    if (!vprop) return;
    const unitFormat = db.getUnitFormat(otype, vprop);
    const modelData = originData.get(getMapKey(otype, oname, vprop, true));
    const scadaData = originData.get(getMapKey(otype, oname, vprop));
    stationDataItem[vprop] = {
      unitFormat,
      scada: {
        ...scadaData,
        value:
          typeof scadaData?.value === 'undefined'
            ? undefined
            : (unitFormat?.getValue(scadaData?.value) as number),
      },
      model: {
        ...modelData,
        value:
          typeof modelData?.value === 'undefined'
            ? undefined
            : (unitFormat?.getValue(modelData?.value) as number),
      },
    };
  });

  return stationDataItem;
}

export function getStationDataSource(
  originData: Map<string, PropertyValue>,
  stationProps: EnergyDashboardConfig['stationProps'],
  db: Database,
  excludeFactory: string[] = [],
  excludePumpStation: string[] = [],
): {
  // string is id
  factory: Map<string, StationDataItem>;
  pumpStation: Map<string, StationDataItem>;
} {
  const dataSource = {
    factory: new Map(),
    pumpStation: new Map(),
  };
  const excludeStations: string[] = [...excludeFactory, ...excludePumpStation];
  db.getAllStations().forEach((item) => {
    if (excludeStations.includes(item.oname)) return;
    const { id, type } = item;

    const stationDataItem: StationDataItem = getStationDataItem(
      item,
      originData,
      stationProps,
      db,
    );

    if (type === StationType.FACTORY) {
      dataSource.factory.set(id, stationDataItem);
    } else {
      dataSource.pumpStation.set(id, stationDataItem);
    }
  });
  return dataSource;
}

export function getPumpDataSource(
  otype: string,
  oname: string,
  originData: Map<string, PropertyValue>,
  stationProps: EnergyDashboardConfig['stationProps'],
  db: Database,
): Map<string, StationDataItem> {
  const dataSource = new Map();
  const stationDevice = db.getDevice(otype, oname);
  if (stationDevice && stationDevice instanceof StationDevice) {
    const { pumpList } = stationDevice;
    pumpList.forEach((item) => {
      const stationDataItem: StationDataItem = getStationDataItem(
        item,
        originData,
        stationProps,
        db,
      );
      dataSource.set(makeObjectId(item.otype, item.oname), stationDataItem);
    });
  }
  return dataSource;
}
