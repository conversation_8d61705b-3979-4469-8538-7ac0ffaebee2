/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { AssessmentFormItem, AssessmentScoreItem } from '../app-config';
import Database from '../database';
import Device, { PropertyValue } from '../device';
import { makeObjectId, splitObjectId } from '../object-item';
import { DefaultOptionType, Key } from '../types';
import { getUnitValue } from '../unit-system';
import { formatNumber } from '../utils';
import { DeviceDashboardCollection } from './device-dashboard-data';

type IndicatorOType = string;

export interface DashboardFormConfig {
  [index: string]: string | string[];
}
export interface IndicatorAssessmentInfo {
  id: string;
  oname: string;
  otype: string;
  /** 可信度 */
  reliability: string | null;
  durationAbs: number | null;
  pid?: string;
  unit?: Key;
  deviceName?: string;
  indicatorName?: string;
  shape?: string;
  shapeType?: string;
  mainCase?: string;
  parentTypeName?: string;
  score?: number;
  summarize: boolean;
}

export interface AssessmentInfo {
  key: string;
  status: string | null;
  statusName: string;
  devices: IndicatorAssessmentInfo[];
  rate: number;
}

export interface AssessmentDataScoreItem {
  score: string | number | undefined;
  time: string;
  title: string;
  otype: string;
  oname: string;
  vprop: string;
  helpDescription?: string;
}

export interface InitialDeviceInfo {
  [index: string]: string | number | null;
}

/** Map<string as otype,Map<string as oname, InitialDeviceInfo>> */
export type InitialDeviceInfoValues = Map<
  string,
  Map<string, InitialDeviceInfo>
>;

export interface FormConfigValue extends AssessmentFormItem {
  options: string[];
}

export interface SearchForm extends AssessmentFormItem {
  options: DefaultOptionType[];
}

interface SelectIndicatorType {
  selectDevicesInfoValues: IndicatorAssessmentInfo[];
  indicatorScoreMap: {
    [key: string]: { length: number; score: number };
  };
}

export interface IndicatorAssessmentData {
  list: AssessmentInfo[];
  indicatorScore: {
    [key: string]: { length: number; score: number };
  };
}

const getScore = (reliability: string | null) => {
  switch (reliability) {
    case '1':
      return 0;
    case '2':
      return 1;
    case '3':
      return 3;
    case '4':
      return 5;
    case '0':
    default:
      return undefined;
  }
};

/**
 * 根据已有信息生成设备计算信息
 * @param oname
 * @param otype
 * @param deviceInfo
 * @param dataValue: InitialDeviceInfo
 * @returns DeviceAssessmentInfo
 */
const generateDeviceAssessmentInfo = (
  db: Database,
  oname: string,
  otype: string,
  deviceInfo: Device | undefined,
  dataValue: InitialDeviceInfo,
): IndicatorAssessmentInfo => {
  const unit = getUnitValue('RELIABILITY_S', dataValue.RELIABILITY_S || '');
  const bottleneckUnit = getUnitValue(
    'Bottleneck',
    dataValue.BOTTLENECK_S || '',
  ) as string;
  const layer = db.layerCollection.getLayer(deviceInfo?.layerName || '');
  const indicator = db.getIndicator(otype, oname);
  return {
    id: makeObjectId(otype, oname),
    pid: makeObjectId(deviceInfo?.otype ?? otype, deviceInfo?.oname ?? oname),
    oname,
    otype,
    reliability: dataValue.RELIABILITY_S
      ? dataValue.RELIABILITY_S.toString()
      : null,
    durationAbs: dataValue.STATE_DAYS
      ? Math.abs(Number(dataValue.STATE_DAYS))
      : 0,
    unit,
    deviceName: deviceInfo?.title,
    indicatorName: indicator?.title,
    shape: deviceInfo?.shape,
    mainCase: bottleneckUnit || dataValue.BOTTLENECK_S?.toString(),
    shapeType: deviceInfo?.shapeType,
    parentTypeName: layer?.title,
    summarize: deviceInfo?.summarize ?? true,
    score: getScore(
      dataValue.RELIABILITY_S ? dataValue.RELIABILITY_S.toString() : null,
    ),
  };
};

const getSameReliabilityDevices = (
  reliability: string | null,
  deviceInfoList: IndicatorAssessmentInfo[],
): IndicatorAssessmentInfo[] =>
  deviceInfoList.filter((item) => item.reliability === reliability);

const getOthersReliabilityDevices = (
  excludes: Array<string | null>,
  deviceInfoList: IndicatorAssessmentInfo[],
): IndicatorAssessmentInfo[] =>
  deviceInfoList.filter((item) => !excludes.includes(item.reliability));

const reliabilityUnit = new Map([
  ['4', '优'],
  ['3', '良'],
  ['2', '差'],
  ['1', '坏'],
  ['0', '未评估'],
]);

export class IndicatorDashboardCollection extends DeviceDashboardCollection {
  private _assessmentInfoList: Map<IndicatorOType, IndicatorAssessmentInfo[]>;

  constructor() {
    super();
    this._formConfig = new Map();
    this._ptypeOptions = new Map();
    this._catchDevice = new Map();
    this._assessmentInfoList = new Map();
  }

  /**
   * 选择符合筛选条件的device
   * @param formValue
   * @returns
   */
  selectIndicator(formValue: DashboardFormConfig): SelectIndicatorType {
    const selectDevicesInfoValues: IndicatorAssessmentInfo[] = [];
    const indicatorScoreMap: {
      [key: string]: { length: number; score: number };
    } = {};
    [...this._assessmentInfoList.keys()].forEach((element: string) => {
      indicatorScoreMap[element] = {
        score: 0,
        length: 0,
      };
    });
    this._assessmentInfoList.forEach(
      (mapValues: IndicatorAssessmentInfo[], key: string) => {
        if (!mapValues?.length) return;

        mapValues.forEach((deviceInfoValue: IndicatorAssessmentInfo) => {
          const deviceId = deviceInfoValue.pid;
          if (!deviceId) {
            return;
          }
          const deviceInfo = this._catchDevice.get(deviceId);
          let flag = true;
          Object.keys(formValue).forEach((key) => {
            if (key === 'otype' || key === 'ptype') return;
            const value = formValue[key];
            // 多选时的匹配，空数据不做过滤，默认通过
            if (Array.isArray(value)) {
              if (
                value.length > 0 &&
                !value.includes(deviceInfo?.[key as keyof Device] as string)
              )
                flag = false;
            } else if (
              value !== '' &&
              value !== (deviceInfo?.[key as keyof Device] as string)
            ) {
              flag = false;
            }
          });
          const otypeFilter = formValue.otype;
          if (otypeFilter?.length > 0) {
            if (!otypeFilter.includes(deviceInfoValue.otype)) {
              flag = false;
              return;
            }
          }

          const ptypeFilter = formValue.ptype;
          if (ptypeFilter?.length > 0) {
            const [otype] = splitObjectId(deviceInfoValue.pid as string);
            if (!ptypeFilter.includes(otype as string)) {
              flag = false;
              return;
            }
          }

          if (flag) {
            if (typeof deviceInfoValue.score !== 'undefined') {
              indicatorScoreMap[key].score += deviceInfoValue.score ?? 0;
              indicatorScoreMap[key].length += 1;
            }
            selectDevicesInfoValues.push(deviceInfoValue);
          }
        });
      },
    );
    return { selectDevicesInfoValues, indicatorScoreMap };
  }

  generateIndicatorAssessmentData(
    formValue: DashboardFormConfig,
  ): IndicatorAssessmentData {
    const selectIndicator: SelectIndicatorType =
      this.selectIndicator(formValue);
    const filterIndicator = selectIndicator.selectDevicesInfoValues;
    if (!filterIndicator.length) return { list: [], indicatorScore: {} };
    const list: AssessmentInfo[] = [];
    reliabilityUnit.forEach((reliabilityName, reliability) => {
      // 统计相同可信度的一组设备
      let sameReliabilityDeviceList = getSameReliabilityDevices(
        reliability,
        filterIndicator,
      );

      // 将reliabilityUnit之外的全部统计到未评估之中
      if (reliability === '0') {
        const other = getOthersReliabilityDevices(
          [...reliabilityUnit.keys()],
          filterIndicator,
        );
        sameReliabilityDeviceList = [...sameReliabilityDeviceList, ...other];
      }
      // 统计占比
      const rate: number = formatNumber(
        (sameReliabilityDeviceList.length / filterIndicator.length) * 100,
        2,
      );

      list.push({
        key: reliabilityName,
        status: reliability,
        statusName: reliabilityName,
        devices: sameReliabilityDeviceList,
        rate,
      });
    });
    return { list, indicatorScore: selectIndicator.indicatorScoreMap };
  }

  initialize(
    db: Database,
    data: InitialDeviceInfoValues,
    formConfig: Map<string, AssessmentFormItem>,
  ) {
    this._catchDevice.clear();
    this._assessmentInfoList.clear();

    const selectDevice = new Map<string, Device>();
    const selectFormConfig = new Map<string, FormConfigValue>();

    const addToFormConfig = (
      key: string,
      value: string | undefined | null,
      baseConfig: AssessmentFormItem,
    ) => {
      if (value === null || value === undefined || value === 'null') return;
      const config = selectFormConfig.get(key) ?? {
        ...baseConfig,
        options: [],
      };
      config.options.push(value, ...baseConfig.initialValues);
      selectFormConfig.set(key, config);
    };

    data.forEach((deviceClueMap, otype) => {
      const selectDeviceAssessmentInfo: IndicatorAssessmentInfo[] = [];
      const otypes = new Set<string>();
      const ptypes = new Set<string>();

      deviceClueMap.forEach((deviceClue, oname) => {
        const indicator = db.getIndicator(otype, oname);
        if (!indicator) return;

        const deviceId = makeObjectId(indicator.ptype!, indicator.pname!);
        const deviceInfo = db.getDeviceById(deviceId);
        if (!deviceInfo) return;

        // 生成评估信息
        const assessmentInfo = generateDeviceAssessmentInfo(
          db,
          oname,
          otype,
          deviceInfo,
          deviceClue,
        );
        selectDeviceAssessmentInfo.push(assessmentInfo);

        // 设备下拉项
        if (deviceInfo.layerName) {
          const title =
            db.getPropertyInfo(deviceInfo.layerName)?.title ||
            deviceInfo.layerName;
          this._ptypeOptions.set(deviceInfo.layerName, {
            pname: deviceInfo.pname,
            ptype: deviceInfo.layerName,
            ptitle: title,
          });
        }

        // 添加设备信息
        selectDevice.set(deviceId, deviceInfo);

        // 表单项收集除otype/ptype外其他字段
        [...formConfig.entries()].forEach(([key, formItem]) => {
          if (key === 'otype' || key === 'ptype') return;
          addToFormConfig(
            key,
            deviceInfo[key as keyof Device] as string,
            formItem,
          );
        });

        otypes.add(otype);
        ptypes.add(deviceInfo.otype);
      });

      // 设置 otype 配置项
      const otypeConfig = formConfig.get('otype');
      if (otypeConfig) {
        const config = selectFormConfig.get('otype') ?? {
          ...otypeConfig,
          options: [],
        };
        config.options.push(...otypes);
        selectFormConfig.set('otype', config);
      }

      // 设置 ptype 配置项
      const ptypeConfig = formConfig.get('ptype');
      if (ptypeConfig) {
        const config = selectFormConfig.get('ptype') ?? {
          ...ptypeConfig,
          options: [],
        };
        config.options.push(...ptypes);
        selectFormConfig.set('ptype', config);
      }

      this._assessmentInfoList.set(otype, selectDeviceAssessmentInfo);
    });

    this._formConfig = selectFormConfig;
    this._catchDevice = selectDevice;
  }
}

export const getScoreData = (
  data: Map<string, PropertyValue> | undefined,
  scoreConfig: AssessmentScoreItem[],
  time: string,
  db: Database,
): AssessmentDataScoreItem[] => {
  const scoreInfo: AssessmentDataScoreItem[] = [];
  if (data) {
    scoreConfig.forEach(
      ({ helpDescription, vprop, title, dateformat = 'YYYY-MM-DD' }) => {
        const dataValue = data.get(vprop);
        if (dataValue) {
          const { otype, oname, value } = dataValue;
          db.getUnitFormat(otype, vprop)?.getValueWithSymbol(
            value as unknown as number,
          );
          const scoreSymbol = db
            .getUnitFormat(otype, vprop)
            ?.getValueWithSymbol(value as unknown as number);
          scoreInfo.push({
            score: scoreSymbol,
            time: dayjs(time).format(dateformat),
            title:
              title ??
              db.getPropertyInfo(otype)?.getPropertyTitle(vprop) ??
              vprop,
            otype,
            oname,
            vprop,
            helpDescription,
          });
        }
      },
    );
  }

  return scoreInfo;
};

export const generateSearchInfo = (
  formValues: DashboardFormConfig | undefined,
  formList: SearchForm[],
): string => {
  let searchInfo = '';
  if (typeof formValues === 'undefined') return searchInfo;
  Object.entries(formValues).forEach((item) => {
    const [key, options]: [string, string | string[]] = item;
    const formListItem = formList.find((item) => item.field === key);
    if (formListItem) {
      const { fieldName, options: formListItemOptions } = formListItem;
      const optionsStr = Array.isArray(options)
        ? options
            .map(
              (key) =>
                formListItemOptions.find((item) => item.value === key)?.label,
            )
            .join(',') || '无'
        : formListItemOptions.find((item) => item.value === options)?.label;
      searchInfo += `${fieldName}:${optionsStr};`;
    }
  });

  if (searchInfo !== '') {
    searchInfo = `(${searchInfo})`;
  }

  return searchInfo;
};
