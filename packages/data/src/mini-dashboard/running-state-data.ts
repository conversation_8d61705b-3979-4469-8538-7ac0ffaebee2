/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import lodash from 'lodash';
import { SystemKettleLogList } from '../system-kettle-log';
import { TimeData } from '../time-data';

dayjs.extend(duration);

export type PassRateType = 'pressure' | 'quality' | 'modelScore';

export const PRESSURE: PassRateType = 'pressure';
export const QUALITY: PassRateType = 'quality';
export const MODELSCORE: PassRateType = 'modelScore';

export interface SeriesData {
  key: PassRateType;
  name: string;
  data: TimeData[];
}

export interface MessageInfo {
  id: string;
  title: string;
  createTime: string;
  description: string;
  content: string;
  isNew: boolean;
}

export function totalWaterSupply(
  list: TimeData[],
  startTime: string,
  endTime: string,
): number {
  if (list.length > 0) {
    let count = 0;
    const countValue = list.reduce((prev, current) => {
      const startTimes = dayjs(startTime).valueOf();
      const endTimes = dayjs(endTime).valueOf();
      const times = dayjs(current.time).valueOf();
      if (times >= startTimes && times <= endTimes) {
        count += 1;
        return prev + current.value;
      }
      return prev;
    }, 0);
    const averValue = count ? countValue / count : 0;
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const durationHours = dayjs.duration(end.diff(start)).asHours();
    return averValue * durationHours;
  }
  return 0;
}

export function generateMessageInfo(
  newList: SystemKettleLogList,
  oldList: MessageInfo[] | undefined,
): MessageInfo[] {
  const oldData: MessageInfo[] = Object.assign(
    [],
    oldList?.map(
      (item): MessageInfo => ({
        ...item,
        isNew: false,
      }),
    ) || [],
  );
  if (!newList.length) return oldData;
  const newData = newList.map(
    ({
      taskId,
      taskName,
      startTime,
      endTime,
      duration,
      taskStatus,
    }): MessageInfo => ({
      id: taskId,
      title: taskName,
      createTime: startTime || '',
      description: '',
      content: `任务开始执行时间:${startTime},执行结束时间:${
        endTime || '无'
      },任务持续:${duration},状态:${taskStatus}`,
      isNew: typeof oldList !== 'undefined',
    }),
  );

  const mergeAndUniqData = lodash.uniqWith(
    [...newData, ...oldData],
    (a, b) => a.id === b.id,
  );
  return mergeAndUniqData.length > 20
    ? mergeAndUniqData.slice(0, 21)
    : mergeAndUniqData;
}
