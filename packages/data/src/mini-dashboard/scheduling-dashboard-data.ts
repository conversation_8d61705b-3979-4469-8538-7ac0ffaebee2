/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';

export function getTimeRange(dateTime: string): [string, string] {
  if (dayjs(dateTime).hour() < 12) {
    return [
      dayjs(dateTime).add(-1, 'day').format('YYYY-MM-DD 12:00:00'),
      dayjs(dateTime)
        .add(1, 'day')
        .startOf('day')
        .format('YYYY-MM-DD 00:00:00'),
    ];
  }

  return [
    dayjs(dateTime).format('YYYY-MM-DD 00:00:00'),
    dayjs(dateTime).add(1, 'day').format('YYYY-MM-DD 12:00:00'),
  ];
}

export function getRealTimeRange(
  dateTime: string,
  currentTime: Dayjs | string,
): [string, string] | null {
  const [start, end] = getTimeRange(dateTime);
  if (dayjs(currentTime).isAfter(start) && dayjs(currentTime).isBefore(end))
    return [start, dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss')];
  if (dayjs(currentTime).isAfter(end)) return [start, end];
  return null;
}

export function getPredictionTimeRange(
  dateTime: string,
  currentTime: Dayjs | string,
): [string, string] | null {
  const [start, end] = getTimeRange(dateTime);
  if (dayjs(currentTime).isAfter(start) && dayjs(currentTime).isBefore(end))
    return [dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss'), end];
  if (dayjs(currentTime).isAfter(end)) return null;
  return [start, end];
}
