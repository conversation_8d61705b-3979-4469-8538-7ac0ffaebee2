/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { merge } from 'lodash';
import {
  AssessmentFormItem,
  MeanAbsoluteError,
  MeanAbsoluteErrorValue,
} from '../app-config';
import { getHelpDescription, getHelpDescriptionByConfig } from '../const/help';
import Database from '../database';
import Device, { StationDevice } from '../device';
import { makeObjectId } from '../object-item';
import { DefaultOptionType } from '../types';
import {
  getUnitFormat,
  getUnitValue,
  getUnitValues,
  getUnitValueWithSymbol,
} from '../unit-system';
import { formatNumber } from '../utils';

export interface InitialIndicatorsValue {
  otype: string;
  oname: string;
  pname: string;
  ptype: string;
  /** value is vprop */
  value: unknown;
}
export interface DashboardFormConfig {
  [index: string]: string | string[];
}

export interface FormConfigValue extends AssessmentFormItem {
  options: string[];
}

export interface SearchForm extends AssessmentFormItem {
  options: DefaultOptionType[];
}

export interface MergeIndicatorsValue
  extends Omit<InitialIndicatorsValue, 'value'> {
  simScore: number | null;
  meanAbsoluteError: number | null;
  meanAbsoluteErrorText?: string;
}

export interface IndicatorsValue extends MergeIndicatorsValue {
  // string as indicatorId
  key: string;
  deviceOname?: string;
  deviceOtype?: string;
  deviceName?: string;
  shape?: string;
  caliber?: number;
}

export interface AssessmentInfo {
  key: string;
  type?: string;
  prefixTitle?: string;
  indicatorTitle?: string;
  meanAbsoluteErrorTitle?: string;
  meanAbsoluteErrorDescription?: string;
  rate?: number;
  values?: IndicatorsValue[];
  children?: AssessmentInfo[];
  threshold?: number;
  thresholdUnit?: string;
  isReachStandard?: boolean;
  helpDescription?: string;
  enableStandard?: boolean;
}

export interface SimulationData {
  totalScore?: number;
  pressScore?: number;
  flowScore?: number;
  standardTotalScore?: number;
  list: AssessmentInfo[];
}

const generateOptions = (
  db: Database,
  options: string[],
): DefaultOptionType[] =>
  // 去重
  [...new Set([...options])].map((value) => {
    const label = db.getPropertyInfo(value)?.title ?? value;
    return {
      label: label || value,
      value,
    };
  });

export const getColumnTitleByOtype = (otype?: string): string => {
  switch (otype) {
    case 'SDVAL_FLOW_W':
    case 'SDVAL_CR':
      return '平均相对误差';
    case 'SDVAL_PRESS_W':
    default:
      return '平均绝对误差';
  }
};

const selectIndicatorsByMeanAbsoluteError = (
  meanAbsoluteErrorValue: [number, number | undefined],
  indicatorsValues: IndicatorsValue[],
  index: number,
) => {
  const [left, right] = meanAbsoluteErrorValue;
  return indicatorsValues.filter(({ meanAbsoluteError }) => {
    if (typeof meanAbsoluteError === 'number') {
      if (typeof right === 'undefined' && index === 0) {
        return meanAbsoluteError <= left;
      }
      if (typeof right === 'undefined' && index !== 0) {
        return meanAbsoluteError > left;
      }
      return meanAbsoluteError > left && meanAbsoluteError <= (right ?? left);
    }
    return false;
  });
};

/**
 * 模型分数=（压力分数*压力指标数+流量分数*流量指标数）/ ( 压力指标数+ 流量指标数）
 * - 压力分数 （均分）  SDVAL_PRESS_W
 *    - 单个压力指标满分15分（其中优秀：≥12，良好：≥9，及格：≥5，不及格：＜5）
 *    - 压力分数 = 每个压力的分数总和 / （压力指标数*15） *100
 * - 流量分数 （均分） SDVAL_FLOW_W
 *    - 单个流量指标满分15分（其中优秀：≥12，良好：≥9，及格：≥5，不及格：＜5）
 *    - 流量分数 =所有 流量指标的分数/15*( 流量指标对应设备的口径caliber/ 全部流量指标对应的设备口径之和）相加*100
 * 只在有分数sim_score的指标中统计总分
 *
 */
const getGradeValue = (
  indicatorsInfo: Map<string, IndicatorsValue[]>,
):
  | { totalScore?: number; pressScore?: number; flowScore?: number }
  | undefined => {
  const pressIndicators = indicatorsInfo
    .get('SDVAL_PRESS_W')
    ?.filter((item: IndicatorsValue) => typeof item.simScore === 'number');
  const flowIndicators = indicatorsInfo
    .get('SDVAL_FLOW_W')
    ?.filter((item: IndicatorsValue) => typeof item.simScore === 'number');
  let gradePress;
  let gradeFlow;
  if (typeof pressIndicators !== 'undefined' && pressIndicators.length > 0) {
    const sumGrade = pressIndicators.reduce(
      (prev, current) => prev + (current.simScore as number),
      0,
    );
    gradePress = (sumGrade / (pressIndicators.length * 15)) * 100;
  }

  if (typeof flowIndicators !== 'undefined' && flowIndicators.length > 0) {
    const sumCaliber = flowIndicators.reduce((prev, current) => {
      const caliber = current.caliber ?? 300;
      return prev + caliber;
    }, 0);
    const sumFlowCaliber = flowIndicators.reduce((prev, current) => {
      const caliber = current.caliber ?? 300;
      return (
        prev + ((current.simScore as number) / 15) * (caliber / sumCaliber)
      );
    }, 0);
    gradeFlow = sumFlowCaliber * 100;
  }
  const pressIndicatorsLength = pressIndicators?.length ?? 0;
  const flowIndicatorsLength = flowIndicators?.length ?? 0;

  if (typeof gradePress === 'undefined' && typeof gradeFlow === 'undefined')
    return undefined;
  const gradeValue =
    ((gradePress ?? 0) * pressIndicatorsLength +
      (gradeFlow ?? 0) * flowIndicatorsLength) /
    (pressIndicatorsLength + flowIndicatorsLength);

  return {
    totalScore: formatNumber(gradeValue, 2),
    pressScore:
      typeof gradePress === 'number' ? formatNumber(gradePress, 2) : gradePress,
    flowScore:
      typeof gradeFlow === 'number' ? formatNumber(gradeFlow, 2) : gradeFlow,
  };
};

const generateChildrenByMeanAbsoluteError = (
  meanAbsoluteError: MeanAbsoluteErrorValue,
  indicatorsValues: IndicatorsValue[],
  otype: string,
  db: Database,
  indicatorTitle?: string,
  prefixTitle?: string,
  valueDescription?: string[],
): AssessmentInfo[] => {
  if (meanAbsoluteError.length === 0) return [];
  const children: AssessmentInfo[] = [];
  const unitKey = db
    .getPropertyInfo(otype)
    ?.getPropertyUnit('ABSOLUTE_MEAN_ERROR');
  meanAbsoluteError.forEach((item, index) => {
    const [left, right] = item;
    const unitSymbol = unitKey ? getUnitFormat(unitKey)?.unitSymbol : '';
    const leftSymbolValue = unitKey ? getUnitValue(unitKey, left) : left;
    const selectIndicators = selectIndicatorsByMeanAbsoluteError(
      item,
      indicatorsValues,
      index,
    );
    const rate: number = formatNumber(
      (selectIndicators.length / indicatorsValues.length) * 100,
      2,
    );
    let meanAbsoluteErrorTitle = '';
    if (typeof right === 'undefined' && index === 0) {
      meanAbsoluteErrorTitle = `≤${leftSymbolValue}`;
    } else if (
      typeof right === 'undefined' &&
      index === meanAbsoluteError.length - 1
    ) {
      meanAbsoluteErrorTitle = `>${leftSymbolValue}`;
    } else {
      const rightSymbolValue = unitKey
        ? getUnitValue(unitKey, right ?? '')
        : right;
      meanAbsoluteErrorTitle = `${leftSymbolValue}-${rightSymbolValue}`;
    }
    meanAbsoluteErrorTitle += unitSymbol;
    children.push({
      key: `${otype}_${meanAbsoluteErrorTitle}`,
      meanAbsoluteErrorTitle,
      rate,
      values: selectIndicators,
      indicatorTitle,
      prefixTitle,
      type: otype,
      meanAbsoluteErrorDescription: valueDescription?.[index],
    });
  });

  return children;
};

const formatIndicatorsValue = (
  indicatorsValue: MergeIndicatorsValue,
  deviceInfo?: Device,
): IndicatorsValue => {
  const { oname, otype } = indicatorsValue;
  const id = makeObjectId(otype, oname);
  return {
    ...indicatorsValue,
    key: id,
    shape: deviceInfo?.shape,
    deviceOname: deviceInfo?.oname,
    deviceOtype: deviceInfo?.otype,
    deviceName: deviceInfo?.title,
    caliber: deviceInfo?.caliber,
  };
};

export const mergeIndicatorsValues = (
  db: Database,
  simScoreValues: Map<string, InitialIndicatorsValue[]>,
  meanAbsoluteErrorValues: Map<string, InitialIndicatorsValue[]>,
): Map<string, MergeIndicatorsValue[]> => {
  const indicatorsValues = new Map();
  simScoreValues.forEach((value, key) => {
    const unitKey = db
      .getPropertyInfo(key)
      ?.getPropertyUnit('ABSOLUTE_MEAN_ERROR');
    const simScoreList = value.map((item) => ({
      ...item,
      simScore: item.value === null ? item.value : Number(item.value),
    }));
    const meanAbsoluteErrorList = meanAbsoluteErrorValues
      .get(key)
      ?.map((item) => ({
        ...item,
        meanAbsoluteError: Number(item.value),
        meanAbsoluteErrorText: unitKey
          ? getUnitValueWithSymbol(unitKey, Number(item.value))
          : '-',
      }));
    const mergeList = merge(simScoreList, meanAbsoluteErrorList);
    indicatorsValues.set(key, mergeList);
  });
  return indicatorsValues;
};

export class SimulationDashboardCollection {
  private _formConfig: Map<string, FormConfigValue>;

  private _indicatorsInfo: Map<string, IndicatorsValue[]>;

  private _catchDevice: Map<string, Device>;

  private _indicatorsUnitValues: [string, number[]][] | undefined;

  private _indicatorsUnitKey: string | undefined;

  constructor() {
    this._formConfig = new Map();
    this._indicatorsInfo = new Map();
    this._catchDevice = new Map();
    this._indicatorsUnitValues = undefined;
    this._indicatorsUnitKey = undefined;
  }

  private selectIndicators(
    formValue: DashboardFormConfig,
  ): Map<string, IndicatorsValue[]> {
    const filterIndicators: Map<string, IndicatorsValue[]> = new Map();
    this._indicatorsInfo.forEach((value, key) => {
      const selectIndicatorsInfoValues: IndicatorsValue[] = [];
      value.forEach((indicatorsItem: IndicatorsValue) => {
        const deviceId = makeObjectId(
          indicatorsItem.ptype,
          indicatorsItem.pname,
        );
        // 反查时 根据缓存的_catchDevice查
        const deviceInfo = this._catchDevice.get(deviceId);
        let flag = true;
        Object.keys(formValue).forEach((key) => {
          const value = formValue[key];
          // 多选时的匹配，空数据不做过滤，默认通过
          if (Array.isArray(value)) {
            if (
              value.length > 0 &&
              !value.includes(deviceInfo?.[key as keyof Device] as string)
            )
              flag = false;
          } else if (
            value !== '' &&
            value !== (deviceInfo?.[key as keyof Device] as string)
          ) {
            flag = false;
          }
        });
        if (flag) {
          selectIndicatorsInfoValues.push(indicatorsItem);
        }
      });
      filterIndicators.set(key, selectIndicatorsInfoValues);
    });
    return filterIndicators;
  }

  getNotAssessmentInfo(
    db: Database,
    otype: string,
    indicatorTitle?: string,
    type?: MeanAbsoluteError['type'],
  ): AssessmentInfo {
    const deviceIds = (this._indicatorsInfo.get(otype) ?? []).map((item) => {
      const { deviceOname, deviceOtype } = item;
      if (deviceOtype && deviceOname)
        return makeObjectId(deviceOtype, deviceOname);
      return '';
    });
    const allDevices = db.getAllDevicesAndStations();
    const filteredDevices = allDevices.filter(
      (device) => !deviceIds.includes(device.id),
    );
    const convertedIndicatorValues: IndicatorsValue[] = [];
    filteredDevices.forEach((device) => {
      const { indicators, otype: deviceOtype, oname, title, shape } = device;
      if (typeof type !== 'undefined') {
        if (
          (type === 'includeFactory' && device instanceof StationDevice) ||
          (type === 'excludeFactory' && !(device instanceof StationDevice))
        ) {
          const indicator = indicators.find(
            (indicator) => indicator.otype === otype,
          );
          if (indicator) {
            convertedIndicatorValues.push({
              key: indicator.id,
              deviceName: title,
              deviceOname: oname,
              deviceOtype,
              simScore: null,
              meanAbsoluteError: null,
              otype: indicator.otype,
              oname: indicator.oname,
              pname: oname,
              ptype: deviceOtype,
              shape,
            });
          }
        }
      }
    });

    return {
      key: `${otype}_未评估}`,
      meanAbsoluteErrorTitle: '未评估',
      values: convertedIndicatorValues,
      type: otype,
      indicatorTitle,
    };
  }

  generateSimulationData(
    db: Database,
    formValue: DashboardFormConfig,
    meanAbsoluteErrorList: MeanAbsoluteError[],
    formula?: string,
    helpConfig?: { [key: string]: string },
  ): SimulationData {
    const filterIndicators: Map<string, IndicatorsValue[]> =
      this.selectIndicators(formValue);
    if (filterIndicators.size === 0)
      return {
        list: [],
      };
    const list: AssessmentInfo[] = [];
    let realityStandard = 0;
    let expectStandard = 0;

    meanAbsoluteErrorList.forEach((meanAbsoluteError) => {
      const {
        type,
        indicatorType: otype,
        value,
        enableStandard = false,
        ratio,
        summaryOthers = false,
        valueDescription,
      } = meanAbsoluteError;
      const [firstRatio = 0, secondRatio = 0] = ratio;
      const indicatorTitle = db.getPropertyInfo(otype)?.title ?? otype;
      let prefixTitle: string = '';
      let filterValues = filterIndicators.get(otype) ?? [];
      const unitKey = db
        .getPropertyInfo(otype)
        ?.getPropertyUnit('ABSOLUTE_MEAN_ERROR');
      const threshold = unitKey
        ? (getUnitValue(unitKey, value[0][0]) as number)
        : value[0][0];
      const thresholdUnit = unitKey ? getUnitFormat(unitKey)?.unitSymbol : '';

      let isReachStandard: boolean = false;
      let helpDescription: string = '';

      let children = generateChildrenByMeanAbsoluteError(
        value,
        filterValues,
        otype,
        db,
        indicatorTitle,
        prefixTitle,
        valueDescription,
      );

      if (otype === 'SDVAL_FLOW_W' && type === 'includeFactory') {
        prefixTitle = '水厂';
        filterValues = filterValues.filter(
          (item) => item.ptype === 'DEV_FACTORY_BASE',
        );
        children = generateChildrenByMeanAbsoluteError(
          value,
          filterValues,
          otype,
          db,
          indicatorTitle,
          prefixTitle,
          valueDescription,
        );
        isReachStandard =
          typeof children[0].rate === 'number'
            ? children[0].rate >= firstRatio
            : false;
        const content =
          helpConfig?.plantFlowStandard ??
          getHelpDescription('plantFlowStandard');
        helpDescription = getHelpDescriptionByConfig(
          content,
          `${threshold}${thresholdUnit}`,
          `${firstRatio}`,
        );
      }

      if (otype === 'SDVAL_FLOW_W' && type === 'excludeFactory') {
        prefixTitle = '管网';
        filterValues = filterValues.filter(
          (item) => item.ptype !== 'DEV_FACTORY_BASE',
        );
        children = generateChildrenByMeanAbsoluteError(
          value,
          filterValues,
          otype,
          db,
          indicatorTitle,
          prefixTitle,
          valueDescription,
        );
        isReachStandard =
          typeof children[0].rate === 'number'
            ? children[0].rate >= firstRatio
            : false;
        const content =
          helpConfig?.networkFlowStandard ??
          getHelpDescription('networkFlowStandard');
        helpDescription = getHelpDescriptionByConfig(
          content,
          `${threshold}${thresholdUnit}`,
          `${firstRatio}`,
        );
      }

      if (otype === 'SDVAL_PRESS_W') {
        const secondThreshold = unitKey
          ? (getUnitValue(unitKey, value[1][1] ?? 0) as number)
          : (value[1][1] ?? 0);
        const firstRate = children[0]?.rate ?? 0;
        const secondRate = children[1]?.rate ?? 0;
        isReachStandard =
          firstRate >= firstRatio && secondRate + firstRate >= secondRatio;

        const content =
          helpConfig?.pressureStandard ??
          getHelpDescription('pressureStandard');
        helpDescription = getHelpDescriptionByConfig(
          content,
          `${secondThreshold}${thresholdUnit}`,
          `${secondRatio}`,
          `${threshold}${thresholdUnit}`,
          `${firstRatio}`,
        );
      }

      if (otype === 'SDVAL_CR') {
        isReachStandard =
          typeof children[0].rate === 'number'
            ? children[0].rate >= firstRatio
            : false;
        const content =
          helpConfig?.chlorineStandard ??
          getHelpDescription('chlorineStandard');
        helpDescription = getHelpDescriptionByConfig(
          content,
          `${threshold}${thresholdUnit}`,
          `${firstRatio}`,
        );
      }

      expectStandard +=
        children.reduce((p, c) => p + (c.values?.length ?? 0), 0) *
        (firstRatio / 100);
      realityStandard += children[0]?.values?.length ?? 0;

      if (summaryOthers) {
        const notAssessmentInfo = this.getNotAssessmentInfo(
          db,
          otype,
          indicatorTitle,
          otype === 'SDVAL_FLOW_W' ? type : undefined,
        );
        children.push(notAssessmentInfo);
      }

      list.push({
        key: `${type ?? ''}@${otype}`,
        type: otype,
        prefixTitle,
        indicatorTitle,
        children,
        threshold,
        thresholdUnit,
        isReachStandard,
        helpDescription,
        enableStandard,
      });
    });

    const { totalScore, pressScore, flowScore } =
      getGradeValue(filterIndicators) ?? {};
    let formulaGradeValue: number | undefined;
    let formulaPressScore: number | undefined;
    let formulaFlowScore: number | undefined;
    try {
      if (formula?.match('%v')) {
        if (typeof totalScore === 'number') {
          const formulaStr = formula.replaceAll('%v', totalScore.toString());
          // eslint-disable-next-line no-eval
          formulaGradeValue = formatNumber(eval(formulaStr), 2);
        }
        if (typeof pressScore === 'number') {
          const formulaStr = formula.replaceAll('%v', pressScore.toString());
          // eslint-disable-next-line no-eval
          formulaPressScore = formatNumber(eval(formulaStr), 2);
        }
        if (typeof flowScore === 'number') {
          const formulaStr = formula.replaceAll('%v', flowScore.toString());
          // eslint-disable-next-line no-eval
          formulaFlowScore = formatNumber(eval(formulaStr), 2);
        }
      }
    } catch (err) {
      console.log(err);
    }
    let standardTotalScore =
      expectStandard > 0 && realityStandard > 0
        ? formatNumber((realityStandard / expectStandard) * 100, 2)
        : undefined;

    if (typeof standardTotalScore === 'number' && standardTotalScore > 100)
      standardTotalScore = 100;

    return {
      list,
      totalScore: formulaGradeValue ?? totalScore,
      pressScore: formulaPressScore ?? pressScore,
      flowScore: formulaFlowScore ?? flowScore,
      standardTotalScore,
    };
  }

  generateSearchFormList(db: Database): Array<SearchForm> {
    const formList: Array<SearchForm> = [];
    this._formConfig.forEach((value) => {
      if (value.enable) {
        const options = generateOptions(db, value.options);
        formList.push({ ...value, options });
      }
    });
    return formList.sort((a, b) => a.index - b.index);
  }

  private initUnitKeyAndValues(db: Database, key: string): void {
    //  vprop = SIM_SCORE
    const titleAndUnitKey = db.getPropertyTitleUnit(key, 'SIM_SCORE');
    // SIM_SCORE
    const [, unitKey] = titleAndUnitKey;
    if (typeof unitKey === 'undefined') return;

    if (typeof this._indicatorsUnitKey === 'undefined') {
      this._indicatorsUnitKey = unitKey;
    }
    const unitValues: Array<[string, Array<number>]> | undefined =
      getUnitValues(unitKey);
    if (
      typeof this._indicatorsUnitValues === 'undefined' &&
      typeof unitValues !== 'undefined'
    ) {
      this._indicatorsUnitValues = unitValues;
    }
  }

  initialize(
    db: Database,
    data: Map<string, MergeIndicatorsValue[]>,
    simulationForm: Map<string, AssessmentFormItem>,
  ) {
    this._catchDevice.clear();
    this._indicatorsInfo.clear();
    this._indicatorsUnitKey = undefined;
    this._indicatorsUnitValues = undefined;
    // catch device
    const selectDevice: Map<string, Device> = new Map();
    const selectFormConfig: Map<string, FormConfigValue> = new Map();
    data.forEach((deviceClueArr, otype) => {
      this.initUnitKeyAndValues(db, otype);
      const selectIndicatorsInfoValues: IndicatorsValue[] = [];
      deviceClueArr.forEach((item) => {
        const { ptype, pname } = item;
        const deviceId = makeObjectId(ptype, pname);
        const deviceInfo = db.getDeviceById(deviceId);
        const selectIndicatorsInfoValue = formatIndicatorsValue(
          item,
          deviceInfo,
        );

        selectIndicatorsInfoValues.push(selectIndicatorsInfoValue);
        if (typeof deviceInfo !== 'undefined') {
          selectDevice.set(deviceId, deviceInfo);
        }
        simulationForm.forEach((value, key) => {
          const config: FormConfigValue = selectFormConfig.get(key) ?? {
            ...value,
            options: [],
          };
          if (
            deviceInfo?.[key as keyof Device] &&
            deviceInfo?.[key as keyof Device] !== 'null'
          ) {
            config.options.push(deviceInfo[key as keyof Device]);
          }
          // 将初始值合并到选项中
          config.options = [...config.options, ...config.initialValues];
          selectFormConfig.set(key, config);
        });
      });
      if (selectIndicatorsInfoValues.length) {
        this._indicatorsInfo.set(otype, selectIndicatorsInfoValues);
      }
    });
    this._formConfig = selectFormConfig;
    this._catchDevice = selectDevice;
  }
}
