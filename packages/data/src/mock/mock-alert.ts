/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AlertDefinition, AlertInstanceList, AlertLevel } from '../alert';

const mockAlertLevels: AlertLevel[] = [
  {
    level: 1,
    icon: '⚠️',
    color: 'red',
    signal: 'Critical',
    description: 'Critical level alert',
    parameter: {}, // 这里假设 AlertLevelParameter 类型是已定义的
  },
  {
    level: 2,
    icon: '🔴',
    color: 'orange',
    signal: 'High',
    description: 'High level alert',
    parameter: {},
  },
  {
    level: 3,
    icon: '🟠',
    color: 'yellow',
    signal: 'Medium',
    description: 'Medium level alert',
    parameter: {},
  },
  {
    level: 4,
    icon: '🟢',
    color: 'green',
    signal: 'Low',
    description: 'Low level alert',
    parameter: {},
  },
];

const mockAlertDefinitions: AlertDefinition[] = [
  {
    id: 'def1',
    title: 'System Overload',
    description: 'The system is experiencing an overload.',
    category: 'System',
    status: 1,
    levels: mockAlertLevels,
    source: 'Monitoring System',
    remark: 'Immediate action required.',
  },
  {
    id: 'def2',
    title: 'Network Issue',
    description: 'There is a network issue detected.',
    category: 'Network',
    status: 1,
    levels: mockAlertLevels,
    source: 'Network Monitoring',
    remark: 'Check the network connection.',
  },
  {
    id: 'def3',
    title: 'Power Failure',
    description: 'Power failure detected in the facility.',
    category: 'Power',
    status: 1,
    levels: mockAlertLevels,
    source: 'Power Monitoring',
    remark: 'Check the power supply.',
  },
];

// 无预警
const mockNoAlertInstance: AlertInstanceList = [];

// 存在预警，但超过已结束时间
const mockOneExpiredAlertInstance: AlertInstanceList = [
  {
    id: 'inst1',
    definitionId: 'def1',
    definitionTitle: 'System Overload',
    definitionCategory: 'System',
    level: 1,
    content: 'CPU usage has exceeded 95% for the last 10 minutes.',
    startTime: '2024-05-25T08:00:00Z',
    endTime: '2024-05-25T08:30:00Z',
    createTime: '2024-05-25T08:00:00Z',
    ptype: 'Server',
    pname: 'Server01',
    ptitle: 'Main Server',
    stime: '2024-05-24T07:00:00Z',
    remark: 'Critical alert for system overload.',
    source: 'Monitoring System',
  },
];

// 存在一个预警
const mockOneAlertInstance: AlertInstanceList = [
  {
    id: 'inst1',
    definitionId: 'def1',
    definitionTitle: 'System Overload',
    definitionCategory: 'System',
    level: 1,
    content: 'CPU usage has exceeded 95% for the last 10 minutes.',
    startTime: '2024-05-25T08:00:00Z',
    endTime: '',
    createTime: '2024-05-25T08:00:00Z',
    ptype: 'Server',
    pname: 'Server01',
    ptitle: 'Main Server',
    stime: '2024-05-24T07:00:00Z',
    remark: 'Critical alert for system overload.',
    source: 'Monitoring System',
  },
];

// 存在多个预警，预警都是同一等级，只是创建时间不同
const mockSameLevelDifferentTimesMultipleAlertsInstance: AlertInstanceList = [
  {
    id: 'inst2',
    definitionId: 'def2',
    definitionTitle: 'Network Issue',
    definitionCategory: 'Network',
    level: 2,
    content: 'Packet loss detected on the main router.',
    startTime: '2024-05-25T09:00:00Z',
    endTime: '',
    createTime: '2024-05-25T09:00:00Z',
    ptype: 'Router',
    pname: 'Router01',
    ptitle: 'Main Router',
    stime: '2024-05-24T07:30:00Z',
    remark: 'High alert for network issue.',
    source: 'Network Monitoring',
  },
  {
    id: 'inst3',
    definitionId: 'def2',
    definitionTitle: 'Network Issue',
    definitionCategory: 'Network',
    level: 2,
    content: 'High latency detected on the backup router.',
    startTime: '2024-05-25T10:00:00Z',
    endTime: '',
    createTime: '2024-05-25T10:00:00Z',
    ptype: 'Router',
    pname: 'Router02',
    ptitle: 'Backup Router',
    stime: '2024-05-24T08:00:00Z',
    remark: 'High alert for network issue.',
    source: 'Network Monitoring',
  },
];

// 存在多个预警，预警都是不同等级，但是创建时间相同
const mockDifferentLevelsSameTimeMultipleAlertsInstance: AlertInstanceList = [
  {
    id: 'inst4',
    definitionId: 'def3',
    definitionTitle: 'Power Failure',
    definitionCategory: 'Power',
    level: 1,
    content: 'Power failure in Zone 3.',
    startTime: '2024-05-25T10:00:00Z',
    endTime: '',
    createTime: '2024-05-25T10:00:00Z',
    ptype: 'PowerUnit',
    pname: 'PowerUnit03',
    ptitle: 'Zone 3 Power Unit',
    stime: '2024-05-24T08:30:00Z',
    remark: 'Critical alert for power failure.',
    source: 'Power Monitoring',
  },
  {
    id: 'inst5',
    definitionId: 'def1',
    definitionTitle: 'System Overload',
    definitionCategory: 'System',
    level: 3,
    content: 'Disk usage has exceeded 90% for the last 5 minutes.',
    startTime: '2024-05-25T10:00:00Z',
    endTime: '',
    createTime: '2024-05-25T10:00:00Z',
    ptype: 'Server',
    pname: 'Server02',
    ptitle: 'Backup Server',
    stime: '2024-05-24T08:30:00Z',
    remark: 'Medium alert for disk usage.',
    source: 'Monitoring System',
  },
];

// 存在多个预警，预警既有相同等级也有不同等级，创建时间既有相同也有不同
const mockMixedMultipleAlertsInstance: AlertInstanceList = [
  {
    id: 'inst6',
    definitionId: 'def3',
    definitionTitle: 'Power Failure',
    definitionCategory: 'Power',
    level: 2,
    content: 'Power failure in Zone 4.',
    startTime: '2024-05-25T11:00:00Z',
    endTime: '',
    createTime: '2024-05-25T11:00:00Z',
    ptype: 'PowerUnit',
    pname: 'PowerUnit04',
    ptitle: 'Zone 4 Power Unit',
    stime: '2024-05-24T08:45:00Z',
    remark: 'High alert for power failure.',
    source: 'Power Monitoring',
  },
  {
    id: 'inst7',
    definitionId: 'def1',
    definitionTitle: 'System Overload',
    definitionCategory: 'System',
    level: 2,
    content: 'Memory usage has exceeded 85% for the last 15 minutes.',
    startTime: '2024-05-25T11:00:00Z',
    endTime: '',
    createTime: '2024-05-25T12:00:00Z',
    ptype: 'Server',
    pname: 'Server03',
    ptitle: 'Memory Server',
    stime: '2024-05-24T08:50:00Z',
    remark: 'High alert for memory usage.',
    source: 'Monitoring System',
  },
  {
    id: 'inst8',
    definitionId: 'def2',
    definitionTitle: 'Network Issue',
    definitionCategory: 'Network',
    level: 3,
    content: 'DNS issues detected.',
    startTime: '2024-05-25T11:00:00Z',
    endTime: '',
    createTime: '2024-05-25T11:00:00Z',
    ptype: 'Router',
    pname: 'Router03',
    ptitle: 'DNS Router',
    stime: '2024-05-24T08:55:00Z',
    remark: 'Medium alert for DNS issues.',
    source: 'Network Monitoring',
  },
  {
    id: 'inst9',
    definitionId: 'def1',
    definitionTitle: 'System Overload',
    definitionCategory: 'System',
    level: 1,
    content: 'CPU usage has exceeded 95% for the last 10 minutes.',
    startTime: '2024-05-25T12:30:00Z',
    endTime: '',
    createTime: '2024-05-25T12:30:00Z',
    ptype: 'Server',
    pname: 'Server01',
    ptitle: 'Main Server',
    stime: '2024-05-24T08:00:00Z',
    remark: 'Critical alert for system overload.',
    source: 'Monitoring System',
  },
  {
    id: 'inst10',
    definitionId: 'def2',
    definitionTitle: 'Network Issue',
    definitionCategory: 'Network',
    level: 4,
    content: 'Intermittent network connection.',
    startTime: '2024-05-25T13:00:00Z',
    endTime: '',
    createTime: '2024-05-25T13:00:00Z',
    ptype: 'Router',
    pname: 'Router02',
    ptitle: 'Backup Router',
    stime: '2024-05-24T09:00:00Z',
    remark: 'Low alert for network issue.',
    source: 'Network Monitoring',
  },
  {
    id: 'inst11',
    definitionId: 'def3',
    definitionTitle: 'Power Failure',
    definitionCategory: 'Power',
    level: 3,
    content: 'Power failure in Zone 2.',
    startTime: '2024-05-25T13:30:00Z',
    endTime: '2024-05-25T13:30:00Z',
    createTime: '2024-05-25T13:30:00Z',
    ptype: 'PowerUnit',
    pname: 'PowerUnit02',
    ptitle: 'Zone 2 Power Unit',
    stime: '2024-05-24T09:15:00Z',
    remark: 'Medium alert for power failure.',
    source: 'Power Monitoring',
  },
];

export {
  mockAlertDefinitions,
  mockAlertLevels,
  mockDifferentLevelsSameTimeMultipleAlertsInstance,
  mockMixedMultipleAlertsInstance,
  mockNoAlertInstance,
  mockOneAlertInstance,
  mockOneExpiredAlertInstance,
  mockSameLevelDifferentTimesMultipleAlertsInstance,
};
