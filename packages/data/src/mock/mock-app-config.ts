/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const mockHighlightStyle = {
  select: {
    pointColor: '#ff0000',
    lineColor: '#ff0000',
    polygonColor: '#ff000080',
    lineWidth: 8,
    polygonStrokeColor: '#ff0000',
    fontSize: 40,
  },
  measure: {
    pointColor: '#ff0000',
    lineColor: '#ff0000',
    polygonColor: '#6495ed10',
    polygonStrokeColor: '#ffcc33',
    fontSize: 24,
  },
  highlight: {
    pointColor: '#deb887',
    lineColor: '#ff7f50',
    polygonColor: '#ff7f5040',
    polygonStrokeColor: '#ff7f50a0',
    fontSize: 24,
  },
  track: {
    pointColor: '#008800',
    lineColor: '#008800',
    polygonColor: '#008800a0',
    fontSize: 24,
  },
  valveManageOpenValve: {
    pointColor: '#0000FF',
    lineColor: '#008800',
    polygonColor: '#008800a0',
    fontSize: 24,
  },
  valveManageClosedValve: {
    pointColor: '#ff0000',
    lineColor: '#ff0000',
    polygonColor: '#ff0000a0',
    fontSize: 24,
  },
  valveAnalyseImpactedValve: {
    pointColor: '#0000ff',
    lineColor: '#0000ff',
    polygonColor: '#f5b533a0',
    fontSize: 24,
  },
  valveAnalyseClosedValve: {
    pointColor: '#ff0000',
    lineColor: '#ff0000',
    polygonColor: '#ff0000a0',
    fontSize: 24,
  },
};
