/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BatchQuery, BatchQueryWhere, WhereType } from '../batch-query-data';

// ["长度","流速", "流量", "管径", "绝对压力"]
const mockVprops1 = ['LENGTH', 'VELOCITY', 'FLOW', 'DIAMETER', 'SDVAL_HEAD'];
// ["路名", "流量", "管径"]
const mockResultVprops1 = ['ROAD_NAME', 'FLOW', 'DIAMETER'];

const mockVprops2: string[] = [
  'RELIABILITY_S',
  'DEVICE_TYPE',
  'TITLE',
  'CALIBER',
  'DISPLAYLEVEL',
  'SUMMARIZE',
];
const mockResultVprops2: string[] = ['TITLE', 'SUMMARIZE'];

export const mockBatchQueryObjects: BatchQuery['objects'] = [
  {
    otype: 'WDM_PIPES',
    title: '管线属性表',
    vprops: mockVprops1,
    resultVprops: mockResultVprops1,
  },
  {
    otype: 'DEV_PF',
    vprops: mockVprops2,
    resultVprops: mockResultVprops2,
  },
];

export const mockBatchQuery: BatchQuery = {
  objects: mockBatchQueryObjects,
};

export const mockBatchQueryWhere: BatchQueryWhere[] = [
  {
    vprop: 'LENGTH',
    type: '>' as WhereType,
    value: 100,
  },
  {
    vprop: 'FLOW',
    type: '<=' as WhereType,
    value: 20,
  },
  {
    vprop: 'DIAMETER',
    type: '>=' as WhereType,
    value: '800',
  },
];

export const mockBatchQueryParams = {
  polygon:
    '429509.5493068244,2888140.607060056,433385.8800277932,2888196.1418841393,433385.8800277932,2885241.6892429423,429531.7632364575,2885497.1494337223,429509.5493068244,2888140.607060056',
  otype: 'WDM_PIPES',
  where: mockBatchQueryWhere,
  resultVprops: mockResultVprops1,
};

const mockResponseDataList = [
  {
    otype: 'WDM_PIPES',
    oname: 'P54746',
    shape: 'xxxxxxxxxxxx',
    values: {
      // 这里的返回是动态的，根据请求参数resultVprops控制
      ROAD_NAME: '福兴大道（化工路-福新东路）',
      FLOW: '248.685974',
      DIAMETER: '400',
    },
  },
];

export const mockResponseData = {
  json_ok: true,
  values: {
    data: mockResponseDataList,
  },
};
