/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import {
  getBaseSolutionInfoData,
  getSolutionDetail,
  JunctionDemandChangeSetting,
  JunctionQualityChangeSetting,
  LinkStatusChangeSetting,
  PlantChangeSetting,
  PlantValueChangeSetting,
  PumpStationChangeSetting,
  ValueChangeSetting,
} from '../solution-detail';
import { TimeData } from '../time-data';

export const mockFlowTimeData: TimeData[] = [
  {
    value: 31952.127859953584,
    time: '2023-03-19 00:00:00',
  },
  {
    value: 32233.81608235014,
    time: '2023-03-19 00:05:00',
  },
  {
    value: 31970.146426739266,
    time: '2023-03-19 00:10:00',
  },
  {
    value: 32118.201737915788,
    time: '2023-03-19 00:15:00',
  },
  {
    value: 31443.70506483329,
    time: '2023-03-19 00:20:00',
  },
  {
    value: 31190.252362765277,
    time: '2023-03-19 00:25:00',
  },
  {
    value: 30546.660452407898,
    time: '2023-03-19 00:30:00',
  },
  {
    value: 30896.959978713698,
    time: '2023-03-19 00:35:00',
  },
  {
    value: 30644.157775396663,
    time: '2023-03-19 00:40:00',
  },
  {
    value: 30087.595228994785,
    time: '2023-03-19 00:45:00',
  },
  {
    value: 29833.300206550426,
    time: '2023-03-19 00:50:00',
  },
  {
    value: 29470.072871144046,
    time: '2023-03-19 00:55:00',
  },
  {
    value: 29369.351482748894,
    time: '2023-03-19 01:00:00',
  },
  {
    value: 28245.440493364327,
    time: '2023-03-19 01:05:00',
  },
  {
    value: 28200.931550222376,
    time: '2023-03-19 01:10:00',
  },
  {
    value: 27767.189887639917,
    time: '2023-03-19 01:15:00',
  },
  {
    value: 27467.814262047206,
    time: '2023-03-19 01:20:00',
  },
  {
    value: 26964.61143817193,
    time: '2023-03-19 01:25:00',
  },
  {
    value: 26992.629954436325,
    time: '2023-03-19 01:30:00',
  },
  {
    value: 26200.61369158035,
    time: '2023-03-19 01:35:00',
  },
  {
    value: 26392.74743678101,
    time: '2023-03-19 01:40:00',
  },
  {
    value: 26099.073739912514,
    time: '2023-03-19 01:45:00',
  },
  {
    value: 26123.542130315785,
    time: '2023-03-19 01:50:00',
  },
  {
    value: 25981.309641955275,
    time: '2023-03-19 01:55:00',
  },
  {
    value: 26157.370359897566,
    time: '2023-03-19 02:00:00',
  },
  {
    value: 25902.843582792986,
    time: '2023-03-19 02:05:00',
  },
  {
    value: 25437.85490380841,
    time: '2023-03-19 02:10:00',
  },
  {
    value: 25474.854833700254,
    time: '2023-03-19 02:15:00',
  },
  {
    value: 25013.92407363078,
    time: '2023-03-19 02:20:00',
  },
  {
    value: 24628.348008845114,
    time: '2023-03-19 02:25:00',
  },
  {
    value: 24411.590693700335,
    time: '2023-03-19 02:30:00',
  },
  {
    value: 24636.696968274788,
    time: '2023-03-19 02:35:00',
  },
  {
    value: 24490.983524255036,
    time: '2023-03-19 02:40:00',
  },
  {
    value: 24238.76763108627,
    time: '2023-03-19 02:45:00',
  },
  {
    value: 24143.8953248529,
    time: '2023-03-19 02:50:00',
  },
  {
    value: 24052.62450947567,
    time: '2023-03-19 02:55:00',
  },
  {
    value: 24009.754960684597,
    time: '2023-03-19 03:00:00',
  },
  {
    value: 23903.27949635325,
    time: '2023-03-19 03:05:00',
  },
  {
    value: 24161.703820775452,
    time: '2023-03-19 03:10:00',
  },
  {
    value: 23943.23562218229,
    time: '2023-03-19 03:15:00',
  },
  {
    value: 23800.48572768489,
    time: '2023-03-19 03:20:00',
  },
  {
    value: 23825.118594024276,
    time: '2023-03-19 03:25:00',
  },
  {
    value: 23830.038775511402,
    time: '2023-03-19 03:30:00',
  },
  {
    value: 24115.46872602611,
    time: '2023-03-19 03:35:00',
  },
  {
    value: 23950.39197435028,
    time: '2023-03-19 03:40:00',
  },
  {
    value: 23857.587575009624,
    time: '2023-03-19 03:45:00',
  },
  {
    value: 24065.459972005014,
    time: '2023-03-19 03:50:00',
  },
  {
    value: 24068.892686337203,
    time: '2023-03-19 03:55:00',
  },
  {
    value: 23978.50160740726,
    time: '2023-03-19 04:00:00',
  },
  {
    value: 23602.17748021053,
    time: '2023-03-19 04:05:00',
  },
  {
    value: 23447.774298645447,
    time: '2023-03-19 04:10:00',
  },
  {
    value: 23431.7807326147,
    time: '2023-03-19 04:15:00',
  },
  {
    value: 23225.417257356166,
    time: '2023-03-19 04:20:00',
  },
  {
    value: 23034.71956373333,
    time: '2023-03-19 04:25:00',
  },
  {
    value: 23288.40351402861,
    time: '2023-03-19 04:30:00',
  },
  {
    value: 23084.38421692648,
    time: '2023-03-19 04:35:00',
  },
  {
    value: 23159.70485666967,
    time: '2023-03-19 04:40:00',
  },
  {
    value: 23160.133713647265,
    time: '2023-03-19 04:45:00',
  },
  {
    value: 23493.035290793476,
    time: '2023-03-19 04:50:00',
  },
  {
    value: 23705.03279375859,
    time: '2023-03-19 04:55:00',
  },
  {
    value: 23570.89393724715,
    time: '2023-03-19 05:00:00',
  },
  {
    value: 23756.385342826976,
    time: '2023-03-19 05:05:00',
  },
  {
    value: 23854.75900115753,
    time: '2023-03-19 05:10:00',
  },
  {
    value: 23992.23207135169,
    time: '2023-03-19 05:15:00',
  },
  {
    value: 24116.560260182956,
    time: '2023-03-19 05:20:00',
  },
  {
    value: 24290.62848089655,
    time: '2023-03-19 05:25:00',
  },
  {
    value: 24042.1830503101,
    time: '2023-03-19 05:30:00',
  },
  {
    value: 24311.19644543315,
    time: '2023-03-19 05:35:00',
  },
  {
    value: 24200.18123439504,
    time: '2023-03-19 05:40:00',
  },
  {
    value: 24597.170039498465,
    time: '2023-03-19 05:45:00',
  },
  {
    value: 24692.255201410044,
    time: '2023-03-19 05:50:00',
  },
  {
    value: 25018.200774364548,
    time: '2023-03-19 05:55:00',
  },
  {
    value: 25549.237408769994,
    time: '2023-03-19 06:00:00',
  },
  {
    value: 25523.119223697933,
    time: '2023-03-19 06:05:00',
  },
  {
    value: 26593.10677688861,
    time: '2023-03-19 06:10:00',
  },
  {
    value: 27532.28892720883,
    time: '2023-03-19 06:15:00',
  },
  {
    value: 27379.806512278064,
    time: '2023-03-19 06:20:00',
  },
  {
    value: 27776.686240774605,
    time: '2023-03-19 06:25:00',
  },
  {
    value: 28798.894948520538,
    time: '2023-03-19 06:30:00',
  },
  {
    value: 28728.4953077914,
    time: '2023-03-19 06:35:00',
  },
  {
    value: 29957.569555707927,
    time: '2023-03-19 06:40:00',
  },
  {
    value: 29559.260904420702,
    time: '2023-03-19 06:45:00',
  },
  {
    value: 30341.21815837268,
    time: '2023-03-19 06:50:00',
  },
  {
    value: 31246.894397109027,
    time: '2023-03-19 06:55:00',
  },
  {
    value: 32095.74156494737,
    time: '2023-03-19 07:00:00',
  },
  {
    value: 32654.93516615163,
    time: '2023-03-19 07:05:00',
  },
  {
    value: 34108.658377339874,
    time: '2023-03-19 07:10:00',
  },
  {
    value: 34422.86587274463,
    time: '2023-03-19 07:15:00',
  },
  {
    value: 35481.948105356925,
    time: '2023-03-19 07:20:00',
  },
  {
    value: 36590.24101779469,
    time: '2023-03-19 07:25:00',
  },
  {
    value: 37124.070088808825,
    time: '2023-03-19 07:30:00',
  },
  {
    value: 37067.77027333213,
    time: '2023-03-19 07:35:00',
  },
  {
    value: 37510.635957253595,
    time: '2023-03-19 07:40:00',
  },
  {
    value: 39109.69869962547,
    time: '2023-03-19 07:45:00',
  },
  {
    value: 38915.577400962415,
    time: '2023-03-19 07:50:00',
  },
  {
    value: 39521.379447829735,
    time: '2023-03-19 07:55:00',
  },
  {
    value: 40342.44315205291,
    time: '2023-03-19 08:00:00',
  },
  {
    value: 40873.870448807014,
    time: '2023-03-19 08:05:00',
  },
  {
    value: 43571.16605193075,
    time: '2023-03-19 08:10:00',
  },
  {
    value: 42982.69959166416,
    time: '2023-03-19 08:15:00',
  },
  {
    value: 43324.706089429776,
    time: '2023-03-19 08:20:00',
  },
  {
    value: 43474.63324137478,
    time: '2023-03-19 08:25:00',
  },
  {
    value: 44035.889822360405,
    time: '2023-03-19 08:30:00',
  },
  {
    value: 44420.72392670527,
    time: '2023-03-19 08:35:00',
  },
  {
    value: 44881.98410409123,
    time: '2023-03-19 08:40:00',
  },
  {
    value: 45216.30650397581,
    time: '2023-03-19 08:45:00',
  },
  {
    value: 46190.355806355,
    time: '2023-03-19 08:50:00',
  },
  {
    value: 46138.81981098382,
    time: '2023-03-19 08:55:00',
  },
  {
    value: 45495.696105145194,
    time: '2023-03-19 09:00:00',
  },
  {
    value: 46416.6915344273,
    time: '2023-03-19 09:05:00',
  },
  {
    value: 46635.352910944086,
    time: '2023-03-19 09:10:00',
  },
  {
    value: 47494.866128025074,
    time: '2023-03-19 09:15:00',
  },
  {
    value: 46398.3420066357,
    time: '2023-03-19 09:20:00',
  },
  {
    value: 47423.86458978259,
    time: '2023-03-19 09:25:00',
  },
  {
    value: 47978.28480565882,
    time: '2023-03-19 09:30:00',
  },
  {
    value: 47562.30979097113,
    time: '2023-03-19 09:35:00',
  },
  {
    value: 47880.85822678005,
    time: '2023-03-19 09:40:00',
  },
  {
    value: 47898.223712245046,
    time: '2023-03-19 09:45:00',
  },
  {
    value: 47759.9335899819,
    time: '2023-03-19 09:50:00',
  },
  {
    value: 47763.81635963407,
    time: '2023-03-19 09:55:00',
  },
  {
    value: 47086.48483565262,
    time: '2023-03-19 10:00:00',
  },
  {
    value: 47302.0503050403,
    time: '2023-03-19 10:05:00',
  },
  {
    value: 46979.02568581467,
    time: '2023-03-19 10:10:00',
  },
  {
    value: 46770.18808038031,
    time: '2023-03-19 10:15:00',
  },
  {
    value: 46866.0855742877,
    time: '2023-03-19 10:20:00',
  },
  {
    value: 48575.47727430197,
    time: '2023-03-19 10:25:00',
  },
  {
    value: 48277.970587126314,
    time: '2023-03-19 10:30:00',
  },
  {
    value: 47549.97796939275,
    time: '2023-03-19 10:35:00',
  },
  {
    value: 48217.04647020301,
    time: '2023-03-19 10:40:00',
  },
  {
    value: 46923.64858108646,
    time: '2023-03-19 10:45:00',
  },
  {
    value: 47435.63510576571,
    time: '2023-03-19 10:50:00',
  },
  {
    value: 47788.125586562805,
    time: '2023-03-19 10:55:00',
  },
  {
    value: 47751.154784806975,
    time: '2023-03-19 11:00:00',
  },
  {
    value: 47190.58489734189,
    time: '2023-03-19 11:05:00',
  },
  {
    value: 47645.76584366009,
    time: '2023-03-19 11:10:00',
  },
  {
    value: 48002.90957374713,
    time: '2023-03-19 11:15:00',
  },
  {
    value: 47833.80607869976,
    time: '2023-03-19 11:20:00',
  },
  {
    value: 47772.68658402513,
    time: '2023-03-19 11:25:00',
  },
  {
    value: 47997.4643085004,
    time: '2023-03-19 11:30:00',
  },
  {
    value: 47976.57761637559,
    time: '2023-03-19 11:35:00',
  },
  {
    value: 47872.82982649797,
    time: '2023-03-19 11:40:00',
  },
  {
    value: 48961.054247304404,
    time: '2023-03-19 11:45:00',
  },
  {
    value: 48462.33473037338,
    time: '2023-03-19 11:50:00',
  },
  {
    value: 48584.74683778837,
    time: '2023-03-19 11:55:00',
  },
  {
    value: 48737.63084982004,
    time: '2023-03-19 12:00:00',
  },
  {
    value: 48628.348644923426,
    time: '2023-03-19 12:05:00',
  },
  {
    value: 48499.286002396104,
    time: '2023-03-19 12:10:00',
  },
  {
    value: 48310.622780095844,
    time: '2023-03-19 12:15:00',
  },
  {
    value: 47479.04707402676,
    time: '2023-03-19 12:20:00',
  },
  {
    value: 47557.98578956262,
    time: '2023-03-19 12:25:00',
  },
  {
    value: 47415.23893846122,
    time: '2023-03-19 12:30:00',
  },
  {
    value: 47672.02671607864,
    time: '2023-03-19 12:35:00',
  },
  {
    value: 47572.77297349693,
    time: '2023-03-19 12:40:00',
  },
  {
    value: 47146.154970192336,
    time: '2023-03-19 12:45:00',
  },
  {
    value: 46609.01784323925,
    time: '2023-03-19 12:50:00',
  },
  {
    value: 45743.15152765095,
    time: '2023-03-19 12:55:00',
  },
  {
    value: 45550.299968400715,
    time: '2023-03-19 13:00:00',
  },
  {
    value: 45539.952415061394,
    time: '2023-03-19 13:05:00',
  },
  {
    value: 45430.8384989266,
    time: '2023-03-19 13:10:00',
  },
  {
    value: 44617.74070389466,
    time: '2023-03-19 13:15:00',
  },
  {
    value: 44981.655475432766,
    time: '2023-03-19 13:20:00',
  },
  {
    value: 45783.44434880216,
    time: '2023-03-19 13:25:00',
  },
  {
    value: 44066.60188489241,
    time: '2023-03-19 13:30:00',
  },
  {
    value: 44132.07084996792,
    time: '2023-03-19 13:35:00',
  },
  {
    value: 42961.04501793483,
    time: '2023-03-19 13:40:00',
  },
  {
    value: 42800.816153757194,
    time: '2023-03-19 13:45:00',
  },
  {
    value: 42808.6032655138,
    time: '2023-03-19 13:50:00',
  },
  {
    value: 42600.02825372836,
    time: '2023-03-19 13:55:00',
  },
  {
    value: 42371.30426991178,
    time: '2023-03-19 14:00:00',
  },
  {
    value: 41892.477838844956,
    time: '2023-03-19 14:05:00',
  },
  {
    value: 41507.46145175683,
    time: '2023-03-19 14:10:00',
  },
  {
    value: 41309.753604583726,
    time: '2023-03-19 14:15:00',
  },
  {
    value: 41035.742731007966,
    time: '2023-03-19 14:20:00',
  },
  {
    value: 40692.47234877444,
    time: '2023-03-19 14:25:00',
  },
  {
    value: 40727.96081357668,
    time: '2023-03-19 14:30:00',
  },
  {
    value: 39580.61311120066,
    time: '2023-03-19 14:35:00',
  },
  {
    value: 39827.524766326314,
    time: '2023-03-19 14:40:00',
  },
  {
    value: 39757.09242712131,
    time: '2023-03-19 14:45:00',
  },
  {
    value: 39541.77527416602,
    time: '2023-03-19 14:50:00',
  },
  {
    value: 39070.51378815371,
    time: '2023-03-19 14:55:00',
  },
  {
    value: 39230.390802697606,
    time: '2023-03-19 15:00:00',
  },
  {
    value: 39497.481806454925,
    time: '2023-03-19 15:05:00',
  },
  {
    value: 40012.294545586265,
    time: '2023-03-19 15:10:00',
  },
  {
    value: 39945.35762285438,
    time: '2023-03-19 15:15:00',
  },
  {
    value: 40332.21756798394,
    time: '2023-03-19 15:20:00',
  },
  {
    value: 39841.20751803724,
    time: '2023-03-19 15:25:00',
  },
  {
    value: 39830.19450112964,
    time: '2023-03-19 15:30:00',
  },
  {
    value: 40707.31817413618,
    time: '2023-03-19 15:35:00',
  },
  {
    value: 39752.905261887994,
    time: '2023-03-19 15:40:00',
  },
  {
    value: 40091.3268666754,
    time: '2023-03-19 15:45:00',
  },
  {
    value: 39494.606094201554,
    time: '2023-03-19 15:50:00',
  },
  {
    value: 39704.173118609506,
    time: '2023-03-19 15:55:00',
  },
  {
    value: 40010.7040932236,
    time: '2023-03-19 16:00:00',
  },
  {
    value: 40728.1163531617,
    time: '2023-03-19 16:05:00',
  },
  {
    value: 41136.38918730065,
    time: '2023-03-19 16:10:00',
  },
  {
    value: 41672.4347606187,
    time: '2023-03-19 16:15:00',
  },
  {
    value: 42147.30119149287,
    time: '2023-03-19 16:20:00',
  },
  {
    value: 42085.65785770176,
    time: '2023-03-19 16:25:00',
  },
  {
    value: 42896.69719926836,
    time: '2023-03-19 16:30:00',
  },
  {
    value: 43015.27745630221,
    time: '2023-03-19 16:35:00',
  },
  {
    value: 43686.56641850337,
    time: '2023-03-19 16:40:00',
  },
  {
    value: 44100.49949502732,
    time: '2023-03-19 16:45:00',
  },
  {
    value: 44348.508138299454,
    time: '2023-03-19 16:50:00',
  },
  {
    value: 44917.28037332849,
    time: '2023-03-19 16:55:00',
  },
  {
    value: 44613.45504553509,
    time: '2023-03-19 17:00:00',
  },
  {
    value: 44593.98574809573,
    time: '2023-03-19 17:05:00',
  },
  {
    value: 45691.330103322696,
    time: '2023-03-19 17:10:00',
  },
  {
    value: 45838.61512512764,
    time: '2023-03-19 17:15:00',
  },
  {
    value: 46726.787641241375,
    time: '2023-03-19 17:20:00',
  },
  {
    value: 47979.96857101929,
    time: '2023-03-19 17:25:00',
  },
  {
    value: 47515.78911259077,
    time: '2023-03-19 17:30:00',
  },
  {
    value: 47166.20631284171,
    time: '2023-03-19 17:35:00',
  },
  {
    value: 47502.96539514895,
    time: '2023-03-19 17:40:00',
  },
  {
    value: 47392.93807015088,
    time: '2023-03-19 17:45:00',
  },
  {
    value: 46609.50735089655,
    time: '2023-03-19 17:50:00',
  },
  {
    value: 47835.637231909735,
    time: '2023-03-19 17:55:00',
  },
  {
    value: 47455.264642503054,
    time: '2023-03-19 18:00:00',
  },
  {
    value: 46916.83603848071,
    time: '2023-03-19 18:05:00',
  },
  {
    value: 47803.7233092924,
    time: '2023-03-19 18:10:00',
  },
  {
    value: 47909.69223828866,
    time: '2023-03-19 18:15:00',
  },
  {
    value: 47545.62569453229,
    time: '2023-03-19 18:20:00',
  },
  {
    value: 48637.299816744744,
    time: '2023-03-19 18:25:00',
  },
  {
    value: 49542.318639504534,
    time: '2023-03-19 18:30:00',
  },
  {
    value: 49415.42189827686,
    time: '2023-03-19 18:35:00',
  },
  {
    value: 49090.35331012458,
    time: '2023-03-19 18:40:00',
  },
  {
    value: 49417.61537561905,
    time: '2023-03-19 18:45:00',
  },
  {
    value: 49786.8794397902,
    time: '2023-03-19 18:50:00',
  },
  {
    value: 49243.637318830326,
    time: '2023-03-19 18:55:00',
  },
  {
    value: 49762.10105533346,
    time: '2023-03-19 19:00:00',
  },
  {
    value: 49127.19628022338,
    time: '2023-03-19 19:05:00',
  },
  {
    value: 48877.759542219304,
    time: '2023-03-19 19:10:00',
  },
  {
    value: 49213.90226126463,
    time: '2023-03-19 19:15:00',
  },
  {
    value: 49907.936296974476,
    time: '2023-03-19 19:20:00',
  },
  {
    value: 49470.20819392983,
    time: '2023-03-19 19:25:00',
  },
  {
    value: 50389.14368160001,
    time: '2023-03-19 19:30:00',
  },
  {
    value: 50189.00503785308,
    time: '2023-03-19 19:35:00',
  },
  {
    value: 50426.3426575449,
    time: '2023-03-19 19:40:00',
  },
  {
    value: 49677.448369568534,
    time: '2023-03-19 19:45:00',
  },
  {
    value: 50415.077060103344,
    time: '2023-03-19 19:50:00',
  },
  {
    value: 50205.52952106143,
    time: '2023-03-19 19:55:00',
  },
  {
    value: 50593.36185696531,
    time: '2023-03-19 20:00:00',
  },
  {
    value: 51181.19311189139,
    time: '2023-03-19 20:05:00',
  },
  {
    value: 51953.75684158388,
    time: '2023-03-19 20:10:00',
  },
  {
    value: 51876.168988623416,
    time: '2023-03-19 20:15:00',
  },
  {
    value: 51844.56529933583,
    time: '2023-03-19 20:20:00',
  },
  {
    value: 51496.65125790888,
    time: '2023-03-19 20:25:00',
  },
  {
    value: 52110.478129711715,
    time: '2023-03-19 20:30:00',
  },
  {
    value: 52147.33693935347,
    time: '2023-03-19 20:35:00',
  },
  {
    value: 52094.38620788652,
    time: '2023-03-19 20:40:00',
  },
  {
    value: 52552.2793858421,
    time: '2023-03-19 20:45:00',
  },
  {
    value: 52376.53225950577,
    time: '2023-03-19 20:50:00',
  },
  {
    value: 52068.96997652281,
    time: '2023-03-19 20:55:00',
  },
  {
    value: 52017.28789230684,
    time: '2023-03-19 21:00:00',
  },
  {
    value: 51586.23402289259,
    time: '2023-03-19 21:05:00',
  },
  {
    value: 52037.28314503237,
    time: '2023-03-19 21:10:00',
  },
  {
    value: 51833.8124374333,
    time: '2023-03-19 21:15:00',
  },
  {
    value: 51355.08251337749,
    time: '2023-03-19 21:20:00',
  },
  {
    value: 51195.3070956589,
    time: '2023-03-19 21:25:00',
  },
  {
    value: 50986.023132997136,
    time: '2023-03-19 21:30:00',
  },
  {
    value: 51131.180187764185,
    time: '2023-03-19 21:35:00',
  },
  {
    value: 49776.2555544086,
    time: '2023-03-19 21:40:00',
  },
  {
    value: 50467.37370247883,
    time: '2023-03-19 21:45:00',
  },
  {
    value: 50173.49500630018,
    time: '2023-03-19 21:50:00',
  },
  {
    value: 49284.485337859645,
    time: '2023-03-19 21:55:00',
  },
  {
    value: 48704.58130617278,
    time: '2023-03-19 22:00:00',
  },
  {
    value: 48662.27614209195,
    time: '2023-03-19 22:05:00',
  },
  {
    value: 48452.25008408127,
    time: '2023-03-19 22:10:00',
  },
  {
    value: 48105.60822383952,
    time: '2023-03-19 22:15:00',
  },
  {
    value: 47268.43688902076,
    time: '2023-03-19 22:20:00',
  },
  {
    value: 47107.723465817544,
    time: '2023-03-19 22:25:00',
  },
  {
    value: 46015.307965196815,
    time: '2023-03-19 22:30:00',
  },
  {
    value: 45356.409573573415,
    time: '2023-03-19 22:35:00',
  },
  {
    value: 45085.69663128704,
    time: '2023-03-19 22:40:00',
  },
  {
    value: 44183.73496109673,
    time: '2023-03-19 22:45:00',
  },
  {
    value: 43392.527075431186,
    time: '2023-03-19 22:50:00',
  },
  {
    value: 42696.03179537844,
    time: '2023-03-19 22:55:00',
  },
  {
    value: 41686.534945782216,
    time: '2023-03-19 23:00:00',
  },
  {
    value: 41480.56939535029,
    time: '2023-03-19 23:05:00',
  },
  {
    value: 41165.130429890174,
    time: '2023-03-19 23:10:00',
  },
  {
    value: 40505.520747402676,
    time: '2023-03-19 23:15:00',
  },
  {
    value: 40064.99258464816,
    time: '2023-03-19 23:20:00',
  },
  {
    value: 38980.27872800273,
    time: '2023-03-19 23:25:00',
  },
  {
    value: 38137.301600623694,
    time: '2023-03-19 23:30:00',
  },
  {
    value: 37242.50313054035,
    time: '2023-03-19 23:35:00',
  },
  {
    value: 37005.155747834055,
    time: '2023-03-19 23:40:00',
  },
  {
    value: 36113.743757681135,
    time: '2023-03-19 23:45:00',
  },
  {
    value: 35395.81816302289,
    time: '2023-03-19 23:50:00',
  },
  {
    value: 34573.226802753255,
    time: '2023-03-19 23:55:00',
  },
];

export function getMockFlowTimeData(date: string, delta: number): TimeData[] {
  const data: TimeData[] = [];
  for (let i: number = 0; i < mockFlowTimeData.length; i += 1) {
    const item: TimeData = {
      time: date + mockFlowTimeData[i].time.slice(date.length),
      value: mockFlowTimeData[i].value + delta,
    };
    data.push(item);
  }

  return data;
}

const yesterday = dayjs().add(-1, 'day').format('YYYY-MM-DD');

export const defaultSolutionObject = {
  solution_type: 'COMMON',
  base_day: {
    date: yesterday,
    device_score: 0,
    model_score: 0,
    total_flow: 0,
  },
  demand: {
    changes: [],
    data: [
      {
        '2023-04-13 00:00:00': 33268.747973,
      },
      {
        '2023-04-13 00:01:00': 33090.380778,
      },
      {
        '2023-04-13 00:02:00': 33070.166145,
      },
    ],
    total_flow: 0,
  },
  junction_quality: [],
  link_state: [],
  model_id: undefined,
  name: undefined,
  note: undefined,
  options: {
    DEMAND_MODEL: 'FIXED',
    'Demand Multiplier': 1,
    MINIMUM_PRESSURE: 0,
    PRESSURE_EXPONENT: 0.5,
    SERVICE_PRESSURE: 14,
  },
  plants: [],
  pump_stations: [],
  simulation_start_time: dayjs().add(-1, 'day').format('YYYY-MM-DD 00:00:00'),
  simulation_end_time: dayjs().add(-1, 'day').format('YYYY-MM-DD 23:59:59'),
  calculate_start_time: dayjs().add(-1, 'day').format('YYYY-MM-DD 00:00:00'),
  calculate_end_time: dayjs().add(-1, 'day').format('YYYY-MM-DD 23:59:00'),
  times: { 'Hydraulic Timestep': '0:05', 'Quality Timestep': '0:05' },
  junction_demand: [],
  observations: [],
};

export function getMockDefaultSolutionDetail() {
  return getSolutionDetail(defaultSolutionObject);
}

const plantValueSetting1: PlantValueChangeSetting = {
  pattern: 'XQSC-HEAD',
  value: 2,
  method: 'add',
  startTime: '08:00',
  endTime: '10:00',
};

const plantValueSetting2: PlantValueChangeSetting = {
  pattern: 'XQSC-HEAD',
  value: 38.5,
  method: 'assign',
  startTime: '14:00',
  endTime: '16:00',
};

const plantChange1: PlantChangeSetting = {
  id: 'XQ-SC',
  mode: 'HEAD',
  valueChanges: [plantValueSetting1, plantValueSetting2],
};

const plantValueSetting3: PlantValueChangeSetting = {
  pattern: 'CQ-SC-FLOW',
  value: 20000,
  method: 'add',
  startTime: '08:00',
  endTime: '10:00',
};

const plantChange2: PlantChangeSetting = {
  id: 'CQ-SC',
  mode: 'FLOW',
  valueChanges: [plantValueSetting3],
};

const plantChange3: PlantChangeSetting = {
  id: 'DQ-SC',
  mode: 'HEAD',
  valueChanges: [],
};

const plantValueSetting4: PlantValueChangeSetting = {
  pattern: 'DN-PUMP1',
  value: 1,
  method: 'assign',
  startTime: '08:00',
  endTime: '10:00',
};

const plantValueSetting5: PlantValueChangeSetting = {
  pattern: 'DN-PUMP2',
  value: 48.5,
  method: 'assign',
  startTime: '08:00',
  endTime: '10:00',
};

const plantChange4: PlantChangeSetting = {
  id: 'DN-SC',
  mode: 'PUMP',
  valueChanges: [plantValueSetting4, plantValueSetting5],
};

const pumpStationValueSetting: ValueChangeSetting = {
  value: 2,
  method: 'add',
  startTime: '08:00',
  endTime: '10:00',
};

const pumpStationPatterns1: Map<string, ValueChangeSetting[]> = new Map();
pumpStationPatterns1.set('XQ-SC-HEAD', [pumpStationValueSetting]);

const pumpStationChange1: PumpStationChangeSetting = {
  id: 'QT-BZ',
  patterns: pumpStationPatterns1,
};

const linkStatus1: LinkStatusChangeSetting = {
  time: '00:01',
  id: 'P56695A',
  status: 'OPEN',
  otype: 'pipe',
  openingValue: 100,
  statusType: 'OPEN',
};

export const demandValueChange1: ValueChangeSetting = {
  value: 20000,
  method: 'add',
  startTime: '08:00',
  endTime: '10:00',
};

export const demandValueChange2: ValueChangeSetting = {
  value: 10000,
  method: 'minus',
  startTime: '14:00',
  endTime: '16:00',
};

export const demandValueChange3: ValueChangeSetting = {
  value: 30000,
  method: 'assign',
  startTime: '18:00',
  endTime: '21:00',
};

const demandTimeData: TimeData[] = [
  { time: '00:00', value: 12345 },
  { time: '00:05', value: 12345 },
  { time: '00:10', value: 12345 },
];

const junctionDemandValue1: JunctionDemandChangeSetting = {
  id: 'XHS-9600',
  demand: 0,
  otype: 'WDM_HYDRANT',
  shape: 'POINT(430298.089778172 2884115.23045981)',
};

const junctionQualityValue1: JunctionQualityChangeSetting = {
  id: 'J56111',
  otype: 'WDM_HYDRANT',
  shape: 'POINT(430273.86950000003 2884182.416200001)',
  startTime: '2024-02-19 06:47:00',
  endTime: '2024-02-19 13:43:00',
};

export function getMockSampleSolutionDetail() {
  const solution = getSolutionDetail();

  solution.name = '3月18日提压方案';
  solution.modelId = '福州';
  solution.note = '早高峰提压加水量...';
  solution.simulationStartTime = '2023-03-18 00:00:00';
  solution.simulationEndTime = '2023-03-18 23:59:00';

  solution.calculateStartTime = '2023-03-18 01:00:00';
  solution.calculateEndTime = '2023-03-18 03:59:00';

  solution.baseDayInfo.date = '2023-03-18 00:00:00';
  solution.baseDayInfo.totalFlow = 1035100;
  solution.baseDayInfo.modelScore = 86.6;
  solution.baseDayInfo.deviceScore = 93.8;

  solution.flowInfo.totalFlow = 1075100;
  solution.flowInfo.changes.push(
    ...[demandValueChange1, demandValueChange2, demandValueChange3],
  );
  solution.flowInfo.data.push(...demandTimeData);

  solution.plantChangeSettings.set(plantChange1.id, plantChange1);
  solution.plantChangeSettings.set(plantChange2.id, plantChange2);
  solution.plantChangeSettings.set(plantChange3.id, plantChange3);
  solution.plantChangeSettings.set(plantChange4.id, plantChange4);

  solution.pumpStationChangeSettings.set(
    pumpStationChange1.id,
    pumpStationChange1,
  );

  solution.linkStatusChangeSettings.push(linkStatus1);

  solution.demandChangeSettings.push(junctionDemandValue1);
  solution.qualityChangeSettings.push(junctionQualityValue1);

  return solution;
}

export function getMockSampleSolutionBaseInfo() {
  const baseInfo = {
    end_time: '2023-04-06 23:55:00',
    solution_error: null,
    solution_note: '',
    solution_share: '1',
    solution_status: 'IDLE',
    solution_status_msg: '完成计算',
    solution_title: '早晚高峰各加1万吨',
    start_time: '2023-04-06 00:00:00',
    solution_from: 'ONLINE',
    creator: '叶长青',
  };
  return getBaseSolutionInfoData(baseInfo);
}

export const sampleSolutionObject = {
  name: '3月18日提压方案',
  model_id: '福州',
  note: '早高峰提压加水量...',
  simulation_start_time: '2023-03-18 00:00:00',
  simulation_end_time: '2023-03-18 23:59:00',
  calculate_start_time: '2023-03-18 01:00:00',
  calculate_end_time: '2023-03-18 03:59:00',
  base_day: {
    date: '2023-03-18 00:00:00',
    total_flow: 1035100,
    model_score: 86.6,
    device_score: 93.8,
  },
  demand: {
    total_flow: 1075100,
    changes: [
      {
        value: 20000,
        method: 'diff',
        start_time: '08:00',
        end_time: '10:00',
      },
      {
        value: -10000,
        method: 'diff',
        start_time: '14:00',
        end_time: '16:00',
      },
      {
        value: 30000,
        method: 'fixed',
        start_time: '18:00',
        end_time: '21:00',
      },
    ],
    data: [{ '00:00': 12345 }, { '00:05': 12345 }, { '00:10': 12345 }],
  },
  plants: [
    {
      id: 'XQ-SC',
      mode: 'HEAD',
      patterns: [
        {
          pattern: 'XQSC-HEAD',
          changes: [
            {
              value: 2,
              method: 'diff',
              start_time: '08:00',
              end_time: '10:00',
            },
            {
              value: 38.5,
              method: 'fixed',
              start_time: '14:00',
              end_time: '16:00',
            },
          ],
        },
      ],
    },
    {
      id: 'CQ-SC',
      mode: 'FLOW',
      patterns: [
        {
          pattern: 'CQ-SC-FLOW',
          changes: [
            {
              value: 20000,
              method: 'diff',
              start_time: '08:00',
              end_time: '10:00',
            },
          ],
        },
      ],
    },
    {
      id: 'DQ-SC',
      mode: 'HEAD',
      patterns: [],
    },
    {
      id: 'DN-SC',
      mode: 'PUMP',
      patterns: [
        {
          pattern: 'DN-PUMP1',
          changes: [
            {
              value: 1,
              method: 'fixed',
              start_time: '08:00',
              end_time: '10:00',
            },
          ],
        },
        {
          pattern: 'DN-PUMP2',
          changes: [
            {
              value: 48.5,
              method: 'fixed',
              start_time: '08:00',
              end_time: '10:00',
            },
          ],
        },
      ],
    },
  ],
  pump_stations: [
    {
      id: 'QT-BZ',
      patterns: [
        {
          pattern: 'XQ-SC-HEAD',
          changes: [
            {
              value: 2,
              method: 'diff',
              start_time: '08:00',
              end_time: '10:00',
            },
          ],
        },
      ],
    },
  ],
  link_state: [
    {
      time: '00:01',
      oname: 'P56695A',
      otype: 'pipe',
      shape: undefined,
      value: 'OPEN',
      openingValue: 100,
      statusType: 'OPEN',
    },
  ],
  options: {
    PRESSURE_EXPONENT: 0.5,
    'Demand Multiplier': 1,
    DEMAND_MODEL: 'FIXED',
    MINIMUM_PRESSURE: 0,
    SERVICE_PRESSURE: 14,
  },
  times: {
    'Hydraulic Timestep': '1:00',
    'Quality Timestep': '0:05',
  },
};
