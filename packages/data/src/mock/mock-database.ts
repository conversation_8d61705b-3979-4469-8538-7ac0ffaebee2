/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import { DeviceCollection } from '../device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '../indicator';
import { LayerDataCollection } from '../layer-data';
import { PropertyInfo } from '../property/property-info';
import { registerSystemUnits } from '../unit-system';
import {
  deviceIndicatorsJson,
  deviceJsonArray,
  iconDataJson,
  indicatorsData,
  indicatorTypesJson,
} from './mock-device';
import { displayLayers, layerDetails } from './mock-layers';
import { definesData, unitData } from './mock-property-info';

export default function mockDatabase(): Database {
  const layerCollection = new LayerDataCollection();
  layerCollection.initialize(displayLayers, layerDetails);
  const deviceCollection: DeviceCollection = new DeviceCollection();
  deviceCollection.initializeDevices(deviceJsonArray, []);
  const indicatorCollection: IndicatorTypeCollection =
    new IndicatorTypeCollection();
  indicatorCollection.initialize(indicatorTypesJson);

  const indicatorObjects: IndicatorObjectCollection =
    new IndicatorObjectCollection();
  indicatorObjects.initializeIndicators(indicatorsData, deviceCollection);

  deviceCollection.initializeOverlayIndicators(
    deviceIndicatorsJson,
    indicatorCollection,
    indicatorObjects,
  );
  layerCollection.initializeDevices(deviceCollection);

  const icons: Map<string, string> = new Map();
  Object.entries(iconDataJson).forEach((item) => {
    icons.set(item[0], item[1]);
  });
  deviceCollection.initializeIcons(icons);

  const propertyInfos: Map<string, PropertyInfo> = new Map();
  definesData.forEach((item: any) => {
    const { otype, title } = item;
    const propertyInfo = new PropertyInfo(otype, title);
    let quickPropertiesData = [];
    if (item.tooltip_view !== undefined && item.tooltip_view.length > 0)
      quickPropertiesData = item.tooltip_view[0].prop_list;

    let scadaTreeConfig = [];
    if (
      typeof item.tooltip_view !== 'undefined' &&
      typeof item.tooltip_view.scadaTreeView !== 'undefined' &&
      item.tooltip_view.scadaTreeView.length > 0
    )
      scadaTreeConfig = item.tooltip_view[0].scadaTreeView;
    propertyInfo.initialize(
      item.prop_view,
      item.props,
      quickPropertiesData,
      scadaTreeConfig,
      true,
    );
    propertyInfos.set(otype, propertyInfo);
  });

  const extent: Array<number> = [0, 0, 100, 100];
  const db: Database = new Database();

  db.initializeDevice(deviceCollection, propertyInfos);
  db.initializeIndicator(indicatorObjects);
  db.initializePropertyInfos(propertyInfos);
  db.initializeIcon(icons);
  db.initializeLayer(
    layerCollection,

    extent,
  );
  registerSystemUnits(unitData);
  return db;
}
