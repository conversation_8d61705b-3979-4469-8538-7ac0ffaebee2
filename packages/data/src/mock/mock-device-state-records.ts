/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// eslint-disable-next-line import/no-extraneous-dependencies
import Mo<PERSON> from 'mockjs';
import { DeviceStateData } from '../device-time-data';

const DeviceStateDataMock = {
  id: '@guid', // 生成 GUID 类型的 id
  key: '@word(5)', // 生成一个长度为 5 的单词作为 key
  title: '@city(true)', // 生成一个真实的城市名作为 title
  typeTitle: '@cname', // 生成一个随机的中文名字作为 typeTitle
  oname: '@cname', // 生成一个随机的中文名字作为 oname
  otype: '@word(2)', // 生成一个长度为 2 的单词作为 otype
  otime: '2023-03-25', // 这里假设 otime 是一个固定的日期，可以根据需要修改
  state: '@boolean', // 生成一个随机的布尔值
  source: '@url', // 生成一个随机的 URL
  note: '@sentence(10)', // 生成一个长度为 10 的随机句子作为 note
  creator: '@cname', // 生成一个随机的中文名字作为 creator
  stime: '2023-03-25 10:20:30', // 这里假设 stime 是一个固定的日期时间，可以根据需要修改
};

export function mockDeviceStateData(total: number): DeviceStateData[] {
  const data: DeviceStateData[] = [];
  for (let i = 0; i < total; i += 1) {
    data.push(Mock.mock(DeviceStateDataMock) as DeviceStateData);
  }
  return data;
}
