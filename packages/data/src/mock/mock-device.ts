/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const deviceFlowJson1 = {
  ADDRESS: null,
  CALIBER: null,
  DATASOURCE: '福田水务局',
  DEVICE_TYPE: null,
  DISPLAYLEVEL: null,
  EQUIPMENT_MODEL: null,
  FACTORY: null,
  GROUND_LEVEL: null,
  JUNCTION: null,
  MAX_DRAW_RATIO: null,
  MEASURING_ACCURACY: null,
  MIN_DRAW_RATIO: null,
  MONITOR_OBJECT: '排水流量',
  MONITOR_POINT: 'C0220010007156',
  ONAME: 'C0220010007156',
  ORANIZATION: null,
  OTYPE: 'DEV_FLOW',
  PARENT_AREA: '福田',
  PNAME: 'NETWORK',
  PTYPE: 'SDFOLD_NETWORK_RAIN',
  REMARK: null,
  SAMPLING_TIMESTEP: null,
  SEND_TIMESTEP: null,
  SHAPE: 'POINT(506542.3232 2491334.7526)',
  SHORT_TITLE: '(雨水)皇岗路与国花路交叉口',
  STATE: null,
  TITLE: '(雨水)皇岗路与国花路交叉口',
  SUMMARIZE: null,
};

export const deviceFlowJson2 = {
  ADDRESS: null,
  CALIBER: null,
  DATASOURCE: '福田水务局',
  DEVICE_TYPE: null,
  DISPLAYLEVEL: null,
  EQUIPMENT_MODEL: null,
  FACTORY: null,
  GROUND_LEVEL: null,
  JUNCTION: null,
  MAX_DRAW_RATIO: null,
  MEASURING_ACCURACY: null,
  MIN_DRAW_RATIO: null,
  MONITOR_OBJECT: '排水流量',
  MONITOR_POINT: 'C0220010007129',
  ONAME: 'C0220010007129',
  ORANIZATION: null,
  OTYPE: 'DEV_FLOW',
  PARENT_AREA: '福田',
  PNAME: 'NETWORK',
  PTYPE: 'SDFOLD_NETWORK_RAIN',
  REMARK: null,
  SAMPLING_TIMESTEP: null,
  SEND_TIMESTEP: null,
  SHAPE: 'POINT(504136.0077 2496058.63)',
  SHORT_TITLE: null,
  STATE: null,
  TITLE: null,
  SUMMARIZE: 'false',
};

export const devicePondJson1 = {
  ADDRESS: null,
  DATASOURCE: null,
  DEVICE_TYPE: null,
  DISPLAYLEVEL: 0,
  EQUIPMENT_MODEL: null,
  FACTORY: null,
  MAX_DRAW_RATIO: 4.5,
  MEASURING_ACCURACY: null,
  MIN_DRAW_RATIO: 2,
  MONITOR_OBJECT: '福田',
  MONITOR_POINT: 'C0220010002003',
  ONAME: 'C0220010002003',
  ORANIZATION: null,
  OTYPE: 'DEV_POND',
  PARENT_AREA: '福田',
  PNAME: 'NETWORK',
  PTYPE: 'SDFOLD_POND_ROOT',
  REMARK: null,
  SAMPLING_TIMESTEP: null,
  SEND_TIMESTEP: null,
  SHAPE: 'POINT(505961.298 2494272.563)',
  SHORT_TITLE: null,
  STATE: null,
  TITLE: '岗厦北高架桥旁                ',
  SUMMARIZE: 'true',
};

export const devRainfallJson = {
  ADDRESS: '深圳市宝安区石岩街道浪心社区水库路168号石岩水库宿舍楼顶',
  DATASOURCE: '深圳气象局',
  DEVICE_TYPE: 'RAINFALL',
  DISPLAYLEVEL: null,
  EQUIPMENT_MODEL: '维萨拉-MAWS301',
  FACTORY: null,
  GROUND_LEVEL: null,
  MAX_DRAW_RATIO: null,
  MEASURING_ACCURACY: null,
  MIN_DRAW_RATIO: null,
  MONITOR_OBJECT: '其他',
  MONITOR_POINT: 'G3531',
  ONAME: 'G3531',
  ORANIZATION: '石岩街道',
  OTYPE: 'DEV_RAINFALL',
  PARENT_AREA: null,
  PNAME: 'NETWORK',
  PTYPE: 'SDFOLD_RAIN_ROOT',
  REMARK: '1998/10/14',
  SAMPLING_TIMESTEP: '5',
  SEND_TIMESTEP: '5',
  SHAPE: 'POINT(489725.583 2511315.58)',
  SHORT_TITLE: '石岩水库',
  STATE: null,
  TITLE: '石岩水库',
};

export const deviceSimulationJson = {
  OTYPE: 'DEV_PF',
  ONAME: 'STA40',
  PTYPE: 'SDFOLD_WDM_ZON1',
  PNAME: 'NETWORK',
  DEVICE_TYPE: '压流一体',
  MAX_DRAW_RATIO: null,
  PARENT_AREA: '假PARENT_AREA',
  SAMPLING_TIMESTEP: '60',
  DATASOURCE: '管网',
  ASSESSMENT_CONFIGURE: null,
  SEND_TIMESTEP: '60',
  CALIBER: '800',
  JUNCTION: null,
  BUILD_TIME: '2013.1',
  GROUND_LEVEL: '20',
  POWER_SUPPLY: null,
  REMARK: null,
  STATION_TYPE_S: '市政基础参数',
  STATION_TYPE_I: '管网',
  STATION_GRADE: '一级分区',
  FACTORY: '汇中',
  DISPLAYLEVEL: '2',
  INSTALLATION_HEIGHT: null,
  TITLE: '洪山桥北DN800',
  MONITOR_POINT: null,
  MEASURING_ACCURACY: null,
  CHANGE_REPORT: '0',
  ADDRESS: null,
  EQUIPMENT_MODEL: null,
  STATE: '1',
  ORANIZATION: null,
  SHORT_TITLE: '洪山桥北DN800',
  MIN_DRAW_RATIO: '4.2',
  MONITOR_OBJECT: '一级分区',
  SHAPE: 'POINT(424857.91779999994 2886043.2237)',
};

export const deviceJsonArray = [
  deviceFlowJson1,
  deviceFlowJson2,
  devicePondJson1,
  devRainfallJson,
  deviceSimulationJson,
];

export const indicatorTypesJson = [
  { group: 'QUOTA', icon: '\\ue681', otype: 'SDVAL_FLOW_W', title: '瞬时流量' },
  { group: 'QUOTA', icon: '\\ue696', otype: 'SDVAL_PRESS', title: '压力' },
  { group: 'QUOTA', icon: '\\ue696', otype: 'SDVAL_PONDING', title: '内涝深' },
];

export const deviceIndicatorsJson = {
  DEV_FLOW: {
    C0220010007156: [{ OTYPE: 'SDVAL_FLOW_W', ONAME: 'C022001000715601' }],
    C0220010007127: [{ OTYPE: 'SDVAL_FLOW_W', ONAME: 'C022001000712701' }],
    C0220010007129: [
      { OTYPE: 'SDVAL_FLOW_W', ONAME: 'C022001000712901' },
      { OTYPE: 'SDVAL_FLOW_W', ONAME: 'C022001000712902' },
    ],
  },
  DEV_POND: {
    C0220010002003: [{ OTYPE: 'SDVAL_PONDING', ONAME: 'C0220010002003_NL' }],
  },
};

export const iconDataJson = {
  SDFOLD_FACT: '\\ue666',
  WDM_MODELNODE: '\\ue68d',
  DEV_FLOW: '\\ue69b',
  SDFOLD_POND_ROOT: '\\ue6a8',
  DEV_FLOW_W: '\\ue69b',
};

export const mockGroupPropValuesParams =
  '{"C022001000715601@SDVAL_FLOW_W@SDVAL":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"SDVAL"},"SUM_FLOW":{"vprop":"SUM_FLOW"},"SUM_FLOW_CF":{"vprop":"SUM_FLOW_CF"},"C022001000715601@SDVAL_FLOW_W@SIM_SCORE":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"SIM_SCORE"},"C022001000715601@SDVAL_FLOW_W@MODEL_ERROR":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"MODEL_ERROR"},"C022001000715601@SDVAL_FLOW_W@MEAN_ERROR":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"MEAN_ERROR"},"C022001000715601@SDVAL_FLOW_W@ABSOLUTE_MEAN_ERROR":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"ABSOLUTE_MEAN_ERROR"},"C022001000715601@SDVAL_FLOW_W@MEDIAN_ERROR":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"MEDIAN_ERROR"},"C022001000715601@SDVAL_FLOW_W@ROOT_MEAN_SQUARE_ERROR":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"ROOT_MEAN_SQUARE_ERROR"},"C022001000715601@SDVAL_FLOW_W@ERROR_OSCILLATION":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"ERROR_OSCILLATION"},"C022001000715601@SDVAL_FLOW_W@NASH_COEFFICIENT":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"NASH_COEFFICIENT"},"RELIABILITY_S":{"vprop":"RELIABILITY_S"},"INTEGRITY":{"vprop":"INTEGRITY"},"TIMELINESS":{"vprop":"TIMELINESS"},"STATE_DAYS_S":{"vprop":"STATE_DAYS_S"},"ONAME":{"vprop":"ONAME"},"TITLE":{"vprop":"TITLE"},"STATION_GRADE":{"vprop":"STATION_GRADE"},"STATION_TYPE_I":{"vprop":"STATION_TYPE_I"},"STATION_TYPE_S":{"vprop":"STATION_TYPE_S"},"MONITOR_OBJECT":{"vprop":"MONITOR_OBJECT"},"DEVICE_TYPE":{"vprop":"DEVICE_TYPE"},"ORANIZATION":{"vprop":"ORANIZATION"},"FACTORY":{"vprop":"FACTORY"},"ADDRESS":{"vprop":"ADDRESS"},"CALIBER":{"vprop":"CALIBER"},"GROUND_LEVEL":{"vprop":"GROUND_LEVEL"},"BUILD_TIME":{"vprop":"BUILD_TIME"},"SAMPLING_TIMESTEP":{"vprop":"SAMPLING_TIMESTEP"},"SEND_TIMESTEP":{"vprop":"SEND_TIMESTEP"},"CHANGE_REPORT":{"vprop":"CHANGE_REPORT"},"REMARK":{"vprop":"REMARK"},"C022001000715601@SDVAL_FLOW_W@RELIABILITY":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"RELIABILITY"},"C022001000715601@SDVAL_FLOW_W@INTEGRITY":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"INTEGRITY"},"C022001000715601@SDVAL_FLOW_W@TIMELINESS":{"oname":"C022001000715601","otype":"SDVAL_FLOW_W","vprop":"TIMELINESS"}}';

const indicatorFlow1 = {
  LIMIT_MAX: '3.5',
  ONAME: 'C022001000715601',
  OTYPE: 'SDVAL_FLOW_W',
  PNAME: 'C0220010007156',
  PTYPE: 'DEV_FLOW',
  TITLE: null,
};

const indicatorFlow2 = {
  LIMIT_MAX: null,
  ONAME: 'D_GW_2356',
  OTYPE: 'SDVAL_FLOW_W',
  PNAME: 'STA42',
  PTYPE: 'DEV_PF',
  TITLE: 'XX流量计',
};

export const factoryJson1 = {
  otype: 'SDFOLD_FACT',
  oname: 'BQ-SC',
  pumps: [
    {
      otype: 'WDM_PUMPS',
      oname: 'BQSC-P2',
      description: '2#',
      frequencymode: 'Variable',
      minfrequency: 25,
      maxfrequency: 50,
      scada: [
        {
          otype: 'SDVAL_FREQUENCY',
          oname: 'BQSC86',
        },
        {
          otype: 'SDVAL_PUMPRUN',
          oname: 'BQSC57',
        },
      ],
    },
  ],
};

export const factoryJson2 = {
  otype: 'SDFOLD_FACT',
  oname: 'XQ-SC',
  pumps: [
    {
      otype: 'WDM_PUMPS',
      oname: 'XQSC-P1',
      description: '1#',
      frequencymode: 'Fixed',
      minfrequency: null,
      maxfrequency: null,
      scada: [
        {
          otype: 'SDVAL_PUMPRUN',
          oname: 'lt.lt.FacO6.EBF.PUMP1.STATE',
        },
      ],
    },
    {
      otype: 'WDM_PUMPS',
      oname: 'XQSC-P2',
      description: '2#',
      frequencymode: 'Variable',
      minfrequency: 25,
      maxfrequency: 50,
      scada: [
        {
          otype: 'SDVAL_FREQUENCY',
          oname: 'lt.lt.FacO6.EBF.PUMP2.FREQ',
        },
        {
          otype: 'SDVAL_PUMPRUN',
          oname: 'lt.lt.FacO6.EBF.PUMP2.STATE',
        },
      ],
    },
  ],
};

export const indicatorsData = [indicatorFlow1, indicatorFlow2];
