/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/
// eslint-disable-next-line import/no-extraneous-dependencies
import Mock from 'mockjs';

// 生成水厂和泵站功率的模拟数据
export const mockPowerData = Mock.mock({
  '水厂功率|100-500': 0,
  '泵站功率|100-500': 0,
});

// 生成水厂和泵站效率的模拟数据
export const mockEfficiencyData = Mock.mock({
  '效率|60-90': 0,
});

const generatePlantData = () => {
  const data = [];
  for (let i = 1; i <= 10; i += 1) {
    const plant = Mock.mock({
      plantName: `水厂${i}`,
      'power|100-500': 0,
      'efficiency|60-90': 0,
    });
    data.push(plant);
  }
  return data;
};

const generatePumpData = () => {
  const data = [];
  for (let i = 1; i <= 10; i += 1) {
    const pump = Mock.mock({
      plantName: `泵站${i}`,
      'power|100-500': 0,
      'efficiency|60-90': 0,
    });
    data.push(pump);
  }
  return data;
};

export const mockPlantData = generatePlantData();
export const mockPumpData = generatePumpData();
