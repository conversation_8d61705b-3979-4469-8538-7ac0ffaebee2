/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable import/no-extraneous-dependencies */
import dayjs from 'dayjs';
import Mock from 'mockjs';

function generateEventTimes(startTime: dayjs.Dayjs, endTime: dayjs.Dayjs) {
  // 转换 dayjs 对象为时间戳
  const startTimestamp = startTime.valueOf();
  const endTimestamp = endTime.valueOf();

  // 生成的开始时间在指定的时间范围内
  const eventStartTimestamp = Mock.Random.integer(startTimestamp, endTimestamp);
  const eventStartTime = dayjs(eventStartTimestamp).format(
    'YYYY-MM-DD HH:mm:ss',
  );

  // 生成的结束时间在开始时间之后，且在指定的时间范围内
  const eventEndTimestamp = Mock.Random.integer(
    eventStartTimestamp,
    endTimestamp,
  );
  const eventEndTime = dayjs(eventEndTimestamp).format('YYYY-MM-DD HH:mm:ss');

  return {
    eventStartTime,
    eventEndTime,
  };
}

export function mockEventSchedulingData(
  startTime: dayjs.Dayjs,
  endTime: dayjs.Dayjs,
  numberOfEvents: number = 5,
) {
  const events = Array.from({ length: numberOfEvents }, () => {
    const { eventStartTime, eventEndTime } = generateEventTimes(
      startTime,
      endTime,
    );

    return Mock.mock({
      eventId: '@increment',
      eventTitle: '@title(5, 10)',
      eventStatus: '@pick(["DONE", "DOING", "PLANNING"])',
      eventType: '@word',
      eventSubType: '@word',
      eventAddress: '@county(true)',
      eventDescription: '@sentence(10, 20)',
      operator: '@name',
      createTime: '@datetime("YYYY-MM-DD HH:mm:ss")',
      isOnTimeline: '@boolean',
      isOnChart: '@boolean',
      oname: '@word',
      otype: '@word',
      shape: '@word',
      eventStartTime,
      eventEndTime,
    });
  });

  return { events };
}
