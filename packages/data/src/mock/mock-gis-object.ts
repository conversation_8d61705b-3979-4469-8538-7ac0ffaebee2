/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import GisObject, { convertGeometryToShape } from '../gis-object';

const gisPipeData = {
  layerId: '62',
  layerName: '管线',
  displayFieldName: 'S_PROJECT_NAME',
  value: '121059',
  attributes: {
    管道长度: '218.388484',
    管线标识码: 'Null',
    管线等级: '市政管线',
    起点管中埋深: '0',
    终点管中埋深: '0',
    起点管外顶标高: '0',
    终点管外顶标高: '0',
    所在道路名称: 'Null',
    建设日期: '1899/11/30',
    建设单位: 'Null',
    勘测单位: 'Null',
    勘测日期: '2017/8/15',
    设计单位: 'Null',
    施工单位: 'Null',
    竣工日期: 'Null',
    数据来源: '施工现场',
    填报人员: '黄海英',
    填报日期: '2019/10/8',
    更新年代: '0',
    备注: '根据福州主城区与马尾区供水干管连接线工程总承包将旧管线报废。',
    工程编号: 'Null',
    工程名称: 'Null',
    管线材质: '球墨铸铁',
    管线口径: '300',
    设施状态: 'Null',
    所属区县: 'Null',
    起点编码: 'Null',
    终点编码: 'Null',
    GUI: '61a5535f-a8da-4caa-af09-caffb683eb97',
    OBJECTID: '121059',
    SHAPE: 'Polyline',
    设施情况描述: 'Null',
    一级分区编号: 'Null',
    SHAPE_Length: '218.388484',
  },
  geometryType: 'esriGeometryPolyline',
  geometry: {
    paths: [
      [
        [436881.78500000015, 2881081.6500000004],
        [436721.5580000002, 2881230.0441999994],
      ],
    ],
    spatialReference: {
      wkid: 4549,
      latestWkid: 4549,
    },
  },
};

const gisValveData = {
  layerId: '13',
  layerName: '阀门',
  displayFieldName: 'S_PROJECT_ID',
  value: '37765',
  attributes: {
    阀门等级: '市政',
    坐标X: '436842.0645',
    坐标Y: '2881127.3818',
    阀门标识码: 'F336246',
    地面高程: '7.2',
    所在道路名称: 'Null',
    建设日期: '2018/12/1',
    建设单位: '福州马尾区住建局',
    勘测日期: '1899/11/30',
    勘测单位: 'Null',
    定位信息: 'Null',
    数据来源: '设计图',
    填报人员: '翁文健',
    填报日期: '2022/4/24',
    填报单位: 'Null',
    设计单位: 'Null',
    施工单位: 'Null',
    竣工日期: 'Null',
    备注: 'Null',
    工作状态: '关闭',
    阀门用途: '泄水',
    阀门类型: '闸阀',
    阀门口径: '200',
    埋设方式: '阀门井',
    阀门地址: 'Null',
    所属区县: 'Null',
    工程编号: 'Null',
    工程名称: 'Null',
    设施状态: 'Null',
    OBJECTID: '37765',
    GUI: '81860387-D33F-498D-B490-85E16068FE2B',
    SHAPE: 'Point',
    符号角度: 'Null',
    所属区县编码: '3',
    一级分区编号: '3',
    阀门标识码附属: 'Null',
    testOID: '46624',
    阀门井盖类型: 'Null',
    管外顶标高: '6.18',
    管中埋深: '1.1',
    管径: 'Null',
    更新年代: 'Null',
  },
  geometryType: 'esriGeometryPoint',
  geometry: {
    x: 436842.0645000003,
    y: 2881127.3817999996,
    spatialReference: {
      wkid: 4549,
      latestWkid: 4549,
    },
  },
};

const gisNoShape = {
  layerId: '13',
  layerName: '阀门',
  displayFieldName: 'S_PROJECT_ID',
  value: '37765',
  attributes: {
    阀门等级: '市政',
    OBJECTID: '37765',
    GUI: '81860387-D33F-498D-B490-85E16068FE2B',
    SHAPE: 'Point',
    符号角度: 'Null',
  },
  geometryType: 'invalid_shapeType',
  geometry: {
    x: 436842.0645000003,
    y: 2881127.3817999996,
    spatialReference: {
      wkid: 4549,
      latestWkid: 4549,
    },
  },
};

const gisDMAData = {
  layerId: 146,
  layerName: '一级分区（不透明）',
  displayFieldName: 'YJFQBH',
  value: '3',
  attributes: {
    OBJECTID: '4',
    Shape: 'Polygon',
    一级分区编号: '3',
    备注: 'Null',
    一级分区名称: '三分区',
    SHAPE_Length: '56939.74353',
    SHAPE_Area: '78621526.030053',
  },
  geometryType: 'esriGeometryPolygon',
  geometry: {
    rings: [
      [
        [436449.32710000034, 2889945.1435000002],
        [436717.20830000006, 2888614.386499999],
        [436747.8359000003, 2888462.2377000004],
        [437010.6557, 2887385.1357000005],
        [438926.7701000003, 2888358.9702000003],
        [440370.7726999996, 2887143.7205],
        [439999.0493000001, 2886786.2940999996],
        [437082.3838999998, 2886786.466],
        [437082.33579999954, 2885304.2334000003],
        [437646.92819999997, 2884867.1653000005],
        [437646.9193000002, 2884592.2445],
        [437273.6222000001, 2884244.2687],
        [437559.94010000024, 2884057.9372000005],
        [437412.6255999999, 2883323.4135],
        [437260.7708999999, 2882820.7753999997],
        [436835.47750000004, 2882078.2284999993],
        [438035.1794999996, 2880727.0485999994],
        [437909.50210000016, 2880369.8604000006],
        [437500.14350000024, 2880217.3528000005],
        [437385.8492999999, 2880318.8885999992],
        [436362.43290000036, 2881228.0632000007],
        [436151.39329999965, 2881349.834899999],
        [436419.7505000001, 2881848.4728999995],
        [436083.39969999995, 2882385.2558999993],
        [435889.926, 2883289.9542999994],
        [435689.9221999999, 2883525.4091999996],
        [435489.9182000002, 2883760.864],
        [434875.6012000004, 2883889.3069],
        [434303.2034999998, 2883978.5164],
        [433885.9316999996, 2884043.5492000002],
        [433479.0987999998, 2884106.955],
        [432903.2111999998, 2884111.1724999994],
        [431835.0100999996, 2884118.995100001],
        [431778.3827999998, 2884737.2994],
        [431767.6361999996, 2884874.5864000004],
        [431745.1162999999, 2885100.5305000003],
        [431682.6854999997, 2885726.9046],
        [431511.1255999999, 2886296.5875000004],
        [431473.4446999999, 2886359.7084],
        [431225.6528000003, 2886774.7957000006],
        [431171.2252000002, 2887116.1745999996],
        [431110.75, 2887495.4846],
        [431080.62569999974, 2887684.4289999995],
        [431070.27190000005, 2887749.3702000007],
        [431065.67619999964, 2887778.1948000006],
        [431064.98039999977, 2887782.5593],
        [431054.46889999975, 2887840.4109000005],
        [431054.4648000002, 2887840.433599999],
        [431042.0554999998, 2887908.729699999],
        [431035.6469999999, 2887944.0002999995],
        [430993.2977, 2888177.0756],
        [430815.38879999984, 2888606.1906000003],
        [430707.6001000004, 2888877.4264],
        [430664.5432000002, 2888985.773499999],
        [430607.07100000046, 2889130.3947],
        [430523.1350999996, 2889341.6078999992],
        [430435.74590000045, 2889589.3049],
        [430318.9484000001, 2889818.9333999995],
        [430305.8443, 2889864.2081000004],
        [430294.17870000005, 2889904.5122999996],
        [430272.5462999996, 2889979.2522],
        [430260.11000000034, 2890022.2192],
        [430255.38549999986, 2890036.6819],
        [430237.73709999956, 2890090.708799999],
        [430215.0089999996, 2890160.2858000007],
        [430205.2927000001, 2890190.030099999],
        [430201.6676000003, 2890315.4104999993],
        [430286.9600999998, 2890503.6766999997],
        [428333.9023000002, 2891110.7623999994],
        [426911.1553999996, 2890950.2748000007],
        [427347.5195000004, 2891831.9323999994],
        [427408.0903000003, 2891954.3134000003],
        [427864.48539999966, 2892558.3428000007],
        [427639.6182000004, 2892870.019099999],
        [427587.6935999999, 2893507.4981999993],
        [427594.27020000014, 2893546.9499999993],
        [427608.79530000035, 2893603.1680999994],
        [427608.79640000034, 2893603.1728000008],
        [427652.29590000026, 2893771.5349000003],
        [428980.0538999997, 2896044.4189999998],
        [429684.96690000035, 2896552.432],
        [430891.5603, 2896977.8773],
        [431202.7196000004, 2896628.5979999993],
        [431151.8970999997, 2896038.0132],
        [430179.7748999996, 2894464.5617999993],
        [430978.8690999998, 2894459.249500001],
        [431637.32550000027, 2894650.179300001],
        [432259.66899999976, 2894726.3680000007],
        [432894.70990000013, 2894694.5999999996],
        [433472.7045999998, 2894589.698000001],
        [433890.77880000044, 2894743.155099999],
        [434445.81799999997, 2894135.9661999997],
        [434782.36870000046, 2893348.5646],
        [435341.16980000027, 2890592.6591],
        [436449.32710000034, 2889945.1435000002],
      ],
    ],
    spatialReference: {
      wkid: 4549,
      latestWkid: 4549,
    },
  },
};

function getMockGisObject(gisData: any): GisObject {
  const { layerId, layerName, geometryType, geometry, value } = gisData;

  const shape = convertGeometryToShape(geometryType, geometry);
  const attributes: Array<[string, any]> = Object.entries(gisData.attributes);

  const gisObject: GisObject = new GisObject(
    layerId,
    layerName,
    gisData.attributes.OBJECTID ?? value,
    shape || '',
    attributes,
  );

  return gisObject;
}

export const mockGisPipe = getMockGisObject(gisPipeData);
export const mockGisValve = getMockGisObject(gisValveData);
export const mockGisDMA = getMockGisObject(gisDMAData);
export const mockGisNoShape = getMockGisObject(gisNoShape);
