/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const displayLayers = [
  { class_name: 'GisMapLayerBaidu', args: { layer_name: 'ditu' } },
  {
    class_name: 'GisMapLayerMap',
    args: { layer_name: 'GIS_SERVICE', type: 'WMTS' },
  },
  { class_name: 'GisMapLayerNetwork', args: { layer_name: 'WDM_PIPES' } },
  { class_name: 'GisMapLayerNetwork1', args: { layer_name: 'WDM_PIPES1' } }, // invalid case
  {
    class_name: 'GisMapLayerFeatureMapSDSTATION',
    args: { layer_name: 'SDFOLD_DEV_PUMP_ROOT' },
  },
  {
    class_name: 'GisMapLayerFeatureMapFACTORY',
    args: { layer_name: 'SDFOLD_NETWORK_RAIN' },
  },
];

export const layerDetails = [
  {
    name: 'BAIDU_MAP',
    group: 'STREET_MAP',
    title: '地图',
    layer_name: 'ditu',
    icon: null,
  },
  {
    name: 'WDM_PIPES',
    group: 'LINK',
    title: '管道',
    layer_name: 'WDM_PIPES',
    icon: '\\ue691',
  },
  {
    name: 'WDM_PIPES1',
    group: 'LINK',
    title: '管道',
    layer_name: 'WDM_PIPES1',
    icon: '\\ue691',
  },
  {
    name: 'SDFOLD_STATION_WATER_ROOT',
    group: 'CLASSIFY',
    title: '泵站',
    layer_name: 'SDFOLD_STATION_WATER_ROOT',
    icon: null,
  },
  {
    name: 'SDFOLD_NETWORK_RAIN',
    group: 'CLASSIFY',
    title: '水厂',
    layer_name: 'SDFOLD_NETWORK_RAIN',
    icon: null,
  },
  {
    name: 'SDFOLD_NETWORK_RAIN2',
    group: 'CLASSIFY',
    title: '水厂2',
    layer_name: 'SDFOLD_NETWORK_RAIN',
    icon: null,
  },
  {
    name: 'SDFOLD_DEV_PUMP_ROOT',
    group: 'CLASSIFY',
    title: '泵站监测',
    layer_name: 'SDFOLD_DEV_PUMP_ROOT',
    icon: null,
  },
  {
    // duplicate layer
    name: 'SDFOLD_DEV_PUMP_ROOT',
    group: 'CLASSIFY',
    title: null,
    layer_name: 'SDFOLD_DEV_PUMP_ROOT',
    icon: null,
  },
  {
    name: 'GIS_SERVICE',
    group: 'STREET_MAP',
    title: 'GIS地图',
    layer_name: 'GIS_SERVICE',
    icon: null,
  },
];

export const mockGisMapDataArgs = {
  layers: [
    {
      theme: 'default',
      type: 'ImageArcGISRest',
      params: {
        access_token: 'AT-284-5KZq3-faDAE8vKStzxqM3WyLUTFfN8PW',
        layers:
          'show:1,4,5,6,7,11,12,14,15,16,17,18,19,20,21,23,24,25,26,27,28,29,30,31',
      },
      projection: 'EPSG:4549',
      url: 'http://220.250.29.188:64080/onemap/rest/services/FZDT_Basemap_Q/MapServer',
      opacity: 0.5,
    },
    {
      theme: 'dark',
      type: 'WMTS',
      url: 'http://168.1.9.73:6080/arcgis/rest/services/FZDT/FZ_Basemap_cache6/MapServer/WMTS',
      matrixSet: 'default028mm',
      projection: 'EPSG:4549',
      layer: 'FZDT_FZ_Basemap_cache6',
      style: 'default',
      version: '1.0.0',
      format: 'image/png',
      tileGrid: {
        origin: [-5123200.0, 1.00021e7],
        matrixIds: [
          '0',
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
        ],
        resolutions: [
          33.86673440013547, 16.933367200067735, 8.466683600033868,
          4.233341800016934, 2.116670900008467, 1.0583354500042335,
          0.5291677250021167, 0.26458386250105836, 0.13229193125052918,
          0.06614596562526459, 0.033072982812632296, 0.016536491406316148,
        ],
      },
      extent: [
        323975.47579999897, 2784850.0834, 520304.97890000156, 2959484.2218,
      ],
    },
  ],
};
