/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupData, LegendGroupDataCollection } from '../legend-data';

export const legendData = {
  DEV_FLOW: [
    {
      title: '当前数据时效',
      icon: '\\ue68f',
      legend: 'DEV_TIMELY#DEV_TIMELY#AA',
      legend_count: true,
      values: [
        {
          color: 'FF1492CC',
          value: '5',
          title: '<5分钟',
          legend_id: 9,
          legend_count: 235,
          legend_hidden: true,
        },
        {
          color: 'FF008000',
          value: '30',
          title: '5-30分钟',
          legend_id: 8,
          legend_count: 45,
        },
        {
          color: 'FFF5B533',
          value: '60',
          title: '30-60分钟',
          legend_id: 7,
          legend_count: 2,
        },
        {
          color: 'FFED3434',
          value: '360',
          title: '1-6小时',
          legend_id: 6,
          legend_count: 182,
        },
        {
          color: 'FF827E79',
          title: '≥6小时',
          legend_id: 10,
          legend_count: 230,
        },
      ],
    },
    {
      title: '设备类型',
      legend: 'DEV_TIMELY#DEV_TYPE#DEVICE_TYPE',
      legend_count: true,
      values: [
        {
          color: '',
          value: 'DEV_PF',
          size: '1',
          title: '压流一体',
          legend_id: 1,
          legend_count: 555,
        },
        {
          color: '',
          value: 'DEV_FLOW_W',
          size: '2',
          title: '测流点',
          legend_id: 2,
          legend_count: 38,
        },
        {
          color: '',
          value: 'DEV_PRESSURE_W',
          size: '2',
          title: '测压点',
          legend_id: 3,
          legend_count: 37,
        },
        {
          color: '',
          value: 'DEV_QUALITY_W',
          size: '2',
          title: '水质',
          legend_id: 4,
          legend_count: 60,
        },
        {
          color: '',
          size: '2',
          title: '未定义',
          legend_id: 5,
          legend_count: 9,
        },
      ],
    },
  ],
  WDM_JUNCTIONS: [
    {
      title: '小区/节点压力',
      icon: '\\ue68f',
      legend: 'PIPES_FLOW#JUNCTION_PRESS_RATIO#PRESSURE',
      values: [
        {
          color: 'AFED3434',
          value: '18',
          title: '<14m',
          legend_id: 1,
        },
        {
          color: 'AFF5B533',
          value: '22',
          title: '14-20',
          legend_id: 2,
        },
        {
          color: 'AF008000',
          value: '26',
          title: '20-25',
          legend_id: 5,
        },
        {
          color: 'AF1492CC',
          value: '30',
          title: '25-30',
          legend_id: 6,
        },
        {
          color: 'AF0000FF',
          title: '≥30',
          legend_id: 4,
        },
      ],
    },
  ],
  WDM_PIPES: [
    {
      title: '管道流量m3/h',
      icon: '\\ue7fd',
      legend: 'PIPES_FLOW#PIPES_FLOW#FLOW',
      values: [
        {
          color: 'FFADD8E6',
          value: '50',
          title: '<50',
          legend_id: 11,
        },
        {
          color: 'FF00BFFF',
          value: '200',
          title: '50-200',
          legend_id: 8,
        },
        {
          color: 'FF6495ED',
          value: '500',
          title: '200-500',
          legend_id: 9,
        },
        {
          color: 'FF4169E1',
          value: '800',
          title: '500-800',
          legend_id: 6,
        },
        {
          color: 'FF0000FF',
          title: '≥800',
          legend_id: 7,
        },
      ],
    },
    {
      title: '管径(mm)',
      legend: 'PIPES_FLOW#PIPES_DIANETER#GEOM',
      values: [
        {
          color: 'FFFF0000',
          value: '151',
          size: '1',
          title: '≤DN150',
          legend_id: 4,
        },
        {
          color: 'FFFFFF00',
          value: '301',
          size: '2',
          title: 'DN150～300',
          legend_id: 5,
        },
        {
          color: 'FF00FF00',
          value: '501',
          size: '3',
          title: 'DN300～500',
          legend_id: 2,
        },
        {
          color: 'FF00FF00',
          value: '1001',
          size: '4',
          title: 'DN500～1000',
          legend_id: 3,
        },
        {
          color: 'FF00FFFF',
          size: '4',
          title: '大于DN1000',
          legend_id: 1,
        },
      ],
    },
  ],
};

export const themeData = {
  SDFOLD_FACT: {
    XQSC: {
      style: null,
      size: null,
      color: '    NULL',
    },
  },
};

export const invalidLegendData = {
  DEV_FLOW: [
    // legend is null
    {
      title: 'legend是null',
      icon: '\\ue68f',
      legend: null,
      legend_count: true,
      values: [
        {
          color: '',
          size: '1',
          title: 'legend is null',
          legend_id: null,
          legend_count: 555,
        },
        {
          color: '',
          size: '1',
          title: 'no value',
          legend_id: null,
          legend_count: 555,
        },
        // no title
        {
          color: '',
          size: '1',
          legend_id: null,
          legend_count: 555,
        },
        // count is null
        {
          color: '',
          size: '1',
          legend_id: null,
          legend_count: null,
        },
      ],
    },
    // legendDataItem is empty
    {},
    // values is null
    {
      values: null,
    },
    {
      title: '设备类型',
      legend: 'DEV_TIMELY#DEV_TYPE#DEVICE_TYPE',
      legend_count: true,
      values: [
        // legend_id is null
        {
          color: '',
          value: 'DEV_PF',
          size: '1',
          title: 'legend_id 是 null',
          legend_id: null,
          legend_count: 555,
        },
        // color not is string
        {
          color: 222,
          value: 'DEV_PF',
          size: '1',
          title: 'color不是字符串',
          legend_id: null,
          legend_count: 555,
        },
        // color is empty string
        {
          color: '',
          value: 'DEV_PF',
          size: '1',
          title: 'color是空的字符串',
          legend_id: null,
          legend_count: 555,
        },
      ],
    },
  ],
  // legendDataItem is null
  DEV_PUMP_OTHER: null,
};

export const mockLegendGroupData = (): LegendGroupData[] => {
  const legend: LegendGroupDataCollection = new LegendGroupDataCollection();
  legend.initialize(legendData, themeData);
  return legend.legendDataCollection;
};

export const mockInvalidLegendGroupData = (): LegendGroupData[] => {
  const legend: LegendGroupDataCollection = new LegendGroupDataCollection();
  legend.initialize(invalidLegendData, themeData);
  return legend.legendDataCollection;
};
