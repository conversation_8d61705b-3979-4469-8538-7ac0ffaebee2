/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import ModelObject from '../model-object';

export const mockModelPipe: ModelObject = new ModelObject(
  'WDM_PIPES',
  'P00001',
  'LINESTRING(436881.78500000015 2881081.6500000004,436721.5580000002 2881230.0441999994)',
);

export const mockModelJunction: ModelObject = new ModelObject(
  'WDM_JUNCTIONS',
  'J00001',
  'POINT(436881.78500000015 2881081.6500000004)',
);

export const mockModelValve: ModelObject = new ModelObject(
  'WDM_VALVES',
  'J00001',
  'POINT(436881.78500000015 2881081.6500000004)',
);

export const mockModelDMA: ModelObject = new ModelObject(
  'WDM_DMA',
  'DMA00001',
  'POLYGON(436881.78500000015 2881081.6500000004,436721.5580000002 2881230.0441999994,436881.78500000015 2881081.6500000004)',
);

export const mockModelUnknownShape: ModelObject = new ModelObject(
  'WDM_VALVES',
  'J00001',
  'MULTIPOINT(436881.78500000015 2881081.6500000004)',
);
