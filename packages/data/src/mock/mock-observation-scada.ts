/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ObservationScadaItem,
  ObservationType,
} from '../observation-scada-data';

export const mockObservationScadaList: ObservationScadaItem[] = [
  {
    id: 'd7971a9225ba434da3775d2a10ba06ba',
    otype: 'SDVAL_FLOW_W',
    oname: 'D_GW_10E',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: '南厂高区瞬时流',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: true,
    createTime: '2023-06-06 14:18:38',
    updateTime: '2023-06-06 18:09:18',
    name: '南厂高区瞬时流',
    unit: 'm³/h',
  },
  {
    id: '060776043d7a42b49aad6304e96e5b93',
    otype: 'SDVAL_FLOW_W',
    oname: 'DNSC13',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: '东南厂连江出厂水瞬时流量',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: true,
    createTime: '2023-06-06 14:18:27',
    updateTime: '2023-06-06 18:09:18',
    name: '东南厂连江出厂水瞬时流量',
    unit: 'm³/h',
  },
  {
    id: '763bff320fc2445d921681ee439db127',
    otype: 'SDVAL_FLOW_W',
    oname: 'D_YXZYZ_01_BZ',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: '义序出水流量',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: true,
    createTime: '2023-06-06 11:29:13',
    updateTime: '2023-06-06 11:29:13',
    name: '义序出水流量',
    unit: 'm³/h',
  },
  {
    id: 'baa87fe27e3c4e2589c4badec1d66b12',
    otype: 'SDVAL_FLOW_W',
    oname: 'D_FFS_GPLL',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: '飞凤山二泵冠浦瞬时流量',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: false,
    createTime: '2023-06-06 14:18:07',
    updateTime: '2023-06-06 14:18:07',
    name: '飞凤山二泵冠浦瞬时流量',
    unit: 'm³/h',
  },
  {
    id: 'f19aec1e0acd4138a075a620df37d056',
    otype: 'SDVAL_FLOW_W',
    oname: 'D_GW_10E',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: '南厂高区瞬时流',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: false,
    createTime: '2023-06-06 14:55:11',
    updateTime: '2023-06-06 14:55:11',
    name: '南厂高区瞬时流',
    unit: 'm³/h',
  },
  {
    id: 'ca03cfa5a0a64844ba0576cfa56db29a',
    otype: 'SDVAL_FLOW_W',
    oname: 'D_KP_09',
    vprop: 'SDVAL',
    type: ObservationType.SCADA,
    description: 'test',
    observationValues: [
      {
        id: 'd7971a9225ba434da3775d2a10ba06ba',
        max: 1000,
        min: 100,
        startTime: '01:00',
        endTime: '02:00',
      },
    ],
    isPublic: true,
    createTime: '2023-06-06 14:18:20',
    updateTime: '2023-06-06 15:42:41',
    name: '柯坪原水流量',
    unit: 'm³/h',
  },
];
