/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { TimeData } from '../time-data';

export const mockXQSCHead: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 32.496,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 32.7,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 32.598,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 32.904,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 32.904,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 33.108,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 33.312,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 33.516,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 33.516,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 33.618,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 33.516,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 33.516,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 33.72,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 33.924,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 33.72,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 34.23,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 34.23,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 34.23,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 34.23,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 34.332,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 33.924,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 33.924,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 33.924,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 34.128,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 34.638,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 34.638,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 34.638,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 34.638,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 34.638,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 34.434,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 34.536,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 34.026,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 33.822,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 33.618,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 33.516,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 33.618,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 33.618,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 33.21,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 32.904,
  },
];

export const mockXQSCFlow: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 13474.737111,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 12791.579204,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 12802.10552,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 12953.684469,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 12681.052885,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 12778.947624,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 12784.210782,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 12584.210778,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 12483.158144,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 12505.263408,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 12573.684462,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 12395.789722,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 12328.421299,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 12147.368664,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 12153.684454,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 11975.789713,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 11541.052863,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 11747.368656,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 11785.263393,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 11424.210755,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 11291.579173,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 11417.894965,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 11158.947591,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 11201.052856,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 11412.631807,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 11488.421283,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 11411.579175,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 11254.737067,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 11261.052857,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 11120.000223,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 10920.000219,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 11053.684431,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 10970.526536,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 10933.684429,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 10894.73706,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 10660.000213,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 10596.842317,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 10597.894949,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 10551.579158,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 10607.368633,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 10443.158103,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 10265.263363,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 10382.105471,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 10460.00021,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 10548.421263,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 10635.789686,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 10496.842315,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 10197.894941,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 10475.789683,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 10517.894947,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 10385.263366,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 10363.158102,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 10403.158103,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 10477.894946,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 10421.05284,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 10410.526524,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 10494.737052,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 10583.158106,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 10350.526523,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 10464.210735,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 10718.947583,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 10758.947583,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 10756.84232,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 10632.631792,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 10466.315999,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 10810.526532,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 10852.631796,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 11661.052865,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 11161.052854,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 11562.105495,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 11238.947593,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 11484.210756,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 11523.158125,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 11857.894974,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 11974.737082,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 12235.789719,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 12457.894986,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 12856.842362,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 12335.789721,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 12344.210773,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 12604.210779,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 12936.842364,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 13605.26343,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 14213.684495,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 14653.684504,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 14565.263449,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 15636.842418,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 15165.263461,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 15060.000301,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 15214.737147,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 15976.842424,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 15524.210837,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 16245.263483,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 16485.263488,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 17068.421394,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 16832.631916,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 16782.105599,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 17292.631925,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 18355.789841,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 16993.684551,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 17920.000358,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 17591.579299,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 17456.842454,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 18037.895098,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 18233.684575,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 18248.421417,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 18787.368796,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 17962.105623,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 18153.684573,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 18260.000365,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 18660.000373,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 18492.631949,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 18518.947739,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 18848.42143,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 18958.947748,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 18936.842484,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 18987.368801,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 18370.526684,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 18268.421418,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 18528.421423,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 18050.526677,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 17902.105622,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 17833.684568,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 18208.421417,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 18084.210888,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 17562.105615,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 18089.474046,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 17710.52667,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 17784.210882,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 18197.895101,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 18243.15826,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 18209.474048,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 17834.737199,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 18766.316165,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 18021.052992,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 18256.84247,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 18329.474051,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 18223.158259,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 18288.421419,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 18313.684577,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 18093.684573,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 18388.421421,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 17956.842464,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 18258.947734,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 18225.263522,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 17875.789831,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 17807.368777,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 17910.526674,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 18383.158262,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 17853.684568,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 18381.052999,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 18653.684583,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 18808.421429,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 17929.474043,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 18063.158256,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 18322.105629,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 17707.368775,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 17272.631925,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 17887.368779,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 18038.94773,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 17340.000346,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 17807.368777,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 18183.158258,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 17508.421403,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 17448.421401,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 17501.052982,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 16830.526652,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 17347.368768,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 16855.789811,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 17186.316133,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 17037.895077,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 16758.947703,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 16724.210861,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 17087.368763,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 16777.895073,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 16021.052952,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 15982.105583,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 15870.526633,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 16461.052961,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 16255.789799,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 16170.526639,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 16344.210853,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 16188.421377,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 16683.158229,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 16195.789798,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 16317.895064,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 16383.158223,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 16762.105598,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 16372.631906,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 16341.052958,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 15908.421371,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 16443.158223,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 16640.000333,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 16578.9477,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 16878.947706,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 17169.474027,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 16762.105599,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 16601.052964,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 16697.895071,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 16600.000332,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 16677.895071,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 16694.737176,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 16802.105599,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 16584.210858,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 16741.052967,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 17013.684551,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 16432.631908,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 17263.15824,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 17286.316136,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 17574.737194,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 17289.47403,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 17333.684557,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 17270.526661,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 17314.737189,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 17422.105612,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 17258.947713,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 18265.263523,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 18025.263519,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 18442.105632,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 18590.526687,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 18365.263525,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 18510.526686,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 18740.000375,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 18268.421418,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 18274.737207,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 18387.368789,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 18467.36879,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 18442.105632,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 18168.421416,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 18491.579317,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 18863.158272,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 18441.053001,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 18373.684578,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 19176.842489,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 18964.210905,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 18610.526688,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 18973.68459,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 19101.053013,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 19576.842497,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 18970.526695,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 19436.842494,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 19153.684593,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 18847.368798,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 19604.210919,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 19343.158282,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 19330.526703,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 18884.210904,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 19186.316174,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 19236.84249,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 18921.05301,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 19042.105644,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 19573.684602,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 19354.737229,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 19622.105656,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 19272.631965,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 20009.474084,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 19154.737225,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 19436.842494,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 19371.579335,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 19103.158277,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 19064.210908,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 19374.737229,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 19126.316172,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 18528.421423,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 18512.631949,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 18565.263529,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 17980.00036,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 19006.316169,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 18442.105632,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 18695.789848,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 18402.105631,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 18038.94773,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 18118.947731,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 17606.316142,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 17485.263507,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 17413.684559,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 17169.474028,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 17613.684563,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 17230.526661,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 17067.368762,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 16408.421381,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 15950.526634,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 15657.89505,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 15583.158207,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 15292.631884,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 15351.579254,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 15367.368728,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 14577.895028,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 13744.210801,
  },
];

function getPumpOffTimeData(date: string): TimeData[] {
  const timeData: TimeData[] = [];
  let startTime = dayjs(date).startOf('day');
  const endTime = dayjs(date).add(1, 'day').startOf('day');
  while (startTime.isBefore(endTime)) {
    timeData.push({ time: startTime.format('YYYY-MM-DD HH:mm:ss'), value: 0 });
    startTime = startTime.add(5, 'minute');
  }

  return timeData;
}

export const mockXQSCP1: TimeData[] = getPumpOffTimeData('2023-03-19');
export const mockXQSCP4: TimeData[] = getPumpOffTimeData('2023-03-19');
export const mockXQSCP5: TimeData[] = getPumpOffTimeData('2023-03-19');
export const mockXQSCP6: TimeData[] = getPumpOffTimeData('2023-03-19');
export const mockXQSCP2: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 0.738,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 0.9,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 0.894,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 0.884,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 0.888,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 0.89,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 0.886,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 0.884,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 0.88,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 0.878,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 0.876,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 0.87,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 0.872,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 0.864,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 0.856,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 0.848,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 0.838,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 0.826,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 0.814333,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 0.824,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 0.822,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 0.818,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 0.824,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 0.818,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 0.816,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 0.808,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 0.794,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 0.808,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 0.806,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 0.808,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 0.816,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 0.82,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 0.87,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 0.878,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 0.894,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 0.872,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 0.88273,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 0.892,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 0.902,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 0.938,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 0.942,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 0.966,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 0.986,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 0.77,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 0.768,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 0.774,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 0.782,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 0.788,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 0.794,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 0.81,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 0.82,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 0.82,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 0.824,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 0.824,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 0.86,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 0.848,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 0.858,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 0.856,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 0.838,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 0.848,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 0.838,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 0.826,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 0.818,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 0.81,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 0.81,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 0.806,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 0.782,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 0.784,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 0.788,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 0.788,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 0.794,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 0.786,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 0.786,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 0.784,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 0.802,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 0.804,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 0.784,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 0.786,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 0.786,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 0.792,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 0.79,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 0.788,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 0.796,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 0.81,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 0.816,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 0.818,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 0.816,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 0.822,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 0.834,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 0.83,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 0.838,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 0.832,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 0.844,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 0.848,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 0.846,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 0.848,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 0.856,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 0.86,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 0.858,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 0.86,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 0.862,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 0.862,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 0.866,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 0.864,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 0.86,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 0.856,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 0.854,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 0.852,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 0.85,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 0.842,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 0.836,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 0.838,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 0.84,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 0.828,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 0.824,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 0.82,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 0.814,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 0.812,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 0.808,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 0.8,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 0.798,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 0.794,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 0.778,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 0.768,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 0.77,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 0.76,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 0.77,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 0.772698,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 0.776,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 0.76,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 0.73,
  },
];

export const mockXQSCP3: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 1,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 1,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 1,
  },
];

export const mockQTBZDQOP: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 78.921323,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 78.462754,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 79.021677,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 78.334,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 78.334,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 78.946,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 78.334,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 79.293803,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 78.182645,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 78.735311,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 79.313869,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 78.827548,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 79.253672,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 78.946,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 78.823934,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 78.436,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 78.844,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 77.98029,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 78.742,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 78.538,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 78.459032,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 79.033429,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 78.64,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 78.64,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 78.64,
  },
];

export const mockQTBZGQOP: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 96.898,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 97.102,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 97,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 97,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 97,
  },
];

export const mockQTBZIF: TimeData[] = [
  {
    time: '2023-03-19 00:00:00',
    value: 126.883,
  },
  {
    time: '2023-03-19 00:05:00',
    value: 126.139,
  },
  {
    time: '2023-03-19 00:10:00',
    value: 126.2,
  },
  {
    time: '2023-03-19 00:15:00',
    value: 126.291,
  },
  {
    time: '2023-03-19 00:20:00',
    value: 126.174,
  },
  {
    time: '2023-03-19 00:25:00',
    value: 125.63,
  },
  {
    time: '2023-03-19 00:30:00',
    value: 125.688,
  },
  {
    time: '2023-03-19 00:35:00',
    value: 125.923,
  },
  {
    time: '2023-03-19 00:40:00',
    value: 127.155,
  },
  {
    time: '2023-03-19 00:45:00',
    value: 125.583,
  },
  {
    time: '2023-03-19 00:50:00',
    value: 127.449,
  },
  {
    time: '2023-03-19 00:55:00',
    value: 126.615,
  },
  {
    time: '2023-03-19 01:00:00',
    value: 127.046,
  },
  {
    time: '2023-03-19 01:05:00',
    value: 126.464,
  },
  {
    time: '2023-03-19 01:10:00',
    value: 126.288,
  },
  {
    time: '2023-03-19 01:15:00',
    value: 126.138,
  },
  {
    time: '2023-03-19 01:20:00',
    value: 127.432,
  },
  {
    time: '2023-03-19 01:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 01:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 02:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 03:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 04:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 05:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 06:30:00',
    value: 73.989,
  },
  {
    time: '2023-03-19 06:35:00',
    value: 74.062,
  },
  {
    time: '2023-03-19 06:40:00',
    value: 74.89,
  },
  {
    time: '2023-03-19 06:45:00',
    value: 73.108,
  },
  {
    time: '2023-03-19 06:50:00',
    value: 74.067,
  },
  {
    time: '2023-03-19 06:55:00',
    value: 73.903,
  },
  {
    time: '2023-03-19 07:00:00',
    value: 73.665,
  },
  {
    time: '2023-03-19 07:05:00',
    value: 73.246,
  },
  {
    time: '2023-03-19 07:10:00',
    value: 73.006,
  },
  {
    time: '2023-03-19 07:15:00',
    value: 73.262,
  },
  {
    time: '2023-03-19 07:20:00',
    value: 73.022,
  },
  {
    time: '2023-03-19 07:25:00',
    value: 73.32,
  },
  {
    time: '2023-03-19 07:30:00',
    value: 73.509,
  },
  {
    time: '2023-03-19 07:35:00',
    value: 73.161,
  },
  {
    time: '2023-03-19 07:40:00',
    value: 73.042,
  },
  {
    time: '2023-03-19 07:45:00',
    value: 72.457,
  },
  {
    time: '2023-03-19 07:50:00',
    value: 72.438,
  },
  {
    time: '2023-03-19 07:55:00',
    value: 71.432,
  },
  {
    time: '2023-03-19 08:00:00',
    value: 72.064,
  },
  {
    time: '2023-03-19 08:05:00',
    value: 71.841,
  },
  {
    time: '2023-03-19 08:10:00',
    value: 71.102,
  },
  {
    time: '2023-03-19 08:15:00',
    value: 72.947,
  },
  {
    time: '2023-03-19 08:20:00',
    value: 71.965,
  },
  {
    time: '2023-03-19 08:25:00',
    value: 70.99,
  },
  {
    time: '2023-03-19 08:30:00',
    value: 71.68,
  },
  {
    time: '2023-03-19 08:35:00',
    value: 72.563,
  },
  {
    time: '2023-03-19 08:40:00',
    value: 71.615,
  },
  {
    time: '2023-03-19 08:45:00',
    value: 70.536,
  },
  {
    time: '2023-03-19 08:50:00',
    value: 72.559,
  },
  {
    time: '2023-03-19 08:55:00',
    value: 72.271,
  },
  {
    time: '2023-03-19 09:00:00',
    value: 71.382,
  },
  {
    time: '2023-03-19 09:05:00',
    value: 71.314,
  },
  {
    time: '2023-03-19 09:10:00',
    value: 71.764,
  },
  {
    time: '2023-03-19 09:15:00',
    value: 72.331,
  },
  {
    time: '2023-03-19 09:20:00',
    value: 71.695,
  },
  {
    time: '2023-03-19 09:25:00',
    value: 71.669,
  },
  {
    time: '2023-03-19 09:30:00',
    value: 71.988,
  },
  {
    time: '2023-03-19 09:35:00',
    value: 71.717,
  },
  {
    time: '2023-03-19 09:40:00',
    value: 71.717,
  },
  {
    time: '2023-03-19 09:45:00',
    value: 71.718,
  },
  {
    time: '2023-03-19 09:50:00',
    value: 71.896,
  },
  {
    time: '2023-03-19 09:55:00',
    value: 71.955,
  },
  {
    time: '2023-03-19 10:00:00',
    value: 71.157,
  },
  {
    time: '2023-03-19 10:05:00',
    value: 161.708,
  },
  {
    time: '2023-03-19 10:10:00',
    value: 162.992,
  },
  {
    time: '2023-03-19 10:15:00',
    value: 163.004,
  },
  {
    time: '2023-03-19 10:20:00',
    value: 162.325,
  },
  {
    time: '2023-03-19 10:25:00',
    value: 162.324,
  },
  {
    time: '2023-03-19 10:30:00',
    value: 162.196,
  },
  {
    time: '2023-03-19 10:35:00',
    value: 162.255,
  },
  {
    time: '2023-03-19 10:40:00',
    value: 164.083,
  },
  {
    time: '2023-03-19 10:45:00',
    value: 163.685,
  },
  {
    time: '2023-03-19 10:50:00',
    value: 162.848,
  },
  {
    time: '2023-03-19 10:55:00',
    value: 163.309,
  },
  {
    time: '2023-03-19 11:00:00',
    value: 163.622,
  },
  {
    time: '2023-03-19 11:05:00',
    value: 162.954,
  },
  {
    time: '2023-03-19 11:10:00',
    value: 130.44,
  },
  {
    time: '2023-03-19 11:15:00',
    value: 130.601,
  },
  {
    time: '2023-03-19 11:20:00',
    value: 130.088,
  },
  {
    time: '2023-03-19 11:25:00',
    value: 129.252,
  },
  {
    time: '2023-03-19 11:30:00',
    value: 131.467,
  },
  {
    time: '2023-03-19 11:35:00',
    value: 130.572,
  },
  {
    time: '2023-03-19 11:40:00',
    value: 130.34,
  },
  {
    time: '2023-03-19 11:45:00',
    value: 130.498,
  },
  {
    time: '2023-03-19 11:50:00',
    value: 131.128,
  },
  {
    time: '2023-03-19 11:55:00',
    value: 130.791,
  },
  {
    time: '2023-03-19 12:00:00',
    value: 131.38,
  },
  {
    time: '2023-03-19 12:05:00',
    value: 131.452,
  },
  {
    time: '2023-03-19 12:10:00',
    value: 130.534,
  },
  {
    time: '2023-03-19 12:15:00',
    value: 131.141,
  },
  {
    time: '2023-03-19 12:20:00',
    value: 130.225,
  },
  {
    time: '2023-03-19 12:25:00',
    value: 129.644,
  },
  {
    time: '2023-03-19 12:30:00',
    value: 129.683,
  },
  {
    time: '2023-03-19 12:35:00',
    value: 131.12,
  },
  {
    time: '2023-03-19 12:40:00',
    value: 131.789,
  },
  {
    time: '2023-03-19 12:45:00',
    value: 130.017,
  },
  {
    time: '2023-03-19 12:50:00',
    value: 130.91,
  },
  {
    time: '2023-03-19 12:55:00',
    value: 130.985,
  },
  {
    time: '2023-03-19 13:00:00',
    value: 126.479,
  },
  {
    time: '2023-03-19 13:05:00',
    value: 125.542,
  },
  {
    time: '2023-03-19 13:10:00',
    value: 124.949,
  },
  {
    time: '2023-03-19 13:15:00',
    value: 124.946,
  },
  {
    time: '2023-03-19 13:20:00',
    value: 125.245,
  },
  {
    time: '2023-03-19 13:25:00',
    value: 125.434,
  },
  {
    time: '2023-03-19 13:30:00',
    value: 124.852,
  },
  {
    time: '2023-03-19 13:35:00',
    value: 123.67,
  },
  {
    time: '2023-03-19 13:40:00',
    value: 125.443,
  },
  {
    time: '2023-03-19 13:45:00',
    value: 125.389,
  },
  {
    time: '2023-03-19 13:50:00',
    value: 124.634,
  },
  {
    time: '2023-03-19 13:55:00',
    value: 125.242,
  },
  {
    time: '2023-03-19 14:00:00',
    value: 126.641,
  },
  {
    time: '2023-03-19 14:05:00',
    value: 124.968,
  },
  {
    time: '2023-03-19 14:10:00',
    value: 125.409,
  },
  {
    time: '2023-03-19 14:15:00',
    value: 125.209,
  },
  {
    time: '2023-03-19 14:20:00',
    value: 125.292,
  },
  {
    time: '2023-03-19 14:25:00',
    value: 126.553,
  },
  {
    time: '2023-03-19 14:30:00',
    value: 126.45,
  },
  {
    time: '2023-03-19 14:35:00',
    value: 127.05,
  },
  {
    time: '2023-03-19 14:40:00',
    value: 126.268,
  },
  {
    time: '2023-03-19 14:45:00',
    value: 125.021,
  },
  {
    time: '2023-03-19 14:50:00',
    value: 125.108,
  },
  {
    time: '2023-03-19 14:55:00',
    value: 125.777,
  },
  {
    time: '2023-03-19 15:00:00',
    value: 126.308,
  },
  {
    time: '2023-03-19 15:05:00',
    value: 125.589,
  },
  {
    time: '2023-03-19 15:10:00',
    value: 125.365,
  },
  {
    time: '2023-03-19 15:15:00',
    value: 126.256,
  },
  {
    time: '2023-03-19 15:20:00',
    value: 125.568,
  },
  {
    time: '2023-03-19 15:25:00',
    value: 126.289,
  },
  {
    time: '2023-03-19 15:30:00',
    value: 126.252,
  },
  {
    time: '2023-03-19 15:35:00',
    value: 125.158,
  },
  {
    time: '2023-03-19 15:40:00',
    value: 125.15,
  },
  {
    time: '2023-03-19 15:45:00',
    value: 125.947,
  },
  {
    time: '2023-03-19 15:50:00',
    value: 125.629,
  },
  {
    time: '2023-03-19 15:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:35:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:40:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:45:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:50:00',
    value: 0,
  },
  {
    time: '2023-03-19 16:55:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:00:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:05:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:10:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:15:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:20:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:25:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:30:00',
    value: 0,
  },
  {
    time: '2023-03-19 17:35:00',
    value: 16.157,
  },
  {
    time: '2023-03-19 17:40:00',
    value: 74.994,
  },
  {
    time: '2023-03-19 17:45:00',
    value: 72.745,
  },
  {
    time: '2023-03-19 17:50:00',
    value: 72.051,
  },
  {
    time: '2023-03-19 17:55:00',
    value: 71.186,
  },
  {
    time: '2023-03-19 18:00:00',
    value: 72.988,
  },
  {
    time: '2023-03-19 18:05:00',
    value: 71.758,
  },
  {
    time: '2023-03-19 18:10:00',
    value: 71.66,
  },
  {
    time: '2023-03-19 18:15:00',
    value: 72.392,
  },
  {
    time: '2023-03-19 18:20:00',
    value: 72.387,
  },
  {
    time: '2023-03-19 18:25:00',
    value: 72.5,
  },
  {
    time: '2023-03-19 18:30:00',
    value: 71.799,
  },
  {
    time: '2023-03-19 18:35:00',
    value: 72.518,
  },
  {
    time: '2023-03-19 18:40:00',
    value: 71.729,
  },
  {
    time: '2023-03-19 18:45:00',
    value: 72.013,
  },
  {
    time: '2023-03-19 18:50:00',
    value: 72.62,
  },
  {
    time: '2023-03-19 18:55:00',
    value: 72.625,
  },
  {
    time: '2023-03-19 19:00:00',
    value: 71.605,
  },
  {
    time: '2023-03-19 19:05:00',
    value: 71.687,
  },
  {
    time: '2023-03-19 19:10:00',
    value: 72.627,
  },
  {
    time: '2023-03-19 19:15:00',
    value: 72.308,
  },
  {
    time: '2023-03-19 19:20:00',
    value: 72.219,
  },
  {
    time: '2023-03-19 19:25:00',
    value: 71.873,
  },
  {
    time: '2023-03-19 19:30:00',
    value: 72.389,
  },
  {
    time: '2023-03-19 19:35:00',
    value: 72.226,
  },
  {
    time: '2023-03-19 19:40:00',
    value: 71.063,
  },
  {
    time: '2023-03-19 19:45:00',
    value: 71.163,
  },
  {
    time: '2023-03-19 19:50:00',
    value: 71.546,
  },
  {
    time: '2023-03-19 19:55:00',
    value: 163.854,
  },
  {
    time: '2023-03-19 20:00:00',
    value: 163.787,
  },
  {
    time: '2023-03-19 20:05:00',
    value: 163.153,
  },
  {
    time: '2023-03-19 20:10:00',
    value: 161.989,
  },
  {
    time: '2023-03-19 20:15:00',
    value: 163.066,
  },
  {
    time: '2023-03-19 20:20:00',
    value: 161.857,
  },
  {
    time: '2023-03-19 20:25:00',
    value: 161.68,
  },
  {
    time: '2023-03-19 20:30:00',
    value: 161.231,
  },
  {
    time: '2023-03-19 20:35:00',
    value: 161.197,
  },
  {
    time: '2023-03-19 20:40:00',
    value: 160.576,
  },
  {
    time: '2023-03-19 20:45:00',
    value: 161.764,
  },
  {
    time: '2023-03-19 20:50:00',
    value: 161.25,
  },
  {
    time: '2023-03-19 20:55:00',
    value: 161.47,
  },
  {
    time: '2023-03-19 21:00:00',
    value: 160.746,
  },
  {
    time: '2023-03-19 21:05:00',
    value: 161.875,
  },
  {
    time: '2023-03-19 21:10:00',
    value: 160.346,
  },
  {
    time: '2023-03-19 21:15:00',
    value: 162.059,
  },
  {
    time: '2023-03-19 21:20:00',
    value: 161.558,
  },
  {
    time: '2023-03-19 21:25:00',
    value: 161.597,
  },
  {
    time: '2023-03-19 21:30:00',
    value: 161.451,
  },
  {
    time: '2023-03-19 21:35:00',
    value: 162.279,
  },
  {
    time: '2023-03-19 21:40:00',
    value: 162.068,
  },
  {
    time: '2023-03-19 21:45:00',
    value: 161.811,
  },
  {
    time: '2023-03-19 21:50:00',
    value: 130.347,
  },
  {
    time: '2023-03-19 21:55:00',
    value: 129.773,
  },
  {
    time: '2023-03-19 22:00:00',
    value: 129.558,
  },
  {
    time: '2023-03-19 22:05:00',
    value: 129.162,
  },
  {
    time: '2023-03-19 22:10:00',
    value: 130.456,
  },
  {
    time: '2023-03-19 22:15:00',
    value: 130.152,
  },
  {
    time: '2023-03-19 22:20:00',
    value: 128.862,
  },
  {
    time: '2023-03-19 22:25:00',
    value: 129.082,
  },
  {
    time: '2023-03-19 22:30:00',
    value: 130.809,
  },
  {
    time: '2023-03-19 22:35:00',
    value: 129.691,
  },
  {
    time: '2023-03-19 22:40:00',
    value: 130.869,
  },
  {
    time: '2023-03-19 22:45:00',
    value: 131.681,
  },
  {
    time: '2023-03-19 22:50:00',
    value: 131.287,
  },
  {
    time: '2023-03-19 22:55:00',
    value: 129.835,
  },
  {
    time: '2023-03-19 23:00:00',
    value: 127.899,
  },
  {
    time: '2023-03-19 23:05:00',
    value: 124.783,
  },
  {
    time: '2023-03-19 23:10:00',
    value: 126.63,
  },
  {
    time: '2023-03-19 23:15:00',
    value: 126.177,
  },
  {
    time: '2023-03-19 23:20:00',
    value: 125.571,
  },
  {
    time: '2023-03-19 23:25:00',
    value: 126.009,
  },
  {
    time: '2023-03-19 23:30:00',
    value: 126.316,
  },
  {
    time: '2023-03-19 23:35:00',
    value: 125.687,
  },
  {
    time: '2023-03-19 23:40:00',
    value: 125.889,
  },
  {
    time: '2023-03-19 23:45:00',
    value: 126.202,
  },
  {
    time: '2023-03-19 23:50:00',
    value: 126.657,
  },
  {
    time: '2023-03-19 23:55:00',
    value: 126.575,
  },
  {
    time: '2023-03-20 00:00:00',
    value: 126.01,
  },
];

export async function getMockPatternValues(
  _date: string,
  patterns: string[],
): Promise<Map<string, TimeData[]>> {
  const mockPatterns = new Map<string, TimeData[]>();
  mockPatterns.set('XQSC-HEAD', mockXQSCHead);
  mockPatterns.set('XQSC-Q', mockXQSCFlow);
  mockPatterns.set('XQSC-P1', mockXQSCP1);
  mockPatterns.set('XQSC-P2', mockXQSCP2);
  mockPatterns.set('XQSC-P3', mockXQSCP3);
  mockPatterns.set('XQSC-P4', mockXQSCP4);
  mockPatterns.set('XQSC-P5', mockXQSCP5);
  mockPatterns.set('XQSC-P6', mockXQSCP6);
  mockPatterns.set('QTBZDQ-OP', mockQTBZDQOP);
  mockPatterns.set('QTBZGQ-OP', mockQTBZGQOP);
  mockPatterns.set('QTBZ-IF', mockQTBZIF);

  const patternValues = new Map<string, TimeData[]>();
  patterns.forEach((item) => {
    const values = mockPatterns.get(item);
    if (values !== undefined) patternValues.set(item, values);
  });
  return patternValues;
}

export async function getMockPlantDayFlowValues(
  _date: string,
  _plantIds: string[],
): Promise<Map<string, number>> {
  const mockFlowValues = new Map<string, number>();
  mockFlowValues.set('CM-SC', 90472.04532891669);
  mockFlowValues.set('BQ-SC', 97195.66666666666);
  mockFlowValues.set('DN-SC', 53380.83333333333);
  mockFlowValues.set('DCSQ-SC', 252182.07786666666);
  mockFlowValues.set('DSH-SC', 64914.91357883332);
  mockFlowValues.set('XQ-SC', 297275.3568232498);
  mockFlowValues.set('FFS-SC', 111523.58333333334);

  return mockFlowValues;
}
