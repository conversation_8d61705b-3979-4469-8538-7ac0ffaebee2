/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const devLiquidPropViewData = [
  {
    type: 'button_prop',
    title: '监测指标',
    prop_list: [
      {
        type: 'quota',
        otype: 'SDVAL_LIQ',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_WATER_DEPTH',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'PIPE_FULL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'OVERLOAD',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'enumerateChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'IS_FULL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'enumerateChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
    ],
  },
  {
    type: 'craft_pic',
    title: '示意图',
    prop_list: [
      {
        type: 'quota',
        otype: 'SDVAL_WATER_DEPTH',
        vprop: 'WARNING_VALUE',
      },
    ],
  },
  {
    type: 'button_prop',
    title: '运行评估(昨日)',
    prop_list: [
      {
        type: 'prop',
        vprop: 'DAY_PIPE_FULL',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_OVERLOAD',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_HILOAD_TIME',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_MAX_DEPTH',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_MAX_FULL',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_AVG_FULL',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_FULL_TIME',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_OVER_COUNT',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'DAY_OVER_TIME',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
    ],
  },
  {
    type: 'button_prop',
    title: '设备评估(昨日)',
    prop_list: [
      {
        type: 'prop',
        vprop: 'RELIABILITY_S',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'INTEGRITY',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'TIMELINESS',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'STATE_DAYS_S',
      },
      {
        type: 'prop',
        vprop: 'BOTTLENECK_S',
      },
    ],
  },
  {
    type: 'quota_prop',
    title: '指标评估(昨日)',
    otype_list: [
      {
        otype: 'SDVAL_LIQ',
        prop_list: [
          {
            vprop: 'RELIABILITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'INTEGRITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'TIMELINESS',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
      {
        otype: 'SDVAL_WATER_DEPTH',
        prop_list: [
          {
            vprop: 'RELIABILITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'INTEGRITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'TIMELINESS',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
    ],
  },
  {
    type: 'base_prop',
    title: '基本属性',
    prop_list: [
      {
        type: 'prop',
        vprop: 'ONAME',
      },
      {
        type: 'prop',
        vprop: 'TITLE',
      },
      {
        type: 'prop',
        vprop: 'DEVICE_TYPE',
      },
      {
        type: 'prop',
        vprop: 'MONITOR_OBJECT',
      },
      {
        type: 'prop',
        vprop: 'JUNCTION',
        button: 'profileGraph',
      },
      {
        type: 'prop',
        vprop: 'DATASOURCE',
      },
      {
        type: 'prop',
        vprop: 'ORANIZATION',
      },
      {
        type: 'prop',
        vprop: 'CALIBER',
      },
      {
        type: 'prop',
        vprop: 'FACTORY',
      },
      {
        type: 'prop',
        vprop: 'ADDRESS',
      },
      {
        type: 'prop',
        vprop: 'GROUND_LEVEL',
      },
      {
        type: 'prop',
        vprop: 'WELL_DEPTH',
      },
      {
        type: 'prop',
        vprop: 'BOTTOM_ELEVATION',
      },
      {
        type: 'prop',
        vprop: 'SAMPLING_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'SEND_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'REMARK',
      },
    ],
  },
  {
    type: 'oname_pic',
    title: '设备图片',
  },
  {
    type: 'note_prop',
    title: '备注',
    prop_list: [
      {
        type: 'prop',
        vprop: 'REMARK',
      },
    ],
  },
  {
    type: 'correlation_prop',
    title: '设备相关',
    otype_list: [
      { otype: 'SDVAL_LIQ', prop_list: [] },
      { otype: 'SDVAL_FLOW_W', prop_list: [] },
    ],
  },
  {
    type: 'warn_list',
    title: '警告列表',
    otype_list: [],
  },
  {
    type: 'chart',
    title: '曲线',
    mode: 'child',
  },
  {
    type: 'buttons',
    buttons: [
      {
        type: 'operations',
        title: '操作记录',
      },
      {
        type: 'upstream',
        title: '来源追踪',
      },
      {
        type: 'downstream',
        title: '去向追踪',
      },
    ],
  },
];

export const sdvalLiqPropViewData = [
  {
    vprop: 'BOUND_MAX',
    title: '水位上限',
  },
  {
    vprop: 'RELIABILITY',
    title: '可靠性',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'INTEGRITY',
    title: '完整率',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'INTEGRITY',
  },
  {
    vprop: 'TIMELINESS',
    title: '及时率',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'TIMELINESS',
  },
  {
    vprop: 'SDHALT',
    title: '健康状况',
  },
  {
    vprop: 'BOTTLENECK',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'SCORE_UNCHANGE',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_UNCHANGE_S',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY_S',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_CONTINUITY_S',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'BOTTLENECK_S',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'SCORE_CONTINUITY',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'TITLE',
    title: '名称',
  },
  {
    vprop: 'LIMIT_MAX',
    title: '上限',
  },
  {
    vprop: 'SDVAL',
    title: '黄海高程',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'LEVEL',
  },
  {
    vprop: 'SDVAL_FC1',
    title: '黄海高程',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'LEVEL',
  },
  {
    vprop: 'ENVELOP_MIN',
    title: '包络线下限',
  },
  {
    vprop: 'ENVELOP_MAX',
    title: '包络线上限',
  },
  {
    vprop: 'FORECAST_VAL',
    title: '预测值',
  },
];

export const devLiquidPropsData = [
  {
    vprop: 'SCORE_UNCHANGE_S',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'RELIABILITY_S',
    title: '设备状态',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'RELIABILITY',
  },
  {
    vprop: 'TIMELINESS',
    title: '设备及时',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'TIMELINESS',
  },
  {
    vprop: 'BOTTLENECK',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'RELIABILITY',
    title: '可靠性',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_UNCHANGE',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY_S',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'BOTTLENECK_S',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'SDHALT',
    title: '健康状况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'SCORE',
  },
  {
    vprop: 'SCORE_CONTINUITY',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_CONTINUITY_S',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'INTEGRITY',
    title: '设备完整',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'INTEGRITY',
  },
  {
    vprop: 'DAY_PIPE_FULL',
    title: '日均充满度',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'DAY_OVER_COUNT',
    title: '日冒溢次数',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'NUMBER',
  },
  {
    vprop: 'DAY_OVERLOAD',
    title: '日均充满度情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO1',
  },
  {
    vprop: 'DAY_MAX_DEPTH',
    title: '日最大水深',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'ELEVATION',
  },
  {
    vprop: 'DAY_MAX_FULL',
    title: '日满管情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO2',
  },
  {
    vprop: 'DAY_AVG_FULL',
    title: '日均满管情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO2',
  },
  {
    vprop: 'DAY_OVER_TIME',
    title: '日冒溢时间',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'HOUR',
  },
  {
    vprop: 'DAY_FULL_TIME',
    title: '日满管时间',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'HOUR',
  },
  {
    vprop: 'STIME_DAY',
    title: '入库时间',
  },
  {
    vprop: 'DAY_HILOAD_TIME',
    title: '日高负荷时间',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'HOUR',
  },
  {
    vprop: 'REMARK',
    title: '备注',
  },
  {
    vprop: 'STATE_DAYS_S',
    title: '持续状态(最新)',
  },
  {
    vprop: 'IS_PDG_AREA',
    title: '是否是积水区',
  },
  {
    vprop: 'ORANIZATION',
    title: '所属分公司',
  },
  {
    vprop: 'STATE',
    title: '状态',
  },
  {
    vprop: 'SHORT_TITLE',
    title: '简称',
  },
  {
    vprop: 'MAX_DEPTH_LIMIT',
    title: '水深上限',
  },
  {
    vprop: 'SEND_TIMESTEP',
    title: '发送步长',
    unit: 'MINUTE',
  },
  {
    vprop: 'BOTTOM_ELEVATION',
    title: '井底标高',
    unit: 'ELEVATION',
  },
  {
    vprop: 'CALIBER',
    title: '管道口径',
    unit: 'DIAMETER',
  },
  {
    vprop: 'JUNCTION',
    title: '所在检查井',
  },
  {
    vprop: 'WELL_DEPTH',
    title: '井深',
    unit: 'ELEVATION',
  },
  {
    vprop: 'INSTALLATION_HEIGHT',
    title: '安装高度',
    unit: 'ELEVATION',
  },
  {
    vprop: 'PARENT_AREA',
    title: '所属区域',
  },
  {
    vprop: 'DISPLAYLEVEL',
    title: '显示等级',
  },
  {
    vprop: 'SAMPLING_TIMESTEP',
    title: '采样步长',
    unit: 'MINUTE',
  },
  {
    vprop: 'EQUIPMENT_MODEL',
    title: '设备型号',
  },
  {
    vprop: 'GROUND_LEVEL',
    title: '地面标高',
    unit: 'ELEVATION',
  },
  {
    vprop: 'ADDRESS',
    title: '位置',
  },
  {
    vprop: 'STATE_DAYS',
    title: '状态(好/坏)持续天数',
  },
  {
    vprop: 'STATE_UPDATE_DATE',
    title: '状态(好/坏)更新时间',
  },
  {
    vprop: 'DEVICE_TYPE',
    title: '类型',
  },
  {
    vprop: 'MEASURING_ACCURACY',
    title: '测量精度',
  },
  {
    vprop: 'MONITOR_POINT',
    title: '监测点号',
  },
  {
    vprop: 'MIN_DRAW_RATIO',
    title: '最小比例尺',
  },
  {
    vprop: 'MAX_DRAW_RATIO',
    title: '最大比例尺',
  },
  {
    vprop: 'TITLE',
    title: '名称',
  },
  {
    vprop: 'FACTORY',
    title: '设备厂家',
  },
  {
    vprop: 'MONITOR_OBJECT',
    title: '分组',
  },
  {
    vprop: 'DATASOURCE',
    title: '设备来源',
  },
  {
    vprop: 'CUR_DEPTH',
    title: '当前水深',
  },
  {
    vprop: 'STIME_TM',
    title: '采集时间',
  },
  {
    vprop: 'OVERLOAD',
    title: '是否高负荷',
    chart_def: {
      type: 'chart',
      charts: 'enumerateChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'YESNO1',
  },
  {
    vprop: 'PIPE_FULL',
    title: '充满度',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'IS_FULL',
    title: '是否满管',
    chart_def: {
      type: 'chart',
      charts: 'enumerateChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'YESNO2',
  },
  {
    vprop: 'PIPE_FULL_FC1',
    title: '充满度',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'CUR_DEPTH_FC1',
    title: '当前水深',
  },
  {
    vprop: 'STIME_FC1',
    title: '采集时间',
  },
  {
    vprop: 'OVERLOAD_FC1',
    title: '是否高负荷',
    chart_def: {
      type: 'chart',
      charts: 'enumerateChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'YESNO1',
  },
  {
    vprop: 'IS_FULL_FC1',
    title: '是否满管',
    chart_def: {
      type: 'chart',
      charts: 'enumerateChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'YESNO2',
  },
];

export const ScadaSummaryPropsData = [
  {
    vprop: 'CUMULATIVE_FLOW',
    title: '日累计流量',
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'CUMULATIVE_POWER',
    title: '日累计功率',
  },
  {
    vprop: 'CUMULATIVE_CAPACITY',
    title: '日累计负荷',
  },
  {
    vprop: 'FREE_SPACE',
    title: '空管比例',
  },
  {
    vprop: 'CUMULATIVE_TM_FLOW',
    title: '累计流量',
  },
  {
    vprop: 'TM_FLOW',
    title: '瞬时流量',
    unit: 'FLOW',
  },
  {
    vprop: 'RELIABILITY',
    title: '设备评估总分',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'SCORE',
  },
  {
    vprop: 'RELIABILITY_S',
    title: '设备状态',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'RELIABILITY',
  },
  {
    vprop: 'IN_FLOW_DAY',
    title: '流入日汇总',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'DAY_FLOW',
    title: '日汇总',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'MIN_NMF',
    title: '夜间最小流量',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'MIN_NMF_TIME',
    title: '夜间最小流量时间',
  },
  {
    vprop: 'OUT_FLOW_DAY',
    title: '流出日汇总',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'AVERAGE_PRESSURE',
    title: '压力平均值',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PRESSURE',
  },
  {
    vprop: 'AVERAGE_NMF',
    title: '夜间流量平均',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'USER_METER_COUNT',
    title: '用户表数量',
  },
  {
    vprop: 'MAX_FLOW',
    title: '最大瞬时值',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'MAX_FLOW_TIME',
    title: '最大瞬时值时间',
  },
  {
    vprop: 'AVERAGE_FLOW',
    title: '流量平均值',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'PRESSURE_PASSRATE',
    title: '压力合格率',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'PERCENTAGE100',
  },
  {
    vprop: 'WATERQUALITY_PASSRATE',
    title: '水质合格率',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'PERCENTAGE100',
  },
  {
    vprop: 'REALFLOW',
    title: '瞬时流量',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'OUT_FLOW',
    title: '实时流出',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'IN_FLOW',
    title: '实时流入',
    chart_def: {
      type: 'chart',
      charts: 'modelChart',
      proCharts: 'TIME_MAX_MIN',
    },
    unit: 'FLOW',
  },
  {
    vprop: 'DAY_OCCURRED_WARNING',
    title: '日警报汇总',
  },
  {
    vprop: 'DAY_PROCESS_WARNING',
    title: '日处理警报汇总',
  },
];

export const unitData = {
  VELOCITY: {
    unit_title: '流速',
    value_title: 'm/s',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  MONTH: {
    unit_title: '',
    value_title: 'YYYY-MM',
    unit_type: 'T',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  CHLORINE: {
    unit_title: '余氯',
    value_title: 'mg/l',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  FLOW: {
    unit_title: '流量',
    value_title: 'm3/h',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 1,
  },
  SPEED: {
    unit_title: '转速',
    value_title: 'r/min',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  AREA: {
    unit_title: '面积',
    value_title: 'm2',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 1,
  },
  YEAR: {
    unit_title: '',
    value_title: 'YYYY',
    unit_type: 'T',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  FLOW_MODLE: {
    unit_title: '流量',
    value_title: 'm3/h',
    unit_type: 'D',
    value_muli: 3600,
    value_add: 0,
    value_precision: 1,
  },
  RAINFALL_INTENSITY: {
    unit_title: '降雨强度',
    value_title: 'mm/h',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 1,
  },
  PIPEFULL_TEST: {
    unit_title: '充满度数值',
    value_title:
      '[\n{"极低": [0]},\n{"低": [0,0.05]},\n{"中": [0.05,0.2]},\n{"高": [0.2]}\n]',
    unit_type: 'R',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  MINUTE: {
    unit_title: '分钟',
    value_title: 'min',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  HOUR_MINUTE: {
    unit_title: '时间',
    value_title: 'HH:mm',
    unit_type: 'T',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  RAINFALL: {
    unit_title: '降雨',
    value_title: 'mm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  NUMBER: {
    unit_title: '次数',
    value_title: '次',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  AREA_HA: {
    unit_title: '汇水区面积',
    value_title: 'ha',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 4,
  },
  VOLUME: {
    unit_title: '体积',
    value_title: 'm3',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  LEVEL: {
    unit_title: '液位',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  TOTAL_CUMULATIVE: {
    unit_title: '累计流量',
    value_title: '万吨',
    unit_type: 'D',
    value_muli: 0.0001,
    value_add: 0,
    value_precision: 2,
  },
  DATE: {
    unit_title: '',
    value_title: 'YYYY-MM-DD',
    unit_type: 'T',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  DIAMETER: {
    unit_title: '直径',
    value_title: 'mm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  NH3: {
    unit_title: 'NH3',
    value_title: 'mg/l',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 1,
  },
  YESNO2: {
    unit_title: '是否满管',
    value_title: '{\r\n"":"-",\r\n"1":"满管",\r\n"0":"不满管"\r\n}',
    unit_type: 'E',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  YESNO3: {
    unit_title: '是否正在积水',
    value_title: '{\r\n"":"-",\r\n"1":"是",\r\n"0":"否"\r\n}',
    unit_type: 'E',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  YESNO1: {
    unit_title: '是否高负荷',
    value_title: '{\r\n"":"-",\r\n"1":"高负荷",\r\n"0":"低负荷"\r\n}',
    unit_type: 'E',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  ELECTRIC: {
    unit_title: '电流',
    value_title: 'A',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  HEAD: {
    unit_title: '水头',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  Bottleneck: {
    unit_title: '设备为坏主要原因',
    value_title:
      '{\r\n"":"-",\r\n"integrity":"数据缺失",\r\n"unchanged_longtime":"数据不变",\r\n"unchanged":"数据不变",\r\n"continuity":"数据波动",\r\n"stability":"数据不稳定",\r\n"no_data":"无数据"\r\n}',
    unit_type: 'E',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  VOLTAGE: {
    unit_title: '电压',
    value_title: 'u',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  SCORE: {
    unit_title: '',
    value_title: '分',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  HOUR: {
    unit_title: '时间',
    value_title: 'h',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  LENGTH: {
    unit_title: '长度',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 1,
  },
  WIDTH: {
    unit_title: '宽度',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  RELIABILITY: {
    unit_title: '可靠度',
    value_title:
      '{\r\n"0":"未评估",\r\n"1":"坏",\r\n"2":"差",\r\n"3":"良",\r\n"4":"优"\r\n}',
    unit_type: 'E',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  PIPEFULL: {
    unit_title: '充满度数值',
    value_title: null,
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  PERCENTAGE100: {
    unit_title: '',
    value_title: '%',
    unit_type: 'D',
    value_muli: 100,
    value_add: 0,
    value_precision: 1,
  },
  PRESSURE: {
    unit_title: '压力',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  INTEGRITY: {
    unit_title: '',
    value_title: '%',
    unit_type: 'D',
    value_muli: 100,
    value_add: 0,
    value_precision: 0,
  },
  FREQUENCY: {
    unit_title: '频率',
    value_title: 'Hz',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  COD: {
    unit_title: 'COD',
    value_title: 'mg/l',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  TIMELINESS: {
    unit_title: '',
    value_title: '%',
    unit_type: 'D',
    value_muli: 100,
    value_add: 0,
    value_precision: 0,
  },
  DESIFN_SCALE_P: {
    unit_title: '设计规模',
    value_title: '万吨/天',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  CUMULATIVE_FLOW: {
    unit_title: '累计流量',
    value_title: 'm3',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  ELEVATION: {
    unit_title: '标高',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  DESIFN_SCALE_F: {
    unit_title: '设计规模',
    value_title: '万吨/天',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  POWER: {
    unit_title: '功率',
    value_title: 'KW',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 3,
  },
  DIAMETER_K: {
    unit_title: '直径',
    value_title: 'mm',
    unit_type: 'D',
    value_muli: 1000,
    value_add: 0,
    value_precision: 0,
  },
  PERCENTAGE: {
    unit_title: '',
    value_title: '%',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 0,
  },
  INUNDATION: {
    unit_title: '内涝风险',
    value_title:
      '[\n{"无": [10]},\n{"低": [10,40]},\n{"中": [40,60]},\n{"高": [60,80]},\n{"很高": [80]}\n]',
    unit_type: 'R',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  VOLUME_MODLE: {
    unit_title: '体积',
    value_title: 'm3',
    unit_type: 'D',
    value_muli: 1000,
    value_add: 0,
    value_precision: 2,
  },
  DEPTH: {
    unit_title: '深度',
    value_title: 'm',
    unit_type: 'D',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
  SIM_SCORE: {
    unit_title: '模拟分数',
    value_title:
      '[\n{"较差": [5]},\n{"一般": [5,9]},\n{"良好": [9,10]},\n{"优秀": [10]}\n]',
    unit_type: 'R',
    value_muli: 1,
    value_add: 0,
    value_precision: 2,
  },
};

export const invalidPropView = [
  {
    type: 'button_prop',
    title: '监测指标',
    prop_list: [
      {
        type: 'unknown_type',
        otype: 'SDVAL_LIQ',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
    ],
  },
];

export const devLiquidTooltipViewData = [
  {
    type: 'tooltip_prop',
    title: '属性',
    prop_list: [
      {
        type: 'prop',
        vprop: 'TITLE',
      },
      {
        type: 'prop',
        vprop: 'MONITOR_OBJECT',
      },
      {
        type: 'prop',
        vprop: 'GROUND_LEVEL',
      },
      {
        type: 'prop',
        vprop: 'WELL_DEPTH',
      },
      {
        type: 'prop',
        vprop: 'RELIABILITY_S',
      },
      {
        type: 'quota',
        otype: 'SDVAL_LIQ',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: 'chart',
      },
    ],
  },
];

export const devFlowPropViewData = [
  {
    type: 'button_prop',
    title: '监测指标',
    prop_list: [
      {
        type: 'quota',
        otype: 'SDVAL_FLOW_W',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_FULL_FLOW',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_PRESS_W',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_HEAD',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_STORAGE_DEPTH',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_CR',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_TURBIDITY',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_QUALITY',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_PUMPRUN',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_FREQUENCY',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_POWER',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_PH',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'quota',
        otype: 'SDVAL_VOLTAGE',
        vprop: 'SDVAL',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
    ],
  },
  {
    type: 'button_prop',
    title: '日汇总水量(昨日)',
    prop_list: [
      {
        type: 'prop',
        vprop: 'SUM_FLOW',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'SUM_FLOW_CF',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
    ],
  },
  {
    type: 'quota_prop',
    title: '指标模拟精度',
    otype_list: [
      {
        otype: 'SDVAL_FLOW_W',
        prop_list: [
          {
            vprop: 'SIM_SCORE',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MODEL_ERROR',
            button: {
              type: 'chart',
              charts: 'modelChart',
              proCharts: 'TIME_MAX_MIN',
            },
          },
          {
            vprop: 'MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ABSOLUTE_MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MEDIAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ROOT_MEAN_SQUARE_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ERROR_OSCILLATION',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'NASH_COEFFICIENT',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
      {
        otype: 'SDVAL_PRESS_W',
        prop_list: [
          {
            vprop: 'SIM_SCORE',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MODEL_ERROR',
            button: {
              type: 'chart',
              charts: 'modelChart',
              proCharts: 'TIME_MAX_MIN',
            },
          },
          {
            vprop: 'MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ABSOLUTE_MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MEDIAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ROOT_MEAN_SQUARE_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ERROR_OSCILLATION',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'NASH_COEFFICIENT',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
      {
        otype: 'SDVAL_STORAGE_DEPTH',
        prop_list: [
          {
            vprop: 'SIM_SCORE',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MODEL_ERROR',
            button: {
              type: 'chart',
              charts: 'modelChart',
              proCharts: 'TIME_MAX_MIN',
            },
          },
          {
            vprop: 'MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ABSOLUTE_MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MEDIAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ROOT_MEAN_SQUARE_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ERROR_OSCILLATION',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'NASH_COEFFICIENT',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
      {
        otype: 'SDVAL_CR',
        prop_list: [
          {
            vprop: 'SIM_SCORE',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MODEL_ERROR',
            button: {
              type: 'chart',
              charts: 'modelChart',
              proCharts: 'TIME_MAX_MIN',
            },
          },
          {
            vprop: 'MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ABSOLUTE_MEAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'MEDIAN_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ROOT_MEAN_SQUARE_ERROR',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'ERROR_OSCILLATION',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'NASH_COEFFICIENT',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
    ],
  },
  {
    type: 'button_prop',
    title: '设备评估(昨日)',
    prop_list: [
      {
        type: 'prop',
        vprop: 'RELIABILITY_S',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'INTEGRITY',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'TIMELINESS',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'STATE_DAYS_S',
      },
    ],
  },
  {
    type: 'base_prop',
    title: '基本属性',
    prop_list: [
      {
        type: 'prop',
        vprop: 'ONAME',
      },
      {
        type: 'prop',
        vprop: 'TITLE',
      },
      {
        type: 'prop',
        vprop: 'STATION_GRADE',
      },
      {
        type: 'prop',
        vprop: 'STATION_TYPE_I',
      },
      {
        type: 'prop',
        vprop: 'STATION_TYPE_S',
      },
      {
        type: 'prop',
        vprop: 'MONITOR_OBJECT',
      },
      {
        type: 'prop',
        vprop: 'DEVICE_TYPE',
      },
      {
        type: 'prop',
        vprop: 'ORANIZATION',
      },
      {
        type: 'prop',
        vprop: 'FACTORY',
      },
      {
        type: 'prop',
        vprop: 'ADDRESS',
      },
      {
        type: 'prop',
        vprop: 'CALIBER',
      },
      {
        type: 'prop',
        vprop: 'GROUND_LEVEL',
      },
      {
        type: 'prop',
        vprop: 'BUILD_TIME',
      },
      {
        type: 'prop',
        vprop: 'SAMPLING_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'SEND_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'CHANGE_REPORT',
      },
      {
        type: 'prop',
        vprop: 'REMARK',
      },
    ],
  },
  {
    type: 'quota_prop',
    title: '指标评估(昨日)',
    otype_list: [
      {
        otype: 'SDVAL_FLOW_W',
        prop_list: [
          {
            vprop: 'RELIABILITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'INTEGRITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'TIMELINESS',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
      {
        otype: 'SDVAL_PRESS_W',
        prop_list: [
          {
            vprop: 'RELIABILITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'INTEGRITY',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
          {
            vprop: 'TIMELINESS',
            button: {
              type: 'chart',
              dateType: 'day',
              charts: 'barChart',
              proCharts: 'DAY_BAR',
            },
          },
        ],
      },
    ],
  },
  {
    type: 'note_prop',
    title: '备注',
    prop_list: [
      {
        type: 'prop',
        vprop: 'REMARK',
      },
    ],
  },
];

export const devFlowPropsData = [
  {
    vprop: 'BOTTLENECK',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'BOTTLENECK_S',
    title: '设备为坏主要原因',
    unit: 'Bottleneck',
  },
  {
    vprop: 'RELIABILITY_S',
    title: '设备状态',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'RELIABILITY',
  },
  {
    vprop: 'SCORE_CONTINUITY',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_CONTINUITY_S',
    title: '数据波动',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'RELIABILITY',
    title: '可靠性',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PERCENTAGE',
  },
  {
    vprop: 'SDHALT',
    title: '健康状况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'SCORE',
  },
  {
    vprop: 'SCORE_UNCHANGE',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'STIME_DAY',
    title: '入库时间',
  },
  {
    vprop: 'SCORE_UNCHANGE_S',
    title: '数据不变',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY_S',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'SCORE_STABILITY',
    title: '数据不稳定',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'INTEGRITY',
    title: '设备完整',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'INTEGRITY',
  },
  {
    vprop: 'TIMELINESS',
    title: '设备及时',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'TIMELINESS',
  },
  {
    vprop: 'STIME',
    title: '入库时间',
  },
  {
    vprop: 'PERCENTAGE',
    title: '数据比例',
  },
  {
    vprop: 'NUMBER_AMOUNT',
    title: '数据个数',
  },
  {
    vprop: 'SUM_FLOW_CF2',
    title: '日汇总水量(累计2)',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'CF2_SECOND_NUM_TIME',
    title: '第二个值时间',
  },
  {
    vprop: 'CF2_SECOND_NUM',
    title: '第二个值',
  },
  {
    vprop: 'CF2_FIRST_NUM_TIME',
    title: '第一个值时间',
  },
  {
    vprop: 'CF2_FIRST_NUM',
    title: '第一个值',
  },
  {
    vprop: 'CF1_SECOND_NUM_TIME',
    title: '第二个值时间',
  },
  {
    vprop: 'CF1_SECOND_NUM',
    title: '第二个值',
  },
  {
    vprop: 'SUM_FLOW',
    title: '日汇总水量(瞬时）',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'SUM_FLOW_CF',
    title: '日汇总水量(累计)',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'CUMULATIVE_FLOW',
  },
  {
    vprop: 'CF1_FIRST_NUM_TIME',
    title: '第一个值时间',
  },
  {
    vprop: 'CF1_FIRST_NUM',
    title: '第一个值',
  },
  {
    vprop: 'DAY_FULL_TIME',
    title: '日满管时间',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'HOUR',
  },
  {
    vprop: 'DAY_PIPE_FULL',
    title: '日均充满度',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'PIPEFULL',
  },
  {
    vprop: 'DAY_HILOAD_TIME',
    title: '日高负荷时间',
  },
  {
    vprop: 'DAY_MAX_FULL',
    title: '日满管情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO2',
  },
  {
    vprop: 'DAY_MAX_DEPTH',
    title: '日最大水深',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'ELEVATION',
  },
  {
    vprop: 'DAY_OVER_TIME',
    title: '日冒溢时间',
  },
  {
    vprop: 'DAY_OVER_COUNT',
    title: '日冒溢次数',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'NUMBER',
  },
  {
    vprop: 'DAY_AVG_FULL',
    title: '日均满管情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO2',
  },
  {
    vprop: 'DAY_OVERLOAD',
    title: '日均充满度情况',
    chart_def: {
      type: 'chart',
      charts: 'barChart',
      proCharts: 'DAY_BAR',
    },
    unit: 'YESNO1',
  },
  {
    vprop: 'CALIBER',
    title: '管道口径',
    unit: 'DIAMETER',
  },
  {
    vprop: 'MAX_DRAW_RATIO',
    title: '最大比例尺',
  },
  {
    vprop: 'MEASURING_ACCURACY',
    title: '测量精度',
  },
  {
    vprop: 'REMARK',
    title: '备注',
  },
  {
    vprop: 'MIN_DRAW_RATIO',
    title: '最小比例尺',
  },
  {
    vprop: 'MONITOR_POINT',
    title: '监测点号',
  },
  {
    vprop: 'STATE_UPDATE_DATE',
    title: '状态(好/坏)更新时间',
  },
  {
    vprop: 'STATE_DAYS',
    title: '状态(好/坏)持续天数',
  },
  {
    vprop: 'ASSESSMENT_CONFIGURE',
    title: '设备评估配置',
  },
  {
    vprop: 'SAMPLING_TIMESTEP',
    title: '采样步长',
    unit: 'MINUTE_SECOND',
  },
  {
    vprop: 'STATE_DAYS_S',
    title: '持续状态(最新)',
  },
  {
    vprop: 'IS_PDG_AREA',
    title: '是否是积水区',
  },
  {
    vprop: 'CHANGE_REPORT',
    title: '是否缝变则报',
    unit: 'YESNO3',
  },
  {
    vprop: 'BUILD_TIME',
    title: '建设时间',
  },
  {
    vprop: 'PARENT_AREA',
    title: '所属区域',
  },
  {
    vprop: 'GROUND_LEVEL',
    title: '地面标高',
    unit: 'ELEVATION',
  },
  {
    vprop: 'MONITOR_OBJECT',
    title: '站点类别',
  },
  {
    vprop: 'SHORT_TITLE',
    title: '简称',
  },
  {
    vprop: 'POWER_SUPPLY',
    title: '供电方式',
  },
  {
    vprop: 'ORANIZATION',
    title: '所属分公司',
  },
  {
    vprop: 'DISPLAYLEVEL',
    title: '显示等级',
  },
  {
    vprop: 'STATION_TYPE_S',
    title: '站点小类',
  },
  {
    vprop: 'STATE',
    title: '状态',
  },
  {
    vprop: 'SEND_TIMESTEP',
    title: '发送步长',
    unit: 'MINUTE_SECOND',
  },
  {
    vprop: 'DEVICE_TYPE',
    title: '设备类型',
  },
  {
    vprop: 'STATION_TYPE_I',
    title: '站点大类',
  },
  {
    vprop: 'TITLE',
    title: '名称',
  },
  {
    vprop: 'DATASOURCE',
    title: '设备来源',
  },
  {
    vprop: 'FACTORY',
    title: '设备厂家',
  },
  {
    vprop: 'STATION_GRADE',
    title: '站点等级',
  },
  {
    vprop: 'EQUIPMENT_MODEL',
    title: '设备型号',
  },
  {
    vprop: 'ADDRESS',
    title: '位置',
  },
  {
    vprop: 'JUNCTION',
    title: '所在检查井',
  },
];

export const devFlowTooltipViewData = [
  {
    type: 'tooltip_prop',
    title: '属性',
    prop_list: [
      {
        type: 'prop',
        vprop: 'GROUND_LEVEL',
      },
      {
        type: 'prop',
        vprop: 'BUILD_TIME',
      },
      {
        type: 'prop',
        vprop: 'SAMPLING_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'SEND_TIMESTEP',
      },
      {
        type: 'prop',
        vprop: 'RELIABILITY_S',
        button: 'chart',
      },
    ],
  },
];

export const dmaPropViewData = [
  {
    type: 'base_prop',
    title: '基本属性',
    prop_list: [
      {
        type: 'prop',
        vprop: 'TITLE',
      },
      {
        type: 'prop',
        vprop: 'DMA_TYPE',
      },
      {
        type: 'prop',
        vprop: 'DMA_LEVEL',
      },
      {
        type: 'prop',
        vprop: 'POPULATION',
      },
      {
        type: 'prop',
        vprop: 'NETWORKLENGTH',
      },
      {
        type: 'prop',
        vprop: 'AREA',
      },
      {
        type: 'prop',
        vprop: 'CREATER',
      },
      {
        type: 'prop',
        vprop: 'CREATION_TIME',
      },
      {
        type: 'prop',
        vprop: 'REMARKS',
      },
    ],
  },
  {
    type: 'button_prop',
    title: '模拟结果',
    prop_list: [
      {
        type: 'prop',
        vprop: 'TOTAL_FLOW',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'PRESSURE',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'TOTALHEAD',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'AGE',
        fc_ext: 'realAndForecast',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'WATERSOURCE',
      },
      {
        type: 'relation',
        title: '测试值',
        rmode: 'CALCULATION',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
    ],
  },
  {
    type: 'dma_related_devices',
    title: '分区与流量计关系',
    prop_list: [],
  },
  {
    type: 'button_prop',
    title: '实时监测信息',
    prop_list: [
      {
        type: 'prop',
        vprop: 'REALFLOW',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
      {
        type: 'prop',
        vprop: 'IN_FLOW',
        button: {
          type: 'chart',
          charts: 'modelChart',
          proCharts: 'TIME_MAX_MIN',
        },
      },
    ],
  },
  {
    type: 'button_prop',
    title: '日汇总信息(昨日)',
    prop_list: [
      {
        type: 'prop',
        vprop: 'USER_METER_COUNT',
        button: [
          {
            type: 'dmaCustomer',
          },
          {
            type: 'chart',
            dateType: 'day',
            charts: 'barChart',
            proCharts: 'DAY_BAR',
          },
        ],
      },
      {
        type: 'prop',
        vprop: 'DAY_FLOW',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'IN_FLOW_DAY',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'MAX_FLOW',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'AVERAGE_FLOW',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
      {
        type: 'prop',
        vprop: 'AVERAGE_PRESSURE',
        button: {
          type: 'chart',
          dateType: 'day',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
      },
    ],
  },
  {
    type: 'chart',
    title: '曲线',
    mode: 'child',
  },
];

export const definesData = [
  {
    device_list_type: 'DEVICE',
    otype: 'DEV_LIQUID',
    title: '液位计',
    prop_view: devLiquidPropViewData,
    props: devLiquidPropsData,
    tooltip_view: devLiquidTooltipViewData,
  },
  {
    device_list_type: null,
    otype: 'SDVAL_LIQ',
    title: '液位',
    props: sdvalLiqPropViewData,
  },
  {
    device_list_type: 'DEVICE',
    otype: 'DEV_FLOW',
    title: '流量计',
    prop_view: devFlowPropViewData,
    props: devFlowPropsData,
    tooltip_view: devFlowTooltipViewData,
  },
  {
    otype: 'SDVAL_FLOW_W',
    title: '瞬时流量(供水)',
    device_list_type: null,
    props: [
      {
        vprop: 'SIM_SCORE',
        title: '模拟分数',
        chart_def: {
          type: 'chart',
          charts: 'barChart',
          proCharts: 'DAY_BAR',
        },
        unit: 'SIM_SCORE',
      },
    ],
  },
  {
    device_list_type: null,
    otype: 'NRW_DMA3',
    prop_view: dmaPropViewData,
    props: [],
    tooltip_view: [],
  },
  {
    device_list_type: null,
    otype: 'SCADA_SUMMARY',
    title: 'scada汇总',
    prop_view: [],
    props: ScadaSummaryPropsData,
    tooltip_view: [],
  },
];
