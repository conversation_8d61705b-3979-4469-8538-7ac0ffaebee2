/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { PropertyValue, PumpInfo } from '../device';
import { TimeData } from '../time-data';

function mockFixedPumpStatus(): TimeData[] {
  const timeData: TimeData[] = [];
  const startTime = dayjs('2023-04-01');
  for (let i = 0; i < 720; i += 1) {
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: 0,
    });
  }

  for (let i = 720; i < 1000; i += 1) {
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: 1,
    });
  }
  for (let i = 1000; i < 1440; i += 1) {
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: 0,
    });
  }
  return timeData;
}

function mockVariablePumpStatus(): TimeData[] {
  const timeData: TimeData[] = [];
  const startTime = dayjs('2023-04-01');
  for (let i = 0; i < 720; i += 1) {
    const random = Math.random() / 2 + 0.5;
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: random,
    });
  }

  for (let i = 480; i < 960; i += 1) {
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: 0,
    });
  }
  for (let i = 960; i < 1440; i += 1) {
    const random = Math.random() / 2 + 0.5;
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: random,
    });
  }
  return timeData;
}

function mockFixedPredictPumpStatus(): TimeData[] {
  const timeData: TimeData[] = [];
  const startTime = dayjs('2023-04-01');
  for (let i = 1000; i < 1440; i += 1) {
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: 0,
    });
  }
  return timeData;
}

function mockVariablePredictPumpStatus(): TimeData[] {
  const timeData: TimeData[] = [];
  const startTime = dayjs('2023-04-01');

  for (let i = 960; i < 1440; i += 1) {
    const random = Math.random() / 2 + 0.5;
    timeData.push({
      time: startTime.add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value: random,
    });
  }
  return timeData;
}

export const mockPumpInfo1_Variable: PumpInfo = {
  title: '1#',
  oname: 'XQSC155',
  otype: 'SDVAL_FREQUENCY',
  variable: true,
  minFrequency: 25,
  maxFrequency: 50,
  indicators: [],
  onOffIndicator: {
    id: 'SDVAL_FREQUENCY@XQSC155',
    otype: 'SDVAL_FREQUENCY',
    oname: 'XQSC155',
  },
} as any;
export const mockPumpInfo2_Variable: PumpInfo = {
  title: '2#',
  oname: 'XQSC153',
  otype: 'SDVAL_FREQUENCY',
  variable: true,
  minFrequency: 25,
  maxFrequency: 50,
  indicators: [],
  onOffIndicator: {
    id: 'SDVAL_FREQUENCY@XQSC153',
    otype: 'SDVAL_FREQUENCY',
    oname: 'XQSC153',
  },
} as any;
export const mockPumpInfo3_Run: PumpInfo = {
  title: '3#',
  oname: 'XQSC172',
  otype: 'SDVAL_FREQUENCY',
  variable: true,
  minFrequency: 0,
  maxFrequency: 0,
  indicators: [],
  onOffIndicator: {
    id: 'SDVAL_FREQUENCY@XQSC172',
    otype: 'SDVAL_FREQUENCY',
    oname: 'XQSC172',
  },
} as any;

export const mockPumpList: PumpInfo[] = [
  mockPumpInfo1_Variable,
  mockPumpInfo2_Variable,
  mockPumpInfo3_Run,
];

export const mockPumpValveInfo: Map<string, PropertyValue> = new Map([
  [
    'SDVAL_PUMPRUN@XQSC172@SDVAL',
    {
      oname: 'XQSC172',
      otime: '2025-07-24 15:49:00',
      otype: 'SDVAL_PUMPRUN',
      value: '0',
      vprop: 'SDVAL',
    },
  ],
  [
    'SDVAL_FREQUENCY@XQSC155@SDVAL',
    {
      oname: 'XQSC155',
      otime: '2025-07-24 15:48:00',
      otype: 'SDVAL_FREQUENCY',
      value: '40.084',
      vprop: 'SDVAL',
    },
  ],
  [
    'SDVAL_FREQUENCY@XQSC153@SDVAL',
    {
      oname: 'XQSC153',
      otime: '2025-07-24 15:48:00',
      otype: 'SDVAL_FREQUENCY',
      value: '0.03',
      vprop: 'SDVAL',
    },
  ],
]);

export const mockPumpStateColor = {
  closed: '#808080',
  variable: '#009ece',
  fixed: '#00c542',
};

export const mockFixedPumpStatus1 = mockFixedPumpStatus();
export const mockVariablePumpStatus1 = mockVariablePumpStatus();

export const mockFixedPumpStatus2 = mockFixedPumpStatus().slice(0, 1000);
export const mockVariablePumpStatus2 = mockVariablePumpStatus().slice(0, 960);
export const mockFixedPredictPumpStatus2 = mockFixedPredictPumpStatus();
export const mockVariablePredictPumpStatus2 = mockVariablePredictPumpStatus();
