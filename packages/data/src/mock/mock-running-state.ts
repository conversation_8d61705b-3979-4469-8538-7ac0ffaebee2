/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
// eslint-disable-next-line import/no-extraneous-dependencies
import Mock from 'mockjs';
import { QUALITY, SeriesData } from '../mini-dashboard/running-state-data';

export const getWaterSupplyData = (
  date: Dayjs,
  randomRange: [number, number],
): { time: string; value: number }[] => {
  const data: { time: string; value: number }[] = [];
  let initialTimeDate = date.format('YYYY-MM-DD 00:00:00');
  const endTimeDate = dayjs(date.add(1, 'days').format('YYYY-MM-DD 00:00:00'));

  while (dayjs(initialTimeDate).isBefore(endTimeDate, 'minutes')) {
    data.push({
      time: initialTimeDate,
      value: Mock.Random.integer(randomRange[0], randomRange[1]),
    });
    initialTimeDate = dayjs(initialTimeDate)
      .add(5, 'minutes')
      .format('YYYY-MM-DD HH:mm:00');
  }
  return data;
};

export const getPassRateData = (date: Dayjs): SeriesData[] => {
  const series = ['0-1m', '1-2m', '2-4m', '大于4m'];

  return series.map((name, index): SeriesData => {
    const data: { time: string; value: number }[] = [];
    let initialTimeDate = date.format('YYYY-MM-DD 00:00:00');
    const endTimeDate = dayjs(
      date.add(1, 'days').format('YYYY-MM-DD 00:00:00'),
    );

    while (dayjs(initialTimeDate).isBefore(endTimeDate, 'minutes')) {
      const str = dayjs(initialTimeDate)
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm:00');
      data.push({
        time: str,
        value: Mock.Random.integer(60 + index * 10, 60 + index * 10 + 3),
      });
      initialTimeDate = str;
    }

    return {
      key: QUALITY,
      name,
      data,
    };
  });
};
