/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ScadaColumn,
  ScadaDevice,
  ScadaTableGroupConfig,
} from '../scada-dashboard-data';

const mockPlantDevice: ScadaDevice[] = [
  {
    otype: 'SDFOLD_FACT',
    oname: 'DN-SC',
    index: 1,
    chart: {
      indicators: [
        {
          otype: 'SDVAL_FLOW_W',
          index: 0,
          defaultOName: '305828',
        },
        {
          otype: 'SDVAL_PRESS_W',
          index: 0,
        },
      ],
      pump: {
        show: true,
      },
    },
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'FFS-SC',
    index: 2,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'GH-SC',
    index: 3,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'CM-SC',
    index: 4,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'DSH-SC',
    index: 5,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'DCSQ-SC',
    index: 6,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'KP-SC',
    index: 7,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'BQ-SC',
    index: 8,
    indicators: [
      {
        oname: 'BQSC26_SC',
        otype: 'SDVAL_PRESS_W',
        dataIndex: 'SDVAL_PRESS_W',
        children: [],
      },
      {
        oname: 'BQSC7_SC',
        otype: 'SDVAL_FLOW_W',
        dataIndex: 'SDVAL_FLOW_W',
        actions: 'SUM',
        children: [
          {
            oname: 'BQSC11_SC',
            otype: 'SDVAL_FLOW_W',
          },
          { oname: 'BQSC7_SC', otype: 'SDVAL_FLOW_W' },
        ],
      },
      {
        oname: 'BQSC24_SC',
        dataIndex: 'SDVAL_STORAGE_DEPTH',
        otype: 'SDVAL_STORAGE_DEPTH',
      },
      { oname: 'BQSC37_SC', otype: 'SDVAL_HEAD', dataIndex: 'SDVAL_HEAD' },
    ],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'TL-SC',
    index: 9,
    indicators: [],
  },
  {
    otype: 'SDFOLD_FACT',
    oname: 'XQ-SC',
    index: 10,
    indicators: [],
  },
];
const mockPlantColumns: ScadaColumn[] = [
  {
    dataIndex: 'SDVAL_PRESS_W',
    otype: 'SDVAL_PRESS_W',
    title: '压力123',
  },
  {
    dataIndex: 'SDVAL_FLOW_W',
    otype: 'SDVAL_FLOW_W',
  },
  {
    dataIndex: 'SDVAL_STORAGE_DEPTH',
    otype: 'SDVAL_STORAGE_DEPTH',
  },
  {
    dataIndex: 'SDVAL_FULL_FLOW',
    otype: 'SDVAL_FULL_FLOW',
  },
  {
    dataIndex: 'SDVAL_HEAD',
    otype: 'SDVAL_HEAD',
  },
];
const mockPumpDevice: ScadaDevice[] = [
  {
    otype: 'WDM_PUMPS',
    oname: 'CMSC-P1',
    index: 1,
    indicators: [],
    chart: {
      indicators: [
        {
          otype: 'SDVAL_FLOW_W',
          index: 0,
          defaultOName: '305828',
        },
        {
          otype: 'SDVAL_PRESS_W',
          index: 1,
        },
      ],
      pump: {
        show: true,
      },
    },
  },
];
const mockPumpColumns: ScadaColumn[] = [];

const mockGroupPlant: ScadaTableGroupConfig = {
  groupId: 'SDFOLD_FACT',
  groupName: '水厂',
  devices: mockPlantDevice,
  columns: mockPlantColumns,
};

const mockGroupPump: ScadaTableGroupConfig = {
  groupId: 'SDFOLD_STATION_WATER',
  groupName: '泵站',
  devices: mockPumpDevice,
  columns: mockPumpColumns,
};

export const mockGroupList = [mockGroupPlant, mockGroupPump];
