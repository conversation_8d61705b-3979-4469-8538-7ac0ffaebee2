/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FormSchemaType, ValueType } from '../otype-vprop-config-data';

export const mockSchemaFormList: FormSchemaType[] = [
  // 示例数据
  {
    dataIndex: '文本',
    title: '文本',
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: '只读文本',
    title: '只读文本',
    readonly: true,
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: '禁用文本',
    title: '禁用文本',
    disabled: true,
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: '长文本',
    title: '长文本',
    valueType: 'LongText' as ValueType,
  },

  {
    dataIndex: '长文本显示字数',
    title: '长文本显示字数',
    valueType: 'LongText' as ValueType,
    showCount: true,
  },
  {
    dataIndex: '长文本最大长度为100字',
    title: '长文本最大长度为100字',
    valueType: 'LongText' as ValueType,
    maxLength: 100,
    showCount: true,
  },
  {
    dataIndex: '数值',
    title: '数值',
    valueType: 'Number' as ValueType,
  },
  {
    dataIndex: '数值限制1~100',
    title: '数值限制1~100',
    valueType: 'Number' as ValueType,
    min: 1,
    max: 100,
  },
  {
    dataIndex: '数值精度小数后3位',
    title: '数值精度小数后3位',
    valueType: 'Number' as ValueType,
    precision: 3,
  },
  {
    dataIndex: '开关',
    title: '开关',
    valueType: 'Switch' as ValueType,
  },
  {
    dataIndex: '开关加文本',
    title: '开关加文本',
    valueType: 'Switch' as ValueType,
    checkedChildren: '开',
    unCheckedChildren: '关',
  },
  {
    dataIndex: '单选按钮',
    title: '单选按钮',
    valueType: 'Radio' as ValueType,
    options: [
      {
        label: 'A',
        value: 'A',
      },
      {
        label: 'B',
        value: 'B',
      },
      {
        label: 'C',
        value: 'C',
      },
      {
        label: 'D',
        value: 'D',
      },
    ],
  },
  {
    dataIndex: '多选按钮',
    title: '多选按钮',
    valueType: 'Checkbox' as ValueType,
    options: [
      {
        label: 'A',
        value: 'A',
      },
      {
        label: 'B',
        value: 'B',
      },
      {
        label: 'C',
        value: 'C',
      },
      {
        label: 'D',
        value: 'D',
      },
    ],
  },
  {
    dataIndex: '下拉选择',
    title: '下拉选择',
    valueType: 'Select' as ValueType,
    options: [
      {
        label: 'A',
        value: 'A',
      },
      {
        label: 'B',
        value: 'B',
      },
      {
        label: 'C',
        value: 'C',
      },
      {
        label: 'D',
        value: 'D',
      },
    ],
  },
  {
    dataIndex: '图片',
    title: '图片',
    valueType: 'Image' as ValueType,
  },
  {
    dataIndex: '日期选择',
    title: '日期选择',
    valueType: 'DatePicker' as ValueType,
  },
  {
    dataIndex: '日期时间选择',
    title: '日期时间选择',
    valueType: 'DateTimePicker' as ValueType,
  },
];

export const mockDEVPFFormListBase: FormSchemaType[] = [
  {
    dataIndex: 'TITLE',
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: 'SHORT_TITLE',
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: 'EQUIPMENT_MODEL',
    valueType: 'Text' as ValueType,
  },
];

export const mockDEVPFFormListInvalidData: FormSchemaType[] = [
  {
    dataIndex: '',
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: '',
    valueType: 'Text' as ValueType,
  },
  {
    dataIndex: '',
    valueType: 'Text' as ValueType,
  },
];

export const mockDEVPFFormListHaveOrder: FormSchemaType[] = [
  {
    dataIndex: 'TITLE',
    valueType: 'Text' as ValueType,
    order: 2,
  },
  {
    dataIndex: 'SHORT_TITLE',
    valueType: 'Text' as ValueType,
    order: 3,
  },
  {
    dataIndex: 'EQUIPMENT_MODEL',
    valueType: 'Text' as ValueType,
    order: 1,
  },
];
