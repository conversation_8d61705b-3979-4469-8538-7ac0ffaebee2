/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ScadaGroupListItem, ScadaTreeListItem } from '../scada-tree-data';
import { transListToTree } from '../tree';

export const mockGroup1ScadaTreeList: ScadaTreeListItem[] = [
  {
    id: '7313e0e3-87d4-4783-9f2a-6b155c477b61',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: '水厂',
    order: 1,
    type: 'other',
  },
  {
    id: 'd6ad6737-9ebd-4d4f-b4d7-9fb1eb6f1799',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: '泵站',
    order: 2,
    type: 'other',
  },
  {
    id: '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: '水箱',
    order: 3,
    type: 'other',
  },
  {
    id: 'b8db3f3c-4aeb-4f84-89e3-520eefbe7d5a',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: '流量计',
    order: 4,
    type: 'other',
  },
  {
    id: 'e0ce6c4f-19c2-44b6-8dc3-31b227a9630a',
    parentId: '7313e0e3-87d4-4783-9f2a-6b155c477b61',
    title: '取水口',
    order: 1,
    type: 'other',
  },
  {
    id: '24e49a6c-7a1a-4cfc-8e02-82ebd8c8de6b',
    parentId: '7313e0e3-87d4-4783-9f2a-6b155c477b61',
    title: '水处理设备',
    order: 2,
    type: 'other',
  },
  {
    id: '9c3d769d-24f4-4bc2-824a-3a9b4b616e85',
    parentId: 'd6ad6737-9ebd-4d4f-b4d7-9fb1eb6f1799',
    title: '主泵',
    order: 1,
    type: 'other',
  },
  {
    id: '5f1b84f7-790e-4c9e-8925-cbbf8a14ed14',
    parentId: 'd6ad6737-9ebd-4d4f-b4d7-9fb1eb6f1799',
    title: '备用泵',
    order: 2,
    type: 'other',
  },
  {
    id: '7c84c267-2c7f-4b14-9b0b-2c9f75c8d7b0',
    parentId: '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
    title: '上水箱',
    order: 1,
    type: 'other',
  },
  {
    id: '1c08f07d-7ef1-4f96-a537-79dbd52c5b29',
    parentId: '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
    title: '下水箱',
    order: 2,
    type: 'other',
  },
  {
    id: '20deef96-1b20-4e9f-b21d-3b7be508f3b5',
    parentId: 'b8db3f3c-4aeb-4f84-89e3-520eefbe7d5a',
    title: 'A流量计',
    order: 1,
    type: 'other',
  },
  {
    id: '45a1860c-3e9f-4988-9677-3f8ff73c4995',
    parentId: 'b8db3f3c-4aeb-4f84-89e3-520eefbe7d5a',
    title: 'B流量计',
    order: 2,
    type: 'other',
  },
  {
    id: 'bd8b6d3a-6655-4ee7-bf59-2f1ee24e9ed9',
    parentId: 'e0ce6c4f-19c2-44b6-8dc3-31b227a9630a',
    title: '取水口1',
    order: 1,
    type: 'other',
  },
  {
    id: 'd06c1525-34f7-43b0-9566-990b6be3f37d',
    parentId: 'e0ce6c4f-19c2-44b6-8dc3-31b227a9630a',
    title: '取水口2',
    order: 2,
    type: 'other',
  },
];

export const mockGroup2ScadaTreeList: ScadaTreeListItem[] = [
  {
    id: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    parentId: '',
    title: '总公司',
    order: 0,
    type: 'other',
  },
  {
    id: '7c1a0dce-46f0-4d2d-9872-76b8551c989c',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: '人力资源部',
    order: 1,
    type: 'other',
  },
  {
    id: '1d8d54f7-51be-402d-9cb7-2a0e2568e1a1',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: '技术研发部',
    order: 2,
    type: 'other',
  },
  {
    id: 'deff2b09-5e7a-4b72-9b10-46f56d67bc27',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: '市场销售部',
    order: 3,
    type: 'other',
  },
  {
    id: '4627b61a-31b9-4b41-97e5-bce46a244a61',
    parentId: '7c1a0dce-46f0-4d2d-9872-76b8551c989c',
    title: '人事管理组',
    order: 1,
    type: 'other',
  },
  {
    id: 'f5b0e39d-10e4-49df-bf55-5b5be3b85819',
    parentId: '7c1a0dce-46f0-4d2d-9872-76b8551c989c',
    title: '薪资福利组',
    order: 2,
    type: 'other',
  },
  {
    id: 'e50d3d9f-b55f-456d-8ab0-57f54de34d3e',
    parentId: '1d8d54f7-51be-402d-9cb7-2a0e2568e1a1',
    title: '前端开发组',
    order: 1,
    type: 'other',
  },
  {
    id: '346c9d02-d733-4fdd-9d1b-5e3b46ef12da',
    parentId: '1d8d54f7-51be-402d-9cb7-2a0e2568e1a1',
    title: '后端开发组',
    order: 2,
    type: 'other',
  },
  {
    id: '67fc8d6c-eb97-4e94-990e-01ef58c86db6',
    parentId: 'deff2b09-5e7a-4b72-9b10-46f56d67bc27',
    title: '市场推广组',
    order: 1,
    type: 'other',
  },
  {
    id: '8b7d7a55-2e0e-4b9b-ba61-07a30f15d8d3',
    parentId: 'deff2b09-5e7a-4b72-9b10-46f56d67bc27',
    title: '销售组',
    order: 2,
    type: 'other',
  },
];

export const mockScadaTreeData1: ScadaTreeListItem[] = transListToTree(
  mockGroup1ScadaTreeList,
  'id',
  'parentId',
);

export const mockScadaGroupList: ScadaGroupListItem[] = [
  {
    id: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Group 1',
    order: 1,
    children: mockGroup1ScadaTreeList,
  },
  {
    id: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Group 2',
    order: 2,
    children: mockGroup2ScadaTreeList,
  },
];

export const mockScadaTreeList = [
  {
    id: 'b12576be-3586-4c70-b861-d75c2a54819e',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Tree 1',
    order: 1,
  },
  {
    id: '34ae5b59-2c23-429e-a1a2-4cc85c14fc94',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Tree 2',
    order: 2,
  },
  {
    id: '7e84157b-d83f-43d3-88b2-7e3a3e3c61af',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Tree 3',
    order: 3,
  },
  {
    id: 'f95a28f7-56b6-4c2d-aef6-d2c63413e7d3',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Tree 4',
    order: 4,
  },
  {
    id: 'd33e8fa3-3f84-42e7-9011-88055e3dcf12',
    parentId: 'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d',
    title: 'Tree 5',
    order: 5,
  },
  {
    id: 'b90b7f7d-40fe-48e9-8f4a-00180d6ddc1e',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Tree 1',
    order: 1,
  },
  {
    id: 'b523b0f6-b635-4d2b-9813-2b6ef0f41c24',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Tree 2',
    order: 2,
  },
  {
    id: 'ef4e049e-4530-41f3-a96b-fb9446b4e36f',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Tree 3',
    order: 3,
  },
  {
    id: '8a2a4de5-3a25-4741-8a89-f52aaf1bfc03',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Tree 4',
    order: 4,
  },
  {
    id: '73a783b9-9077-46e9-9b1a-631fc56a19a5',
    parentId: '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f',
    title: 'Tree 5',
    order: 5,
  },
  {
    id: '271119be-5b8a-4e4c-ba0c-71a7392993c2',
    parentId: '32768f5d-0389-408d-8237-ff6386f5b716',
    title: 'Tree 1',
    order: 1,
  },
  {
    id: '1908ae62-c4b5-4e88-b123-351ea2277f85',
    parentId: '32768f5d-0389-408d-8237-ff6386f5b716',
    title: 'Tree 2',
    order: 2,
  },
  {
    id: 'a4d16286-3d7a-4d4d-8e84-ebf0c1f774a0',
    parentId: '32768f5d-0389-408d-8237-ff6386f5b716',
    title: 'Tree 3',
    order: 3,
  },
  {
    id: '8e29dbd7-532b-4df0-a891-9a89a27c9d45',
    parentId: '32768f5d-0389-408d-8237-ff6386f5b716',
    title: 'Tree 4',
    order: 4,
  },
  {
    id: '9f2a0b42-6b75-44e2-9635-853755a1f6e0',
    parentId: '32768f5d-0389-408d-8237-ff6386f5b716',
    title: 'Tree 5',
    order: 5,
  },
  {
    id: 'b12576be-3586-4c70-b861-d75c2a54819e',
    parentId: 'c5897b78-2c59-4c7d-a126-369f5df3b78e',
    title: 'Tree 1',
    order: 1,
  },
  {
    id: '34ae5b59-2c23-429e-a1a2-4cc85c14fc94',
    parentId: 'c5897b78-2c59-4c7d-a126-369f5df3b78e',
    title: 'Tree 2',
    order: 2,
  },
  {
    id: '7e84157b-d83f-43d3-88b2-7e3a3e3c61af',
    parentId: 'c5897b78-2c59-4c7d-a126-369f5df3b78e',
    title: 'Tree 3',
    order: 3,
  },
  {
    id: 'f95a28f7-56b6-4c2d-aef6-d2c63413e7d3',
    parentId: 'c5897b78-2c59-4c7d-a126-369f5df3b78e',
    title: 'Tree 4',
    order: 4,
  },
  {
    id: 'd33e8fa3-3f84-42e7-9011-88055e3dcf12',
    parentId: 'c5897b78-2c59-4c7d-a126-369f5df3b78e',
    title: 'Tree 5',
    order: 5,
  },
  {
    id: 'b90b7f7d-40fe-48e9-8f4a-00180d6ddc1e',
    parentId: '2e8154f6-5f6d-4d68-9868-0840fe28a7e7',
    title: 'Tree 1',
    order: 1,
  },
  {
    id: 'b523b0f6-b635-4d2b-9813-2b6ef0f41c24',
    parentId: '2e8154f6-5f6d-4d68-9868-0840fe28a7e7',
    title: 'Tree 2',
    order: 2,
  },
  {
    id: 'ef4e049e-4530-41f3-a96b-fb9446b4e36f',
    parentId: '2e8154f6-5f6d-4d68-9868-0840fe28a7e7',
    title: 'Tree 3',
    order: 3,
  },
  {
    id: '8a2a4de5-3a25-4741-8a89-f52aaf1bfc03',
    parentId: '2e8154f6-5f6d-4d68-9868-0840fe28a7e7',
    title: 'Tree 4',
    order: 4,
  },
  {
    id: '73a783b9-9077-46e9-9b1a-631fc56a19a5',
    parentId: '2e8154f6-5f6d-4d68-9868-0840fe28a7e7',
    title: 'Tree 5',
    order: 5,
  },
];

export const mockScadaDeviceList = [
  {
    oname: 'STA1691',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_BMBYQLK_500',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1945',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_BYQJT_400',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_DPLWSBL_300',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_DTJT_400',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_FGLFXD_300',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_FXDDFXD_400',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_FYL_300',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_GYXH_500',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_GYYQ_600',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1889',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1895',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1963',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1960',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [],
    },
  },
  {
    oname: 'STA1961',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA2',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA216',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA226',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA28',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA227',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA35',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA37',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA40',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA42',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
];

export const mockTestScadaDeviceData = [
  {
    oname: 'STA1691',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_BMBYQLK_500',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'STA1945',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
  {
    oname: 'D_BYQJT_400',
    otype: 'DEV_PF',
    group: {
      'f63e1c02-6ca4-4f9f-91c6-058c4db9c35d': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
      '4ab0e71a-036e-42ed-8e96-cc9680f2cb8f': [
        '9ae3748e-2d78-44b7-8d32-c8e73d20e2de',
      ],
    },
  },
];
