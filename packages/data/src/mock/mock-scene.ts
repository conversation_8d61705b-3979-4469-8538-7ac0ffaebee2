/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const mockLayerStates = [
  {
    name: 'WDM_PIPES',
    title: 'WDM_PIPES',
    visible: true,
  },
  {
    name: 'WDM_MODELPIPES',
    title: 'WDM_MODELPIPES',
    visible: true,
  },
  {
    name: 'WDM_AIRRELEASE',
    title: 'WDM_AIRRELEASE',
    visible: true,
  },
  {
    name: 'WDM_ENDPOINT',
    title: 'WDM_ENDPOINT',
    visible: true,
  },
  {
    name: 'WDM_HYDRANT',
    title: 'WDM_HYDRANT',
    visible: true,
  },
  {
    name: 'WDM_JUNCTIONS',
    title: 'WDM_JUNCTIONS',
    visible: true,
  },
  {
    name: 'WDM_MODELNODE',
    title: 'WDM_MODELNODE',
    visible: true,
  },
  {
    name: 'WDM_PUMPS',
    title: 'WDM_PUMPS',
    visible: true,
  },
  {
    name: 'WDM_RESERVOIRS',
    title: 'WDM_RESERVOIRS',
    visible: true,
  },
  {
    name: 'WDM_TANKS',
    title: 'WDM_TANKS',
    visible: true,
  },
];

export const mockCurrentThemeItem = {
  name: 'PIPES_FLOW',
  title: '流量',
};

export const mockThemeItems = [
  {
    name: 'PIPES_FLOW',
    title: '流量',
  },
  {
    name: 'PIPES_VELOCITY',
    title: '流速',
  },
  {
    name: 'PIPES_UNITHEADLOSS',
    title: '单位水损',
  },
  {
    name: 'JUNCTION_PRESS',
    title: '压力',
  },
  {
    name: 'JUNCTION_HEAD',
    title: '绝压',
  },
  {
    name: 'JUNCTION_AGE',
    title: '水龄',
  },
  {
    name: 'JUNCTION_WATERSOURCE',
    title: '供水范围',
  },
  {
    name: 'JUNCTION_WATERSOURCE1',
    title: '西厂供水范围',
  },
  {
    name: 'JUNCTION_DEMAND_VACANCY',
    title: '节点需水缺额',
  },
];

export const mockThemeSections = [
  {
    type: 'network',
    title: '管网',
    layerStates: mockLayerStates,
    themeItems: mockThemeItems,
    currentThemeItem: mockCurrentThemeItem,
  },
];
export const mockScene = {
  id: 'MODEL',
  title: '模型',
  dashboard: 'SimulationKpi',
  themeSections: mockThemeSections,
};
