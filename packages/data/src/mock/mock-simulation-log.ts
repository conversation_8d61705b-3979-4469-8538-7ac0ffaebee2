/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

// eslint-disable-next-line import/no-extraneous-dependencies
import Mo<PERSON> from 'mockjs';

export const mockSimulationLogList = () =>
  Mock.mock({
    'data|10-50': [
      {
        'id|+1': 1,
        modelTime: () => Mock.mock('@time("hh:mm")'),
        actualCalculateTime: () => Mock.mock('@datetime("yyyy-MM-dd H:m:s")'),
        'borderNumber|10-50': 50,
        'missBorderNumber|1-10': 10,
        'status|+1': ['success', 'error'],
      },
    ],
  }).data;

export const mockSimulationLogDetailList = () =>
  Mock.mock({
    'data|0-10': [
      {
        'id|+1': 1,
        name: () => Mock.mock('@ctitle(5, 8)'),
        'detectionValue|0.1-1': 0.22,
        time: () => Mock.mock('@datetime("yyyy-MM-dd H:m:s")'),
        'type|1-3': 3,
        description: () => Mock.mock('@ctitle(5, 8)'),
      },
    ],
  }).data;
