/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
/* eslint-disable import/no-extraneous-dependencies */
import Mock from 'mockjs';
import { WarnSettingList } from '../warn-setting';

function generateLoginData(yearMonthStr: string) {
  const startDate = dayjs(`${yearMonthStr}-01`);
  const currentMonth = dayjs().format('YYYY-MM');
  let endDate;

  // 如果是当前月份，结束日期为今天，否则为该月的最后一天
  if (yearMonthStr === currentMonth) {
    endDate = dayjs();
  } else {
    endDate = startDate.endOf('month');
  }

  const data = [];

  for (
    let date = startDate;
    date.isBefore(endDate) || date.isSame(endDate);
    date = date.add(1, 'day')
  ) {
    data.push({
      date: date.format('YYYY-MM-DD'),
      loginTimes: Mock.Random.integer(0, 100),
      loginUsers: Mock.Random.integer(0, 100),
    });
  }

  return {
    loginSituations: {
      loginTimes: Mock.Random.integer(0, 100),
      loginUsers: Mock.Random.integer(0, 100),
      loginTimesOfWaterDepartment: Mock.Random.integer(0, 100),
      loginUsersOfWaterDepartment: Mock.Random.integer(0, 100),
    },
    dailyLoginSituations: data,
  };
}

function generateUsageData(yearMonthStr: string) {
  const startDate = dayjs(`${yearMonthStr}-01`);
  const currentMonth = dayjs().format('YYYY-MM');
  let endDate;

  // 如果是当前月份，结束日期为今天，否则为该月的最后一天
  if (yearMonthStr === currentMonth) {
    endDate = dayjs();
  } else {
    endDate = startDate.endOf('month');
  }

  const data = [];

  for (
    let date = startDate;
    date.isBefore(endDate) || date.isSame(endDate);
    date = date.add(1, 'day')
  ) {
    data.push({
      date: date.format('YYYY-MM-DD'),
      detailedScheme: Mock.Random.integer(0, 100),
      quickScheme: Mock.Random.integer(0, 100),
      warnProcessTimes: Mock.Random.integer(0, 100),
      smsSendTimes: Mock.Random.integer(0, 100),
    });
  }

  return {
    usageSituations: {
      detailedScheme: Mock.Random.integer(0, 100),
      quickScheme: Mock.Random.integer(0, 100),
      warnProcessTimes: Mock.Random.integer(0, 100),
      smsSendTimes: Mock.Random.integer(0, 100),
    },
    dailyUsageSituations: data,
  };
}

function generateWarnData(yearMonthStr: string, warnDefine: WarnSettingList[]) {
  const startDate = dayjs(`${yearMonthStr}-01`);
  const currentMonth = dayjs().format('YYYY-MM');
  let endDate;

  // 如果是当前月份，结束日期为今天，否则为该月的最后一天
  if (yearMonthStr === currentMonth) {
    endDate = dayjs();
  } else {
    endDate = startDate.endOf('month');
  }

  const data = [];

  for (
    let date = startDate;
    date.isBefore(endDate) || date.isSame(endDate);
    date = date.add(1, 'day')
  ) {
    const dailyData: any = {
      date: date.format('YYYY-MM-DD'),
      /** 已解决的警告 */
      resolved: Mock.Random.integer(0, 100),
    };

    warnDefine.forEach((warning) => {
      const key = warning.projectWarningId || '';
      dailyData[key] = Mock.Random.integer(0, 100); // 现在这里不会有 TypeScript 错误
    });

    data.push(dailyData);
  }

  const warnSituations: any = {};

  warnDefine.forEach((warning) => {
    const key = warning.projectWarningId || '';
    warnSituations[key] = Mock.Random.integer(0, 100); // 现在这里不会有 TypeScript 错误
  });

  return {
    warnSituations,
    dailyWarnSituations: data,
  };
}

export function mockFetchStatisticsLogin(yearMonth: string) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const data = generateLoginData(yearMonth);
      resolve(data);
    }, 500);
  });
}

export function mockFetchStatisticsUsage(yearMonth: string) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const data = generateUsageData(yearMonth);
      resolve(data);
    }, 500);
  });
}

export function mockFetchStatisticsWarn(
  yearMonth: string,
  warnDefine: WarnSettingList[],
) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const data = generateWarnData(yearMonth, warnDefine);
      resolve(data);
    }, 500);
  });
}
