/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ThirdPartyUserInfo, ThirdPartyVehicleInfo } from '../third-party-data';

/**
 * 模拟第三方人员数据
 */
export const mockThirdPartyPersonnel: ThirdPartyUserInfo[] = [
  {
    userId: 1001,
    deviceId: 'DEV001',
    location: {
      latitude: 26.0823,
      longitude: 119.2983,
      locationType: 'GPS',
      radius: 10,
      altitude: 100,
      direction: 45,
      speed: 30,
      time: Date.now(),
      x: 436842.0645,
      y: 2881127.3818,
    },
    userInfo: {
      userName: '张**',
      cellphone: '138****8001',
      workno: 'EMP001',
      orgInfo: {
        orgKey: 'ORG001',
        orgId: 1,
        parentOrgId: 0,
        orgName: '**市***局',
      },
    },
  },
  {
    userId: 1002,
    deviceId: 'DEV002',
    location: {
      latitude: 26.0933,
      longitude: 119.3093,
      locationType: 'GPS',
      radius: 10,
      altitude: 100,
      direction: 90,
      speed: 0,
      time: Date.now(),
    },
    userInfo: {
      userName: '李**',
      cellphone: '138****8002',
      workno: 'EMP002',
      orgInfo: {
        orgKey: 'ORG002',
        orgId: 2,
        parentOrgId: 1,
        orgName: '**市***局-运维部',
      },
    },
  },
  {
    userId: 1003,
    deviceId: 'DEV003',
    location: {
      latitude: 26.0743,
      longitude: 119.3203,
      locationType: 'GPS',
      radius: 10,
      altitude: 100,
      direction: 180,
      speed: 15,
      time: Date.now(),
      x: 437679.1913,
      y: 2884235.6831,
    },
    userInfo: {
      userName: '王**',
      cellphone: '138****8003',
      workno: 'EMP003',
      orgInfo: {
        orgKey: 'ORG003',
        orgId: 3,
        parentOrgId: 1,
        orgName: '**市***局-工程部',
      },
    },
  },
  {
    userId: 1004,
    deviceId: 'DEV004',
    location: {
      latitude: 26.0653,
      longitude: 119.2913,
      locationType: 'GPS',
      radius: 10,
      altitude: 100,
      direction: 270,
      speed: 5,
      time: Date.now(),
    },
    userInfo: {
      userName: '赵**',
      cellphone: '138****8004',
      workno: 'EMP004',
      orgInfo: {
        orgKey: 'ORG004',
        orgId: 4,
        parentOrgId: 1,
        orgName: '**市***局-调度部',
      },
    },
  },
  {
    userId: 1005,
    deviceId: 'DEV005',
    location: {
      latitude: 26.0963,
      longitude: 119.2823,
      locationType: 'GPS',
      radius: 10,
      altitude: 100,
      direction: 135,
      speed: 20,
      time: Date.now(),
      x: 437672.1901,
      y: 2884212.682,
    },
    userInfo: {
      userName: '钱**',
      cellphone: '138****8005',
      workno: 'EMP005',
      orgInfo: {
        orgKey: 'ORG005',
        orgId: 5,
        parentOrgId: 1,
        orgName: '**市***局-客服部',
      },
    },
  },
];

/**
 * 模拟第三方车辆数据
 */
export const mockThirdPartyVehicles: ThirdPartyVehicleInfo[] = [
  {
    vehicleId: 'V001',
    vehicleNickName: '工程车1号',
    vin: 'LSVAM4187C2184841',
    corpId: 'C001',
    deptId: 'D001',
    deptName: '**市***局-工程部',
    productName: '工程车',
    brandName: '东风',
    licenseplate: '浙G****1',
    deviceId: 'DEV001',
    bindDevice: '1',
    onlineStatus: 2,
    location: {
      longitude: 119.3153,
      latitude: 26.0723,
      direction: 45,
      speed: 30,
      posTime: new Date().toISOString(),
      posMethod: 1,
    },
    distanceTotal: '10000',
    fuelConsumption: '5000',
  },
  {
    vehicleId: 'V002',
    vehicleNickName: '运维车1号',
    vin: 'LSVAM4187C2184842',
    corpId: 'C001',
    deptId: 'D002',
    deptName: '**市***局-运维部',
    productName: '运维车',
    brandName: '江淮',
    licenseplate: '浙G****2',
    deviceId: 'DEV002',
    bindDevice: '1',
    onlineStatus: 1,
    location: {
      longitude: 119.3253,
      latitude: 26.0833,
      direction: 90,
      speed: 0,
      posTime: new Date().toISOString(),
      posMethod: 1,
      x: 436717.2083,
      y: 2888614.3865,
    },
    distanceTotal: '8000',
    fuelConsumption: '4000',
  },
  {
    vehicleId: 'V003',
    vehicleNickName: '调度车1号',
    vin: 'LSVAM4187C2184843',
    corpId: 'C001',
    deptId: 'D003',
    deptName: '**市***局-调度部',
    productName: '调度车',
    brandName: '福田',
    licenseplate: '浙G****3',
    deviceId: 'DEV003',
    bindDevice: '1',
    onlineStatus: 2,
    location: {
      longitude: 119.3353,
      latitude: 26.0943,
      direction: 180,
      speed: 15,
      posTime: new Date().toISOString(),
      posMethod: 1,
    },
    distanceTotal: '12000',
    fuelConsumption: '6000',
  },
  {
    vehicleId: 'V004',
    vehicleNickName: '客服车1号',
    vin: 'LSVAM4187C2184844',
    corpId: 'C001',
    deptId: 'D004',
    deptName: '**市***局-客服部',
    productName: '客服车',
    brandName: '长安',
    licenseplate: '浙G****4',
    deviceId: 'DEV004',
    bindDevice: '1',
    onlineStatus: 0,
    location: {
      longitude: 119.3453,
      latitude: 26.1053,
      direction: 270,
      speed: 5,
      posTime: new Date().toISOString(),
      posMethod: 1,
      x: 437010.6557,
      y: 2887385.1357,
    },
    distanceTotal: '5000',
    fuelConsumption: '2500',
  },
  {
    vehicleId: 'V005',
    vehicleNickName: '工程车2号',
    vin: 'LSVAM4187C2184845',
    corpId: 'C001',
    deptId: 'D001',
    deptName: '**市***局-工程部',
    productName: '工程车',
    brandName: '东风',
    licenseplate: '浙G****5',
    deviceId: 'DEV005',
    bindDevice: '1',
    onlineStatus: 2,
    location: {
      longitude: 119.3553,
      latitude: 26.1163,
      direction: 135,
      speed: 20,
      posTime: new Date().toISOString(),
      posMethod: 1,
    },
    distanceTotal: '15000',
    fuelConsumption: '7500',
  },
];

// 高德坐标点
const FUZHOU_COORDINATES = [
  [119.306239, 26.075302], // 鼓楼区
  [119.322382, 26.082284], // 台江区
  [119.273841, 26.062155], // 仓山区
  [119.352761, 26.082284], // 马尾区
  [119.196726, 26.074508], // 晋安区
  [119.537362, 26.210273], // 长乐区
  [119.537362, 26.210273], // 闽侯县
  [119.537362, 26.210273], // 连江县
  [119.537362, 26.210273], // 罗源县
  [119.537362, 26.210273], // 闽清县
  [119.537362, 26.210273], // 永泰县
  [119.537362, 26.210273], // 平潭县
  [119.537362, 26.210273], // 福清市
];

// 生成随机坐标偏移
const getRandomOffset = () => {
  const offset = Math.random() * 0.01 - 0.005; // 生成 -0.005 到 0.005 之间的随机数
  return offset;
};

// 生成随机速度
const getRandomSpeed = () => Math.floor(Math.random() * 60); // 0-60 km/h

// 生成随机方向
const getRandomDirection = () => Math.floor(Math.random() * 360); // 0-359 度

// 生成随机时间戳（最近24小时内）
const getRandomTime = () => {
  const now = Date.now();
  const randomTime = now - Math.floor(Math.random() * 24 * 60 * 60 * 1000);
  return new Date(randomTime).toISOString();
};

// 生成新的坐标
const getNewCoordinate = (baseCoordinate: number[]) => [
  baseCoordinate[0] + getRandomOffset(),
  baseCoordinate[1] + getRandomOffset(),
];

// 生成新的车辆数据
export const mockThirdPartyVehiclesNew = () =>
  mockThirdPartyVehicles.map((vehicle, index) => {
    const baseCoordinate =
      FUZHOU_COORDINATES[index % FUZHOU_COORDINATES.length];
    const newCoordinate = getNewCoordinate(baseCoordinate);
    return {
      ...vehicle,
      location: {
        longitude: newCoordinate[0],
        latitude: newCoordinate[1],
        x: newCoordinate[0],
        y: newCoordinate[1],
        speed: getRandomSpeed(),
        direction: getRandomDirection(),
        posTime: getRandomTime(),
        posMethod: 1,
      },
    };
  });

// 生成新的人员数据
export const mockThirdPartyPersonnelNew = () =>
  mockThirdPartyPersonnel.map((person, index) => {
    const baseCoordinate =
      FUZHOU_COORDINATES[index % FUZHOU_COORDINATES.length];
    const newCoordinate = getNewCoordinate(baseCoordinate);
    return {
      ...person,
      location: {
        latitude: newCoordinate[1],
        longitude: newCoordinate[0],
        locationType: 'GPS',
        radius: 10,
        altitude: 0,
        direction: getRandomDirection(),
        speed: getRandomSpeed(),
        time: Date.now(),
        x: newCoordinate[0],
        y: newCoordinate[1],
      },
    };
  });
