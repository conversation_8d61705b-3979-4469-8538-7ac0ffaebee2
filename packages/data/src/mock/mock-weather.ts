/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const mockCurrentDay = {
  weather: '晴',
  weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/00.png',
  minTemperature: '31',
  maxTemperature: '32',
  date: '20230726',
};

export const mockForecast = [
  {
    weather: '晴晴晴晴晴晴晴',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/00.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: 'Invalid Date',
  },
  {
    weather: '晴',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/00.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230727',
  },
  {
    weather: '多云',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/01.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230728',
  },
  {
    weather: '小雨',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/07.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230729',
  },
  {
    weather: '小雨',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/07.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230730',
  },
  {
    weather: '晴',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/00.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230731',
  },
  {
    weather: '小雨',
    weatherIconUrl: 'http://app1.showapi.com/weather/icon/day/07.png',
    minTemperature: '31',
    maxTemperature: '32',
    date: '20230801',
  },
];
