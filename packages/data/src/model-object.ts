/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { IndicatorObject } from './indicator';
import {
  getShapeType,
  IObjectItem,
  makeObjectId,
  SHAPE_TYPE,
} from './object-item';

export default class ModelObject implements IObjectItem {
  constructor(otype: string, oname: string, shape: string, db?: Database) {
    this._id = makeObjectId(otype, oname);
    this._otype = otype;
    this._oname = oname;
    this._title = oname;
    this._shape = shape;
    this._indicators = [];
    this._shapeType = getShapeType(shape);
    this.initIcon(db);
    this.initializeIndicators(db);
  }

  private _id: string;

  get id(): string {
    return this._id;
  }

  private _oname: string;

  get oname(): string {
    return this._oname;
  }

  private _otype: string;

  get otype(): string {
    return this._otype;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _shape: string;

  get shape(): string {
    return this._shape;
  }

  private _shapeType: SHAPE_TYPE;

  get shapeType(): SHAPE_TYPE {
    return this._shapeType;
  }

  private _indicators: Array<IndicatorObject>;

  // eslint-disable-next-line class-methods-use-this
  get indicators(): Array<IndicatorObject> {
    return this._indicators;
  }

  private _highlightIcon: string | undefined;

  get highlightIcon(): string | undefined {
    return this._highlightIcon;
  }

  addIndicator(indicator: IndicatorObject) {
    this._indicators.push(indicator);
  }

  private initIcon(db?: Database) {
    if (this._shapeType === 'LINE') return;
    if (this._otype === 'WDM_JUNCTIONS') return;
    this._highlightIcon = db?.icons.get(this._otype);
  }

  private initializeIndicators(db?: Database) {
    const indicators = db?.getIndicatorGroup(this.otype, this.oname);
    if (indicators)
      indicators.forEach((indicator) => this.addIndicator(indicator));
  }
}
