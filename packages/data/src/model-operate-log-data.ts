/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface GetModelOperateLogParams {
  current: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  operateType?: string;
  modelType?: string;
  userName?: string;
}

export interface ModelOperateLog {
  id: string;
  createTime: string | undefined;
  modelId: string;
  modelType: string;
  operateType: string;
  startTime: string;
  endTime: string;
  remark: string;
  userName: string;
}

export const MODEL_TYPE = [
  {
    label: '在线',
    value: 'ONLINE',
  },
  {
    label: '方案',
    value: 'SOLUTION',
  },
];

export const OPERATE_TYPE = [
  {
    label: '模型上传',
    value: '模型上传',
  },
  {
    label: '模型下载',
    value: '模型下载',
  },
  {
    label: '方案上传',
    value: '方案上传',
  },
  {
    label: '方案下载',
    value: '方案下载',
  },
];
