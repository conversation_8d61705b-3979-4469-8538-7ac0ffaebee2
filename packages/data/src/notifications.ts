/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Dayjs } from 'dayjs';

export enum NotificationType {
  /** 检查是否需要交班 */
  CHECK_HAND_OFF = 'CHECK_HAND_OFF',
  /** 检查是否需要接班 */
  CHECK_TAKE_OFF = 'CHECK_TAKE_OFF',
  /** 检查计划中的事件是否到开始时间 */
  CHECK_PLAN_EVENT_START = 'CHECK_PLAN_EVENT_START',
}

export interface Notifications {
  id: string;
  type: NotificationType;
  // triggerTime 表示通知触发的时间。当当前时间达到此时间时，将展示通知。
  triggerTime: Dayjs;
  // reloadTime 表示通知销毁的时间。在此时间到达时，通知将被销毁，并重新加载相关业务逻辑。
  reloadTime: Dayjs;
  onTrigger?: () => void;
  onReload?: () => void;
}
