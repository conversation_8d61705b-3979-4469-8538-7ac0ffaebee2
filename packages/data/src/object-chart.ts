/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import lodash from 'lodash';
import { MarkInfoItem, MarkInfoList } from './chart-mark';
import Database from './database';
import {
  getChartProperties,
  getChartPropertiesByConfig,
  getChartPropertiesByIndicator,
} from './device';
import { EventSchedulingBasicInfo } from './event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from './event-scheduling/related-info';
import { IObjectItem, makeId } from './object-item';
import { ObjectChartProperty } from './property/property-info';
import {
  GroupTimeData,
  getRangeValues,
  ObjectTimeDataSeriesNew,
  TimeData,
} from './time-data';
import { UnitFormat } from './unit-system';
import { formatNumber, getMinMax, isTimeBetween } from './utils';
import { ChartWarnInfo } from './warn';

export enum ChartGroup {
  timeChart = 'timeChart',
  dailyChart = 'dailyChart',
}
export enum TimeRangeType {
  oneDay = '1day',
  threeDays = '3days',
  sevenDays = '7days',
  oneMonth = '1months',
  threeMonths = '3months',
  oneYear = '1year',
  customize = 'timeRangeCustomize',
  dateCustomize = 'dateCustomize',
}

export enum TimeStepType {
  oneMinute = 60,
  fiveMinutes = 300,
  fifteenMinutes = 900,
  thirtyMinutes = 1800,
  oneHour = 3600,
}

export enum DailyRangeType {
  oneMonth = '1month',
  threeMonths = '3months',
  customize = 'dailyRangeCustomize',
}

/**
 * ringComparison 环比
 *
 * yearOnYear 同比
 *
 * custom 自定义
 *
 * chainBase 多日对比
 */
export enum ChartCompareType {
  ringComparison = 'ringComparison',
  yearOnYear = 'yearOnYear',
  custom = 'custom',
  chainBase = 'chainBase',
}

export type AxisDirection = 'left' | 'right';

export type AxisDataItem = {
  title: string;
  type: string;
  prop: string;
  data: Array<ObjectTimeDataSeriesNew>;
  unitFormat?: UnitFormat;
  isDailyChart: boolean;
  propertyName: string;
  direction: AxisDirection;
  yMinValue?: number;
  yMaxValue?: number;
};

export type TableDataItemValue = number | string | null;

export type TableData = {
  time: string;
  [index: string]: TableDataItemValue;
};

export type ToolboxName =
  | 'dataZoom'
  | 'myChartMode'
  | 'dataView'
  | 'restore'
  | 'myDownload'
  | 'brush';

export const defaultToolboxNames: ToolboxName[] = [
  'dataZoom',
  'myChartMode',
  'dataView',
  'restore',
  'myDownload',
  'brush',
];

export type XAxisType = 'timely' | 'daily' | 'singleDay';

export function getTimeRangeByType(
  timeRangeType: string,
  currentDateTime: string = dayjs().format('YYYY-MM-DD HH:mm:ss'),
): [Dayjs, Dayjs] {
  const startDateTime = dayjs(
    dayjs(currentDateTime).format('YYYY-MM-DD 00:00:00'),
  );
  const endDateTime = dayjs(dayjs(startDateTime).format('YYYY-MM-DD 23:59:59'));
  switch (timeRangeType) {
    case TimeRangeType.threeDays:
      return [startDateTime.add(-2, 'day'), endDateTime];
    case TimeRangeType.sevenDays:
      return [startDateTime.add(-6, 'day'), endDateTime];
    case TimeRangeType.oneYear:
      return [startDateTime.add(-1, 'year'), endDateTime];
    case TimeRangeType.customize:
      return [startDateTime.add(-1, 'month'), endDateTime];
    case TimeRangeType.oneMonth:
    case DailyRangeType.oneMonth:
      return [startDateTime.add(-1, 'month'), endDateTime];
    case TimeRangeType.threeMonths:
      return [startDateTime.add(-3, 'month'), endDateTime];
    case DailyRangeType.customize:
      return [startDateTime.add(-6, 'month'), endDateTime];
    case TimeRangeType.oneDay:
    case TimeRangeType.dateCustomize:
    default:
      return [startDateTime, endDateTime];
  }
}

export function getTimeRangeTypeName(timeRangeType: TimeRangeType): string {
  switch (timeRangeType) {
    case TimeRangeType.customize:
      return '自定义';
    case TimeRangeType.oneDay:
      return '近一天';
    case TimeRangeType.oneMonth:
      return '近一月';
    case TimeRangeType.oneYear:
      return '近一年';
    case TimeRangeType.sevenDays:
      return '近七天';
    case TimeRangeType.threeDays:
      return '近三天';
    case TimeRangeType.threeMonths:
      return '近三月';
    default:
      return timeRangeType;
  }
}

export function getTimeStepName(type: TimeStepType): string {
  switch (type) {
    case TimeStepType.oneMinute:
      return '实时';
    case TimeStepType.fiveMinutes:
      return '5分钟';
    case TimeStepType.fifteenMinutes:
      return '15分钟';
    case TimeStepType.thirtyMinutes:
      return '30分钟';
    case TimeStepType.oneHour:
      return '1小时';
    default:
      return type;
  }
}

export const getSeriesType = (
  type: string | undefined,
  showAsLineType: boolean | undefined,
): 'line' | 'scatter' | 'bar' => {
  if (showAsLineType && type !== 'bar') return 'line';
  if (type === 'scatter' || type === 'line' || type === 'bar') return type;
  return 'line';
};

export const doubleValueFormatter = (
  value: string | number,
  unitFormat?: UnitFormat,
): string | number => {
  if (typeof unitFormat !== 'undefined' && unitFormat?.unitType === 'D')
    return unitFormat.getValue(value) as number;
  return value;
};

export const enumValueFormatter = (
  value: string | number,
  unitFormat?: UnitFormat,
): string | number => {
  if (typeof unitFormat !== 'undefined' && unitFormat.unitType === 'E')
    return unitFormat.getValue(value) ?? '';
  return value;
};

export const valueFormatter = (
  value: string | number,
  unitFormat?: UnitFormat,
): string | number => {
  if (typeof unitFormat !== 'undefined') {
    switch (unitFormat?.unitType) {
      case 'D':
        return doubleValueFormatter(value, unitFormat);
      case 'E':
        return enumValueFormatter(value, unitFormat);
      default:
        return unitFormat.getValue(value) ?? '';
    }
  }
  return value;
};

export const getSeriesRangeValues = (
  objectTimeDataSeries: Array<ObjectTimeDataSeriesNew>,
): [number, number] => {
  const data: Array<number> = [];
  objectTimeDataSeries.forEach((timeDataSeries) => {
    timeDataSeries.series.forEach((timeDataArray) => {
      if (timeDataArray.timeData.length > 0) {
        const values = timeDataArray.timeData.map((item) => item.value);

        // 使用循环避免调用栈溢出，同时提高性能
        const { min, max } = getMinMax(values);

        data.push(
          timeDataArray.unitFormat
            ? (timeDataArray.unitFormat.getValue(min) as number)
            : min,
        );
        data.push(
          timeDataArray.unitFormat
            ? (timeDataArray.unitFormat.getValue(max) as number)
            : max,
        );
      }
    });
  });

  return getRangeValues(data);
};

export const isYScale = (
  objectTimeDataSeries: Array<ObjectTimeDataSeriesNew>,
  unitFormat: UnitFormat | undefined,
): boolean => {
  if (unitFormat && unitFormat.unitType === 'D') {
    const yRange = getSeriesRangeValues(objectTimeDataSeries);
    const [min, max] = yRange;
    return min > 1 || max < -1 || (min > 0 && max < 1) || (min > -1 && max < 0);
  }

  return false;
};

export const getYAxisName = (
  propertyName: string,
  direction: 'left' | 'right',
  unitFormat?: UnitFormat,
): string => {
  let name = propertyName;
  if (unitFormat?.unitSymbol) name = `${unitFormat?.unitSymbol}`;
  // pad space to make sure the Y axis name display fully
  if (name.length > 4) {
    if (direction === 'left') name = name.padStart(name.length * 3, ' ');
    else name = name.padEnd(name.length * 3, ' ');
  }
  return name;
};

export const hasScatterType = (axisData: AxisDataItem[]): boolean => {
  let flag = false;
  axisData.forEach(({ data }) => {
    data.forEach(({ series }) => {
      series.forEach(({ type }) => {
        if (type === 'scatter') flag = true;
      });
    });
  });
  return flag;
};

export const getYAxisMaxValue = (
  yMaxValue: number | undefined | null,
  unitFormat: UnitFormat | undefined,
): string | number | undefined => {
  const yAxisValues = unitFormat?.getYAxisValues();
  let yMax;
  if (yMaxValue !== undefined && yMaxValue !== null) yMax = yMaxValue;
  else if (yAxisValues && yAxisValues.length > 0)
    yMax = yAxisValues[yAxisValues.length - 1];
  return yMax;
};

const generateStepData = <T>(
  timeStep: number,
  fakeTime: string,
  list?: (T & { startTime: string })[],
): Map<string, T[]> => {
  const fakeDate = dayjs(dayjs(fakeTime).format('YYYY-MM-DD 00:00:00'));
  const timeDataMap: Map<string, T[]> = new Map();
  list?.forEach((i) => {
    const minute = dayjs(i.startTime).minute();
    const step = timeStep / 60;
    const time = dayjs(dayjs(i.startTime).format('YYYY-MM-DD HH:00:00'));
    const diffDays = time.diff(fakeDate, 'd');
    const fakeTime = `${fakeDate
      .add(diffDays, 'd')
      .format('YYYY-MM-DD')} ${dayjs(i.startTime).format('HH:mm:ss')}`;
    const timeIndex = dayjs(fakeTime)
      .subtract(minute % step, 'minutes')
      .format('YYYY-MM-DD HH:mm:00');

    const currentData = timeDataMap.get(timeIndex);
    if (currentData) {
      currentData.push(i);
    } else {
      timeDataMap.set(timeIndex, [i]);
    }
  });
  return timeDataMap;
};

const getMarkListDescription = (
  markList: MarkInfoItem[] | undefined,
): string => {
  if (markList) {
    return markList
      .map((item) => `${item.startTime}~${item.endTime}:${item.description}`)
      .toString();
  }
  return '';
};

const getEventListDescription = (
  eventList: EventSchedulingBasicInfo[] | undefined,
): string => {
  if (eventList?.length) {
    return eventList.map((item) => item.eventTitle).toString();
  }
  return '';
};

const getEventRelatedListDescription = (
  eventRelatedList: EventSchedulingRelatedInfo[] | undefined,
): string => {
  if (eventRelatedList?.length) {
    return eventRelatedList.map((item) => item.relatedDescription).toString();
  }
  return '';
};

const getWarnListDescription = (
  warnList: ChartWarnInfo[] | undefined,
): string => {
  if (warnList) {
    return warnList
      .map((item) => `${item.startTime}~${item.endTime}:${item.description}`)
      .toString();
  }
  return '';
};

const generateTableTimeSeriesData = (
  timeData: TimeData[],
  timeStep: number,
  startDateTime: string,
  endDateTime: string,
  markList?: MarkInfoList,
  warnList?: ChartWarnInfo[],
  unitFormat?: UnitFormat,
): TableData[] => {
  const timeDataMap: Map<string, number | string> = new Map();
  timeData.forEach((i) => {
    timeDataMap.set(i.time, valueFormatter(i.value, unitFormat));
  });
  const warnData = generateStepData<ChartWarnInfo>(
    timeStep,
    startDateTime,
    warnList,
  );
  const markData = generateStepData<MarkInfoItem>(
    timeStep,
    startDateTime,
    markList,
  );
  const step = timeStep / 60;
  const diffDay =
    Math.ceil(dayjs(endDateTime).diff(startDateTime, 'day', true)) + 1;
  const timeSeriesData: TableData[] = [];
  const count = (diffDay * 1440) / step;
  const startTime = dayjs(dayjs(startDateTime).format('YYYY-MM-DD 00:00:00'));

  for (let i = 0; i < count; i += 1) {
    const time = startTime
      .add(step * i, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    if (isTimeBetween([startDateTime, endDateTime], time)) {
      if (timeDataMap.has(time)) {
        timeSeriesData.push({
          time,
          markList: getMarkListDescription(markData.get(time)),
          warnList: getWarnListDescription(warnData.get(time)),
          value: timeDataMap.get(time) as number,
        });
      } else {
        timeSeriesData.push({
          time,
          value: null,
        });
      }
    }
  }
  return timeSeriesData;
};

const generateTimeSeriesData = (
  timeData: TimeData[],
  timeStep: number,
  startDateTime: string,
  endDateTime: string,
  unitFormat?: UnitFormat,
): TableData[] => {
  const timeDataMap: Map<string, number | string> = new Map();
  timeData.forEach((i) => {
    timeDataMap.set(i.time, valueFormatter(i.value, unitFormat));
  });
  const step = timeStep / 60;
  const diffDay =
    Math.ceil(dayjs(endDateTime).diff(startDateTime, 'day', true)) + 1;
  const timeSeriesData: TableData[] = [];
  const count = (diffDay * 1440) / step;
  const startTime = dayjs(dayjs(startDateTime).format('YYYY-MM-DD 00:00:00'));

  for (let i = 0; i < count; i += 1) {
    const time = startTime
      .add(step * i, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    if (isTimeBetween([startDateTime, endDateTime], time)) {
      if (timeDataMap.has(time)) {
        timeSeriesData.push({
          time,
          value: timeDataMap.get(time) as number,
        });
      } else {
        timeSeriesData.push({
          time,
          value: null,
        });
      }
    }
  }
  return timeSeriesData;
};

export const getDataSource = (
  axisData: AxisDataItem[],
  timeStep: number,
  startDateTime: string,
  endDateTime: string,
): TableData[] => {
  let dataSource: TableData[] = [];

  const dataSourceGroup: TableData[][] = [];
  axisData.forEach((axisDataItem, i) => {
    const { type, prop, data, unitFormat } = axisDataItem;
    data.forEach((dataItem, j) => {
      const { oname } = dataItem;

      const dataIndex = makeId(type, oname, prop, `${i}`, `${j}`);

      const foundSeries = dataItem?.series.find((i) => i.dataType === 'scada');
      const timeSeriesData = generateTimeSeriesData(
        foundSeries?.timeData ?? [],
        timeStep,
        startDateTime,
        endDateTime,
        foundSeries?.unitFormat ?? unitFormat,
      );
      const timeData: TableData[] =
        timeSeriesData.map((item) => ({
          time: item.time,
          [dataIndex]: item.value,
        })) ?? [];
      dataSourceGroup.push(timeData);
    });
  });

  const groupedArrays = lodash.groupBy(dataSourceGroup.flat(), 'time');

  dataSource = Object.values(groupedArrays)
    .reduce((prev, curr) => {
      const curDataItem = curr.reduce((p, c): TableData => ({ ...p, ...c }), {
        time: '',
      });
      return [...prev, curDataItem];
    }, [])
    .sort((a, b) => dayjs(a.time).unix() - dayjs(b.time).unix());
  return dataSource;
};

export const exportTableDataSource = (
  axisData: AxisDataItem[],
  timeStep: number,
  startDateTime: string,
  endDateTime: string,
  eventList?: EventSchedulingBasicInfo[],
  eventRelatedList?: EventSchedulingRelatedInfo[],
): TableData[] => {
  let dataSource: TableData[] = [];

  const dataSourceGroup: TableData[][] = [];
  axisData.forEach((axisDataItem, i) => {
    const { type, prop, data, unitFormat } = axisDataItem;
    data.forEach((dataItem, j) => {
      const { oname, markList, warnList } = dataItem;
      const dataIndex = makeId(type, oname, prop, `${i}`, `${j}`);
      const markListDataIndex = makeId(dataIndex, 'markList');
      const warnListDataIndex = makeId(dataIndex, 'warnList');
      const foundSeries = dataItem?.series.find((i) => i.dataType === 'scada');
      const timeSeriesData = generateTableTimeSeriesData(
        foundSeries?.timeData ?? [],
        timeStep,
        startDateTime,
        endDateTime,
        markList,
        warnList,
        foundSeries?.unitFormat ?? unitFormat,
      );
      const timeData: TableData[] =
        timeSeriesData.map((item) => ({
          time: item.time,
          [markListDataIndex]: item.markList,
          [warnListDataIndex]: item.warnList,
          [dataIndex]: item.value,
        })) ?? [];
      dataSourceGroup.push(timeData);
    });
  });

  generateStepData<EventSchedulingRelatedInfo>(
    timeStep,
    startDateTime,
    eventRelatedList?.map((item) => ({
      ...item,
      startTime: item.relatedTime,
    })),
  ).forEach((eventRelatedList, time) => {
    dataSourceGroup.push([
      {
        time,
        eventTitle: getEventRelatedListDescription(eventRelatedList),
      },
    ]);
  });

  generateStepData<EventSchedulingBasicInfo>(
    timeStep,
    startDateTime,
    eventList?.map((item) => ({ ...item, startTime: item.eventStartTime })),
  ).forEach((eventList, time) => {
    dataSourceGroup.push([
      {
        time,
        eventTitle: getEventListDescription(eventList),
      },
    ]);
  });

  const groupedArrays = lodash.groupBy(dataSourceGroup.flat(), 'time');

  dataSource = Object.values(groupedArrays)
    .reduce((prev, curr) => {
      const curDataItem = curr.reduce((p, c): TableData => ({ ...p, ...c }), {
        time: '',
      });
      return [...prev, curDataItem];
    }, [])
    .sort((a, b) => dayjs(a.time).unix() - dayjs(b.time).unix());
  return dataSource;
};

export const getExportColumns = (
  axisData: AxisDataItem[],
): Array<{ title: string; dataIndex: string }> => {
  const columns: { title: string; dataIndex: string }[] = [
    {
      title: '日期',
      dataIndex: 'time',
    },
  ];

  axisData.forEach((axisDataItem, i) => {
    const { type, prop, unitFormat, data } = axisDataItem;
    data.forEach((dataItem, j) => {
      const { oname } = dataItem;
      const unitSymbol = unitFormat?.unitSymbol
        ? ` (${unitFormat?.unitSymbol})`
        : '';
      // 通过 i、j解决oname重复问题
      const dataIndex = makeId(type, oname, prop, `${i}`, `${j}`);
      const markListDataIndex = makeId(dataIndex, 'markList');
      const warnListDataIndex = makeId(dataIndex, 'warnList');
      const foundSeries = dataItem?.series.find((i) => i.dataType === 'scada');
      if (foundSeries) {
        const { name } = foundSeries;
        columns.push(
          ...[
            {
              title: `${name}${unitSymbol}`,
              dataIndex,
            },
            {
              title: `${name}-标注`,
              dataIndex: markListDataIndex,
            },
            {
              title: `${name}-警告`,
              dataIndex: warnListDataIndex,
            },
          ],
        );
      }
    });
  });

  columns.push({
    title: '事件',
    dataIndex: 'eventTitle',
  });

  return columns;
};

type SummaryValueList = Array<{
  value: number | string | undefined;
  dataIndex: number | string | undefined;
}>;

export const getSummary = (
  data: TableData[],
  columns: ReturnType<typeof getExportColumns>,
): {
  summaryMaxValue: SummaryValueList;
  summaryMinValue: SummaryValueList;
  summaryAverageValue: SummaryValueList;
} => {
  const summaryMaxValue: SummaryValueList = [];
  const summaryMinValue: typeof summaryMaxValue = [];
  const summaryAverageValue: typeof summaryMaxValue = [];

  [...columns].forEach((item) => {
    const { dataIndex } = item;
    if (dataIndex === 'time') return;
    const valueList = [...data]
      .filter((f) => dataIndex && typeof f[dataIndex] === 'number')
      .map((dataItem) =>
        item.dataIndex ? (dataItem[dataIndex] as number) : undefined,
      );
    const maxValue = lodash.max(valueList);
    const minValue = lodash.min(valueList);
    const averageValue = formatNumber(lodash.mean(valueList), 4);
    summaryMaxValue.push({
      value: maxValue,
      dataIndex: dataIndex as string | number,
    });
    summaryMinValue.push({
      value: minValue,
      dataIndex: dataIndex as string | number,
    });
    summaryAverageValue.push({
      value: averageValue,
      dataIndex: dataIndex as string | number,
    });
  });

  return {
    summaryMaxValue,
    summaryMinValue,
    summaryAverageValue,
  };
};

const reduceObjectByList = (
  list: SummaryValueList,
  timeName: string,
): TableData =>
  list.reduce(
    (prev, curr) => ({
      ...prev,
      [curr.dataIndex as string]: curr.value,
    }),
    {
      time: timeName,
    },
  );

export const getExportSummary = (
  data: TableData[],
  columns: ReturnType<typeof getExportColumns>,
): TableData[] => {
  const { summaryMaxValue, summaryMinValue, summaryAverageValue } = getSummary(
    data,
    columns,
  );

  const summaryData = [];

  summaryData.push(reduceObjectByList(summaryMaxValue, '最大值'));
  summaryData.push(reduceObjectByList(summaryMinValue, '最小值'));
  summaryData.push(reduceObjectByList(summaryAverageValue, '平均值'));

  return summaryData;
};
export const getChartCompareTypeName = (type: string): string => {
  switch (type) {
    case ChartCompareType.custom:
      return '自定义';
    case ChartCompareType.yearOnYear:
      return '同比';
    case ChartCompareType.ringComparison:
      return '环比';
    case ChartCompareType.chainBase:
      return '多日对比';
    default:
      return type;
  }
};

export const getChartIndicatorsByConfig = (
  db: Database,
  object: IObjectItem,
): {
  otype: string;
  oname: string;
  vprop: string;
  direction?: 'left' | 'right';
  formData?: {
    timeRange: [Dayjs, Dayjs];
    timeStep?: number;
  };
}[] =>
  getChartPropertiesByConfig(db, object, 'chartConfig').map(
    ({ oname, otype, vprop }) => ({
      oname,
      otype,
      vprop,
    }),
  );

export const getMatchedChartPropertyByObject = (
  db: Database,
  selectedObject: IObjectItem,
  indicatorType: string | undefined,
  indicatorName: string | undefined,
  vprop: string | undefined,
): ObjectChartProperty | undefined => {
  const chartProperties: ObjectChartProperty[] = getChartProperties(
    db,
    selectedObject,
  );

  // 比较otype、oname、vprop
  const matchById = chartProperties.find(
    ({ indicatorOType, indicatorOName, vprop: propertyVprop }) =>
      indicatorOType === indicatorType &&
      indicatorOName === indicatorName &&
      propertyVprop === vprop,
  );
  if (matchById) return matchById;

  // 比较otype、vprop
  const matchByOtype = chartProperties.find(
    ({ indicatorOType, vprop: propertyVprop }) =>
      indicatorOType === indicatorType && propertyVprop === vprop,
  );
  if (matchByOtype) return matchByOtype;

  // 比较vprop
  const matchByVprop = chartProperties.find(
    ({ vprop: propertyVprop }) => propertyVprop === vprop,
  );
  if (matchByVprop) return matchByVprop;
  return undefined;
};

export const getMatchedChartProperty = (
  db: Database,
  selectedObject: IObjectItem,
  indicatorType: string | undefined,
  indicatorName: string | undefined,
  vprop: string | undefined,
): ObjectChartProperty | undefined => {
  const chartProperties: ObjectChartProperty[] = getChartProperties(
    db,
    selectedObject,
  );
  if (indicatorType && indicatorName && vprop) {
    const chartPropertyByIndicator = getChartPropertiesByIndicator(
      db,
      indicatorType,
      indicatorName,
      vprop,
    );
    if (chartPropertyByIndicator)
      chartProperties.push(chartPropertyByIndicator);
  }

  // 比较otype、oname、vprop
  const matchById = chartProperties.find(
    ({ indicatorOType, indicatorOName, vprop: propertyVprop }) =>
      indicatorOType === indicatorType &&
      indicatorOName === indicatorName &&
      propertyVprop === vprop,
  );
  if (matchById) return matchById;

  // 比较otype、vprop
  const matchByOtype = chartProperties.find(
    ({ indicatorOType, vprop: propertyVprop }) =>
      indicatorOType === indicatorType && propertyVprop === vprop,
  );
  if (matchByOtype) return matchByOtype;

  // 比较vprop
  const matchByVprop = chartProperties.find(
    ({ vprop: propertyVprop }) => propertyVprop === vprop,
  );
  if (matchByVprop) return matchByVprop;

  if (chartProperties.length) return chartProperties[0];
  return undefined;
};

export function getScadaRealDataByTimeStep(
  vprop: string,
  timeStep: number | undefined,
): string {
  if (typeof timeStep === 'number' && timeStep !== 60 && vprop === 'SDVAL') {
    return 'SDVAL_MATCH';
  }
  return vprop;
}

export function replaceIndicatorTitle(title: string, replace: string): string {
  const index = title.indexOf(replace);
  if (index !== -1) {
    const prefix = title.substring(0, index);
    const suffix = title.substring(index + replace.length);
    const newTitle = prefix + suffix;
    // 替换后的值如果是空，则返回原始值
    return newTitle || title;
  }
  return title;
}

export function getYoYDateRange(dateRange: [Dayjs, Dayjs]): [Dayjs, Dayjs] {
  const startTime = dateRange[0].subtract(1, 'year');
  const endTime = dateRange[1].subtract(1, 'year');
  return [startTime, endTime];
}
export function getQoQDateRange(
  dateRange: [Dayjs, Dayjs],
  cycle: number = 1,
): [Dayjs, Dayjs] {
  const diff = dateRange[1].diff(dateRange[0], 'd');
  const startTime = dateRange[0].subtract((diff + 1) * cycle, 'd');
  const endTime = dateRange[1].subtract((diff + 1) * cycle, 'd');
  return [startTime, endTime];
}
export function getCustomDateRange(
  dateRange: [Dayjs, Dayjs],
): Array<[Dayjs, Dayjs]> {
  const range: Array<[Dayjs, Dayjs]> = [];
  for (let i = 1; i <= 3; i += 1) {
    range.push(getQoQDateRange(dateRange, i));
  }
  return range;
}

export function getChainBaseDateRange(
  dateRange: [Dayjs, Dayjs],
): Array<[Dayjs, Dayjs]> {
  const range: Array<[Dayjs, Dayjs]> = [];
  const [startTime, endTime] = dateRange;
  const diff = endTime.diff(startTime, 'd');
  for (let i = 0; i <= diff; i += 1) {
    range.push([startTime.add(i, 'day'), endTime.add(i - diff, 'day')]);
  }
  return range;
}

export function timeLabelFormatter(value: number): string {
  const time = dayjs(value);

  if (time.isSame(dayjs(time).startOf('d'))) {
    // 零点显示成日期
    return dayjs(value).format('M/DD');
  }
  return dayjs(value).format('H:mm');
}

export function singleDayLabelFormatter(value: number): string {
  return dayjs(value).format('H:mm');
}

export function dayTimeLabelFormatter(value: number): string {
  const time = dayjs(value);
  if (time.isSame(dayjs(time).startOf('year'))) {
    // 一月一日显示年份
    return dayjs(value).format('YYYY');
  }
  if (time.isSame(dayjs(time).startOf('d'))) {
    return dayjs(value).format('M/DD');
  }
  return dayjs(value).format('H:mm');
}

export function xAxisLabelFormatter(value: number, type: XAxisType): string {
  switch (type) {
    case 'daily':
      return dayTimeLabelFormatter(value);
    case 'singleDay':
      return singleDayLabelFormatter(value);
    case 'timely':
    default:
      return timeLabelFormatter(value);
  }
}

export function timeFormatter(value: string, type: XAxisType): string {
  switch (type) {
    case 'daily':
      return dayjs(value).format('YYYY-MM-DD');
    case 'singleDay':
      return dayjs(value).format('HH:mm');
    case 'timely':
    default:
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
  }
}

export function getEnvelopUnitFormat(
  timeDataMap: Map<string, GroupTimeData>,
  db: Database,
): UnitFormat | undefined {
  const foundSdvalKey = [...timeDataMap.keys()].find((key) =>
    key.startsWith('SDVAL'),
  );
  if (foundSdvalKey) {
    const sdvalData = timeDataMap.get(foundSdvalKey)!;
    if (sdvalData) return db.getUnitFormat(sdvalData.otype, sdvalData.vprop);
  }
  return undefined;
}

/**
 * 将timeData中time按照chainBaseDate 对齐， 时间不变，只改变日期从chainBaseDate开始对齐
 * @param chainBaseDate
 * @param timeData
 */
export function convertChainBaseData(
  chainBaseDate: string | Dayjs,
  timeData: TimeData[],
): TimeData[] {
  if (timeData.length === 0) return timeData;
  const date = dayjs(dayjs(chainBaseDate).format('YYYY-MM-DD 00:00:00'));
  const diffDate = dayjs(date).diff(
    dayjs(timeData[0].time).format('YYYY-MM-DD 00:00:00'),
    'day',
  );
  return timeData.map((v) => {
    const newTime = dayjs(v.time)
      .add(diffDate, 'day')
      .format('YYYY-MM-DD HH:mm:ss');
    return {
      time: newTime,
      value: v.value,
    };
  });
}

export function getAxisDataTitle(
  title: string,
  timeRange: dayjs.Dayjs[],
  axisObjectsCount: number,
  chainBase?: boolean,
): string {
  let timeStr = '';
  if (chainBase) {
    timeStr = `${timeRange[0].format('YYYY/MM/DD')}`;
    return axisObjectsCount > 1 ? `${timeStr} ${title}` : `${timeStr}`;
  }
  return title;
}

export function getCorrelatedProps(
  chartProperty: ObjectChartProperty | undefined,
): string[] {
  const chartEditor = chartProperty?.editors.find(
    (item) => item.type === 'chart',
  );
  return chartEditor?.correlatedVprop ?? [];
}
