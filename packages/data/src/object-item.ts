/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IndicatorObject } from './indicator';
import { HighlightStyleType } from './style-config';

export type SHAPE_TYPE =
  | 'POINT'
  | 'LINE'
  | 'POLYGON'
  | 'MULTIPOLYGON'
  | 'UNKNOWN';

export type LINE_TYPE = 'default' | 'dash';

export interface IObjectItem {
  otype: string;
  oname: string;
  id: string;
  title: string;
  shape: string | undefined;
  shapeType: SHAPE_TYPE;
  indicators: Array<IndicatorObject>;
  highlightLineWidth?: number;
  highlightSize?: number;
  highlightType?: HighlightStyleType;
  highlightColor?: string;
  highlightIcon?: string;
  highlightText?: string;
  highlightTextBgColor?: string;
  value?: number;
  caliber?: number;
  lineType?: LINE_TYPE;
}

export function makeObjectId(otype: string, oname: string): string {
  return `${otype}@${oname}`;
}

export function splitObjectId(id: string): [string, string] {
  const [otype, oname] = id.split('@');
  return [otype, oname];
}

export function makeId(...args: string[]): string {
  return args.join('@');
}

export function splitId(id: string): string[] {
  return id.split('@');
}

export function getIObjectIds(iObjects: IObjectItem[]): string[] {
  return iObjects.map((item) => makeObjectId(item.otype, item.oname));
}

export function getShapeType(shape: string | undefined): SHAPE_TYPE {
  if (!shape) return 'UNKNOWN';
  if (shape.startsWith('POINT')) return 'POINT';
  if (shape.startsWith('LINE')) return 'LINE';
  if (shape.startsWith('POLYGON')) return 'POLYGON';
  if (shape.startsWith('MULTIPOLYGON')) return 'MULTIPOLYGON';
  return 'UNKNOWN';
}

function getXY(xy: string): number[] {
  const point = xy.trim().split(' ');
  if (point.length === 2) {
    const x = Number(point[0]);
    const y = Number(point[1]);
    if (Number.isNaN(x) || Number.isNaN(y)) return [];
    return [x, y];
  }
  return [];
}

function getLineCenter(coordinates: string): number[] {
  let sumX: number = 0;
  let sumY: number = 0;
  let count: number = 0;
  const line = coordinates.split(',');
  line.forEach((item) => {
    const xy = getXY(item);
    if (xy.length === 2) {
      sumX += xy[0];
      sumY += xy[1];
      count += 1;
    }
  });

  if (count > 0) {
    return [sumX / count, sumY / count];
  }

  return [];
}

export function getShapeCenter(shape: string): number[] {
  const shapeType: SHAPE_TYPE = getShapeType(shape);
  if (shapeType === 'UNKNOWN') return [];

  const coordinates = shape.substring(
    shape.indexOf('(') + 1,
    shape.lastIndexOf(')'),
  );
  switch (shapeType) {
    case 'POINT': {
      const xy: number[] = getXY(coordinates);
      if (xy.length === 2) return xy;
      break;
    }
    case 'LINE': {
      const center: number[] = getLineCenter(coordinates);
      if (center.length === 2) return center;
      break;
    }
    case 'POLYGON': {
      const polygon = coordinates.substring(
        coordinates.indexOf('(') + 1,
        coordinates.lastIndexOf(')'),
      );
      const center: number[] = getLineCenter(polygon);
      if (center.length === 2) return center;
      break;
    }
    case 'MULTIPOLYGON': {
      const polygon = shape.substring(
        shape.indexOf('(((') + 3,
        shape.lastIndexOf(')),'),
      );
      const center: number[] = getLineCenter(polygon);
      if (center.length === 2) return center;
      break;
    }
    default:
      break;
  }

  return [];
}

export function validateShape(shape: string | undefined): boolean {
  if (!shape) return false;
  const center = getShapeCenter(shape);
  if (center.length === 0) return false;
  return true;
}

export function getShapesCenter(shapes: Array<string>): number[] {
  let sumX: number = 0;
  let sumY: number = 0;
  let count: number = 0;
  shapes.forEach((item) => {
    const center = getShapeCenter(item);
    if (center.length === 2) {
      sumX += center[0];
      sumY += center[1];
      count += 1;
    }
  });

  if (count === 0) return [];

  return [sumX / count, sumY / count];
}

function createEmpty(): number[] {
  return [Infinity, Infinity, -Infinity, -Infinity];
}

function extendCoordinate(extent: number[], coordinate: number[]): number[] {
  const [minX, minY, maxX, maxY] = extent;
  const [x, y] = coordinate;
  const extendedExtent = [
    Math.min(minX, x),
    Math.min(minY, y),
    Math.max(maxX, x),
    Math.max(maxY, y),
  ];
  return extendedExtent;
}

function extend(extent1: number[], extent2: number[]) {
  const [min1, min2, max1, max2] = extent1;
  const [otherMin1, otherMin2, otherMax1, otherMax2] = extent2;

  const min = [Math.min(min1, otherMin1), Math.min(min2, otherMin2)];
  const max = [Math.max(max1, otherMax1), Math.max(max2, otherMax2)];

  return [...min, ...max];
}

function getLineExtent(coordinates: string): number[] {
  let extent: number[] = createEmpty();
  const line = coordinates.split(',');
  line.forEach((item) => {
    const xy = getXY(item);
    if (xy.length === 2) {
      extent = extendCoordinate(extent, xy);
    }
  });

  return extent;
}

export function getShapeExtent(shape: string): number[] {
  let extent: number[] = createEmpty();
  const shapeType: SHAPE_TYPE = getShapeType(shape);
  if (shapeType === 'UNKNOWN') return extent;

  const coordinates = shape.substring(
    shape.indexOf('(') + 1,
    shape.lastIndexOf(')'),
  );

  switch (shapeType) {
    case 'POINT': {
      const xy: number[] = getXY(coordinates);
      if (xy.length === 2) extent = extendCoordinate(extent, xy);
      break;
    }
    case 'LINE': {
      const lineExtent: number[] = getLineExtent(coordinates);
      extent = extend(extent, lineExtent);
      break;
    }
    case 'POLYGON': {
      const polygon = coordinates.substring(
        coordinates.indexOf('(') + 1,
        coordinates.lastIndexOf(')'),
      );
      const polygonExtent: number[] = getLineExtent(polygon);
      extent = extend(extent, polygonExtent);
      break;
    }
    case 'MULTIPOLYGON': {
      const polygon = shape.substring(
        shape.indexOf('(((') + 3,
        shape.lastIndexOf(')),'),
      );
      const polygonExtent: number[] = getLineExtent(polygon);
      extent = extend(extent, polygonExtent);
      break;
    }
    default:
      break;
  }

  return extent;
}

export function getShapesExtent(
  shapes: Array<string>,
  ratio: number,
): number[] {
  let extent: number[] = createEmpty();
  if (shapes.length === 0) return extent;

  shapes.forEach((item) => {
    const itemExtent: number[] = getShapeExtent(item);
    extent = extend(extent, itemExtent);
  });

  if (ratio > 0) {
    const width: number = extent[2] - extent[0];
    const height: number = extent[3] - extent[1];
    extent[0] -= (width / 2) * (ratio - 1);
    extent[1] -= (height / 2) * (ratio - 1);
    extent[2] += (width / 2) * (ratio - 1);
    extent[3] += (height / 2) * (ratio - 1);
  }

  return extent;
}
