/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { PropertyValue } from './device';
import { makeObjectId } from './object-item';

export enum ObservationType {
  SCADA = 'SCADA',
  MODEL = 'MODEL',
}

export enum ObservationBelong {
  DEFAULT = 'default',
  RAW = 'raw',
  RECLAIMED_WATER = 'reclaimed_water',
}

export interface ObservationScadaBaseItem {
  id: string;
  oname: string;
  otype: string;
  vprop: string;
  type: ObservationType;
  description?: string;
  observationValues: {
    id: string;
    startTime: string;
    endTime: string;
    max?: number;
    min?: number;
  }[];
  isPublic: boolean;
  createTime: string;
  updateTime: string;
}

export interface ObservationScadaItem extends ObservationScadaBaseItem {
  name?: string;
  value?: number | string;
  simulatedValue?: number | string;
  shape?: string;
  unit?: string;
  typeTitle?: string;
}

export interface ObservationScadaItemList {
  id: string;
  oname: string;
  otype: string;
  vprop: string;
  type: ObservationType;
  description?: string;
  startTime?: string;
  endTime?: string;
  max?: number;
  min?: number;
  isPublic: boolean;
  createTime: string;
  updateTime: string;
  name?: string;
  value?: number | string;
  simulatedValue?: number | string;
  shape?: string;
  unit?: string;
  typeTitle?: string;
}

export const formatObservationScadaList = (
  list: ObservationScadaBaseItem[],
  observationValue: Map<string, PropertyValue>,
  db: Database,
  isCustom?: boolean,
): ObservationScadaItem[] => {
  const arr = list.map((item): ObservationScadaItem => {
    const { otype, oname, vprop, type } = item;
    const deviceId = makeObjectId(otype, oname);
    const indicatorInfo = db.getIndicator(otype, oname);
    const { ptype, pname } = indicatorInfo ?? {};
    const device = ptype && pname ? db.getDevice(ptype, pname) : undefined;
    const propertyInfo = db.getPropertyInfo(otype);
    const unitFormat = db.getUnitFormat(otype, vprop);
    const valueId = makeObjectId(deviceId, vprop);
    const simulatedValueId = makeObjectId(valueId, 'CALCULATION');
    const value = observationValue.get(valueId)?.value;
    const simulatedValue = observationValue.get(simulatedValueId)?.value;
    const indicatorTypeTitle = indicatorInfo?.indicatorType?.title;
    const modelTypeTitle = db.getPropertyInfo(otype)?.getPropertyTitle(vprop);
    return {
      ...item,
      name:
        type === ObservationType.SCADA
          ? device?.title
          : `${propertyInfo?.title} ${oname}`,
      value: typeof value === 'undefined' ? value : unitFormat?.getValue(value),
      simulatedValue:
        typeof simulatedValue === 'undefined'
          ? simulatedValue
          : unitFormat?.getValue(simulatedValue),
      unit: unitFormat?.unitSymbol,
      shape: device?.shape,
      type,
      typeTitle:
        type === ObservationType.MODEL ? modelTypeTitle : indicatorTypeTitle,
    };
  });

  if (isCustom)
    return arr.filter((item) => item.type === ObservationType.SCADA);
  return arr;
};

export const getValueGroupParams = (list: ObservationScadaBaseItem[]): {} => {
  const params: any = {};
  list.forEach((item) => {
    const { otype, oname, vprop } = item;
    const id = makeObjectId(otype, oname);
    const observationValueId = makeObjectId(id, vprop);
    const calculationId = makeObjectId(observationValueId, 'CALCULATION');
    params[calculationId] = {
      vprop,
      otype,
      oname,
    };
    if (item.type === ObservationType.SCADA) {
      params[observationValueId] = {
        vprop,
        otype,
        oname,
      };
      params[calculationId].rmode = '@CALCULATION';
    }
  });

  return params;
};

export const isBetweenRange = (
  value?: number,
  min?: number,
  max?: number,
): boolean => {
  if (typeof value !== 'number') return true;
  if (typeof min === 'number' && value < min) return false;
  if (typeof max === 'number' && value > max) return false;

  return true;
};
