/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { PropertyValue } from './device';
import { makeObjectId } from './object-item';
import { getUnitFormat, UnitFormat } from './unit-system';

export enum ValueType {
  Text = 'Text',
  LongText = 'LongText',
  Number = 'Number',
  Switch = 'Switch',
  Radio = 'Radio',
  Checkbox = 'Checkbox',
  Select = 'Select',
  Image = 'Image',
  DateTimePicker = 'DateTimePicker',
  DatePicker = 'DatePicker',
  Shape = 'Shape',
}

export type FormRule = {
  /** 是否为必选字段 */
  required?: boolean;
  /** 校验失败的信息 */
  message?: string;
};

export interface BaseFormSchema {
  /** 属性字段名 vprop */
  dataIndex: string;
  /** 控件类型 */
  valueType: ValueType;
  /** 表单控件的title,如果不配默认取vprop的title */
  title?: string;
  /** 是否为只读 */
  readonly?: boolean;
  rule?: FormRule[];
  /** 初始值 */
  initialValue?: string | number | boolean;
  /** 是否显示  */
  hidden?: boolean;
  disabled?: boolean;
  /** 排序 */
  order?: number;
  /** 快速校验是否必填 */
  required?: boolean;
}

export interface TextSchema extends BaseFormSchema {}
export interface LongTextSchema extends BaseFormSchema {
  /** 显示多少行 */
  rows?: number;
  /** 是否展示字数 */
  showCount?: boolean;
  /** 最大长度 */
  maxLength?: number;
}
export interface NumberSchema extends BaseFormSchema {
  /** 最大值 */
  max?: number;
  /** 最小值 */
  min?: number;
  /** 数值精度 */
  precision?: number;
}
export interface SwitchSchema extends BaseFormSchema {
  /** 选中时显示文本 */
  checkedChildren?: string;
  /** 未选中时显示文本 */
  unCheckedChildren?: string;
}
export interface RadioSchema extends BaseFormSchema {
  options?: {
    label: string;
    value: string;
  }[];
}
export interface CheckboxSchema extends BaseFormSchema {
  options?: {
    label: string;
    value: string;
  }[];
}
export interface SelectSchema extends BaseFormSchema {
  options?: {
    label: string;
    value: string;
  }[];
}
export interface ImageSchema extends BaseFormSchema {}
export interface ShapeSchema extends BaseFormSchema {}

export type FormSchemaType = TextSchema &
  LongTextSchema &
  NumberSchema &
  SwitchSchema &
  RadioSchema &
  CheckboxSchema &
  SelectSchema &
  ImageSchema &
  ShapeSchema & {
    unit?: UnitFormat;
  };

export type ScadaDataConfig = {
  //  index as otype
  [index: string]: FormSchemaType[];
};

export interface ValueItem {
  title: string;
  value:
    | null
    | number
    | string
    | {
        [key: string]: ValueItem;
      }[];
  vprop?: string;
}

type ValueObjectToInterface = {
  [key: string]: ValueItem | number;
} & {
  state: number;
};

export interface ValueObject {
  [key: string]: ValueItem;
}

export interface WarnConfigObject {
  title: string;
  type: string;
  value: ValueObject;
  order: string[];
}

export interface DefaultScadaWarnConfig {
  ONAME: string;
  OTYPE: string;
  PNAME: string;
  PTYPE: string;
  TITLE: string;
  KET_WARN: boolean;
}

export type ScadaWarnConfigList = {
  [key: string]: ValueObjectToInterface;
} & DefaultScadaWarnConfig;

export interface WarnConfigToInterface {
  [key: string]: ValueObjectToInterface;
}

export interface WarnConfig {
  [key: string]: WarnConfigObject;
}

export function formatFormSchema(
  formList: FormSchemaType[],
  otype: string,
  oname: string,
  type: 'device' | 'indicator',
  db: Database,
): FormSchemaType[] {
  return formList
    .filter((item) => item.dataIndex)
    .sort(({ order: aOrder = 0 }, { order: bOrder = 0 }) => aOrder - bOrder)
    .map((item) => {
      const { dataIndex } = item;
      const propertyInfo = db.getPropertyInfo(otype);
      const title = propertyInfo?.getPropertyTitle(dataIndex);
      const unitKey = propertyInfo?.getPropertyUnit(dataIndex);
      const unit = getUnitFormat(unitKey as string);
      let value: string | undefined | number | boolean;
      if (type === 'device') {
        value = db
          .getDeviceById(makeObjectId(otype, oname))
          ?.getPropertyValue(dataIndex);
      } else {
        value = db.getIndicator(otype, oname)?.getPropertyValue(dataIndex);
      }
      return {
        ...item,
        title: title ?? dataIndex,
        // 如果 initialValue 为 null, 则使用 initialValue
        initialValue:
          item.initialValue === undefined ? value : item.initialValue,
        unit,
      };
    });
}

function getSelectedObjectFormConfig(
  otype: string | undefined,
  oname: string | undefined,
  scadaDataConfigItem: FormSchemaType[] | undefined,
  type: 'device' | 'indicator',
  db: Database,
): FormSchemaType[] {
  if (otype && oname) {
    const list = scadaDataConfigItem ?? [];

    return formatFormSchema(list, otype, oname, type, db);
  }
  return [];
}

export function getSelectedDeviceFormConfig(
  otype: string | undefined,
  oname: string | undefined,
  scadaDataConfigItem: FormSchemaType[] | undefined,
  vpropValue: Map<string, PropertyValue>,
  db: Database,
): FormSchemaType[] {
  const newScadaDataConfigItem = scadaDataConfigItem?.map((item) => ({
    ...item,
    initialValue: vpropValue.get(item.dataIndex)?.value,
  }));
  return getSelectedObjectFormConfig(
    otype,
    oname,
    newScadaDataConfigItem,
    'device',
    db,
  );
}

export function getSelectedIndicatorFormConfig(
  otype: string | undefined,
  oname: string | undefined,
  scadaDataConfigItem: FormSchemaType[] | undefined,
  vpropValue: Map<string, PropertyValue>,
  db: Database,
): FormSchemaType[] {
  const newScadaDataConfigItem = scadaDataConfigItem?.map((item) => ({
    ...item,
    initialValue: vpropValue.get(item.dataIndex)?.value,
  }));
  return getSelectedObjectFormConfig(
    otype,
    oname,
    newScadaDataConfigItem,
    'indicator',
    db,
  );
}
