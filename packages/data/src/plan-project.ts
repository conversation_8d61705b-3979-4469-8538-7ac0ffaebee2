/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from './event-scheduling/basic-info';

export type PlanProjectType = {
  /** 类型 */
  type: string;
  /** 类型名称 */
  title: string;
}[];

export const convertPlanProjectTypeDataToMap = (
  data: PlanProjectType,
): Record<string, string> =>
  data.reduce(
    (acc, item) => ({ ...acc, [item.type]: item.title }),
    {} as Record<string, string>,
  );

export enum ProjectType {
  /** 管网停水 */
  EPI_WATER_SHUT_OFF_NOTICE = 'EPI_WATER_SHUT_OFF_NOTICE',
  /** 水厂减停产 */
  WATER_FACTORY_SHUTDOWN = 'WATER_FACTORY_SHUTDOWN',
}

export enum ProjectState {
  /** 计划中 */
  PLANNING = 'PLANNING',
  /** 实施中 */
  IMPLEMENTING = 'IMPLEMENTING',
  /** 完成 */
  COMPLETED = 'COMPLETED',
}

export const getPlanProjectTypeTitle = (
  typeData: PlanProjectType,
  type: string,
): string => {
  const data = typeData.find((item) => item.type === type);
  return data ? data.title : '';
};

export const projectStateNames: Record<ProjectState, string> = {
  [ProjectState.PLANNING]: '计划中',
  [ProjectState.IMPLEMENTING]: '实施中',
  [ProjectState.COMPLETED]: '完成',
};

export const projectStateOptions = Object.values(ProjectState).map((value) => ({
  label: projectStateNames[value],
  value,
}));

export interface PlanProjectListParams {
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 工程 ID */
  systemProjectId?: string;
  /** 计划开始时间 */
  projectStartTime?: string;
  /** 计划结束时间 */
  projectEndTime?: string;
  /** 工程名称 */
  projectName?: string;
  /** 工程类型 */
  projectType?: ProjectType;
  /** 工程状态 */
  projectState?: ProjectState;
  /** 工程内容 */
  projectContent?: string;
  /** 申请人 */
  userName?: string;
  /** 申请人部门 */
  departmentName?: string;
}

export interface PlanProjectList {
  /** 工程 ID */
  systemProjectId?: string;
  /** 工程名称 */
  projectName?: string;
  /** 工程类型 */
  projectType?: ProjectType;
  /** 工程部门 ID */
  departmentId?: string;
  /** 工程部门名称 */
  departmentName?: string;
  /** 工程状态 */
  projectState?: ProjectState;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 工程内容 */
  projectContent?: string;
  /** 用户名 */
  userName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 关联事件 ID */
  eventName?: string;
  /** 关联事件名称 */
  eventTitle?: string;
  editable?: boolean;
  note?: string;
  [key: string]: string | boolean | undefined;
}

export interface UpdatePlanProjectParams {
  /** 工程 ID */
  systemProjectId?: string;
  /** 工程名称 */
  projectName?: string;
  /** 工程类型 */
  projectType?: ProjectType;
  /** 工程部门 ID */
  departmentId?: string;
  /** 工程部门名称 */
  departmentName?: string;
  userName?: string;
  /** 工程状态 */
  projectState?: ProjectState;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 工程内容 */
  projectContent?: string;
  /** 关联事件 ID */
  eventId?: string;
  source?: string;
  note?: string;
  jsonDetail?: { [key: string]: string };
}

export interface DeletePlanProjectParams {
  /** 工程 ID 列表 */
  projectIdList?: string;
}

/** 工程字段到事件字段的映射规则 */
export interface ProjectEventFieldMapping {
  /** 工程字段名 */
  from: keyof PlanProjectList;
  /** 事件字段名 */
  to: keyof EventSchedulingBasicInfo;
}

/** 工程到事件的映射规则 */
export interface ProjectEventMapping {
  /** 事件大类 */
  eventType: string;
  /** 事件小类 */
  eventSubType: string;
  /** 字段映射规则 */
  fieldMapping: ProjectEventFieldMapping[];
}

/** 工程类型的事件映射配置 */
export interface ProjectTypeEventMapping {
  /** 工程类型 */
  type: ProjectType;
  /** 映射到事件的配置 */
  mapping: ProjectEventMapping;
}

/** 工程到事件的完整映射配置 */
export interface ProjectEventMappingConfig {
  /** 工程到事件的映射配置列表 */
  eventMapping: ProjectTypeEventMapping[];
}

/** 将工程信息转换为事件基本信息 */
export function convertPlanProjectToEventBaseInfo(
  planProject: PlanProjectList,
  eventMapping: ProjectEventMappingConfig['eventMapping'],
): Partial<EventSchedulingBasicInfo> {
  const mappingConfig = eventMapping.find(
    (item) => item.type === planProject.projectType,
  );
  if (!mappingConfig) return {};

  const { mapping } = mappingConfig;
  const result: Partial<EventSchedulingBasicInfo> = {};

  if (mapping.eventType) result.eventType = mapping.eventType;
  if (mapping.eventSubType) result.eventSubType = mapping.eventSubType;

  if (Array.isArray(mapping.fieldMapping)) {
    mapping.fieldMapping.forEach(({ from, to }) => {
      const value = planProject[from];
      if (typeof value === 'string' || typeof value === 'undefined') {
        result[to] = value as any;
      }
    });
  }

  return result;
}
