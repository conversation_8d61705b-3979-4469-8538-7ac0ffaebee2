/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable class-methods-use-this */
import Database from '../database';
import { IndicatorObject } from '../indicator';
import {
  ObjectChartProperty,
  ObjectProperty,
  PropertyCategory,
  updatePropertyTitleUnit,
} from './property-info';

export default class DiagramPropertyCategory implements PropertyCategory {
  constructor(title: string, propertyItems: Array<ObjectProperty>) {
    this._title = title;
    this._propertyItems = propertyItems;
  }

  getValueQueryParameters(
    _indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    return params;
  }

  getChartProperties(_indicators: IndicatorObject[]): ObjectChartProperty[] {
    return [];
  }

  updatePropertyTitleUnit(db: Database, otype: string) {
    updatePropertyTitleUnit(db, otype, this._propertyItems);
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _propertyItems: Array<ObjectProperty>;

  get propertyItems(): Array<ObjectProperty> {
    return this._propertyItems;
  }
}
