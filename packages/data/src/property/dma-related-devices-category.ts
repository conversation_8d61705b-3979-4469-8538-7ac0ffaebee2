/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable class-methods-use-this */
import Database from '../database';
import { HighlightObject } from '../highlight-object';
import { IndicatorObject } from '../indicator';
import { ObjectChartProperty, PropertyCategory } from './property-info';

export interface DmaRelatedDevice extends HighlightObject {
  otype: string;
  oname: string;
  indicatorType: string;
  indicatorName: string;
  deviceTitle: string;
  historySupplyFlow: string;
  historySupplyFlowNumber: number;
  supplyFlow: string;
  supplyFlowNumber: number;
}

export type DmaType = 'THIRD';
export const DMA_THIRD: DmaType = 'THIRD';
export default class DmaRelatedDeviceCategory implements PropertyCategory {
  _dmaType: DmaType | undefined;

  constructor(title: string, dmaType: DmaType | undefined) {
    this._title = title;
    this._dmaType = dmaType;
  }

  getValueQueryParameters(
    _indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    return params;
  }

  getChartProperties(_indicators: IndicatorObject[]): ObjectChartProperty[] {
    return [];
  }

  updatePropertyTitleUnit(_db: Database, _otype: string) {}

  private _title: string;

  get title(): string {
    return this._title;
  }

  get dmaType(): DmaType | undefined {
    return this._dmaType;
  }
}
