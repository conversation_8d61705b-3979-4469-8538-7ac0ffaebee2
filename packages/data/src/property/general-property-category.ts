/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import { IndicatorObject } from '../indicator';
import {
  ObjectChartProperty,
  ObjectProperty,
  PropertyCategory,
  updatePropertyTitleUnit,
} from './property-info';

export default class GeneralPropertyCategory implements PropertyCategory {
  constructor(title: string, propertyItems: Array<ObjectProperty>) {
    this._title = title;
    this._propertyItems = propertyItems;
  }

  getValueQueryParameters(
    indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    this._propertyItems.forEach((item) => {
      if (item.type === 'Indicator') {
        indicators.forEach((indicator) => {
          if (indicator.otype === item.otype) {
            const key = `${indicator.oname}@${item.otype}@${item.name}`;
            const props: Map<string, string> = new Map();
            props.set('oname', indicator.oname);
            props.set('otype', item.otype);
            props.set('vprop', item.name);
            params.set(key, props);
          }
        });
      } else if (item.type === 'Property') {
        const props: Map<string, string> = new Map();
        if (item.otype) props.set('otype', item.otype);
        props.set('vprop', item.name);
        params.set(item.name, props);
      }
    });
    return params;
  }

  getChartProperties(indicators: IndicatorObject[]): ObjectChartProperty[] {
    const chartProperties: Array<ObjectChartProperty> = [];
    this._propertyItems.forEach((item) => {
      const chartEditor = item.editors;
      if (chartEditor !== undefined) {
        if (item.type === 'Indicator') {
          indicators.forEach((indicator) => {
            if (indicator.otype === item.otype) {
              chartProperties.push({
                indicatorOType: item.otype,
                indicatorOName: indicator.oname,
                vprop: item.name,
                propertyTitle: item.title,
                unitFormat: item.unitFormat,
                editors: chartEditor,
              });
            }
          });
        } else if (item.type === 'Property') {
          chartProperties.push({
            indicatorOType: undefined,
            indicatorOName: undefined,
            vprop: item.name,
            propertyTitle: item.title,
            unitFormat: item.unitFormat,
            editors: chartEditor,
          });
        }
      }
    });

    return chartProperties;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _propertyItems: Array<ObjectProperty>;

  get propertyItems(): Array<ObjectProperty> {
    return this._propertyItems;
  }

  updatePropertyTitleUnit(db: Database, otype: string) {
    updatePropertyTitleUnit(db, otype, this._propertyItems);
  }
}
