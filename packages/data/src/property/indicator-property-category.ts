/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable @typescript-eslint/no-unused-vars */
import Database from '../database';
import { IndicatorObject } from '../indicator';
import {
  ObjectChartProperty,
  ObjectProperty,
  PropertyCategory,
  updatePropertyTitleUnit,
} from './property-info';

export default class IndicatorPropertyCategory implements PropertyCategory {
  constructor(title: string) {
    this._title = title;
    this._indicatorPropertyItems = [];
  }

  getValueQueryParameters(
    indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    indicators.forEach((indicator) => {
      this._indicatorPropertyItems.forEach((item) => {
        const otype = item[0];
        if (indicator.otype === otype) {
          item[1].forEach((prop) => {
            const key = `${indicator.oname}@${otype}@${prop.name}`;
            const props: Map<string, string> = new Map();
            props.set('oname', indicator.oname);
            props.set('otype', otype);
            props.set('vprop', prop.name);
            params.set(key, props);
          });
        }
      });
    });

    return params;
  }

  getChartProperties(indicators: IndicatorObject[]): ObjectChartProperty[] {
    const chartProperties: Array<ObjectChartProperty> = [];
    indicators.forEach((indicator) => {
      this._indicatorPropertyItems.forEach((item) => {
        const otype = item[0];
        if (indicator.otype === otype) {
          item[1].forEach((prop) => {
            if (prop.editors !== undefined) {
              chartProperties.push({
                indicatorOType: otype,
                indicatorOName: indicator.oname,
                vprop: prop.name,
                propertyTitle: prop.title,
                unitFormat: prop.unitFormat,
                editors: prop.editors,
              });
            }
          });
        }
      });
    });

    return chartProperties;
  }

  updatePropertyTitleUnit(db: Database, _otype: string) {
    this._indicatorPropertyItems.forEach((item) => {
      updatePropertyTitleUnit(db, item[0], item[1]);
    });
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _indicatorPropertyItems: Array<[string, Array<ObjectProperty>]>; // [otype, propertyItems]

  get indicatorPropertyItems(): Array<[string, Array<ObjectProperty>]> {
    return this._indicatorPropertyItems;
  }

  addPropertyItems(otype: string, propertyItems: Array<ObjectProperty>) {
    this._indicatorPropertyItems.push([otype, propertyItems]);
  }
}
