/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import { PropertyValue } from '../device';
import { IndicatorObject } from '../indicator';
import { IObjectItem, makeId } from '../object-item';
import { Key } from '../types';
import { getUnitFormat, getUnitValue, UnitFormat } from '../unit-system';
import toObject from '../utils';
import BasicPropertyCategory from './basic-property-category';
import CorrelationPropertyCategory from './correlation-property-category';
import DiagramPropertyCategory from './diagram-property-category';
import DmaRelatedDeviceCategory from './dma-related-devices-category';
import GeneralPropertyCategory from './general-property-category';
import IndicatorPropertyCategory from './indicator-property-category';
import NotePropertyCategory from './note-property-category';
import PicturePropertyCategory from './picture-property-category';
import PumpStateCategory from './pump-state-category';
import WarnPropertyCategory from './warn-property-category';

export type PropertyType = 'Indicator' | 'Property' | 'Unknown';

export interface PropertyButtonEditor {
  actionName: string;
  type: string;
  charts?: string;
  proCharts?: string;
  correlatedVprop?: string[];
  dateType?: string;
  iconText?: string;
  title?: string;
}

export interface ObjectChartProperty {
  indicatorOType?: string;
  indicatorOName?: string;
  vprop: string;
  propertyTitle: string | undefined;
  unitFormat: UnitFormat | undefined;
  editors: PropertyButtonEditor[];
}

export interface ChartConfig extends PropertyButtonEditor {
  otype?: string;
  type: string;
  vprop: string;
  proCharts?: string;
  charts?: string;
  dateType?: string;
}

export interface ChartProperties {
  key: Key;
  deviceName?: string;
  pname: string;
  ptype: string;
  oname: string;
  otype: string;
  vprop: string;
  title: string | undefined;
  unitFormat: UnitFormat | undefined;
  editors?: ChartConfig[];
  /** 开始时间 */
  stime?: string;
  etime?: string;
}

export type ButtonType = 'chart' | 'propertyTrack';

function getEditorActionConfig(buttonType: ButtonType): {
  iconText: string;
  title: string;
  actionName: string;
} {
  const actionNamesMap = {
    chart: {
      iconText: '\ue629',
      title: '查看曲线',
      actionName: 'propertyChartAction',
    },
    dmaCustomer: {
      iconText: '\ue659',
      title: '查看用户列表',
      actionName: 'propertyDmaCustomerAction',
    },
    propertyTrack: {
      iconText: '\ue61b',
      title: '追踪',
      actionName: 'propertyTrackAction',
    },
  };
  return actionNamesMap[buttonType] ?? actionNamesMap.chart;
}

function generatePropertyEditors(data: any): PropertyButtonEditor[] {
  if (Array.isArray(data)) {
    return data.map((item: any) => {
      const editorActionConfig = getEditorActionConfig(item.type);
      return {
        charts: item.charts,
        proCharts: item.proCharts,
        correlatedVprop: item.correlatedVprop,
        type: item.type,
        dateType: item.dateType,
        iconText: editorActionConfig.iconText,
        title: editorActionConfig.title,
        actionName: editorActionConfig.actionName,
      };
    });
  }
  const editorActionConfig = getEditorActionConfig(data.type);
  return [
    {
      charts: data.charts,
      proCharts: data.proCharts,
      correlatedVprop: data.correlatedVprop,
      type: data.type,
      dateType: data.dateType,
      iconText: editorActionConfig.iconText,
      title: editorActionConfig.title,
      actionName: editorActionConfig.actionName,
    },
  ];
}

export class ObjectProperty {
  constructor() {
    this._type = 'Property';
    this._name = '';
    this._title = '';
    this._unit = '';
    this._dataMode = '';
    this._comparable = false;
    this._editable = false;
  }

  private _type: PropertyType;

  get type(): PropertyType {
    return this._type;
  }

  private _otype: string | undefined;

  get otype(): string | undefined {
    return this._otype;
  }

  private _name: string; // vprop

  get name(): string {
    return this._name;
  }

  private _dataMode: string; // fc_ext

  get dataMode(): string {
    return this._dataMode;
  }

  private _comparable: boolean;

  get comparable(): boolean {
    return this._comparable;
  }

  private _editable: 'number' | boolean;

  get editable(): 'number' | boolean {
    return this._editable;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  set title(title: string) {
    this._title = title;
  }

  private _unit: string;

  get unit(): string {
    return this._unit;
  }

  set unit(unit: string) {
    this._unit = unit;
  }

  private _unitFormat: UnitFormat | undefined;

  get unitFormat(): UnitFormat | undefined {
    if (this._unitFormat === undefined) {
      this._unitFormat = getUnitFormat(this._unit);
    }

    return this._unitFormat;
  }

  private _editors: PropertyButtonEditor[] | undefined;

  get editors(): PropertyButtonEditor[] | undefined {
    return this._editors;
  }

  // eslint-disable-next-line class-methods-use-this
  generateEditor(data: any): 'number' | boolean {
    if (!data) return false;
    return data.type;
  }

  initialize(data: any, editable?: boolean) {
    switch (data.type) {
      case 'quota':
        this._type = 'Indicator';
        break;
      case 'prop':
        this._type = 'Property';
        break;
      default:
        this._type = 'Unknown';
        break;
    }

    this._dataMode = data.fc_ext;
    this._comparable = data.comparable;
    this._editable = editable ? this.generateEditor(data.editable) : false;
    this._name = data.vprop;
    this._title = this._name; // the title is default to name (vprop)
    this._otype = data.otype;
    if (data.button !== undefined) {
      this._editors = generatePropertyEditors(data.button);
    }
  }
}

function generateObjectProperty(data: any, editable?: boolean): ObjectProperty {
  const propertyItem = new ObjectProperty();
  propertyItem.initialize(data, editable);
  return propertyItem;
}

function generateObjectPropertyList(
  data: any[],
  editable?: boolean,
): Array<ObjectProperty> {
  const itemList: Array<ObjectProperty> = [];
  data.forEach((element) => {
    itemList.push(generateObjectProperty(element, editable));
  });
  return itemList;
}

export interface PropertyCategory {
  title: string;
  updatePropertyTitleUnit(db: Database, otype: string): void;
  getValueQueryParameters(
    indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>>;
  getChartProperties(
    indicators: Array<IndicatorObject>,
  ): Array<ObjectChartProperty>;
}

export interface PropertyButton {
  type: string;
  title: string;
}

export function updatePropertyTitleUnit(
  db: Database,
  otype: string,
  propertyItems: Array<ObjectProperty>,
) {
  propertyItems.forEach((item) => {
    let actualOtype = otype;
    if (item.type === 'Indicator') {
      if (item.otype !== undefined) {
        actualOtype = item.otype;
      }
    }
    const [title, unit] = db.getPropertyTitleUnit(actualOtype, item.name);
    // eslint-disable-next-line no-param-reassign
    if (title !== undefined && title !== '') item.title = title;
    // eslint-disable-next-line no-param-reassign
    if (unit !== undefined) item.unit = unit;
  });
}

function generatePropertyCategory(
  data: any,
  editable: boolean,
): PropertyCategory | null {
  let propertyItems: Array<ObjectProperty> = [];
  switch (data.type) {
    case 'button_prop':
      propertyItems = generateObjectPropertyList(data.prop_list, editable);
      return new GeneralPropertyCategory(data.title, propertyItems);
    case 'craft_pic':
      propertyItems = generateObjectPropertyList(data.prop_list, editable);
      return new DiagramPropertyCategory(data.title, propertyItems);
    case 'note_prop':
      propertyItems = generateObjectPropertyList(data.prop_list, editable);
      return new NotePropertyCategory(data.title, propertyItems);
    case 'base_prop':
      propertyItems = generateObjectPropertyList(data.prop_list, editable);
      return new BasicPropertyCategory(data.title, propertyItems);
    case 'oname_pic':
      return new PicturePropertyCategory(data.title);
    case 'pump_state':
      return new PumpStateCategory(data.title);
    case 'dma_related_devices':
      return new DmaRelatedDeviceCategory(data.title, data.dmaType);
    case 'warn_list':
      return new WarnPropertyCategory(data.title);
    case 'correlation_prop': {
      const propertyCategory = new CorrelationPropertyCategory(data.title);
      data.otype_list.forEach((element: any) => {
        propertyCategory.addPropertyItems(
          element.otype,
          generateObjectPropertyList(element.prop_list),
        );
      });
      return propertyCategory;
    }
    case 'quota_prop': {
      const propertyCategory = new IndicatorPropertyCategory(data.title);
      data.otype_list.forEach((element: any) => {
        propertyCategory.addPropertyItems(
          element.otype,
          generateObjectPropertyList(element.prop_list),
        );
      });
      return propertyCategory;
    }
    default:
      return null;
  }
}

function generatePropertyButtons(data: any[]): PropertyButton[] {
  const buttonItems: Array<PropertyButton> = [];
  data.forEach((item) => {
    const { type, title } = item;
    if (typeof type === 'string' && typeof title === 'string') {
      buttonItems.push({ type, title });
    }
  });
  return buttonItems;
}

export class PropertyInfo {
  constructor(otype: string, title: string, editable?: boolean) {
    this._otype = otype;
    if (title === '') this._title = otype;
    else this._title = title;
    this._categories = [];
    this._buttons = [];
    this._quickProperties = [];
    this._treeConfig = [];
    this._chartConfig = [];
    this._propertyTitles = new Map();
    this._propertyUnits = new Map();
    this._propertyCharts = new Map();
    this._editable = editable || false;
    this._isDevice = false;
    this._propertyItems = [];
  }

  private _propertyItems: ObjectProperty[];

  private _otype: string;

  get otype(): string {
    return this._otype;
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _categories: Array<PropertyCategory>;

  get categories(): Array<PropertyCategory> {
    return this._categories;
  }

  private _buttons: Array<PropertyButton>;

  get buttons(): Array<PropertyButton> {
    return this._buttons;
  }

  private _quickProperties: Array<ObjectProperty>;

  get quickProperties(): Array<ObjectProperty> {
    return this._quickProperties;
  }

  private _treeConfig: Array<ChartConfig>;

  get treeConfig(): Array<ChartConfig> {
    return this._treeConfig;
  }

  private _chartConfig: Array<ChartConfig>;

  get chartConfig(): Array<ChartConfig> {
    return this._chartConfig;
  }

  private _editable: boolean;

  get editable(): boolean {
    return this._editable;
  }

  private _isDevice: boolean;

  get isDevice(): boolean {
    return this._isDevice;
  }

  // <propertyName, title>
  private _propertyTitles: Map<string, string>;

  getPropertyTitle(propertyName: string): string | undefined {
    return this._propertyTitles.get(propertyName);
  }

  getProperties(): { title: string; vprop: string }[] {
    return [...this._propertyTitles.entries()].map(([vprop, title]) => ({
      title,
      vprop,
    }));
  }

  // <propertyName, unit>
  private _propertyUnits: Map<string, string>;

  getPropertyUnit(propertyName: string): string | undefined {
    return this._propertyUnits.get(propertyName);
  }

  // <propertyName, chartConfig>
  private _propertyCharts: Map<string, ChartConfig>;

  getPropertyChart(propertyName: string): ChartConfig | undefined {
    return this._propertyCharts.get(propertyName);
  }

  addCategory(category: PropertyCategory) {
    this._categories.push(category);
  }

  initializePropertyItems() {
    const data = [...this._propertyCharts.values()].map((item) => ({
      ...item,
      vprop: item.vprop,
      button: {
        ...item,
      },
    }));
    this._propertyItems = generateObjectPropertyList(data);
  }

  initialize(
    propViewData: {}[] | undefined,
    propTitlesData: {}[] | undefined,
    quickPropertiesData: {}[] | undefined,
    scadaTreeConfig: ChartConfig[],
    isDevice: boolean,
    chartConfig?: ChartConfig[],
  ) {
    this._isDevice = isDevice;

    propViewData?.forEach((element: any) => {
      const category = generatePropertyCategory(element, this._editable);
      if (category !== null) this.addCategory(category);
      if (element.buttons)
        this._buttons = generatePropertyButtons(element.buttons);
    });

    propTitlesData?.forEach((item: any) => {
      const { vprop, title, unit, chart_def: chartDef } = item;
      if (typeof vprop !== 'string') return;
      if (typeof title === 'string') this._propertyTitles.set(vprop, title);
      if (typeof unit === 'string') this._propertyUnits.set(vprop, unit);
      if (typeof chartDef !== 'undefined')
        this._propertyCharts.set(vprop, {
          type: chartDef.type,
          vprop,
          proCharts: chartDef.proCharts,
          charts: chartDef.charts,
          dateType: chartDef.dateType,
          actionName: '',
        });
    });

    this.initializePropertyItems();

    if (quickPropertiesData !== undefined)
      this._quickProperties = generateObjectPropertyList(quickPropertiesData);

    if (scadaTreeConfig !== undefined) this._treeConfig = scadaTreeConfig;
    if (chartConfig !== undefined) this._chartConfig = chartConfig;
  }

  updatePropertyTitleUnit(db: Database) {
    this._categories.forEach((item) =>
      item.updatePropertyTitleUnit(db, this.otype),
    );

    updatePropertyTitleUnit(db, this.otype, this._quickProperties);
    updatePropertyTitleUnit(db, this.otype, this._propertyItems);
  }

  getValueQueryParameters(indicators: Array<IndicatorObject>): any {
    const params: Map<string, Map<string, string>> = new Map();
    this._categories.forEach((category) => {
      const categoryParams = category.getValueQueryParameters(indicators);
      categoryParams.forEach((value, key) => {
        params.set(key, value);
      });
    });
    return toObject(params);
  }

  getChartProperties(indicators: IndicatorObject[]): ObjectChartProperty[] {
    const chartProperties: Array<ObjectChartProperty> = [];
    this._categories.forEach((category) => {
      const properties = category.getChartProperties(indicators);
      chartProperties.push(...properties);
    });

    return chartProperties;
  }

  getChartPropertyByIndicator(
    indicatorType: string,
    indicatorOName: string,
    vprop: string,
  ): ObjectChartProperty | undefined {
    const foundPropertyItem = this.getChartProperties([
      {
        otype: indicatorType,
        oname: indicatorOName,
      },
    ] as IndicatorObject[]).find((item) => item.vprop === vprop);
    return foundPropertyItem;
  }

  getChartPropertiesByConfig(
    object: IObjectItem,
    db: Database,
    configArray: 'treeConfig' | 'chartConfig',
  ): ChartProperties[] {
    const chartProperties: Array<ChartProperties> = [];
    const chartConfig =
      configArray === 'treeConfig' ? this.treeConfig : this.chartConfig;
    chartConfig.forEach((config) => {
      if (config.type === 'quota' || config.type === 'quota_prop') {
        const { indicators } = object;
        const indicatorArray = indicators.filter(
          (indicator) => indicator.otype === config.otype,
        );
        const indicatorChartArray = indicatorArray.map((item) => {
          let title = item.title ?? db.getPropertyInfo(item.otype)?.title ?? '';
          if (config.type === 'quota_prop') {
            title +=
              db.getPropertyTitleUnit(item.otype, config.vprop)[0] ??
              config.vprop;
          }
          const editors = db.getChartConfig(item.otype, config.vprop);
          return {
            key: makeId(item.otype, item.oname, config.vprop),
            ptype: object.otype,
            pname: object.oname,
            otype: item.otype,
            oname: item.oname,
            vprop: config.vprop,
            title,
            unitFormat: db.getUnitFormat(item.otype, config.vprop),
            editors: editors ? [editors] : undefined,
          };
        });
        chartProperties.push(...indicatorChartArray);
      } else {
        const [title] = db.getPropertyTitleUnit(object.otype, config.vprop);
        const editors = db.getChartConfig(object.otype, config.vprop);
        if (editors) {
          chartProperties.push({
            key: makeId(object.otype, object.oname, config.vprop),
            ptype: object.otype,
            pname: object.oname,
            otype: object.otype,
            oname: object.oname,
            vprop: config.vprop,
            title,
            unitFormat: db.getUnitFormat(object.otype, config.vprop),
            editors: editors ? [editors] : undefined,
          });
        }
      }
    });
    return chartProperties;
  }
}

export function getPropertyValue(
  propertyValues: Map<string, PropertyValue | PropertyValue[]>,
  key: string,
  unit: string,
) {
  const propertyValue = propertyValues.get(key);
  let value;
  let compareValue: (string | number | undefined)[] = [];
  if (Array.isArray(propertyValue)) {
    value = getUnitValue(unit, propertyValue[0].value as string | number);
    const [, ...args] = propertyValue;
    compareValue = args.map((property) =>
      getUnitValue(unit, property.value as string | number),
    );
  } else {
    value = getUnitValue(unit, propertyValue?.value as string | number);
  }
  return {
    value,
    compareValue,
    unitFormat: getUnitFormat(unit),
  };
}

export function getPropertyInfo(
  propertyValues: Map<string, PropertyValue | PropertyValue[]>,
  key: string,
) {
  const propertyValue = propertyValues.get(key);
  let propertyInfo: {
    oname: string;
    otype: string;
    vprop: string;
  };
  if (Array.isArray(propertyValue)) {
    propertyInfo = {
      oname: propertyValue[0].oname,
      otype: propertyValue[0].otype,
      vprop: propertyValue[0].vprop,
    };
  } else {
    propertyInfo = {
      oname: propertyValue?.oname ?? '',
      otype: propertyValue?.otype ?? '',
      vprop: propertyValue?.vprop ?? '',
    };
  }
  return propertyInfo;
}
