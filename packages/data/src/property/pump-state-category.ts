/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import Database from '../database';
import { IndicatorObject } from '../indicator';
import {
  ObjectChartProperty,
  ObjectProperty,
  PropertyCategory,
  updatePropertyTitleUnit,
} from './property-info';

export default class PumpStateCategory implements PropertyCategory {
  constructor(title: string) {
    this._title = title;
    this._indicatorPropertyItems = [];
  }

  getValueQueryParameters(
    _indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    return params;
  }

  getChartProperties(_indicators: IndicatorObject[]): ObjectChartProperty[] {
    return [];
  }

  updatePropertyTitleUnit(db: Database, _otype: string) {
    this._indicatorPropertyItems.forEach((item) => {
      updatePropertyTitleUnit(db, item[0], item[1]);
    });
  }

  private _title: string;

  get title(): string {
    return this._title;
  }

  private _indicatorPropertyItems: Array<[string, Array<ObjectProperty>]>; // [otype, propertyItems]

  get indicatorPropertyItems(): Array<[string, Array<ObjectProperty>]> {
    return this._indicatorPropertyItems;
  }

  addPropertyItems(otype: string, propertyItems: Array<ObjectProperty>) {
    this._indicatorPropertyItems.push([otype, propertyItems]);
  }
}
