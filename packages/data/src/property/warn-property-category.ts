/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import Database from '../database';
import { IndicatorObject } from '../indicator';
import { ObjectChartProperty, PropertyCategory } from './property-info';

export default class WarnPropertyCategory implements PropertyCategory {
  constructor(title: string) {
    this._title = title;
  }

  getValueQueryParameters(
    _indicators: Array<IndicatorObject>,
  ): Map<string, Map<string, string>> {
    const params: Map<string, Map<string, string>> = new Map();
    return params;
  }

  getChartProperties(_indicators: IndicatorObject[]): ObjectChartProperty[] {
    return [];
  }

  updatePropertyTitleUnit(_db: Database, _otype: string) {}

  private _title: string;

  get title(): string {
    return this._title;
  }
}
