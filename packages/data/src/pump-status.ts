/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { PumpStateColor } from './app-config';
import { PropertyValue, PumpInfo } from './device';
import { TimeData } from './time-data';
import { formatNumber } from './utils';

export interface PumpTimeData {
  name: string;
  variable: boolean;
  timeData: TimeData[];
}

export interface PumpStatusData {
  startTimeIndex: number; // minutes since the start of date
  endTimeIndex: number;
  value: number;
}

function getPumpOnOffStateByValue(value: number, variable: boolean): boolean {
  if (variable) return value > 0.2;
  return value !== 0;
}

export function timeDataToPumpStatus(
  timeData: TimeData[],
  variable: boolean = false,
): PumpStatusData[] {
  if (timeData.length === 0) return [];

  const dayStartTime = dayjs(timeData[0].time).startOf('date');
  const statusDataList: PumpStatusData[] = [];
  let lastState: boolean = getPumpOnOffStateByValue(
    timeData[0].value,
    variable,
  );
  const firstTimeIndex = dayjs(timeData[0].time).diff(dayStartTime, 'minutes');
  statusDataList.push({
    startTimeIndex: firstTimeIndex,
    endTimeIndex: 1,
    value: lastState ? 1 : 0,
  });

  timeData.forEach((item) => {
    const currentState: boolean = getPumpOnOffStateByValue(
      item.value,
      variable,
    );
    const duration = dayjs(item.time).diff(dayStartTime, 'minutes');
    if (lastState === currentState) {
      statusDataList[statusDataList.length - 1].endTimeIndex = duration;
    } else {
      statusDataList[statusDataList.length - 1].endTimeIndex = duration;
      lastState = currentState;
      statusDataList.push({
        startTimeIndex: duration,
        endTimeIndex: duration + 1,
        value: lastState ? 1 : 0,
      });
    }
  });

  return statusDataList;
}

export function formatPumpData(
  value: number | string | undefined,
  min: number,
  max: number,
): number {
  if (typeof value === 'number') {
    if (value < min) return 0;
    if (value > max) return 50;
    return value;
  }
  return 0;
}

export interface PumpStatusChangeFlag {
  time: string;
  change: number; // -1: turn off; 0: turn on & turn off; 1: turn on
}

export interface PumpStatusChanges {
  plantName: string;
  pumpName: string;
  changeFlags: PumpStatusChangeFlag[];
}

function formatTo5MinuteInterval(timeString: string): string {
  const dateTime = dayjs(timeString);
  const minutes = dateTime.minute();
  const minutesToSubtract = minutes % 5;
  const formattedTime = dateTime
    .subtract(minutesToSubtract, 'minute')
    .startOf('minute');

  return formattedTime.format('YYYY-MM-DD HH:mm:ss');
}

export function getPumpStatusChangeTimeData(
  pumpTimeData: PumpTimeData,
  plantName: string = '',
): PumpStatusChanges {
  if (pumpTimeData.timeData.length === 0)
    return { plantName, pumpName: pumpTimeData.name, changeFlags: [] };

  const changes: PumpStatusChangeFlag[] = [];
  let lastState = getPumpOnOffStateByValue(
    pumpTimeData.timeData[0].value,
    pumpTimeData.variable,
  );
  for (let i: number = 1; i < pumpTimeData.timeData.length; i += 1) {
    const state = getPumpOnOffStateByValue(
      pumpTimeData.timeData[i].value,
      pumpTimeData.variable,
    );
    if (state === lastState) continue;

    const time = formatTo5MinuteInterval(pumpTimeData.timeData[i].time);
    changes.push({ time, change: state ? 1 : -1 });
    lastState = state;
  }

  return { plantName, pumpName: pumpTimeData.name, changeFlags: changes };
}

export function getPumpStatusChangeFlags(
  pumpTimeData: Map<string, PumpTimeData[]>,
): PumpStatusChanges[] {
  if (pumpTimeData.size === 0) return [];

  const changes: PumpStatusChanges[] = [];
  pumpTimeData.forEach((value, key) => {
    for (let i: number = 0; i < value.length; i += 1) {
      const change = getPumpStatusChangeTimeData(value[i], key);
      if (change.changeFlags.length > 0) changes.push(change);
    }
  });

  return changes;
}

export function getPumpSwitchName(flag: boolean): string {
  return flag ? '开' : '关';
}

export function getConvertPumpData(
  value: Pick<PropertyValue, 'value' | 'otime'>,
  pumpInfo: PumpInfo,
  pumpStateColor?: PumpStateColor,
): {
  pumpSwitch?: boolean;
  switchName?: string;
  frequency?: number;
  color?: string;
  otime?: string;
} {
  const originValue =
    typeof value.value === 'number' || typeof value.value === 'string'
      ? Number(value.value)
      : undefined;
  if (pumpInfo.variable) {
    const valueData = formatPumpData(
      originValue,
      pumpInfo.minFrequency,
      pumpInfo.maxFrequency,
    );
    const pumpSwitch = valueData > 20;
    return {
      pumpSwitch,
      switchName: getPumpSwitchName(pumpSwitch),
      color: pumpSwitch ? pumpStateColor?.variable : pumpStateColor?.closed,
      frequency: pumpSwitch ? formatNumber(valueData, 0) : undefined,
      otime: value?.otime,
    };
  }
  let pumpSwitch;
  let color;
  let switchName;
  if (typeof originValue === 'number') {
    pumpSwitch = !!originValue;
    switchName = getPumpSwitchName(pumpSwitch);
    color = pumpSwitch ? pumpStateColor?.fixed : pumpStateColor?.closed;
  }
  return {
    pumpSwitch,
    switchName,
    color,
    otime: value?.otime,
  };
}

export function getFlatTimeData(
  startTime: string,
  endTime: string,
  value: number,
): TimeData[] {
  const timeData = [];
  const dayStartTime = dayjs(startTime).startOf('date');
  const startTimeIndex = dayjs(startTime).diff(dayStartTime, 'minutes');
  const endTimeIndex = dayjs(endTime).diff(dayStartTime, 'minutes');
  for (let i = startTimeIndex; i <= endTimeIndex; i += 1) {
    timeData.push({
      time: dayjs(dayStartTime).add(i, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
      value,
    });
  }
  return timeData;
}
