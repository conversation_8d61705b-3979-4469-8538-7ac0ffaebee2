/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from '../database';
import { HighlightDataItem, HighlightObject } from '../highlight-object';
import { LegendGroupData, LegendItemData } from '../legend-data';
import {
  HIGHLIGHT_CUSTOM,
  HIGHLIGHT_SELECT,
  HighlightStyleType,
} from '../style-config';
import { getUnitFormat, getUnitValue, UnitFormat } from '../unit-system';
import {
  ExcelSheetData,
  exportToExcel,
  exportToExcelWithMultipleSheets,
  formatNumber,
  getMinMax,
} from '../utils';
import { SolutionAnalysisAdvanceSetting } from './solution-analysis-data';

export enum ResultReviewStatus {
  NoReviewNeeded = -1, // 无需审核
  PendingReview = 0, // 待审核
  ReviewPassed = 1, // 审核通过
  ReviewFailed = 2, // 审核未通过
}

export enum QuickAnalysisType {
  ValveAnalysis = 0,
  BurstFlushing = 1,
  WaterShutdown = 2,
}

export interface AnalysisObject {
  oname: string;
  otype: string;
  shape: string;
  otypeTitle?: string;
  key: string;
  highlightColor?: string;
  highlightIcon?: string;
  gisOType?: string;
  gisOName?: string;
  gisOTypeTitle?: string;
  gisShape?: string;
  // 是否在模型中
  isModel?: boolean;
}

export interface AnalysisObjects {
  closeList: AnalysisObject[];
  openList: AnalysisObject[];
}

export interface QuickSolutionData {
  id: string;
  type: QuickAnalysisType;
  title: string;
  source: string;
  time: string;
  analysisParams: AnalysisObjects;
  creator: string;
  creatorId: string;
  reviewer: string;
  reviewState: number | null;
  reviewTime: string;
  solutionShare: string;
  createTime: string;
  updateTime: string;
  note: string;
  noticeId?: string;
  noticeDetails?: string;
}

export type ThemesValue = {
  value?: number;
  color: string;
};

export type ValveStateType = 'CLOSE' | 'OPEN';

export interface AnalysisThemeItemConfig {
  title: string;
  icon?: string;
  /**
   * @deprecated type属性展示没有用到，不清楚具体作用，计划删除
   */
  type?: string;
  value: ThemesValue[];
}

export type AnalysisThemes = {
  [index: string]: AnalysisThemeItemConfig;
} & {
  default?: AnalysisThemeItemConfig;
};

export interface AnalysisConfigItem {
  title?: string;
  category: string[];
  otype: string;
  order?: number;
  table?: string[];
  themes?: AnalysisThemes;
  defaultHighlightColor?: string;
}

export type PlantStationType = 'plants' | 'pumpStations';

export type AnalysisUiConfigType =
  | 'affectedArea'
  | 'affectedAreaUser'
  | 'affectedPipeline'
  | 'reversePipeline'
  | 'closedPipeline'
  | 'valveList'
  | 'waterOutageArea'
  | 'waterOutageAreaUser'
  | 'waterReverseAffectedArea'
  | 'waterReverseAffectedAreaUser';

export type AnalysisUiConfig = Record<
  AnalysisUiConfigType,
  AnalysisConfigItem | undefined
>;

export interface AnalysisConfig extends AnalysisUiConfig {
  /** 节点是否默认高亮 */
  defaultHighlightNode?: boolean;
  /** 是否显示调度功能 */
  scheduleFunc?: boolean;
  /** 是否显示节点高亮功能 */
  highlightNodeFunc?: boolean;
  /** 短信发送最大数量 */
  maxSendSMSCount?: number;
  /** 能耗 */
  showEnergy?: boolean;
}

// 动态字段， 由配置生成
export type AnalysisUiDataItem = {
  oname: string;
  otype: string;
  shape: string | null;
  unitSymbol?: string;
  [index: string]: string | number | boolean | undefined | null;
};

export interface PlantStationInfo {
  unitFormat?: UnitFormat;
  id: string;
  ptype: string;
  pname: string;
  ptitle: string;
  otype: string;
  vprop: string;
  title: string;
  oldValue: number;
  newValue: number;
  rowSpan: number;
}

export type AnalysisResultData = {
  waterOutageArea: AnalysisUiDataItem[];
  waterOutageAreaUser: AnalysisUiDataItem[];
  closedPipeline: AnalysisUiDataItem[];
  valveList: AnalysisUiDataItem[];
  affectedArea: AnalysisUiDataItem[];
  affectedAreaUser: AnalysisUiDataItem[];
  affectedPipeline: AnalysisUiDataItem[];
  reversePipeline: AnalysisUiDataItem[];
  closeNode: AnalysisUiDataItem[];
  affectedNode: AnalysisUiDataItem[];
  waterOutageAreaUserCount: number;
  affectedAreaUserCount: number;
  plantStation: Record<PlantStationType, PlantStationInfo[]>;
  waterReverseAffectedArea: AnalysisUiDataItem[];
  waterReverseAffectedAreaUser: AnalysisUiDataItem[];
  waterReverseAffectedAreaUserCount: number;
  energyConsumption: PlantStationInfo[];
};

export type AnalysisUiData = Record<AnalysisUiConfigType, AnalysisUiDataItem[]>;

export type QueryParams = {
  type: AnalysisUiConfigType;
  params: {
    // otype: Array<oname>.join(",")
    [index: string]: string;
  };
};

export type WaterMeterInfo = {
  key: string;
  userId?: string;
  userName?: string;
  phone?: string;
  address?: string;
  waterMeterCode?: string;
};

export type SendWaterMeterDataItem = { hidden?: boolean } & WaterMeterInfo;

export type SendWaterMeter = {
  key: AnalysisUiConfigType | 'custom';
  label: string;
  data: Array<SendWaterMeterDataItem>;
};

export function filterArrayByKeyName<T extends { [key: string]: any }>(
  arrayToFilter: T[],
  compareArray: T[],
  keyName: string,
): T[] {
  const compareSet = new Set(compareArray.map((item) => item[keyName]));

  const filteredArray = arrayToFilter.filter(
    (item) => !compareSet.has(item[keyName]),
  );

  return filteredArray;
}

export function getAnalysisUiConfig(
  analysisConfig: AnalysisConfig,
): AnalysisUiConfig {
  const {
    affectedArea,
    affectedAreaUser,
    affectedPipeline,
    reversePipeline,
    closedPipeline,
    valveList,
    waterOutageArea,
    waterOutageAreaUser,
    waterReverseAffectedArea,
    waterReverseAffectedAreaUser,
  } = analysisConfig;
  return {
    affectedArea,
    affectedAreaUser,
    affectedPipeline,
    reversePipeline,
    closedPipeline,
    valveList,
    waterOutageArea,
    waterOutageAreaUser,
    waterReverseAffectedArea,
    waterReverseAffectedAreaUser,
  };
}

function getThemeItems(themeValues: ThemesValue[]): LegendItemData[] {
  const themeItems: LegendItemData[] = [];
  const { length } = themeValues;
  themeValues.forEach((themeValue, index: number) => {
    const id = `legend${index}`;
    const previousValue = themeValues[index - 1]?.value?.toString() ?? '';
    const value = themeValue.value?.toString() ?? '';
    if (length > 1) {
      let formatTitle = '';
      if (index === 0) {
        formatTitle = `<${value}`;
      } else if (index === length - 1) {
        formatTitle = `>=${themeValues[index - 1].value}`;
      } else {
        formatTitle = `>=${previousValue}且<${value}`;
      }
      const themeItem = {
        color: themeValue.color,
        count: undefined,
        id,
        title: formatTitle,
        value,
        checked: false,
      };
      themeItems.push(themeItem);
    } else {
      const themeItem = {
        color: themeValue.color,
        count: undefined,
        id,
        title:
          typeof themeValue.value === 'number'
            ? `变化超过${themeValues[index].value}`
            : '',
        value,
        checked: false,
      };
      themeItems.push(themeItem);
    }
  });
  return themeItems;
}

export function getAnalysisThemeConfig(
  otype: string,
  config: AnalysisConfigItem['themes'],
  db: Database,
  advanceSetting: Partial<SolutionAnalysisAdvanceSetting> = {},
): LegendGroupData[] | undefined {
  if (typeof config === 'undefined') return undefined;
  const themes: LegendGroupData[] = [];
  Object.entries(config).forEach((item) => {
    const [themeItemConfigKey, themeItemConfigValue] = item;
    const { title, value, icon } = themeItemConfigValue;
    const otypeProperty = db.getPropertyInfo(otype);
    const otypeTitle = otypeProperty?.getPropertyTitle(themeItemConfigKey);
    const unitKey = otypeProperty?.getPropertyUnit(themeItemConfigKey);
    const unit = unitKey ? getUnitFormat(unitKey) : undefined;

    let newValue = [...value];
    const foundAdvanceValue = Object.values(advanceSetting).find(
      (item) => item.name === themeItemConfigKey,
    );
    if (foundAdvanceValue) {
      newValue = [
        {
          color: value[0].color,
          value: foundAdvanceValue.value,
        },
      ];
    }
    const theme: LegendGroupData = {
      icon: icon ?? '\ue68f',
      name: themeItemConfigKey,
      title: (title ?? otypeTitle) + (unit ? `(${unit.unitSymbol})` : ''),
      unitSymbol: unit ? unit.unitSymbol : '',
      unit,
      type: 'text',
      items: getThemeItems(newValue),
    };
    themes.push(theme);
  });

  return themes;
}

export function getAnalysisThemes(
  analysisConfig: AnalysisConfig,
  db: Database,
  advanceSetting: Partial<SolutionAnalysisAdvanceSetting> = {},
): {
  [key in AnalysisUiConfigType]?: LegendGroupData[];
} {
  const analysisThemes: {
    [key in AnalysisUiConfigType]?: LegendGroupData[];
  } = {};
  const analysisUIConfig = getAnalysisUiConfig(analysisConfig);
  Object.entries(analysisUIConfig).forEach((item) => {
    const [panelKey, config] = item;
    if (config) {
      const analysisThemeConfig = getAnalysisThemeConfig(
        config.otype,
        config.themes,
        db,
        advanceSetting,
      );
      analysisThemes[panelKey as AnalysisUiConfigType] = analysisThemeConfig;
    }
  });
  return analysisThemes;
}

export function formatAnalysisResultData(
  data: AnalysisUiDataItem[],
  analysisConfigItem: AnalysisConfigItem | undefined,
  db: Database,
): AnalysisUiDataItem[] {
  if (!analysisConfigItem) return data;
  const { otype, category = [], table = [] } = analysisConfigItem;
  const otypeProperty = otype ? db.getPropertyInfo(otype) : undefined;
  return data.map((item) => {
    const dataItemObj = { ...item };
    const propertyInfo = otypeProperty ?? db.getPropertyInfo(dataItemObj.otype);
    Object.keys(item).forEach((key) => {
      if (category.includes(key) || table.includes(key)) {
        let unitKey = propertyInfo?.getPropertyUnit(key);
        // 历史问题:
        // 虚拟属性可能是小写，但是单位的key在规范下都是大写的，导致直接使用key可能导致取不到unitKey,
        // 因此当直接获取失败时，将key转成大写再取一次作为缺省值
        if (!unitKey)
          unitKey = propertyInfo?.getPropertyUnit(key.toLocaleUpperCase());
        const value = item[key];
        if (
          unitKey &&
          (typeof value === 'number' || typeof value === 'string')
        ) {
          dataItemObj[key] = getUnitValue(unitKey, value);
        } else {
          dataItemObj[key] = value;
        }
      }
    });
    return dataItemObj;
  });
}

export function getAnalysisUiData(
  analysisResult: AnalysisResultData,
): AnalysisUiData {
  const {
    waterOutageArea,
    waterOutageAreaUser,
    closedPipeline,
    valveList,
    affectedArea,
    affectedAreaUser,
    affectedPipeline,
    reversePipeline,
    waterReverseAffectedArea = [],
    waterReverseAffectedAreaUser = [],
  } = analysisResult;
  return {
    affectedArea,
    affectedAreaUser,
    affectedPipeline,
    reversePipeline,
    closedPipeline,
    valveList,
    waterOutageArea,
    waterOutageAreaUser,
    waterReverseAffectedArea,
    waterReverseAffectedAreaUser,
  };
}

export function getAnalysisHighlightData(
  data: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightObject?: Partial<HighlightObject>,
): HighlightDataItem[] {
  const {
    highlightType = HIGHLIGHT_CUSTOM,
    highlightIcon,
    highlightColor,
    highlightTextBgColor,
    highlightText,
    highlightShowMark = false,
  } = highlightObject ?? {};
  return data
    .filter((item) => item.shape !== null)
    .map(
      ({
        oname,
        otype,
        shape,
        highlightType: itemType,
        highlightIcon: itemIcon,
        highlightText: itemText,
        ...rest
      }): HighlightDataItem => {
        let color;
        let text;
        if (themeConfig) {
          const { name, items, unit } = themeConfig;
          const value = rest[name] ?? '';
          const compareValue = value;
          text = `${value}${unit?.unitSymbol ?? ''}`;
          color = items.find((item, index) => {
            if (items.length > 1) {
              return index === items.length - 1
                ? Number(compareValue) >= Number(item.value)
                : Number(compareValue) < Number(item.value);
            }
            return true;
          })?.color;
        }

        const textFormat = (itemText ?? highlightText ?? text)?.toString();

        return {
          ...rest,
          oname,
          otype,
          shape: shape!,
          highlightTextBgColor: highlightTextBgColor ?? '#ffffff',
          highlightColor: highlightColor ?? color,
          highlightIcon: (itemIcon ?? highlightIcon) as string,
          highlightType: (itemType ?? highlightType) as HighlightStyleType,
          highlightShowMark,
          highlightText: highlightShowMark ? textFormat : undefined,
        };
      },
    );
}

export function getCollapseItemKeysByOrder(
  analysisUiConfig: AnalysisUiConfig,
): Array<AnalysisUiConfigType> {
  const uiConfigEntries = Object.entries(analysisUiConfig);
  return uiConfigEntries
    .filter((item) => item[1])
    .sort((a, b) => (a[1]?.order ?? 0) - (b[1]?.order ?? 0))
    .map((item) => item[0] as AnalysisUiConfigType);
}

export function getHoverHighlightData(
  hoverObjects: AnalysisUiDataItem[],
  db: Database,
): HighlightDataItem[] {
  if (!hoverObjects.length) return [];
  const { otype, shape } = hoverObjects[0];
  const highlightIcon = db.getIconByShape(otype, shape ?? '');
  return getAnalysisHighlightData(hoverObjects, undefined, {
    highlightIcon,
    highlightType: HIGHLIGHT_SELECT,
  });
}

export function getSelectedObjectHighlightData(
  analysisObjectList: AnalysisObject[],
  highlightType?: HighlightStyleType,
): HighlightDataItem[] {
  const list: AnalysisUiDataItem[] = analysisObjectList.map((m) => ({
    ...m,
    oname: m.gisOName ?? m.oname,
    otype: m.gisOType ?? m.otype,
    shape: m.gisShape ?? m.shape,
  }));
  return getAnalysisHighlightData(list, undefined, {
    highlightType: highlightType ?? HIGHLIGHT_SELECT,
  });
}

export function getWaterOutageAreaHighlightData(
  waterOutageArea: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(waterOutageArea, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getAffectedAreaUserHighlightData(
  affectedAreaUser: AnalysisUiDataItem[],
  db: Database,
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  if (!affectedAreaUser.length) return [];
  const { otype, shape } = affectedAreaUser[0];
  const highlightIcon = db.getIconByShape(otype, shape ?? '');
  return getAnalysisHighlightData(affectedAreaUser, themeConfig, {
    highlightIcon,
    highlightColor,
    highlightShowMark,
  });
}

export function getWaterOutageAreaUserHighlightData(
  waterOutageAreaUser: AnalysisUiDataItem[],
  db: Database,
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  if (!waterOutageAreaUser.length) return [];
  const { otype, shape } = waterOutageAreaUser[0];
  const highlightIcon = db.getIconByShape(otype, shape ?? '');
  return getAnalysisHighlightData(waterOutageAreaUser, themeConfig, {
    highlightIcon,
    highlightColor,
    highlightShowMark,
  });
}

export function getValveListHighlightData(
  valveList: AnalysisUiDataItem[],
  db: Database,
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  if (!valveList.length) return [];
  const { otype, shape } = valveList[0];
  const highlightIcon = db.getIconByShape(otype, shape ?? '');
  const formatHighlightTextList = valveList.map((item, index) => ({
    ...item,
    highlightText: `顺序: ${index + 1}`,
  }));
  return getAnalysisHighlightData(formatHighlightTextList, themeConfig, {
    highlightIcon,
    highlightColor,
    highlightShowMark,
  });
}

export function getAffectedAreaHighlightData(
  affectedArea: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(affectedArea, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getClosedPipelineHighlightData(
  closedPipeline: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(closedPipeline, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getAffectedPipelineHighlightData(
  affectedPipeline: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(affectedPipeline, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getReversePipelineHighlightData(
  reversePipeline: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(reversePipeline, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getCloseNodeHighlightData(
  closeNode: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(closeNode, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getAffectedNodeHighlightData(
  affectedNode: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(affectedNode, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getWaterReverseAffectedAreaHighlightData(
  waterReverseAffectedArea: AnalysisUiDataItem[],
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  return getAnalysisHighlightData(waterReverseAffectedArea, themeConfig, {
    highlightColor,
    highlightShowMark,
  });
}

export function getWaterReverseAffectedAreaUserHighlightData(
  waterReverseAffectedAreaUser: AnalysisUiDataItem[],
  db: Database,
  themeConfig?: LegendGroupData,
  highlightColor?: string,
  highlightShowMark?: boolean,
): HighlightDataItem[] {
  if (!waterReverseAffectedAreaUser.length) return [];
  const { otype, shape } = waterReverseAffectedAreaUser[0];
  const highlightIcon = db.getIconByShape(otype, shape ?? '');
  return getAnalysisHighlightData(waterReverseAffectedAreaUser, themeConfig, {
    highlightIcon,
    highlightColor,
    highlightShowMark,
  });
}

export function getAnalysisResultHighlightDataTheme(
  activeThemes: {
    [key in AnalysisUiConfigType]?: string;
  },
  analysisThemesConfig: { [key in AnalysisUiConfigType]?: LegendGroupData[] },
): {
  [key in AnalysisUiConfigType]?: LegendGroupData;
} {
  const analysisResultHighlightDataTheme: {
    [key in AnalysisUiConfigType]?: LegendGroupData;
  } = {};

  Object.entries(activeThemes).forEach((item) => {
    const [key, name] = item as [AnalysisUiConfigType, string];
    const data = analysisThemesConfig[key]?.find((item) => item.name === name);
    if (data) {
      analysisResultHighlightDataTheme[key] = data;
    }
  });

  return analysisResultHighlightDataTheme;
}

export function getAnalysisResultHightData(
  db: Database,
  analysisResult: AnalysisResultData,
  analysisConfig: AnalysisConfig | undefined,
  highlightDataTheme: { [key in AnalysisUiConfigType]?: LegendGroupData },
  dimension: boolean,
  highlightNode: boolean,
): {
  [index: string]: HighlightDataItem[];
} {
  const {
    waterOutageArea,
    waterOutageAreaUser,
    valveList,
    affectedArea,
    affectedAreaUser,
    closedPipeline,
    affectedPipeline,
    reversePipeline,
    closeNode,
    affectedNode,
    waterReverseAffectedArea,
    waterReverseAffectedAreaUser,
  } = analysisResult;

  const filterWaterOutageArea = filterArrayByKeyName(
    waterOutageArea,
    affectedArea,
    'oname',
  );

  return {
    waterOutageArea: analysisConfig?.waterOutageArea
      ? getWaterOutageAreaHighlightData(
          filterWaterOutageArea,
          highlightDataTheme.waterOutageArea,
          analysisConfig.waterOutageArea.defaultHighlightColor,
          dimension,
        )
      : [],
    waterOutageAreaUser: analysisConfig?.waterOutageAreaUser
      ? getWaterOutageAreaUserHighlightData(
          waterOutageAreaUser,
          db,
          highlightDataTheme.waterOutageAreaUser,
          analysisConfig.waterOutageAreaUser.defaultHighlightColor,
          dimension,
        )
      : [],
    valveList: analysisConfig?.valveList
      ? getValveListHighlightData(
          valveList,
          db,
          highlightDataTheme.valveList,
          analysisConfig.valveList.defaultHighlightColor,
          dimension,
        )
      : [],
    affectedArea: analysisConfig?.affectedArea
      ? getAffectedAreaHighlightData(
          affectedArea,
          highlightDataTheme.affectedArea,
          analysisConfig.affectedArea.defaultHighlightColor,
          dimension,
        )
      : [],
    affectedAreaUser: analysisConfig?.affectedAreaUser
      ? getAffectedAreaUserHighlightData(
          affectedAreaUser,
          db,
          highlightDataTheme.affectedAreaUser,
          analysisConfig.affectedAreaUser.defaultHighlightColor,
          dimension,
        )
      : [],
    closedPipeline: analysisConfig?.closedPipeline
      ? getClosedPipelineHighlightData(
          closedPipeline,
          highlightDataTheme.closedPipeline,
          analysisConfig.closedPipeline.defaultHighlightColor,
          dimension,
        )
      : [],
    affectedPipeline: analysisConfig?.affectedPipeline
      ? getAffectedPipelineHighlightData(
          affectedPipeline,
          highlightDataTheme.affectedPipeline,
          analysisConfig.affectedPipeline.defaultHighlightColor,
          dimension,
        )
      : [],
    reversePipeline: analysisConfig?.reversePipeline
      ? getReversePipelineHighlightData(
          reversePipeline,
          highlightDataTheme.reversePipeline,
          analysisConfig.reversePipeline.defaultHighlightColor,
          dimension,
        )
      : [],
    closeNode:
      highlightNode && analysisConfig?.waterOutageAreaUser
        ? getCloseNodeHighlightData(
            closeNode,
            highlightDataTheme.waterOutageAreaUser,
            analysisConfig.waterOutageAreaUser.defaultHighlightColor,
            dimension,
          )
        : [],
    affectedNode:
      highlightNode && analysisConfig?.affectedAreaUser
        ? getAffectedNodeHighlightData(
            affectedNode,
            highlightDataTheme.affectedAreaUser,
            analysisConfig.affectedAreaUser.defaultHighlightColor,
            dimension,
          )
        : [],
    waterReverseAffectedArea: analysisConfig?.waterReverseAffectedArea
      ? getWaterReverseAffectedAreaHighlightData(
          waterReverseAffectedArea,
          highlightDataTheme.waterReverseAffectedArea,
          analysisConfig.waterReverseAffectedArea.defaultHighlightColor,
          dimension,
        )
      : [],
    waterReverseAffectedAreaUser: analysisConfig?.waterReverseAffectedAreaUser
      ? getWaterReverseAffectedAreaUserHighlightData(
          waterReverseAffectedAreaUser,
          db,
          highlightDataTheme.waterReverseAffectedAreaUser,
          analysisConfig.waterReverseAffectedAreaUser.defaultHighlightColor,
          dimension,
        )
      : [],
  };
}

export function mergeSameRoadNameAndDiameter(
  data: AnalysisUiDataItem[],
): AnalysisUiDataItem[] {
  const groupedArr = data.reduce((acc, obj) => {
    const key = `${obj.ROAD_NAME}-${obj.DIAMETER}`;
    if (!acc.has(key)) {
      acc.set(key, []);
    }
    acc.get(key).push(obj);
    return acc;
  }, new Map());

  const mergedArr: AnalysisUiDataItem[] = [...groupedArr.values()].map(
    (group: AnalysisUiDataItem[]) => {
      const result: AnalysisUiDataItem = {
        ...group[0],
        ROAD_NAME: group[0].ROAD_NAME,
        DIAMETER: group[0].DIAMETER,
      };
      const numberProps = Object.entries(group[0])
        .filter(([, value]) => typeof value === 'number')
        .map(([key]) => key);
      numberProps.forEach((prop) => {
        const values = group.map((obj) => obj[prop]) as number[];
        const { min, max } = getMinMax(values);
        const value = min === max ? min : `${min}~${max}`;
        result[prop] = value;
      });
      return result;
    },
  );

  return mergedArr;
}

export function getAllSelectedByMergeRoadName(
  selectedKeys: string[],
  dataSource: AnalysisUiDataItem[],
): AnalysisUiDataItem[] {
  const roadNameAndDiameterArr = dataSource
    .filter((item) => selectedKeys.includes(item.oname))
    .map((item) => `${item.ROAD_NAME}-${item.DIAMETER}`);
  return dataSource.filter((item) =>
    roadNameAndDiameterArr.includes(`${item.ROAD_NAME}-${item.DIAMETER}`),
  );
}

export function getAllSelected(
  selectedKeys: string[],
  dataSource: AnalysisUiDataItem[],
): AnalysisUiDataItem[] {
  return dataSource.filter((item) => selectedKeys.includes(item.oname));
}

export function getPipelineLength(
  data: AnalysisUiDataItem[],
  precision?: number,
): number {
  const length =
    data.reduce((prev, current) => {
      if (typeof current.LENGTH === 'number') return prev + current.LENGTH;
      return prev;
    }, 0) / 1000;
  return formatNumber(length, precision ?? 2);
}

// USER_METER_COUNT
export function getWaterMeterCount(
  resultData: AnalysisResultData[
    | 'affectedArea'
    | 'waterOutageArea'
    | 'waterReverseAffectedArea'],
): number {
  return resultData.reduce(
    (prev, curr) =>
      prev +
      (typeof curr?.USER_METER_COUNT === 'number' ? curr.USER_METER_COUNT : 0),
    0,
  );
}

export function getFlowVelocity(flow: number, diameter: number): number {
  const r = diameter / 1000 / 2;
  const area = Math.PI * r * r;
  return formatNumber(flow / 3600 / area, 1);
}

export function getAsyncExportParams(
  dataSource: AnalysisUiDataItem[],
): QueryParams['params'] {
  const paramsMap: Map<string, Set<string>> = new Map();
  const params: { [index: string]: string } = {};

  dataSource.forEach(({ otype, oname }) => {
    const set = paramsMap.get(otype) ?? new Set();
    set.add(oname);
    if (!paramsMap.get(otype)) {
      paramsMap.set(otype, set);
    }
  });
  Array.from(paramsMap.entries()).forEach((item) => {
    const [otype, onameSet] = item;
    params[otype] = [...onameSet.values()].join(',');
  });

  return params;
}

export function exportAnalysisData(
  data: { [index: string]: string | number | boolean | undefined | null }[],
  columns: { dataIndex: string; title: string; [index: string]: unknown }[],
  fileName: string,
) {
  const columnsData = columns.map(({ dataIndex, title }) => ({
    dataIndex,
    title,
  }));
  exportToExcel(
    data,
    columnsData,
    `${fileName}_${dayjs().format('YYYYMMDD_HHmmss')}`,
    undefined,
    '-',
  );
}

export function exportMultipleAnalysisData(
  sheets: Array<{
    data: { [index: string]: string | number | boolean | undefined | null }[];
    columns: { dataIndex: string; title: string; [index: string]: unknown }[];
    fileName: string;
  }>,
  fileName: string,
) {
  const sheetData: Array<ExcelSheetData> = [];
  sheets.forEach((sheet) => {
    const columnsData = sheet.columns.map(({ dataIndex, title }) => ({
      dataIndex,
      title,
    }));
    sheetData.push({
      dataSource: sheet.data,
      columns: columnsData,
      sheetName: sheet.fileName,
      placeholderWithSheet: '-',
    });
  });
  exportToExcelWithMultipleSheets(
    sheetData,
    `${fileName}_${dayjs().format('YYYYMMDD_HHmmss')}`,
    '-',
  );
}

export const getReviewStatusTypeName = (
  reviewStatus: ResultReviewStatus,
): string => {
  switch (reviewStatus) {
    case ResultReviewStatus.NoReviewNeeded:
      return '无需审核';
    case ResultReviewStatus.PendingReview:
      return '待审核';
    case ResultReviewStatus.ReviewPassed:
      return '审核通过';
    case ResultReviewStatus.ReviewFailed:
      return '审核未通过';
    default:
      return reviewStatus;
  }
};

export const getQuickAnalysisTypeName = (type: QuickAnalysisType): string => {
  switch (type) {
    case QuickAnalysisType.ValveAnalysis:
      return '关阀分析';
    case QuickAnalysisType.BurstFlushing:
      return '爆管分析';
    case QuickAnalysisType.WaterShutdown:
      return '停水方案';
    default:
      return type;
  }
};

export const convertReviewStatusToSelectOptions = (): {
  label: string;
  value: number | string;
}[] =>
  Object.values(ResultReviewStatus)
    .filter((f) => !Number.isNaN(Number(f)))
    .map((key) => {
      const label = getReviewStatusTypeName(
        key as unknown as ResultReviewStatus,
      );
      return {
        label,
        value: key,
      };
    });

export const convertAnalysisTypeToSelectOptions = (): {
  label: string;
  value: number | string;
}[] =>
  Object.values(QuickAnalysisType)
    .filter((f) => !Number.isNaN(Number(f)))
    .map((key) => {
      const label = getQuickAnalysisTypeName(
        key as unknown as QuickAnalysisType,
      );
      return {
        label,
        value: key,
      };
    });

export const getPanelHeaderCountText = (
  panelKey: AnalysisUiConfigType,
  analysisResultData: AnalysisResultData,
): string => {
  switch (panelKey) {
    case 'affectedArea':
    case 'waterOutageArea':
    case 'waterReverseAffectedArea':
      return `(总数:${
        analysisResultData[panelKey].length
      }, 用户表数量:${getWaterMeterCount(analysisResultData[panelKey])})`;
    case 'waterReverseAffectedAreaUser':
      return `(${analysisResultData.waterReverseAffectedAreaUserCount})`;
    case 'affectedAreaUser':
      return `(${analysisResultData.affectedAreaUserCount})`;
    case 'waterOutageAreaUser':
      return `(${analysisResultData.waterOutageAreaUserCount})`;
    case 'valveList':
      return `(${analysisResultData[panelKey].length})`;
    case 'affectedPipeline':
    case 'closedPipeline':
    case 'reversePipeline':
      return `(${getPipelineLength(analysisResultData[panelKey], 2)}km)`;
    default:
      return '';
  }
};

export const formatDiffValue = (record: PlantStationInfo): number | string =>
  typeof record.newValue === 'number' && typeof record.oldValue === 'number'
    ? formatNumber(
        record.newValue - record.oldValue,
        record.unitFormat?.valuePrecision ?? 0,
      )
    : '';

export const getPlantStationHeaderTitle = (type: PlantStationType): string =>
  type === 'plants' ? '水厂变化' : '泵站变化';

export const getEnergyHeaderTitle = (): string => '能耗变化';

export const getPanelHeaderTitle = (
  panelKey: AnalysisUiConfigType,
  analysisUiConfig: AnalysisConfig,
): string => {
  const title = analysisUiConfig[panelKey]?.title;
  if (typeof title !== 'undefined') return title;
  switch (panelKey) {
    case 'waterOutageArea':
      return '停水小区';
    case 'waterOutageAreaUser':
      return '停水小区外用户';
    case 'affectedArea':
      return '压力受影响小区';
    case 'affectedAreaUser':
      return '压力受影响小区外用户';
    case 'affectedPipeline':
      return '流速受影响管道';
    case 'closedPipeline':
      return '关闭的管道';
    case 'valveList':
      return '需要关闭的阀门';
    case 'reversePipeline':
      return '反向管道';
    case 'waterReverseAffectedArea':
      return '反向水流影响小区';
    case 'waterReverseAffectedAreaUser':
      return '反向水流影响小区外用户';
    default:
      return '';
  }
};

export const getTableDataSource = (
  panelKey: AnalysisUiConfigType | undefined,
  analysisData: AnalysisResultData,
): AnalysisUiDataItem[] => {
  const {
    waterOutageArea: waterOutageAreaData,
    waterOutageAreaUser: waterOutageAreaUserData,
    closedPipeline: closedPipelineData,
    valveList: valveListData,
    affectedArea: affectedAreaData,
    affectedAreaUser: affectedAreaUserData,
    affectedPipeline: affectedPipelineData,
    reversePipeline: reversePipelineData,
    waterReverseAffectedArea: waterReverseAffectedAreaData,
    waterReverseAffectedAreaUser: waterReverseAffectedAreaUserData,
  } = analysisData;
  switch (panelKey) {
    case 'waterOutageArea':
      return waterOutageAreaData;
    case 'waterOutageAreaUser':
      return waterOutageAreaUserData;
    case 'affectedArea':
      return affectedAreaData;
    case 'affectedAreaUser':
      return affectedAreaUserData;
    case 'affectedPipeline':
      return mergeSameRoadNameAndDiameter(affectedPipelineData);
    case 'closedPipeline':
      return mergeSameRoadNameAndDiameter(closedPipelineData);
    case 'valveList':
      return valveListData;
    case 'reversePipeline':
      return mergeSameRoadNameAndDiameter(reversePipelineData);
    case 'waterReverseAffectedArea':
      return waterReverseAffectedAreaData;
    case 'waterReverseAffectedAreaUser':
      return waterReverseAffectedAreaUserData;
    default:
      return [];
  }
};

export const getPlantsStationsDataByType = (
  type: PlantStationType | undefined,
  plantStation: AnalysisResultData['plantStation'],
): PlantStationInfo[] => {
  if (typeof type === 'undefined') return [];
  const { plants, pumpStations } = plantStation;
  const dataSource = type === 'plants' ? plants : pumpStations;
  return dataSource.map((m) => ({
    ...m,
    diffValue: formatDiffValue(m),
    title: m.unitFormat?.unitSymbol
      ? `${m.title}(${m.unitFormat?.unitSymbol})`
      : m.title,
  }));
};

export const getAnalysisLegendData = (
  activeThemes: Partial<Record<AnalysisUiConfigType, string>>,
  analysisThemesConfig: Partial<
    Record<AnalysisUiConfigType, LegendGroupData[]>
  >,
  analysisConfig: AnalysisConfig,
): LegendGroupData[] => {
  const legendGroupData: LegendGroupData[] = [];
  Object.entries(activeThemes).forEach((item, index) => {
    const [panelKey, activeKey] = item as [AnalysisUiConfigType, string];
    const themeConfig = analysisThemesConfig[panelKey];
    const themeItems = themeConfig?.find((item) => item.name === activeKey);

    if (themeItems) {
      legendGroupData.push({
        ...themeItems,
        key: `${themeItems.name}@${index}`,
        title: `${getPanelHeaderTitle(panelKey, analysisConfig)}:${
          themeItems?.title
        }`,
      });
    }
  });
  return legendGroupData;
};

export const DefaultValveHighlightColor = 'select';
