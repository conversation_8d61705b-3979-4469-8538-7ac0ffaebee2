/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Docxtemplater from 'docxtemplater';
import expressionParser from 'docxtemplater/expressions';
import { saveAs } from 'file-saver';
import PizZip from 'pizzip';
import PizZipUtils from 'pizzip/utils/index';
import Database from '../database';
import {
  ComparisonResult,
  exportComparisonResultToExcel,
} from '../export-solution-excel';
import { makeId } from '../object-item';
import { SchedulingData } from '../scheduling-data';
import { CompareSourceType, getSolutionSourceName } from '../solution';
import {
  getModifyChangeDescription,
  getSolutionDemandChangeDescription,
  getSolutionObservationsDescription,
  getSolutionPlantsChangesDescription,
  getSolutionPumpChangesDescription,
  getSolutionQualityChangeDescription,
  getSolutionValvesChangesDescription,
  getSolutionWaterChangesDescription,
  SolutionBaseInfo,
  SolutionDetail,
  SolutionInfo,
  SolutionModifyItem,
} from '../solution-detail';
import {
  ExcelSheetData,
  exportToExcelWithMultipleSheets,
  formatNumber,
} from '../utils';
import {
  AnalysisConfig,
  AnalysisResultData,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  getAnalysisUiConfig,
  getCollapseItemKeysByOrder,
  getPipelineLength,
  getPlantStationHeaderTitle,
  getWaterMeterCount,
  PlantStationInfo,
  PlantStationType,
} from './quick-analysis-data';

export type AdvanceSettingValue = {
  value: number;
  name?: string;
};

export type AdvanceSettingType =
  | 'nodeAbsPress'
  | 'nodeLowPress'
  | 'linkMinFlow'
  | 'linkMinSpeed'
  | 'linkMinDiam'
  | 'linkMinChange'
  | 'linkMaxChange'
  | 'linkMinChangeVelocity'
  | 'linkMaxChangeVelocity'
  | 'linkReverseMinVelocity';

type TextList = {
  text: string;
  id?: string;
}[];
export interface AnalysisReportData {
  name: string;
  time: string;
  simulationStartTime: string;
  simulationEndTime: string;
  calculateStartTime: string;
  calculateEndTime: string;
  baseDayDate: string;
  totalFlow: number;
  source: string;
  creator: string;
  note: string;
  totalDemandChange: TextList;
  plantChange: TextList;
  pumpStationChange: TextList;
  linkStatusChange: TextList;
  demandChange: TextList;
  qualityChange: TextList;
  modifyList: TextList;
  observationSettings: TextList;
  options: {
    hydraulicTimestep: string;
    demandModel: string;
    qualityTimestep: string;
    servicePressure: string;
  };
}

export interface ReportAnalysisData {
  plantStation: {
    plants: (PlantStationInfo & { diffValue: number | string })[];
    pumpStations: (PlantStationInfo & { diffValue: number | string })[];
  };
  valveList: AnalysisUiDataItem[];
  waterOutageAreaCount: number;
  waterOutageAreaMeterCount: number;
  waterOutageAreaUserCount: number;
  affectedAreaCount: number;
  affectedAreaMeterCount: number;
  affectedAreaUserCount: number;
  closedPipeLength: number;
  affectedPipelineLength: number;
  reversePipelineLength: number;
  waterReverseAffectedAreaCount: number;
  waterReverseAffectedAreaMeterCount: number;
  waterReverseAffectedAreaUserCount: number;
}

export type AnalysisReport = {
  analysisResult: ReportAnalysisData;
  solution: AnalysisReportData;
  compare: AnalysisReportData | undefined;
  compareDateTime: string;
  solutionDateTime: string;
  compareSource: CompareSourceType;
};

export type MultipleAnalysisReportData = AnalysisReportData & {
  solutionId: string;
  analysisResult: ReportAnalysisData;
  compareDateTime: string;
};

type SimulationPlantOrPumpChangeItem = {
  ptitle: string;
  title: string;
  newValue: number;
  [index: string]: string | number;
};

export type MultipleAnalysisReport = {
  solution: AnalysisReportData;
  compare: MultipleAnalysisReportData[];
  solutionDateTime: string;
  plantChanges: SimulationPlantOrPumpChangeItem[];
  pumpChanges: SimulationPlantOrPumpChangeItem[];
  showList: {
    key: AnalysisUiConfigType | PlantStationType;
    title: string;
    dataKey?: keyof ReportAnalysisData;
    subKey?: keyof ReportAnalysisData;
  }[];
};

export type SolutionAnalysisAdvanceSetting = Record<
  AdvanceSettingType,
  AdvanceSettingValue
>;

export function getUpdatedAdvanceSetting(
  defaultSetting: SolutionAnalysisAdvanceSetting,
  formSetting: Partial<Record<AdvanceSettingType, number>>,
): Partial<SolutionAnalysisAdvanceSetting> {
  const compareSetting: Partial<SolutionAnalysisAdvanceSetting> = {};
  Object.entries(defaultSetting).forEach((item) => {
    const [key, value] = item;
    const formSettingValue = formSetting[key as AdvanceSettingType];
    if (
      key in formSetting &&
      typeof formSettingValue !== 'undefined' &&
      formSettingValue !== value.value
    ) {
      compareSetting[key as AdvanceSettingType] = {
        ...value,
        value: formSettingValue,
      };
    }
  });
  return compareSetting;
}

export function convertPlantStationReport(
  data: PlantStationInfo[],
): (PlantStationInfo & { diffValue: number | string })[] {
  const convertData: (PlantStationInfo & { diffValue: number | string })[] = [];

  data.forEach((item) => {
    const newItem = {
      ...item,
      title: item.unitFormat?.unitSymbol
        ? `${item.title}(${item.unitFormat?.unitSymbol})`
        : item.title,
      newValue: item.newValue ?? '',
      oldValue: item.oldValue ?? '',
      diffValue:
        typeof item.newValue === 'number' && typeof item.oldValue === 'number'
          ? formatNumber(
              item.newValue - item.oldValue,
              item.unitFormat?.valuePrecision ?? 0,
            )
          : '',
    };

    convertData.push(newItem);
  });

  return convertData;
}

export function getReportAnalysisData(
  analysisResultData: AnalysisResultData,
): ReportAnalysisData {
  const data: ReportAnalysisData = {
    plantStation: {
      plants: [],
      pumpStations: [],
    },
    valveList: analysisResultData.valveList,
    waterOutageAreaCount: analysisResultData.waterOutageArea.length,
    waterOutageAreaMeterCount: getWaterMeterCount(
      analysisResultData.waterOutageArea,
    ),
    waterOutageAreaUserCount: analysisResultData.waterOutageAreaUserCount,
    affectedAreaCount: analysisResultData.affectedArea.length,
    affectedAreaMeterCount: getWaterMeterCount(analysisResultData.affectedArea),
    affectedAreaUserCount: analysisResultData.affectedAreaUserCount,
    closedPipeLength: getPipelineLength(analysisResultData.closedPipeline),
    affectedPipelineLength: getPipelineLength(
      analysisResultData.affectedPipeline,
    ),
    reversePipelineLength: getPipelineLength(
      analysisResultData.reversePipeline,
    ),
    waterReverseAffectedAreaCount:
      analysisResultData.waterReverseAffectedArea.length,
    waterReverseAffectedAreaMeterCount: getWaterMeterCount(
      analysisResultData.waterReverseAffectedArea,
    ),
    waterReverseAffectedAreaUserCount:
      analysisResultData.waterReverseAffectedAreaUserCount,
  };

  data.plantStation.plants = convertPlantStationReport(
    analysisResultData.plantStation.plants,
  );
  data.plantStation.pumpStations = convertPlantStationReport(
    analysisResultData.plantStation.pumpStations,
  );

  return data;
}

export function getSolutionReportData(
  baseInfo: SolutionBaseInfo | undefined,
  details: SolutionDetail | undefined,
  db: Database,
  pumpListInfo: SchedulingData | undefined,
  modifyList: SolutionModifyItem[],
): AnalysisReportData {
  const data: AnalysisReportData = {
    name: baseInfo?.name ?? '-',
    simulationStartTime: details?.simulationStartTime ?? '-',
    simulationEndTime: details?.simulationEndTime ?? '-',
    calculateStartTime: details?.calculateStartTime ?? '-',
    calculateEndTime: details?.calculateEndTime ?? '-',
    time: details?.calculateStartTime ?? '-',
    baseDayDate: details?.baseDayInfo.date ?? '-',
    totalFlow: details
      ? formatNumber(details.baseDayInfo.totalFlow / 10000, 2)
      : 0,
    source: getSolutionSourceName(baseInfo?.sourceType ?? '-'),
    creator: baseInfo?.creator ?? '-',
    note: baseInfo?.note ?? '-',
    totalDemandChange: [],
    plantChange: [],
    pumpStationChange: [],
    linkStatusChange: [], // 管阀状态
    demandChange: [], // 节点水量
    qualityChange: [], // 污染物设置
    modifyList: [], // todo: modifyList不在details信息中，获取相当麻烦，需要单独接口查询。多方案对比时还需处理不同view情况
    options: {
      hydraulicTimestep: details?.options.HydraulicTimestep ?? '-',
      demandModel: details?.options.DemandModel ?? '-',
      qualityTimestep: details?.options.QualityTimestep ?? '-',
      servicePressure: details?.options.ServicePressure?.toString() ?? '-',
    },
    observationSettings: [],
  };

  data.totalDemandChange = getSolutionWaterChangesDescription(
    details?.flowInfo.changes ?? [],
  ).map((m) => ({
    text: m,
  }));
  data.plantChange = getSolutionPlantsChangesDescription(
    details?.plantChangeSettings ?? new Map(),
    pumpListInfo,
  );
  data.pumpStationChange = getSolutionPumpChangesDescription(
    details?.pumpStationChangeSettings ?? new Map(),
    pumpListInfo,
  );
  data.linkStatusChange = getSolutionValvesChangesDescription(
    details?.linkStatusChangeSettings ?? [],
  ).map((m) => ({
    text: m,
  }));
  data.demandChange = getSolutionDemandChangeDescription(
    details?.demandChangeSettings ?? [],
  );
  data.qualityChange = getSolutionQualityChangeDescription(
    details?.qualityChangeSettings ?? [],
  );
  data.modifyList = getModifyChangeDescription(modifyList, db);

  data.observationSettings = getSolutionObservationsDescription(
    details?.observationSettings ?? [],
  );

  return data;
}

export function getReportData(options: {
  baseInfo: SolutionBaseInfo;
  details: SolutionDetail;
  compareBaseInfo: SolutionBaseInfo | undefined;
  compareDetails: SolutionDetail | undefined;
  db: Database;
  pumpListInfo: SchedulingData | undefined;
  modifyList: SolutionModifyItem[];
  compareModifyList: SolutionModifyItem[];
  analysisResult: AnalysisResultData;
  compareDateTime: string;
  solutionDateTime: string;
  compareSource: CompareSourceType;
}): AnalysisReport {
  const {
    baseInfo,
    details,
    compareBaseInfo,
    compareDetails,
    db,
    pumpListInfo,
    modifyList,
    compareModifyList,
    analysisResult,
    compareDateTime,
    solutionDateTime,
    compareSource,
  } = options;
  const solutionReportData = getSolutionReportData(
    baseInfo,
    details,
    db,
    pumpListInfo,
    modifyList,
  );

  const compareReportData = getSolutionReportData(
    compareBaseInfo,
    compareDetails,
    db,
    pumpListInfo,
    compareModifyList,
  );

  return {
    solution: solutionReportData,
    compare: compareReportData,
    analysisResult: getReportAnalysisData(analysisResult),
    compareDateTime,
    solutionDateTime,
    compareSource,
  };
}

export function exportAnalysisReport(
  templateDocx: any,
  reportData: AnalysisReport,
  sheets?: Array<ExcelSheetData>,
): void {
  PizZipUtils.getBinaryContent(templateDocx, (error, content) => {
    if (error) {
      throw error;
    }
    const zip = new PizZip(content);
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      parser: expressionParser,
    });
    doc.render(reportData);
    const out = doc.getZip().generate({
      type: 'blob',
      mimeType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    saveAs(out, `快速对比结果报告_${dayjs().format('YYYYMMDD_HHmmss')}.docx`);
    if (sheets?.length) {
      exportToExcelWithMultipleSheets(
        sheets,
        `附录_${dayjs().format('YYYYMMDD_HHmmss')}`,
        '-',
      );
    }
  });
}

function getReportPanelTitle(
  panelKey: AnalysisUiConfigType,
  analysisConfig: AnalysisConfig,
): string {
  switch (panelKey) {
    case 'waterOutageArea':
      return `${analysisConfig[panelKey]?.title ?? '停水小区'}(用户表)`;
    case 'waterOutageAreaUser':
      return analysisConfig[panelKey]?.title ?? '停水小区外用户';
    case 'affectedArea':
      return `${analysisConfig[panelKey]?.title ?? '压力受影响小区'}(用户表)`;
    case 'affectedAreaUser':
      return analysisConfig[panelKey]?.title ?? '压力受影响小区外用户';
    case 'affectedPipeline':
      return `${analysisConfig[panelKey]?.title ?? '流速受影响管道'}km`;
    case 'closedPipeline':
      return `${analysisConfig[panelKey]?.title ?? '关闭的管道'}km`;
    case 'valveList':
      return analysisConfig[panelKey]?.title ?? '需要关闭的阀门';
    case 'reversePipeline':
      return `${analysisConfig[panelKey]?.title ?? '反向管道'}km`;
    case 'waterReverseAffectedArea':
      return `${analysisConfig[panelKey]?.title ?? '反向水流影响小区'}(用户表)`;
    case 'waterReverseAffectedAreaUser':
      return analysisConfig[panelKey]?.title ?? '反向水流影响小区外用户';
    default:
      return '';
  }
}

function getCountProps(
  panelKey: AnalysisUiConfigType,
): keyof ReportAnalysisData | Array<keyof ReportAnalysisData> | undefined {
  switch (panelKey) {
    case 'affectedArea':
      return ['affectedAreaCount', 'affectedAreaMeterCount'];
    case 'affectedAreaUser':
      return 'affectedAreaUserCount';
    case 'affectedPipeline':
      return 'affectedPipelineLength';
    case 'reversePipeline':
      return 'reversePipelineLength';
    case 'closedPipeline':
      return 'closedPipeLength';
    case 'valveList':
      return 'valveList';
    case 'waterOutageArea':
      return ['waterOutageAreaCount', 'waterOutageAreaMeterCount'];
    case 'waterOutageAreaUser':
      return 'waterOutageAreaUserCount';
    case 'waterReverseAffectedArea':
      return [
        'waterReverseAffectedAreaCount',
        'waterReverseAffectedAreaMeterCount',
      ];
    case 'waterReverseAffectedAreaUser':
      return 'waterReverseAffectedAreaUserCount';
    default:
      return undefined;
  }
}

export function getMultipleReportData(options: {
  baseInfo: SolutionBaseInfo;
  details: SolutionDetail;
  compareSolutions: SolutionInfo[];
  analysisResults: {
    results: AnalysisResultData;
    taskId: string;
    solutionId: string;
  }[];
  pumpListInfo: SchedulingData | undefined;
  db: Database;
  compareTime: number;
  analysisConfig?: AnalysisConfig;
}): MultipleAnalysisReport {
  const {
    baseInfo,
    details,
    compareSolutions,
    analysisResults,
    pumpListInfo,
    db,
    compareTime,
    analysisConfig,
  } = options;
  const solutionDateTime = dayjs(baseInfo.startTime, 'YYYY-MM-DD 00:00:00')
    .add(compareTime, 'minutes')
    .format('YYYY-MM-DD HH:mm:ss');
  const data: MultipleAnalysisReport = {
    solution: getSolutionReportData(baseInfo, details, db, pumpListInfo, []),
    compare: [],
    solutionDateTime,
    plantChanges: [],
    pumpChanges: [],
    showList: [],
  };

  data.showList.push({
    key: 'plants',
    title: getPlantStationHeaderTitle('plants'),
  });
  data.showList.push({
    key: 'pumpStations',
    title: getPlantStationHeaderTitle('pumpStations'),
  });

  if (analysisConfig) {
    const analysisUiConfig = getAnalysisUiConfig(analysisConfig);
    const sortCollapsePanelKeys = getCollapseItemKeysByOrder(analysisUiConfig);
    sortCollapsePanelKeys.forEach((panelKey) => {
      const props = getCountProps(panelKey);
      if (!props) return;
      if (Array.isArray(props)) {
        data.showList.push({
          key: panelKey,
          title: `${getReportPanelTitle(panelKey, analysisConfig)}`,
          dataKey: props[0],
          subKey: props[1],
        });
      } else {
        data.showList.push({
          key: panelKey,
          title: `${getReportPanelTitle(panelKey, analysisConfig)}`,
          dataKey: props,
        });
      }
    });
  }
  compareSolutions.forEach((compareSolution) => {
    const compareReportData = getSolutionReportData(
      compareSolution.baseInfo,
      compareSolution.detail,
      db,
      pumpListInfo,
      [],
    );
    const compareDateTime = dayjs(
      compareSolution.baseInfo.startTime,
      'YYYY-MM-DD 00:00:00',
    )
      .add(compareTime, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    const analysisResult = analysisResults.find(
      (f) => compareSolution.baseInfo.id === f.solutionId,
    );
    if (analysisResult) {
      data.compare.push({
        ...compareReportData,
        analysisResult: getReportAnalysisData(analysisResult.results),
        compareDateTime,
        solutionId: compareSolution.baseInfo.id,
      });
    }
  });
  const resultPlantChanges: Map<string, SimulationPlantOrPumpChangeItem>[] = [];
  const resultPumpChanges: Map<string, SimulationPlantOrPumpChangeItem>[] = [];

  data.compare.forEach((item) => {
    const plantMap: Map<string, SimulationPlantOrPumpChangeItem> = new Map();
    const pumpMap: Map<string, SimulationPlantOrPumpChangeItem> = new Map();
    item.analysisResult.plantStation.plants.forEach(
      ({
        ptype,
        pname,
        otype,
        vprop,
        ptitle,
        title,
        oldValue,
        newValue,
        diffValue,
      }) => {
        plantMap.set(makeId(ptype, pname, otype, vprop, title), {
          ptitle,
          title,
          oldValue,
          newValue,
          diffValue,
        });
      },
    );
    item.analysisResult.plantStation.pumpStations.forEach(
      ({
        ptype,
        pname,
        otype,
        vprop,
        ptitle,
        title,
        oldValue,
        newValue,
        diffValue,
      }) => {
        pumpMap.set(makeId(ptype, pname, otype, vprop, title), {
          ptitle,
          title,
          oldValue,
          newValue,
          diffValue,
        });
      },
    );
    resultPlantChanges.push(plantMap);
    resultPumpChanges.push(pumpMap);
  });

  const plantChanges: SimulationPlantOrPumpChangeItem[] = [];
  const pumpChanges: SimulationPlantOrPumpChangeItem[] = [];

  if (
    data.compare.length > 0 &&
    data.compare[0].analysisResult.plantStation.plants.length > 0
  ) {
    data.compare[0].analysisResult.plantStation.plants.forEach((item) => {
      const key = makeId(
        item.ptype,
        item.pname,
        item.otype,
        item.vprop,
        item.title,
      );
      const data: SimulationPlantOrPumpChangeItem = {
        ptitle: item.ptitle,
        title: item.title,
        newValue: item.newValue,
      };
      resultPlantChanges.forEach((map, i) => {
        const value = map.get(key);
        if (value) {
          data[`oldValue${i + 1}`] = value.oldValue;
          data[`diffValue${i + 1}`] = value.diffValue;
        }
      });
      plantChanges.push(data);
    });
  }

  if (
    data.compare.length > 0 &&
    data.compare[0].analysisResult.plantStation.pumpStations.length > 0
  ) {
    data.compare[0].analysisResult.plantStation.pumpStations.forEach((item) => {
      const key = makeId(
        item.ptype,
        item.pname,
        item.otype,
        item.vprop,
        item.title,
      );
      const data: SimulationPlantOrPumpChangeItem = {
        ptitle: item.ptitle,
        title: item.title,
        newValue: item.newValue,
      };
      resultPumpChanges.forEach((map, i) => {
        const value = map.get(key);
        if (value) {
          data[`oldValue${i + 1}`] = value.oldValue;
          data[`diffValue${i + 1}`] = value.diffValue;
        }
      });
      pumpChanges.push(data);
    });
  }
  data.plantChanges = plantChanges;
  data.pumpChanges = pumpChanges;

  return data;
}

export function exportMultipleAnalysisReport(
  templateDocx: any,
  reportData: MultipleAnalysisReport,
  sheets: Array<ExcelSheetData>,
  observationsData: ComparisonResult,
): void {
  PizZipUtils.getBinaryContent(templateDocx, (error, content) => {
    if (error) {
      throw error;
    }
    const zip = new PizZip(content);
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      parser: expressionParser,
    });
    doc.render(reportData);
    const out = doc.getZip().generate({
      type: 'blob',
      mimeType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    saveAs(out, `多方案对比结果报告_${dayjs().format('YYYYMMDD_HHmmss')}.docx`);
    if (sheets?.length) {
      exportToExcelWithMultipleSheets(
        sheets,
        `附录_${dayjs().format('YYYYMMDD_HHmmss')}`,
        '-',
      );
    }
    if (observationsData) {
      exportComparisonResultToExcel(observationsData);
    }
  });
}

export function getCompareStep(
  originStep: number,
  compareStep: number,
): number {
  return Math.max(originStep, compareStep);
}

export const getAdvanceInitialValues = (
  advanceSetting: SolutionAnalysisAdvanceSetting | undefined,
): Record<AdvanceSettingType, number> => {
  const defaultValue = {} as Record<AdvanceSettingType, number>;
  return advanceSetting
    ? Object.entries(advanceSetting).reduce((prev, cure) => {
        const [key, value] = cure;
        return {
          ...prev,
          [key]: value.value,
        };
      }, defaultValue)
    : defaultValue;
};
