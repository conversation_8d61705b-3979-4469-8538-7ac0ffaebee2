/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { PropertyValue } from './device';

export const getRawWaterUnitFormat = (
  data: Map<string, PropertyValue>,
  db: Database,
): Map<string, PropertyValue> => {
  const newData = new Map<string, PropertyValue>();

  data.forEach((value, key) => {
    const { otype, vprop } = value;
    const newValue =
      db.getUnitFormat(otype, vprop)?.getValue(value?.value ?? '') ??
      value.value;

    if (newValue !== undefined) {
      newData.set(key, { ...value, value: newValue.toString() });
    } else {
      newData.set(key, value);
    }
  });

  return newData;
};
