/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import { splitId } from './object-item';

export const findSameUnitFormatByKey = (
  tagKey: string,
  sourceKey: string[],
  db: Database,
): boolean => {
  const [, , otype, , vprop] = splitId(tagKey);
  const tagUnit = db.getUnitFormat(otype, vprop);
  return !!sourceKey.find((key) => {
    const [, , otype, , vprop] = splitId(key);
    const itemUnit = db.getUnitFormat(otype, vprop);
    return (
      typeof itemUnit?.unitSymbol !== 'undefined' &&
      typeof tagUnit?.unitSymbol !== 'undefined' &&
      itemUnit?.unitSymbol === tagUnit?.unitSymbol
    );
  });
};
