/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SCADA_SUMMARY_OBJECT } from './const/system-const';
import Database from './database';
import { PumpInfo } from './device';
import { replaceIndicatorTitle } from './object-chart';
import { makeId, makeObjectId } from './object-item';
import { UnitFormat } from './unit-system';

type ScadaColumnAction = 'SUM';

export interface ScadaColumn {
  otype: string;
  actions?: ScadaColumnAction;
  title?: string;
  index?: number;
  dataIndex: string;
}

export interface ScadaIndicator extends ScadaColumn {
  oname: string;
  children?: Array<Omit<ScadaIndicator, 'dataIndex'>>;
}

export interface ScadaDevice {
  otype: string;
  oname: string;
  index?: number;
  title?: string;
  indicators: ScadaIndicator[];
  // 泵站相关配置 结构暂时不确定，后续可能会放到基本信息中   -- todo
  WATER_PUMP?: any[];
  chart?: {
    indicators: Array<{
      otype: string;
      index: number;
      defaultOName?: string;
    }>;
    pump: {
      show: boolean;
      title?: string;
    };
  };
}

export interface ScadaTableGroupConfig {
  groupId: string;
  groupName: string;
  devices: Array<ScadaDevice>;
  columns: Array<ScadaColumn>;
}

export type ScadaTableGroupsConfigs = Array<ScadaTableGroupConfig>;

export type ScadaDashboardConfig = {
  group: ScadaTableGroupsConfigs;
};

type GroupMap = Map<
  string,
  {
    groupId: string;
    groupName: string;
    devices: Map<string, ScadaDevice>;
    columns: Map<string, ScadaColumn>;
  }
>;

interface IndicatorValue {
  deviceOType: string;
  deviceOName: string;
  indicatorOType: string;
  indicatorONmae: string;
  title: string;
}

type IndicatorGroupMap = Map<
  string,
  {
    indicators: Map<string, IndicatorValue>;
  }
>;

type PumpGroupMap = Map<
  string,
  {
    pumps: Map<string, PumpInfo[]>;
  }
>;

type ParamsMapValue = {
  otype: string;
  oname: string;
  vprop: string;
  rmode?: string;
  fc_ext?: string;
};

export type TableDataValue = string | number | undefined;
export type TableDataValueInfo = ScadaIndicator & {
  deviceOType: string;
  deviceOName: string;
};
export type TableData = {
  [index: string]: TableDataValueInfo | TableDataValue;
};

export class ScadaTableData {
  private _groupMap: GroupMap;

  private _pumpGroupMap: PumpGroupMap;

  private _indicatorGroupMap: IndicatorGroupMap;

  private _paramsMap: Map<
    // string as makeId(otype, oname, vprop, rmode | forecast = undefined)
    string,
    {
      [index: string]: ParamsMapValue;
    }
  >;

  constructor() {
    this._groupMap = new Map();
    this._indicatorGroupMap = new Map();
    this._paramsMap = new Map();
    this._pumpGroupMap = new Map();
  }

  private initialParams() {
    this._indicatorGroupMap.forEach((indicatorGroup, groupId) => {
      const paramsValue: { [index: string]: ParamsMapValue } = {};
      indicatorGroup.indicators.forEach(
        ({ indicatorOType, indicatorONmae }) => {
          const id = makeId(indicatorOType, indicatorONmae, 'SDVAL');
          paramsValue[id] = {
            otype: indicatorOType,
            oname: indicatorONmae,
            vprop: 'SDVAL',
          };
        },
      );
      this._paramsMap.set(groupId, paramsValue);
    });
  }

  initialConfiguration(configs: ScadaTableGroupsConfigs, db: Database) {
    configs.forEach((item) => {
      const deviceMap: Map<string, ScadaDevice> = new Map();
      const columnMap: Map<string, ScadaColumn> = new Map();
      const indicatorsMap: Map<string, IndicatorValue> = new Map();
      const pumpsMap: Map<string, PumpInfo[]> = new Map();
      // trans list to map
      item.devices.forEach((device) => {
        const deviceId = makeObjectId(device.otype, device.oname);
        const deviceInfo = db.getDeviceById(deviceId);
        const pumps: PumpInfo[] = [];
        if (deviceInfo) {
          deviceMap.set(deviceInfo.id, {
            ...device,
            title: device.title ?? deviceInfo.title,
          });
        }
        device.indicators.forEach((indicator) => {
          const indicators: Array<
            Omit<ScadaIndicator, 'dataIndex'> & {
              dataIndex?: string;
            }
          > = [indicator];
          indicator.children?.forEach((children) => {
            indicators.push(children);
          });
          indicators.forEach((i) => {
            const { oname, title } = i;
            item.columns.forEach((item) => {
              const { otype } = item;
              const indicatorInfo = db.getIndicator(otype, oname);
              if (indicatorInfo) {
                const convertTitle =
                  title ??
                  (indicatorInfo.title
                    ? replaceIndicatorTitle(
                        indicatorInfo.title,
                        deviceInfo?.title ?? '',
                      )
                    : '');
                indicatorsMap.set(indicatorInfo.id, {
                  deviceOType: device.otype,
                  deviceOName: device.oname,
                  indicatorOType: otype,
                  indicatorONmae: oname,
                  title: convertTitle,
                });
              }
            });
          });
        });

        device.WATER_PUMP?.forEach((pumpItem) => {
          const item = {
            ...pumpItem,
            scada: [
              {
                otype: pumpItem.otype,
                oname: pumpItem.oname,
              },
              ...(pumpItem.children ?? []),
            ],
          };
          const pump = new PumpInfo(item);
          pumps.push(pump);
          if (deviceInfo && pump.onOffIndicator) {
            indicatorsMap.set(
              makeId(
                pump.onOffIndicator.otype,
                pump.onOffIndicator.oname,
                'SDVAL',
              ),
              {
                deviceOType: deviceInfo.otype,
                deviceOName: deviceInfo.oname,
                indicatorOType: pump.onOffIndicator.otype,
                indicatorONmae: pump.onOffIndicator.oname,
                title: `${pump.title ?? ''}`,
              },
            );
          }
        });

        pumpsMap.set(deviceId, pumps);
      });

      item.columns.forEach((column) => {
        const title =
          db.getPropertyInfo(column.otype)?.getPropertyTitle('SDVAL') ?? '';
        const unit = db.getUnitFormat(column.otype, 'SDVAL');
        columnMap.set(column.dataIndex, {
          ...column,
          title: `${column.title ?? title}(${unit?.unitSymbol ?? '-'})`,
        });
      });

      this._groupMap.set(item.groupId, {
        ...item,
        devices: deviceMap,
        columns: columnMap,
      });
      this._indicatorGroupMap.set(item.groupId, {
        indicators: indicatorsMap,
      });

      this._pumpGroupMap.set(item.groupId, {
        pumps: pumpsMap,
      });
    });

    this.initialParams();
  }

  getParamsValue(
    groupId: string,
  ): { [index: string]: ParamsMapValue } | undefined {
    return this._paramsMap.get(groupId);
  }

  get groupMap(): GroupMap {
    return this._groupMap;
  }

  get pumpGroupMap(): PumpGroupMap {
    return this._pumpGroupMap;
  }

  get indicatorGroupMap(): IndicatorGroupMap {
    return this._indicatorGroupMap;
  }
}

export const TM_FLOW = 'TM_FLOW';
export const CUMULATIVE_TM_FLOW = 'CUMULATIVE_TM_FLOW';
export const CUMULATIVE_FLOW = 'CUMULATIVE_FLOW';
export const tmFlowId = makeId(
  SCADA_SUMMARY_OBJECT.otype,
  SCADA_SUMMARY_OBJECT.oname,
  TM_FLOW,
);
export const tmFlowForecastId = makeId(tmFlowId, 'forecast');
export const cumulativeTmId = makeId(
  SCADA_SUMMARY_OBJECT.otype,
  SCADA_SUMMARY_OBJECT.oname,
  CUMULATIVE_TM_FLOW,
);
export const cumulativeForecastTmId = makeId(cumulativeTmId, 'forecast');
export const yesterdayCumulativeTmId = makeId(
  SCADA_SUMMARY_OBJECT.otype,
  SCADA_SUMMARY_OBJECT.oname,
  CUMULATIVE_FLOW,
);

export const getPanelParams = (): {
  [index: string]: ParamsMapValue;
} => {
  const params: { [index: string]: ParamsMapValue } = {
    // 瞬时流量 和 预测瞬时流量
    [tmFlowId]: {
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: TM_FLOW,
    },
    [tmFlowForecastId]: {
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: TM_FLOW,
      fc_ext: 'true',
    },
    // 当前累计供水量 和 今日预测供水量
    [cumulativeTmId]: {
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: CUMULATIVE_TM_FLOW,
    },
    [cumulativeForecastTmId]: {
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: CUMULATIVE_TM_FLOW,
      fc_ext: 'true',
    },
    [yesterdayCumulativeTmId]: {
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: CUMULATIVE_FLOW,
    },
  };

  return params;
};

export const getUnit = (db: Database, vprop: string): UnitFormat | undefined =>
  db.getUnitFormat(SCADA_SUMMARY_OBJECT.otype, vprop);

export const getUnitTitle = (db: Database, vprop: string): string => {
  const unit = getUnit(db, vprop);
  return unit?.unitSymbol ? `(${unit?.unitSymbol})` : '';
};
