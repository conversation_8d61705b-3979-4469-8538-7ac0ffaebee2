/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';
import Device, { getChartPropertiesByConfig } from './device';
import { makeObjectId } from './object-item';
import { ChartProperties } from './property/property-info';
import { getChildrenFromTreeData, transListToTree } from './tree';
import { Key } from './types';

export interface ScadaDeviceItem {
  oname: string;
  otype: string;
  group: {
    /** {groupId: [parentIds]} */
    [index: string]: string[];
  };
}

export type ScadaTreeNodeType = 'device' | 'indicator' | 'other';

interface TreeNodeProps {
  className?: string;
  checkable?: boolean;
  disabled?: boolean;
  disableCheckbox?: boolean;
  key?: Key;
  eventKey?: string;
  isLeaf?: boolean;
  checked?: boolean;
  expanded?: boolean;
  loading?: boolean;
  selected?: boolean;
  selectable?: boolean;
}

export interface ScadaTreeListItem extends TreeNodeProps {
  id: string;
  parentId: string;
  parentIds?: string[];
  title: string;
  order: number;
  filteredCount?: number;
  deviceCount?: number;
  deviceInfo?: Device;
  indicators?: ChartProperties[];
  children?: ScadaTreeListItem[];
  editing?: boolean;
  isEdit?: boolean;
  type: ScadaTreeNodeType;
}

export interface ScadaGroupListItem {
  id: string;
  title: string;
  order: number;
  isEdit?: boolean;
  children: ScadaTreeListItem[];
}

export type ExpandedGroupKeys = {
  /** groupId: expandedKeys */
  [index: string]: Key[] | undefined;
};

export class ScadaDeviceCollection {
  /**  Map<groupId, Map<groupChildrenId, string[]>> */
  private _groupMap: Map<string, Map<string, string[]>>;

  constructor(scadaDeviceList: ScadaDeviceItem[]) {
    this._groupMap = this.buildScadaDeviceItemIndex(scadaDeviceList);
  }

  // eslint-disable-next-line class-methods-use-this
  private buildScadaDeviceItemIndex(
    scadaDeviceList: ScadaDeviceItem[],
  ): Map<string, Map<string, string[]>> {
    const index = new Map<string, Map<string, string[]>>();

    scadaDeviceList.forEach((item) => {
      const groupKeys = Object.keys(item.group);

      groupKeys.forEach((groupKey) => {
        const deviceId = makeObjectId(item.otype, item.oname);
        const groupIndexMap: Map<string, string[]> =
          index.get(groupKey) ?? new Map();

        const groupChildrenIds = item.group[groupKey];
        groupChildrenIds.forEach((groupChildrenId) => {
          const groupChildrenDevices = groupIndexMap.get(groupChildrenId) ?? [];
          if (!groupChildrenDevices.includes(deviceId)) {
            groupChildrenDevices.push(deviceId);
          }
          groupIndexMap.set(groupChildrenId, groupChildrenDevices);
        });
        index.set(groupKey, groupIndexMap);
      });
    });
    return index;
  }

  getDeviceIdsById(groupId: string, parentId: string): string[] {
    return this._groupMap.get(groupId)?.get(parentId) ?? [];
  }
}

export function makeTreeDeviceId(
  groupId: string,
  parentId: string,
  deviceId: string,
): string {
  return `${groupId}&${parentId}&${deviceId}`;
}

export function makeTreeIndicatorId(
  treeDeviceId: string,
  indicatorId: string,
): string {
  return `${treeDeviceId}&${indicatorId}`;
}

export function splitTreeDeviceId(
  treeDeviceId: string,
): [string, string, string] | undefined {
  if (typeof treeDeviceId !== 'string') return undefined;
  const splitRes = treeDeviceId.split('&');
  const [groupId, parentId, deviceId] = splitRes;
  if (splitRes.length === 3) return [groupId, parentId, deviceId];
  return undefined;
}

export function splitTreeIndicatorId(
  treeIndicatorId: string,
): [string, string, string, string, string] | undefined {
  if (typeof treeIndicatorId !== 'string') return undefined;
  const splitRes = treeIndicatorId.split('&');
  const [groupId, parentId, deviceId, indicatorId, vprop] = splitRes;
  if (splitRes.length === 5)
    return [groupId, parentId, deviceId, indicatorId, vprop];
  return undefined;
}

function generateScadaTreeDevice(
  db: Database,
  deviceId: string,
  groupId: string,
  parentId: string,
  showIndicator: boolean,
  indicatorFilter?: string[],
): ScadaTreeListItem[] {
  const scadaTreeListItems: ScadaTreeListItem[] = [];
  const deviceInfo = db.getDeviceById(deviceId);
  const treeDeviceId = makeTreeDeviceId(groupId, parentId, deviceId);
  const isLeaf = !showIndicator;
  if (showIndicator && deviceInfo) {
    const chartIndicators = getChartPropertiesByConfig(db, deviceInfo) ?? [];
    const indicators =
      indicatorFilter && indicatorFilter.length > 0
        ? chartIndicators.filter((item) =>
            indicatorFilter?.includes(item.otype),
          )
        : chartIndicators;

    if (indicators.length > 0) {
      scadaTreeListItems.push({
        id: treeDeviceId,
        title: deviceInfo?.title ?? deviceId,
        parentId,
        order: 0,
        deviceInfo,
        isLeaf,
        disableCheckbox: !isLeaf,
        type: 'device',
      });
    }
    indicators.forEach((indicator) => {
      const id = makeObjectId(indicator.otype, indicator.oname);
      const indicatorId = makeTreeIndicatorId(treeDeviceId, id);
      const chartId = makeTreeIndicatorId(indicatorId, indicator.vprop);
      scadaTreeListItems.push({
        id: chartId,
        title: indicator.title ?? chartId,
        parentId: treeDeviceId,
        order: 0,
        indicators: chartIndicators,
        isLeaf: true,
        type: 'indicator',
      });
    });
  } else {
    scadaTreeListItems.push({
      id: treeDeviceId,
      title: deviceInfo?.title ?? `${deviceId}(已删除)`,
      parentId,
      order: 0,
      deviceInfo,
      isLeaf,
      disableCheckbox: !isLeaf,
      type: 'device',
    });
  }
  return scadaTreeListItems;
}

function getDeviceTreeByProperty(
  db: Database,
  propertyName: keyof Device,
  parentId: string,
  showIndicator: boolean,
  useDataAccess: boolean,
): ScadaTreeListItem[] {
  const devices = db.getAllDevicesAndStations(useDataAccess);

  const groupMap: Map<string, ScadaTreeListItem> = new Map();
  const scadaTreeListItems: ScadaTreeListItem[] = [];
  devices.forEach((device) => {
    const propertyValue = device[propertyName] || '其它';
    const group = groupMap.get(propertyValue);
    if (group) {
      (group.deviceCount as number) += 1;
    } else {
      groupMap.set(propertyValue, {
        id: propertyValue,
        title: db.getPropertyInfo(propertyValue)?.title ?? propertyValue,
        parentId,
        disableCheckbox: true,
        order: groupMap.size,
        deviceCount: 1,
        type: 'other',
      });
    }

    const otherGroup = groupMap.get('其它');
    if (otherGroup) otherGroup.order = groupMap.size + 1;

    scadaTreeListItems.push(
      ...generateScadaTreeDevice(
        db,
        device.id,
        parentId,
        propertyValue,
        showIndicator,
      ),
    );
  });

  return [
    ...groupMap.values(),
    ...scadaTreeListItems.sort((a, b) => a.title.localeCompare(b.title)),
  ];
}

function getPlantAndPumpStationTreeDataByProperty(
  db: Database,
  propertyName: keyof Device,
  parentId: string,
  showIndicator: boolean,
  indicatorsFilter?: string[],
): ScadaTreeListItem[] {
  const devices = db.getAllDevicesAndStations();

  const groupMap: Map<string, ScadaTreeListItem> = new Map();
  const scadaTreeListItems: ScadaTreeListItem[] = [];
  devices.forEach((device) => {
    const propertyValue = device[propertyName] || '其它';
    const group = groupMap.get(propertyValue);
    if (group) {
      (group.deviceCount as number) += 1;
    } else {
      groupMap.set(propertyValue, {
        id: propertyValue,
        title: db.getPropertyInfo(propertyValue)?.title ?? propertyValue,
        parentId,
        disableCheckbox: true,
        order: groupMap.size,
        deviceCount: 1,
        type: 'other',
      });
    }

    const otherGroup = groupMap.get('其它');
    if (otherGroup) otherGroup.order = groupMap.size + 1;

    scadaTreeListItems.push(
      ...generateScadaTreeDevice(
        db,
        device.id,
        parentId,
        propertyValue,
        showIndicator,
        indicatorsFilter,
      ),
    );
  });

  return [...scadaTreeListItems.sort((a, b) => a.title.localeCompare(b.title))];
}

export const getDeviceTreeDataByDeviceType = (
  db: Database,
  order?: number,
  showIndicator = true,
  useDataAccess = false,
) => {
  // 设备类型分组
  const devicesGroupByOtype = getDeviceTreeByProperty(
    db,
    'otype',
    'otype',
    showIndicator,
    useDataAccess,
  );
  return [
    {
      id: 'otype',
      title: '设备类型',
      order: order ?? 1,
      children: devicesGroupByOtype,
    },
  ];
};

export const getDeviceTreeDataByDeviceDataSource = (
  db: Database,
  order?: number,
  showIndicator = true,
  useDataAccess = false,
) => {
  // 设备类型分组
  const devicesGroupByDataSource = getDeviceTreeByProperty(
    db,
    'dataSource',
    'dataSource',
    showIndicator,
    useDataAccess,
  );
  return [
    {
      id: 'dataSource',
      title: '数据来源',
      order: order ?? 1,
      children: devicesGroupByDataSource,
    },
  ];
};

export function injectDeviceInGroupChildren(
  groupList: ScadaGroupListItem[],
  scadaDeviceList: ScadaDeviceItem[],
  db: Database,
  showIndicator = true,
  isEdit = false,
  useDataAccess = false,
): ScadaGroupListItem[] {
  const filterDeviceList = useDataAccess
    ? scadaDeviceList.filter((f) => {
        const { otype, oname } = f;
        const device = db.getDevice(otype, oname);
        return device ? device.dataAccess : true;
      })
    : scadaDeviceList;
  const scadaDeviceMap = new ScadaDeviceCollection(filterDeviceList);
  const newGroup = groupList.map((groupItem): ScadaGroupListItem => {
    const children: ScadaTreeListItem[] = [];
    const formatChildrenToTree = transListToTree(
      groupItem.children,
      'id',
      'parentId',
    );
    const groupId = groupItem.id;
    groupItem.children.forEach((item) => {
      const parentId = item.id;
      const childrenIds = getChildrenFromTreeData(
        formatChildrenToTree,
        parentId,
      ).map((item) => item.id);
      const deviceIds = scadaDeviceMap.getDeviceIdsById(groupId, parentId);
      const deviceCount = [...childrenIds, parentId].reduce(
        (prev, curr) =>
          prev + scadaDeviceMap.getDeviceIdsById(groupId, curr).length,
        0,
      );
      children.push({
        ...item,
        disableCheckbox: true,
        isLeaf: false,
        isEdit: true,
        deviceCount,
      });
      deviceIds.forEach((deviceId) => {
        children.push(
          ...generateScadaTreeDevice(
            db,
            deviceId,
            groupId,
            parentId,
            showIndicator,
          ),
        );
      });
    });
    return {
      ...groupItem,
      isEdit,
      children,
    };
  });

  if (!isEdit) {
    const groupMaxOrder = Math.max(
      ...newGroup.map((item): number => item.order),
    );
    newGroup.push(
      ...[
        ...getDeviceTreeDataByDeviceType(
          db,
          groupMaxOrder + 1,
          showIndicator,
          useDataAccess,
        ),
        ...getDeviceTreeDataByDeviceDataSource(
          db,
          groupMaxOrder + 2,
          showIndicator,
          useDataAccess,
        ),
      ],
    );
  }
  return newGroup;
}

function getParentIds(
  id: string,
  mapList: Map<string, ScadaTreeListItem>,
): string[] {
  const parentIds: string[] = [];

  let currentData = mapList.get(id);
  let currentParentId = currentData?.parentId;

  while (currentParentId) {
    parentIds.push(currentParentId);
    currentData = mapList.get(currentParentId);
    currentParentId = currentData?.parentId;
  }

  return parentIds;
}

export function filterTreeData(
  list: ScadaTreeListItem[],
  searchValue: string,
): ScadaTreeListItem[] {
  if (searchValue === '') return list;
  const mapList: Map<string, ScadaTreeListItem & { calculated?: boolean }> =
    new Map();
  let allSelectedIds: string[] = [];

  // 第一步：构建映射
  list.forEach((item) => {
    mapList.set(item.id, { ...item, filteredCount: 0 });
  });

  // 第二步：搜索匹配的节点
  list.forEach((item) => {
    const searchLower = searchValue.toLowerCase();
    const titleLower = item.title.toLowerCase();
    let isMatch = false;

    // 情况1：直接匹配节点标题
    if (titleLower.indexOf(searchLower) > -1) {
      isMatch = true;
    }

    // 情况2：如果是指标节点，检查其父设备是否匹配
    if (item.type === 'indicator' && !isMatch) {
      const parentDevice = mapList.get(item.parentId);
      if (
        parentDevice &&
        parentDevice.title.toLowerCase().indexOf(searchLower) > -1
      ) {
        isMatch = true;
      }
    }

    // 情况3：如果是设备节点，检查其指标是否匹配
    if (item.type === 'device' && !isMatch) {
      const hasMatchingIndicator = list.some(
        (indicator) =>
          indicator.parentId === item.id &&
          indicator.type === 'indicator' &&
          indicator.title.toLowerCase().indexOf(searchLower) > -1,
      );
      if (hasMatchingIndicator) {
        isMatch = true;
      }
    }

    if (isMatch) {
      const parentIds = getParentIds(item.id, mapList);
      allSelectedIds = [...allSelectedIds, ...parentIds, item.id];

      // 如果是设备节点或其指标匹配，更新父节点的计数
      if (item.type === 'device' || item.deviceInfo) {
        parentIds.forEach((parentId) => {
          const parentNode = mapList.get(parentId);
          if (parentNode) {
            parentNode.filteredCount = (parentNode.filteredCount || 0) + 1;
          }
        });
      }
    }
  });

  // 第三步：确保匹配设备的所有指标都显示
  list.forEach((item) => {
    if (item.type === 'indicator') {
      const parentDevice = mapList.get(item.parentId);
      if (parentDevice && allSelectedIds.includes(parentDevice.id)) {
        allSelectedIds.push(item.id);
      }
    }
  });

  // 去重
  allSelectedIds = [...new Set(allSelectedIds)];

  // 构建结果列表，保持原有顺序
  return list
    .filter((item) => allSelectedIds.includes(item.id))
    .map((item) => {
      const info = mapList.get(item.id);
      return {
        ...item,
        filteredCount: info?.filteredCount || undefined,
      };
    });
}

export function getPlantsAndPumpStationsTreeData(
  db: Database,
  indicatorFilter?: string[],
  showIndicator = true,
): ScadaTreeListItem[] {
  return getPlantAndPumpStationTreeDataByProperty(
    db,
    'otype',
    'otype',
    showIndicator,
    indicatorFilter,
  );
}

export function getAllDeviceTreeData(
  db: Database,
  showIndicator = true,
  useDataAccess = false,
): ScadaTreeListItem[] {
  return getDeviceTreeByProperty(
    db,
    'otype',
    'otype',
    showIndicator,
    useDataAccess,
  );
}
