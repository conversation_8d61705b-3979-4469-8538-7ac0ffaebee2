/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from './database';

/** 编辑表单所需的数据格式 */
export interface EditScadaWarnConfigValue {
  [key: string]: number | null;
}

/** 对完整信息去重后的自定义警告名称 */
export interface CustomScadaWarnName {
  title: string;
  type: string;
  key: string;
}

/** 定义警告配置 */
export interface WarnConfigValue {
  title: string;
  value: number | null;
  vprop?: string;
}

/** SCADA 警告配置的自定义属性集合 */
export type WarnConfigValueType = {
  state: number;
  [additionalKey: string]: WarnConfigValue | number | string | undefined;
};

/** 后端所需的更新警告配置 */
export interface BackendWarnConfigValue {
  [key: string]: WarnConfigValueType;
}

/** 基础配置基础上扩展的自定义属性 */

export interface GenericScadaWarnKeyInfo {
  title: string;
  type: string;
  value?: WarnConfigValueType;
}
export interface GenericScadaWarnConfig {
  [key: string]: GenericScadaWarnKeyInfo;
}

/** SCADA 警告配置的基本属性 */
export interface BaseScadaWarnConfig {
  ONAME: string;
  OTYPE: string;
  PNAME: string;
  PTYPE: string;
  TITLE: string;
  KET_WARN: boolean;
}

/** SCADA 警告配置的完整信息 */
export type CompleteScadaWarnConfig = BaseScadaWarnConfig &
  GenericScadaWarnConfig;

/** 从 SCADA 完整警告配置中提取自定义警告名称 */
export const convertToCustomScadaWarnName = (
  data: CompleteScadaWarnConfig[],
): CustomScadaWarnName[] => {
  if (!data || !Array.isArray(data)) {
    return [];
  }

  const resultMap = new Map<string, { title: string; key: string }>();

  data.forEach((item) => {
    Object.keys(item).forEach((key) => {
      const value = item[key];
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        if ('title' in value && 'type' in value) {
          resultMap.set(value.type, { title: value.title, key });
        }
      }
    });
  });

  return Array.from(resultMap, ([type, item]) => ({
    type,
    title: item.title,
    key: item.key,
  }));
};

/** 将 SCADA 完整警告配置中过滤非对象属性 */
export const filterScadaWarnConfig = (
  data?: CompleteScadaWarnConfig,
): GenericScadaWarnConfig => {
  if (!data) {
    return {};
  }

  const result: GenericScadaWarnConfig = {};

  Object.keys(data).forEach((key) => {
    const value = data[key];
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = value;
    }
  });

  return result;
};

/** 将编辑 SCADA 的表单数据转成接口所需的数据格式 */
export const convertFormToUpdateScadaWarnConfig = (
  originalData: GenericScadaWarnConfig,
  editedFormData: EditScadaWarnConfigValue,
  modifiedKey: string,
  curDb: Database,
  otype: string,
): BackendWarnConfigValue => {
  const updatedData: BackendWarnConfigValue = {};

  if (!originalData || !editedFormData || !modifiedKey) {
    return updatedData;
  }

  if (originalData[modifiedKey]?.value) {
    const updatedValue = { ...originalData[modifiedKey].value };

    Object.entries(editedFormData).forEach(([field, data]) => {
      if (field in updatedValue) {
        const value = updatedValue[field];
        if (typeof value === 'object' && 'value' in value) {
          const unitFormat = curDb.getUnitFormat(otype, value.vprop ?? '');
          if (data === null) {
            value.value = null;
          } else {
            const originValue = unitFormat?.getOriginalValue(data) as number;
            value.value = originValue ?? data ?? null;
          }
        } else {
          updatedValue[field] = Number(data);
        }
      }
    });

    updatedData[modifiedKey] = updatedValue as WarnConfigValueType;
  }

  return updatedData;
};
