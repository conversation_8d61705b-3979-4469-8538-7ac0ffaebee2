/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DisplayType, LayerDataCollection } from './layer-data';
import { SidebarMenuType } from './sidebar-menu-data';

export type SceneIdType = 'ONLINE' | 'SOLUTION' | 'MINIMAP';
export const SCENE_ID_TYPE_ONLINE: SceneIdType = 'ONLINE';
export const SCENE_ID_TYPE_SOLUTION: SceneIdType = 'SOLUTION';
export const SCENE_ID_TYPE_MINIMAP: SceneIdType = 'MINIMAP';

export type SceneType =
  | 'RUN'
  | 'SCADA'
  | 'INDICATOR'
  | 'MODEL'
  | 'SCHEME'
  | 'SCHEDULE'
  | 'WARNING'
  | 'MODEL_SERVICE'
  | 'ENERGY_CONSUMPTION'
  | 'RAW_WATER'
  | 'SCHEDULE_SOLUTION'
  | 'CONTINGENCY_SCHEDULE'
  | 'RECLAIMED_WATER';
/** 场景:运行状态 */
export const SCENE_TYPE_RUN: SceneType = SidebarMenuType.SCENE_RUN;
/** 场景:分质水看板 */
export const SCENE_TYPE_RECLAIMED_WATER: SceneType =
  SidebarMenuType.SCENE_RECLAIMED_WATER;
/** 场景:设备评估 */
export const SCENE_TYPE_SCADA: SceneType = SidebarMenuType.SCENE_SCADA;
/** 场景:指标评估 */
export const SCENE_TYPE_INDICATOR: SceneType = SidebarMenuType.SCENE_INDICATOR;
/** 场景:模拟评估 */
export const SCENE_TYPE_MODEL: SceneType = SidebarMenuType.SCENE_MODEL;
/** 场景:方案概况 */
export const SCENE_TYPE_SCHEME: SceneType = SidebarMenuType.SCENE_SCHEME;
/** 场景:调度 */
export const SCENE_TYPE_SCHEDULE: SceneType = SidebarMenuType.SCHEDULE;
/** 工具:优化调度 */
export const { SCHEDULE_SOLUTION } = SidebarMenuType;
/** 场景：警告 */
export const SCENE_TYPE_WARNING: SceneType = 'WARNING';
/** 场景:模型服务 */
export const SCENE_MODEL_SERVICE: SceneType = 'MODEL_SERVICE';
/** 场景:原水监测 */
export const SCENE_RAW_WATER: SceneType = 'RAW_WATER';
/** 场景:能耗分析 */
export const SCENE_ENERGY_CONSUMPTION: SceneType = 'ENERGY_CONSUMPTION';
/** 场景:调度方案 */
export const SCENE_SCHEDULE_SOLUTION: SceneType = 'SCHEDULE_SOLUTION';
/** 场景:应急调度 */
export const SCENE_CONTINGENCY_SCHEDULE: SceneType = 'CONTINGENCY_SCHEDULE';

type SceneDashboardSize = 'small' | 'large';
const SMALL_WIDTH = 349;
const LARGE_WIDTH = 400;

export interface LayerState {
  name: string;
  title: string;
  visible: boolean;
  icon?: string;
  type?: DisplayType;
}

export interface ThemeItem {
  name: string;
  title: string;
}
export interface ThemeSection {
  type: string;
  title: string;
  layerStates: LayerState[];
  themeItems: ThemeItem[];
  currentThemeItem: ThemeItem | undefined;
}

export interface Scene {
  id: SceneType;
  hiddenDashboard?: boolean;
  /* 是否隐藏，默认 false */
  hidden?: boolean;
  /* 是否为默认场景 */
  default: boolean;
  title: string;
  dashboard: string;
  themeSections: ThemeSection[];
  simpleThemeSections: ThemeSection[];
}

export function getSceneSize(sceneId: SceneType): SceneDashboardSize {
  switch (sceneId) {
    case SCENE_TYPE_RUN:
    case SCENE_TYPE_SCHEDULE:
    case SCENE_TYPE_WARNING:
    case SCENE_RAW_WATER:
    case SCENE_SCHEDULE_SOLUTION:
    case SCENE_TYPE_RECLAIMED_WATER:
      return 'large';
    default:
      return 'small';
  }
}

export function getSceneWidth(sceneId: SceneType): number {
  switch (getSceneSize(sceneId)) {
    case 'large':
      return LARGE_WIDTH;
    default:
      return SMALL_WIDTH;
  }
}

export function getDefaultSceneId(scenes: Scene[]): SceneType | undefined {
  return scenes.find((f) => f.default)?.id ?? scenes[0]?.id;
}

export function getScene(
  scenes: Scene[],
  sceneId: string | undefined,
): Scene | undefined {
  return scenes.find((scene) => scene.id === sceneId);
}

export function getThemeSectionTypes(scene: Scene | undefined): string[] {
  if (typeof scene === 'undefined') return [];
  const { themeSections } = scene;
  const types: Array<string> = [];
  themeSections.forEach(({ type }) => {
    if (type) types.push(type);
  });
  return types;
}

export function generateThemeSection(
  data: any,
  themeInfo: Map<string, string>,
): ThemeSection | undefined {
  const { type, title, layerList, themeList } = data;
  if (
    typeof type === 'string' &&
    typeof title === 'string' &&
    Array.isArray(layerList) &&
    Array.isArray(themeList)
  ) {
    const layerStates: Array<LayerState> = [];
    layerList.forEach((layer: any) => {
      const { id } = layer;
      const { checked } = layer;
      layerStates.push({ name: id, title: id, visible: checked });
    });

    const themeItems: Array<ThemeItem> = [];
    themeList.forEach((name: string) => {
      const themeTitle = themeInfo.get(name);
      themeItems.push({ name, title: themeTitle || name });
    });
    return {
      type,
      title,
      layerStates,
      themeItems,
      currentThemeItem: themeItems.length > 0 ? themeItems[0] : undefined,
    };
  }

  return undefined;
}

export function getInvisibleLayers(layerStates: LayerState[]): string[] {
  return layerStates.filter((item) => !item.visible).map((item) => item.name);
}

export function getInvisibleLayerNames(scene: Scene | undefined): string[] {
  if (typeof scene === 'undefined') return [];
  const { themeSections } = scene;
  let names: Array<string> = [];
  themeSections.forEach(({ layerStates }) => {
    const layerNames: Array<string> = getInvisibleLayers(layerStates);
    names = names.concat(layerNames);
  });
  return names;
}

export function getAllLayerNames(scene: Scene | undefined): string[] {
  if (typeof scene === 'undefined') return [];
  const { themeSections } = scene;
  let names: Array<string> = [];
  themeSections.forEach(({ layerStates }) => {
    const layerNames: Array<string> = layerStates.map((item) => item.name);
    names = names.concat(layerNames);
  });
  return names;
}

export function getThemeNames(scene: Scene | undefined): string[] {
  if (typeof scene === 'undefined') return [];
  const { themeSections } = scene;
  const names: Array<string> = [];
  themeSections.forEach(({ currentThemeItem }) => {
    if (currentThemeItem) names.push(currentThemeItem.name);
  });
  return names;
}

export function refreshLayerData(
  scenes: Scene[],
  layerCollection: LayerDataCollection,
): Scene[] {
  return scenes.map((scene: Scene) => ({
    ...scene,
    themeSections: scene.themeSections.map((themeSection: ThemeSection) => ({
      ...themeSection,
      layerStates: themeSection.layerStates.map((layerState: LayerState) => {
        const layerData = layerCollection.getLayer(layerState.name);
        if (typeof layerData === 'undefined') return layerState;
        return {
          ...layerState,
          type: layerData.type,
          title: layerData.title,
          icon: layerData.icon,
        };
      }),
    })),
  }));
}

export function getLayerIsAllVisible(
  layerStates: LayerState[],
): boolean | null {
  const selectionsCount = layerStates.reduce(
    (prevCount, layerState) => (layerState.visible ? prevCount + 1 : prevCount),
    0,
  );
  if (selectionsCount > 0 && selectionsCount < layerStates.length) return null;
  return selectionsCount > 0;
}

export function getLayerNames(layerStates: LayerState[]): string[] {
  return layerStates.map((layerState) => layerState.name);
}

export function getSimpleThemeAndLayerList(
  simpleThemeSections: ThemeSection[],
  themeSections: ThemeSection[],
): ThemeSection[] {
  const settingLists: ThemeSection[] = [];
  simpleThemeSections.forEach((themeSection) => {
    const currentScene = themeSections.find(
      (item) => item.type === themeSection.type,
    );
    if (currentScene) {
      const layerStates: LayerState[] = [];
      themeSection.layerStates.forEach((item) => {
        const layer = currentScene.layerStates.find(
          (currentLayer) => currentLayer.name === item.name,
        );
        if (layer) {
          layerStates.push(layer);
        }
      });
      settingLists.push({
        ...themeSection,
        layerStates,
        currentThemeItem: currentScene.currentThemeItem,
      });
    }
  });
  return settingLists;
}
