/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/** 默认未处理警告显示时长 */
export const DEFAULT_UNPROCESSED_WARN_TIME = 48;
/** 默认已处理警告显示时长 */
export const DEFAULT_PROCESSED_WARN_TIME = 48;
/** 默认阀门显示时长 */
export const DEFAULT_VALVE_TIME = 48;
/** 默认关阀事件显示时长 */
export const DEFAULT_VALVE_GROUP_TIME = 48;

interface Items {
  /** 数据卡片唯一ID（禁止修改） */
  key?: string;
  /** 数据卡片标题 */
  title?: string;
  /** 控制是否隐藏 */
  hidden?: boolean;
}

/** 警告tab的具体配置 */
export interface WarnTabConfig {
  /** 控制未处理警告显示时长。默认: 48小时 */
  unprocessedWarnTime?: number;
  /** 控制已处理警告显示时长。默认: 48小时 */
  processedWarnTime?: number;
  /** 控制警告显示的等级。默认：全部等级 */
  warnLevel?: number[];
  /** 控制特级警告显示红色加粗效果 */
  showSpecialWarn?: boolean;
}

/** 阀门tab的具体配置 */
export interface ValveTabConfig {
  /** 控制阀门显示时长。默认: 48小时 */
  valveTime?: number;
  /** 控制关阀事件显示时长。默认：48小时 */
  valveGroupTime?: number;
}

/** 事件看板配置 */
export interface DispatchSceneConfig {
  /** 控制消息中心tab的显示隐藏; key禁止修改 */
  noticeItems?: Items[];
  /** 控制消息中心中警告tab的具体配置 */
  warnTabConfig?: WarnTabConfig;
  /** 控制消息中心中阀门tab的具体配置 */
  valveTabConfig?: ValveTabConfig;
  /** 控制消息中心中全部问号的具体内容 */
  helpConfig?: { [key: string]: string };
}
