/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface ReservoirMonitorPointListConfig {
  otype: string;
  oname: string;
  title: string;
  info: {
    otype: string;
    oname: string;
  };
}

export interface RawWaterMonitoringSceneConfig {
  /** 控制消息中心中全部问号的具体内容 */
  helpConfig?: { [key: string]: string };
  /** 配置水位监测点 otype oname */
  waterLevelMonitorPointConfig?: { otype: string; oname: string };
  /** 配置多水库的监测点 otype oname */
  reservoirMonitorPointListConfig?: ReservoirMonitorPointListConfig[];
}
