/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { WarnConfirmStatus } from '../warn';

/** 默认模型计算时间允许的延时时间 */
export const DEFAULT_LAST_SIMULATION_DELAY_MINUTES = 60;
/** 默认未处理警告显示时长 */
export const DEFAULT_UNPROCESSED_WARN_TIME = 48;
/** 默认已处理警告显示时长 */
export const DEFAULT_PROCESSED_WARN_TIME = 48;
/** 默认阀门显示时长 */
export const DEFAULT_VALVE_TIME = 48;

/** 数据卡片和消息中心的具体配置 */
interface Items {
  /** 数据卡片唯一ID（禁止修改） */
  key?: string;
  /** 数据卡片标题（禁止修改） */
  title?: string;
  /** 控制是否隐藏 */
  hidden?: boolean;
}

/** 警告tab的具体配置 */
export interface WarnTabConfig {
  /** 控制未处理警告显示时长。默认: 48小时 */
  unprocessedWarnTime?: number;
  /** 控制已处理警告显示时长。默认: 48小时 */
  processedWarnTime?: number;
  /** 控制警告显示的等级。默认：全部等级 */
  warnLevel?: number[];
  /** 控制特级警告显示红色加粗效果 */
  showSpecialWarn?: boolean;
  /** 警告信息过滤规则 */
  filterRules?: {
    /** 警告类型过滤，指定要显示的警告类型列表 */
    warnType?: string[];
    /** 最大显示数量限制 */
    showMaxCount?: number;
    /** 警告状态过滤，指定要显示的警告状态列表 */
    warnStatus?: WarnConfirmStatus[];
    /** 查询时间范围（小时），从当前时间往前推多少小时去查询警告 */
    timeRange?: number;
  };
}

/** 阀门tab的具体配置 */
export interface ValveTabConfig {
  /** 控制阀门显示时长。默认: 48小时 */
  valveTime?: number;
}

/** 运行看板配置 */
export interface RunningStateConfig {
  /** 控制最新模型计算时间允许的延时时间; 超出这个时间显示红色。默认: 60分钟  */
  lastSimulationDelayMinutes?: number;
  /** 控制是否显示模型计算时间 */
  showModelCalculationTime?: boolean;
  /** 控制数据卡片的显示隐藏; key和title只是标识，禁止修改 */
  cardItems?: Items[];
  /** 控制消息中心tab的显示隐藏; key和title只是标识，禁止修改 */
  noticeItems?: Items[];
  /** 控制消息中心中警告tab的具体配置 */
  warnTabConfig?: WarnTabConfig;
  /** 控制消息中心中阀门tab的具体配置 */
  valveTabConfig?: ValveTabConfig;
  /** 控制消息中心中全部问号的具体内容 */
  helpConfig?: { [key: string]: string };
}
