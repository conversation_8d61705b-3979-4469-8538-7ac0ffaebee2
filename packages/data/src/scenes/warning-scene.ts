/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/** 默认未处理警告显示时长 */
export const DEFAULT_UNPROCESSED_WARN_TIME = 3;
/** 默认已处理警告显示时长 */
export const DEFAULT_PROCESSED_WARN_TIME = 48;
/** 默认未处理警告显示时长单位 */
export const DEFAULT_UNPROCESSED_WARN_TIME_UNIT = 'months';
/** 默认已处理警告显示时长单位 */
export const DEFAULT_PROCESSED_WARN_TIME_UNIT = 'hours';

/** 二级筛选项的具体配置 */
export interface SecondFilterItems {
  // 查询分类: 按照警告的属性字段分类 | 按照警告的主要设备的属性字段分类
  filterBy?: 'default' | 'device';
  // 展示在面板上的字段名，如：'按分类' | '按来源' | '按位置'
  label?: string;
  // 查询字段名，如：'secondTypeName' | 'source' | 'location'
  value?: string;
  /** 控制是否隐藏 */
  hidden?: boolean;
}

/** 警告tab的具体配置 */
export interface WarnConfig {
  /** 控制未处理警告显示时长。默认: 3个月 */
  unprocessedWarnTime?: number;
  /** 控制未处理警告显示时长单位。默认: months */
  unprocessedWarnTimeUnit?: 'hours' | 'days' | 'weeks' | 'months';
  /** 控制已处理警告显示时长。默认: 48小时 */
  processedWarnTime?: number;
  /** 控制已处理警告显示时长单位。默认: hours */
  processedWarnTimeUnit?: 'hours' | 'days' | 'weeks' | 'months';
  /** 控制特级警告显示红色加粗效果 */
  showSpecialWarn?: boolean;
  /** 控制哪些类型的警告不展示，数组中存储警告类型的英文key */
  hiddenWarnTypes?: string[];
  /** 控制警告面板最大显示条数 */
  maxDisplayCount?: number;
}

export interface WarningSceneConfig {
  /** 控制二级筛选项配置 */
  secondFilterItems?: SecondFilterItems[];
  /** 控制警告配置 */
  warnConfig?: WarnConfig;
}
