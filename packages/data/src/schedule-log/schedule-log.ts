/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { EventSchedulingBasicInfo } from '../event-scheduling/basic-info';
import { ScheduleInfo, ShiftInfo } from '../shift-schedule';
import { ShiftHandoverInfo } from './shift-handover';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

export type ScheduleLogDateRange = [string, string];

export const defaultDateRange: ScheduleLogDateRange = [
  dayjs().subtract(8, 'days').format('YYYY-MM-DD'),
  dayjs().add(8, 'days').format('YYYY-MM-DD'),
];

/** 班次元数据 */
export interface ScheduleShiftMeta {
  id?: string; // 班次 ID
  key?: string; // 班次 DOM ID
  name?: string; // 班次名称
  date?: string; // 班次日期
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  nextId?: string; // 下一个班次 ID
}

/** 天气数据 */
export interface ScheduleWeather {
  dayWeather?: string; // 天气
  temperatureMax?: string; // 最高温
  temperatureMin?: string; // 最低温
}

/** 按日期分类的天气数据 */
export interface ScheduleWeatherByDate {
  [key: string]: ScheduleWeather;
}

/** 班次数据 */
export interface ScheduleShift {
  shift?: ScheduleShiftMeta; // 班次数据
  events?: EventSchedulingBasicInfo[]; // 事件
  handovers?: ShiftHandoverInfo[]; // 交接班
  schedules?: ScheduleInfo[]; // 排班
}

/** 日志数据 */
export interface ScheduleLog {
  date?: string; // 日期
  weather?: ScheduleWeather; // 天气
  shifts?: ScheduleShift[]; // 班次数据
}

/** 每日概况 */
export interface DailySummaryItems {
  date?: string; // 日期
  summary?: string; // 概况
  creator?: string; // 创建人
  createTime?: string; // 创建时间
}

/** 每日概况日志 */
export interface DailySummaryLogItems {
  id?: string; // 日志 ID
  date?: string; // 日期
  summary?: string; // 概况
  operator?: string; // 操作人
  time?: string; // 操作时间
}

/** 格式化日期 */
export const formatDate = (date?: dayjs.Dayjs | string): string =>
  dayjs(date).format('YYYY-MM-DD');

/** 生成指定日期范围的日期 */
export const generateDateKeyByDateRange = (
  dateRange: ScheduleLogDateRange,
): string[] => {
  const startDay = dayjs(dateRange[0]);
  const endDay = dayjs(dateRange[1]);
  const daysDiff = endDay.diff(startDay, 'day');

  return Array.from({ length: daysDiff + 1 }, (_, index) =>
    startDay.add(index, 'day').format('YYYY-MM-DD'),
  );
};

/** 将配置的班次信息和数据库储存的班次信息合为一体 */
export const generateScheduleShiftMeta = (
  shift: ShiftInfo,
  shiftConfigs: ScheduleShiftMeta[],
  nextId?: string,
): ScheduleShiftMeta => {
  const shiftConfig = shiftConfigs.find((sc) => sc.name === shift.shiftName);

  return {
    id: shift.shiftId,
    key: shiftConfig?.key,
    name: shift.shiftName,
    date: shift.shiftDate,
    startTime: shift.startTime,
    endTime: shift.endTime,
    nextId,
  };
};

/** 聚合事件、班次、排班、交接班、天气为一体的日志信息 */
export const newAggregateScheduleDataToLog = (
  events: EventSchedulingBasicInfo[],
  shiftInfos: ShiftInfo[],
  scheduleInfos: ScheduleInfo[],
  handovers: ShiftHandoverInfo[],
  weathers: ScheduleWeatherByDate,
  dateRange: ScheduleLogDateRange,
  shiftsConfig: ScheduleShiftMeta[],
): ScheduleLog[] => {
  const dates = generateDateKeyByDateRange(dateRange);

  const firstShiftIdOfNextDay = dates.reduce<{
    [key: string]: string | undefined;
  }>((acc, date, index) => {
    const nextDay = dates[index + 1];
    if (nextDay) {
      const firstShiftOfNextDay = shiftInfos.find(
        (shift) => formatDate(shift.shiftDate) === nextDay,
      );
      if (firstShiftOfNextDay) {
        return { ...acc, [date]: firstShiftOfNextDay.shiftId };
      }
    }
    return acc;
  }, {});

  return dates.map((date) => {
    const dailyShiftInfos = shiftInfos.filter(
      (shift) => formatDate(shift.shiftDate) === date,
    );

    const shifts = dailyShiftInfos.map((shiftInfo, index) => {
      const nextShiftId =
        index + 1 < dailyShiftInfos.length
          ? dailyShiftInfos[index + 1].shiftId
          : firstShiftIdOfNextDay[date];
      const shiftsMeta = generateScheduleShiftMeta(
        shiftInfo,
        shiftsConfig,
        nextShiftId,
      );

      const eventsForShift = events
        .filter((event) => {
          const eventStartTime = dayjs(event.eventStartTime);
          const shiftStartTime = dayjs(shiftInfo.startTime);
          const shiftEndTime = dayjs(shiftInfo.endTime);
          return (
            eventStartTime.isSameOrAfter(shiftStartTime) &&
            eventStartTime.isBefore(shiftEndTime)
          );
        })
        .sort((a, b) => a.eventStartTime.localeCompare(b.eventStartTime));

      const schedulesForShift = scheduleInfos.filter(
        (schedule) => schedule.shiftId === shiftInfo.shiftId,
      );

      const handoversForShift = handovers.filter(
        (handover) => handover.fromShiftId === shiftInfo.shiftId,
      );

      return {
        shift: shiftsMeta,
        events: eventsForShift,
        handovers: handoversForShift,
        schedules: schedulesForShift,
      };
    });

    return {
      date,
      weather: weathers?.[date] || undefined,
      shifts,
    };
  });
};

/** 获取指定时间所对应的日志信息 */
export const getScheduleLogByDate = (
  targetTime: string,
  scheduleLogs: ScheduleLog[],
): ScheduleShift | undefined => {
  const targetDate = dayjs(targetTime);
  const dateKeys = [
    targetDate.subtract(1, 'day').format('YYYY-MM-DD'),
    targetDate.format('YYYY-MM-DD'),
    targetDate.add(1, 'day').format('YYYY-MM-DD'),
  ];

  const targetLogs = scheduleLogs.filter((log) =>
    dateKeys.includes(log.date ?? ''),
  );

  let foundShift: ScheduleShift | undefined;
  targetLogs.some((targetLog) => {
    const shiftData = targetLog.shifts?.find(
      (log) =>
        dayjs(log.shift?.startTime).isSameOrBefore(targetTime) &&
        dayjs(log.shift?.endTime).isSameOrAfter(targetTime),
    );

    if (shiftData) {
      foundShift = {
        shift: shiftData.shift,
        events: shiftData.events,
        handovers: shiftData.handovers,
        schedules: shiftData.schedules,
      };
      return true;
    }

    return false;
  });

  return foundShift;
};
