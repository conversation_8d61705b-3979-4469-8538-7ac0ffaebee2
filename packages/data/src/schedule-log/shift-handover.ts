/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export enum ShiftHandoverState {
  /** 未生成日志 */
  NOT_CREATED = -1,
  /** 已生成日志，未点击交接班 */
  CREATED = 0,
  /** 已生成日志，已点击交接班，未确认交接 */
  CONFIRMED = 1,
  /** 已生成日志，已点击交接班，已确认交接 */
  CREATED_AND_CONFIRMED = 2,
}

export interface ShiftHandoverInfo {
  id?: string; // 交接班 ID
  content?: string; // 交接工作内容
  planContent?: string; // 计划工作内容
  noteContent?: string; // 注意事项
  remark?: string; // 备注
  state?: ShiftHandoverState; // 交接班状态
  creator?: string; // 创建人
  createTime?: string; // 创建时间
  handoverTime?: string; // 交班时间
  takeoverTime?: string; // 接班时间
  fromShiftId?: string; // 交班班次 ID
  fromShiftName?: string; // 交班班次名称
  fromShiftDate?: string; // 交班日期
  fromShiftStartTime?: string; // 交班开始时间
  fromShiftEndTime?: string; // 交班结束时间
  fromUser?: string; // 交班人
  fromUserId?: string; // 交班人 ID
  toShiftId?: string; // 接班班次 ID
  toShiftName?: string; // 接班班次名称
  toShiftDate?: string; // 接班日期
  toShiftStartTime?: string; // 接班开始时间
  toShiftEndTime?: string; // 接班结束时间
  toUser?: string; // 接班人
  toUserId?: string; // 接班人 ID
}

export interface HandoverOperationLogItem {
  logId: string;
  operationType: string;
  operationTime: string;
  operatorId: string;
  operatorName: string;
  beforeData: string;
  afterData: string;
}

export const getHandoverOperationTypeText = (type: string): string => {
  switch (type) {
    case 'INSERT':
      return '创建';
    case 'UPDATE':
      return '编辑';
    default:
      return type;
  }
};

export const getHandoverStateText = (state: ShiftHandoverState): string => {
  switch (state) {
    case ShiftHandoverState.CREATED:
      return '已生成日志';
    case ShiftHandoverState.CONFIRMED:
      return '确认交班';
    case ShiftHandoverState.CREATED_AND_CONFIRMED:
      return '确认接班';
    case ShiftHandoverState.NOT_CREATED:
      return '未创建';
    default:
      return state;
  }
};

export const getHandoverStateTagColor = (state: ShiftHandoverState): string => {
  switch (state) {
    case ShiftHandoverState.CONFIRMED:
      return 'processing';
    case ShiftHandoverState.CREATED_AND_CONFIRMED:
      return 'success';
    default:
      return 'default';
  }
};
