/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from './database';
import { makeObjectId } from './object-item';
import { getUnitFormat, UnitFormat } from './unit-system';

export interface PumpData {
  pumpName: string;
  pumpPattern: string;
  variable: boolean;
  defaultValue: number;
  value?: number;
  minFrequency?: number | null;
  maxFrequency?: number | null;
  defaultRange?: number;
}
export interface SchedulingOperateTimeData {
  name: string;
  startTime: string;
  endTime: string;
  stateDetail: {
    otype: string;
    oname: string;
    scadaName: string;
    value: string;
    originalValue: string | number;
    unit?: string;
    formatValue?: string | number;
    targetValue?: string | number;
    startTime?: string;
    endTime?: string;
    targetDiff?: string | number;
  }[];
  change?: boolean;
}

export interface SchedulingOperateTimeFlattenData {
  otype: string;
  oname: string;
  scadaName: string;
  value: string | number;
  originalValue: string | number;
  targetValue?: string | number;
  startTime: string;
  endTime: string;
  change?: boolean;
}
interface BarData {
  value: [string, number];
  elseData: SchedulingOperateTimeData;
  itemStyle: {
    color: string;
    decal:
      | {
          color: string;
          dashArrayX: number[];
          dashArrayY: number[];
          rotation: number;
        }
      | undefined;
    borderWidth: number;
    borderColor: string;
  };
}

export interface PlantAndPumpStationSchedulingData {
  pattern: string;
  value: string | number | boolean;
  type?: string;
  title?: string;
  variable?: boolean;
  unit?: UnitFormat;
}

export interface MatchSchedulingOperateData {
  id: string;
  matchTime: string;
  matchStartTime: string;
  matchEndTime: string;
  matchScore: number;
  remark: string;
  convertSchedulingData: {
    [key: string]: SchedulingOperateTimeData[];
  };
}
export interface HistorySuggestScheduling {
  startTime: string;
  endTime: string;
  matchTime: string;
  schedulingOperateData: MatchSchedulingOperateData[];
}

function initializeSchedulingData(schedulingData: any): {
  heads: PlantAndPumpStationSchedulingData[];
  flows: PlantAndPumpStationSchedulingData[];
  pumps: PumpData[];
} {
  const data = {
    heads: [],
    flows: [],
    pumps: [],
  };
  if (schedulingData.HEAD) {
    data.heads = schedulingData.HEAD.map((item: any) => ({
      pattern: item.pat_name,
      value: item.pat_value,
      title: item.title,
      unit: getUnitFormat('HEAD_SIM'),
    }));
  }
  if (schedulingData.FLOW) {
    data.flows = schedulingData.FLOW.map((item: any) => ({
      pattern: item.pat_name,
      value: item.pat_value,
      title: item.title,
      unit: getUnitFormat('FLOW_SIM'),
    }));
  }
  if (schedulingData.PUMP) {
    data.pumps = schedulingData.PUMP.map((item: any) => ({
      pumpName: item.title,
      pumpPattern: item.pat_name,
      variable: item.frequency_mode === 'Variable',
      value:
        item.frequency_mode === 'Variable'
          ? Number((item.pat_value * 50).toFixed(1))
          : item.pat_value,
      defaultValue:
        item.frequency_mode === 'Variable'
          ? Number((item.pat_value * 50).toFixed(1))
          : item.pat_value,
      minFrequency: item.min_frequency,
      maxFrequency: item.max_frequency,
    }));
  }
  return data;
}

export class PlantSimulationInfo {
  private _id: string = '';

  private _title: string = '';

  private _head: PlantAndPumpStationSchedulingData[] = [];

  private _flow: PlantAndPumpStationSchedulingData[] = [];

  private _pumps: PumpData[] = [];

  private _settingSchedulingMode: string = '';

  private _settingSchedulingData:
    | PlantAndPumpStationSchedulingData[]
    | undefined = undefined;

  schedulingResultData: { title: string; value: number }[] | undefined =
    undefined;

  headUnit = getUnitFormat('HEAD_SIM');

  flowUnit = getUnitFormat('FLOW_SIM');

  constructor(plantData: any) {
    this._id = plantData.fact_name;
    this._title = plantData.fact_title;
    const schedulingData = initializeSchedulingData(plantData.modes);
    this._head = schedulingData.heads;
    this._flow = schedulingData.flows;
    this._pumps = schedulingData.pumps;

    this.settingSchedulingMode = plantData.fact_mode;
    if (plantData.fact_mode === 'HEAD') {
      if (this._head) {
        this.settingSchedulingData = this._head.map((item) => ({
          pattern: item.pattern,
          value: item.value,
          title: item.title,
          unit: this.headUnit,
        }));
      }
    } else if (plantData.fact_mode === 'FLOW') {
      if (this._flow) {
        this.settingSchedulingData = this._flow.map((item) => ({
          pattern: item.pattern,
          value: item.value,
          title: item.title,
          unit: this.flowUnit,
        }));
      }
    } else {
      this.settingSchedulingData = this._pumps.map((item) => ({
        variable: item.variable,
        pattern: item.pumpPattern,
        value: item.defaultValue,
      }));
    }
  }

  set settingSchedulingMode(mode: string) {
    this._settingSchedulingMode = mode;
  }

  set settingSchedulingData(data:
    | PlantAndPumpStationSchedulingData[]
    | undefined,) {
    const currentData: PlantAndPumpStationSchedulingData[] = [];
    if (this._settingSchedulingMode === 'FLOW') {
      if (data) {
        data.forEach((item) => {
          currentData.push({
            ...item,
            value: this.flowUnit?.getValue(item.value as number) as number,
            unit: this.flowUnit,
          });
        });
      }
    } else if (this._settingSchedulingMode === 'HEAD') {
      if (data) {
        data.forEach((item) => {
          currentData.push({
            ...item,
            value: this.headUnit?.getValue(item.value as number) as number,
            unit: this.headUnit,
          });
        });
      }
    }
    this._settingSchedulingData = data;
  }

  get id(): string {
    return this._id;
  }

  get title(): string {
    return this._title;
  }

  get head(): PlantAndPumpStationSchedulingData[] {
    return this._head;
  }

  get flow(): PlantAndPumpStationSchedulingData[] {
    return this._flow;
  }

  get pumps(): PumpData[] {
    return this._pumps;
  }

  getPumpByPattern(pattern: string): PumpData | undefined {
    return this._pumps.find((item) => item.pumpPattern === pattern);
  }

  get currentSchedulingMode(): string {
    return this._settingSchedulingMode;
  }

  get currentSchedulingData(): PlantAndPumpStationSchedulingData[] | undefined {
    return this._settingSchedulingData;
  }

  getSchedulingDataByMode(
    mode: string | undefined,
  ): PlantAndPumpStationSchedulingData[] {
    switch (mode) {
      case 'HEAD':
        return this._head;
      case 'FLOW':
        return this._flow;
      case 'PUMP':
        return this._pumps.map((item) => ({
          variable: item.variable,
          pattern: item.pumpPattern,
          value: item.defaultValue,
        }));
      default:
        return [];
    }
  }

  getPatternTitle(pattern: string): string {
    const foundHeadInfo = this._head.find((item) => item.pattern === pattern);
    if (foundHeadInfo) return foundHeadInfo.title ?? pattern;
    const foundFlowInfo = this._flow.find((item) => item.pattern === pattern);
    if (foundFlowInfo) return foundFlowInfo.title ?? pattern;
    const foundPumpInfo = this._pumps?.find(
      (item) => item.pumpPattern === pattern,
    );
    if (foundPumpInfo) return foundPumpInfo.pumpName ?? pattern;
    return pattern;
  }

  getPatternType(
    pattern: string,
  ): 'HEAD' | 'FLOW' | 'FIXED' | 'VARIABLE' | undefined {
    if (this.head.findIndex((item) => item.pattern === pattern) >= 0)
      return 'HEAD';
    if (this.flow.findIndex((item) => item.pattern === pattern) >= 0)
      return 'FLOW';
    const pump = this.pumps.find((item) => item.pumpPattern === pattern);
    if (pump !== undefined) return pump.variable ? 'VARIABLE' : 'FIXED';
    return undefined;
  }
}

export class PumpStationSimulationInfo {
  private _id: string = '';

  private _title: string = '';

  private _head: PlantAndPumpStationSchedulingData[] = [];

  private _flow: PlantAndPumpStationSchedulingData[] = [];

  private _pumps: PumpData[] = [];

  private _settingSchedulingData:
    | PlantAndPumpStationSchedulingData[]
    | undefined = undefined;

  schedulingResultData: { title: string; value: number }[] | undefined =
    undefined;

  headUnit = getUnitFormat('HEAD_SIM');

  flowUnit = getUnitFormat('FLOW_SIM');

  constructor(plantData: any) {
    this._id = plantData.fact_name;
    this._title = plantData.fact_title;
    const schedulingData = initializeSchedulingData(plantData.modes);
    this._head = schedulingData.heads;
    this._flow = schedulingData.flows;
    this._pumps = schedulingData.pumps;

    const schedulingDatas = [];
    if (this._head) {
      schedulingDatas.push(
        ...this._head.map((item) => ({
          title: item.title,
          pattern: item.pattern,
          value: item.value,
          unit: this.headUnit,
        })),
      );
    }
    if (this._flow) {
      schedulingDatas.push(
        ...this._flow.map((item) => ({
          title: item.title,
          pattern: item.pattern,
          value: item.value,
          unit: this.flowUnit,
        })),
      );
    }
    schedulingDatas.push(
      ...this._pumps.map((item) => ({
        type: 'pump',
        pattern: item.pumpPattern,
        value: item.defaultValue,
        variable: item.variable,
        title: item.pumpName,
      })),
    );
    this._settingSchedulingData = schedulingDatas;
  }

  set settingSchedulingData(data:
    | PlantAndPumpStationSchedulingData[]
    | undefined,) {
    this._settingSchedulingData = data;
  }

  get id(): string {
    return this._id;
  }

  get title(): string {
    return this._title;
  }

  get head(): PlantAndPumpStationSchedulingData[] {
    return this._head;
  }

  get flow(): PlantAndPumpStationSchedulingData[] {
    return this._flow;
  }

  get pumps(): PumpData[] {
    return this._pumps;
  }

  get currentSchedulingData(): PlantAndPumpStationSchedulingData[] | undefined {
    return this._settingSchedulingData;
  }

  getPatternType(
    pattern: string,
  ): 'HEAD' | 'FLOW' | 'FIXED' | 'VARIABLE' | undefined {
    if (this.head.findIndex((item) => item.pattern === pattern) >= 0)
      return 'HEAD';
    if (this.flow.findIndex((item) => item.pattern === pattern) >= 0)
      return 'FLOW';
    const pump = this.pumps.find((item) => item.pumpPattern === pattern);
    if (pump !== undefined) return pump.variable ? 'VARIABLE' : 'FIXED';
    return undefined;
  }

  getPatternTitle(pattern: string): string {
    const foundHeadInfo = this._head.find((item) => item.pattern === pattern);
    if (foundHeadInfo) return foundHeadInfo.title ?? pattern;
    const foundFlowInfo = this._flow.find((item) => item.pattern === pattern);
    if (foundFlowInfo) return foundFlowInfo.title ?? pattern;
    const foundPumpInfo = this._pumps?.find(
      (item) => item.pumpPattern === pattern,
    );
    if (foundPumpInfo) return foundPumpInfo.pumpName ?? pattern;
    return pattern;
  }
}

export class SchedulingData {
  private _plants: Map<string, PlantSimulationInfo> = new Map();

  private _pumpStations: Map<string, PumpStationSimulationInfo> = new Map();

  constructor(data: any) {
    const plantList = data.filter(
      (item: any) => item.fact_type === 'SDFOLD_FACT',
    );
    this.initializePlant(plantList);
    const pumpList = data.filter(
      (item: any) => item.fact_type !== 'SDFOLD_FACT',
    );
    this.initializePump(pumpList);
  }

  private initializePlant(plantData: any) {
    plantData.forEach((plant: any) => {
      this._plants.set(plant.fact_name, new PlantSimulationInfo(plant));
    });
  }

  private initializePump(pumpData: any) {
    pumpData.forEach((pump: any) => {
      this._pumpStations.set(
        pump.fact_name,
        new PumpStationSimulationInfo(pump),
      );
    });
  }

  get plants(): Map<string, PlantSimulationInfo> {
    return this._plants;
  }

  get pumpStations(): Map<string, PumpStationSimulationInfo> {
    return this._pumpStations;
  }

  getPlant(id: string): PlantSimulationInfo | undefined {
    return this._plants.get(id);
  }

  getPumpStation(id: string): PumpStationSimulationInfo | undefined {
    return this._pumpStations.get(id);
  }
}

function formatSchedulingWaterPumpData(
  data: any,
  database: Database,
  elseInfo?: any,
) {
  return data
    ? data.map((pump: any) => {
        const unitFormat = database.getUnitFormat(pump.otype, 'SDVAL');
        return {
          otype: pump.otype,
          oname: pump.oname,
          startTime: pump.start_time,
          endTime: pump.end_time,
          scadaName: (elseInfo?.title ?? '') + pump.description,
          value: unitFormat?.getValueWithSymbol(pump.base_value),
          formatValue: unitFormat?.getValue(pump.base_value) ?? '',
          unit: unitFormat?.unitSymbol ?? '',
          originalValue: unitFormat?.getValue(pump.base_value),
        };
      })
    : [];
}

function formatSchedulingPressureData(data: any, database: Database) {
  return data
    ? data.map((pressure: any) => {
        const unitFormat = database.getUnitFormat(pressure.otype, 'SDVAL');
        return {
          otype: pressure.otype,
          oname: pressure.oname,
          startTime: pressure.start_time,
          endTime: pressure.end_time,
          scadaName: pressure.description,
          value: unitFormat?.getValueWithSymbol(pressure.base_value),
          formatValue: unitFormat?.getValue(pressure.base_value) ?? '',
          unit: unitFormat?.unitSymbol ?? '',
          originalValue: unitFormat?.getValue(pressure.base_value),
        };
      })
    : [];
}

function getIndicatorMap(data: any): {
  [key: string]: { otype: string; oname: string };
} {
  if (!data) return {};
  const indicatorMap: { [key: string]: { otype: string; oname: string } } = {};
  data.forEach((pressure: any) => {
    const id = makeObjectId(pressure.otype, pressure.oname);
    const indicator = indicatorMap[id];
    if (!indicator) {
      indicatorMap[id] = {
        otype: pressure.otype,
        oname: pressure.oname,
      };
    }
  });
  return indicatorMap;
}

export function transformToOperateTimeData(
  baseData: any[],
  database: Database,
): SchedulingOperateTimeData[] {
  const formatDataArray: SchedulingOperateTimeData[] = [];

  baseData.forEach((item: any, index: number) => {
    const stateDetail = item.data.flatMap((factory: any) => {
      const factoryInfo = database.getDeviceById(
        makeObjectId(factory.otype, factory.oname),
      );

      if (!factoryInfo) return [];

      const waterPumpDetails = formatSchedulingWaterPumpData(
        factory.WATER_PUMP,
        database,
        factoryInfo,
      );

      const pressureDetails = formatSchedulingPressureData(
        factory.PRESSURE_ADJUSTMENT,
        database,
      );

      const flowDetails = formatSchedulingPressureData(
        item.data.FLOW_ADJUSTMENT,
        database,
      );

      return [...waterPumpDetails, ...pressureDetails, ...flowDetails];
    });
    formatDataArray.push({
      name: `data-${index}`,
      startTime: item.startTime,
      endTime: item.endTime,
      stateDetail,
    });
  });

  return formatDataArray;
}

export function transformPlantToOperateTimeData(
  baseData: any[],
  database: Database,
): {
  schedulingOperateData: SchedulingOperateTimeData[];
  indicatorMap: {
    [key: string]: {
      otype: string;
      oname: string;
    };
  };
} {
  const formatDataArray: SchedulingOperateTimeData[] = [];
  let indicatorMap: {
    [key: string]: { otype: string; oname: string };
  } = {};
  baseData.forEach((item: any, index: number) => {
    const waterPumpDetails = formatSchedulingWaterPumpData(
      item.data.WATER_PUMP,
      database,
    );

    const pressureDetails = formatSchedulingPressureData(
      item.data.PRESSURE_ADJUSTMENT,
      database,
    );

    const flowDetails = formatSchedulingPressureData(
      item.data.FLOW_ADJUSTMENT,
      database,
    );

    indicatorMap = {
      ...indicatorMap,
      ...getIndicatorMap(item.data.WATER_PUMP),
      ...getIndicatorMap(item.data.PRESSURE_ADJUSTMENT),
      ...getIndicatorMap(item.data.FLOW_ADJUSTMENT),
    };

    formatDataArray.push({
      name: `data-${index}`,
      startTime: item.startTime,
      endTime: item.endTime,
      stateDetail: [...waterPumpDetails, ...pressureDetails, ...flowDetails],
    });
  });

  return { schedulingOperateData: formatDataArray, indicatorMap };
}

export function transformScadaToOperateTimeData(
  baseData: any[],
  database: Database,
): SchedulingOperateTimeData[] {
  const formatDataArray: SchedulingOperateTimeData[] = [];

  baseData.forEach((item: any, index: number) => {
    const unitFormat = database.getUnitFormat(item.otype, 'SDVAL');
    const indicator = database.getIndicator(item.otype, item.oname);
    formatDataArray.push({
      name: `data-${index}`,
      startTime: item.startTime,
      endTime: item.endTime,
      stateDetail: [
        {
          otype: item.otype,
          oname: item.oname,
          scadaName: indicator?.title ?? unitFormat?.unitTitle ?? '',
          value: unitFormat?.getValueWithSymbol(item.data) ?? '',
          originalValue: unitFormat?.getValue(item.data)?.toString() ?? '',
          formatValue: unitFormat?.getValue(item.data) ?? '',
          unit: unitFormat?.unitSymbol ?? '',
        },
      ],
    });
  });

  return formatDataArray;
}

export function getDiff(originalValue: number, targetValue: number) {
  const oValue = Number(originalValue);
  const tValue = Number(targetValue);
  if (Number.isNaN(tValue) || Number.isNaN(oValue)) {
    return targetValue;
  }
  return tValue - oValue;
}

function calculateChange(
  beforeData: SchedulingOperateTimeData,
  afterData: SchedulingOperateTimeData,
): SchedulingOperateTimeData | null {
  const beforeEndTime = dayjs(beforeData.endTime);
  const afterStartTime = dayjs(afterData.startTime);

  if (beforeEndTime.isSame(afterStartTime)) {
    console.error('Time ranges are not continuous');
    return null;
  }

  const afterDataMap = new Map(
    afterData.stateDetail.map((item) => [`${item.otype}@${item.oname}`, item]),
  );

  const newData: SchedulingOperateTimeData = {
    name: afterData.name,
    startTime: beforeData.endTime,
    endTime: afterData.startTime,
    change: true,
    stateDetail: [],
  };

  beforeData.stateDetail.forEach((detail1) => {
    const key = `${detail1.otype}@${detail1.oname}`;
    const detail2 = afterDataMap.get(key);

    if (detail2) {
      if (detail1.value !== detail2.value) {
        newData.stateDetail.push({
          otype: detail1.otype,
          oname: detail1.oname,
          scadaName: detail1.scadaName,
          value: `${detail1.formatValue}->${detail2.formatValue}${detail2.unit}`,
          originalValue: detail1.originalValue,
          targetValue: detail2.originalValue,
          targetDiff: getDiff(
            detail1.originalValue as number,
            detail2.originalValue as number,
          ),
        });
      }
      afterDataMap.delete(key);
    } else {
      newData.stateDetail.push({
        otype: detail1.otype,
        oname: detail1.oname,
        scadaName: detail1.scadaName,
        value: `删除该点`,
        originalValue: detail1.value,
      });
    }
  });

  [...afterDataMap.values()].forEach((item) => {
    newData.stateDetail.push({
      otype: item.otype,
      oname: item.oname,
      scadaName: item.scadaName,
      value: `新增 ${item.value}`,
      originalValue: '',
      targetValue: item.value,
    });
  });

  return newData;
}

export function processOperateTimeData(
  inputData: SchedulingOperateTimeData[],
  currentDates: string[],
): SchedulingOperateTimeData[] {
  const result: SchedulingOperateTimeData[] = [];
  let allTimeData: SchedulingOperateTimeData[] = [];
  for (let i = 0; i < inputData.length; i += 1) {
    const current = inputData[i];
    const next = inputData[i + 1];
    if (
      currentDates.includes(dayjs(current.endTime).format('YYYY-MM-DD')) ||
      (next &&
        currentDates.includes(dayjs(current.startTime).format('YYYY-MM-DD')))
    ) {
      result.push(current);
      if (next) {
        const newData = calculateChange(current, next);
        if (newData) {
          result.push(newData);
        }
      }
    }
    if (
      dayjs(current.startTime).isBefore(dayjs(currentDates[0]), 'd') &&
      dayjs(current.endTime).isAfter(
        dayjs(currentDates[currentDates.length - 1]),
        'd',
      )
    ) {
      allTimeData = [current];
    }
  }

  return result.length > 0 ? result : allTimeData;
}

function getOperateColor(isAfterTime: boolean, isChange?: boolean) {
  if (isChange) {
    if (!isAfterTime) {
      return '#ffffff';
    }
    return '#b8f1c5';
  }
  return '#B9DFF2';
}

export function getStatusChangeFlags(
  pumpTimeData: SchedulingOperateTimeData[],
  previous?: boolean,
) {
  if (pumpTimeData.length === 0) return [];
  const operateTimeData: BarData[] = [];
  for (let i: number = 0; i < pumpTimeData.length; i += 1) {
    let time = dayjs(pumpTimeData[i].startTime)
      .add(1, 'minute')
      .format('YYYY-MM-DD HH:mm:00');
    let step = 1;
    while (!dayjs(time).isAfter(dayjs(pumpTimeData[i].endTime))) {
      operateTimeData.push({
        value: [time, 1],
        elseData: pumpTimeData[i],
        itemStyle: {
          color: getOperateColor(!!previous, pumpTimeData[i].change),
          decal: pumpTimeData[i].change
            ? {
                color: previous ? '#8ceb8b' : '#096dd9',
                dashArrayX: [1, 0],
                dashArrayY: [4, 3],
                rotation: -Math.PI / 4,
              }
            : undefined,
          borderWidth: -1,
          borderColor: 'transparent',
        },
      });
      time = dayjs(pumpTimeData[i].startTime)
        .add(step * 1, 'minute')
        .format('YYYY-MM-DD HH:mm:00');
      step += 1;
    }
  }

  return operateTimeData;
}

function getDateArray(startDate: Date, endDate: Date) {
  const dates = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    dates.push(dayjs(currentDate).format('YYYY-MM-DD'));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

export function getOperationChange(
  dateRange: { startTime: string; endTime: string },
  pumpTimeData: SchedulingOperateTimeData[],
): SchedulingOperateTimeData[] {
  const startDate = new Date(
    dayjs(dateRange.startTime).format('YYYY-MM-DD 00:00:00'),
  );
  const endDate = new Date(dateRange.endTime);
  const dateArray: string[] = getDateArray(startDate, endDate);

  return processOperateTimeData(pumpTimeData, dateArray);
}

export function getOperationTime(
  dateRange: { startTime: string; endTime: string },
  pumpTimeData: SchedulingOperateTimeData[],
): Array<BarData> {
  const resetData = getOperationChange(dateRange, pumpTimeData);
  return getStatusChangeFlags(resetData);
}
