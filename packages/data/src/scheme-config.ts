/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Key } from './types';

export type SchemeTypeData = {
  /** 类型 */
  type: string;
  /** 类型名称 */
  title: string;
}[];

export const convertSchemeTypeDataToOptions = (
  data: SchemeTypeData,
): Array<{ label: string; value: string }> =>
  data.map(({ type, title }) => ({
    label: title,
    value: type,
  }));

export const getSchemeTypeDataTitle = (
  typeData: SchemeTypeData,
  type: string,
): string => {
  const data = typeData.find((item) => item.type === type);
  return data ? data.title : type;
};

export enum ApplyType {
  SCADA_MANAGE = 'SCADA_MANAGE',
  CHART_ANALYSIS = 'CHART_ANALYSIS',
}

export enum SchemeType {
  MONITORING_EXCEPTION = 'MONITORING_EXCEPTION',
  PIPELINE_BURST = 'PIPELINE_BURST',
  WATER_QUALITY_POLLUTION = 'WATER_QUALITY_POLLUTION',
  WATER_PLANT_EXCEPTION = 'WATER_PLANT_EXCEPTION',
  ZONE_MONITORING = 'ZONE_MONITORING',
  OTHER = 'OTHER',
  WATER_PLANT_CONTROL = 'WATER_PLANT_CONTROL',
}

export interface ScadaFavorite {
  title: string;
  checkedKeys: Key[];
  tabType: string;
  formData:
    | {
        timeRange?: string[];
        warn?: boolean | undefined;
        warnLine?: boolean | undefined;
        envelopLine?: boolean | undefined;
        chainBase?: boolean | undefined;
        yMin?: number | undefined;
        yMax?: number | undefined;
        timeRangeType?: string;
        compareType?: {
          type: string;
          dateRanges: string[][];
        };
      }
    | undefined;
}

export interface SchemeConfig {
  schemeName: string;
  applyType: string;
  schemeType: string;
  configValue: string;
  schemeShare: boolean;
  isFixedTime: boolean;
  remark: string;
}

export interface SchemeData {
  id: string;
  userName: string;
  schemeName: string;
  applyType: string;
  schemeType: string;
  configValue: string;
  schemeShare: boolean;
  isFixedTime: boolean;
  remark: string;
  createTime: string;
  createdByQueryUser: boolean;
}
