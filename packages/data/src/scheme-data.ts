/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const backgroundSizeOptions = [
  { label: '自动', value: 'auto' },
  { label: '拉伸', value: 'cover' },
  { label: '适应', value: 'contain' },
  { label: '默认', value: 'inherit' },
];

export const backgroundPositionOptions = [
  { label: '居中', value: 'center' },
  { label: '居左', value: 'left' },
  { label: '居右', value: 'right' },
  { label: '居顶', value: 'top' },
  { label: '居底', value: 'bottom' },
];

export const backgroundRepeatOptions = [
  { label: '不循环', value: 'no-repeat' },
  { label: '循环', value: 'repeat' },
  { label: '横向循环', value: 'repeat-x' },
  { label: '纵向循环', value: 'repeat-y' },
  { label: '环绕', value: 'round' },
];
