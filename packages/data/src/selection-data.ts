/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface Item {
  objectId: string;
  indicatorType?: string;
  indicatorName?: string;
  vprop?: string;
  forecast?: boolean | undefined;
}

const ua =
  typeof navigator !== 'undefined' && typeof navigator.userAgent !== 'undefined'
    ? navigator.userAgent.toLowerCase()
    : '';

const MAC = ua.includes('macintosh');

export function platformModifierKeyOnly(event: MouseEvent): boolean {
  return (
    !event.altKey && (MAC ? event.metaKey : event.ctrlKey) && !event.shiftKey
  );
}
