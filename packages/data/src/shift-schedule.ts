/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import * as XLSX from 'xlsx-js-style';
import { Shift } from './dispatch-log/shift';
import {
  EventSchedulingContentType,
  EventSchedulingLog,
} from './event-scheduling/event-log';
import { HandoverOperationLogItem } from './schedule-log/shift-handover';
import { UserInfo } from './system-user';

export const MONTH_FORMAT = 'YYYY-MM'; // 月份格式
export const DAY_FORMAT_WITH_ZERO = 'YYYY-MM-DD 00:00:00'; // 某天的开始时间日期格式
export const DAY_FORMAT_WITH_FINIAL = 'YYYY-MM-DD 23:59:59'; // 某天的结束时间日期格式
export const DAYS_IN_WEEK = ['日', '一', '二', '三', '四', '五', '六']; // 一周的日期

export type ViewMode = 'table' | 'calendar'; // 视图模式
export type DateCellType = {
  type: string;
  content: string;
  isEmpty: boolean;
}[];

/** 班次信息 */
export interface ShiftInfo {
  shiftId?: string; // 班次ID
  shiftDate?: string; // 班次时间
  shiftName?: string; // 班次名称
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  createUser?: string; // 创建人
  createTime?: string; // 创建时间
}

/** 排班记录 */
export interface ScheduleInfo {
  scheduleId?: string; // 排班ID
  shiftId?: string; // 班次ID
  shiftDate?: string; // 班次时间
  shiftName?: string; // 班次名称
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  scheduleUser?: string; // 值班人
  scheduleUserId?: string; // 值班人ID
  remark?: string; // 备注
  createUser?: string; // 创建人
  createTime?: string; // 创建时间
}

/** 班次排班综合信息 */
export interface ShiftScheduleInfo {
  id?: string; // ID
  scheduleUserId?: string; // 值班人ID
  scheduleUser?: string; // 值班人
  department?: string; // 部门
  shiftName?: string; // 班次名称
  shiftIndex?: number; // 班次顺序
  shiftConfig?: Shift[]; // 班次配置
  scheduleInfo: ScheduleInfo[]; // 排班记录
}

export interface ShiftScheduleLogInfo {
  logId: string;
  shiftClassesId: string;
  type: 'DELETE' | 'CREATE';
  schedulingUser: string;
  remark: string;
  creator: string;
  shiftClasses: string;
  shiftDate: string;
  createTime: string;
}

/** 通过用户列表，班次列表，排班列表，整合综合信息 */
export const getShiftScheduleInfo = (
  users: UserInfo[],
  _shifts: ShiftInfo[],
  schedules: ScheduleInfo[],
  shiftConfig?: Shift[],
): ShiftScheduleInfo[] => {
  const shiftScheduleInfos: ShiftScheduleInfo[] = [];

  users.forEach((user) => {
    shiftConfig?.forEach((shiftConf, index) => {
      const userSchedulesForShift = schedules.filter(
        (schedule) =>
          schedule.scheduleUserId === user.id &&
          schedule.shiftName === shiftConf.name,
      );

      const info: ShiftScheduleInfo = {
        id: user.id + shiftConf.name,
        scheduleUserId: user.id,
        scheduleUser: user.name,
        department: user.departmentName,
        shiftName: shiftConf.name,
        shiftIndex: index,
        shiftConfig,
        scheduleInfo: userSchedulesForShift,
      };

      shiftScheduleInfos.push(info);
    });
  });

  return shiftScheduleInfos;
};

export const createMonthColumn = (month: string) => {
  const date = dayjs(month);
  const numberOfDays = date.daysInMonth();

  const columns = Array.from({ length: numberOfDays }, (_, index) => {
    const dayIndex = index + 1;
    const dayDate = date.date(dayIndex);
    const weekDay = DAYS_IN_WEEK[dayDate.day()];

    return {
      title: dayIndex.toString(),
      children: [
        {
          title: weekDay,
          dataIndex: `day${dayIndex}`,
          width: 25,
        },
      ],
    };
  });

  return columns;
};

export const createShiftColumn = (shifts: Shift[]) =>
  (shifts || [])?.map((shift) => ({
    title: shift.name,
  }));

export const convertToCalendarInfo = (
  value: dayjs.Dayjs,
  scheduleInfo: ScheduleInfo[] | undefined,
  shifts: Shift[] | undefined,
): DateCellType => {
  const shiftContent: Record<string, string[]> = {};

  scheduleInfo?.forEach((info) => {
    if (info.shiftDate && dayjs(info.shiftDate).isSame(value, 'day')) {
      const { shiftName } = info;
      if (shiftName) {
        if (!shiftContent[shiftName]) {
          shiftContent[shiftName] = [];
        }
        if (info.scheduleUser) {
          shiftContent[shiftName].push(info.scheduleUser);
        }
      }
    }
  });

  const listData: DateCellType =
    shifts
      ?.map((shift) => {
        const names = shiftContent[shift.name] || [];
        return {
          type: 'success',
          content: `${shift.name}: ${names.join(', ')}`,
          isEmpty: names.length === 0,
        };
      })
      .filter((item) => !item.isEmpty) ?? [];

  return listData;
};

// 创建排班表
export function exportScheduleExcel(
  shiftScheduleInfo: ShiftScheduleInfo[] | undefined,
  month: string | undefined,
  outputFileName: string,
  shifts: Shift[] | undefined,
): void {
  const startOfMonth = dayjs(month).startOf('month');
  const daysInMonth = startOfMonth.daysInMonth();

  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  const weekdaysForMonth = Array.from(
    { length: daysInMonth },
    (_, i) => weekDays[startOfMonth.add(i, 'day').day()],
  );
  const daysOfMonth = Array.from({ length: daysInMonth }, (_, i) =>
    startOfMonth.add(i, 'day').date(),
  );

  const shiftNames = (shifts ?? []).map((shift) => shift.name);

  const worksheetData: any[][] = [
    [
      '姓名',
      startOfMonth.format('YYYY年MM月'),
      ...Array(daysInMonth - 1).fill(null),
      ...shiftNames,
      '备注',
    ],
    ['', ...weekdaysForMonth],
    ['', ...daysOfMonth],
  ];

  const uniqueUsers: Record<string, any> = {};

  shiftScheduleInfo?.forEach((item) => {
    const userId = item.scheduleUserId ?? '';

    if (!uniqueUsers[userId]) {
      uniqueUsers[userId] = {
        userName: item.scheduleUser,
        schedule: [],
        shiftCount: shiftNames?.reduce(
          (acc, shift) => ({ ...acc, [shift]: 0 }), // 初始化班次统计
          {},
        ),
      };
    }

    // 将排班数据按日期存储
    item.scheduleInfo.forEach((schedule) => {
      const shiftDate = dayjs(schedule.shiftDate).date();
      const shiftName = schedule.shiftName ?? '';

      if (!uniqueUsers[userId].schedule[shiftDate]) {
        uniqueUsers[userId].schedule[shiftDate] = [];
      }

      // 添加班次的第一个字母
      uniqueUsers[userId].schedule[shiftDate].push(
        schedule.shiftName?.charAt(0),
      );
      uniqueUsers[userId].shiftCount[shiftName] += 1;
    });
  });

  Object.values(uniqueUsers).forEach((user) => {
    const row: any[] = [user.userName];

    for (let i = 1; i <= daysInMonth; i += 1) {
      const shifts = user.schedule[i];
      if (shifts && shifts.length > 0) {
        const combinedShifts = Array.from(new Set(shifts)).join('');
        row.push(combinedShifts);
      } else {
        row.push('休');
      }
    }

    shiftNames.forEach((shiftName) => {
      row.push(user.shiftCount[shiftName]);
    });

    row.push(''); // 备注
    worksheetData.push(row);
  });

  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // 合并 A1 到 A3
  worksheet['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 2, c: 0 } }];

  // 合并 B1 到指定列
  worksheet['!merges'].push({ s: { r: 0, c: 1 }, e: { r: 0, c: daysInMonth } });

  /// 合并班次总结列
  const summaryColumnStart = daysInMonth + 1; // 班次总结的起始列
  shifts?.forEach((_, index) => {
    worksheet?.['!merges']?.push({
      s: { r: 0, c: summaryColumnStart + index },
      e: { r: 2, c: summaryColumnStart + index },
    });
  });

  // 合并备注列
  worksheet['!merges'].push({
    s: { r: 0, c: summaryColumnStart + (shifts?.length ?? 0) },
    e: { r: 2, c: summaryColumnStart + (shifts?.length ?? 0) },
  });

  Object.keys(worksheet).forEach((key) => {
    if (!key.startsWith('!')) {
      worksheet[key].s = {
        font: {
          sz: '12',
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center',
          wrapText: true,
        },
      };
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, '排班表');

  XLSX.writeFile(workbook, outputFileName);
}

// 修改日志信息类型
export interface EventLogInfo {
  id: string; // ID 不为空
  time: string;
  contentType: EventSchedulingContentType;
  fieldName: string | null;
  changeType: string | null;
  oldValue: string | null;
  newValue: string | null;
  associationName: string | null;
  changeUserName: string;
}

// 事件信息类型
export interface EventInfo {
  otype: string; // 组合 ID 不为空
  oname: string; // 组合 ID 不为空
  title: string | null;
  eventType1: string | null;
  eventType2: string | null;
  address: string | null;
  content: string | null;
  startTime: string | null;
  endTime: string | null;
  creator: string | null;
  createTime: string | null;
  eventState: string | null;
  state: string | null;
  planStartTime: string | null;
  planEndTime: string | null;
  mark: string | null;
  eventLevel: string | null;
  label: string | null;
  logs: EventSchedulingLog[];
}

// 班次列表信息类型
export interface ShiftScheduleListItem {
  shiftId: string; // ID 不为空
  handoverId: string; // ID 不为空
  shiftDate: string | null;
  shiftClasses: string | null;
  schedulingUserNames: string | null;
  toSchedulingUserNames: string | null;
  startTime: string | null;
  endTime: string | null;
  creator: string | null;
  handoverContent: string | null;
  planContent: string | null;
  noteContent: string | null;
  remark: string | null;
  handoverTime: string | null;
  takeoverTime: string | null;
  events: EventInfo[];
  handoverLogs: HandoverOperationLogItem[];
}
