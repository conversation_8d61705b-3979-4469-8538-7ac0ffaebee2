/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';

export interface SimulationModelState {
  modelTime: string;
  calculationTime: string;
  strongConditionCount: number;
  strongConditionMissCount: number;
  state: string;
}

export interface SimulationModelDetail {
  otype: string;
  oname: string;
  value: number;
  dataMode: string;
  dataSampling: string;
  /** 累计时间；单位 秒 */
  duration: number;
  dataDetails: { time: string; value: string | number | null }[];
  otypeTitle: string;
  deviceTitle: string;
  isStrongCondition: boolean;
}

export interface ModelOption {
  modelId: string;
  modelName: string;
}

export interface SimulationLogItem extends ModelOption {
  states: SimulationModelState[];
}

export type LastSimulationInfo = ModelOption & SimulationModelState;

export interface SimulationLogChartData {
  modelTime: string;
  rate: number;
  delayTime: number;
  state: string;
}

export interface BarData {
  value: number;
  name: string;
}

export const getSimulationLogChartData = (
  data: SimulationModelState[],
): SimulationLogChartData[] =>
  data
    .map((state) => ({
      modelTime: state.modelTime,
      rate: Math.round(
        (state.strongConditionCount /
          (state.strongConditionCount + state.strongConditionMissCount)) *
          100,
      ),
      delayTime: dayjs(state.calculationTime).diff(
        dayjs(state.modelTime),
        'minute',
      ),
      state: state.state,
    }))
    .reverse();

export const getDelayChartData = (
  data: SimulationLogChartData[],
): BarData[] => {
  let value15 = 0;
  let value10 = 0;
  let value5 = 0;
  let value = 0;

  data.forEach(({ delayTime }) => {
    if (delayTime >= 0 && delayTime < 5) {
      value += 1;
    } else if (delayTime >= 5 && delayTime < 10) {
      value5 += 1;
    } else if (delayTime >= 10 && delayTime < 15) {
      value10 += 1;
    } else if (delayTime >= 15) {
      value15 += 1;
    }
  });
  return [
    { value, name: '准时' },
    { value: value5, name: '5~10分钟' },
    { value: value10, name: '10~15分钟' },
    { value: value15, name: '大于15分钟' },
  ];
};

export const getConditionChartData = (
  data: SimulationLogChartData[],
): BarData[] => {
  let value80 = 0;
  let value60 = 0;
  let value50 = 0;
  let value = 0;
  data.forEach(({ rate }) => {
    if (rate >= 0 && rate < 50) {
      value += 1;
    } else if (rate >= 50 && rate < 60) {
      value50 += 1;
    } else if (rate >= 60 && rate < 80) {
      value60 += 1;
    } else if (rate >= 80) {
      value80 += 1;
    }
  });
  return [
    { value: value80, name: '超过80%' },
    { value: value60, name: '60%~80%' },
    { value: value50, name: '50%~60%' },
    { value, name: '小于50%' },
  ];
};
