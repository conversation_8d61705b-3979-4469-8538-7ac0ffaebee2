/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { WDM_PUMPS, WDM_VALVES } from './const/system-const';
import Database from './database';
import { PumpInfo, StationDevice } from './device';
import { makeId } from './object-item';
import { getConvertPumpData } from './pump-status';
import { formatNumber } from './utils';

/** 运行观测指标项 */
export interface RunObservationIndicator {
  /** 对象类型 */
  otype: string;
  /** 对象名称 */
  oname: string;
  /** 属性值 */
  vprop: string;
  /** 是否显示预测 */
  showPrediction?: boolean;
}

/** 运行观测主观测项 */
export interface RunObservationMainItem {
  /** 属性类型 */
  ptype: string;
  /** 属性名称 */
  pname: string;
  /** 指标列表 */
  indicators: RunObservationIndicator[];
}

/** 运行观测配置项 */
export interface RunObservationConfigItem {
  /** 标题 */
  title: string;
  /** 对象类型 */
  otype: string;
  /** 对象名称 */
  oname: string;
  /** 属性值 */
  vprop: string;
  /** 预测属性值 */
  predictionVprop: string;
  /** 预测总流量属性值 */
  predictTotalFlowVprop: string;
  /** 主观测项列表 */
  mainObservation: RunObservationMainItem[];
}

interface OperationObjectInfo {
  otype: string;
  oname: string;
  preValue: number;
  curValue: number;
}

export interface ScheduleSimulationOperation {
  pumps: OperationObjectInfo[];
  pressure?: number | null;
  flow?: number | null;
  valves?: OperationObjectInfo[];
}

/** 智能调度策略项 */
export interface ScheduleSimulationItem {
  /** 操作时间 */
  otime: string;
  /** 模型ID */
  modelId: string;
  /** 厂站 otype */
  otype: string;
  /** 厂站 oname */
  oname: string;
  /** 操作内容 */
  operations: ScheduleSimulationOperation;
  /** 预测流量 */
  forecastFlow: number;
  /** 任务ID */
  taskId: string;
  /** 入库时间 */
  stime: string;
}

/** 智能调度策略列表查询参数 */
export interface ScheduleSimulationListParams {
  /** 任务ID */
  taskId?: string;
  /** 模型ID */
  modelId?: string;
  /** 操作时间开始 */
  otimeStart?: string;
  /** 操作时间结束 */
  otimeEnd?: string;
  /** 每页大小 */
  pageSize?: number;
  /** 页码 */
  pageNum?: number;
}

/** 智能调度策略列表响应数据 */
export interface ScheduleSimulationListData {
  /** 策略列表 */
  list: ScheduleSimulationItem[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 总页数 */
  pages?: number;
}

/** 最新任务信息 */
export interface LatestTaskInfo {
  /** 任务ID */
  taskId: string;
  /** 模型ID */
  modelId: string;
  /** 入库时间 */
  stime: string;
}

/** 最新任务信息列表响应数据 */
export interface LatestTaskInfoListData {
  /** 最新任务信息列表 */
  latestTaskInfoList: LatestTaskInfo[];
}

/** 最新调度策略详情响应数据 */
export interface LatestScheduleSimulationData {
  /** 最新任务信息列表 */
  latestTaskInfoList: LatestTaskInfo[];
  /** 策略总数 */
  total: number;
  /** 调度策略列表 */
  strategies: ScheduleSimulationItem[];
}

export type ScadaDataSourceItem = {
  title?: string;
  otype?: string;
  oname?: string;
  valueWithSymbol: string;
  otime?: string;
};

export function parseScheduleSimulationOperation(
  otype: string,
  oname: string,
  operations: string,
): ScheduleSimulationOperation {
  const operationData: Required<ScheduleSimulationOperation> = {
    pumps: [],
    valves: [],
    pressure: 0,
    flow: 0,
  };
  if (operations) {
    const operationsData = JSON.parse(operations);
    if (otype === WDM_VALVES) {
      if (
        typeof operationsData.prev === 'string' &&
        typeof operationsData.cur === 'string'
      ) {
        let preValue = Number(operationsData.prev);
        let curValue = Number(operationsData.cur);
        if (operationsData.prev === 'CLOSED') {
          preValue = 0;
        } else if (operationsData.prev === 'OPEN') {
          preValue = 100;
        }
        if (operationsData.cur === 'CLOSED') {
          curValue = 0;
        } else if (operationsData.cur === 'OPEN') {
          curValue = 100;
        }
        operationData.valves.push({
          otype: WDM_VALVES,
          oname,
          preValue,
          curValue,
        });
      }
    } else {
      if (operationsData.pumps) {
        operationData.pumps = operationsData.pumps.map(
          ({ id, prev, cur }: any) => ({
            otype: WDM_PUMPS,
            oname: id,
            preValue: prev,
            curValue: cur,
          }),
        );
      }
      operationData.pressure = operationsData.pressure;
      operationData.flow = operationsData.flow;
    }
  }
  return operationData;
}

export enum StrategyItemChangeType {
  PRESSURE = 'PRESSURE',
  FLOW = 'FLOW',
  PUMP_CLOSE = 'PUMP_CLOSE',
  PUMP_OPEN = 'PUMP_OPEN',
  PUMP_FREQUENCY = 'PUMP_FREQUENCY',
  VALVE_CLOSE = 'VALVE_CLOSE',
  VALVE_OPEN = 'VALVE_OPEN',
  /** 开度 */
  VALVE_OPENING = 'VALVE_OPENING',
}

export interface StrategyItemChangeValue {
  changeType: StrategyItemChangeType;
  contentText: string;
  originPumpValue?: OperationObjectInfo;
  originPressureValue?: number;
  originPressureWithPump?: (OperationObjectInfo & { pumpInfo?: PumpInfo })[];
  pumpInfo?: PumpInfo;
  originValveValue?: OperationObjectInfo;
}

/** 厂站策略信息项 */
export interface StrategyItem {
  id: string; // 时刻标识，如 "2024-01-15T09:00:00"
  time: string; // datetime string，用于时间判断和排序
  changes: StrategyItemChangeValue[];
  stationInfo?: StationDevice;
}

function parseStationStrategyOperations(
  db: Database,
  strategyOperations: ScheduleSimulationOperation,
  stationId: string,
): StrategyItemChangeValue[] {
  const { pumps, pressure } = strategyOperations;
  const station = db.getStationById(stationId);
  const changes: StrategyItemChangeValue[] = [];
  const originPressureWithPump: (OperationObjectInfo & {
    pumpInfo?: PumpInfo;
  })[] = [];
  pumps.forEach((item) => {
    const pumpInfo = station?.pumpList.find(
      (f) => f.otype === item.otype && f.oname === item.oname,
    );
    originPressureWithPump.push({
      ...item,
      pumpInfo,
    });
    if (pumpInfo) {
      const prePumpDataValue = getConvertPumpData(
        { value: item.preValue as unknown as string, otime: '' },
        pumpInfo,
      );
      const curPumpDataValue = getConvertPumpData(
        { value: item.curValue as unknown as string, otime: '' },
        pumpInfo,
      );
      // 判断这个pump状态变化是否调整开关还是调整频率，如果调整开关，则返回调整了开关信息，否则不返回
      if (prePumpDataValue.pumpSwitch !== curPumpDataValue.pumpSwitch) {
        changes.push({
          changeType: curPumpDataValue.pumpSwitch
            ? StrategyItemChangeType.PUMP_OPEN
            : StrategyItemChangeType.PUMP_CLOSE,
          contentText: `${pumpInfo.title}泵${curPumpDataValue.pumpSwitch ? '打开' : '关闭'}`,
          pumpInfo,
          originPumpValue: item,
        });
      }
    }
  });

  const unit = db.getUnitFormat('SDVAL_PRESS_W', 'SDVAL');
  const unitValue =
    unit?.getValueWithSymbol(pressure as number) ?? formatNumber(pressure, 2);
  changes.push({
    changeType: StrategyItemChangeType.PRESSURE,
    contentText: `压力 -> ${unitValue}`,
    originPressureValue: pressure as number,
    originPressureWithPump,
  });

  return changes;
}

function parseValveStrategyOperations(
  valveOperations: OperationObjectInfo[],
): StrategyItemChangeValue[] {
  const changes: StrategyItemChangeValue[] = [];
  valveOperations.forEach((item) => {
    const { oname, preValue, curValue } = item;
    if (
      curValue === 0 ||
      curValue === 100 ||
      preValue === 0 ||
      preValue === 100
    ) {
      // 开关阀操作
      const changeType =
        curValue === 0
          ? StrategyItemChangeType.VALVE_CLOSE
          : StrategyItemChangeType.VALVE_OPEN;

      changes.push({
        changeType,
        contentText: `阀门${oname} ${changeType === StrategyItemChangeType.VALVE_CLOSE ? '关闭' : '打开'}`,
        originValveValue: item,
      });
    } else {
      // 开度调整
      changes.push({
        changeType: StrategyItemChangeType.VALVE_OPENING,
        contentText: `阀门${oname}开度 ${preValue} -> ${curValue}`,
        originValveValue: item,
      });
    }
  });
  return changes;
}

export function getScheduleStragyData(
  strategies: ScheduleSimulationItem[],
  db: Database,
): {
  stragiesMap: Map<string, StrategyItem[]>;
  allTimes: string[];
} {
  // 1. 获取所有不同的时刻，并按时间排序
  const allTimes = Array.from(new Set(strategies.map((s) => s.otime))).sort(
    (a, b) => dayjs(a).diff(dayjs(b)),
  );

  // 2. 获取所有不同的厂站
  const allStations = db.getAllStations().map((m) => m.id);

  // 3. 创建结果 Map
  const result = new Map<string, StrategyItem[]>();

  // 4. 为每个厂站构建完整的策略信息列表
  allStations.forEach((stationId) => {
    const stationStrategies: StrategyItem[] = [];

    allTimes.forEach((otime) => {
      // 查找该厂站在该时刻的策略
      const strategy = strategies.find(
        (s) => s.otime === otime && `${makeId(s.otype, s.oname)}` === stationId,
      );

      if (strategy) {
        const { operations } = strategy;

        const changes = parseStationStrategyOperations(
          db,
          operations,
          stationId,
        );

        stationStrategies.push({
          id: otime,
          stationInfo: db.getStationById(stationId),
          changes,
          time: otime,
        });
      } else {
        stationStrategies.push({
          id: otime,
          changes: [],
          time: otime,
        });
      }
    });

    // 按时间排序（虽然 allTimes 已经排序了，但确保一致性）
    stationStrategies.sort((a, b) => dayjs(a.time).diff(dayjs(b.time)));

    result.set(stationId, stationStrategies);
  });

  const valveOperationsWithTimeMap: Map<string, OperationObjectInfo[]> =
    new Map();
  // 获取阀门的调度策略
  strategies.forEach((strategy) => {
    const { otype, operations } = strategy;
    const { valves = [] } = operations;

    if (otype === WDM_VALVES) {
      let valveOperations: OperationObjectInfo[] =
        valveOperationsWithTimeMap.get(strategy.otime) ?? [];
      valveOperations = [...valveOperations, ...valves];
      valveOperationsWithTimeMap.set(strategy.otime, valveOperations);
    }
  });

  const valveStrategies: StrategyItem[] = [];

  allTimes.forEach((otime) => {
    // 查找该厂站在该时刻的策略
    const valveOperationsWithTime = valveOperationsWithTimeMap.get(otime);

    if (valveOperationsWithTime) {
      valveStrategies.push({
        id: otime,
        changes: parseValveStrategyOperations(valveOperationsWithTime),
        time: otime,
      });
    } else {
      valveStrategies.push({
        id: otime,
        changes: [],
        time: otime,
      });
    }
  });

  // 按时间排序（虽然 allTimes 已经排序了，但确保一致性）
  valveStrategies.sort((a, b) => dayjs(a.time).diff(dayjs(b.time)));

  result.set(WDM_VALVES, valveStrategies);

  return {
    stragiesMap: result,
    allTimes,
  };
}

export function getChangeTooltips(
  changes: StrategyItemChangeValue[],
): string[] {
  const tooltipsContent: string[][] = changes.map((change) => {
    switch (change.changeType) {
      case StrategyItemChangeType.PRESSURE:
        if (change.originPressureWithPump) {
          return change.originPressureWithPump.map((m) => {
            const pumpName = m.pumpInfo?.title ?? m.oname;
            return `🔧 ${pumpName}频率 ${m.preValue} -> ${m.curValue}`;
          });
        }
        return ['🔧 压力调节操作'];
      case StrategyItemChangeType.FLOW:
        return ['💧 流量调节操作'];
      case StrategyItemChangeType.PUMP_CLOSE:
        if (change.originPumpValue) {
          const pumpName =
            change.pumpInfo?.title ?? change.originPumpValue.oname;
          return [
            `⛔ ${pumpName}频率 ${change.originPumpValue.preValue} -> ${change.originPumpValue.curValue} (敏感操作需手动确认)`,
          ];
        }
        return ['⛔ 泵关闭操作 - 敏感操作需手动确认'];
      case StrategyItemChangeType.PUMP_OPEN:
        if (change.originPumpValue) {
          const pumpName =
            change.pumpInfo?.title ?? change.originPumpValue.oname;
          return [
            `🟢 ${pumpName}频率 ${change.originPumpValue.preValue} -> ${change.originPumpValue.curValue} (敏感操作需手动确认)`,
          ];
        }
        return ['🟢 泵开启操作 - 敏感操作需手动确认'];
      case StrategyItemChangeType.VALVE_CLOSE:
        if (change.originValveValue) {
          return [
            `🔴 阀门${change.originValveValue.oname}开度 ${change.originValveValue.preValue} -> ${change.originValveValue.curValue}`,
          ];
        }
        return ['🔴 阀门关闭操作'];
      case StrategyItemChangeType.VALVE_OPEN:
        if (change.originValveValue) {
          return [
            `🟢 阀门${change.originValveValue.oname}开度 ${change.originValveValue.preValue} -> ${change.originValveValue.curValue}`,
          ];
        }
        return ['🟢 阀门开启操作'];
      case StrategyItemChangeType.VALVE_OPENING:
        if (change.originValveValue) {
          return [
            `🔧 阀门${change.originValveValue.oname}开度 ${change.originValveValue.preValue} -> ${change.originValveValue.curValue}`,
          ];
        }
        return ['🔧 阀门开度调节操作'];
      default:
        return ['🔧 其他操作'];
    }
  });
  return tooltipsContent.length > 0 ? tooltipsContent.flat() : [];
}
