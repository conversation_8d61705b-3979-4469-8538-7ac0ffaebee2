/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from './database';
import { DeviceAction, ScheduleMode } from './dispatch-command/create-command';
import { makeObjectId } from './object-item';
import { getDiff } from './scheduling-data';
import { TimeData } from './time-data';
import { formatNumber } from './utils';

export const VPROP_TITLE = 'TITLE';
export const VPROP_REALFLOW = 'REALFLOW';
export const VPROP_TM_FLOW = 'TM_FLOW';
export const VPROP_REALFLOW_FC = 'REALFLOW_FC1';
export const VPROP_ABSOLUTE_MEAN_ERROR = 'ABSOLUTE_MEAN_ERROR_FC';

export enum SmartSchedulingType {
  HISTORY = 'HISTORY',
  SCHEDULE = 'SCHEDULE',
  MODEL = 'MODEL',
}

export type SmartSchedulingColumnType = {
  label?: string;
  display?: boolean;
  index?: number;
  value: SmartSchedulingType;
};

export enum FactoryDisplayMode {
  /** 一起显示 */
  Together = 'TOGETHER',
  /** 分开显示 */
  Separate = 'SEPARATE',
}

export enum WidgetKey {
  /** 下一个调度策略 */
  NEXT_SCHEDULE_STRATEGY = 'NEXT_SCHEDULE_STRATEGY',
  /** 水量预测 */
  WATER_PREDICTION = 'WATER_PREDICTION',
  /** 监测点 */
  MONITORING_POINT = 'MONITORING_POINT',
  /** 分区水量 */
  ZONE_WATER_FLOW = 'ZONE_WATER_FLOW',
}

// 定义默认标题
const defaultTitles: Record<WidgetKey, string> = {
  [WidgetKey.NEXT_SCHEDULE_STRATEGY]: '下一个调度策略',
  [WidgetKey.WATER_PREDICTION]: '水量预测',
  [WidgetKey.MONITORING_POINT]: '监测点',
  [WidgetKey.ZONE_WATER_FLOW]: '分区水量',
};

export const getWidgetTitle = (key: WidgetKey): string => defaultTitles[key];

export interface ZoneWaterAmountWidgetData {
  otype: string;
  oname: string;
}

type WidgetDataMap = {
  [WidgetKey.NEXT_SCHEDULE_STRATEGY]: undefined;
  [WidgetKey.WATER_PREDICTION]: undefined;
  [WidgetKey.MONITORING_POINT]: undefined;
  [WidgetKey.ZONE_WATER_FLOW]: ZoneWaterAmountWidgetData[];
};

export interface WidgetConfig<K extends WidgetKey> {
  key: K;
  // 不填写时为默认标题
  title?: string;
  show: boolean;
  order: number;
  data: WidgetDataMap[K];
}

export const DEFAULT_WIDGET_CONFIG: WidgetConfig<WidgetKey>[] = [
  {
    key: WidgetKey.NEXT_SCHEDULE_STRATEGY,
    title: '下一个调度策略',
    show: true,
    order: 1,
    data: undefined,
  },
  {
    key: WidgetKey.WATER_PREDICTION,
    title: '水量预测',
    show: true,
    order: 2,
    data: undefined,
  },
  {
    key: WidgetKey.MONITORING_POINT,
    title: '监测点',
    show: true,
    order: 3,
    data: undefined,
  },
];

export enum SmartScheduleOperationType {
  /** 无需操作 */
  NO_OPREATION = 'NO_OPREATION',
  /** 无需操作 */
  NO_OPREATION_FROM_HISTORY = 'NO_OPREATION_FROM_HISTORY',
  /** 无需操作 */
  NO_OPREATION_FROM_CONTROL = 'NO_OPREATION_FROM_CONTROL',
  /** 无需操作 */
  NO_OPREATION_FROM_MODEL = 'NO_OPREATION_FROM_MODEL',
  OPREATION = 'OPREATION',
  /** 匹配不到模型 */
  UNABLE_SUGGEST_NO_MODEL = 'UNABLE_SUGGEST_NO_MODEL',
  /** 匹配不到历史 */
  UNABLE_SUGGEST_NO_HISTORY = 'UNABLE_SUGGEST_NO_HISTORY',
  /** 当前处于操作段 */
  UNABLE_SUGGEST_IN_DISPATCH = 'UNABLE_SUGGEST_IN_DISPATCH',
  /** 主控数据缺失 */
  UNABLE_SUGGEST_NO_DATA = 'UNABLE_SUGGEST_NO_DATA',
}

// 压力调整信息
export interface PressureAdjustmentInfo {
  otype: string;
  oname: string;
  description: string;
  mainControl: boolean;
  startTime: string;
  endTime: string;
  baseValue: number;
  firstValue?: number;
  changeValue?: number;
  secondValue?: number;
}

// 流量调整信息
export interface FlowAdjustmentInfo {
  otype: string;
  oname: string;
  description: string;
  mainControl: boolean;
  startTime: string;
  endTime: string;
  baseValue: number;
  firstValue?: number;
  changeValue?: number;
  secondValue?: number;
}

// 泵调整信息
export interface PumpAdjustmentInfo {
  otype: string;
  oname: string;
  description: string;
  mainControl: boolean;
  startTime: string;
  endTime: string;
  baseValue: number;
  firstValue?: number;
  changeValue?: number;
  secondValue?: number;
  frequencymode: boolean;
}

interface PumpInfo {
  otype: string;
  oname: string;
  startMinutes: number;
  endMinutes: number;
  startTime: string;
  endTime: string;
  value: number;
}

// 等效泵信息
export interface EquivalentPumpInfo {
  pressure: string;
  flow: string;
}

// 调度策略信息
export interface SchedulingStrategyInfo {
  otype: string;
  oname: string;
  startTime?: string;
  endTime?: string;
  pressureAdjustment?: PressureAdjustmentInfo[];
  flowAdjustment?: FlowAdjustmentInfo[];
  openEquivalentPump?: boolean;
  equivalentPumpInfo?: EquivalentPumpInfo;
  waterPump?: PumpAdjustmentInfo[];
  info?: PumpInfo[];
}

export interface FlowForecast {
  time: string;
  value: number;
}

export interface SchedulingOperateTimeData {
  name: string;
  startTime: string;
  endTime: string;
  stateDetail: {
    otype: string;
    oname: string;
    scadaName: string;
    value: string;
    originalValue: string | number;
    unit?: string;
    formatValue?: string | number;
    targetValue?: string | number;
    startTime?: string;
    endTime?: string;
    targetDiff?: string | number;
  }[];
  change?: boolean;
}

export interface HistoricalSchedulingSuggestionInfo {
  /** 推荐id */
  suggestId: string;
  /** 策略生成时间 */
  otime: string;
  startTime: string;
  endTime: string;
  timeAlign: string;
  strategyInfo: SchedulingStrategyInfo[];
  matchStartTime: string;
  matchEndTime: string;
  matchTime: string;
  matchTimeAlign: string;
  /** 匹配历史策略信息 */
  matchStrategyInfo: SchedulingStrategyInfo[];
  suggestStartTime: string;
  suggestEndTime: string;
  tmFlowFc: FlowForecast[];
  sumFlowFc: number;
  /** 匹配分数 */
  matchScore: number;
  remark: string | null;
  /** 调度推荐操作 */
  suggestDispatchInfo: SchedulingStrategyInfo[];
  suggestState: SmartScheduleOperationType;
  stime: string;
  /** 建议使用状态:-1:不可使用,0:忽略,1:可以使用 */
  suggestUseState: number;
  /** 建议采纳状态:0:未采纳,1:已采纳,2:采纳已结束 */
  suggestAcceptState: number;
}

export interface HistoricalSchedulingSuggestionListItem {
  // 前端生成
  id: string;
  otime: string;
  startTime: string;
  endTime: string;
  timeAlign: string;
  strategyInfo: SchedulingStrategyInfo[];
  details: HistoricalSchedulingSuggestionInfo[];
}

export interface ControlSchedulingSuggestionInfo {
  /** 推荐id */
  suggestId: string;
  /** 策略生成时间 */
  otime: string;
  triggerInfo: string;
  suggestState: SmartScheduleOperationType;
  /** 调度推荐操作 */
  suggestDispatchInfo: SchedulingStrategyInfo[];
  remark: string | null;
  stime: string;
}

export interface ModelInfo {
  fileName: string;
  userName: string;
  note: string;
}

export interface ModelSchedulingSuggestionInfo {
  /** 推荐id */
  suggestId: string;
  /** 策略生成时间 */
  otime: string;
  startTime: string;
  endTime: string;
  timeAlign: string;
  strategyInfo: SchedulingStrategyInfo[];
  matchModelId: string;
  matchModelInfo: ModelInfo | undefined;
  matchStartTime: string;
  matchEndTime: string;
  matchTime: string;
  matchTimeAlign: string;
  /** 匹配历史策略信息 */
  matchStrategyInfo: SchedulingStrategyInfo[];
  suggestStartTime: string;
  suggestEndTime: string;
  tmFlowFc: FlowForecast[];
  sumFlowFc: number;
  /** 匹配分数 */
  matchScore: number;
  remark: string | null;
  /** 调度推荐操作 */
  suggestDispatchInfo: SchedulingStrategyInfo[];
  suggestState: SmartScheduleOperationType;
  stime: string;
  /** 建议使用状态:-1:不可使用,0:忽略,1:可以使用 */
  suggestUseState: number;
  /** 建议采纳状态:0:未采纳,1:已采纳,2:采纳已结束 */
  suggestAcceptState: number;
}

export interface ModelSchedulingSuggestionListItem {
  // 前端生成
  id: string;
  otime: string;
  startTime: string;
  endTime: string;
  timeAlign: string;
  strategyInfo: SchedulingStrategyInfo[];
  details: ModelSchedulingSuggestionInfo[];
}

export function getSmartSchedulingTypeName(type: string): string {
  switch (type) {
    case SmartSchedulingType.HISTORY:
      return '根据历史经验';
    case SmartSchedulingType.SCHEDULE:
      return '根据调度规则';
    case SmartSchedulingType.MODEL:
      return '根据模型优化';
    default:
      return type;
  }
}

export function getSmartScheduleOperationTypeDesc(
  type: string,
): string | undefined {
  switch (type) {
    case SmartScheduleOperationType.NO_OPREATION:
    case SmartScheduleOperationType.NO_OPREATION_FROM_MODEL:
      return '无需操作';
    case SmartScheduleOperationType.NO_OPREATION_FROM_HISTORY:
      return '根据历史经验，当前无需操作';
    case SmartScheduleOperationType.NO_OPREATION_FROM_CONTROL:
      return '根据调度规则，当前无需操作';
    case SmartScheduleOperationType.UNABLE_SUGGEST_NO_HISTORY:
      return '当前工况在历史中不存在，无法推荐';
    case SmartScheduleOperationType.UNABLE_SUGGEST_IN_DISPATCH:
      return '当前处于调度操作期间，无法推荐';
    case SmartScheduleOperationType.UNABLE_SUGGEST_NO_MODEL:
      return '匹配不到模型，无法推荐';
    case SmartScheduleOperationType.UNABLE_SUGGEST_NO_DATA:
      return '当前主控数据缺失，无法推荐';
    default:
      return undefined;
  }
}

export function getMaxScoreSuggestionInfo(
  data: HistoricalSchedulingSuggestionInfo[],
): HistoricalSchedulingSuggestionInfo | undefined {
  if (data.length === 0) {
    return undefined;
  }

  return data.reduce((max, current) =>
    current.matchScore > max.matchScore ? current : max,
  );
}

function getOperateDataFromAdjustmentInfo(
  // otype@oname
  strategyInfoId: string,
  adjustmentInfo:
    | PressureAdjustmentInfo
    | FlowAdjustmentInfo
    | PumpAdjustmentInfo,
  db: Database,
): SchedulingOperateTimeData {
  const unitFormat = db.getUnitFormat(adjustmentInfo.otype, 'SDVAL');
  const indicator = db.getIndicator(adjustmentInfo.otype, adjustmentInfo.oname);
  const name = makeObjectId(adjustmentInfo.otype, adjustmentInfo.oname);

  const targetValue = adjustmentInfo.secondValue
    ? (unitFormat?.getValue(adjustmentInfo.secondValue) ?? '')
    : '';
  const originalValue = adjustmentInfo.firstValue
    ? (unitFormat?.getValue(adjustmentInfo.firstValue) ?? '')
    : '';

  return {
    name: makeObjectId(strategyInfoId, name),
    startTime: adjustmentInfo.startTime,
    endTime: adjustmentInfo.endTime,
    change: true,
    stateDetail: [
      {
        otype: adjustmentInfo.otype,
        oname: adjustmentInfo.oname,
        scadaName: indicator?.title ?? unitFormat?.unitTitle ?? '',
        formatValue: unitFormat?.getValue(adjustmentInfo.baseValue) ?? '',
        unit: unitFormat?.unitSymbol ?? '',
        value: `${unitFormat?.getValue(
          adjustmentInfo.firstValue ?? '',
        )}->${unitFormat?.getValue(adjustmentInfo.secondValue ?? '')}${unitFormat?.unitSymbol ?? ''}`,
        originalValue,
        targetValue,
        targetDiff: getDiff(
          adjustmentInfo.firstValue ?? 0,
          adjustmentInfo.secondValue ?? 0,
        ),
      },
    ],
  };
}

function getOperateDataFromAdjustmentPumpInfo(
  // otype@oname
  strategyInfoId: string,
  adjustmentInfo: PumpInfo,
  db: Database,
): SchedulingOperateTimeData {
  const unitFormat = db.getUnitFormat(adjustmentInfo.otype, 'SDVAL');
  const indicator = db.getIndicator(adjustmentInfo.otype, adjustmentInfo.oname);

  const name = makeObjectId(adjustmentInfo.otype, adjustmentInfo.oname);
  const targetValue = adjustmentInfo.value
    ? (unitFormat?.getValue(adjustmentInfo.value) ?? '')
    : '';
  return {
    name: makeObjectId(strategyInfoId, name),
    startTime: adjustmentInfo.startTime,
    endTime: adjustmentInfo.endTime,
    change: true,
    stateDetail: [
      {
        otype: adjustmentInfo.otype,
        oname: adjustmentInfo.oname,
        scadaName: indicator?.title ?? unitFormat?.unitTitle ?? '',
        formatValue: '',
        unit: unitFormat?.unitSymbol ?? '',
        value: `调整到${targetValue}${unitFormat?.unitSymbol ?? ''}`,
        originalValue: '',
        targetValue,
        targetDiff: 0,
      },
    ],
  };
}

export function getSuggestionOperateData(
  data: SchedulingStrategyInfo[],
  db: Database,
): SchedulingOperateTimeData[] {
  const dataList: SchedulingOperateTimeData[] = [];
  data.forEach((l) => {
    const strategyInfoId = makeObjectId(l.otype, l.oname);
    if (l.flowAdjustment?.length)
      l.flowAdjustment.forEach((item) =>
        dataList.push(
          getOperateDataFromAdjustmentInfo(strategyInfoId, item, db),
        ),
      );
    if (l.pressureAdjustment?.length)
      l.pressureAdjustment.forEach((item) =>
        dataList.push(
          getOperateDataFromAdjustmentInfo(strategyInfoId, item, db),
        ),
      );
    if (l.waterPump?.length) {
      l.waterPump.forEach((item) =>
        dataList.push(
          getOperateDataFromAdjustmentInfo(strategyInfoId, item, db),
        ),
      );
    }
    if (l.info?.length) {
      l.info.forEach((item) =>
        dataList.push(
          getOperateDataFromAdjustmentPumpInfo(strategyInfoId, item, db),
        ),
      );
    }
  });
  return dataList.sort(
    (a, b) => dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf(),
  );
}

export function convertKeyToScheduleMode(
  key: string,
):
  | keyof Pick<
      ScheduleMode,
      | DeviceAction.FLOW_ADJUSTMENT
      | DeviceAction.PRESSURE_ADJUSTMENT
      | DeviceAction.WATER_PUMP
    >
  | undefined {
  switch (key) {
    case 'pressureAdjustment':
      return DeviceAction.PRESSURE_ADJUSTMENT;
    case 'flowAdjustment':
      return DeviceAction.FLOW_ADJUSTMENT;
    case 'waterPump':
      return DeviceAction.WATER_PUMP;
    // todo 智能调度 非等效泵调整时发送指令如何发送
    // case 'info':
    //   return 'INFO';
    // 其他可能的键
    default:
      return undefined;
  }
}

export const calculateRelativeError = (
  observed: TimeData[],
  predicted: TimeData[],
): {
  relativeErrors: TimeData[];
  errors: TimeData[];
} => {
  const relativeErrors: TimeData[] = [];
  const errors: TimeData[] = [];

  // 取监测数组作为基准map查找
  const observeMap: Map<string, number> = new Map();
  observed.forEach((item) => {
    observeMap.set(item.time, item.value);
  });

  predicted.forEach((item) => {
    const predictedValue = item.value as number;
    const observedValue = observeMap.get(item.time);
    /**
     * 仅当predictedValue和observedValue 不为0、undefined、null时参与计算
     */
    if (predictedValue && observedValue) {
      // 计算误差
      const error = predictedValue - observedValue;
      errors.push({
        time: item.time,
        value: error,
      });

      // 计算相对误差，避免除以零
      const relativeError =
        observedValue !== 0
          ? (predictedValue - observedValue) / observedValue
          : 0;
      relativeErrors.push({
        time: item.time,
        value: formatNumber(relativeError, 3),
      });
    }
  });

  return {
    errors,
    relativeErrors,
  };
};

/**
 * 判断所有调度项的 startTime 是否都在当前时间后一小时内
 */
export function isAllStartTimeWithinOneHour(
  data: { startTime: string }[],
): boolean {
  const now = dayjs();
  const oneHourLater = now.add(1, 'hour');
  return data.every((item) => !dayjs(item.startTime).isAfter(oneHourLater));
}
