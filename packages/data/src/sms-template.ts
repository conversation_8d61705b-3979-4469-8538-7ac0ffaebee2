/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/** 短信模板通知用户 */
export interface SmsTemplateUser {
  /** 用户ID */
  id?: string;
  /** 用户名称 */
  name?: string;
  /** 用户手机号 */
  mobile?: string;
  /** 用户部门 */
  department?: string;
}

/** 短信模板 */
export interface SmsTemplate {
  /** 短信模板ID */
  id?: string;
  /** 短信模板类型 */
  type?: string;
  /** 短信模板名称 */
  name?: string;
  /** 短信模板内容 */
  content?: string;
  /** 短信模板通知用户 */
  users?: SmsTemplateUser[];
}
