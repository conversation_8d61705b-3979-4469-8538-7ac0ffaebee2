/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export enum UserSource {
  SYSTEM = 'system',
  NON_SYSTEM = 'non-system',
}

/** 消息来源
 * (Tips:此处只维护从前端发送短信的来源, 新增完请记得去数据库[messageConfig]内更新相关配置, 不然查询列表无法显示中文和下拉筛选)
 */
export enum MsgFrom {
  /** 突发爆管 */
  SUDDEN_BURST = 'SUDDEN_BURST',
  /** 系统运维 */
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
  /** 问题上报 */
  ISSUE_REPORT = 'ISSUE_REPORT',
  /** 停水通知 */
  WATER_SHUTOFF_MSG = 'WATER_SHUTOFF_MSG',
  /** 指令通知 */
  DISPATCH_COMMAND = 'DISPATCH_COMMAND',
}

export enum ChannelType {
  /** 短信 */
  SHMSG = 'SHMSG',
  /** 企微 */
  WECOM = 'WECOM',
}

export enum Importance {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}

export enum MsgStatus {
  WAIT = 'WAIT',
  STOP = 'STOP',
  ERROR = 'ERROR',
}

export const msgFromName: Record<MsgFrom, string> = {
  [MsgFrom.SUDDEN_BURST]: '突发爆管',
  [MsgFrom.SYSTEM_MAINTENANCE]: '系统运维',
  [MsgFrom.ISSUE_REPORT]: '问题上报',
  [MsgFrom.WATER_SHUTOFF_MSG]: '停水通知',
  [MsgFrom.DISPATCH_COMMAND]: '指令通知',
};

export const ChannelTypeName: Record<ChannelType, string> = {
  [ChannelType.SHMSG]: '短信',
  [ChannelType.WECOM]: '企业微信',
};

export const importanceName: Record<Importance, string> = {
  [Importance.HIGH]: '高',
  [Importance.MEDIUM]: '中',
  [Importance.LOW]: '低',
};

export const msgStatusName: Record<MsgStatus, string> = {
  [MsgStatus.WAIT]: '待发送',
  [MsgStatus.STOP]: '已发送',
  [MsgStatus.ERROR]: '发送失败',
};

export const channelTypeOptions = Object.values(ChannelType).map((value) => ({
  label: ChannelTypeName[value],
  value,
}));

export const importanceOptions = Object.values(Importance).map((value) => ({
  label: importanceName[value],
  value,
}));

export const msgStatusOptions = Object.values(MsgStatus).map((value) => ({
  label: msgStatusName[value],
  value,
}));

export interface SMSListParams {
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 消息类型 */
  channelType?: ChannelType;
  /** 重要程度 */
  importance?: Importance;
  /** 消息内容模糊查询 */
  content?: string;
  /** 备注(关联事件)模糊查询 */
  note?: string;
  /** 通知人模糊查询 */
  receiver?: string;
  /** 消息来源 */
  msgFrom?: MsgFrom;
}

export interface SMSList {
  msgId?: string; // 消息id
  msgFrom?: string; // 消息来源
  msgType?: string; // 消息类型
  msgText?: string; // 消息内容
  createTime?: string; // 创建时间
  note?: string; // 备注
  importance?: Importance; // 重要程度
  userName?: string; // 用户名
  msgHost?: string; // 消息发送商
  msgSendType?: string; // 消息发送方式
  msgSendChannel?: string; // 消息发送渠道
  msgStatus?: MsgStatus; // 发送状态
  sendTime?: string; // 发送时间
  errMsg?: string; // 错误原因
}

interface MsgSendJsonItem {
  msg_send_type?: string;
  msg_send_code?: string;
  msg_send_user?: string;
}

export interface SendSMSParams {
  /** 消息创建人 */
  msgCreator?: string;
  /** 消息来源 */
  msgFrom?: MsgFrom;
  /** 消息类型 */
  channelType?: ChannelType;
  /** 消息主要信息 */
  msgJson?: string;
  /** 消息内容 */
  msgText?: string;
  /** 重要程度 */
  importance?: Importance;
  /** 消息指定发送渠道列表 */
  msgSendJson?: MsgSendJsonItem[];
}

export const NO_WATER_MSG_TEMPLATE = `尊敬的用户:
您好!因()供水管道需要临时抢修，市政水务将于()停水维修，恢复通水时间预计为 ()。市政水务将竭力完成抢修工作，确保第一时间恢复供水，请提前作好储水准备。

温馨提示:
恢复供水后，请打开水龙头排水，直至水清后，再使用。如无受到停水影响，请忽略此短信。如有疑问，请拨打咨询电话:24小时值班电话()。谢谢!`;
