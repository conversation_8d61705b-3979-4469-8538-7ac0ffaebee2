/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import Database from './database';
import { makeObjectId } from './object-item';
import { SchedulingData } from './scheduling-data';
import { SolutionSource, SolutionStatus, SolutionType } from './solution';
import { TimeData } from './time-data';
import { formatNumber } from './utils';
import {
  VALVE_CLOSED,
  VALVE_OPEN,
  ValveStatus,
} from './valve-manager/valve-manager-data';

export enum SolutionModifyType {
  SPLIT = 'SPLIT',
  CREATE = 'CREATE',
  /** 修改 */
  VPROP = 'VPROP',
  DELETE = 'DELETE',
  REF_CREATE = 'REF_CREATE',
}
export interface SolutionModifyItem {
  id: string;
  time: string;
  type: SolutionModifyType;
  otype: string;
  oname: string;
  vprop: string;
  originValue?: string | number | null;
  value?: string | number | null;
  shape?: string;
  groupId?: string;
  groupTime?: string;
  isCurrent?: boolean;
}

export interface BaseDayInfo {
  date: string;
  totalFlow: number;
  modelScore: number;
  deviceScore: number;
}

export type ValueChangeMethod = 'add' | 'minus' | 'assign';
export const VALUE_ADD: ValueChangeMethod = 'add';
export const VALUE_MINUS: ValueChangeMethod = 'minus';
export const VALUE_ASSIGN: ValueChangeMethod = 'assign';

export type PlantSimulationMode = 'HEAD' | 'FLOW' | 'PUMP';
/** 压力 */
export const MODE_HEAD: PlantSimulationMode = 'HEAD';
/** 流量  */
export const MODE_FLOW: PlantSimulationMode = 'FLOW';
/** 开泵 */
export const MODE_PUMP: PlantSimulationMode = 'PUMP';

export interface ValueChangeSetting {
  method: ValueChangeMethod;
  value: number;
  startTime: string;
  endTime: string;
}

export interface PumpStationValueChangeSetting extends ValueChangeSetting {
  id: string;
  pattern: string;
}

export interface PlantValueChangeSetting extends ValueChangeSetting {
  pattern: string;
}

export interface SolutionFlowInfo {
  totalFlow: number;
  changes: ValueChangeSetting[];
  data: TimeData[];
}

export interface PlantChangeSetting {
  id: string;
  mode: PlantSimulationMode;
  valueChanges: PlantValueChangeSetting[];
}

export interface PumpStationChangeSetting {
  id: string;
  patterns: Map<string, ValueChangeSetting[]>;
}

export interface LinkStatusChangeSetting {
  id: string;
  time: string;
  status: ValveStatus;
  otype: string;
  shape?: string;
  openingValue?: number;
  statusType?: string;
}

export interface SimulationOptions {
  DemandMultiplier: number;
  DemandModel: 'FIXED' | 'CONSTRAINED' | 'POWER' | 'LOGISTIC';
  MinimumPressure: number;
  ServicePressure: number;
  PressureExponent: number;
  HydraulicTimestep: string;
  QualityTimestep: string;
  Quality: string;
  DurationTimeRange: [Dayjs, Dayjs];
}

export interface JunctionDemandChangeSetting {
  // oname
  id: string;
  demand: number;
  otype: string;
  shape?: string;
}

export interface JunctionQualityChangeSetting {
  // oname
  id: string;
  otype: string;
  shape?: string;
  startTime: string;
  endTime: string;
}

export type ObservationSetting = {
  // oname
  id: string;
  otype: string;
  shape?: string;
};

export interface QuerySolutionShareListParams {
  /** 方案id */
  solutionId: string;
}

export interface UpdateSolutionShareListParams {
  /** 方案id */
  solutionId?: string;
  /** 方案共享权限清单(json) */
  solutionShareJson?: {
    /** 共享部门 */
    department: string[];
    /** 共享用户 */
    user: string[];
  };
}

const yesterday = dayjs().add(-1, 'day');

const defaultSimulationOptions: SimulationOptions = {
  PressureExponent: 0.5,
  DemandMultiplier: 1,
  DemandModel: 'FIXED',
  MinimumPressure: 0,
  ServicePressure: 14,
  HydraulicTimestep: '1:00',
  QualityTimestep: '0:05',
  Quality: 'None',
  DurationTimeRange: [
    dayjs(yesterday.format('YYYY-MM-DD 00:00:00')),
    dayjs(yesterday.format('YYYY-MM-DD 23:59:00')),
  ],
};

const defaultDetail = {
  solutionType: SolutionType.COMMON,
  simulationStartTime: yesterday.format('YYYY-MM-DD 00:00:00'),
  simulationEndTime: yesterday.format('YYYY-MM-DD 23:59:00'),
  calculateStartTime: yesterday.format('YYYY-MM-DD 00:00:00'),
  calculateEndTime: yesterday.format('YYYY-MM-DD 23:59:00'),
  options: defaultSimulationOptions,
  flowInfo: { totalFlow: 0, changes: [], data: [] },
  baseDayInfo: {
    date: yesterday.format('YYYY-MM-DD'),
    totalFlow: 0,
    modelScore: 0,
    deviceScore: 0,
    junctionCount: 0,
    pipeCount: 0,
    closedValveCount: 0,
  },
};

export interface SolutionDetail {
  name?: string;
  modelId?: string;
  note?: string;
  solutionType: SolutionType;
  simulationStartTime: string;
  simulationEndTime: string;
  calculateStartTime: string;
  calculateEndTime: string;
  baseDayInfo: BaseDayInfo;
  flowInfo: SolutionFlowInfo;
  plantChangeSettings: Map<string, PlantChangeSetting>;
  pumpStationChangeSettings: Map<string, PumpStationChangeSetting>;
  linkStatusChangeSettings: LinkStatusChangeSetting[];
  options: SimulationOptions;
  demandChangeSettings: JunctionDemandChangeSetting[];
  qualityChangeSettings: JunctionQualityChangeSetting[];
  observationSettings: ObservationSetting[];
}

interface JunctionQualityChangeSettingJson {
  id: string;
  otype: string;
  shape?: string;
  start_time: string;
  end_time: string;
}
interface TimeDataJSON {
  [index: string]: number;
}
interface ValueChangesJson {
  value: number;
  method: string;
  start_time: string;
  end_time: string;
}
interface PumpStationChangeSettingsJson {
  id: string;
  patterns: {
    pattern: string;
    changes: ValueChangesJson[];
  }[];
}

interface PlantChangeSettingsJson {
  id: string;
  mode: PlantSimulationMode;
  patterns: {
    pattern: string;
    changes: ValueChangesJson[];
  }[];
}
interface LinkStatusChangeSettingJson {
  time: string;
  oname: string;
  value: ValveStatus;
  otype: string;
  shape?: string;
  openingValue?: number;
  statusType?: string;
}

export interface SolutionDetailJson {
  solution_type: string;
  name?: string;
  model_id?: string;
  note?: string;
  simulation_start_time: string;
  simulation_end_time: string;
  calculate_start_time: string;
  calculate_end_time: string;
  base_day: {
    date: string;
    total_flow: number;
    model_score: number;
    device_score: number;
  };
  demand: {
    total_flow: number;
    changes: ValueChangesJson[];
    data: TimeDataJSON[];
  };
  plants: PlantChangeSettingsJson[];
  pump_stations: PumpStationChangeSettingsJson[];
  link_state: LinkStatusChangeSettingJson[];
  options: {
    PRESSURE_EXPONENT: number;
    'Demand Multiplier': number;
    DEMAND_MODEL: string;
    MINIMUM_PRESSURE: number;
    SERVICE_PRESSURE: number;
  };
  times: {
    'Hydraulic Timestep': string;
    'Quality Timestep': string;
  };
  junction_demand: JunctionDemandChangeSetting[];
  junction_quality: JunctionQualityChangeSettingJson[];
  observations: ObservationSetting[];
}

function junctionQualityChangeSettingToJson(
  data: JunctionQualityChangeSetting[],
): JunctionQualityChangeSettingJson[] {
  return data.map(({ id, shape, otype, startTime, endTime }) => ({
    id,
    otype,
    shape,
    start_time: startTime,
    end_time: endTime,
  }));
}

function timeDataToJSON(data: TimeData[]): TimeDataJSON[] {
  const result: TimeDataJSON[] = [];
  data.forEach((d) => {
    result.push({ [d.time]: d.value });
  });

  return result;
}

function valueChangesToJson(data: ValueChangeSetting[]): ValueChangesJson[] {
  const result: ValueChangesJson[] = [];
  data.forEach((d) => {
    result.push({
      value: d.method === VALUE_MINUS ? -d.value : d.value,
      method: d.method === VALUE_ASSIGN ? 'fixed' : 'diff',
      start_time: d.startTime,
      end_time: d.endTime,
    });
  });

  return result;
}

function linkStatusChangeSettingToJson(
  data: LinkStatusChangeSetting[],
): LinkStatusChangeSettingJson[] {
  const result: LinkStatusChangeSettingJson[] = [];

  data.forEach((d) => {
    result.push({
      time: d.time,
      oname: d.id,
      value: d.status,
      otype: d.otype,
      shape: d.shape,
      openingValue: d.openingValue,
      statusType: d.statusType,
    });
  });
  return result;
}

function pumpStationChangeSettingsToJson(
  data: Map<string, PumpStationChangeSetting>,
): PumpStationChangeSettingsJson[] {
  const result: PumpStationChangeSettingsJson[] = [];
  data.forEach((d) => {
    const patterns: {
      pattern: string;
      changes: ValueChangesJson[];
    }[] = [];
    d.patterns.forEach((item, key) => {
      const changes: ValueChangesJson[] = [];
      item.forEach((change) => {
        changes.push({
          value: change.method === 'minus' ? -change.value : change.value,
          method: change.method === 'assign' ? 'fixed' : 'diff',
          start_time: change.startTime,
          end_time: change.endTime,
        });
      });
      patterns.push({ pattern: key, changes });
    });
    result.push({ id: d.id, patterns });
  });
  return result;
}

function plantChangeSettingsToJson(
  data: Map<string, PlantChangeSetting>,
): PlantChangeSettingsJson[] {
  const result: PlantChangeSettingsJson[] = [];
  data.forEach((d) => {
    const patternChanges: Map<string, PlantValueChangeSetting[]> = new Map();
    d.valueChanges.forEach((change) => {
      if (patternChanges.has(change.pattern))
        patternChanges.get(change.pattern)?.push(change);
      else patternChanges.set(change.pattern, [change]);
    });

    const patterns: {
      pattern: string;
      changes: ValueChangesJson[];
    }[] = [];
    patternChanges.forEach((item, key) => {
      const changes: ValueChangesJson[] = [];
      item.forEach((change) => {
        changes.push({
          value: change.method === 'minus' ? -change.value : change.value,
          method: change.method === 'assign' ? 'fixed' : 'diff',
          start_time: change.startTime,
          end_time: change.endTime,
        });
      });
      patterns.push({ pattern: key, changes });
    });

    result.push({ id: d.id, mode: d.mode, patterns });
  });
  return result;
}

function parseValueChanges(valueChange: any): ValueChangeSetting {
  let method;
  let { value }: { value: number } = valueChange;
  if (valueChange.method === 'fixed') method = VALUE_ASSIGN;
  else if (value > 0) method = VALUE_ADD;
  else {
    method = VALUE_MINUS;
    value = -value;
  }
  return {
    method,
    value,
    startTime: valueChange.start_time,
    endTime: valueChange.end_time,
  };
}

export function toRequestJson(
  solutionDetail: SolutionDetail,
): SolutionDetailJson {
  return {
    solution_type: solutionDetail.solutionType,
    name: solutionDetail.name,
    model_id: solutionDetail.modelId,
    note: solutionDetail.note,
    simulation_start_time: dayjs(solutionDetail.baseDayInfo.date).format(
      'YYYY-MM-DD 00:00:00',
    ),
    simulation_end_time: dayjs(solutionDetail.baseDayInfo.date).format(
      'YYYY-MM-DD 23:59:59',
    ),
    calculate_start_time: solutionDetail.calculateStartTime,
    calculate_end_time: solutionDetail.calculateEndTime,
    base_day: {
      date: solutionDetail.baseDayInfo.date,
      total_flow: solutionDetail.baseDayInfo.totalFlow,
      model_score: solutionDetail.baseDayInfo.modelScore,
      device_score: solutionDetail.baseDayInfo.deviceScore,
    },
    demand: {
      total_flow: solutionDetail.flowInfo.totalFlow,
      changes: valueChangesToJson(solutionDetail.flowInfo.changes),
      data: timeDataToJSON(solutionDetail.flowInfo.data),
    },
    plants: plantChangeSettingsToJson(solutionDetail.plantChangeSettings),
    pump_stations: pumpStationChangeSettingsToJson(
      solutionDetail.pumpStationChangeSettings,
    ),
    link_state: linkStatusChangeSettingToJson(
      solutionDetail.linkStatusChangeSettings,
    ),
    options: {
      PRESSURE_EXPONENT: solutionDetail.options.PressureExponent,
      'Demand Multiplier': solutionDetail.options.DemandMultiplier,
      DEMAND_MODEL: solutionDetail.options.DemandModel,
      MINIMUM_PRESSURE: solutionDetail.options.MinimumPressure,
      SERVICE_PRESSURE: solutionDetail.options.ServicePressure,
    },
    times: {
      'Hydraulic Timestep': solutionDetail.options.HydraulicTimestep,
      'Quality Timestep': solutionDetail.options.QualityTimestep,
    },
    junction_demand: solutionDetail.demandChangeSettings ?? [],
    junction_quality: junctionQualityChangeSettingToJson(
      solutionDetail.qualityChangeSettings ?? [],
    ),
    observations: solutionDetail.observationSettings ?? [],
  };
}

export function timeToMinutes(time: string): number {
  const times = time.trim().split(':');
  if (times.length === 2 && times[0].length > 0 && times[1].length > 0) {
    const hours: number = Number(times[0]);
    const minutes: number = Number(times[1]);
    return hours * 60 + minutes;
  }

  return 0;
}

export function timeRangeAsHour(startTime: string, endTime: string): number {
  const start = timeToMinutes(startTime);
  const end = timeToMinutes(endTime);
  return (end - start) / 60;
}

export function flowToTotalFlow(
  startTime: string,
  endTime: string,
  value: number,
): number {
  const hours = timeRangeAsHour(startTime, endTime);
  const decimalPlaces = value.toPrecision().split('.')[1]?.length;
  const totalValue = (value * hours) / 10000;
  return decimalPlaces ? formatNumber(totalValue, decimalPlaces) : totalValue;
}

function updateTimeData(
  timeData: TimeData[],
  valueChange: ValueChangeSetting,
): TimeData[] {
  if (timeData.length === 0) return [];
  const startMinutes = timeToMinutes(valueChange.startTime);
  const endMinutes = timeToMinutes(valueChange.endTime);
  const startTime = dayjs(timeData[0].time)
    .startOf('day')
    .add(startMinutes, 'minute');
  const endTime = dayjs(timeData[0].time)
    .startOf('day')
    .add(endMinutes, 'minute');
  return timeData.map((item) => {
    const time = dayjs(item.time);
    if (
      time.isBefore(startTime) ||
      time.isSame(endTime) ||
      time.isAfter(endTime)
    )
      return item;

    let { value } = item;
    switch (valueChange.method) {
      case 'add':
        value += valueChange.value;
        break;
      case 'minus':
        value -= valueChange.value;
        break;
      case 'assign':
        value = valueChange.value;
        break;
      default:
        break;
    }

    return {
      time: item.time,
      value,
    };
  });
}

export function getChangedTimeData(
  timeData: TimeData[],
  valueChanges: ValueChangeSetting[],
): TimeData[] {
  let newTimeData: TimeData[] = timeData;
  valueChanges.forEach((valueChange) => {
    newTimeData = updateTimeData(newTimeData, valueChange);
  });

  return newTimeData;
}

export function getSolutionDetail(initialData?: any): SolutionDetail {
  const plantChangeSettings: Map<string, PlantChangeSetting> = new Map();
  const pumpStationChangeSettings: Map<string, PumpStationChangeSetting> =
    new Map();
  const linkStatusChangeSettings: LinkStatusChangeSetting[] = [];
  const demandChangeSettings: JunctionDemandChangeSetting[] = [];
  const qualityChangeSettings: JunctionQualityChangeSetting[] = [];
  const observationSettings: ObservationSetting[] = [];

  if (typeof initialData === 'undefined') {
    return {
      ...defaultDetail,
      plantChangeSettings,
      pumpStationChangeSettings,
      linkStatusChangeSettings,
      demandChangeSettings,
      qualityChangeSettings,
      observationSettings,
    };
  }

  const flowInfoChanges: ValueChangeSetting[] =
    initialData?.demand?.changes?.map(
      (item: any): ValueChangeSetting => parseValueChanges(item),
    ) ?? [];
  const flowInfoData: TimeData[] =
    initialData?.demand?.data?.map((item: any) => {
      const [time, value] = Object.entries(item)[0];
      return {
        time,
        value,
      };
    }) ?? [];
  // linkStatus
  initialData?.link_state?.forEach((linkState: any) => {
    linkStatusChangeSettings.push({
      id: linkState.oname,
      time: linkState.time,
      status: linkState.value,
      otype: linkState.otype,
      shape: linkState.shape,
      openingValue: linkState.openingValue,
      statusType: linkState.statusType,
    });
  });

  // plants
  initialData?.plants?.forEach((plant: any) => {
    const { id, mode, patterns } = plant;
    const valueChanges: PlantValueChangeSetting[] = [];
    patterns?.forEach((patternItem: any) => {
      const { pattern, changes } = patternItem;
      changes?.forEach((item: any) => {
        const valueChange = parseValueChanges(item);
        valueChanges.push({
          ...valueChange,
          pattern,
        });
      });
    });
    const plantChangeSetting = {
      id,
      mode,
      valueChanges,
    };
    plantChangeSettings.set(id, plantChangeSetting);
  });

  // pump
  initialData?.pump_stations?.forEach((pumpStation: any) => {
    const { id, patterns } = pumpStation;
    const patternsMap: Map<string, ValueChangeSetting[]> = new Map();
    patterns?.forEach((patternItem: any) => {
      const { pattern, changes } = patternItem;
      const valueChanges: ValueChangeSetting[] = [];
      changes?.forEach((item: any) => {
        const valueChange = parseValueChanges(item);
        valueChanges.push(valueChange);
      });
      patternsMap.set(pattern, valueChanges);
    });
    const pumpChangeSetting = {
      id,
      patterns: patternsMap,
    };
    pumpStationChangeSettings.set(id, pumpChangeSetting);
  });

  // junction demand
  initialData?.junction_demand?.forEach((change: any) => {
    demandChangeSettings.push({
      id: change.id,
      demand: change.demand,
      otype: change.otype,
      shape: change.shape,
    });
  });

  // junction quality
  initialData?.junction_quality?.forEach((change: any) => {
    qualityChangeSettings.push({
      id: change.id,
      otype: change.otype,
      shape: change.shape,
      startTime: change.start_time,
      endTime: change.end_time,
    });
  });

  // observation
  initialData?.observations?.forEach((observation: any) => {
    observationSettings.push({
      id: observation.id,
      otype: observation.otype,
      shape: observation.shape,
    });
  });

  return {
    solutionType: initialData.solutionType ?? SolutionType.COMMON,
    name: initialData.name,
    modelId: initialData.model_id,
    note: initialData.note,
    simulationStartTime: initialData.simulation_start_time,
    simulationEndTime: initialData?.simulation_end_time,
    calculateStartTime: initialData.calculate_start_time,
    calculateEndTime: initialData?.calculate_end_time,
    baseDayInfo: {
      date: initialData.base_day?.date,
      totalFlow: initialData.base_day?.total_flow,
      modelScore: initialData.base_day?.model_score,
      deviceScore: initialData.base_day?.device_score,
    },
    flowInfo: {
      totalFlow: initialData.demand?.total_flow,
      changes: flowInfoChanges,
      data: flowInfoData,
    },
    options: {
      DemandModel:
        initialData.options?.DEMAND_MODEL ??
        defaultSimulationOptions.DemandModel,
      HydraulicTimestep: initialData.times?.['Hydraulic Timestep'],
      QualityTimestep: initialData.times?.['Quality Timestep'],
      MinimumPressure: initialData.options?.MINIMUM_PRESSURE,
      DemandMultiplier: initialData.options?.['Demand Multiplier'],
      ServicePressure: initialData.options?.SERVICE_PRESSURE,
      PressureExponent: initialData.options?.PRESSURE_EXPONENT,
      Quality: defaultSimulationOptions.Quality,
      DurationTimeRange: [
        dayjs(initialData.calculate_start_time),
        dayjs(initialData.calculate_end_time),
      ],
    },
    plantChangeSettings,
    pumpStationChangeSettings,
    linkStatusChangeSettings,
    demandChangeSettings,
    qualityChangeSettings,
    observationSettings,
  };
}

export function getUpdatedPumpStationChanges(
  changes: PumpStationValueChangeSetting[],
): Map<string, PumpStationChangeSetting> {
  const pumpStationChangeSettings: Map<string, PumpStationChangeSetting> =
    new Map();
  changes.forEach((item) => {
    const valueChange: ValueChangeSetting = {
      method: item.method,
      value: item.value,
      startTime: item.startTime,
      endTime: item.endTime,
    };

    const pumpStation: PumpStationChangeSetting | undefined =
      pumpStationChangeSettings.get(item.id);
    if (pumpStation === undefined) {
      const patterns: Map<string, ValueChangeSetting[]> = new Map();
      patterns.set(item.pattern, [valueChange]);
      pumpStationChangeSettings.set(item.id, {
        id: item.id,
        patterns,
      });
    } else if (pumpStation.patterns.has(item.pattern))
      pumpStation.patterns.get(item.pattern)?.push(valueChange);
    else {
      pumpStation.patterns.set(item.pattern, [valueChange]);
    }
  });

  return pumpStationChangeSettings;
}

export const ValueChangeOptions = [
  { label: '增加', value: VALUE_ADD },
  { label: '减少', value: VALUE_MINUS },
  { label: '等于', value: VALUE_ASSIGN },
];

export const FixedValueChangeOptions = [{ label: '等于', value: VALUE_ASSIGN }];

export type SolutionBaseInfo = {
  name: string;
  startTime: string;
  endTime: string;
  errorMessage?: string;
  status: SolutionStatus;
  statusMessage: string;
  isShared: boolean;
  note?: string;
  sourceType: SolutionSource;
  createByMyself: boolean;
  solutionType: SolutionType;
  creator: string;
  /** 计算任务ID */
  calcId?: string;
  /** 计算完成时间 */
  calcTime?: string;
  solutionGuid: string;
};

export type SolutionInfo = {
  baseInfo: SolutionBaseInfo & { id: string };
  detail: SolutionDetail;
};

export function getBaseSolutionInfoData(initialInfo: any): SolutionBaseInfo {
  return {
    name: initialInfo?.solution_title,
    startTime: initialInfo?.start_time,
    endTime: initialInfo?.end_time,
    errorMessage: initialInfo?.solution_error,
    status: initialInfo?.solution_status,
    statusMessage: initialInfo?.solution_status_msg,
    isShared: initialInfo?.solution_share === '1',
    note: initialInfo?.solution_note,
    sourceType: initialInfo?.solution_from,
    createByMyself: initialInfo?.is_belong ?? false,
    creator: initialInfo?.creator ?? '',
    solutionType: initialInfo?.solution_type ?? SolutionType.COMMON,
    calcId: initialInfo?.calc_id,
    calcTime: initialInfo?.calc_time,
    solutionGuid: initialInfo?.solution_guid,
  };
}

export function getSumFlow(timeData: TimeData[]): number {
  if (timeData.length === 0) return 0;
  const date = dayjs(timeData[0].time).startOf('day');
  let sum = 0;
  if (date.isBefore(dayjs(timeData[0].time))) {
    const diff = dayjs(timeData[0].time).diff(date, 'minutes');
    sum += (diff / 60) * timeData[0].value;
  }

  for (let i = 0; i < timeData.length - 1; i += 1) {
    const diff = dayjs(timeData[i + 1].time).diff(timeData[i].time, 'minutes');
    sum += (diff / 60) * timeData[i].value;
  }

  const diff = date
    .add(1, 'day')
    .diff(dayjs(timeData[timeData.length - 1].time), 'minutes');
  if (diff > 0) sum += (diff / 60) * timeData[timeData.length - 1].value;

  return sum;
}

export function getSumFlowText(timeData: TimeData[]): string {
  const sumFlow = getSumFlow(timeData);
  return `${(sumFlow / 10000).toFixed(2)}万吨`;
}

function getMethodDescriptionName(method: ValueChangeMethod): string {
  switch (method) {
    case VALUE_ADD:
      return '增加';
    case VALUE_ASSIGN:
      return '设置为';
    case VALUE_MINUS:
      return '减少';
    default:
      return method;
  }
}

function getModeDescriptionName(mode: PlantSimulationMode): string {
  switch (mode) {
    case MODE_HEAD:
      return '压力';
    case MODE_FLOW:
      return '流量';
    case MODE_PUMP:
      return '开泵';
    default:
      return mode;
  }
}

export function getValveStatusName(status: ValveStatus): string | number {
  switch (status) {
    case VALVE_OPEN:
      return '打开';
    case VALVE_CLOSED:
      return '关闭';
    default:
      return status;
  }
}

function getWaterValueDescription(
  waterValueChangeSetting: ValueChangeSetting,
): string | number {
  const { value, startTime, endTime } = waterValueChangeSetting;
  const diffHours = (timeToMinutes(endTime) - timeToMinutes(startTime)) / 60;
  return formatNumber((diffHours * value) / 10000, 2);
}

function getPumpPattenTypeUnit(
  mode: 'HEAD' | 'FLOW' | 'FIXED' | 'VARIABLE' | undefined,
): string {
  switch (mode) {
    case 'HEAD':
      return '米';
    case 'FLOW':
      return '万吨';
    case 'VARIABLE':
      return 'Hz';
    default:
      return '';
  }
}

function getPumpTypeByPattenType(
  mode: 'HEAD' | 'FLOW' | 'FIXED' | 'VARIABLE' | undefined,
): string {
  switch (mode) {
    case 'VARIABLE':
      return '频率';
    case 'HEAD':
    case 'FLOW':
    default:
      return '';
  }
}

export type PlantModeDescription = { plantName: string; modeName: string };

export function getPlantsModeList(
  plants: SolutionDetail['plantChangeSettings'],
  pumpListInfo: SchedulingData,
): PlantModeDescription[] {
  const plantsModeList: PlantModeDescription[] = [];
  plants?.forEach((plant) => {
    const { id, mode } = plant;
    const plantInfo = pumpListInfo.getPlant(id);
    const modeName = getModeDescriptionName(mode);
    const plantName = plantInfo?.title ?? id;
    plantsModeList.push({
      plantName,
      modeName,
    });
  });
  return plantsModeList;
}

function formatValueByPatternType(
  valueChange: ValueChangeSetting,
  patternType: 'HEAD' | 'FLOW' | 'FIXED' | 'VARIABLE' | undefined,
): number | string {
  const { value, startTime, endTime } = valueChange;
  switch (patternType) {
    case 'FIXED':
      return value ? '开' : '关';
    case 'VARIABLE':
      return formatNumber(value * 50, 2);
    case 'FLOW':
      return formatNumber(flowToTotalFlow(startTime, endTime, value), 2);
    case 'HEAD':
      return formatNumber(value, 2);
    default:
      return value;
  }
}

export function getSolutionWaterChangesDescription(
  water: ValueChangeSetting[],
): string[] {
  const description: string[] = [];
  water?.forEach((change) => {
    const { method, startTime, endTime } = change;
    const text = `总水量在${startTime}~${endTime}${getMethodDescriptionName(
      method,
    )} ${getWaterValueDescription(change)}万吨`;
    description.push(text);
  });
  return description;
}

export function getSolutionPlantsChangesDescription(
  plants: SolutionDetail['plantChangeSettings'],
  pumpListInfo: SchedulingData | undefined,
): {
  id: string;
  text: string;
}[] {
  const description: { id: string; text: string }[] = [];
  plants?.forEach((plant) => {
    const { id, valueChanges } = plant;
    const plantInfo = pumpListInfo?.getPlant(id);
    const plantName = plantInfo?.title ?? id;

    valueChanges?.forEach((change) => {
      const { method, startTime, endTime, pattern } = change;
      const pattenType = plantInfo?.getPatternType(pattern);
      const pattenName = `${plantInfo?.getPatternTitle(pattern)}${
        pattenType === 'VARIABLE' || pattenType === 'FIXED' ? '水泵' : ''
      }`;
      const pattenTypeName = getPumpTypeByPattenType(pattenType);
      const text = `${plantName}的${pattenName}${
        pattenTypeName ? `的${pattenTypeName}` : ''
      }在${startTime}~${endTime}${getMethodDescriptionName(
        method,
      )} ${formatValueByPatternType(change, pattenType)}${getPumpPattenTypeUnit(
        pattenType,
      )}`;
      description.push({ id, text });
    });
  });
  return description;
}

export function getSolutionPumpChangesDescription(
  pump: SolutionDetail['pumpStationChangeSettings'],
  pumpListInfo: SchedulingData | undefined,
): {
  id: string;
  text: string;
}[] {
  const description: {
    id: string;
    text: string;
  }[] = [];
  pump?.forEach((pump) => {
    const { id, patterns } = pump;
    const pumpInfo = pumpListInfo?.getPumpStation(id);
    const pumpName = pumpInfo?.title ?? id;
    [...patterns.entries()].forEach((pattenChange) => {
      const [pattern, changes] = pattenChange;
      const pattenType = pumpInfo?.getPatternType(pattern);
      const pattenName = `${pumpInfo?.getPatternTitle(pattern)}${
        pattenType === 'VARIABLE' || pattenType === 'FIXED' ? '水泵' : ''
      }`;
      const pattenTypeName = getPumpTypeByPattenType(pattenType);
      changes.forEach((change) => {
        const { method, startTime, endTime } = change;
        const text = `${pumpName}的${pattenName}${
          pattenTypeName ? `的${pattenTypeName}` : ''
        }在${startTime}~${endTime}${getMethodDescriptionName(
          method,
        )} ${formatValueByPatternType(
          change,
          pattenType,
        )}${getPumpPattenTypeUnit(pattenType)}`;
        description.push({ id, text });
      });
    });
  });
  return description;
}

export function getSolutionValvesChangesDescription(
  valves: SolutionDetail['linkStatusChangeSettings'],
) {
  const description: string[] = [];
  valves?.forEach((valve) => {
    const { id, time, status } = valve;
    const valveStatusName = getValveStatusName(status);
    const text = `${id}在${time}时${
      typeof valveStatusName === 'number'
        ? `设置为${valveStatusName}`
        : valveStatusName
    }`;
    description.push(text);
  });
  return description;
}

export function getSolutionDemandChangeDescription(
  changes: JunctionDemandChangeSetting[],
): {
  id: string;
  text: string;
}[] {
  const description: {
    id: string;
    text: string;
  }[] = [];
  changes.forEach((item) => {
    const { id, demand } = item;
    description.push({
      id,
      text: `节点${id}的水量${demand > 0 ? '增加' : '减少'}${Math.abs(
        demand,
      )}吨/小时`,
    });
  });
  return description;
}

export function getSolutionQualityChangeDescription(
  changes: JunctionQualityChangeSetting[],
): {
  id: string;
  text: string;
}[] {
  const description: {
    id: string;
    text: string;
  }[] = [];
  changes.forEach((item) => {
    const { id, startTime, endTime } = item;
    description.push({
      id,
      text: `污染物${id}在${dayjs(startTime).format('HH:mm')} -
      ${dayjs(endTime).format('HH:mm')}发生`,
    });
  });
  return description;
}

export function getSolutionObservationsDescription(
  changes: ObservationSetting[],
): {
  id: string;
  text: string;
}[] {
  const description: {
    id: string;
    text: string;
  }[] = [];
  changes.forEach((item) => {
    const { id, otype } = item;
    description.push({
      id: makeObjectId(otype, id),
      text: id,
    });
  });
  return description;
}

export function getSolutionChangesDescription(
  changes: {
    water: ValueChangeSetting[];
    plants: SolutionDetail['plantChangeSettings'];
    pump: SolutionDetail['pumpStationChangeSettings'];
    valves: SolutionDetail['linkStatusChangeSettings'];
  },
  pumpListInfo: SchedulingData,
): string[] {
  const { water, plants, pump, valves } = changes;
  const changesDescription: string[] = [];

  getSolutionWaterChangesDescription(water).forEach((item) =>
    changesDescription.push(item),
  );

  getSolutionPlantsChangesDescription(plants, pumpListInfo).forEach((item) =>
    changesDescription.push(item.text),
  );

  getSolutionPumpChangesDescription(pump, pumpListInfo).forEach((item) =>
    changesDescription.push(item.text),
  );

  getSolutionValvesChangesDescription(valves).forEach((item) =>
    changesDescription.push(item),
  );

  return changesDescription;
}

export function getModifyTypeText(modifyType: SolutionModifyType): string {
  switch (modifyType) {
    case SolutionModifyType.CREATE:
      return '增加';
    case SolutionModifyType.SPLIT:
      return '打断';
    case SolutionModifyType.VPROP:
      return '修改';
    case SolutionModifyType.DELETE:
      return '删除';
    default:
      return modifyType;
  }
}

export function getModifyChangeDescription(
  changes: SolutionModifyItem[],
  db: Database,
): {
  id: string;
  text: string;
}[] {
  const description: {
    id: string;
    text: string;
  }[] = [];
  changes.forEach((item) => {
    const { type, time, otype, oname, id, vprop, originValue, value } = item;
    const modifyTypeText = getModifyTypeText(type);
    const info = db.getPropertyInfo(otype);
    const otypeTitle = info?.title ?? otype;
    const objectName = `${otypeTitle}${oname}`;
    const vpropTitle = info?.getPropertyTitle(vprop) || vprop;
    const modifyVpropInfo = `${vpropTitle}值由${originValue}修改为${value}`;
    description.push({
      id,
      text: `${time}${modifyTypeText}${objectName}${
        type === SolutionModifyType.VPROP ? modifyVpropInfo : ''
      }`,
    });
  });
  return description;
}
