/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SolutionBaseInfo } from './solution-detail';

export enum CompareSourceType {
  SOLUTION = 'SOLUTION',
  HISTORY = 'HISTORY',
}

export enum SolutionSource {
  ONLINE = 'ONLINE',
  UPLOAD = 'UPLOAD',
  SOLUTION = 'SOLUTION',
}

export enum SolutionType {
  /** 普通 */
  COMMON = 'COMMON',
  /** 基准 */
  BASELINE = 'BASELINE',
  /** 分支 */
  BRANCH = 'BRANCH',
}

/**
 * 单地图模式 | 多地图模式
 */
export enum CompareMapModeType {
  SingleMap = 'SingleMap',
  MultipleMap = 'MultipleMap',
}

export enum HydraulicTimeStep {
  ONE_HOUR = '1:00',
  HALF_HOUR = '0:30',
  QUARTER_HOUR = '0:15',
  FIVE_MINUTES = '0:05',
}

export enum CollapsePanelKeys {
  TOTAL_WATER = 'TOTAL_WATER',
  PLANT_MODE = 'PLANT_MODE',
  PUMP_MODE = 'PUMP_MODE',
  PIPE_VALVE = 'PIPE_VALVE',
  JUNCTION_DEMAND = 'JUNCTION_DEMAND',
  JUNCTION_QUALITY = 'JUNCTION_QUALITY',
  TOPOLOGY = 'TOPOLOGY',
  CALCULATION = 'CALCULATION',
  SOURCE = 'SOURCE',
  OBSERVATION = 'OBSERVATION',
  NOTE = 'NOTE',
  BRANCH = 'BRANCH',
}

export type CompareInfo = {
  name: string;
  id: string;
  historyDate?: string;
  mapName?: string;
  mapType: string;
  info: SolutionBaseInfo | undefined;
};

export enum SolutionStatus {
  CREATE = 'CREATE',
  CREATED = 'CREATED',
  WAIT_CALC = 'WAIT_CALC',
  CALCULATE = 'CALCULATE',
  IDLE = 'IDLE',
  ERROR = 'ERROR',
  INVALID = 'INVALID',
}

export type SolutionListItem = {
  id: string;
  name: string;
  sourceType: SolutionSource;
  sourceName: string;
  sourceSolutionId?: string;
  createTime: string;
  startTime: string;
  endTime: string;
  createUser: string;
  note?: string;
  status: SolutionStatus;
  statusMessage: string;
  duration: number;
  interval: number;
  isShared: boolean;
  createByMyself?: boolean;
  department: string;
  solutionType: SolutionType;
  children?: SolutionListItem[];
  parentIndex?: string;
  isReadOnly?: boolean;
  solutionGuid: string;
  eventsInfo?: string;
};

export interface SolutionComparisonItem {
  /** 对比ID */
  comparisonId: string;
  /** 对比类型 */
  comparisonType: CompareSourceType;
  /** 基准方案ID */
  baseSolutionId: string;
  /** 测试方案ID */
  testSolutionId: string;
  /** 基准方案GUID */
  baseSolutionGuid: string;
  /** 测试方案GUID */
  testSolutionGuid: string;
  /** 对比标题 */
  comparisonTitle: string;
  /** 对比时间 */
  comparisonTime: string;
  /** 对比备注 */
  comparisonNote: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 创建人 */
  createUser: string;
  /** 基准方案名称 */
  baseSolutionName: string;
  /** 测试方案名称 */
  testSolutionName: string;
  /** 事件关联 */
  eventsInfo?: string;
}

export type QuerySolutionShareList = {
  department: string[];
  user: string[];
};

export const maxSelectCount = 3;
export const validatorErrMsg1 = '选择的对比方案不能包含或与当前方案相同';
export const validatorErrMsg2 = `请选择1~${maxSelectCount}个方案`;

export const getSolutionTypeName = (source: string): string => {
  switch (source) {
    case SolutionType.COMMON:
      return '普通';
    case SolutionType.BASELINE:
      return '基准';
    case SolutionType.BRANCH:
      return '分支';
    default:
      return '';
  }
};

export const getSolutionStatusName = (status: SolutionStatus): string => {
  switch (status) {
    case SolutionStatus.CREATE:
      return '创建中';
    case SolutionStatus.CREATED:
      return '已创建';
    case SolutionStatus.WAIT_CALC:
      return '等待计算';
    case SolutionStatus.CALCULATE:
      return '计算中';
    case SolutionStatus.ERROR:
      return '计算失败';
    case SolutionStatus.IDLE:
      return '计算完成';
    case SolutionStatus.INVALID:
      return '计算结果失效';
    default:
      return status;
  }
};

export const getSolutionSourceName = (solutionType: string): string => {
  switch (solutionType) {
    case SolutionSource.ONLINE:
      return '在线';
    case SolutionSource.UPLOAD:
      return '上传';
    case SolutionSource.SOLUTION:
      return '方案';
    default:
      return solutionType;
  }
};

export const getCompareSourceName = (sourceType: string) => {
  switch (sourceType) {
    case CompareSourceType.HISTORY:
      return '历史';
    case CompareSourceType.SOLUTION:
      return '方案';
    default:
      return sourceType;
  }
};

export const getHydraulicTimeStepLabel = (
  hydraulicTimeStep: string,
): string => {
  switch (hydraulicTimeStep) {
    case HydraulicTimeStep.ONE_HOUR:
      return '1小时';
    case HydraulicTimeStep.HALF_HOUR:
      return '30分钟';
    case HydraulicTimeStep.QUARTER_HOUR:
      return '15分钟';
    case HydraulicTimeStep.FIVE_MINUTES:
      return '5分钟';
    default:
      return hydraulicTimeStep;
  }
};

export const getHydraulicTimeStepMinutes = (
  hydraulicTimeStep: string,
): number => {
  switch (hydraulicTimeStep) {
    case HydraulicTimeStep.ONE_HOUR:
      return 60;
    case HydraulicTimeStep.HALF_HOUR:
      return 30;
    case HydraulicTimeStep.QUARTER_HOUR:
      return 15;
    case HydraulicTimeStep.FIVE_MINUTES:
      return 5;
    default:
      return 0;
  }
};

export const compareSourceOptions = Object.keys(CompareSourceType).map(
  (key) => ({
    label: getCompareSourceName(key),
    value: key,
  }),
);

export const solutionSourceOptions = Object.keys(SolutionSource).map((key) => ({
  label: getSolutionSourceName(key),
  value: key,
}));

export const allSolutionTypeOptions = Object.keys(SolutionType).map((key) => ({
  label: getSolutionTypeName(key),
  value: key,
}));

export const createSolutionTypeOptions = Object.keys(SolutionType)
  .map((key) => ({
    label: getSolutionTypeName(key),
    value: key,
  }))
  .filter((item) => item.value !== SolutionType.BRANCH);

export const saveAsSolutionTypeOptions = Object.keys(SolutionType)
  .map((key) => ({
    label: getSolutionTypeName(key),
    value: key,
  }))
  .filter((item) => item.value !== SolutionType.BASELINE);

export const hydraulicTimeStepOptions = Object.values(HydraulicTimeStep).map(
  (value) => ({
    label: getHydraulicTimeStepLabel(value),
    value,
  }),
);

export const SHOW_POLLUTION_SPREAD_ANALYSIS_PARAM =
  'showPollutionSpreadAnalysis';

/** 污染物扩散分析列表项 */
export interface PollutionSpreadAnalysisItem {
  /** 方案ID */
  solutionId: string;
  /** 方案GUID */
  solutionGuid: string;
  /** 标题 */
  analysisTitle: string;
  /** 备注 */
  analysisNote: string;
  /** 污染开始时间 */
  pollutionStartTime: string;
  /** 污染结束时间 */
  pollutionEndTime: string;
  /** 模拟结束时间 */
  simulationEndTime: string;
  /** 计算ID */
  calcId: string;
  /** 计算时间 */
  calcTime: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 创建人 */
  createUser: string;
  /** 方案名称 */
  solutionName: string;
}
