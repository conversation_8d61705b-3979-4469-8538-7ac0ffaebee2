/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MENU_TYPE_FUNC, MENU_TYPE_PAGE, MenuInfo } from './menu-data';
import { loopTreeList, transListToTree } from './tree';

export type RoutePath = MenuInfo['functionUrl'];

export enum PageCode {
  PAGE_SCHEDULE_DASHBOARD = 'PAGE_SCHEDULE_DASHBOARD',
  /** 应急预案 */
  PAGE_CONTINGENCY_PLAN = 'PAGE_CONTINGENCY_PLAN',
}

export enum FeatureCode {
  /** 新增阀门记录 */
  VALVE_ADD_RECORD = 'FEATURE_VALVE_ADD_RECORD',
  /** 下载数据： 基础数据和延时数据 */
  DOWNLOAD_SCADA = 'FEATURE_DOWNLOAD_SCADA',
  /** 下载数据： 水龄数据 */
  DOWNLOAD_WATERAGE = 'FEATURE_DOWNLOAD_WATERAGE',
  /** 公共关注点编辑 */
  UPDATE_OBSERVATION = 'FEATURE_UPDATE_OBSERVATION',
  /** 添加智慧阀门关联关系 */
  VALVE_ADD_SCADA_RELATION = 'FEATURE_VALVE_ADD_SCADA_RELATION',
  /** 创建方案 */
  CREATE_SOLUTION = 'FEATURE_CREATE_SOLUTION',

  /** 计划事件增删改 */
  EDITABLE_PLAN_PROJECTION = 'FEATURE_EDITABLE_PLAN_PROJECTION',
  /** 调度看板 */
  SCHEDULING_DASHBOARD = 'FEATURE_SCHEDULE_DASHBOARD',
  /** 地图快速菜单 */
  QUICK_MAP_MENU = 'FEATURE_QUICK_MAP_MENU',
  /** 指令及时性修改 */
  COMMAND_TIMELY_EXECUTE = 'FEATURE_TIMELY_EXECUTE',
  /** 快速方案短信  */
  QUICK_SOLUTION_SMS = 'FEATURE_QUICK_SOLUTION_SMS',
  /** 污染物扩散分析  */
  POLLUTION_SPREAD_ANALYSIS = 'FEATURE_POLLUTION_SPREAD_ANALYSIS',

  /** 应急预案编辑功能 */
  CONTINGENCY_PLAN_EDIT = 'FEATURE_CONTINGENCY_PLAN_EDIT',

  /** 设备编辑权限 */
  DEVICE_EDIT = 'PAGE_OBJECTS_ANALYSIS_UPDATE_DEVICE',
}

class SystemPage {
  private _pageInfo: MenuInfo;

  private _featureMap: Map<FeatureCode, MenuInfo> = new Map();

  constructor(data: MenuInfo) {
    this._pageInfo = data;
    if (data.type === MENU_TYPE_PAGE && data?.children?.length) {
      this.initializeFeature(data.children);
    }
  }

  initializeFeature(menuList: MenuInfo[]) {
    menuList.forEach((menuInfo) => {
      if (
        menuInfo.type === MENU_TYPE_FUNC &&
        !this._featureMap.has(menuInfo.functionKey as FeatureCode)
      ) {
        this._featureMap.set(menuInfo.functionKey as FeatureCode, menuInfo);
      }
    });
  }

  hasFeatureInfo(featureCode: FeatureCode): boolean {
    return this._featureMap.has(featureCode);
  }

  hasPageCode(pageCode: PageCode): boolean {
    return this._pageInfo.code === pageCode;
  }

  get info(): MenuInfo {
    return this._pageInfo;
  }
}

export function generateSystemPage(data: MenuInfo): SystemPage {
  return new SystemPage(data);
}

class SystemFeatureManagement {
  static getInstance(): SystemFeatureManagement {
    if (SystemFeatureManagement._instance === undefined) {
      SystemFeatureManagement._instance = new SystemFeatureManagement();
    }
    return SystemFeatureManagement._instance;
  }

  private _pageMap: Map<RoutePath, SystemPage> = new Map();

  // eslint-disable-next-line no-use-before-define
  private static _instance: SystemFeatureManagement;

  registerSystemFeatures(menuList: MenuInfo[]) {
    this._pageMap.clear();
    const menuTree = transListToTree<MenuInfo>(menuList, 'id', 'parentId');
    if (menuTree?.length) {
      loopTreeList(menuTree, this.setPageMap.bind(this));
    }
  }

  private setPageMap(menuInfo: MenuInfo) {
    if (
      menuInfo.type === MENU_TYPE_PAGE &&
      !this._pageMap.has(menuInfo.functionUrl as RoutePath)
    ) {
      this._pageMap.set(
        menuInfo.functionUrl as RoutePath,
        generateSystemPage(menuInfo),
      );
    }
  }

  hasPageInfo(routePath: RoutePath | PageCode): boolean {
    const hasPath = this._pageMap.has(routePath);
    if (hasPath) return true;
    return !!Array.from(this._pageMap.values()).find((pageInfo) =>
      pageInfo.hasPageCode(routePath as PageCode),
    );
  }

  hasFeatureInfo(routePath: RoutePath, featureCode: FeatureCode): boolean {
    const pageInfo = this._pageMap.get(routePath);

    if (!pageInfo) return false;
    return pageInfo.hasFeatureInfo(featureCode);
  }
}

export function pageEnabled(pageCode: PageCode): boolean {
  return SystemFeatureManagement.getInstance().hasPageInfo(pageCode);
}

export function featureEnabled(
  routePathNames: RoutePath | RoutePath[] | undefined,
  featureCode: FeatureCode,
): boolean {
  if (typeof routePathNames === 'undefined') return false;
  const newRoutePathNames =
    typeof routePathNames === 'string' ? [routePathNames] : routePathNames;
  const hasFeature = newRoutePathNames.find((f) =>
    SystemFeatureManagement.getInstance().hasFeatureInfo(f, featureCode),
  );
  return !!hasFeature;
}

export function registerSystemFeatures(menuList: MenuInfo[]) {
  SystemFeatureManagement.getInstance().registerSystemFeatures(menuList);
}
