/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface StatisticsLoginDataItem {
  /** 日期 */
  date: string;
  /** 登录次数 */
  loginTotalCount: number;
  /** 登录用户数 */
  loginUserCount: number;
}

export interface StatisticsLoginData {
  loginTotalCount: number;
  loginUserCount: number;
  data: StatisticsLoginDataItem[];
}

export interface StatisticsUsageData {
  /** 使用月数据 */
  usageSituations: {
    /** 详细方案计算 */
    detailedScheme: number;
    /** 快速方案计算 */
    quickScheme: number;
    /** 警告处理次数 */
    warnProcessTimes: number;
    /** 短信发送次数 */
    smsSendTimes: number;
  };
  /** 使用日数据 */
  dailyUsageSituations: {
    /** 日期 */
    date: string;
    /** 详细方案计算 */
    detailedScheme: number;
    /** 快速方案计算 */
    quickScheme: number;
    /** 警告处理次数 */
    warnProcessTimes: number;
    /** 短信发送次数 */
    smsSendTimes: number;
  }[];
}

export interface StatisticsWarnData {
  warnSituations?: Record<string, number>;
  dailyWarnSituations?: Record<string, number>[];
}

export interface ModelServiceItem {
  name: string;
  value: number;
}
