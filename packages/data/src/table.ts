/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export const getConvertedSort = async (
  sort: Record<string, 'ascend' | 'descend' | null>,
) => {
  const convertedSort = Object.entries(sort).reduce(
    (acc, [key, value]) => {
      if (value) {
        return { ...acc, [key]: value === 'ascend' ? 'asc' : 'desc' } as Record<
          string,
          'asc' | 'desc'
        >;
      }
      return acc;
    },
    {} as Record<string, 'asc' | 'desc'>,
  );

  return convertedSort;
};
