/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/**
 * 第三方系统组织信息
 */
export interface ThirdPartyOrgInfo {
  /** 组织唯一标识 */
  orgKey: string;
  /** 组织ID */
  orgId: number;
  /** 父组织ID */
  parentOrgId: number;
  /** 组织名称 */
  orgName: string;
}

/**
 * 第三方系统用户基础信息
 */
export interface ThirdPartyUserBaseInfo {
  /** 用户名称 */
  userName: string;
  /** 手机号 */
  cellphone: string;
  /** 工号 */
  workno: string;
  /** 组织信息 */
  orgInfo: ThirdPartyOrgInfo;
}

/**
 * 第三方系统用户位置信息
 */
export interface ThirdPartyUserLocation {
  /** 纬度 */
  latitude: number;
  /** 经度 */
  longitude: number;
  /** 位置类型 */
  locationType: string;
  /** 半径 */
  radius: number;
  /** 海拔 */
  altitude: number;
  /** 方向 */
  direction: number;
  /** 速度 */
  speed: number;
  /** 时间戳 */
  time: number;
  /** X坐标 */
  x?: number;
  /** Y坐标 */
  y?: number;
}

/**
 * 第三方系统用户信息
 */
export interface ThirdPartyUserInfo {
  /** 用户ID */
  userId: number;
  /** 设备ID */
  deviceId: string;
  /** 位置信息 */
  location: ThirdPartyUserLocation;
  /** 用户基础信息 */
  userInfo: ThirdPartyUserBaseInfo;
}

/**
 * 第三方系统车辆位置信息
 */
export interface ThirdPartyVehicleLocation {
  /** 经度 */
  longitude: number;
  /** 纬度 */
  latitude: number;
  /** 移动的方位角度:0-360,-1 代表未知 */
  direction: number;
  /** 速度,单位 km, -1 为未知 */
  speed: number;
  /** 定位时间 */
  posTime: string;
  /** 定位方式,0: 无/未知.1:GPS,2:基站/网络定位,3:其他 */
  posMethod: number;
  /** X坐标 */
  x?: number;
  /** Y坐标 */
  y?: number;
}

/**
 * 第三方系统车辆信息
 */
export interface ThirdPartyVehicleInfo {
  /** 车辆主键 ID */
  vehicleId: string;
  /** 车辆别名 */
  vehicleNickName: string;
  /** 车架号 */
  vin: string;
  /** 所属企业 ID */
  corpId: string;
  /** 所属部门 ID */
  deptId: string;
  /** 企业、部门名称 */
  deptName: string;
  /** 系列名 */
  productName: string;
  /** 品牌名称 */
  brandName: string;
  /** 车牌号 */
  licenseplate: string;
  /** 设备号 */
  deviceId: string;
  /** 是否处于绑定状态,0: 否, 1:是 */
  bindDevice: string;
  /** 在线状态,-1:未知 ,0:不在线,1:在线静止,2:在线且运行 */
  onlineStatus: number;
  /** 位置信息 */
  location: ThirdPartyVehicleLocation;
  /** 当前行驶总里程，单位 M */
  distanceTotal: string;
  /** 总油耗，单位 ml */
  fuelConsumption: string;
}

/**
 * 坐标系类型
 */
export enum CoordinateSystem {
  /** 高德地图坐标系 - gcj02; EPSG:4326 */
  AMAP = 'AMAP',
  /** 百度地图坐标系 - bd09; EPSG:4326 */
  BAIDU = 'BAIDU',
  /** WGS84坐标系 - GPS; EPSG:4326 */
  WGS84 = 'WGS84',
  /** 墨卡托投影坐标系 - EPSG:3857 */
  MERCATOR = 'MERCATOR',
  /** CGCS2000  EPSG:4549 */
  CGCS2000_39 = 'CGCS2000_39',
}
