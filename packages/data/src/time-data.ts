/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import { MarkInfoList } from './chart-mark';
import Database from './database';
import { EventSchedulingBasicInfo } from './event-scheduling/basic-info';
import { DoubleUnitFormat, UnitFormat } from './unit-system';
import { getMinMax } from './utils';
import { ChartWarnInfo } from './warn';

export interface ValueGroupParams {
  [index: string]: {
    otype?: string;
    oname?: string;
    vprop: string;
    rmode?: string;
  };
}

export interface TimeData {
  time: string;
  value: number;
}

export interface GroupTimeData {
  otype: string;
  oname: string;
  vprop: string;
  timeData: Array<TimeData>;
}

/**
 * time interval as minutes
 */
export type TimeInterval = 1 | 5 | 60;

export interface TimeDataSeries {
  name: string; // for legend data
  type: string; // 'line' | 'scatter' for series type
  timeData: Array<TimeData>;
  dataType?: 'scada' | 'model' | 'envelopMin' | 'envelopMax' | 'forecast';
  legendSelected?: boolean; // Is it activated by default
  unitFormat?: UnitFormat;
  hidden?: boolean;
}

export interface ObjectTimeDataSeriesNew {
  // oname
  oname: string;
  series: Array<TimeDataSeries>;
  warnList?: ChartWarnInfo[];
  markList?: MarkInfoList;
  eventList?: EventSchedulingBasicInfo[];
  maxLimitation?: number;
  minLimitation?: number;
  positionLine?: {
    x: string | number;
    y: string | number | null;
  };
}

export interface ObjectTimeDataSeries {
  typeTitle: string;
  objectTitle: string;
  objectName: string;
  series: Array<TimeDataSeries>;
}

/**
 * get range values from an array of number
 */
export function getRangeValues(data: Array<number>): [number, number] {
  if (data.length === 0) return [0, 1];

  if (data.length === 1) {
    if (data[0] > 0) return [data[0] * 0.9, data[0] * 1.1];
    return [data[0] * 1.1, data[0] * 0.9];
  }

  const { min, max } = getMinMax(data);
  const delta: number = max - min;
  const low: number = min - 0.1 * delta;
  const up: number = max + 0.1 * delta;
  return [low, up];
}

export function convertTimeData(
  db: Database,
  timeData: Array<TimeData>,
  sourceOtype: string,
  sourceVprop: string,
  targetOtype: string,
  targetVprop: string,
): Array<TimeData> {
  if (sourceOtype === targetOtype && sourceVprop === targetOtype)
    return timeData;

  const sourceUnit = db.getUnitFormat(sourceOtype, sourceVprop);
  const targetUnit = db.getUnitFormat(targetOtype, targetVprop);
  if (
    sourceUnit instanceof DoubleUnitFormat &&
    targetUnit instanceof DoubleUnitFormat &&
    sourceUnit.unitName !== targetUnit.unitName
  ) {
    const newTimeData: Array<TimeData> = [];
    timeData.forEach((item) => {
      let originalValue = item.value;
      const exactValue = sourceUnit.getExactValue(item.value);
      if (typeof exactValue === 'number')
        originalValue = targetUnit.getOriginalValue(exactValue);

      newTimeData.push({ time: item.time, value: originalValue });
    });

    return newTimeData;
  }
  return timeData;
}

export function getTimeDataMaxMinAverageValue(
  datas: Array<TimeData> | undefined,
): { min: number; max: number; average: number } | undefined {
  if (typeof datas !== 'undefined') {
    const values = datas.map((data) => data.value).sort((a, b) => a - b);
    const min = values[0];
    const max = values[values.length - 1];
    const average = values.reduce((a, b) => a + b, 0) / values.length;
    return { min, max, average };
  }
  return undefined;
}

export const getDateTimeFromValue = (
  value: number,
  dateValue?: Dayjs | string,
): Dayjs => {
  const date: string = dayjs(
    dateValue ?? dayjs().format('YYYY-MM-DD 00:00:00'),
  ).format('YYYY-MM-DD 00:00:00');
  return dayjs(date).add(value, 'minute');
};

export const getValueFromDateTime = (dateTime: Dayjs): number =>
  dateTime.hour() * 60 + dateTime.minute();

export const getCurrentTimeState = (
  value: number,
  dateValue?: Dayjs,
): 'history' | 'future' =>
  !getDateTimeFromValue(value, dateValue).isBefore(dayjs())
    ? 'future'
    : 'history';

export function changeDateOfTimeData(
  timeData: TimeData[],
  newDate: string,
): TimeData[] {
  const data: TimeData[] = [];
  for (let i: number = 0; i < timeData.length; i += 1) {
    const item: TimeData = {
      time: newDate + timeData[i].time.slice(newDate.length),
      value: timeData[i].value,
    };
    data.push(item);
  }

  return data;
}

/** 将传入的日期数组转成时间区间数组
 *
 * @param dates 格式为 'YYYY-MM-DD HH:mm:ss' 的字符串数组
 * @returns 包含时间区间的开始和结束时间
 */
export const convertDatesToRange = (dates?: string[]): [Dayjs, Dayjs] => {
  // 如果数组为空，返回近三日的时间区间
  if (dates?.length === 0 || !dates) return [dayjs().add(-2, 'day'), dayjs()];

  // 将日期字符串转换为 dayjs 对象并排序
  const sortedDates = dates
    .map((date) => dayjs(date))
    .sort((a, b) => (a.isAfter(b) ? 1 : -1));

  // 获取最小和最大日期
  const minDate = sortedDates[0];
  const maxDate = sortedDates[sortedDates.length - 1];

  // 如果最小和最大日期的差值在三天以内，返回包含最小和最大日期的三日时间区间
  if (maxDate.diff(minDate, 'day') <= 2)
    return [minDate.add(-1, 'day'), maxDate.add(1, 'day')];

  // 否则，返回最小和最大日期的时间区间
  return [minDate, maxDate];
};
