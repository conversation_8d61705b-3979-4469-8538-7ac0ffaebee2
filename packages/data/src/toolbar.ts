/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MOUSE_EDIT_ADDSINGLEPIPE,
  MOUSE_MEASURE_LINESTRING,
  MOUSE_SELECT,
} from './ui-types';

export const TOOLBAR_BUTTONS = {
  BACK: 'BACK',
  ZOOM_FULL_EXTENT: 'ZOOM_FULL_EXTENT',
  CLEAR_HIGH_LIGHT: 'CLEAR_HIGH_LIGHT',
  COORDINATE: 'COORDINATE',
  INDICATOR: 'INDICATOR',
  CHART: 'CHART',
  SIMULATION: 'SIMULATION',
  REFRESH: 'REFRESH',
  ISSUE_REPORT: 'ISSUE_REPORT',
  ADD_WORK_ORDER: 'ADD_WORK_ORDER',
  SEND_SMS: 'SEND_SMS',
  MOUSE_SELECT,
  MOUSE_MEASURE_LINESTRING,
  MOUSE_EDIT_ADDSINGLEPIPE,
} as const;

export type ToolbarButton = keyof typeof TOOLBAR_BUTTONS;
