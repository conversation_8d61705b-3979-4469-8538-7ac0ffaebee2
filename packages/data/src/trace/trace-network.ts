/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeObjectId } from '../object-item';

export class Pipe {
  private _data: Map<string, any> = new Map();

  constructor(data: {}) {
    const dataMap = new Map(Object.entries(data));
    [...dataMap].forEach(([key, value]) => {
      this._data.set(key, value);
    });

    this._oname = this._data.get('oname');
    this._otype = this._data.get('otype');
    this._id = makeObjectId(this._otype, this._oname);
    this._shape = this._data.get('shape');
    if (typeof this._shape !== 'string' || this._shape === '') return;
    if (this._data.has('FLOW')) {
      const flowValue = this._data.get('FLOW');
      if (flowValue === null || flowValue === '') return;
      this._flow = flowValue ?? 0;
    }
    if (this._data.has('DIAMETER'))
      this._diameter = this._data.get('DIAMETER') ?? 0;
    if (this._data.has('VELOCITY'))
      this._velocity = this._data.get('VELOCITY') ?? 0;
    this.initialize();
  }

  private _id: string = '';

  get id(): string {
    return this._id;
  }

  private _oname: string = '';

  get oname(): string {
    return this._oname;
  }

  private _otype: string = '';

  get otype(): string {
    return this._otype;
  }

  private _diameter: number = 0;

  get diameter(): number {
    return this._diameter;
  }

  private _velocity: number = 0;

  get velocity(): number {
    return this._velocity;
  }

  private _flow: number = 0;

  get flow(): number {
    return this._flow;
  }

  private _shape: string = '';

  get shape(): string | undefined {
    return this._shape;
  }

  private _startNodeId: string | undefined;

  get startNodeId(): string | undefined {
    return this._startNodeId;
  }

  private _endNodeId: string | undefined;

  get endNodeId(): string | undefined {
    return this._endNodeId;
  }

  isValid(): boolean {
    return (
      this._startNodeId !== undefined &&
      this._endNodeId !== undefined &&
      this._flow !== 0
    );
  }

  getCoordinates(): string[] {
    const coordinates = this._shape.substring(
      this._shape.indexOf('(') + 1,
      this._shape.lastIndexOf(')'),
    );
    return coordinates.split(',');
  }

  private reverseShape() {
    if (!this._shape.startsWith('LINE')) return;

    const coordinates = this.getCoordinates();
    const reversedCoordinates = coordinates.slice().reverse();
    this._shape = `LINESTRING(${reversedCoordinates.join(',')})`;
  }

  initialize(): void {
    if (this._flow < 0) this.reverseShape();
    if (!this._shape.startsWith('LINE')) return;

    const coordinates = this._shape.substring(
      this._shape.indexOf('(') + 1,
      this._shape.lastIndexOf(')'),
    );
    const line = coordinates.split(',');
    if (line.length >= 2) {
      this._startNodeId = line[0].trim();
      this._endNodeId = line[line.length - 1].trim();
    }
  }
}

export class Node {
  constructor(xy: string) {
    this._id = xy;
  }

  private _id: string;

  get id(): string {
    return this._id;
  }

  private _upPipes: Set<string> = new Set<string>();

  get upPipes(): Set<string> {
    return this._upPipes;
  }

  private _downPipes: Set<string> = new Set<string>();

  get downPipes(): Set<string> {
    return this._downPipes;
  }

  get pipeCount(): number {
    return this._upPipes.size + this._downPipes.size;
  }

  addConnectedPipe(pipe: Pipe) {
    if (pipe.startNodeId === this._id) {
      this._downPipes.add(pipe.id);
    } else if (pipe.endNodeId === this._id) {
      this._upPipes.add(pipe.id);
    }
  }
}

export class TracePath {
  constructor() {
    this._pipes = [];
  }

  private _pipes: Array<Pipe>;

  get pipes(): Array<Pipe> {
    return this._pipes;
  }

  private _coordinates: string[] = [];

  get coordinates(): string[] {
    return this._coordinates;
  }

  get shape(): string {
    return `LINESTRING(${this._coordinates.join(',')})`;
  }

  private _diameter: number = 0;

  get diameter(): number {
    return this._diameter;
  }

  private _velocity: number = 0;

  get velocity(): number {
    return this._velocity;
  }

  private _flow: number = 0;

  get flow(): number {
    return this._flow;
  }

  addPipe(pipe: Pipe) {
    this._pipes.push(pipe);
    if (this._coordinates.length === 0) {
      this._coordinates.push(...pipe.getCoordinates());
      this._flow = Math.abs(pipe.flow);
    } else {
      this._coordinates.push(...pipe.getCoordinates().slice(1));
    }
    if (this._diameter === 0) this._diameter = pipe.diameter;
    if (this._velocity === 0) this._velocity = pipe.velocity;
  }

  canMerge(pipe: Pipe): boolean {
    if (this._pipes.length === 0) return true;

    if (
      this._diameter !== pipe.diameter &&
      this._diameter !== 0 &&
      pipe.diameter !== 0
    )
      return false;
    const deltaFlow: number = Math.abs(this._flow - Math.abs(pipe.flow));
    if (deltaFlow <= 50) return true;
    if (this._flow === 0) return false;
    if (deltaFlow / this._flow > 0.2) return false;
    return true;
  }
}

export default class TraceNetwork {
  private _pipes: Map<string, Pipe>;

  private _nodes: Map<string, Node>;

  constructor() {
    this._pipes = new Map<string, Pipe>();
    this._nodes = new Map<string, Node>();
  }

  get pipes(): Map<string, Pipe> {
    return this._pipes;
  }

  get nodes(): Map<string, Node> {
    return this._nodes;
  }

  private _arrayTracePath: Array<TracePath> = [];

  get arrayTracePath(): Array<TracePath> {
    return this._arrayTracePath;
  }

  initialize(data: any[]) {
    data.forEach((item) => {
      const pipe: Pipe = new Pipe(item);
      if (pipe.isValid()) this._pipes.set(pipe.id, pipe);
    });

    this._pipes.forEach((value) => {
      if (value.startNodeId !== undefined) {
        this.tryAddNode(value.startNodeId, value);
      }

      if (value.endNodeId !== undefined) {
        this.tryAddNode(value.endNodeId, value);
      }
    });
  }

  private tryAddNode(nodeId: string, pipe: Pipe) {
    let node = this._nodes.get(nodeId);
    if (node === undefined) {
      node = new Node(nodeId);
      this._nodes.set(node.id, node);
    }
    node.addConnectedPipe(pipe);
  }

  validPipeCount(): number {
    return this._pipes.size;
  }

  hasPipe(id: string): boolean {
    return this._pipes.has(id);
  }

  private findStartPipeIds(): Set<string> {
    const startPipes: Set<string> = new Set<string>();
    this._nodes.forEach((item) => {
      if (item.upPipes.size !== 1 || item.downPipes.size > 1) {
        item.downPipes.forEach((downPipe) => {
          startPipes.add(downPipe);
        });
      }
    });
    return startPipes;
  }

  private getNextPipe(pipeId: string): Pipe | undefined {
    const pipe: Pipe | undefined = this._pipes.get(pipeId);
    if (pipe?.endNodeId === undefined) return undefined;
    const endNode = this._nodes.get(pipe.endNodeId);
    if (endNode === undefined) return undefined;
    if (endNode.upPipes.size === 1 && endNode.downPipes.size === 1) {
      const nextPipeId = endNode.downPipes.values().next().value;
      if (nextPipeId === undefined) return undefined;
      return this._pipes.get(nextPipeId);
    }

    return undefined;
  }

  tryMergePath() {
    this._arrayTracePath = [];
    const startPipeIds: Set<string> = this.findStartPipeIds();
    startPipeIds.forEach((startPipeId) => {
      const setPath: Set<TracePath> = this.mergePath(startPipeId);
      this._arrayTracePath.push(...setPath);
    });
  }

  private mergePath(startPipeId: string): Set<TracePath> {
    const setPath: Set<TracePath> = new Set<TracePath>();
    let currentPath: TracePath | undefined;
    let currentPipe = this._pipes.get(startPipeId);
    while (currentPipe !== undefined) {
      if (currentPath === undefined) {
        currentPath = new TracePath();
        setPath.add(currentPath);
      }
      currentPath.addPipe(currentPipe);
      currentPipe = this.getNextPipe(currentPipe.id);
      if (currentPipe !== undefined && !currentPath.canMerge(currentPipe))
        currentPath = undefined;
    }

    return setPath;
  }
}
