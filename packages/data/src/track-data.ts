/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from './database';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
  TrackScada,
  TrackUpLink,
} from './highlight-object';
import { AnimateConfig, LegendGroupData, LegendItemData } from './legend-data';
import { getShapeType, makeObjectId } from './object-item';
import { SidebarMenuType } from './sidebar-menu-data';
import { getSelectColor, HIGHLIGHT_TRACK_SELECT } from './style-config';
import { getUnitFormat } from './unit-system';
import { exportToExcel } from './utils';

export type TrackType =
  | SidebarMenuType.UPSTREAM_TRACKING
  | SidebarMenuType.DOWNSTREAM_TRACKING
  | SidebarMenuType.POLLUTION_SOURCE_TRACING
  | SidebarMenuType.CUSTOM_TRACKING
  | SidebarMenuType.POLLUTION_SPREAD_ANALYSIS;

/** 上下游追踪 - 去向追踪 */
export const TRACK_DOWN: TrackType = SidebarMenuType.DOWNSTREAM_TRACKING;
/** 上下游追踪 - 来源追踪 */
export const TRACK_UP: TrackType = SidebarMenuType.UPSTREAM_TRACKING;
/** 上下游追踪 - 污染溯源 */
export const TRACK_POLLUTION: TrackType =
  SidebarMenuType.POLLUTION_SOURCE_TRACING;
/** 上下游追踪 - 自定义追踪 */
export const TRACK_CUSTOM: TrackType = SidebarMenuType.CUSTOM_TRACKING;
/** 上下游追踪 - 污染扩散分析 */
export const TRACK_POLLUTION_SPREAD: TrackType =
  SidebarMenuType.POLLUTION_SPREAD_ANALYSIS;

export const SELECT_ICON = '\ue6a4';

export interface ResultData {
  defaultHighlightDatas?:
    | HighlightObject[]
    | TrackDownLink[]
    | TrackDownDma[]
    | TrackUpLink[];
  upDmaDatas?: TrackDownDma[];
  upLinkDatasMap?: Map<string, TrackUpLink>;
  upLinkDatas?: HighlightObject[];
  upScadaDatas?: TrackScada[];
  upWaterMeterDatas?: HighlightObject[];
  downDmaDatas?: TrackDownDma[];
  downLinkDatasMap?: Map<string, TrackDownLink>;
  downLinkDatas?: HighlightObject[];
  downScadaDatas?: TrackScada[];
  downWaterMeterDatas?: HighlightObject[];
  linkSourceDatas?: HighlightObject[];
  linkSourceDatasMap?: Map<string, TrackUpLink>;
  mapViewName?: string;
}

export interface PollutionResultData {
  pollutedSourceDmaDatas?: TrackDownDma[];
  pollutedSourceLinkDatasMap?: Map<string, TrackUpLink>;
  pollutedSourceLinkDatas?: HighlightObject[];
  pollutedSourceScadaDatas?: HighlightObject[];

  pollutedDmaDatas?: TrackDownDma[];
  pollutedLinkDatasMap?: Map<string, TrackDownLink>;
  pollutedLinkDatas?: HighlightObject[];
  pollutedScadaDatas?: TrackScada[];

  unpollutedDmaDatas?: TrackDownDma[];
  unpollutedLinkDatasMap?: Map<string, TrackDownLink>;
  unpollutedLinkDatas?: HighlightObject[];
  unpollutedScadaDatas?: TrackScada[];
  mapViewName?: string;
}

export interface ObjectFormItem {
  oname: string;
  otype: string;
  shape: string;
  otypeTitle: string;
  title: string;
  key: string;
  name: number;
  highlightColor?: string;
  highlightIcon?: string;
  gisOtype?: string;
  gisOname?: string;
}

export interface ThemeConfig {
  vprop: string;
  title: string;
  label: string;
  value: string;
  data: { value: number; color: string }[];
}

type ColumnsType = Array<{
  key: string;
  dataIndex: string;
  title: string;
  width: string | number;
  ellipsis: boolean;
}>;

export class TrackConfig {
  private _db: Database;

  collapes: Map<string, any> = new Map();

  themes: Map<string, LegendGroupData[]> = new Map();

  panelColumnsMap: Map<string, ColumnsType> = new Map();

  tableColumnsMap: Map<string, ColumnsType> = new Map();

  trackType: TrackType | undefined;

  constructor(db: Database, data: any, trackType: TrackType) {
    this._db = db;
    if (data) {
      this.trackType = trackType;
      this.initialize(data);
    }
  }

  static getThemeItems(themeItemDatas: any): LegendItemData[] {
    const themeItems: LegendItemData[] = [];
    const { length } = themeItemDatas.value;
    themeItemDatas.value.forEach(
      (themeValue: { color: any; value: any }, index: number) => {
        const id = `legend${index}`;
        let themeItem = {
          color: themeValue.color,
          count: undefined,
          id,
          title: `< ${themeValue.value}`,
          value: themeValue.value,
          checked: false,
        };
        if (length === 1) {
          themeItem = {
            color: themeValue.color,
            count: undefined,
            id,
            title: '',
            value: themeValue.value,
            checked: false,
          };
          themeItems.push(themeItem);
          return;
        }
        if (typeof themeValue.value === 'undefined') {
          themeItem = {
            color: themeValue.color,
            count: undefined,
            id,
            title: `>= ${themeItemDatas.value[length - 2].value}`,
            value: '',
            checked: false,
          };
        }
        themeItems.push(themeItem);
      },
    );
    return themeItems;
  }

  initializeThemes(
    otype: string,
    data: any,
    animateConfig?: AnimateConfig,
  ): LegendGroupData[] {
    const themes: LegendGroupData[] = [];
    Object.keys(data).forEach((item) => {
      if (data[item].type && data[item].type !== this.trackType) {
        return;
      }
      const otypeProperty = this._db.getPropertyInfo(otype);
      const title = otypeProperty?.getPropertyTitle(item);
      const unitKey = otypeProperty?.getPropertyUnit(item);
      const unit = unitKey ? getUnitFormat(unitKey) : undefined;
      if (!title) return;
      const theme: LegendGroupData = {
        icon: data[item].icon ?? '\ue68f',
        name: item,
        title: (data[item].title ?? title) + (unit ? unit.unitSymbol : ''),
        unitSymbol: unit ? unit.unitSymbol : '',
        type: 'text',
        items: TrackConfig.getThemeItems(data[item]),
        animateConfig: animateConfig ?? {
          pipeColor: '#5cdbff',
          waterFlowColor: '#5cdbff',
          symbolSize: 8,
        },
      };
      themes.push(theme);
    });
    return themes;
  }

  initializeCategoryColumns(
    collapesKey: string,
    otypes: string[],
    vprops: string[],
  ) {
    otypes.forEach((otype) => {
      const otypeProperty = this._db.getPropertyInfo(otype);
      if (!otypeProperty) {
        return;
      }
      const columns: ColumnsType = [];
      vprops.forEach((vprop) => {
        const title = otypeProperty.getPropertyTitle(vprop);
        if (!title) {
          return;
        }
        const unitKey = otypeProperty.getPropertyUnit(vprop);
        const unit = unitKey ? getUnitFormat(unitKey) : undefined;
        columns.push({
          key: vprop,
          dataIndex: vprop,
          title: `${title}${unit ? `(${unit.unitSymbol ?? ''})` : ''}`,
          width: 100,
          ellipsis: true,
        });
      });
      this.panelColumnsMap.set(collapesKey, columns);
    });
  }

  initializeTableColumns(
    collapesKey: string,
    otypes: string[],
    vprops: string[],
  ) {
    otypes.forEach((otype) => {
      const otypeProperty = this._db.getPropertyInfo(otype);
      if (!otypeProperty) {
        return;
      }
      const columns: ColumnsType = [];
      vprops.forEach((vprop) => {
        const title = otypeProperty.getPropertyTitle(vprop);
        if (!title) {
          return;
        }
        const unitKey = otypeProperty.getPropertyUnit(vprop);
        const unit = unitKey ? getUnitFormat(unitKey) : undefined;
        columns.push({
          key: vprop,
          dataIndex: vprop,
          title: `${title}${unit ? `(${unit.unitSymbol ?? ''})` : ''}`,
          width: 100,
          ellipsis: true,
        });
      });
      this.tableColumnsMap.set(collapesKey, columns);
    });
  }

  initialize(datas: any) {
    Object.keys(datas).forEach((collapesKey) => {
      this.themes.set(
        collapesKey,
        this.initializeThemes(
          datas[collapesKey].otype[0],
          datas[collapesKey].themes,
          datas[collapesKey].animateConfig,
        ),
      );

      this.initializeCategoryColumns(
        collapesKey,
        datas[collapesKey].otype,
        datas[collapesKey].category,
      );

      this.initializeTableColumns(
        collapesKey,
        datas[collapesKey].otype,
        datas[collapesKey].table,
      );
    });
  }
}

export function getColorAndValueByThemeConfig(
  value: number | string | undefined | null,
  themeConfig: LegendGroupData,
): { color: string; valueAndUnit: string } {
  const { items, unit, unitSymbol } = themeConfig;
  const themeConfigLength = items.length;
  const lastColor = items[themeConfigLength - 1].color;
  if (typeof value === 'number' || typeof value === 'string') {
    let valueAndUnit: string | number = value;
    if (unit) {
      valueAndUnit = unit.getValueWithSymbol(value) ?? value;
    } else if (unitSymbol) {
      valueAndUnit = value + (unitSymbol ?? '');
    }
    for (let i = 0; themeConfigLength > i; i += 1) {
      const themeValue = items[i].value;

      if (!themeValue) {
        return {
          color: lastColor,
          valueAndUnit: valueAndUnit.toString(),
        };
      }
      if (typeof value === 'number' && Number(themeValue) > value)
        return {
          color: themeConfig.items[i].color,
          valueAndUnit: valueAndUnit.toString(),
        };
    }
  }
  return {
    color: lastColor,
    valueAndUnit: '',
  };
}

export function generateHighlightObject(
  objectFormData: ObjectFormItem[],
  highlightObject: Partial<HighlightObject>,
): HighlightObject[] {
  const {
    highlightIcon,
    highlightText,
    highlightType,
    highlightShowMark = false,
  } = highlightObject || {};
  return objectFormData.map((item: ObjectFormItem) => ({
    oname: item.oname,
    otype: item.otype,
    id: makeObjectId(item.otype, item.oname),
    shape: item.shape,
    shapeType: getShapeType(item.shape),
    highlightIcon: getShapeType(item.shape) === 'LINE' ? highlightIcon : '',
    highlightText,
    highlightType: highlightType ?? HIGHLIGHT_TRACK_SELECT,
    highlightShowMark,
    highlightColor: getSelectColor(highlightType ?? HIGHLIGHT_TRACK_SELECT),
  }));
}

export function exportTrackData(
  data: { [index: string]: string | number | undefined | null }[],
  columns: { dataIndex: string; title: string; [index: string]: unknown }[],
  fileName: string,
) {
  const columnsData = columns.map(({ dataIndex, title }) => ({
    dataIndex,
    title,
  }));
  exportToExcel(
    data,
    columnsData,
    `${fileName}_${dayjs().format('YYYYMMDD_HHmmss')}`,
    undefined,
    '-',
  );
}

/**
 * 将包含Map的数据结构转换为可JSON序列化的格式
 * @param data 包含Map的数据
 * @returns 转换后的可序列化数据
 */
export function convertMapToSerializable<T>(data: T): unknown {
  if (data === null || data === undefined) {
    return data;
  }

  if (data instanceof Map) {
    // 直接将Map转换为键值对数组，这样可以保留所有信息并且易于序列化
    return Array.from(data.entries()).map(([key, value]) => ({
      key,
      value: convertMapToSerializable(value),
    }));
  }

  if (Array.isArray(data)) {
    return data.map((item) => convertMapToSerializable(item));
  }

  if (typeof data === 'object') {
    const result: Record<string, unknown> = {};
    Object.keys(data as Record<string, unknown>).forEach((key) => {
      result[key] = convertMapToSerializable(
        (data as Record<string, unknown>)[key],
      );
    });
    return result;
  }

  return data;
}

/**
 * 将序列化后的数据转换回包含Map的结构
 * @param data 序列化的数据
 * @returns 转换后的包含Map的数据
 */
export function convertSerializableToMap<T>(data: unknown): T {
  if (data === null || data === undefined) {
    return data as T;
  }

  // 检测是否为我们转换的Map数组形式（数组中元素都有key和value属性）
  if (
    Array.isArray(data) &&
    data.length > 0 &&
    data.every(
      (item) =>
        item && typeof item === 'object' && 'key' in item && 'value' in item,
    )
  ) {
    const map = new Map();
    data.forEach((item) => {
      map.set(item.key, convertSerializableToMap(item.value));
    });
    return map as unknown as T;
  }

  if (Array.isArray(data)) {
    return data.map((item) => convertSerializableToMap(item)) as unknown as T;
  }

  if (typeof data === 'object' && data !== null) {
    const result: Record<string, unknown> = {};
    Object.keys(data as Record<string, unknown>).forEach((key) => {
      result[key] = convertSerializableToMap(
        (data as Record<string, unknown>)[key],
      );
    });
    return result as unknown as T;
  }

  return data as T;
}
