/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { cloneDeep } from 'lodash';

type TreeDataItem<T> = T & { children?: TreeDataItem<T>[] };

type TreeOptions<L, C = L> = {
  sorted?: 'asc' | 'desc';
  sortedKey?: keyof L;
  convertItem?: (item: L) => C;
} & (
  | {
      sorted: 'asc' | 'desc';
      sortedKey: keyof L;
    }
  | {
      sorted?: undefined;
      sortedKey?: undefined;
    }
);

/**
 * 将列表数据转换为树形结构
 * @param list 列表数据
 * @param idKey 主键字段名
 * @param parentIdKey 父级主键字段名
 * @param options 配置项
 * @param options.sorted 排序方式，asc为升序，desc为降序
 * @param options.sortedKey 排序字段名，当sorted非空时必填
 * @param options.convertItem 转换函数，用于将输入类型转换为输出类型
 */
export const transListToTree = <
  L extends { [key: string]: any },
  C extends { [key: string]: any } = L,
>(
  list: Array<L>,
  idKey: keyof L,
  parentIdKey: keyof L,
  options?: TreeOptions<L, C>,
): TreeDataItem<C>[] => {
  const listData = cloneDeep(list);
  const mapList = new Map<string, TreeDataItem<C>>();
  const treeData: TreeDataItem<C>[] = [];

  // 先建立映射关系
  listData.forEach((item) => {
    const convertedItem = options?.convertItem
      ? options.convertItem(item)
      : (item as unknown as C);

    mapList.set(item[idKey] as string, convertedItem);
  });

  // 构建树
  listData.forEach((item) => {
    const currentId = item[idKey] as string;
    const parentId = item[parentIdKey] as string;
    const currentItem = mapList.get(currentId)!;
    const parent = mapList.get(parentId);

    if (parent && parentId !== currentId) {
      parent.children = parent.children || [];
      parent.children.push(currentItem);
    } else {
      treeData.push(currentItem);
    }
  });

  // 排序处理
  if (options?.sorted && options?.sortedKey) {
    const visited = new Set<string>();

    const sortTree = (tree: TreeDataItem<C>[]) => {
      tree.sort((a, b) => {
        const orderA = (a as any)[options.sortedKey!] || 0;
        const orderB = (b as any)[options.sortedKey!] || 0;
        return options.sorted === 'asc' ? orderA - orderB : orderB - orderA;
      });

      tree.forEach((node) => {
        const currentId = (node as any)[idKey] as string;

        // 检查循环引用
        if (visited.has(currentId)) {
          console.warn(`检测到循环引用: 节点 ${currentId} 已被访问`);
          return;
        }

        visited.add(currentId);

        if (node.children?.length) {
          sortTree(node.children);
        }

        visited.delete(currentId);
      });
    };

    sortTree(treeData);
  }

  return treeData;
};

export const transTreeToList = <
  T extends TreeDataItem<{ [index: string]: any }>,
>(
  treeData: Array<T>,
): Array<Omit<T, 'children'>> => {
  let listData: Array<Omit<T, 'children'>> = [];
  for (let i = 0; i < treeData.length; i += 1) {
    const { children, ...rest } = treeData[i];
    listData.push(rest);
    if (Array.isArray(children)) {
      listData = [...listData, ...transTreeToList(children as T[])];
    }
  }
  return listData;
};

/**
 * 递归查找指定节点
 * @param treeData  treeData
 * @param keyName 指定节点的属性名
 * @param key  指定节点的属性名的值, 全等匹配
 * @returns 指定的节点
 */
export const getTreeDataNode = <
  T extends TreeDataItem<{ [index: string]: any }>,
  K extends keyof T,
>(
  treeData: T[],
  keyName: K,
  key: T[K],
): T | undefined => {
  let node: T | undefined;
  for (let i = 0; i < treeData.length; i += 1) {
    if (node) return node;
    if (treeData[i][keyName] === key) {
      return treeData[i];
    }
    if (Array.isArray(treeData[i].children)) {
      node = getTreeDataNode(treeData[i].children as T[], keyName, key);
    }
  }
  return node;
};

/**
 * 递归遍历树， 执行回调
 * @param treeData
 * @param callback
 */
export const loopTreeList = <T extends TreeDataItem<{ [index: string]: any }>>(
  treeData: T[],
  callback?: (value: T) => void,
): void => {
  for (let i = 0; i < treeData.length; i += 1) {
    callback?.(treeData[i]);
    if (typeof treeData[i]?.children !== 'undefined' && treeData[i]?.children)
      loopTreeList(treeData[i].children as T[], callback);
  }
};

/** 获取指定树节点下面所有子节点信息
 * @param list
 * @param idKey 指定节点id
 */
export const getChildrenFromTreeData = <
  T extends TreeDataItem<{ [index: string]: any }>,
  K extends keyof T,
>(
  treeList: T[],
  id: T[K],
): T[] => {
  const childrens: T[] = [];
  const node = getTreeDataNode(treeList, 'id' as keyof T, id);
  if (node?.children?.length) {
    loopTreeList(node.children, (item) => {
      childrens.push(item as T);
    });
  }
  return childrens;
};

export const getParentFromTreeData = <
  T extends TreeDataItem<{ [index: string]: any }>,
  K extends keyof T,
>(
  treeData: T[],
  keyName: K,
  key: T[K],
): T | undefined => {
  let parent: T | undefined;
  for (let i = 0; i < treeData.length; i += 1) {
    if (parent) return parent;
    const node = treeData[i];
    if (node.children) {
      if (node.children.some((item) => item[keyName as string] === key)) {
        parent = node;
      } else {
        parent = getParentFromTreeData(node.children, keyName as string, key) as
          | T
          | undefined;
      }
    }
  }
  return parent;
};
