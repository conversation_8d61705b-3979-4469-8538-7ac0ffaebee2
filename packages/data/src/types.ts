/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export type Key = string | number;

export interface DefaultOptionType<V = Key> {
  label: string;
  value: V;
}

export type Options<
  L = DefaultOptionType['label'],
  V = DefaultOptionType['value'],
> = Array<{
  label: L;
  value: V;
}>;
