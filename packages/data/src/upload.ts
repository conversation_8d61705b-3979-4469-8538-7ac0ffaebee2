/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export enum UploadFileType {
  warnFlow = 'WARN_FLOW',
  menuIcon = 'MENU_ICON',
  schedulingEvent = 'SCHEDULING_EVENT',
  contingencyPlan = 'CONTINGENCY_PLAN',
}

export interface FileItem {
  fileType: UploadFileType;
  uuid: string;
  fileName: string;
}

export interface UploadParams {
  fileName: string;
  fileType: UploadFileType;
  file: Blob;
  uuid: string;
}

export const FILE_TYPE_MAP = {
  // 图片类型
  JPG: 'Image',
  JPEG: 'Image',
  PNG: 'Image',
  GIF: 'Image',
  BMP: 'Image',
  WEBP: 'Image',
  ICO: 'Image',
  SVG: 'Image',

  // 文档类型
  DOC: 'Document',
  DOCX: 'Document',
  PDF: 'PDF',
  TXT: 'Text',
  RTF: 'Document',
  MD: 'Text',

  // 表格类型
  XLS: 'Excel',
  XLSX: 'Excel',
  CSV: 'Excel',

  // 压缩包类型
  ZIP: 'Archive',
  RAR: 'Archive',
  '7Z': 'Archive',
  TAR: 'Archive',
  GZ: 'Archive',

  // 演示文稿类型
  PPT: 'PowerPoint',
  PPTX: 'PowerPoint',

  // 音频类型
  MP3: 'Audio',
  WAV: 'Audio',
  OGG: 'Audio',
  AAC: 'Audio',

  // 视频类型
  MP4: 'Video',
  AVI: 'Video',
  MOV: 'Video',
  WMV: 'Video',
  MKV: 'Video',
} as const;

// 文件图标大小常量
export const FILE_ICON_SIZE = 48;
