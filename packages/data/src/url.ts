/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/**
 * 构建应用内页面 URL
 * @param path 页面路径
 * @param params URL 参数
 * @returns 完整的 URL
 */
export const buildAppUrl = (path: string, params?: Record<string, string>) => {
  const baseUrl =
    process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000' // 开发环境使用固定端口
      : window.location.origin;
  const basePath = window.location.pathname.split('/')[1] || '';

  // 构建基础 URL
  const base = `${baseUrl}/${basePath ? `${basePath}/` : ''}`;

  // 添加 hash
  const withHash = `${base}#${path}`;

  // 添加查询参数
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    return `${withHash}?${searchParams.toString()}`;
  }

  return withHash;
};

/**
 * 在新标签页中打开应用内页面
 * @param path 页面路径
 * @param params URL 参数
 * @param windowFeatures 窗口特性，默认使用安全参数
 * @returns 新打开的窗口对象
 */
export const openInNewTab = (
  path: string,
  params?: Record<string, string>,
  windowFeatures: string = 'noopener,noreferrer',
): Window | null => {
  const url = buildAppUrl(path, params);
  return window.open(url, '_blank', windowFeatures);
};
