/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import { utils, writeFileXLSX } from 'xlsx';
import { Key } from './types';

type TimeRange = [startTime: Dayjs | string, endTime: Dayjs | string];

export type ExcelSheetData = {
  dataSource: Array<{
    [index: string]: number | string | boolean | undefined | null;
  }>;
  columns: Array<{ title: string; dataIndex: string }>;
  sheetName?: string;
  placeholderWithSheet?: string;
};

type MapValue = unknown;
type MapKey = string | number | symbol;
type ConvertedObject = Record<MapKey, MapValue>;

export default function toObject(map = new Map()): ConvertedObject {
  if (!(map instanceof Map)) return map as ConvertedObject;

  const result: ConvertedObject = {};
  Array.from(map.entries()).forEach(([k, v]) => {
    if (Array.isArray(v)) {
      result[k] = v.map(toObject);
    } else if (v instanceof Map) {
      result[k] = toObject(v);
    } else {
      result[k] = v;
    }
  });
  return result;
}

export function round(number: number, precision?: number): number {
  const p = precision ? 10 ** precision : 1;
  return Math.round(number * p) / p;
}

export const formatNumber = (
  number: string | number | undefined | null,
  precision: number,
): number => {
  const value: number = Number(number);

  if (
    !Object.is(value, NaN) &&
    !Object.is(value, Infinity) &&
    !Object.is(value, -Infinity)
  ) {
    return round(value, precision);
  }
  return 0;
};

/**
 * 判断一个时间点是否在时间段之间
 * @param range
 * @param time
 * @returns
 */
export function isTimeBetween(range: TimeRange, time: Dayjs | string): boolean {
  const [startTime, endTime] = range.map((time) => dayjs(time));
  const dayjsTime = dayjs(time);
  if (dayjsTime.isValid() && startTime.isValid() && endTime.isValid())
    return !dayjsTime.isBefore(startTime) && !dayjsTime.isAfter(endTime);
  return false;
}

/**
 * 比较2个时间段是否存在重合
 * @param range1 比较时间段1
 * @param range2 比较时间段2
 * @returns
 */
export function isTimeRangesOverlap(
  range1: TimeRange,
  range2: TimeRange,
): boolean {
  const [range1Start, range1End] = range1.map((time) => dayjs(time));
  const [range2Start, range2End] = range2.map((time) => dayjs(time));

  return (
    isTimeBetween([range2Start, range2End], range1Start) ||
    isTimeBetween([range2Start, range2End], range1End) ||
    isTimeBetween([range1Start, range1End], range2Start) ||
    isTimeBetween([range1Start, range1End], range2End)
  );
}

export function convertToSpecificDate<T extends string | Dayjs>(
  time: T,
  specificDate: string | Dayjs,
): T {
  const originalTime = dayjs(time);
  const hours = originalTime.hour();
  const minutes = originalTime.minute();
  const seconds = originalTime.second();

  const todayWithOriginalTime = dayjs(specificDate)
    .startOf('day')
    .set('hour', hours)
    .set('minute', minutes)
    .set('second', seconds);

  return (
    typeof time === 'string'
      ? todayWithOriginalTime.format('YYYY-MM-DD HH:mm:ss')
      : todayWithOriginalTime
  ) as T;
}

export function getTimeRangesOverlap(
  range1: TimeRange,
  range2: TimeRange,
): TimeRange | undefined {
  const [range1Start, range1End] = range1.map((time) => dayjs(time));
  const [range2Start, range2End] = range2.map((time) => dayjs(time));

  if (range1End.isBefore(range2Start) || range2End.isBefore(range1Start))
    return undefined;
  const overlapStart = range1Start.isAfter(range2Start)
    ? range1Start
    : range2Start;
  const overlapEnd = range1End.isBefore(range2End) ? range1End : range2End;

  return [overlapStart, overlapEnd];
}

export function compareNumbers(
  a: number | undefined | null,
  b: number | undefined | null,
): number {
  return (a ?? -1) - (b ?? -1);
}

export function exportToExcel(
  dataSource: ExcelSheetData['dataSource'],
  columns: ExcelSheetData['columns'],
  fileName: string,
  sheetName?: ExcelSheetData['sheetName'],
  placeholder?: ExcelSheetData['placeholderWithSheet'],
): void {
  const sheetData = dataSource.map((item) => {
    const newObject: {
      [index: string]: number | string | undefined | null | boolean;
    } = {};
    columns.forEach((column) => {
      const { title, dataIndex } = column;
      newObject[title] = item[dataIndex] ?? placeholder;
    });
    return newObject;
  });

  /* get state data and export to XLSX */
  const ws = utils.json_to_sheet(sheetData);
  const wb = utils.book_new();

  utils.book_append_sheet(wb, ws, sheetName ?? 'Sheet1');
  writeFileXLSX(wb, `${fileName}.xlsx`);
}

export function exportToExcelWithMultipleSheets(
  sheets: Array<ExcelSheetData>,
  fileName: string,
  placeholder?: string,
): void {
  const wb = utils.book_new();
  sheets.forEach((sheet, index) => {
    const { dataSource, columns, sheetName, placeholderWithSheet } = sheet;
    const sheetData = dataSource.map((item) => {
      const newObject: {
        [index: string]: number | string | undefined | null | boolean;
      } = {};
      columns.forEach((column) => {
        const { title, dataIndex } = column;
        newObject[title] =
          item[dataIndex] ?? placeholderWithSheet ?? placeholder;
      });
      return newObject;
    });

    /* get state data and export to XLSX */
    const ws = utils.json_to_sheet(sheetData);
    utils.book_append_sheet(wb, ws, sheetName ?? `Sheet${index + 1}`);
  });
  writeFileXLSX(wb, `${fileName}.xlsx`);
}

export const isBetweenRange = (
  value: number,
  min: number = -Number.MAX_VALUE,
  max: number = Number.MAX_VALUE,
): boolean => {
  if (typeof value !== 'number') return false;
  return value >= min && value <= max;
};

/** 用于 table 时间排序 */
export const dateSorter = (a?: string | null, b?: string | null) => {
  const dateA = a ? dayjs(a) : null;
  const dateB = b ? dayjs(b) : null;

  if (dateA && dateB) {
    if (dateA.isSame(dateB)) return 0;
    return dateA.isAfter(dateB) ? 1 : -1;
  }
  if (dateA && !dateB) {
    return 1; // a 有值，b 无值
  }
  if (!dateA && dateB) {
    return -1; // a 无值，b 有值
  }
  return 0; // 都无值
};

/**
 * 将 React.Key[] 转换为项目的 Key[] 类型，过滤掉 bigint 类型
 * @param keys React.Key[] 数组
 * @returns 过滤后的 Key[] 数组，只包含 string 和 number 类型
 */
export const convertReactKeysToKeys = (
  keys: Array<string | number | bigint>,
): Key[] =>
  keys.filter(
    (key): key is string | number =>
      typeof key === 'string' || typeof key === 'number',
  );

export const getMinMax = (data: number[]): { min: number; max: number } => {
  let min = data[0];
  let max = data[0];
  for (let i = 1; i < data.length; i += 1) {
    if (data[i] < min) min = data[i];
    if (data[i] > max) max = data[i];
  }
  return { min, max };
};

/**
 * 测试 exportToExcelWithMultipleSheets 方法在导出巨量数据时的表现
 * @param rowCount 要导出的数据行数
 * @param sheetCount 要创建的工作表数量，默认为1
 */
export function testExportToExcelWithMultipleSheets(
  rowCount: number,
  sheetCount: number = 1,
): void {
  console.log(`🧪 开始测试导出 ${rowCount} 行数据到 ${sheetCount} 个工作表...`);

  const startTime = Date.now();
  const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;

  // 定义影响用户测试的列结构
  const columns = [
    { title: '用户姓名', dataIndex: 'userName' },
    { title: '影响程度', dataIndex: 'impactLevel' },
    { title: '详细地址', dataIndex: 'address' },
    { title: '联系电话', dataIndex: 'phone' },
    { title: '备用电话', dataIndex: 'backupPhone' },
    { title: '区域代码', dataIndex: 'areaCode' },
    { title: '影响时长(小时)', dataIndex: 'duration' },
    { title: '补偿金额(元)', dataIndex: 'compensation' },
    { title: '处理状态', dataIndex: 'status' },
    { title: '备注', dataIndex: 'remark' },
  ];

  // 生成mock数据的辅助函数
  const generateMockData = (count: number, sheetIndex: number) => {
    const mockData = [];
    const surnames = [
      '张',
      '李',
      '王',
      '刘',
      '陈',
      '杨',
      '赵',
      '黄',
      '周',
      '吴',
      '徐',
      '孙',
      '马',
      '朱',
      '胡',
      '林',
      '何',
      '高',
      '梁',
      '郑',
    ];
    const names = [
      '伟',
      '芳',
      '娜',
      '敏',
      '静',
      '丽',
      '强',
      '磊',
      '军',
      '洋',
      '勇',
      '艳',
      '杰',
      '涛',
      '明',
      '超',
      '秀英',
      '霞',
      '平',
      '刚',
    ];
    const areas = [
      '大浙新居',
      '水景花园',
      '阳光小区',
      '绿地家园',
      '金桂花园',
      '银河新城',
      '紫荆花园',
      '梅花新村',
      '桃源小区',
      '竹园新居',
    ];
    const statuses = ['待处理', '处理中', '已完成', '需复核'];

    for (let i = 0; i < count; i++) {
      const surname = surnames[Math.floor(Math.random() * surnames.length)];
      const name = names[Math.floor(Math.random() * names.length)];
      const area = areas[Math.floor(Math.random() * areas.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      mockData.push({
        userName: `${surname}${name}${i % 100 === 0 ? '武' : ''}`,
        impactLevel: (Math.random() * 0.1).toFixed(3),
        address: `${area}${Math.floor(Math.random() * 999) + 1}号${Math.floor(Math.random() * 50) + 1}栋`,
        phone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        backupPhone: `159${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        areaCode: `${Math.floor(Math.random() * 900) + 100}${Math.floor(Math.random() * 9000) + 1000}`,
        duration: Math.floor(Math.random() * 24) + 1,
        compensation: Math.floor(Math.random() * 1000) + 50,
        status,
        remark:
          i % 10 === 0
            ? `Sheet${sheetIndex + 1}-批次${Math.floor(i / 1000) + 1}`
            : '-',
      });
    }

    return mockData;
  };

  // 生成多个工作表的数据
  const sheets: ExcelSheetData[] = [];
  const rowsPerSheet = Math.ceil(rowCount / sheetCount);

  for (let i = 0; i < sheetCount; i++) {
    const currentSheetRows =
      i === sheetCount - 1
        ? rowCount - i * rowsPerSheet // 最后一个工作表处理剩余行数
        : rowsPerSheet;

    sheets.push({
      dataSource: generateMockData(currentSheetRows, i),
      columns,
      sheetName: `影响用户测试_${i + 1}`,
      placeholderWithSheet: '-',
    });
  }

  console.log(`📊 数据生成完成，共 ${sheets.length} 个工作表：`);
  sheets.forEach((sheet, index) => {
    console.log(`  - Sheet ${index + 1}: ${sheet.dataSource.length} 行数据`);
  });

  // 执行导出
  const exportStartTime = Date.now();
  try {
    exportToExcelWithMultipleSheets(
      sheets,
      `影响用户测试_${rowCount}行数据_${dayjs().format('YYYYMMDD_HHmmss')}`,
      '-',
    );

    const exportTime = Date.now() - exportStartTime;
    const totalTime = Date.now() - startTime;
    const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
    const memoryUsed = memoryAfter - memoryBefore;

    console.log(`✅ 导出完成！性能统计：`);
    console.log(`  - 总耗时: ${totalTime}ms`);
    console.log(`  - 导出耗时: ${exportTime}ms`);
    console.log(`  - 数据生成耗时: ${totalTime - exportTime}ms`);
    console.log(`  - 内存使用: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  - 平均每行耗时: ${(totalTime / rowCount).toFixed(2)}ms`);

    // 性能评估
    if (totalTime < 1000) {
      console.log(`🚀 性能评估: 优秀 (< 1s)`);
    } else if (totalTime < 5000) {
      console.log(`⚡ 性能评估: 良好 (1-5s)`);
    } else if (totalTime < 10000) {
      console.log(`⚠️ 性能评估: 一般 (5-10s)`);
    } else {
      console.log(`🐌 性能评估: 较慢 (> 10s)`);
    }
  } catch (error) {
    console.error(`❌ 导出失败:`, error);
    throw error;
  }
}
