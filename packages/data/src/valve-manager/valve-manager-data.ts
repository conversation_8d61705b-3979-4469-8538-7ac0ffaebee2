/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { formatNumber } from '../utils';

export type ValveStatus = 'OPEN' | 'CLOSED' | number | '';
export const VALVE_OPEN: ValveStatus = 'OPEN';
export const VALVE_CLOSED: ValveStatus = 'CLOSED';

export const STATUS_TYPES = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
  OPENING: 'OPENING',
  LOSS: 'LOSS',
} as const;

export enum ValveGroupStatus {
  Finished = 'Finished',
  Pending = 'Pending',
}

export interface ValveOperationValue {
  id: string;
  otime: string;
  type: string;
  otype: string;
  shape: string;
  oname: string;
  status: string | null;
  /** 状态变更，OPEN：打开，CLOSED：关闭，数值：数值 */
  sdval: ValveStatus;
  /** operation */
  operation: string;
  diameter: string;
  source: string;
  department: string;
  createUser: string;
  createTime: string;
  /** 是否在模型中 */
  isModel: boolean;
  note: string;
  /** beforestatus 前序状态 */
  lastStatus: string;
  /** 入库时间 */
  stime: string;
  eventId: string | null;
}

export interface ValveOperationGroup {
  groupID: string;
  title: string;
  startTime: string;
  endTime: string;
  duration: number;
  valveCount: number;
  operationCount: number;
  department: string;
  status: ValveGroupStatus;
  impact: { [index: string]: any } | null;
  note: string;
  stime: string;
  updateTime: string;
  details: ValveOperationValue[];
  eventTitle: string;
  eventName: string;
}

interface ValveOperationValueData extends ValveOperationValue {
  index: number;
}

export interface SmartValveInfo {
  id: string;
  otype: string;
  oname: string;
  ptype: string;
  pname: string;
  deviceName: string;
  deviceType: string;
  deviceTitle: string;
  indicatorName: string;
  indicatorType: string;
  indicatorTitle: string;
}

export type SetValveOperationValueParams = {
  id?: string;
  otime: string;
  otype: string;
  oname: string;
  sdval: string;
  operation?: string;
  source?: string;
  department?: string;
  note?: string;
  beforestatus?: string;
  createUser?: string;
  status?: string;
};

export type UpdateValveGroupDetailsInfo = {
  valveGroupId?: string;
  addValveRecIds?: string[];
  title?: string;
};

export enum ClosedValveStatus {
  CLOSED = 'CLOSED',
  SETTING = 'SETTING',
}

export type ClosedValveInfo = {
  diameter: number;
  mainPipe: boolean;
  otype: string;
  oname: string;
  shape: string;
  status: ClosedValveStatus.CLOSED | ClosedValveStatus.SETTING;
  valveType: string;
  description: string;
  tag: string;
  note1: string;
  note2: string;
  note3: string;
  note4: string;
  note5: string;
};
export const formatClosedValveStatus = (value: ClosedValveStatus) => {
  switch (value) {
    case ClosedValveStatus.CLOSED:
      return '关闭';

    case ClosedValveStatus.SETTING:
    default:
      return '半开';
  }
};

export const closedValveStatusOptions = Object.keys(ClosedValveStatus).map(
  (item) => ({
    text: formatClosedValveStatus(item as ClosedValveStatus),
    value: item,
  }),
);

export const getHighlightGroupsValveData = (
  groups: ValveOperationGroup[],
): {
  otime: string;
  oname: string;
  otype: string;
  shape: string;
  sdval: boolean;
  operatedCount: number;
}[] => {
  const valveMap = new Map<string, ValveOperationValue[]>();
  groups.forEach((group) => {
    group.details.forEach((item) => {
      const { oname } = item;
      const groupValves = valveMap.get(oname) || [];
      groupValves.push(item);
      valveMap.set(oname, groupValves);
    });
  });

  const highlightValveData = [...valveMap.entries()].map(
    ([, valveOperation]) => {
      let latestValveOperation: ValveOperationValue = valveOperation[0];
      valveOperation.forEach((item) => {
        const isBefore = dayjs(latestValveOperation.otime).isBefore(item.otime);
        if (!latestValveOperation || isBefore) {
          latestValveOperation = item;
        }
      });
      const { oname, otype, shape, otime, sdval } = latestValveOperation;
      return {
        otime,
        oname,
        otype,
        shape,
        sdval: sdval !== 'CLOSED',
        operatedCount: valveOperation.length,
      };
    },
  );
  return highlightValveData;
};

export const formatSdval = (
  value: ValveOperationValue['sdval'],
): string | number => {
  if (value === 'OPEN') {
    return '打开';
  }
  if (value === 'CLOSED') {
    return '关闭';
  }
  if (typeof value === 'number') return formatNumber(value, 1);
  return value;
};

const getSdval = (sdval: string): ValveStatus => {
  if (sdval === VALVE_OPEN || sdval === VALVE_CLOSED || sdval === '') {
    return sdval;
  }
  return Number(sdval);
};

export const getValveGroupStatusName = (status: ValveGroupStatus): string => {
  switch (status) {
    case ValveGroupStatus.Finished:
      return '结束';
    default:
      return '进行中';
  }
};

export const parseValveOperationValue = (item: any): ValveOperationValue => ({
  id: item.rec_id ?? '',
  otime: item.otime ?? '',
  type: item.type ?? '',
  otype: item.otype ?? '',
  shape: item.shape ?? '',
  oname: item.oname ?? '',
  status: item.status,
  sdval: getSdval(item.sdval ?? ''),
  operation: item.operation ?? '',
  diameter: item.diameter ?? '',
  source: item.source ?? '',
  department: item.department ?? '',
  createUser: item.create_user ?? '',
  createTime: item.create_time ?? '',
  isModel: item.is_model === '1' || item.is_model === 1,
  note: item.note ?? '',
  lastStatus: item.beforestatus ?? '',
  stime: item.stime ?? '',
  eventId: item.event_name ?? '',
});

export const parseValveOperationList = (list: any[]): ValveOperationValue[] =>
  list?.map((item): ValveOperationValue => parseValveOperationValue(item));

export const parseValveOperationGroups = (data: any): ValveOperationGroup[] => {
  const groups: ValveOperationGroup[] = [];
  data.forEach((element: any) => {
    const duration = dayjs(element.end_time).diff(element.start_time, 'minute');
    const group: ValveOperationGroup = {
      groupID: element.valve_group_id,
      title: element.title,
      startTime: element.start_time,
      endTime: element.end_time,
      duration,
      valveCount: element.valve_count,
      operationCount: element.operation_count,
      department: element.department,
      status: element.status,
      impact:
        typeof element.impact === 'string' && element.impact
          ? JSON.parse(element.impact)
          : element.impact,
      note: element.note,
      stime: element.stime,
      updateTime: element.update_time,
      details: element.details?.map((item: any) =>
        parseValveOperationValue(item),
      ),
      eventTitle: element.event_title ?? '',
      eventName: element.event_name ?? '',
    };

    groups.push(group);
  });

  return groups;
};

export const displayDuration = (duration: number): string => {
  if (duration < 1) return '单时刻';

  const days = Math.floor(duration / (24 * 60));
  const hours = Math.floor((duration % (24 * 60)) / 60);
  const remainingMinutes = duration % 60;
  let durationString = '';
  if (days > 0) durationString += `${days}天`;
  if (hours > 0) durationString += `${hours}小时`;
  if (remainingMinutes > 0) durationString += `${remainingMinutes}分钟`;

  return durationString;
};

export const getValveOperationGroupTitle = (
  group: ValveOperationGroup,
): string => {
  if (group.title || group.details.length === 0) return group.title;

  if (group.details.length === 1) {
    let operation: string = '操作';
    if (group.details[0].sdval === 'OPEN') {
      operation = '打开';
    } else if (group.details[0].sdval === 'CLOSED') {
      operation = '关闭';
    }
    if (group.details[0].otype === 'WDM_PIPES')
      return `${operation}管道 ${group.details[0].oname}`;
    return `${operation}阀门 ${group.details[0].oname}`;
  }

  const valves: Set<string> = new Set();
  group.details.forEach((item) => {
    valves.add(item.oname);
  });

  return `操作阀门 ${[...valves].join(', ')}`;
};

export const containsShapeInValveGroup = (
  group: ValveOperationGroup,
): boolean =>
  group.details.filter((item) => item.shape != null && item.shape.length > 0)
    .length > 0;

export const valveGroupStatusOptions = Object.keys(ValveGroupStatus).map(
  (m) => ({
    label: getValveGroupStatusName(m as ValveGroupStatus),
    value: m,
  }),
);

export const getValveOperationValueData = (
  items: ValveOperationValue[],
): ValveOperationValueData[] =>
  items.map((item, index) => ({ ...item, index: index + 1 }));

export const parseValveOpenCurveData = () => {};

export const getValveLoseCoeff = (
  opening: number,
  data: Array<{ x: number; y: number }>,
): number | null => {
  if (data.length === 0) return null;
  if (opening <= data[0].x) return data[0].y;
  if (opening >= data[data.length - 1].x) return data[data.length - 1].y;

  // 遍历数据，找到 x 在哪两个点之间
  for (let i = 0; i < data.length - 1; i += 1) {
    const x1 = data[i].x;
    const y1 = data[i].y;
    const x2 = data[i + 1].x;
    const y2 = data[i + 1].y;

    if ((opening >= x1 && opening <= x2) || (opening >= x2 && opening <= x1)) {
      // 线性插值计算公式
      return formatNumber(y1 + ((y2 - y1) / (x2 - x1)) * (opening - x1), 2);
    }
  }
  return null;
};
