/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ChannelType } from './sms';
import { WarningLevel, warningLevelMethodEnum } from './warn/warn-level';

export enum WarnSource {
  /** 模型系统 */
  MODEL_SYSTEM = 'MODEL_SYSTEM',
  /** 大数据中心 */
  BIG_DATA_CENTER = 'BIG_DATA_CENTER',
  /** 调度系统 */
  DISPATCH_SYSTEM = 'DISPATCH_SYSTEM',
}

export enum WarnDisplayMode {
  /** 闪烁 */
  FLASH = 'FLASH',
  /** 上时间轴 */
  TIMELINE = 'TIMELINE',
  /** 消息弹窗 */
  MSG_BAR = 'MSG_BAR',
}

export const warnSourceName: Record<WarnSource, string> = {
  [WarnSource.MODEL_SYSTEM]: '模型系统',
  [WarnSource.BIG_DATA_CENTER]: '大数据（第三方）',
  [WarnSource.DISPATCH_SYSTEM]: '综合调度系统',
};

export const warnDisplayModeName: Record<WarnDisplayMode, string> = {
  [WarnDisplayMode.FLASH]: '闪烁',
  [WarnDisplayMode.TIMELINE]: '上时间轴',
  [WarnDisplayMode.MSG_BAR]: '消息弹窗、声音',
};

export const warnDisplayModeOptions = Object.values(WarnDisplayMode).map(
  (value) => ({
    label: warnDisplayModeName[value],
    value,
  }),
);

export interface WarnSettingOriginDataType {
  [key: string]: {
    title: string | number;
    value: string | number;
  };
}

export interface DataType {
  key: string | number;
  id: string | number;
  name: string | number;
  value: string | number;
}

export interface WarnSettingListParams {
  // 警告定义ID
  projectWarningId?: string;
}

export interface WarnSettingList {
  // 警告定义ID
  projectWarningId?: string;
  // 警告定义名称
  projectWarningTitle?: string;
  // 警告参数
  parameter?: string;
  // 警告类型
  type?: string;
  // 警告描述
  description?: string;
  // 展示方式
  displayMode?: WarnDisplayMode[];
  // 工单发送部门
  workOrder?: string;
  // 短信发送人
  message?: string;
  // 警告备注
  remark?: string;
  // 警告来源
  warnSource?: WarnSource;
  // 警告级别
  warningLevel?: WarningLevel;
  // 报警短信模板
  msgTemplate?: string;
  // 报警企微模板
  wecomTemplate?: string;
  // 报警短信发送级别
  msgLevel?: WarningLevel;
  // 报警级别生效方法
  warningLevelMethod?: warningLevelMethodEnum;
}

export interface UpdateWarnSettingParams {
  // 警告定义ID
  projectWarningId?: string;
  // 警告定义名称
  projectWarningTitle?: string;
  // 警告类型
  type?: string;
  // 警告参数
  parameter?: DataType[];
  // 警告来源
  warnSource?: WarnSource;
  // 警告级别
  warningLevel?: WarningLevel;
  // 展示方式
  displayMode?: WarnDisplayMode[];
  // 警告描述
  description?: string;
  // 报警短信发送级别
  msgLevel?: WarningLevel;
}

export interface QueryWarningShareListParams {
  // 警告类型
  warnType?: string;
  // 渠道类型
  channelType?: ChannelType;
}

export interface QueryWarningShareList {
  // 部门
  department: string[];
  // 用户
  user: string[];
}

export interface UpdateWarningShareListParams {
  // 警告类型
  warnType?: string;
  // 警告分享权限清单(json)
  warningShareInfo?: {
    department: string[];
    user: string[];
  };
  // 渠道类型
  channelType?: ChannelType;
}

export const convertObjectToDataType = (
  inputObject: WarnSettingOriginDataType,
): DataType[] =>
  Object.keys(inputObject).map((key) => ({
    key,
    id: key,
    name: inputObject[key].title,
    value: inputObject[key].value,
  }));

export const convertDataTypeToObject = (
  dataArray: DataType[],
): WarnSettingOriginDataType =>
  dataArray.reduce((acc, { id, name, value }) => {
    const parsedValue = Number.isNaN(parseFloat(value as string))
      ? value
      : parseFloat(value as string);
    return {
      ...acc,
      [id]: {
        title: name,
        value: parsedValue,
      },
    };
  }, {} as WarnSettingOriginDataType);

export const convertDisplayModeToBackend = (
  mode?: WarnDisplayMode[],
): string => {
  const modeSettings: Record<WarnDisplayMode, boolean> = {
    FLASH: false,
    TIMELINE: false,
    MSG_BAR: false,
  };

  mode?.forEach((item) => {
    modeSettings[item] = true;
  });

  return JSON.stringify(modeSettings);
};

export const convertDisplayModeFromBackend = (
  mode?: string,
): WarnDisplayMode[] => {
  try {
    if (mode) {
      const modeSettings = JSON.parse(mode);
      return Object.keys(modeSettings).reduce((acc, key) => {
        if (modeSettings[key]) {
          return [...acc, key as WarnDisplayMode];
        }
        return acc;
      }, [] as WarnDisplayMode[]);
    }
    return [];
  } catch {
    return [mode] as WarnDisplayMode[];
  }
};
