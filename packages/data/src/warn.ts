/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import lodash from 'lodash';
import Database from './database';
import { HighlightObject } from './highlight-object';
import {
  getShapeCenter,
  getShapeType,
  makeId,
  makeObjectId,
} from './object-item';
import {
  DEFAULT_PROCESSED_WARN_TIME,
  DEFAULT_UNPROCESSED_WARN_TIME,
  WarnTabConfig,
} from './scenes/running-state';
import { WarnConfig } from './scenes/warning-scene';
import { HIGHLIGHT_CUSTOM } from './style-config';
import { SELECT_ICON } from './track-data';
import { WarnDisplayMode, WarnSettingList } from './warn-setting';

dayjs.extend(duration);

export type WarnTypeData = {
  type: string;
  title: string;
}[];

export const convertWarnTypeDataToOptions = (
  data: WarnTypeData,
): Array<{ label: string; value: string }> =>
  data.map(({ type, title }) => ({
    label: title,
    value: type,
  }));

export const getWarnTypeTitle = (
  typeData: WarnTypeData,
  type: string,
): string => {
  const data = typeData.find((item) => item.type === type);
  return data ? data.title : type;
};

export enum WarnConfirmStatus {
  CONFIRM = 'CONFIRM',
  NOT_CONFIRM = 'NOT_CONFIRM',
  NOT_WARN = 'NOT_WARN',
  SHELVE = 'SHELVE',
  AUTO_IGNORE = 'AUTO_IGNORE',
}

export interface WarnConfirmConfig {
  warnConfirmOptions?: WarnConfirmStatus[];
}

export const DEFAULT_WARN_CONFIRM_OPTIONS: WarnConfirmStatus[] = [
  WarnConfirmStatus.CONFIRM,
  WarnConfirmStatus.SHELVE,
  WarnConfirmStatus.NOT_WARN,
];

export interface WarnDetail {
  id: string;
  warnId: string;
  deviceId: string;
  ptype: string;
  pname: string;
  otype: string;
  oname: string;
  vprop: string;
  abnormalType: string;
  description: string;
  startTime: string;
  endTime: string;
  isKey: boolean;
  confirmStatus: WarnConfirmStatus;
  rank?: number;
  shape?: string;
  name?: string;
  typeName?: string;
  displayMode?: WarnDisplayMode[];
  storageTime?: string;
  endStatus?: number;
}

export enum WarnPrimaryType {
  REALTIME = 'real_time',
  ASSESSMENT = 'assessment',
  MESSAGE = 'message',
}

export interface OperationList {
  time?: string;
  type?: WarnConfirmStatus;
  desc?: string;
  operator?: string;
}

export interface WarnInfoItem {
  id: string;
  primaryType: WarnPrimaryType;
  secondType: string;
  secondTypeName: string;
  rank: number;
  startTime: string;
  endTime: string;
  createTime: string;
  duration: [number | null, string];
  description: string;
  workOrderStatus: string;
  confirmStatus: WarnConfirmStatus;
  details: WarnDetail[];
  eventId: string;
  eventName: string;
  remark: string;
  dealWithRemark?: string;
  dealWithTime?: string;
  operator?: string;
  shelveTime?: string;
  source?: string;
  displayMode?: WarnDisplayMode[];
  classId?: string;
  storageTime?: string;
  groupIndex?: number;
  operationList?: OperationList[];
  orderId?: string;
  orderCode?: string;
  orderTitle?: string;
  endStatus?: number; // 0: 持续中, 1: 已结束
}

export type ChartWarnInfo = Pick<
  WarnInfoItem,
  | 'id'
  | 'startTime'
  | 'endTime'
  | 'createTime'
  | 'endStatus'
  | 'description'
  | 'remark'
  | 'primaryType'
  | 'secondTypeName'
  | 'rank'
>;

export interface UpdateWarnInfo {
  id: string;
  primaryType: WarnPrimaryType;
  secondType: string;
  secondTypeName: string;
  rank: number;
  startTime: string;
  endTime: string;
  createTime: string;
  duration: [number | null, string];
  description: string;
  confirmStatus: WarnConfirmStatus;
  source?: string;
  displayMode?: WarnDisplayMode[];
  classId?: string;
  eventId?: string;
  storageTime?: string;
  endStatus?: number; // 0: 持续中, 1: 已结束
}

export interface WarnSecondType {
  type: string;
  typeName: string;
  primaryType: string;
}

export type WarnInfoList = WarnInfoItem[];

export type WarnStatusCounts = {
  -readonly [K in keyof typeof WarnConfirmStatus]: number;
};

export const getRecordDetailText = (
  _: WarnDetail[],
  record: WarnInfoItem,
): string => lodash.uniq(record.details.map((item) => item.name)).join();

export const getDurationTime = (
  startTime: Dayjs | string | undefined,
  endTime: Dayjs | string | undefined,
): [duration: number | null, durationStr: string] => {
  if (!startTime || !endTime) return [null, '-'];
  const hours = dayjs(endTime).diff(startTime, 'hours');
  const hoursStr = hours ? `${hours}小时` : '';
  const durationValue: duration.Duration = dayjs.duration(
    dayjs(endTime).diff(startTime),
  );
  const minutes = durationValue.minutes();
  const minutesStr = minutes ? `${minutes}分钟` : '';
  let durationStr = `${hoursStr}${minutesStr}` || '';
  const seconds = durationValue.seconds();
  if (durationStr === '' && seconds > 0) durationStr = `${seconds}秒钟`;
  return [dayjs(endTime).diff(startTime), durationStr];
};

export const getDurationDate = (
  startTime: Dayjs | string | undefined,
  endTime: Dayjs | string | undefined,
): [duration: number | null, durationStr: string] => {
  if (!startTime || !endTime) return [null, '-'];
  const duration = dayjs(endTime).diff(startTime);
  const durationDays = dayjs(endTime).diff(startTime, 'days');
  const durationDaysStr = durationDays ? `${durationDays}天` : '';
  return [duration, durationDaysStr];
};

export const getWarnConfirmStatusName = (status: string): string => {
  switch (status) {
    case WarnConfirmStatus.NOT_CONFIRM:
      return '待确认';
    case WarnConfirmStatus.CONFIRM:
      return '已确认';
    case WarnConfirmStatus.NOT_WARN:
      return '已忽略';
    case WarnConfirmStatus.SHELVE:
      return '搁置';
    case WarnConfirmStatus.AUTO_IGNORE:
      return '自动忽略';
    default:
      return status;
  }
};

export const getWarnConfirmStatusOptionName = (value: string): string => {
  switch (value) {
    case WarnConfirmStatus.CONFIRM:
      return '确认';
    case WarnConfirmStatus.SHELVE:
      return '搁置';
    case WarnConfirmStatus.NOT_WARN:
      return '忽略';
    case WarnConfirmStatus.AUTO_IGNORE:
      return '自动忽略';
    default:
      return value;
  }
};

export const shelveTimeOptions = [
  {
    label: '5分钟',
    value: 5,
  },
  {
    label: '1小时',
    value: 60,
  },
  {
    label: '3小时',
    value: 180,
  },
  {
    label: '6小时',
    value: 360,
  },
  {
    label: '1天',
    value: 1440,
  },
  {
    label: '3天',
    value: 4320,
  },
];

export interface WarnFormValues {
  startTime?: string;
  endTime?: string;
  warnType?: string;
  warnLevel?: number;
  deviceId?: string;
  orderId?: string;
  warnSource?: string;
  type?: WarnPrimaryType;
  deviceTitle?: string;
  warnConfirmStatus?: WarnConfirmStatus;
  classId?: string;
}

export const getShelveValue = (
  dealWithTime?: string,
  shelveTime?: string,
): number | undefined => {
  if (!dealWithTime || !shelveTime) return undefined;
  return dayjs(shelveTime).diff(dealWithTime, 'minutes');
};

export const warnConfirmStatusOptions = Object.values(WarnConfirmStatus).map(
  (key) => ({
    label: getWarnConfirmStatusName(key),
    value: key,
  }),
);

export interface DeviceWarningType {
  [deviceId: string]: WarnDetail[];
}

export const formatDevicesWarns = (
  originalData: DeviceWarningType,
  warnDetailList: WarnDetail[],
): DeviceWarningType => {
  const data: DeviceWarningType = originalData;
  warnDetailList.forEach((detail) => {
    const currentData = data[detail.deviceId];
    if (currentData) {
      currentData.push(detail);
    } else {
      data[detail.deviceId] = [detail];
    }
  });
  return data;
};

export const formatIndicateWarnsMap = (
  originalData: DeviceWarningType,
  warnInfoList: WarnInfoList,
): DeviceWarningType => {
  const data: DeviceWarningType = originalData;
  warnInfoList.forEach((warnItem) => {
    warnItem.details.forEach((detail) => {
      const indicatorId = makeObjectId(detail.otype, detail.oname);
      const currentData = data[indicatorId];
      if (currentData) {
        currentData.push(detail);
      } else {
        data[indicatorId] = [detail];
      }
    });
  });
  return data;
};

export function displayBlink(warnDetail: WarnDetail | WarnInfoItem): boolean {
  return !!warnDetail.displayMode?.includes(WarnDisplayMode.FLASH);
}

export function displayMessageBar(
  warnDetail: WarnDetail | WarnInfoItem,
): boolean {
  return !!warnDetail.displayMode?.includes(WarnDisplayMode.MSG_BAR);
}

export function displayTimeline(
  warnDetail: WarnDetail | WarnInfoItem,
): boolean {
  return !!warnDetail.displayMode?.includes(WarnDisplayMode.TIMELINE);
}

export type onSuccessCallback = (params: {
  warnIdList: string[];
  confirmStatus: WarnConfirmStatus;
  note?: string;
  // shelveTime as minutes
  shelveTime?: number;
}) => void;

export interface WarnGroupDisplayConfig {
  enable: number;
  itemTimeDiff: number;
  groupTimeDiff: number;
}

export function reorderGroupIndexes(arr: number[]): number[] {
  let count = 1;
  return arr.map((value, index, array) => {
    if (array.length <= 1) return 0;
    if (index === 0) {
      if (value !== array[index + 1]) count = 0;
      return count;
    }

    if (value !== array[index - 1]) {
      if (index === array.length - 1) return 0;
      if (value !== array[index + 1]) {
        count = 0;
        return 0;
      }
      count += 1;
    }

    return count;
  });
}

export const refreshWarnItemGroupIndexes = (
  list: WarnInfoItem[],
  itemTimeDiff: number,
  groupTimeDiff: number,
): WarnInfoItem[] => {
  let blockStartItemIndex: number = 0;
  let currentBlockIndex: number = 1;
  if (list.length < 2) return list;

  const blockIndexes: number[] = [];
  blockIndexes.push(currentBlockIndex);
  for (let i = 1; i < list.length; i += 1) {
    const blockDiff: number = dayjs(list[blockStartItemIndex].startTime).diff(
      dayjs(list[i].startTime),
      'minutes',
      true,
    );

    if (blockDiff > groupTimeDiff) {
      currentBlockIndex += 1;
      blockIndexes.push(currentBlockIndex);
      blockStartItemIndex = i;
      continue;
    }

    const previousItemDiff: number = dayjs(list[i - 1].startTime).diff(
      dayjs(list[i].startTime),
      'minutes',
      true,
    );
    if (previousItemDiff > itemTimeDiff) {
      currentBlockIndex += 1;
      blockStartItemIndex = i;
    }
    blockIndexes.push(currentBlockIndex);
  }

  const modifiedBlockIndexes = reorderGroupIndexes(blockIndexes);
  return list.map((obj, index) => ({
    ...obj,
    groupIndex: modifiedBlockIndexes[index],
  }));
};

function isWarnTabConfig(
  config: WarnTabConfig | WarnConfig | undefined,
): config is WarnTabConfig {
  return (config as WarnTabConfig).warnLevel !== undefined;
}

export const getWarnListByConfig = (
  warnList: WarnInfoList,
  config: WarnTabConfig | WarnConfig | undefined,
): WarnInfoList => {
  const warnTime = (config as WarnTabConfig).filterRules?.timeRange;
  const unprocessedWarnTime =
    config?.unprocessedWarnTime ?? warnTime ?? DEFAULT_UNPROCESSED_WARN_TIME;
  const unprocessedWarnTimeUnit =
    config && 'unprocessedWarnTimeUnit' in config
      ? config.unprocessedWarnTimeUnit
      : 'hours';

  const processedWarnTime =
    config?.processedWarnTime ?? warnTime ?? DEFAULT_PROCESSED_WARN_TIME;
  const processedWarnTimeUnit =
    config && 'processedWarnTimeUnit' in config
      ? config.processedWarnTimeUnit
      : 'hours';

  const currentTime = dayjs();

  const getTimeLimit = (confirmStatus: WarnConfirmStatus) =>
    confirmStatus === WarnConfirmStatus.NOT_CONFIRM
      ? unprocessedWarnTime
      : processedWarnTime;

  const getTimeUnit = (confirmStatus: WarnConfirmStatus) =>
    confirmStatus === WarnConfirmStatus.NOT_CONFIRM
      ? unprocessedWarnTimeUnit
      : processedWarnTimeUnit;

  let showMaxCount: number | undefined;

  const filteredList = warnList
    .filter((item) => {
      // 通过config过滤无关警告
      let filterFlag = true;
      if (isWarnTabConfig(config)) {
        // 是否满足警告等级
        if (filterFlag) {
          filterFlag = config.warnLevel?.includes(item.rank) ?? filterFlag;
        }
        if (config.filterRules) {
          // 是否满足警告类型
          if (filterFlag) {
            filterFlag =
              config.filterRules.warnType?.includes(item.secondType) ??
              filterFlag;
          }
          showMaxCount = config.filterRules.showMaxCount;
          // 是否满足警告状态
          if (filterFlag) {
            filterFlag =
              config.filterRules.warnStatus?.includes(item.confirmStatus) ??
              filterFlag;
          }
        }
      }
      return filterFlag;
    })
    .filter((item) => {
      const diff = currentTime.diff(
        dayjs(item.startTime),
        getTimeUnit(item.confirmStatus),
      );
      return diff < getTimeLimit(item.confirmStatus);
    });

  const sortedList = filteredList.sort((a, b) =>
    dayjs(b.startTime).diff(dayjs(a.startTime)),
  );

  const sliceList =
    showMaxCount === undefined ? sortedList : sortedList.slice(0, showMaxCount);

  return sliceList;
};

export const generateHighlightObject = (
  devices: WarnDetail[],
  keyDeviceWarnDetail: WarnDetail,
): HighlightObject[] => {
  const deviceCoordinate = getShapeCenter(keyDeviceWarnDetail.shape ?? '');
  const relatedDevices: HighlightObject[] = [];
  devices.forEach((item) => {
    if (item.shape && item.oname !== keyDeviceWarnDetail.oname) {
      const indicatorShape = getShapeCenter(item.shape);
      const shape = `LINESTRING(${deviceCoordinate[0]} ${deviceCoordinate[1]},${indicatorShape[0]} ${indicatorShape[1]})`;
      relatedDevices.push(
        ...[
          {
            oname: '',
            otype: '',
            id: `Line${item.id}`,
            shape,
            highlightColor: '#ffbf6b',
            shapeType: 'LINE',
            lineType: 'dash',
            highlightType: HIGHLIGHT_CUSTOM,
          },
          {
            oname: '',
            otype: '',
            id: item.id,
            shape: item.shape,
            highlightIcon: SELECT_ICON,
            shapeType: getShapeType(item.shape),
            highlightType: HIGHLIGHT_CUSTOM,
            highlightColor: '#ffbf6b',
            highlightSize: 34,
          },
        ],
      );
    }
  });
  return relatedDevices;
};

export const formatUpdateWarnInfoItem = (
  item: any,
  warnTypeList?: { [key: string]: WarnSettingList },
): UpdateWarnInfo => {
  const id = makeId(item.warn_category, item.warn_type);
  let displayMode: WarnDisplayMode[] = [];

  if (warnTypeList) {
    const warnType = warnTypeList?.[id];
    displayMode = warnType?.displayMode ?? [];
  }

  const warnEndTime =
    item.end_status === 0
      ? dayjs().format('YYYY-MM-DD HH:mm:ss')
      : item.end_time;

  return {
    id: item.warn_id ?? '',
    secondType: item.warn_type ?? '',
    secondTypeName: item.title ?? '',
    primaryType: item.type ?? '',
    rank: Number(item.level),
    startTime: item.start_time ?? '',
    endTime: warnEndTime,
    createTime: item.create_time ?? '',
    duration:
      item.warn_type === WarnPrimaryType.ASSESSMENT
        ? getDurationDate(item.start_time, warnEndTime)
        : getDurationTime(item.start_time, warnEndTime),
    description: item.main_reason ?? '',
    confirmStatus: item.operation_type ?? WarnConfirmStatus.NOT_CONFIRM,
    source: item.warn_source,
    classId: item.class_id,
    eventId: item.event_id ?? '',
    storageTime: item.stime ?? '',
    displayMode: item.displayMode ?? displayMode,
    endStatus: item.end_status,
  };
};

export const formatWarnDetailItem = (
  item: any,
  database: Database,
  record?: any,
): WarnDetail | undefined => {
  const deviceId = makeObjectId(item.ptype, item.pname);
  const device = database.getDeviceById(deviceId);
  if (device && !device.dataAccess) return undefined;

  const deviceTypeName = database.getPropertyInfo(item.otype)?.title;
  const warnDetail: WarnDetail = {
    id: item.log_detailed_id ?? item.detail_id,
    warnId: item.warn_id,
    deviceId,
    ptype: item.ptype ?? '',
    pname: item.pname ?? '',
    otype: item.otype ?? '',
    oname: item.oname ?? '',
    vprop: item.vprop ?? '',
    confirmStatus: record?.operation_type ?? WarnConfirmStatus.NOT_CONFIRM,
    abnormalType: item.abnormal_type ?? '',
    description: item.description ?? '',
    startTime: item.start_time ?? '',
    endTime: item.end_time ?? '',
    isKey: item.iskey === 1,
    rank: item.level,
    shape: device?.shape,
    name: device?.title ?? '',
    typeName: deviceTypeName ?? '',
    storageTime: item.stime ?? '',
    endStatus: record?.end_status ?? 0,
    displayMode: record?.displayMode,
  };

  return warnDetail;
};

// 搁置时间单位枚举
export enum ShelveTimeUnit {
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
}

// 获取搁置时间单位名称
export const getShelveTimeUnitName = (unit: ShelveTimeUnit): string => {
  switch (unit) {
    case ShelveTimeUnit.MINUTE:
      return '分钟';
    case ShelveTimeUnit.HOUR:
      return '小时';
    case ShelveTimeUnit.DAY:
      return '天';
    default:
      return unit;
  }
};

// 搁置时间单位选项
export const shelveTimeUnitOptions = Object.values(ShelveTimeUnit).map(
  (unit) => ({
    label: getShelveTimeUnitName(unit),
    value: unit,
  }),
);

// 单位与分钟互转
export function shelveTimeToMinute(
  value: number,
  unit: ShelveTimeUnit,
): number {
  if (unit === ShelveTimeUnit.MINUTE) return value;
  if (unit === ShelveTimeUnit.HOUR) return value * 60;
  if (unit === ShelveTimeUnit.DAY) return value * 1440;
  return value;
}

export function minuteToShelveTime(minute: number | null | undefined): {
  value: number | undefined;
  unit: ShelveTimeUnit;
} {
  if (typeof minute !== 'number' || Number.isNaN(minute))
    return { value: undefined, unit: ShelveTimeUnit.MINUTE };
  if (minute < 60) return { value: minute, unit: ShelveTimeUnit.MINUTE };
  if (minute < 1440)
    return { value: Math.round(minute / 60), unit: ShelveTimeUnit.HOUR };
  return { value: Math.round(minute / 1440), unit: ShelveTimeUnit.DAY };
}
