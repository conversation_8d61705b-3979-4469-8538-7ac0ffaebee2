/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import Device from '../device';
import { snakeToCamelCase } from '../string';
import { getWarnConfirmStatusName, WarnInfoItem, WarnInfoList } from '../warn';
import {
  getWarningLevelName,
  WARNING_LEVEL_CUSTOM,
  WARNING_LEVEL_CUSTOM_NAME,
  WARNING_LEVEL_OTHERS,
  WARNING_LEVEL_OTHERS_NAME,
  WarningLevel,
} from './warn-level';

export const WARNING_CATEGORY_TYPE = 'type';
export const WARNING_CATEGORY_SOURCE = 'source';

export const WARNING_UNPROCESSED = '全部未处理';
export const WARNING_PROCESSED = '已处理';

export enum WarningProcess {
  PROCESSED = WARNING_PROCESSED,
  UNPROCESSED = WARNING_UNPROCESSED,
}

export interface TabItem {
  name: string;
  count: number;
}

export const warningLevels = [
  {
    value: WarningLevel.LevelOne,
    label: getWarningLevelName(WarningLevel.LevelOne, true),
  },
  {
    value: WarningLevel.LevelTwo,
    label: getWarningLevelName(WarningLevel.LevelTwo, true),
  },
  {
    value: WarningLevel.LevelThree,
    label: getWarningLevelName(WarningLevel.LevelThree, true),
  },
  {
    value: WARNING_LEVEL_OTHERS,
    label: WARNING_LEVEL_OTHERS_NAME,
  },
  {
    value: WARNING_LEVEL_CUSTOM,
    label: WARNING_LEVEL_CUSTOM_NAME,
  },
];

export const classifyByRank = (
  data: WarnInfoList,
  observationList?: Array<{ otype: string; oname: string }>,
): Record<string, WarnInfoList> => {
  const classifiedData: Record<string, WarnInfoList> = {
    [WarningLevel.LevelOne]: [],
    [WarningLevel.LevelTwo]: [],
    [WarningLevel.LevelThree]: [],
    [WARNING_LEVEL_OTHERS]: [],
    [WARNING_LEVEL_CUSTOM]: [],
  };

  data.forEach((item) => {
    switch (item.rank) {
      case WarningLevel.LevelZero:
        classifiedData[WarningLevel.LevelOne].push({ ...item });
        break;
      case WarningLevel.LevelOne:
      case WarningLevel.LevelTwo:
      case WarningLevel.LevelThree:
        classifiedData[item.rank.toString()].push({ ...item });
        break;
      default:
        classifiedData.others.push({ ...item });
        break;
    }

    if (observationList) {
      for (let i = 0; i < item.details.length; i += 1) {
        const warnDetail = item.details[i];
        if (
          observationList.find(
            (observation) =>
              warnDetail.otype === observation.otype &&
              warnDetail.oname === observation.oname,
          )
        ) {
          classifiedData.custom.push(item);
          break;
        }
      }
    }
  });

  return classifiedData;
};

export const aggregateWarningByOwn = (
  classifiedData: Record<string, WarnInfoList>,
  filterBy: string,
  classTypeOptions: Array<{ label: string; value: string }>,
): Record<string, Record<string, WarnInfoList>> => {
  const convertedFilterBy = snakeToCamelCase(filterBy);

  return Object.keys(classifiedData).reduce(
    (result, rank) => {
      const aggregatedByRank = classifiedData[rank].reduce(
        (acc, item) => {
          const rawValue = item[convertedFilterBy as keyof WarnInfoItem];
          let key: string;

          if (convertedFilterBy === 'confirmStatus') {
            key = getWarnConfirmStatusName(String(rawValue));
          } else if (typeof rawValue !== 'string') {
            key = '未分类';
          } else if (convertedFilterBy === 'classId') {
            key =
              classTypeOptions.find((option) => option.value === rawValue)
                ?.label || '未分类';
          } else {
            key = rawValue;
          }

          const updatedAcc = { ...acc };
          updatedAcc[key] = updatedAcc[key]
            ? [...updatedAcc[key], item]
            : [item];

          return updatedAcc;
        },
        {} as Record<string, WarnInfoList>,
      );

      return { ...result, [rank]: aggregatedByRank };
    },
    {} as Record<string, Record<string, WarnInfoList>>,
  );
};

export const aggregateWarningByDevice = (
  classifiedData: Record<string, WarnInfoList>,
  filterBy: string,
  db: Database,
): Record<string, Record<string, WarnInfoList>> =>
  Object.keys(classifiedData).reduce(
    (result, rank) => {
      const aggregatedByRank = classifiedData[rank].reduce(
        (acc, item) => {
          const mainDevice = item.details.find((detail) => detail.isKey);
          let key = '未分类';

          if (mainDevice) {
            const device = db.getDeviceById(mainDevice.deviceId);
            if (
              device &&
              filterBy in device &&
              typeof device[filterBy as keyof Device] === 'string'
            ) {
              key = device[filterBy as keyof Device] as string;
            }
          }

          const updatedAcc = { ...acc };
          updatedAcc[key] = updatedAcc[key]
            ? [...updatedAcc[key], item]
            : [item];

          return updatedAcc;
        },
        {} as Record<string, WarnInfoList>,
      );

      return { ...result, [rank]: aggregatedByRank };
    },
    {} as Record<string, Record<string, WarnInfoList>>,
  );
