/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

const unspecifiedWarningLevelName = '未知警告';
const unspecifiedWarningLevelColor = '#ffffff';

export const WARNING_LEVEL_OTHERS = 'others';
export const WARNING_LEVEL_OTHERS_NAME = '其它';
export const WARNING_LEVEL_CUSTOM = 'custom';
export const WARNING_LEVEL_CUSTOM_NAME = '自定义';

export enum WarningLevel {
  /** 特级警告 */
  LevelZero = 0,
  /** 一级警告 */
  LevelOne = 1,
  /** 二级警告 */
  LevelTwo,
  /** 三级警告 */
  LevelThree,
  /** 四级警告 */
  LevelFour,
  /** 五级警告 */
  LevelFive,
}

export const WarningLevelNameMap = new Map<WarningLevel, string>([
  [WarningLevel.LevelZero, '特级警告'],
  [WarningLevel.LevelOne, '一级警告'],
  [WarningLevel.LevelTwo, '二级警告'],
  [WarningLevel.LevelThree, '三级警告'],
  [WarningLevel.LevelFour, '四级警告'],
  [WarningLevel.LevelFive, '五级警告'],
]);

export const getWarningLevelName = (
  level: WarningLevel,
  isAbbreviation: boolean = false,
): string => {
  let warningLevelName =
    WarningLevelNameMap.get(level) ?? unspecifiedWarningLevelName;

  if (isAbbreviation) {
    warningLevelName = warningLevelName.replace('警告', '');
  }

  return warningLevelName;
};

export const WarningLevelColorMap = new Map<WarningLevel, string>([
  [WarningLevel.LevelZero, '#a8071a'], // 特级警告
  [WarningLevel.LevelOne, '#f5222d'], // 一级警告
  [WarningLevel.LevelTwo, '#fa8c16'], // 二级警告
  [WarningLevel.LevelThree, '#fadb14'], // 三级警告
  [WarningLevel.LevelFour, '#1677ff'], // 四级警告
  [WarningLevel.LevelFive, '#52c41a'], // 五级警告
]);

export const getWarningLevelColor = (level: WarningLevel): string =>
  WarningLevelColorMap.get(level) ?? unspecifiedWarningLevelColor;

export const warningLevelOptions = Array.from(WarningLevelNameMap).map(
  ([value, label]) => ({
    label,
    value,
  }),
);

export const extendedWarningLevelOptions = Array.from(WarningLevelNameMap).map(
  ([value, label]) => ({
    label,
    value,
    color: WarningLevelColorMap.get(value) ?? unspecifiedWarningLevelColor,
    abbreviation: label.replace('警告', ''),
  }),
);

export enum warningLevelMethodEnum {
  ByWarnType = 'ByWarnType',
  ByLevelAsScada = 'ByLevelAsScada',
  ByMaxLevelWithScada = 'ByMaxLevelWithScada',
  ByMinLevelWithScada = 'ByMinLevelWithScada',
}

export const warningLevelMethodMap = new Map<string, string>([
  [warningLevelMethodEnum.ByWarnType, '仅按警告类型级别'],
  [warningLevelMethodEnum.ByLevelAsScada, '仅按监测量警告级别'],
  [warningLevelMethodEnum.ByMaxLevelWithScada, '与监测量警告等级比较取高'],
  [warningLevelMethodEnum.ByMinLevelWithScada, '与监测量警告等级比较取低'],
]);

export const warningLevelMethodOptions = Array.from(warningLevelMethodMap).map(
  ([value, label]) => ({
    label,
    value,
  }),
);
