/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '../database';
import { makeId, makeObjectId } from '../object-item';
import { WarnConfig } from '../scenes/warning-scene';
import {
  formatUpdateWarnInfoItem,
  formatWarnDetailItem,
  getWarnListByConfig,
  UpdateWarnInfo,
  WarnDetail,
  WarnInfoList,
} from '../warn';
import { WarnDisplayMode, WarnSettingList } from '../warn-setting';

export interface LatestWarnRecords {
  added: any[];
  changed: any[];
}

export interface LatestWarnDetails {
  added: any[];
  changed: any[];
}

export interface LatestWarnWebSocketData {
  records: LatestWarnRecords;
  details: LatestWarnDetails;
}

export interface LatestWarnWebSocketList {
  addedRecords: UpdateWarnInfo[];
  changedRecords: UpdateWarnInfo[];
  addedDevices: WarnDetail[];
  changedDevices: WarnDetail[];
}

const formatMap = (details: any[], database: Database) => {
  const object: { [key: string]: any[] } = {};
  details.forEach((item) => {
    const deviceInfo = database.getDevice(item.ptype, item.pname);
    if (deviceInfo && !deviceInfo.dataAccess) return;
    if (!object[item.warn_id]) {
      object[item.warn_id] = [];
    }
    object[item.warn_id].push(item);
  });
  return object;
};

export const formatAddWarnList = (
  records: any,
  details: any,
  warnTypeList: { [key: string]: WarnSettingList },
  database: Database,
): {
  deviceList: WarnDetail[];
  recordsList: UpdateWarnInfo[];
  deviceMap: { [key: string]: WarnDetail[] };
  recordsMap: { [key: string]: UpdateWarnInfo };
} => {
  const recordsMap: { [key: string]: UpdateWarnInfo } = {};
  const detailsMap = formatMap(details, database);
  const deviceMap: { [key: string]: WarnDetail[] } = {};
  records.forEach((item: any) => {
    const id = makeId(item.warn_category, item.warn_type);
    let displayMode: WarnDisplayMode[] = [];

    if (warnTypeList) {
      const warnType = warnTypeList?.[id];
      displayMode = warnType?.displayMode ?? [];
    }
    const warnInfo = item;
    warnInfo.displayMode = displayMode;
    recordsMap[item.warn_id] = formatUpdateWarnInfoItem(warnInfo);
    const details = detailsMap[item.warn_id];
    if (details && details.length > 0) {
      details.forEach((detail) => {
        const warnDetail = formatWarnDetailItem(detail, database, warnInfo);
        if (warnDetail) {
          if (deviceMap[item.warn_id]) {
            deviceMap[item.warn_id].push(warnDetail);
          } else {
            deviceMap[item.warn_id] = [warnDetail];
          }
        }
      });
    }
  });
  return {
    deviceMap,
    deviceList: Object.values(deviceMap).flat(),
    recordsMap,
    recordsList: Object.values(recordsMap).flat(),
  };
};

export const formatWsRecordsList = (records: LatestWarnRecords) => {
  const { added, changed } = records;

  const addedResult = added.map((item) => formatUpdateWarnInfoItem(item));
  const changedResult = changed.map((item) => formatUpdateWarnInfoItem(item));

  return {
    added: addedResult,
    changed: changedResult,
  };
};

export const formatWsDetailsList = (
  details: LatestWarnDetails,
  database: Database,
  warnConfig: WarnConfig | undefined,
) => {
  const { added, changed } = details;
  const addedResult: WarnDetail[] = [];
  added.forEach((item, index) => {
    const warnDetail = formatWarnDetailItem(item, database, index);
    if (warnDetail) addedResult.push(warnDetail);
  });
  const changedResult: WarnDetail[] = [];
  changed.forEach((item, index) => {
    const warnDetail = formatWarnDetailItem(item, database, index);
    if (warnDetail) changedResult.push(warnDetail);
  });

  return {
    added: getWarnListByConfig(
      addedResult as unknown as WarnInfoList,
      warnConfig,
    ) as unknown as WarnDetail[],
    changed: getWarnListByConfig(
      changedResult as unknown as WarnInfoList,
      warnConfig,
    ) as unknown as WarnDetail[],
  };
};

export class SocketIndicatorWarn {
  // 指定监测量, undefined表示不指定 Set<indicatorId>
  private indicatorSet: Set<string> | undefined;

  // 所有的 Map<detailId, WarnDetail>
  private detailMap: Map<string, WarnDetail> = new Map();

  // 监测量和对应警告detailId信息 Map<indicatorId, Set<detailId>>
  private indicatorWarnMap: Map<string, Set<string>> = new Map();

  // Map<warnId, Set<detailId>>
  private warnAndDetailIdMap: Map<string, Set<string>> = new Map();

  private indicatorKeysChange(indicatorKeys?: string[]) {
    if (indicatorKeys && indicatorKeys.length > 0) {
      this.indicatorSet = new Set(indicatorKeys);
    } else {
      const removeKeys = this.indicatorSet?.values()
        ? [...this.indicatorSet.values()]
        : [];
      removeKeys.forEach((key) => this.indicatorWarnMap.delete(key));
      this.indicatorSet = undefined;
    }
  }

  private pushDetailMap(details: WarnDetail[]) {
    details.forEach((detail) => {
      const indicatorId = makeObjectId(detail.otype, detail.oname);
      // 如果指定了监测量，只存指定监测量的相关警告，否则全部存
      if (
        this.indicatorSet?.has(indicatorId) ||
        this.indicatorSet === undefined
      ) {
        this.detailMap.set(detail.id, detail);
        const set = this.indicatorWarnMap.get(indicatorId) ?? new Set();
        this.indicatorWarnMap.set(indicatorId, set.add(detail.id));
        const detailIdSet =
          this.warnAndDetailIdMap.get(detail.warnId) ?? new Set();
        this.warnAndDetailIdMap.set(detail.warnId, detailIdSet.add(detail.id));
      }
    });
  }

  initialWarnList(
    data: WarnInfoList | undefined,
    indicatorKeys: string[] | undefined,
  ) {
    if (data?.length) {
      this.indicatorKeysChange(indicatorKeys);
      const details = data.flatMap((item) => item.details ?? []);
      this.pushDetailMap(details);
    }
  }

  getSocketWarn(data: LatestWarnWebSocketList) {
    const { addedDevices, addedRecords, changedDevices, changedRecords } = data;
    const combinedDetails = [...addedDevices, ...changedDevices];
    this.pushDetailMap(combinedDetails);
    const combinedWarnInfo = [...addedRecords, ...changedRecords];
    combinedWarnInfo.forEach((item) => {
      const detailsIds = this.warnAndDetailIdMap.get(item.id);
      detailsIds?.forEach((detailId) => {
        if (this.detailMap.has(detailId)) {
          if (item.endStatus === 0) {
            const newDetail = this.detailMap.get(detailId)!;
            this.detailMap.set(detailId, {
              ...newDetail,
              endStatus: item.endStatus,
            });
          } else {
            this.detailMap.delete(detailId);
          }
        }
      });
    });

    const indicatorWarn: Map<string, WarnDetail[]> = new Map();
    this.indicatorWarnMap.forEach((detailIds, indicatorId) => {
      const details: WarnDetail[] = [];
      detailIds.forEach((id) => {
        const detail = this.detailMap.get(id);
        if (detail) details.push(detail);
      });
      if (details.length) indicatorWarn.set(indicatorId, details);
    });
    return indicatorWarn;
  }
}
