/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface WaterOutageInfo {
  id: string;
  oname: string;
  otype: string;
  title: string;
  dmaList: {
    id: string;
    name: string;
    shape: string;
  }[];
  waterMeterList: {
    id: string;
    name: string;
    shape: string;
  }[];
  type: string;
  creator: string;
  planStartTime: string;
  planEndTime: string;
  status: string;
}

export interface Options {
  label: string;
  value: string;
  isDefault?: boolean;
  dependentOptions?: string[];
}

/** 配置选项 */
export interface WaterNoticeOption {
  type?: string; // 组件类型
  label?: string; // 展示名称
  name?: string; // 字段值
  colSpan?: number; // 栅格占位
  rules?: any[]; // 校验规则
  options?: Options[]; // 下拉选项
  dependsOn?: string; // 依赖字段
  dependencies?: string[]; // 依赖字段值
}
