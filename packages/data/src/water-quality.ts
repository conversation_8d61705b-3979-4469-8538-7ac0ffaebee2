/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface WaterQualityReportListParams {
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  stationName?: string;
  reportCode?: string;
  parameterName?: string;
  sampleCode?: string;
  ptype?: string;
  pname?: string;
  deviceId?: string;
  qualified?: string;
}

export interface WaterQualityReportList {
  reportId: string | undefined;
  reportTime: string | undefined;
  sampleCode: string | undefined;
  stationName: string | undefined;
  stationAddress: string | undefined;
  stationCode: string | undefined;
  clientName: string | undefined;
  clientAddress: string | undefined;
  clientContact: string | undefined;
  clientPhone: string | undefined;
  createBy: string | undefined;
  createTime: string | undefined;
  updateBy: string | undefined;
  updateTime: string | undefined;
  ptype: string | undefined;
  pname: string | undefined;
  deviceTitle: string | undefined;
  shape: string | undefined;
  remark: string | undefined;
}

export interface WaterQualityList {
  resultId: string | undefined;
  sampleCode: string | undefined;
  sampleTime: string | undefined;
  sampleType: string | undefined;
  signStatus: string | undefined;
  parameterName: string | undefined;
  parameterCode: string | undefined;
  result: string | undefined;
  unit: string | undefined;
  evaluationStandard: string | undefined;
  limitValue: string | undefined;
  qualified: string | undefined;
  updateTime: string | undefined;
  remark: string | undefined;
  reportCode: string | undefined;
  stationName: string | undefined;
  ptype: string | undefined;
  shape: string | undefined;
  pname: string | undefined;
  deviceTitle: string | undefined;
}

export interface SampleList {
  stationName: string | undefined;
  sampleCode: string | undefined;
  sampleTime: string | undefined;
  updateTime: string | undefined;
  reportCode: string | undefined;
}
