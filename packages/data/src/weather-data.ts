/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';

export interface WeatherInfo {
  weather: string;
  weatherIconUrl: string;
  maxTemperature: string;
  minTemperature: string;
  date: string;
}

export interface WeatherData {
  currentDay: WeatherInfo;
  forecast: WeatherInfo[];
}
export const getWeekdayName = (date: string): string => {
  const today = dayjs().format('YYYY-MM-DD');
  const inputDate = dayjs(date).format('YYYY-MM-DD');

  if (inputDate === 'Invalid Date') return '';

  if (inputDate === today) {
    return '今天';
  }
  const dayOfWeek = dayjs(date).day();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekdays[dayOfWeek];
};
