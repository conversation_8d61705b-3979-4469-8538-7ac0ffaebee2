/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import Database from './database';
import Device from './device';
import { IndicatorObject } from './indicator';
import { makeObjectId } from './object-item';
import { getWarningLevelName } from './warn/warn-level';

export enum WorkOrderRemindStatus {
  NoRemind = '0', // 不提醒
  Remind = '1', // 提醒
}

/** 工单接口 */
export interface WorkOrder {
  title?: string; // 工单标题
  shape?: string; // 坐标
  creator?: string; // 创建人
  orderCreateTime?: string; // 工单创建时间
  id?: string; // 工单ID
  code?: string; // 工单编号
  status?: string; // 工单状态
  statusName?: string; // 工单状态(前端展示)
  type?: string; // 工单类型
  category?: string; // 工单分组
  content?: string; // 工单内容
  description?: string; // 工单描述
  address?: string; // 地址
  source?: string; // 来源
  level?: string; // 紧急程度
  levelName?: string; // 紧急程度(前端展示)
  department?: string; // 责任部门
  opinion?: string; // 处置意见
  acceptTime?: string; // 接单时间
  arriveTime?: string; // 到场时间
  finishTime?: string; // 销单时间
  stime?: string; // 入库/更新时间
  createTime?: string; // 入库时间
  updateTime?: string; // 更新时间
  deviceList?: string; // 相关设备指标列表
  remark?: string; // 备注
  warningList?: string; // 相关警报列表
  innerId?: string; // 跟三方系统ID对应关系
  eventName?: string; // 关联事件 ID
  eventTitle?: string; // 关联事件名称
  remindState?: WorkOrderRemindStatus; // 提醒状态
  communityName?: string; // 提醒状态
}

export interface Options {
  label: string;
  value: string;
  isDefault?: boolean;
  dependentOptions?: string[];
}

/** 工单配置选项 */
export interface WorkOrderOption {
  type?: string; // 组件类型
  label?: string; // 展示名称
  name?: string; // 字段值
  colSpan?: number; // 栅格占位
  rules?: any[]; // 校验规则
  options?: Options[]; // 下拉选项
  dependsOn?: string; // 依赖字段
  dependencies?: string[]; // 依赖字段值
}

export interface AddWorkOrder {
  type?: string; // 工单类型
  category?: string; // 工单类别
  content?: string; // 工单内容
  level?: string; // 紧急程度
  source?: string; // 工单来源
  department?: string; // 责任部门
  title?: string; // 工单标题
  description?: string; // 工单描述
  deviceList?: string[]; // 相关设备指标列表
  formValuesJson?: string; // 监测量信息JSON
  deviceErrorTime?: string; // 设备异常时间
  finishTimeLimit?: string; // 办结时限
  picFile?: Blob[];
}

export interface WorkOrderRepairDetail {
  otype?: string;
  oname?: string;
  content?: string;
  errorTime?: string;
  orderCreateTime?: string;
  finishTime?: string;
  code?: string;
  creator?: string; // 创建人
}

export interface WorkOrderRepairDetailWithDevice {
  deviceTitle?: string; // 站点名称
  deviceOName?: string; // 站点编号
  indicatorType?: string; // 站点小类
  indicatorLevel?: string; // 监测量等级
  content?: string; // 工单内容
  manufacturer?: string; // 厂商
  samplingTimeStep?: string; // 传输频率
  errorTime?: string; // 异常发生时间
  orderCreateTime?: string; // 工单发起时间
  finishTime?: string; // 工单销单时间
  diffTime?: string; // 办结时长
  code?: string; // 工单编号
  creator?: string; // 发起人
}

export interface AddWorkOrderDeviceIndicator {
  deviceId?: string;
  deviceTitle?: string;
  indicatorIds?: string[];
}

// 生成工单描述
export const generateWorkOrderDesc = (
  device: Device | undefined,
  indicatorList: (IndicatorObject | undefined)[],
  errorTime: string,
  description: string,
): {
  desc: string | undefined;
  descJson: string | undefined;
} => {
  if (!device || !indicatorList) {
    return { desc: undefined, descJson: undefined };
  }

  const deviceName = device?.title ?? '-';
  const deviceId = device?.oname ?? '-';
  const deviceOwner = device?.getPropertyValue('STATION_GRADE') ?? '-';
  const deviceOrganization = device?.organization ?? '-';
  const exceptionTime = dayjs(errorTime).format('YYYY-MM-DD HH:mm:ss');
  const exceptionDesc = description ?? '-';
  const indicators = indicatorList.map((indicator) => ({
    metricsTag: indicator?.oname ?? '-',
    metricsName: indicator?.title ?? '-',
    metricsLevel:
      getWarningLevelName(
        parseInt(indicator?.getPropertyValue('SDVAL_GRADE'), 10),
        true,
      ) ?? '-',
    metricsType: indicator?.indicatorType?.title ?? '-',
  }));

  const descJson = JSON.stringify({
    stationNo: deviceId,
    stationName: deviceName,
    stationOwner: deviceOwner,
    stationDept: deviceOrganization,
    exceptionTime,
    exceptionDesc,
    metricsList: indicators,
  });

  const desc = `站点名称: ${deviceName}
  站点编号: ${deviceId}
  站点归属: ${deviceOwner}
  站点归属部门: ${deviceOrganization}
  异常发生时间: ${exceptionTime}
  异常现象: ${exceptionDesc}
  监测量名称: ${indicators.map((indicator) => indicator.metricsName).join(',')}
  监测量TAGNAME: ${indicators
    .map((indicator) => indicator.metricsTag)
    .join(',')}
  监测量等级: ${indicators.map((indicator) => indicator.metricsLevel).join(',')}
  监测量小类: ${indicators
    .map((indicator) => indicator.metricsType)
    .join(',')}`;

  return { desc, descJson };
};

// 获取设备和指标信息
export const getDeviceAndIndicator = (
  record: WorkOrderRepairDetail,
  db: Database,
) => {
  const indicator = db.getIndicator(record.otype, record.oname);
  const deviceId = makeObjectId(indicator?.ptype ?? '', indicator?.pname ?? '');
  const device = db.getDeviceById(deviceId);
  return { indicator, device };
};

// 转换工单详情数据
export const convertRepairDetailsWithDevice = (
  data: WorkOrderRepairDetail[],
  db: Database,
): WorkOrderRepairDetailWithDevice[] =>
  data.map((item) => {
    const { indicator, device } = getDeviceAndIndicator(item, db);
    return {
      ...item,
      deviceTitle: device?.title,
      deviceOName: device?.oname,
      indicatorType: indicator?.indicatorType?.title,
      indicatorLevel: getWarningLevelName(
        parseInt(indicator?.getPropertyValue('SDVAL_GRADE'), 10),
        true,
      ),
      manufacturer: device?.getPropertyValue('FACTORY'),
      samplingTimeStep: device?.samplingTimeStep,
    };
  });
