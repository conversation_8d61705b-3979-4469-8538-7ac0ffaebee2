{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist/", "sourceMap": true, "strictNullChecks": true, "target": "es6", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["jest"]}, "exclude": ["./node_modules"], "include": ["./src/", "typed.d.ts"], "settings": {"import/resolver": {"node": {"paths": ["."], "extensions": [".js", ".ts", ".d.ts"]}}}}