{"name": "@waterdesk/request", "version": "3.1.0", "description": "> TODO: description", "author": "yechangqing <<EMAIL>>", "homepage": "https://github.com/WaterDesk/storm-web#readme", "license": "ISC", "main": "lib/request.js", "types": "src/index.d.ts", "exports": {"./*": "./src/*"}, "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "publishConfig": {"registry": "https://registry.npmmirror.com/"}, "repository": {"type": "git", "url": "git+https://github.com/WaterDesk/storm-web.git"}, "scripts": {"prettier:write": "prettier --write ./src", "lint:fix": "eslint --fix \"./src/**/*.{ts,js}\"", "prettier": "prettier --check ./src", "lint": "eslint --fix-dry-run \"./src/**/*.{ts,js}\"", "build:tsc": "tsc --build", "jest": "jest", "test": "jest", "coverage": "jest --coverage"}, "bugs": {"url": "https://github.com/WaterDesk/storm-web/issues"}, "dependencies": {"@waterdesk/data": "*", "crypto-js": "^4.2.0", "jsencrypt": "^3.3.2"}, "devDependencies": {"@types/crypto-js": "^4.1.1"}}