/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/**
 * 表格排序顺序类型
 */
export type TableSortOrder = 'ascend' | 'descend' | null;

/**
 * 排序器接口定义
 */
export interface Sorter {
  /** 排序字段 */
  field: string;
  /** 排序顺序 */
  order: TableSortOrder;
}

/**
 * 分页参数接口
 */
export interface PageParams {
  /** 当前页码 */
  current?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 排序配置 */
  sorter?: Sorter;
}

/**
 * 后端排序方法类型
 * - asc: 升序
 * - desc: 降序
 * - null: 不排序
 */
export type SortMethod = 'asc' | 'desc' | null;

/**
 * 排序参数接口
 */
export interface SortParams {
  /** 排序字段 */
  sort_by?: string;
  /** 排序方法 */
  sort_method?: SortMethod;
}

/**
 * 表格排序参数类型
 */
export type TableSortParams = Record<string, TableSortOrder>;

/**
 * 表格排序方法映射
 * 将前端排序方式转换为后端接口排序方式
 */
export const sorterMethodMap: Record<string, SortMethod> = {
  ascend: 'asc',
  descend: 'desc',
  null: null,
};
