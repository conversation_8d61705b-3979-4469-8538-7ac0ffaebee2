/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { postRequest } from '../request';
import { APIResponse } from './api-response';

export interface GetDevicePictureResponse extends APIResponse {
  pictureList?: string[];
}

export async function getDevicePictures(
  deviceId: string,
): Promise<GetDevicePictureResponse> {
  const data: any = await postRequest({
    code: 'watergis/getOnamePicFileList',
    params: {
      otype_oname: deviceId,
    },
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  return {
    status: 'Success',
    pictureList: data.icons,
  };
}
