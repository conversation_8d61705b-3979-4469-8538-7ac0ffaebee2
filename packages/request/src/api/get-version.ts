/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { postRequest } from '../request';
import { APIResponse } from './api-response';

export interface GetVersionResponse extends APIResponse {
  version?: string;
}

export async function getVersion(): Promise<GetVersionResponse> {
  const data: any = await postRequest({
    code: 'getVersion',
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  return {
    status: 'Success',
    version: data.json_msg,
  };
}
