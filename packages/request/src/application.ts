/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ApplicationInfo, ApplicationList } from '@waterdesk/data/application';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetApplicationListResponse extends APIResponse {
  applicationList?: ApplicationList;
}

export const getApplicationList = async (
  keyword?: string,
): Promise<GetApplicationListResponse> => {
  const res: any = await postRequest({
    code: 'portal/getApplicationList',
    params: {
      keyword,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const applicationList: ApplicationList = Array.isArray(
          res.value?.applicationList,
        )
          ? res.value?.applicationList.map(
              (item: any): ApplicationInfo => ({
                id: item.APPLICATION_ID ?? '',
                name: item.APPLICATION_NAME ?? '',
                description: item.APPLICATION_DESC ?? '',
                statCode: item.APPLICATION_STAT_CODE ?? '',
              }),
            )
          : [];
        resolve({
          status: 'Success',
          applicationList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
