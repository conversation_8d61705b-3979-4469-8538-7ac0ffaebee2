/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ClosedValveInfo,
  ClosedValveStatus,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetClosedValveListResponse extends APIResponse {
  closedValveList?: ClosedValveInfo[];
}

const parseValveList = (
  valveList: {
    otype: string;
    oname: string;
    status: string;
    shape: string;
    onmainpipe: number;
    diameter: number;
    valvetype: string;
    description: string;
    tag: string;
    note1: string;
    note2: string;
    note3: string;
    note4: string;
    note5: string;
  }[],
): ClosedValveInfo[] =>
  valveList.map((item) => ({
    diameter: item.diameter,
    mainPipe: !!item.onmainpipe,
    otype: item.otype,
    oname: item.oname,
    shape: item.shape,
    status: item.status as ClosedValveStatus,
    valveType: item.valvetype,
    description: item.description,
    tag: item.tag,
    note1: item.note1,
    note2: item.note2,
    note3: item.note3,
    note4: item.note4,
    note5: item.note5,
  }));

export const getClosedValveList = async (params: {
  /** string as YYYY-MM-DD */
  time?: string;
}): Promise<GetClosedValveListResponse> => {
  const { time } = params;
  const res: any = await postRequest({
    code: 'watergis/getClosedValveList',
    params: {
      time,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const closedValveList = parseValveList(res.values.closed_valve_list);
        resolve({
          status: 'Success',
          closedValveList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
