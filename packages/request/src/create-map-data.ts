/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AssessmentFormItem,
  AssessmentParams,
} from '@waterdesk/data/app-config';
import { MapViewName } from '@waterdesk/data/const/map';
import {
  DeviceCollection,
  PlantsAndPumpStationsCollection,
} from '@waterdesk/data/device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '@waterdesk/data/indicator';
import { LayerDataCollection } from '@waterdesk/data/layer-data';
import {
  ChartConfig,
  PropertyInfo,
} from '@waterdesk/data/property/property-info';
import {
  generateThemeSection,
  Scene,
  ThemeSection,
} from '@waterdesk/data/scene';
import {
  getHighlightStyle,
  HighlightStyle,
} from '@waterdesk/data/style-config';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

type Theme = string;

interface CreateViewResponse extends APIResponse {
  layerData?: LayerDataCollection;
  icons?: Map<string, string>;
  extent?: Array<number>;
  viewId?: string;
  startDate?: string;
  endDate?: string;
}

/**
 *
 * @param date string as YYYY-MM-DD
 * @returns
 */
export async function createView(
  theme: Theme,
  solutionId?: string,
): Promise<CreateViewResponse> {
  const data: any = await postRequest({
    code: 'watergis/createView',
    params: {
      skin_id: theme,
      solution_id: solutionId,
    },
  });

  if (data.json_ok) {
    const displayLayers = data.create_layers.create_layers;
    const layerDetails = data.layer_tree;
    const layerCollection: LayerDataCollection = new LayerDataCollection();
    layerCollection.initialize(displayLayers, layerDetails);

    const icons: Map<string, string> = new Map();
    Object.entries(data.icon_list).forEach((item: any) => {
      icons.set(item[0], item[1]);
    });
    return {
      status: 'Success',
      layerData: layerCollection,
      icons,
      extent: data.fit,
      viewId: data.view_id,
      startDate: data.time_range?.start,
      endDate: data.time_range?.end,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function createSolutionView(theme: Theme, solutionId?: string) {
  return createView(theme, solutionId);
}

interface GetPlantAndPumpStationListResponse extends APIResponse {
  plantsAndPumpStations?: PlantsAndPumpStationsCollection;
}

/**
 *
 * @param date string as YYYY-MM-DD
 * @returns
 */
export async function getPlantAndPumpStationList(
  date: string,
): Promise<GetPlantAndPumpStationListResponse> {
  const data: any = await postRequest({
    code: 'supply/getFactoryPumpList',
    params: {
      time: date,
    },
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  const plantsAndPumpStationsCollection: PlantsAndPumpStationsCollection =
    new PlantsAndPumpStationsCollection(data.factory);

  return {
    status: 'Success',
    plantsAndPumpStations: plantsAndPumpStationsCollection,
  };
}

interface GetScadaNameListResponse extends APIResponse {
  devices?: DeviceCollection;
  indicators?: IndicatorObjectCollection;
}

/**
 *
 * @param date string as YYYY-MM-DD
 * @returns
 */
export async function getScadaNameList(
  date: string,
): Promise<GetScadaNameListResponse> {
  const data: any = await postRequest({
    code: 'watergis/getScadaNameList',
    params: {
      time: date,
    },
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  const deviceCollection: DeviceCollection = new DeviceCollection();
  deviceCollection.initializeDevices(data.values, data.data_access ?? []);
  const indicatorCollection: IndicatorObjectCollection =
    new IndicatorObjectCollection();
  indicatorCollection.initializeIndicators(data.values, deviceCollection);
  return {
    status: 'Success',
    devices: deviceCollection,
    indicators: indicatorCollection,
  };
}

interface GetScadaQuotaListResponse extends APIResponse {
  indicatorTypes?: IndicatorTypeCollection;
  deviceIndicators?: {};
}

/**
 *
 * @param date string as YYYY-MM-DD
 * @returns
 */
export async function getScadaQuotaList(
  date: string,
): Promise<GetScadaQuotaListResponse> {
  const data: any = await postRequest({
    code: 'watergis/getScadaQuotaList',
    params: {
      time: date,
    },
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  const indicatorTypes: IndicatorTypeCollection = new IndicatorTypeCollection();
  indicatorTypes.initialize(data.quota_layers);

  return {
    status: 'Success',
    indicatorTypes,
    deviceIndicators: data.quota_list,
  };
}

interface GetPropDefinesResponse extends APIResponse {
  propertyInfos?: Map<string, PropertyInfo>;
  propertyTitles?: {}[];
  unitJson?: {};
  appConfig?: any;
  fieldEnumMap?: { [category: string]: { [key: string]: string } };
}

function generateChartConfig(data: any[]): ChartConfig[] {
  return data.map((item) => ({
    otype: item.otype as string,
    type: item.type as string,
    vprop: item.vprop as string,
    actionName: '',
  }));
}

export async function getPropDefines(
  sectionId: MapViewName,
  editable?: boolean,
): Promise<GetPropDefinesResponse> {
  const data: any = await postRequest({
    code: 'watergis/getPropDefines',
    params: {
      section_id: sectionId.toLowerCase(),
    },
  });

  if (!data.json_ok) {
    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  }

  const propertyInfos: Map<string, PropertyInfo> = new Map();
  data.defines.forEach((item: any) => {
    const { otype, title } = item;
    const propertyInfo = new PropertyInfo(otype, title, editable);
    let quickPropertiesData = [];
    if (item.tooltip_view !== undefined && item.tooltip_view.length > 0)
      quickPropertiesData = item.tooltip_view[0].prop_list;

    let scadaTreeConfig: ChartConfig[] = [];

    let chartConfig: ChartConfig[] = [];

    if (
      typeof item.tooltip_view !== 'undefined' &&
      !Array.isArray(item.tooltip_view) &&
      typeof item.tooltip_view.scadaTreeView !== 'undefined' &&
      item.tooltip_view.scadaTreeView.length > 0
    )
      scadaTreeConfig = generateChartConfig(item.tooltip_view.scadaTreeView);

    if (
      typeof item.tooltip_view !== 'undefined' &&
      !Array.isArray(item.tooltip_view) &&
      typeof item.tooltip_view.chartView !== 'undefined' &&
      item.tooltip_view.chartView.length > 0
    )
      chartConfig = generateChartConfig(item.tooltip_view.chartView);

    propertyInfo.initialize(
      item.prop_view,
      item.props,
      quickPropertiesData,
      scadaTreeConfig,
      item.device_list_type === 'DEVICE',
      chartConfig,
    );
    propertyInfos.set(otype, propertyInfo);
  });

  let highlightStyle: HighlightStyle = new Map();
  const deviceFormConfig: Map<string, AssessmentFormItem> = new Map();
  const indicatorFormConfig: Map<string, AssessmentFormItem> = new Map();
  const simulationFormConfig: Map<string, AssessmentFormItem> = new Map();

  if (data.app_config?.highlightStyle) {
    highlightStyle = getHighlightStyle(data.app_config.highlightStyle);
  }

  if (data.app_config?.assessmentDevice) {
    data.app_config?.assessmentDevice?.formConfig?.forEach(
      (item: any, index: number) => {
        deviceFormConfig.set(item.field, {
          field: item.field,
          fieldName: item.fieldName ?? item.field,
          description: item.description,
          enable: item.enable ?? false,
          multiple: item.multiple || false,
          initialValues: item.initialValues || [],
          index: item.index ?? index,
        });
      },
    );
  }

  if (data.app_config?.assessmentIndicator) {
    data.app_config?.assessmentIndicator?.formConfig?.forEach(
      (item: any, index: number) => {
        indicatorFormConfig.set(item.field, {
          field: item.field,
          fieldName: item.fieldName ?? item.field,
          description: item.description,
          enable: item.enable ?? false,
          multiple: item.multiple || false,
          initialValues: item.initialValues || [],
          index: item.index ?? index,
        });
      },
    );
  }
  const deviceAssessmentParams: AssessmentParams = {
    otypeList: data.app_config?.deviceAccessmentParams?.otypeList || [],
    vpropList: data.app_config?.deviceAccessmentParams?.vpropList || [],
  };

  if (data.app_config?.assessmentSimulation) {
    data.app_config?.assessmentSimulation?.formConfig?.forEach(
      (item: any, index: number) => {
        simulationFormConfig.set(item.field, {
          field: item.field,
          fieldName: item.fieldName ?? item.field,
          description: item.description,
          enable: item.enable ?? false,
          multiple: item.multiple || false,
          initialValues: item.initialValues || [],
          index: item.index ?? index,
        });
      },
    );
  }

  return {
    status: 'Success',
    propertyInfos,
    unitJson: data.units.DEFAULT,
    appConfig: {
      ...data.app_config,
      highlightStyle,
      deviceAccessmentForm: deviceFormConfig,
      deviceAccessmentParams: deviceAssessmentParams,
      simulationForm: simulationFormConfig,
      meanAbsoluteError: data.app_config?.meanAbsoluteError ?? [],
      assessmentDevice: {
        ...data.app_config?.assessmentDevice,
        formConfig: deviceFormConfig,
      },
      assessmentIndicator: {
        ...data.app_config?.assessmentIndicator,
        formConfig: indicatorFormConfig,
      },
      assessmentSimulation: {
        ...data.app_config?.assessmentSimulation,
        formConfig: simulationFormConfig,
      },
    },
    fieldEnumMap: data.field_enum_map,
  };
}

interface GetThemeSectionListResponse extends APIResponse {
  themes?: Map<string, string>; // <name, title>
  scenes?: Array<Scene>;
}

export async function getThemeSectionList(
  sectionId: string,
): Promise<GetThemeSectionListResponse> {
  const data: any = await postRequest({
    code: 'watergis/getThemeSectionList',
    params: {
      section_id: sectionId,
    },
  });

  if (
    data.json_ok &&
    Array.isArray(data.theme_section) &&
    Array.isArray(data.themes)
  ) {
    const themeData: Map<string, string> = new Map();
    data.themes.forEach((item: any) => {
      const name = item.theme_id;
      const title = item.theme_title;
      if (name) {
        themeData.set(name, title || name);
      }
    });

    const scenes: Array<Scene> = [];
    data.theme_section.forEach((item: any) => {
      const {
        sceneId,
        sceneTitle,
        hidden,
        hiddenDashboard,
        default: isDefault,
      } = item;
      const dashboard = item.KPIComponent;
      const themeSections: Array<ThemeSection> = [];
      const simpleThemeSections: Array<ThemeSection> = [];
      item.layerControl.forEach((group: any) => {
        const themeSection = generateThemeSection(group, themeData);
        if (themeSection) themeSections.push(themeSection);
      });

      item.simpleLayerControl?.forEach((simpleLayerControl: any) => {
        const themeSection = themeSections.find(
          (themeSection) => themeSection.type === simpleLayerControl.type,
        );
        if (themeSection) {
          const theme: ThemeSection = {
            type: simpleLayerControl.type,
            title: simpleLayerControl.title || themeSection.title,
            layerStates: simpleLayerControl.layerList.map((layerId: string) =>
              themeSection.layerStates.find(
                (layerState) => layerState.name === layerId,
              ),
            ),
            themeItems: simpleLayerControl.themeList.map((themeId: string) =>
              themeSection.themeItems.find(
                (themeItem) => themeItem.name === themeId,
              ),
            ),
            currentThemeItem: themeSection.currentThemeItem,
          };

          simpleThemeSections.push(theme);
        }
      });

      scenes.push({
        id: sceneId,
        title: sceneTitle,
        hidden: !!hidden,
        default: !!isDefault,
        dashboard,
        themeSections,
        simpleThemeSections,
        hiddenDashboard,
      });
    });

    return {
      status: 'Success',
      themes: themeData,
      scenes,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
