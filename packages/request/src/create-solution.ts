/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SolutionType } from '@waterdesk/data/solution';
import { SolutionDetailJson } from '@waterdesk/data/solution-detail';
import { APIResponse } from './api/api-response';
import { AsyncTaskStatus, getAsyncTaskStatus } from './get-async-task';
import { postRequest, postRequestByView } from './request';

export interface CreateSolutionTaskResponse extends APIResponse {
  taskId?: string;
}

export async function createSolutionTask(
  params: SolutionDetailJson,
  startSimulation: boolean,
): Promise<CreateSolutionTaskResponse> {
  const data: any = await postRequest({
    code: 'supply/createSolution',
    params: {
      ui_config: JSON.stringify(params),
      start_simulation: startSimulation,
      start_time: params.calculate_start_time,
      end_time: params.calculate_end_time,
      solution_type: params.solution_type,
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
      taskId: data.task_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export interface CreatingStateResponse extends APIResponse {
  creatingState?: boolean;
  stateMessage?: string;
}

export async function checkCreatingState(): Promise<CreatingStateResponse> {
  const data: any = await postRequest({
    code: 'supply/getCreateSolutionQueueState',
    params: {},
  });
  if (data.json_ok) {
    return {
      status: 'Success',
      creatingState: !data.job_exist as boolean,
      stateMessage: data.job_state as string,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export interface GetCreateSolutionResultResponse extends APIResponse {
  taskStatus?: AsyncTaskStatus;
  solutionId?: string;
  message?: string;
}

export async function getCreateSolutionResult(
  taskId: string,
): Promise<GetCreateSolutionResultResponse> {
  const res = await getAsyncTaskStatus(taskId);
  if (res.status === 'Success') {
    if (res.taskStatus !== AsyncTaskStatus.SUCCESS)
      return {
        status: 'Success',
        taskStatus: res.taskStatus,
        message: res.message,
      };

    if (res.taskStatus === AsyncTaskStatus.SUCCESS)
      return {
        status: 'Success',
        taskStatus: res.taskStatus,
        solutionId: res.values.solution_id,
        message: res.message,
      };
  }

  return {
    status: 'Fail',
    errorMessage: res.errorMessage,
  };
}

export interface simulateSolutionTaskResponse extends APIResponse {
  taskId?: string;
}

export async function simulateSolutionTask(
  solutionId: string,
  params: any,
): Promise<CreateSolutionTaskResponse> {
  const data: any = await postRequest({
    code: 'supply/calculateSolution',
    params: {
      solution_id: solutionId,
      ui_config: JSON.stringify(params),
      start_time: params.calculate_start_time,
      end_time: params.calculate_end_time,
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
      taskId: solutionId,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function saveAsSolution(
  solutionId: string,
  solutionName: string,
  solutionType: SolutionType,
): Promise<CreateSolutionTaskResponse> {
  const data: any = await postRequest({
    code: 'supply/createSolutionBySolution',
    params: {
      src_solu_id: solutionId,
      solu_name: solutionName,
      solution_type: solutionType,
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
      taskId: data.task_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export const saveSolutionDetail = async (
  solutionDetail: SolutionDetailJson,
): Promise<CreateSolutionTaskResponse> => {
  const data: any = await postRequestByView({
    code: 'supply/updateSolutionConfig',
    params: {
      ui_config: JSON.stringify(solutionDetail),
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
      taskId: data.task_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

export const editSolutionInfo = async (
  solutionId: string,
  solutionName: string,
): Promise<APIResponse> => {
  const data: any = await postRequestByView({
    code: 'supply/updateSolutionName',
    params: {
      solution_id: solutionId,
      solution_title: solutionName,
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};
