/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentInfo, DepartmentList } from '@waterdesk/data/department';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetDepartmentListResponse extends APIResponse {
  departmentList?: DepartmentList;
}

export const getDepartmentList =
  async (): Promise<GetDepartmentListResponse> => {
    const res: any = await postRequest({
      code: 'portal/getDepartmentList',
    });
    return new Promise((resolve) => {
      try {
        if (res.json_ok && res.value) {
          const departmentList: DepartmentInfo[] = Array.isArray(
            res.value.departmentList,
          )
            ? res.value.departmentList
            : [];
          resolve({
            status: 'Success',
            departmentList,
          });
        } else {
          resolve({
            status: 'Fail',
            errorMessage: res.json_msg,
          });
        }
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    });
  };

export const addDepartment = async (
  name: string,
  parentId?: string,
  order?: number,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/createDepartment',
    params: {
      department_parent_id: parentId,
      department_name: name,
      order,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const deleteDepartment = async (id: string): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/deleteDepartment',
    params: {
      department_id: id,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const updateDepartment = async (
  id: string,
  parentId: string,
  name: string,
  order?: number,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateDepartment',
    params: {
      department_id: id,
      department_parent_id: parentId,
      department_name: name,
      order,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
