/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DailySummaryItems,
  DailySummaryLogItems,
} from '@waterdesk/data/schedule-log/schedule-log';
import { APIResponse } from '../api/api-response';
import { postRequest } from '../request';

export interface GetDailySummaryResponse extends APIResponse {
  values: DailySummaryItems;
}

export interface GetDailySummaryLogResponse extends APIResponse {
  values: DailySummaryLogItems[];
}

/** 查询某天的当日概况 */
export const getDailySummary = async (
  time?: string,
): Promise<GetDailySummaryResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryHandoverAbstractByDate',
    params: {
      handover_date: time,
    },
  });

  if (data?.json_ok) {
    const values = {
      date: data?.values?.handover_date,
      summary: data?.values?.abstract,
      creator: data?.values?.creator,
      createTime: data?.values?.create_time,
    };
    return {
      status: 'Success',
      values,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    values: {},
  };
};

/** 查询某天的当日概况日志 */
export const getDailySummaryLog = async (
  time?: string,
): Promise<GetDailySummaryLogResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryHandoverAbstractLogByDate',
    params: {
      handover_date: time,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
      values: data?.values?.map((item: any) => ({
        id: item?.handover_id,
        date: item?.handover_date,
        summary: item?.abstract,
        operator: item?.creator,
        time: item?.create_time,
      })),
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    values: [],
  };
};

/** 创建今日概况 */
export const createTodaySummary = async (
  time: string,
  summary: string,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/addTodayHandoverDate',
    params: {
      handover_date: time,
      abstract: summary,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
  };
};

/** 更新今日概况 */
export const updateTodaySummary = async (
  time: string,
  summary: string,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/updateTodayHandoverDate',
    params: {
      handover_date: time,
      abstract: summary,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
  };
};

/** 新增历史概况 */
export const createHistorySummary = async (
  time: string,
  summary: string,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/addHistoryHandoverDate',
    params: {
      handover_date: time,
      abstract: summary,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
  };
};

/** 更新历史概况 */
export const updateHistorySummary = async (
  time: string,
  summary: string,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/updateHistoryHandoverDate',
    params: {
      handover_date: time,
      abstract: summary,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
  };
};
