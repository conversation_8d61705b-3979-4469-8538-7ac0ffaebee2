/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HandoverInfoList } from '@waterdesk/data/dispatch-log/handover';
import { APIResponse } from '../api/api-response';
import { postRequest } from '../request';

/** 查询当前用户是否需要交班参数 */
export interface CheckUserForShiftHandoffParams {
  /** 当前班次时间 */
  currentShiftTime: string;
  /** 下一班次时间 */
  nextShiftTime: string;
}

export interface CheckUserForShiftTakeoffParams {
  /** 当前班次时间 */
  prevShiftTime: string;
}

export interface CheckUserForShiftParams {
  shiftId: string;
}

interface CheckUserForShiftHandoffResponse extends APIResponse {
  data: boolean;
}

interface CheckUserForShiftTakeoffResponse extends APIResponse {
  data: HandoverInfoList;
}

/** 查询当前用户是否需要交班 */
export const checkUserForShiftHandoff = async (
  params: CheckUserForShiftHandoffParams,
): Promise<CheckUserForShiftHandoffResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryNeedHandoverStateByTime',
    params: {
      current_classes_time: params.currentShiftTime,
      next_classes_time: params.nextShiftTime,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
      data: data?.values?.need_handover,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    data: false,
  };
};

/** 查询当前用户是否需要接班 */
export const checkUserForShiftTakeoff = async (
  params: CheckUserForShiftTakeoffParams,
): Promise<CheckUserForShiftTakeoffResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryHandoverInfoByTime',
    params: {
      last_classes_time: params?.prevShiftTime,
    },
  });

  if (data.json_ok && Object.keys(data.values).length > 0) {
    return {
      status: 'Success',
      data: {
        handoverId: data?.values?.handover_id,
        handoverDate: data?.values?.handover_date,
        handoverClasses: data?.values?.handover_classes,
        handoverState: data?.values?.handover_state,
        handoverFromOperator: data?.values?.handover_from_operator,
        handoverFromUserName: data?.values?.handover_from_user_name,
        handoverToOperator: data?.values?.handover_to_operator,
        handoverToUserName: data?.values?.handover_to_user_name,
        handoverContent: data?.values?.handover_content,
        planContent: data?.values?.plan_content,
        noteContent: data?.values?.note_content,
        createTime: data?.values?.create_time,
        startTime: data?.values?.start_time,
        endTime: data?.values?.end_time,
        remark: data?.values?.remark,
        userName: data?.values?.user_name,
      } as HandoverInfoList,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    data: {} as HandoverInfoList,
  };
};

/** 查询当前用户是否是当班人员 */
export const checkUserForShift = async (
  params: CheckUserForShiftParams,
): Promise<boolean> => {
  const data: any = await postRequest({
    code: 'watergis/queryIsBeOnDutyStateByShiftId',
    params: {
      shift_id: params.shiftId,
    },
  });

  if (data?.json_ok) {
    return data?.values?.is_be_on_duty;
  }

  return false;
};
