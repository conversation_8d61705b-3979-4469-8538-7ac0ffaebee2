/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Dayjs } from 'dayjs';
import { postRequestByView, requestApi } from '../request';

export const downloadFile = (tempFileId: string) => {
  window.open(
    `${
      requestApi.baseUrl
    }portal/downloadTmpFile?tmp_file_id=${tempFileId}&session_id=${localStorage.getItem(
      'token',
    )}`,
  );
};

export function downloadStormDeskFile(_mapViewName: string, time: Dayjs) {
  postRequestByView({
    code: 'drain/download_desktop',
    params: {
      time: time.format('YYYY-MM-DD'),
    },
  }).then((res: any) => {
    if (res.json_ok) {
      downloadFile(res.file_id);
    }
  });
}
