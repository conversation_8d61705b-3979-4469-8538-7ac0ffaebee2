/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from '../api/api-response';
import { postRequest } from '../request';

export interface GetScadaDownloadResponse extends APIResponse {
  fileId?: string;
}

export async function getDownloadFileId(
  startDate: string,
  endDate: string,
  otypeList: string[],
): Promise<GetScadaDownloadResponse> {
  return postRequest({
    code: 'watergis/exportScadaValueToCsv',
    params: {
      start_date: startDate,
      end_date: endDate,
      otype_list: otypeList.toString(),
    },
  }).then((res: any) => {
    if (res.json_ok) {
      return {
        status: 'Success',
        fileId: res.file_id,
      };
    }
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
    };
  });
}

export async function getDownloadScadaInfoFileId(
  startDate: string,
  endDate: string,
  otypeList: string[],
): Promise<GetScadaDownloadResponse> {
  return postRequest({
    code: 'watergis/exportScadaBaseToExcel',
    params: {
      start_date: startDate,
      end_date: endDate,
      otype_list: otypeList.toString(),
    },
  }).then((res: any) => {
    if (res.json_ok) {
      return {
        status: 'Success',
        fileId: res.file_id,
      };
    }
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
    };
  });
}
