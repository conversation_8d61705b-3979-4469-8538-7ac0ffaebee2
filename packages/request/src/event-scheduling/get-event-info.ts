/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ContingencyPlanUserRole } from '@waterdesk/data/contingency-plan/contingency-plan-data';
import {
  EventSchedulingBasicInfo,
  EventStatusType,
} from '@waterdesk/data/event-scheduling/basic-info';
import {
  EventSchedulingLog,
  fieldMapping,
} from '@waterdesk/data/event-scheduling/event-log';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { PageParams, SortMethod, SortParams } from '../api/api-request';
import { APIResponse, DefaultListAPIResponse } from '../api/api-response';
import { postRequest } from '../request';

export interface GetEventSchedulingBasicInfoParams extends PageParams {
  eventIds?: string[];
  eventTitle?: string;
  eventType?: string | string[];
  eventSubType?: string | string[];
  eventStatus?: EventStatusType | EventStatusType[];
  eventStartTime?: string;
  eventEndTime?: string;
  operator?: string;
  /** 是否上轴 */
  isOnTimeline?: boolean;
  /** 是否上曲线 */
  isOnChart?: boolean;
  eventLevel?: number;
  label?: string;
  emergencyEvent?: number;
  updateTime?: string;
}

export interface GetEventSchedulingBasicInfoByDateParams {
  eventId?: string;
  eventTitle?: string;
  eventType?: string | string[];
  eventSubType?: string | string[];
  eventStatus?: EventStatusType | EventStatusType[];
  eventStartDate?: string;
  eventEndDate?: string;
  operator?: string;
  /** 是否上轴 */
  isOnTimeline?: boolean;
  /** 是否上曲线 */
  isOnChart?: boolean;
}

export interface CreateEventSchedulingBasicInfoResponse extends APIResponse {
  eventId: string;
}

export interface GetEventSchedulingBasicInfoLogParams {
  eventId?: string;
  startTime?: string;
  endTime?: string;
}

function mapFields(item: any): EventSchedulingBasicInfo {
  return Object.keys(item).reduce((acc, key) => {
    const mappedKey = fieldMapping[key] || key;
    return { ...acc, [mappedKey]: item[key] };
  }, {} as EventSchedulingBasicInfo);
}

/** 查询调度事件基本信息列表 */
export const getEventSchedulingBasicInfo = async (
  params: GetEventSchedulingBasicInfoParams & SortParams,
): Promise<DefaultListAPIResponse<EventSchedulingBasicInfo>> => {
  const data: any = await postRequest({
    code: 'watergis/queryEventInfoList',
    params: {
      current: params.current,
      pageSize: params.pageSize,
      oname_list: params.eventIds?.toString(),
      title: params.eventTitle,
      event_type1_list: params.eventType?.toString(),
      event_type2_list: params.eventSubType?.toString(),
      state_list: params.eventStatus?.toString(),
      start_time: params.eventStartTime,
      end_time: params.eventEndTime,
      event_on_timeline:
        params.isOnTimeline !== undefined
          ? (params.isOnTimeline && 1) || 0
          : undefined,
      event_on_chart:
        params.isOnChart !== undefined
          ? (params.isOnChart && 1) || 0
          : undefined,
      user_name: params.operator,
      event_level: params.eventLevel,
      label: params.label,
      sort_by: params.sort_by,
      sort_method: params.sort_method,
      emergency_event: params.emergencyEvent,
      update_time: params.updateTime,
    },
  });

  if (data.json_ok) {
    const eventData: EventSchedulingBasicInfo[] = data.values.records.map(
      (item: any) => {
        const membersList: { roleType: string; userId: string }[] =
          item.members ?? [];
        const groupLeader = membersList
          .filter((f) => f.roleType === ContingencyPlanUserRole.GROUP_LEADER)
          .map((m) => m.userId);
        const deputyLeader = membersList
          .filter((f) => f.roleType === ContingencyPlanUserRole.DEPUTY_LEADER)
          .map((m) => m.userId);
        const members = membersList
          .filter((f) => f.roleType === ContingencyPlanUserRole.MEMBER)
          .map((m) => m.userId);

        return {
          ...mapFields(item),
          members,
          groupLeader,
          deputyLeader,
          emergencyUserList: item.members,
        };
      },
    );

    let countData: { [key in keyof typeof EventStatusType]?: number } = {};
    Object.keys(data.values.count).forEach((type) => {
      countData = {
        ...countData,
        [type]: data.values.count[type] ?? 0,
      };
    });

    return {
      status: 'Success',
      total: data.values.total_count,
      count: countData,
      list: eventData,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    total: 0,
    count: {},
    list: [],
  };
};

const sorterServerFieldMap: Record<string, string> = {
  eventStartTime: 'start_time',
  eventEndTime: 'end_time',
  createTime: 'create_time',
};

/** 查询调度事件基本信息列表 By Date */
export const getEventSchedulingBasicInfoByDate = async (
  params: GetEventSchedulingBasicInfoByDateParams & PageParams,
  sort?: Record<string, SortMethod>,
) => {
  const sortKey = Object.keys(sort ?? {})[0];
  const sortMethod = Object.values(sort ?? {})[0];
  const isSort = sortKey && sortMethod;

  const startTime = dayjs(params?.eventStartDate).format('YYYY-MM-DD 00:00:00');
  const endTime = dayjs(params?.eventEndDate).format('YYYY-MM-DD 23:59:59');

  return getEventSchedulingBasicInfo({
    ...params,
    eventStartTime: startTime,
    eventEndTime: endTime,
    sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
    sort_method: isSort ? (sortMethod as SortMethod) : undefined,
  });
};

/** 创建调度事件基本信息 */
export const createEventSchedulingBasicInfo = async (
  params: EventSchedulingBasicInfo,
): Promise<CreateEventSchedulingBasicInfoResponse> => {
  const isOnTimeline = params.isOnTimeline ?? true;
  const isOnChart = params.isOnChart ?? true;
  const members = [
    ...(params.groupLeader?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.GROUP_LEADER,
    })) ?? []),
    ...(params.deputyLeader?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.DEPUTY_LEADER,
    })) ?? []),
    ...(params.members?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.MEMBER,
    })) ?? []),
  ];
  const data: any = await postRequest({
    code: 'watergis/createEventInfo',
    params: {
      title: params.eventTitle,
      state: params.eventStatus,
      event_type1: params.eventType,
      event_type2: params.eventSubType,
      start_time: params.eventStartTime
        ? dayjs(params.eventStartTime).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      end_time: params.eventEndTime
        ? dayjs(params.eventEndTime).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      address: params.eventAddress,
      shape: params.shape,
      event_on_timeline: isOnTimeline ? 1 : 0,
      event_on_chart: isOnChart ? 1 : 0,
      event_level: params.eventLevel,
      affect_user_count: params.affectUserCount,
      label: params.label,
      emergency_event: params.emergencyEvent,
      event_source_type: params.eventSourceType,
      event_source_id: params.eventSourceId,
      members,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      eventId: data.values.oname,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    eventId: '',
  };
};

/** 更新调度事件基本信息 */
export const updateEventSchedulingBasicInfo = async (
  params: Partial<EventSchedulingBasicInfo>,
): Promise<APIResponse> => {
  const members = [
    ...(params.groupLeader?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.GROUP_LEADER,
    })) ?? []),
    ...(params.deputyLeader?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.DEPUTY_LEADER,
    })) ?? []),
    ...(params.members?.map((m) => ({
      userId: m,
      roleType: ContingencyPlanUserRole.MEMBER,
    })) ?? []),
  ];
  const data: any = await postRequest({
    code: 'watergis/updateEventInfo',
    params: {
      oname: params.eventId,
      title: params.eventTitle,
      state: params.eventStatus,
      event_type1: params.eventType,
      event_type2: params.eventSubType,
      start_time: params.eventStartTime
        ? dayjs(params.eventStartTime).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      end_time: params.eventEndTime
        ? dayjs(params.eventEndTime).format('YYYY-MM-DD HH:mm:ss')
        : '',
      address: params.eventAddress,
      shape: params.shape,
      event_on_timeline: params.isOnTimeline,
      event_on_chart: params.isOnChart,
      event_level: params.eventLevel,
      affect_user_count: params.affectUserCount,
      label: params.label,
      emergency_event: params.emergencyEvent,
      members,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

/** 删除调度事件基本信息 */
export const deleteEventSchedulingBasicInfo = async (
  eventId: string,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/deleteEventInfo',
    params: {
      oname: eventId,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

/** 获取日志修改记录 */
export const getEventSchedulingBasicInfoLog = async (
  params: GetEventSchedulingBasicInfoLogParams,
): Promise<DefaultListAPIResponse<EventSchedulingLog>> => {
  const data: any = await postRequest({
    code: 'watergis/queryEventChangeLogList',
    params: {
      event_id: params.eventId,
      start_time: params.startTime
        ? dayjs(params.startTime).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      end_time: params.endTime
        ? dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      total: data.values.total_count,
      list: data.values.records.map((item: any) => ({
        id: uuidv4(),
        eventId: item.oname,
        eventRelatedId: item.association_name,
        logTime: item.otime,
        contentType: item.content_type,
        changeType: item.change_type,
        changedField: fieldMapping[item.field_name] ?? item.field_name,
        beforeContent: item.old_value,
        afterContent: item.new_value,
        operator: item.user_name,
      })),
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    list: [],
    total: 0,
  };
};
