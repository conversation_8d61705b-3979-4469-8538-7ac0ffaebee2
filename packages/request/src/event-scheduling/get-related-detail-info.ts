/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { EventSchedulingRelatedDetailInfo } from '@waterdesk/data/event-scheduling/related-detail-info';
import { splitId } from '@waterdesk/data/object-item';
import {
  ObservationScadaItem,
  ObservationType,
} from '@waterdesk/data/observation-scada-data';
import { ChartProperties } from '@waterdesk/data/property/property-info';
import {
  parseValveOperationGroups,
  parseValveOperationList,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { APIResponse } from '../api/api-response';
import { convertDispatchCommandList } from '../get-dispatch-command';
import { convertPlanProjectList } from '../get-plan-project-data';
import { convertSolutionComparisonList } from '../get-solution-comparsion';
import { formatSolutionItem } from '../get-solution-data';
import { formatWarnInfoList } from '../get-warn';
import { convertWorkOrderList } from '../get-work-order';
import { postRequest } from '../request';

export interface GetEventSchedulingRelatedInfoResponse extends APIResponse {
  data: EventSchedulingRelatedDetailInfo;
}

/** 查询调度事件关联数据列表详情 */
export const getEventSchedulingRelatedDetailInfo = async (
  eventId: string,
  db: Database,
): Promise<GetEventSchedulingRelatedInfoResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryAssociationDetailListByEventId',
    params: {
      oname: eventId,
    },
  });

  if (data?.json_ok) {
    return {
      status: 'Success',
      data: {
        command: convertDispatchCommandList(data.values.COMMAND),
        planning: convertPlanProjectList(data.values.PLANNING),
        remark: data.values.REMARK as ChartProperties[],
        valve: parseValveOperationList(data.values.VALVE),
        valveGroup: parseValveOperationGroups(data.values.VALVE_GROUP),
        warning: formatWarnInfoList(data.values.WARNING ?? [], db),
        workOrder: convertWorkOrderList(data.values.WORK_ORDER),
        images: data?.values?.IMAGES ?? [],
        solution: formatSolutionItem(data?.values?.SOLUTION) ?? [],
        solutionComparison:
          convertSolutionComparisonList(data?.values?.SOLUTION_COMPARISON) ??
          [],
        contingencyPlan: data?.values?.CONTINGENCY_PLAN ?? [],
        observations:
          data?.values?.OBSERVATIONS.map(
            ({ id, description }: any): ObservationScadaItem => {
              const [otype, oname, vprop, observationType] = splitId(id);
              return {
                id,
                otype,
                oname,
                vprop,
                description,
                type: observationType as ObservationType,
                observationValues: [],
                isPublic: true,
                createTime: '',
                updateTime: '',
              };
            },
          ) ?? [],
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    data: {
      command: [],
      planning: [],
      remark: [],
      valve: [],
      valveGroup: [],
      warning: [],
      workOrder: [],
      images: [],
      solution: [],
      solutionComparison: [],
      contingencyPlan: [],
      observations: [],
    },
  };
};
