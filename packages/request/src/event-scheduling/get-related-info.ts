/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import {
  EventRelatedType,
  EventSchedulingRelatedInfo,
} from '@waterdesk/data/event-scheduling/related-info';
import dayjs from 'dayjs';
import { APIResponse, DefaultListAPIResponse } from '../api/api-response';
import { postRequest } from '../request';
import {
  CreateEventSchedulingBasicInfoResponse,
  createEventSchedulingBasicInfo,
} from './get-event-info';

export interface GetEventSchedulingRelatedInfoParams {
  eventId?: string;
  startTime?: string;
  endTime?: string;
  /** 是否上轴 */
  isOnTimeline?: boolean;
  /** 是否上曲线 */
  isOnChart?: boolean;
}

export interface CreateEventSchedulingRelatedInfoParams {
  eventId: string;
  relatedInfo: Partial<EventSchedulingRelatedInfo | undefined>[];
}

export interface CreateEventSchedulingAndRelatedInfoParams {
  eventInfo: EventSchedulingBasicInfo;
  relatedInfo: EventSchedulingRelatedInfo[];
}

export interface DeleteEventSchedulingRelatedInfoParams {
  eventId: string;
  relatedId: string;
}

/** 查询调度事件关联信息列表 */
const getEventSchedulingRelatedInfo = async (
  params: GetEventSchedulingRelatedInfoParams,
): Promise<DefaultListAPIResponse<EventSchedulingRelatedInfo>> => {
  const data: any = await postRequest({
    code: 'watergis/queryEventAssociationList',
    params: {
      oname: params.eventId,
      start_time: params.startTime,
      end_time: params.endTime,
      association_on_timeline:
        params.isOnTimeline !== undefined
          ? (params.isOnTimeline && 1) || 0
          : undefined,
      association_on_chart:
        params.isOnChart !== undefined
          ? (params.isOnChart && 1) || 0
          : undefined,
      need_hide_kind: true,
    },
  });

  if (data.json_ok) {
    const eventRelatedData: EventSchedulingRelatedInfo[] = data.values.records
      ?.map((item: any) => ({
        eventId: item.event_name,
        eventName: item.event_title,
        relatedId: item.association_name,
        relatedType: item.association_kind,
        relatedSubType: item.association_type,
        relatedDescription: item.association_describe,
        relatedTime: item.association_create_time,
        relatedOperator: item.operator,
        relatedOnTimeLine: item.association_on_timeline === 1,
        relatedOnChart: item.association_on_chart === 1,
      }))
      ?.filter(
        (item: EventSchedulingRelatedInfo) =>
          item.relatedType !== EventRelatedType.REMARK &&
          item.relatedType !== EventRelatedType.OBSERVATION &&
          item.relatedType !== EventRelatedType.EMERGENCY_EVENT,
      );

    return {
      status: 'Success',
      list: eventRelatedData,
      total: data.values.total_count,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    total: 0,
    list: [],
  };
};

/** 根据调度事件ID查询调度事件关联信息 */
export const getEventSchedulingRelatedInfoById = async (
  eventId: string,
): Promise<DefaultListAPIResponse<EventSchedulingRelatedInfo>> =>
  getEventSchedulingRelatedInfo({ eventId });

/** 根据日期查询调度事件关联信息 */
export const getEventSchedulingRelatedInfoByDate = async (
  startDate: string,
  endDate: string,
): Promise<DefaultListAPIResponse<EventSchedulingRelatedInfo>> => {
  const startTime = dayjs(startDate)
    .startOf('day')
    .format('YYYY-MM-DD HH:mm:ss');
  const endTime = dayjs(endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss');

  return getEventSchedulingRelatedInfo({
    startTime,
    endTime,
    isOnChart: true,
    isOnTimeline: true,
  });
};

/** 创建调度事件关联信息 */
export const createEventSchedulingRelatedInfo = async (
  params: CreateEventSchedulingRelatedInfoParams,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/createEventAssociation',
    params: {
      oname: params.eventId,
      association_info_list: JSON.stringify(
        params.relatedInfo.map((item) => ({
          association_kind: item?.relatedType,
          association_type: item?.relatedSubType,
          association_name: item?.relatedId,
          association_create_time:
            item?.relatedTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss'),
          association_describe: item?.relatedDescription,
        })),
      ),
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

/** 创建调度事件并关联信息 */
export const createEventSchedulingAndRelatedInfo = async (
  params: CreateEventSchedulingAndRelatedInfoParams,
): Promise<CreateEventSchedulingBasicInfoResponse> => {
  const data: CreateEventSchedulingBasicInfoResponse =
    await createEventSchedulingBasicInfo(params.eventInfo);

  if (data.status === 'Success') {
    const data2: any = await postRequest({
      code: 'watergis/createEventAssociation',
      params: {
        oname: data.eventId,
        association_info_list: JSON.stringify(
          params.relatedInfo.map((item) => ({
            association_kind: item?.relatedType,
            association_type: item?.relatedSubType,
            association_name: item?.relatedId,
            association_create_time:
              item?.relatedTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss'),
            association_describe: item?.relatedDescription,
          })),
        ),
      },
    });

    if (data2.json_ok) {
      return {
        status: 'Success',
        eventId: data.eventId,
      };
    }

    const data3: any = await postRequest({
      code: 'watergis/deleteEventInfo',
      params: {
        oname: data.eventId,
      },
    });

    if (data3.json_ok) {
      return {
        status: 'Fail',
        errorMessage: `由于[${data2.json_msg}], 导致关联失败, 已删除调度事件`,
        eventId: '',
      };
    }

    return {
      status: 'Fail',
      errorMessage: data2.json_msg,
      eventId: '',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.errorMessage,
    eventId: '',
  };
};

/** 编辑调度事件关联信息 */
export const updateEventSchedulingRelatedInfo = async (
  params: CreateEventSchedulingRelatedInfoParams,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/updateEventAssociation',
    params: {
      event_id: params.eventId,
      association_name: params.relatedInfo[0]?.relatedId,
      association_describe: params.relatedInfo[0]?.relatedDescription,
      association_create_time: params.relatedInfo[0]?.relatedTime,
      association_on_chart:
        typeof params.relatedInfo[0]?.relatedOnChart === 'boolean'
          ? Number(params.relatedInfo[0]?.relatedOnChart)
          : undefined,
      association_on_timeline:
        typeof params.relatedInfo[0]?.relatedOnTimeLine === 'boolean'
          ? Number(params.relatedInfo[0]?.relatedOnTimeLine)
          : undefined,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

/** 删除调度事件关联信息 */
export const deleteEventSchedulingRelatedInfo = async (
  params: DeleteEventSchedulingRelatedInfoParams,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/deleteEventAssociation',
    params: {
      event_id: params.eventId,
      association_name: params.relatedId,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};
