/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import ModelObject from '@waterdesk/data/model-object';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface FindAnyElementByPosResponse extends APIResponse {
  foundObjects?: Array<ModelObject>;
}

export interface getModelObjectByIndicatorsResponse extends APIResponse {
  data?: {
    [index: string]: {
      otype: string;
      oname: string;
      shape: string;
    };
  };
}

export async function findAnyElementByPos(
  posx: number,
  posy: number,
  dist: number,
  db: Database,
  limit?: number,
  otypeList?: string,
): Promise<FindAnyElementByPosResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/findAnyElementByPos',
    params: {
      posx,
      posy,
      dist,
      limit: limit ?? 10,
      otype_list:
        otypeList ??
        'WDM_PIPES,WDM_PIPES_DMA,WDM_MODELPIPES,WDM_VALVES,WDM_JUNCTIONS,WDM_JUNCTIONS_DMA,WDM_MODELNODE,WDM_ENDPOINT,WDM_HYDRANT',
    },
  });

  if (data.json_ok && data.values) {
    const foundObjects: Array<ModelObject> = [];
    Object.entries(data.values).forEach((entry) => {
      const [key, items] = entry;
      if (Array.isArray(items)) {
        items.forEach((item) => {
          const modelObject = new ModelObject(key, item.oname, item.shape, db);
          foundObjects.push(modelObject);
        });
      }
    });

    return {
      status: 'Success',
      foundObjects,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getModelObjectByIndicators(
  indicatorIds: string[],
): Promise<getModelObjectByIndicatorsResponse> {
  const res: any = await postRequestByView({
    code: 'watergis/getModelObjectsByIndicators',
    params: {
      indicatorIds: indicatorIds.join(),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      data: res.values,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}
