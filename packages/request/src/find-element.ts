/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import ModelObject from '@waterdesk/data/model-object';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface FindElementResponse extends APIResponse {
  foundObject?: ModelObject;
}

export async function findElementByPos(
  posx: number,
  posy: number,
  dist: number,
  db: Database,
  viewId?: string,
  zoom?: number,
): Promise<FindElementResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/findElementByPos',
    params: {
      posx,
      posy,
      dist,
      count: 1,
      view_id: viewId,
      map_ratio: zoom,
    },
  });

  if (data.json_ok) {
    if (data.elem_geom?.OTYPE && data.elem_geom.ONAME && data.elem_geom.SHAPE) {
      return {
        status: 'Success',
        foundObject: new ModelObject(
          data.elem_geom.OTYPE,
          data.elem_geom.ONAME,
          data.elem_geom.SHAPE,
          db,
        ),
      };
    }

    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
