/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { GisLayerIdentifyArgs } from '@waterdesk/data/gis-layer-data';
import GisObject, { convertGeometryToShape } from '@waterdesk/data/gis-object';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { APIResponse } from './api/api-response';

export interface FindGisObjectResponse extends APIResponse {
  foundObject?: GisObject;
}

export async function findGisObject(
  identifyParams: GisLayerIdentifyArgs,
  x: number,
  y: number,
  extent: number[],
  width: number,
  height: number,
): Promise<FindGisObjectResponse> {
  const params = {
    ...identifyParams.params,
    geometry: `x:${x}, y:${y}`,
    mapExtent: `${extent[0]},${extent[1]},${extent[2]},${extent[3]}`,
    imageDisplay: `${width},${height}`,
  };
  const config: AxiosRequestConfig = {
    method: 'POST',
    url: identifyParams.url,
    timeout: identifyParams.timeout ?? 5000,
    params,
  };

  let response: AxiosResponse<any>;
  try {
    response = await axios(config);
  } catch (err) {
    let message = 'axios exception';
    if (err instanceof Error) message = err.message;

    console.log(message);
    return { status: 'Fail', errorMessage: message };
  }

  if (response.status === 200) {
    if (
      Array.isArray(response.data.results) &&
      response.data.results.length > 0
    ) {
      const result = response.data.results[0];
      const { layerId, layerName, geometryType, geometry, value } = result;
      const shape = convertGeometryToShape(geometryType, geometry);
      const attributes: Array<[string, any]> = Object.entries(
        result.attributes,
      );

      const gisObject: GisObject = new GisObject(
        layerId,
        layerName,
        result.attributes.OBJECTID ?? value,
        shape || '',
        attributes,
      );

      return {
        status: 'Success',
        foundObject: gisObject,
      };
    }
  }

  return { status: 'Fail', errorMessage: response.statusText };
}
