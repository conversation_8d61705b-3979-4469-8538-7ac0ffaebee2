/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ChenTouRoadQueryArgs,
  FuZhouRoadQueryArgs,
  GisRoadConfig,
  GisRoadQueryType,
} from '@waterdesk/data/app-config';
import {
  convertGeometryToShape,
  convertToPoint,
} from '@waterdesk/data/gis-object';
import { SearchRoadInfo } from '@waterdesk/data/search-element-info';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { APIResponse } from './api/api-response';

export interface FindGisRoadResponse extends APIResponse {
  foundRoads?: Array<SearchRoadInfo>;
}

function addRoadShape(
  roads: Array<SearchRoadInfo>,
  title: string,
  shape: string,
) {
  for (let i = 0; i < roads.length; i += 1) {
    if (roads[i].title === title) {
      roads[i]?.shapes?.push(shape);
      return;
    }
  }

  roads.push({
    title,
    shapes: [shape],
    infoType: 'Road',
  });
}

export async function findChengtouGisRoad(
  name: string,
  config: ChenTouRoadQueryArgs,
): Promise<FindGisRoadResponse> {
  const params = {
    ...config.params,
    search_word: name,
  };

  const axiosConfig: AxiosRequestConfig = {
    url: config.url,
    method: config.method,
    params,
    timeout: config.timeout ?? 5000,
  };

  let response: AxiosResponse<any>;
  try {
    response = await axios(axiosConfig);
  } catch (err) {
    let message = 'axios exception';
    if (err instanceof Error) message = err.message;

    return { status: 'Fail', errorMessage: message };
  }

  if (response.status === 200) {
    const foundRoads: Array<SearchRoadInfo> = [];
    const { code, message, data } = response.data;
    if (code === 0 && Array.isArray(data)) {
      data.forEach((item) => {
        /**
         * pointX X坐标
         * pointY Y坐标
         * address 地址描述（测绘院的地址描述）
         * jlxnameMlph 地址描述（公安的数据）
         * poiString poi描述
         * verifyName 道路标准名称
         * roadAlias 道路别名
         * roadName1 道路一名称（道路路口）
         * roadName2 道路二名称（道路路口）
         */
        const {
          pointX,
          pointY,
          address,
          jlxnameMlph,
          poiString,
          verifyName,
          roadAlias,
          roadName1,
          roadName2,
        } = item || {};
        const shape = convertToPoint({ x: pointX, y: pointY });

        const title =
          address ??
          jlxnameMlph ??
          poiString ??
          verifyName ??
          roadAlias ??
          roadName1 ??
          roadName2;

        if (title) {
          foundRoads.push({
            title,
            shapes: shape ? [shape] : undefined,
            infoType: 'Road',
          });
        }
      });

      return {
        status: 'Success',
        foundRoads,
      };
    }
    return { status: 'Fail', errorMessage: message };
  }

  return { status: 'Fail', errorMessage: response.statusText };
}

export async function findFuzhouGisRoad(
  name: string,
  config: FuZhouRoadQueryArgs,
): Promise<FindGisRoadResponse> {
  let where: string = '';
  if (config.where) {
    where = config.where.replace('${name}', name);
  }

  const params = {
    ...config.params,
    where,
  };
  const axiosConfig: AxiosRequestConfig = {
    url: config.url,
    method: config.method,
    params,
    timeout: config.timeout || 5000,
  };

  let response: AxiosResponse<any>;
  try {
    response = await axios(axiosConfig);
  } catch (err) {
    let message = 'axios exception';
    if (err instanceof Error) message = err.message;

    return { status: 'Fail', errorMessage: message };
  }

  if (response.status === 200) {
    const foundRoads: Array<SearchRoadInfo> = [];
    const { geometryType, features } = response.data;
    if (Array.isArray(features) && features.length > 0) {
      features.forEach((item) => {
        const attributes = Object.entries(item.attributes);
        if (attributes.length > 0) {
          const title = attributes[0][1] as string;
          const shape = convertGeometryToShape(geometryType, item.geometry);
          if (shape) addRoadShape(foundRoads, title, shape);
        }
      });

      return {
        status: 'Success',
        foundRoads,
      };
    }
  }

  return { status: 'Fail', errorMessage: response.statusText };
}

export async function findGisRoad(
  name: string,
  gisRoadConfig?: GisRoadConfig,
): Promise<FindGisRoadResponse> {
  const { type, config } = gisRoadConfig || {};
  const failNoSupported: FindGisRoadResponse = {
    status: 'Fail',
    errorMessage: 'not supported',
  };
  if (config === undefined) return failNoSupported;

  let fetchGisRoad;
  switch (type) {
    case GisRoadQueryType.SH_CHENGTOU:
      fetchGisRoad = findChengtouGisRoad;
      break;
    case GisRoadQueryType.FUZHOU:
      fetchGisRoad = findFuzhouGisRoad;
      break;
    default:
      break;
  }

  if (typeof fetchGisRoad === 'undefined') return failNoSupported;

  const res: FindGisRoadResponse = await fetchGisRoad(name, config);
  return res;
}
