/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FunctionInfo, FunctionList } from '@waterdesk/data/function-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetFunctionListResponse extends APIResponse {
  functionList?: FunctionList;
}

export const getFunctionList = async (
  appId?: string,
): Promise<GetFunctionListResponse> => {
  const res: any = await postRequest({
    code: 'portal/getFunctionList',
    params: {
      application_id: appId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const functionList: FunctionList = Array.isArray(
          res.value?.functionList,
        )
          ? res.value?.functionList.map(
              (item: any): FunctionInfo => ({
                id: item.FUNC_CODE ?? '',
                parentId: item.FUNC_PARENT_CODE ?? '',
                application: item.FUNC_APPLICATION ?? '',
                name: item.FUNC_NAME ?? '',
                json: item.FUNC_JSON ?? '',
                note: item.NOTE ?? '',
                type: item.FUNC_TYPE ?? '',
                startPage: item.FUNC_STARTPAGE ?? '',
                description: item.FUNC_DESC ?? '',
                createTime: item.CREATE_TIME ?? '',
                availability: item.FUNC_STAT_CD === '1',
                createUserId: item.CREATE_USER_ID ?? '',
                url: item.FUNC_URL ?? '',
                dataMode: item.URL_ARGS ? item.URL_ARGS.DATA_MODE : '',
              }),
            )
          : [];
        resolve({
          status: 'Success',
          functionList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
