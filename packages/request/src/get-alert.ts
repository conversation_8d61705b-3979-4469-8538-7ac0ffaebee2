/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AlertDefinition, AlertInstance } from '@waterdesk/data/alert';
import dayjs, { Dayjs } from 'dayjs';
import { PageParams } from './api/api-request';
import { DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetAlertDefinitionParams {
  typeId?: string; // 预警类型id
}

export interface GetAlertListParams {
  startDate?: Dayjs; // 开始日期
  endDate?: Dayjs; // 结束日期
  id?: string; // 预警id
  typeIdList?: string[]; // 预警类型id列表
  levelList?: string[]; // 预警级别列表
  content?: string; // 预警内容
  categoryList?: string[]; // 预警类别列表
  sourceList?: string[]; // 预警来源列表
}

/**
 * 查询预警定义列表
 */
export const getAlertDefinitionList = async (
  formData?: GetAlertDefinitionParams,
): Promise<DefaultListAPIResponse<AlertDefinition>> => {
  const params = {
    type_id: formData?.typeId,
  };

  const res: any = await postRequest({
    code: 'supply/queryEarlyWarningDefineList',
    params,
  });

  if (res?.json_ok) {
    const list: AlertDefinition[] = res.values.map((item: any) => {
      const levels = item.parameter ? JSON.parse(item.parameter) : [];
      return {
        id: item.type_id,
        title: item.type_title,
        description: item.description,
        category: item.category,
        status: item.status,
        levels,
        source: item.source,
        remark: item.remark,
      };
    });

    return {
      status: 'Success',
      total: list.length,
      list,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
};

/**
 * 查询当前预警列表(用于主页右侧顶部预警列表)
 */
export const getCurrentAlertList = async (): Promise<
  DefaultListAPIResponse<AlertInstance>
> => {
  const params = {
    time: dayjs().format('YYYY-MM-DD HH:mm:00'),
  };

  const res: any = await postRequest({
    code: 'supply/queryEarlyWarningListAtTime',
    params,
  });

  if (res?.json_ok) {
    const list: AlertInstance[] = res.values.records?.map((item: any) => ({
      id: item.record_id,
      definitionId: item.type_id,
      definitionTitle: item.type_title,
      definitionCategory: item.category,
      startTime: item.start_time,
      endTime: item.end_time,
      createTime: item.create_time,
      level: item.level,
      source: item.source,
      location: item.location,
      title: item.title,
      content: item.content,
      ptype: item.ptype,
      pname: item.pname,
      ptitle: item.ptitle,
      stime: item.stime,
      remark: item.remark,
    }));

    return {
      status: 'Success',
      total: res.values.total_count,
      list,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
};

/**
 * 查询预警记录列表
 */
export const getAlertListByDate = async (
  params: PageParams,
  formData?: GetAlertListParams,
): Promise<DefaultListAPIResponse<AlertInstance>> => {
  const requestParams = {
    start_time: dayjs(formData?.startDate)
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss'),
    end_time: dayjs(formData?.endDate)
      .endOf('day')
      .format('YYYY-MM-DD HH:mm:ss'),
    record_id: formData?.id,
    type_id_list: formData?.typeIdList?.toString(),
    level_list: formData?.levelList?.toString(),
    content: formData?.content,
    category_list: formData?.categoryList?.toString(),
    source_list: formData?.sourceList?.toString(),
    current: params.current,
    pageSize: params.pageSize,
  };

  const res: any = await postRequest({
    code: 'supply/queryEarlyWarningList',
    params: requestParams,
  });

  if (res?.json_ok) {
    const list: AlertInstance[] = res.values.records?.map((item: any) => ({
      id: item.record_id,
      definitionId: item.type_id,
      definitionTitle: item.type_title,
      definitionCategory: item.category,
      startTime: item.start_time,
      endTime: item.end_time,
      createTime: item.create_time,
      level: item.level,
      source: item.source,
      location: item.location,
      title: item.title,
      content: item.content,
      ptype: item.ptype,
      pname: item.pname,
      ptitle: item.ptitle,
      stime: item.stime,
      remark: item.remark,
    }));

    return {
      status: 'Success',
      total: res.values.total_count,
      list,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
};
