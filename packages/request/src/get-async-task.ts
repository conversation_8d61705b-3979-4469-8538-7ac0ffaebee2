/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export enum AsyncTaskStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
}

export interface GetAsyncTaskStatusResponse<T = any> extends APIResponse {
  taskStatus: AsyncTaskStatus;
  values?: T;
  message?: string;
  progress?: number;
}

export const getAsyncTaskStatus = async (
  taskId: string,
): Promise<GetAsyncTaskStatusResponse> => {
  const res: any = await postRequest({
    code: 'portal/getAsyncRequestJson',
    params: {
      task_id: taskId,
    },
  });

  if (res.json_ok) {
    if (res?.task_end) {
      return {
        status: 'Success',
        taskStatus: res.error ? AsyncTaskStatus.FAIL : AsyncTaskStatus.SUCCESS,
        errorMessage: res.error,
        message: res.message,
        values: res.values,
        progress: res.progress,
      };
    }
    return {
      status: 'Success',
      taskStatus: AsyncTaskStatus.IN_PROGRESS,
      message: res.message,
      values: res.values,
      progress: res.progress,
    };
  }

  return {
    status: 'Fail',
    taskStatus: AsyncTaskStatus.FAIL,
    errorMessage: res.json_msg,
    message: res.message,
    values: res.values,
    progress: res.progress,
  };
};
