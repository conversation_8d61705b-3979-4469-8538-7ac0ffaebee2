/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  BatchQueryListItem,
  BatchQueryParams,
} from '@waterdesk/data/batch-query-data';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface GetBatchQueryResponse extends APIResponse {
  list: BatchQueryListItem[];
  total: number;
}

export const getBatchQueryData = async (
  params: BatchQueryParams,
  time: string,
): Promise<any> => {
  const { otype, wkt, where, resultVprops, current, pageSize } = params;
  const res: any = await postRequestByView({
    code: 'watergis/queryWktVPROPFilterRecords',
    params: {
      otype,
      wkt,
      where: where?.length ? JSON.stringify(where) : '',
      resultVprops: resultVprops?.length ? JSON.stringify(resultVprops) : '',
      current,
      pageSize,
      time,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      list: res?.data?.data ?? [],
      total: res?.data?.total ?? 0,
    };
  }
  return {
    status: 'Fail',
    list: [],
    total: 0,
    errorMessage: res.json_msg,
  };
};
