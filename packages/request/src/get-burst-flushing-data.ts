/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  HighlightObject,
  ImpactedDma,
  ImpactedLink,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import {
  HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
  HighlightStyleType,
} from '@waterdesk/data/style-config';
import { APIResponse } from './api/api-response';
import { GetAsyncTaskStatusResponse } from './get-async-task';
import { postRequestByView } from './request';

export interface GetPipeBreakFlowResponse extends APIResponse {
  flow?: number;
  pipeDiameter?: number;
  pressure?: number;
  nodeOname?: string;
  nodeType?: string;
}

export interface BurstFlushingData {
  impactedLinksObjects?: HighlightObject[];
  impactedDmaObjects?: ImpactedDma[];
  burstFlushingDatas?: HighlightObject[];
  impactedLinksObjectsMap?: Map<string, ImpactedLink>;
}

export interface GetBurstFlushingDataResponse extends APIResponse {
  taskId?: string;
}

export interface GetBurstFlushingDataAsyncResponse
  extends GetAsyncTaskStatusResponse<BurstFlushingData> {}

export function formatValveAnalysisData(
  data: any[],
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): HighlightObject[] | ImpactedDma[] {
  const highlightObjects: HighlightObject[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shapeType: getShapeType(item.shape),
    });
  });
  return highlightObjects;
}

export function formatValveAnalysisDmaData(
  data: any[],
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): ImpactedDma[] {
  const highlightObjects: ImpactedDma[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shapeType: getShapeType(item.shape),
      title: item.title,
      pressureDiff: item.sub_press,
      currentPressure: item.cur_press,
    });
  });
  return highlightObjects;
}

export function formatValveAnalysisLinksDataToMap(
  data: any[],
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, ImpactedLink> {
  const linkObjects: Map<string, ImpactedLink> = new Map();
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const id = `${item.road_name}@${item.diameter}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: HighlightObject = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      diameter: item.diameter,
      length: item.length,
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shapeType: getShapeType(item.shape),
    };
    if (comboObject) {
      comboObject.length += item.length;
      if (
        comboObject.minEffectRatio &&
        item.flow_ratio &&
        comboObject.minEffectRatio > item.flow_ratio
      ) {
        comboObject.minEffectRatio = item.flow_ratio;
      }
      if (
        comboObject.maxEffectRatio &&
        item.flow_ratio &&
        comboObject.maxEffectRatio < item.flow_ratio
      ) {
        comboObject.maxEffectRatio = item.flow_ratio;
      }
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        roadName: item.road_name,
        diameter: item.diameter,
        length: item.length,
        minEffectRatio: item.flow_ratio ?? 100,
        maxEffectRatio: item.flow_ratio ?? 100,
        highlightObjects: [highlightObject],
      });
    }
  });
  return linkObjects;
}

export function formatBurstFlushingData(values: any): BurstFlushingData {
  const resultMap = {
    impactedLinksObjects: formatValveAnalysisData(
      values.back_links,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
    impactedLinksObjectsMap: formatValveAnalysisLinksDataToMap(
      values.back_links,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
    impactedDmaObjects: formatValveAnalysisDmaData(
      values.down_dma3,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
  };
  const highlightObjects: HighlightObject[] = [...resultMap.impactedDmaObjects];

  return {
    burstFlushingDatas: highlightObjects,
    impactedLinksObjects: resultMap.impactedLinksObjects,
    impactedDmaObjects: resultMap.impactedDmaObjects,
    impactedLinksObjectsMap: resultMap.impactedLinksObjectsMap,
  };
}

export async function getBurstFlushingData(
  time: string,
  otype: string,
  oname: string,
  flow: number,
): Promise<GetBurstFlushingDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/parsePipeBreak',
    params: {
      time,
      otype,
      oname,
      value: flow,
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (data.no_model) {
          throw Error(`模型未计算`);
        }
        resolve({
          status: 'Success',
          taskId: data.task_id,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

export async function getPipeBreakSuggestFlow(
  time: string,
  otype: string,
  oname: string,
): Promise<GetPipeBreakFlowResponse> {
  const data: any = await postRequestByView({
    code: 'supply/getPipeBreakSuggestFlow',
    params: {
      otime: time,
      otype,
      oname,
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (data.no_model) {
          throw Error(`模型未计算`);
        }
        resolve({
          status: 'Success',
          flow: data.values.suggest_flow,
          pipeDiameter: data.values.pipe_diam,
          pressure: data.values.node_press,
          nodeOname: data.values.node_name,
          nodeType: data.values.node_type,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}
