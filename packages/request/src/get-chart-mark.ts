/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MarkInfoItem,
  MarkInfoList,
  ScadaChartMark,
  ScadaListChartMark,
} from '@waterdesk/data/chart-mark';
import dayjs from 'dayjs';
import { PageParams, sorterMethodMap } from './api/api-request';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

type QueryMarkListParams = {
  startTime?: string;
  endTime?: string;
  otypeList?: string[];
  onameList?: string[];
  vpropList?: string[];
  title?: string;
  markTypeList?: string[];
  remark?: string;
} & PageParams;

export interface UpdateMarkResponse extends APIResponse {
  markId: string | string[];
}

export const addScadaChartMark = async (
  scadaListChartMark: ScadaListChartMark,
): Promise<UpdateMarkResponse> => {
  const { scadaList, startTime, endTime, type, description } =
    scadaListChartMark;
  const res: any = await postRequest({
    code: 'supply/addMarkInfoList',
    params: {
      info_list: JSON.stringify(scadaList),
      stime: startTime,
      etime: endTime,
      mark_type: type,
      remark: description,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          markId: res.values.mark_id,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          markId: '',
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        markId: '',
      });
    }
  });
};

export const updateScadaChartMark = async (
  scadaChartMark: ScadaChartMark,
): Promise<UpdateMarkResponse> => {
  const {
    markId,
    oname,
    otype,
    pname,
    ptype,
    vprop,
    startTime,
    endTime,
    type,
    description,
  } = scadaChartMark;
  const res: any = await postRequest({
    code: 'supply/updateMarkInfo',
    params: {
      mark_id: markId,
      oname,
      otype,
      pname,
      ptype,
      vprop,
      stime: startTime,
      etime: endTime,
      mark_type: type,
      remark: description,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          markId: res.values.mark_id,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          markId: '',
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        markId: '',
      });
    }
  });
};

export const deleteScadaChartMark = async (
  markId: string,
  otype: string,
  oname: string,
  vprop: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteMarkInfo',
    params: {
      mark_id: markId,
      otype,
      oname,
      vprop,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export interface GetMarkListResponse
  extends DefaultListAPIResponse<MarkInfoItem> {}

const sorterServerFieldMap: Record<string, string> = {
  startTime: 'stime',
  endTime: 'etime',
  createTime: 'create_time',
};

const getMarkList = async (
  params: QueryMarkListParams,
): Promise<GetMarkListResponse> => {
  const {
    startTime,
    endTime,
    pageSize,
    current,
    onameList,
    otypeList,
    vpropList,
    markTypeList,
    remark,
    title,
  } = params;

  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const res: any = await postRequest({
    code: 'supply/queryMarkInfoList',
    params: {
      start_time: startTime,
      end_time: endTime,
      pageSize,
      current,
      otype_list: otypeList?.join(),
      oname_list: onameList?.join(),
      vprop_list: vpropList?.join(),
      title,
      mark_type_list: markTypeList?.join(),
      remark,
      sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
      sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && Array.isArray(res?.values?.records)) {
        const markList: MarkInfoList = res.values.records.map(
          (item: any): MarkInfoItem => ({
            markId: item.mark_id ?? '',
            ptype: item.ptype,
            pname: item.pname,
            otype: item.otype,
            oname: item.oname,
            vprop: item.vprop,
            deviceName: item.otitle,
            startTime: item.stime ?? '',
            endTime: item.etime ?? '',
            type: item.mark_type ?? '',
            description: item.remark ?? '',
            user: item.user_name ?? '',
            createTime: item.create_time ?? '',
            eventId: item.event_name,
            eventName: item.event_title,
          }),
        );
        resolve({
          status: 'Success',
          list: markList,
          total: res.values.total_count ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total: 0,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        list: [],
        total: 0,
      });
    }
  });
};

export const getMarkListByDateTime = async (
  params: QueryMarkListParams,
): Promise<GetMarkListResponse> => {
  const { startTime, endTime } = params;
  return getMarkList({
    ...params,
    startTime: startTime
      ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    endTime: endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
  });
};

export const getMarkListByDate = async (
  params: Omit<QueryMarkListParams, 'startTime' | 'endTime'> & {
    startDate?: string;
    endDate?: string;
  },
): Promise<GetMarkListResponse> => {
  const { startDate, endDate } = params;
  return getMarkList({
    ...params,
    startTime: startDate
      ? dayjs(startDate).format('YYYY-MM-DD 00:00:00')
      : undefined,
    endTime: endDate
      ? dayjs(endDate).add(1, 'd').format('YYYY-MM-DD 00:00:00')
      : undefined,
  });
};
