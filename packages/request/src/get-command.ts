/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  CommandValveList,
  convertCommandDevice,
  DispatchCommandConfiguration,
} from '@waterdesk/data/dispatch-command/create-command';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetCurrentDispatchCommandConfiguration extends APIResponse {
  values: DispatchCommandConfiguration;
}

/** 获取当前水厂、泵站和阀门的调度指令配置 */
export const getCurrentDispatchCommandConfiguration = async (
  time: string,
): Promise<GetCurrentDispatchCommandConfiguration> => {
  const data: any = await postRequest({
    code: 'supply/getScheduleMode',
    params: {
      time,
    },
  });

  if (data.json_ok) {
    const waterPlants =
      data.values.factory_list?.map(convertCommandDevice) ?? [];
    const pumpStations =
      data.values.station_list?.map(convertCommandDevice) ?? [];
    let valveList: CommandValveList[] = [];

    try {
      valveList = data.values.valve_list
        ? JSON.parse(data.values.valve_list).map((item: any) => ({
            receiver: item.receiver,
            valveOperation: item.VALVE_OPERATION,
            allowCustom: item.allowCustom,
          }))
        : [];
    } catch (error) {
      console.error('解析 valve_list 失败:', error);
      valveList = [];
    }

    return {
      status: 'Success',
      values: {
        waterPlants,
        pumpStations,
        valveList,
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    values: {
      waterPlants: [],
      pumpStations: [],
      valveList: [],
    },
  };
};
