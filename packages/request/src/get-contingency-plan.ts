/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ContingencyPlan,
  ContingencyPlanAttachmentType,
} from '@waterdesk/data/contingency-plan/contingency-plan-data';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface QueryContingencyPlanListParams {
  startTime?: string;
  endTime?: string;
  planName?: string;
  planType?: string;
  planLevel?: number;
  pageSize?: number;
  pageNum?: number;
}

export const queryContingencyPlanInfo = async (params: {
  planId: string;
}): Promise<APIResponse & { data: ContingencyPlan | undefined }> => {
  const res: any = await postRequest({
    code: 'supply/queryContingencyPlanList',
    params: {
      planId: params.planId,
      pageSize: 1,
      pageNum: 1,
    },
  });
  if (res.json_ok) {
    const data = (res.data?.list.map(
      (item: any): ContingencyPlan => ({
        planId: item.planId,
        planName: item.planName,
        planType: item.planType,
        planLevel: item.planLevel,
        createTime: item.createTime,
        attachments: item.attachments,
        planContent: item.planContent,
        planNote: item.planNote,
      }),
    ) ?? [])[0];
    return {
      status: 'Success',
      data,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    data: undefined,
  };
};

export const queryContingencyPlanList = async (
  params: QueryContingencyPlanListParams,
): Promise<DefaultListAPIResponse<ContingencyPlan>> => {
  const res: any = await postRequest({
    code: 'supply/queryContingencyPlanList',
    params: {
      startTime: params.startTime,
      endTime: params.endTime,
      planName: params.planName,
      planType: params.planType,
      planLevel: params.planLevel,
      pageSize: params.pageSize || 10,
      pageNum: params.pageNum || 1,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      total: res.data?.total ?? 0,
      list:
        res.data?.list.map(
          (item: any): ContingencyPlan => ({
            planId: item.planId,
            planName: item.planName,
            planType: item.planType,
            planLevel: item.planLevel,
            createTime: item.createTime,
            attachments: item.attachments,
            planContent: item.planContent,
            planNote: item.planNote,
          }),
        ) ?? [],
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    total: 0,
    list: [],
  };
};

export const deleteContingencyPlan = async (
  planId: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteContingencyPlan',
    params: { planId },
  });

  return {
    status: res.json_ok ? 'Success' : 'Fail',
    errorMessage: res.json_msg,
  };
};

export const insertContingencyPlan = async (params: {
  planName: string;
  planType: string;
  planLevel: number;
  planContent?: string;
  planNote?: string;
}): Promise<APIResponse & { data?: { planId: string } }> => {
  const res: any = await postRequest({
    code: 'supply/insertContingencyPlan',
    params,
  });

  return {
    status: res.json_ok ? 'Success' : 'Fail',
    errorMessage: res.json_msg,
    data: res.json_ok ? { planId: res.data.planId } : undefined,
  };
};

export const updateContingencyPlan = async (params: {
  planId: string;
  planName: string;
  planType: string;
  planLevel: number;
  planContent?: string;
  planNote?: string;
}): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/updateContingencyPlan',
    params,
  });

  return {
    status: res.json_ok ? 'Success' : 'Fail',
    errorMessage: res.json_msg,
  };
};

export const addContingencyPlanAttachment = async (params: {
  planId: string;
  attachmentId: string;
  attachmentType: ContingencyPlanAttachmentType;
}): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/addContingencyPlanAttachment',
    params,
  });

  return {
    status: res.json_ok ? 'Success' : 'Fail',
    errorMessage: res.json_msg,
  };
};

export const deleteContingencyPlanAttachment = async (params: {
  planId: string;
  attachmentId: string;
}): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteContingencyPlanAttachment',
    params,
  });

  return {
    status: res.json_ok ? 'Success' : 'Fail',
    errorMessage: res.json_msg,
  };
};
