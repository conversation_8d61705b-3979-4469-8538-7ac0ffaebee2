/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import {
  FlowRelativeIndicatorValue,
  PressureRelativeIndicatorValue,
} from '@waterdesk/data/indicator';
import {
  getShapeCenter,
  getShapeType,
  makeObjectId,
} from '@waterdesk/data/object-item';
import {
  DEVICE_RELATION,
  HighlightStyleType,
} from '@waterdesk/data/style-config';
import { SELECT_ICON } from '@waterdesk/data/track-data';
import {
  getUnitFormat,
  getUnitValueWithSymbol,
} from '@waterdesk/data/unit-system';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetCorrelationDeviceResponse extends APIResponse {
  relativeIndicators?: PressureRelativeIndicatorValue[];
  relativeLineString?: HighlightObject[];
}

function getScadaValueUnitTitle(database: Database, otype: string) {
  const [, unitKey] = database.getPropertyTitleUnit(otype, 'SDVAL');
  const unit = unitKey ? getUnitFormat(unitKey) : undefined;
  return unit ? unit.unitSymbol : '';
}

function formatData(
  database: Database,
  data: any,
  highlightColor?: HighlightStyleType,
): PressureRelativeIndicatorValue | undefined {
  const { oname, otype, value, value_model: valueModel } = data;
  let title = oname;
  const indicator = database.getIndicator(otype, oname);
  let relativeIndicatorValue: PressureRelativeIndicatorValue | undefined;
  if (indicator) {
    if (indicator.ptype !== undefined && indicator.pname !== undefined) {
      const device = database.getDevice(indicator.ptype, indicator.pname);
      if (device !== undefined) {
        title = device.title;
        const color = data.highlightColor ?? highlightColor;
        const deviceId = makeObjectId(device.otype, device.oname);
        const indicateId = makeObjectId(data.otype, data.oname);
        relativeIndicatorValue = {
          key: indicateId,
          id: deviceId,
          oname: device.oname,
          otype: device.otype,
          indicatorType: data.otype,
          indicatorName: data.oname,
          shape: device.shape as string,
          shapeType: getShapeType(device.shape),
          title,
          coeff: Number(value),
          coeffWithSymbol: getUnitValueWithSymbol('CORRELATION', value),
          modelCoeff: valueModel,
          modelCoeffWithSymbol: getUnitValueWithSymbol(
            'CORRELATION',
            valueModel,
          ),
          scadaValue: undefined,
          scadaValueUnit: getScadaValueUnitTitle(database, data.otype),
          highlightIcon: SELECT_ICON,
          highlightType: color ?? 'pollutedTrackSelect',
        };
      }
    }
  }
  return relativeIndicatorValue;
}

export async function getPressureCorrelationDevices(
  otype: string,
  oname: string,
  database: Database,
  date: string,
  mode: 'SCADA' | 'BOTH' = 'SCADA',
): Promise<GetCorrelationDeviceResponse> {
  const data: any = await postRequest({
    code: 'watergis/getScadaCorrelationNames',
    params: {
      otype,
      oname,
      time: date,
      mode,
    },
  });

  if (data.json_ok && Array.isArray(data.values)) {
    const relativeIndicators: PressureRelativeIndicatorValue[] = [];
    data.values.forEach((item: any) => {
      if (typeof oname === 'string' && typeof otype === 'string') {
        const relativeIndicatorValue = formatData(database, item);
        if (relativeIndicatorValue) {
          relativeIndicators.push(relativeIndicatorValue);
        }
      }
    });

    return {
      status: 'Success',
      relativeIndicators,
      relativeLineString: [],
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

function formatRelation(value: string): string {
  switch (value) {
    case 'UPPER_REACHES':
      return 'UP_STREAM';
    case 'LOWER_REACHES':
      return 'DOWN_STREAM';
    default:
      return 'ADJACENT';
  }
}

function formatFlowData(
  database: Database,
  data: any,
  highlightColor?: HighlightStyleType,
): FlowRelativeIndicatorValue | undefined {
  const { oname, otype, relation } = data;
  let title = oname;
  const indicator = database.getIndicator(otype, oname);
  let relativeIndicatorValue: FlowRelativeIndicatorValue | undefined;
  if (indicator) {
    if (indicator.ptype !== undefined && indicator.pname !== undefined) {
      const device = database.getDevice(indicator.ptype, indicator.pname);
      if (device !== undefined) {
        title = device.title;
        const color = data.highlightColor ?? highlightColor;
        const deviceId = makeObjectId(device.otype, device.oname);
        const indicateId = makeObjectId(data.otype, data.oname);
        relativeIndicatorValue = {
          key: indicateId,
          id: deviceId,
          oname: device.oname,
          otype: device.otype,
          indicatorType: data.otype,
          indicatorName: data.oname,
          shape: device.shape as string,
          shapeType: getShapeType(device.shape),
          title,
          relation: formatRelation(relation),
          scadaValue: undefined,
          scadaValueUnit: getScadaValueUnitTitle(database, data.otype),
          highlightIcon: SELECT_ICON,
          highlightType: color ?? 'pollutedTrackSelect',
        };
      }
    }
  }
  return relativeIndicatorValue;
}

function getRelativeLineString(
  database: Database,
  otype: string,
  oname: string,
  relativeIndicators: FlowRelativeIndicatorValue[],
): HighlightObject[] {
  const indicator = database.getIndicator(otype, oname);
  if (
    indicator &&
    indicator.ptype !== undefined &&
    indicator.pname !== undefined
  ) {
    const device = database.getDevice(indicator.ptype, indicator.pname);
    const deviceShape = device?.shape;
    if (deviceShape) {
      const deviceCoordinate = getShapeCenter(deviceShape);
      const relativeLineStringShapes: HighlightObject[] = [];
      relativeIndicators.forEach((item) => {
        const indicatorShape = getShapeCenter(item.shape);
        if (item.relation === 'UP_STREAM') {
          const shape = `LINESTRING(${indicatorShape[0]} ${indicatorShape[1]},${deviceCoordinate[0]} ${deviceCoordinate[1]})`;
          relativeLineStringShapes.push({
            oname: '',
            otype: '',
            id: `Line${item.id}`,
            shape,
            highlightColor: '#ff8e16',
            shapeType: 'LINE',
            highlightType: DEVICE_RELATION,
          });
        }
        if (item.relation === 'DOWN_STREAM') {
          const shape = `LINESTRING(${deviceCoordinate[0]} ${deviceCoordinate[1]},${indicatorShape[0]} ${indicatorShape[1]})`;
          relativeLineStringShapes.push({
            oname: '',
            otype: '',
            id: `Line${item.id}`,
            shape,
            highlightColor: '#1677ff',
            shapeType: 'LINE',
            highlightType: DEVICE_RELATION,
          });
        }
      });
      return relativeLineStringShapes;
    }
  }
  return [];
}

export interface GetFlowCorrelationDeviceResponse extends APIResponse {
  relativeIndicators?: FlowRelativeIndicatorValue[];
  relativeLineString?: HighlightObject[];
}

export async function getFlowCorrelationDevices(
  otype: string,
  oname: string,
  database: Database,
  date: string,
): Promise<GetFlowCorrelationDeviceResponse> {
  const data: any = await postRequest({
    code: 'watergis/getScadaCorrelationFlow',
    params: {
      otype,
      oname,
      time: date,
    },
  });

  if (data.json_ok && Array.isArray(data.values)) {
    const relativeIndicators: FlowRelativeIndicatorValue[] = [];
    data.values.forEach((item: any) => {
      if (typeof oname === 'string' && typeof otype === 'string') {
        const relativeIndicatorValue = formatFlowData(database, item);
        if (relativeIndicatorValue) {
          relativeIndicators.push(relativeIndicatorValue);
        }
      }
    });

    const relativeLineString = getRelativeLineString(
      database,
      otype,
      oname,
      relativeIndicators,
    );

    return {
      status: 'Success',
      relativeIndicators,
      relativeLineString,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
