/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DemandSummary,
  DMAInfo,
  MeterInfo,
} from '@waterdesk/data/demand-statistics';
import { TimeData } from '@waterdesk/data/time-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import {
  AsyncTaskStatus,
  GetAsyncTaskStatusResponse,
  getAsyncTaskStatus,
} from './get-async-task';
import { postRequestByView } from './request';

export interface GetDemandByPolygonTaskResponse extends APIResponse {
  taskId?: string;
}

export interface DemandByPolygonValues {
  demandSummary?: DemandSummary;
  todayDemandTimeData?: TimeData[];
  yesterdayDemandTimeData?: TimeData[];
  dmaList?: DMAInfo[];
  meterList?: MeterInfo[];
}

export interface GetDemandByPolygonResultResponse
  extends GetAsyncTaskStatusResponse<DemandByPolygonValues> {}

function getTimeData(data: any, date: string): TimeData[] {
  const timeData: Array<TimeData> = [];
  if (Array.isArray(data)) {
    data.forEach((item: any) => {
      const { otime, value } = item;
      if (typeof otime === 'string' && otime.startsWith(date)) {
        if (typeof value === 'number') timeData.push({ time: otime, value });
        else if (typeof value === 'string')
          timeData.push({ time: otime, value: Number(value) });
      }
    });
  }

  return timeData;
}

function getDmaInfo(data: any): DMAInfo | undefined {
  if (
    typeof data.otype !== 'string' ||
    typeof data.oname !== 'string' ||
    typeof data.TITLE !== 'string' ||
    typeof data.SHAPE !== 'string'
  )
    return undefined;

  const dmaInfo: DMAInfo = {
    otype: data.otype,
    oname: data.oname,
    shape: data.SHAPE,
    title: data.TITLE,
    data: new Map<string, any>(),
  };
  Object.entries(data).forEach((entry) => {
    const [key, value] = entry;
    if (
      key !== 'otype' &&
      key !== 'oname' &&
      key !== 'SHAPE' &&
      key !== 'TITLE'
    )
      dmaInfo.data.set(key, value);
  });

  return dmaInfo;
}

function getDmaInfoList(data: any): DMAInfo[] {
  if (!Array.isArray(data)) return [];

  const dmaList: DMAInfo[] = [];
  data.forEach((item) => {
    const dmaInfo = getDmaInfo(item);
    if (dmaInfo !== undefined) dmaList.push(dmaInfo);
  });

  return dmaList;
}

function getMeterInfo(data: any): MeterInfo | undefined {
  if (
    typeof data.otype !== 'string' ||
    typeof data.oname !== 'string' ||
    typeof data.NAME !== 'string' ||
    typeof data.SHAPE !== 'string'
  )
    return undefined;

  const meterInfo: MeterInfo = {
    otype: data.otype,
    oname: data.oname,
    shape: data.SHAPE,
    name: data.NAME,
    data: new Map<string, any>(),
  };
  Object.entries(data).forEach((entry) => {
    const [key, value] = entry;
    if (key !== 'otype' && key !== 'oname' && key !== 'SHAPE' && key !== 'NAME')
      meterInfo.data.set(key, value);
  });

  return meterInfo;
}

function getMeterInfoList(data: any): MeterInfo[] {
  if (!Array.isArray(data)) return [];

  const meterList: MeterInfo[] = [];
  data.forEach((item) => {
    const meterInfo = getMeterInfo(item);
    if (meterInfo !== undefined) meterList.push(meterInfo);
  });

  return meterList;
}

export async function getDemandByPolygonTask(
  date: string,
  geometry: number[][],
): Promise<GetDemandByPolygonTaskResponse> {
  let polygon = '';
  geometry.forEach((coordinates: number[]) => {
    if (Array.isArray(coordinates) && coordinates.length === 2) {
      if (polygon.length !== 0) polygon += ',';
      polygon += `${coordinates[0]},${coordinates[1]}`;
    }
  });
  const startTime = dayjs(date).add(-1, 'day').format('YYYY-MM-DD 00:00:00');
  const endTime = `${date} 23:59:59`;
  const data: any = await postRequestByView({
    code: 'supply/summaryWaterByPolygon',
    params: {
      start_time: startTime,
      end_time: endTime,
      polygon,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      taskId: data.task_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getDemandByPolygonResult(
  date: string,
  taskId: string,
): Promise<GetDemandByPolygonResultResponse> {
  const res = await getAsyncTaskStatus(taskId);
  if (res.status === 'Success') {
    if (res.taskStatus !== AsyncTaskStatus.SUCCESS)
      return { status: 'Success', taskStatus: AsyncTaskStatus.IN_PROGRESS };

    if (res.taskStatus === AsyncTaskStatus.SUCCESS)
      return {
        status: 'Success',
        taskStatus: AsyncTaskStatus.SUCCESS,
        values: {
          demandSummary: {
            totalUserCount: res.values.summary.all_user_count,
            dmaUserCount: res.values.summary.dma_user_count,
            otherUserCount: res.values.summary.watermeter_count,
            totalDemand: res.values.summary.demand_flows,
          },
          todayDemandTimeData: getTimeData(res.values.node_flow, date),
          yesterdayDemandTimeData: getTimeData(
            res.values.node_flow,
            dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
          ),
          dmaList: getDmaInfoList(res.values.dma3),
          meterList: getMeterInfoList(res.values.watermeter),
        },
      };
  }

  return {
    status: 'Fail',
    errorMessage: res.errorMessage,
    taskStatus: AsyncTaskStatus.FAIL,
  };
}
