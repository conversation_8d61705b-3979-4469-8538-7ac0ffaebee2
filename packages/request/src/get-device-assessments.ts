/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MODEL_SUMMARY_OBJECT,
  SCADA_SUMMARY_OBJECT,
} from '@waterdesk/data/const/system-const';
import InvisibleObject from '@waterdesk/data/invisible-object';
import {
  InitialDeviceInfo,
  InitialDeviceInfoValues,
} from '@waterdesk/data/mini-dashboard/device-dashboard-data';

import { APIResponse } from './api/api-response';
import {
  GetGroupPropValuesResponse,
  getGroupPropValues,
} from './get-group-prop-values';
import { postRequest } from './request';

export interface GetDeviceAssessmentsResponse extends APIResponse {
  devices?: InitialDeviceInfoValues;
}

export interface GetAssessmentScoreResponse extends APIResponse {
  data: {
    values: GetGroupPropValuesResponse['values'];
  };
}

/**
 * 获取设备评估数据
 * @param params
 * @returns
 */
export const getDeviceAssessments = async (params: {
  // time as YYYY-MM-DD
  time: string;
  otype_list: string;
  vprop_list: string;
}): Promise<GetDeviceAssessmentsResponse> => {
  const res: any = await postRequest({
    code: 'watergis/getLastOTYPEValues',
    params,
  });

  return new Promise((resolve) => {
    if (res.json_ok && res.values) {
      try {
        const devices: Map<string, Map<string, InitialDeviceInfo>> = new Map();
        Object.entries(res.values).forEach((item) => {
          const [otype, obj] = item;
          const deviceOTypeObj: Map<string, InitialDeviceInfo> = new Map();
          if (obj) {
            Object.entries(obj).forEach((items) => {
              const [oname, info] = items;
              if (info) deviceOTypeObj.set(oname, info);
            });
          }
          devices.set(otype, deviceOTypeObj);
        });
        resolve({
          status: 'Success',
          devices,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    } else {
      resolve({
        status: 'Fail',
        errorMessage: res.json_msg,
      });
    }
  });
};

export const getDeviceScore = async (
  vprops: string[],
  time: string,
  viewId?: string,
  summaryObject?: InvisibleObject,
): Promise<GetAssessmentScoreResponse> => {
  const valueJson: { [index: string]: { vprop: string } } = {};
  vprops.forEach((vprop) => {
    valueJson[vprop] = { vprop };
  });
  const summaryObjectInfo = summaryObject ?? SCADA_SUMMARY_OBJECT;
  const res = await getGroupPropValues(
    summaryObjectInfo.otype,
    summaryObjectInfo.oname,
    valueJson,
    time,
    viewId,
  );
  if (res.status === 'Success') {
    return {
      status: 'Success',
      data: {
        values: res.values,
      },
    };
  }

  return {
    status: 'Fail',
    data: {
      values: undefined,
    },
    errorMessage: res.errorMessage,
  };
};

export const getSimulationScore = async (
  vprops: string[],
  time: string,
  viewId?: string,
): Promise<GetAssessmentScoreResponse> => {
  const valueJson: { [index: string]: { vprop: string } } = {};
  vprops.forEach((vprop) => {
    valueJson[vprop] = { vprop };
  });
  const res = await getGroupPropValues(
    MODEL_SUMMARY_OBJECT.otype,
    MODEL_SUMMARY_OBJECT.oname,
    valueJson,
    time,
    viewId,
  );
  if (res.status === 'Success') {
    return {
      status: 'Success',
      data: {
        values: res.values,
      },
    };
  }

  return {
    status: 'Fail',
    data: {
      values: undefined,
    },
    errorMessage: res.errorMessage,
  };
};
