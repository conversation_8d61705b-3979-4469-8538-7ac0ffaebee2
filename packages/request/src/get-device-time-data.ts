/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import Device from '@waterdesk/data/device';
import {
  DeviceStateData,
  DeviceTimeData,
  ScadaModelTimeData,
} from '@waterdesk/data/device-time-data';
import { IndicatorObject } from '@waterdesk/data/indicator';
import { makeObjectId, splitId } from '@waterdesk/data/object-item';
import { getUnitValue } from '@waterdesk/data/unit-system';
import dayjs, { Dayjs } from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { PageParams, sorterMethodMap } from './api/api-request';
import { APIResponse } from './api/api-response';
import { postRequest, postRequestByView } from './request';

export interface GetDeviceDataResponse extends APIResponse {
  scadaModelTimeData?: Map<string, ScadaModelTimeData>;
}

export interface GetDeviceStateResponse extends APIResponse {
  data: Map<string, DeviceStateData>;
}

export interface GetDeviceStateListResponse extends APIResponse {
  list: DeviceStateData[];
  total: number;
}

function getUnit(
  otype: string,
  vprop: string,
  db: Database,
): string | undefined {
  const propertyInfo = db?.getPropertyInfo(otype);
  return propertyInfo?.getPropertyUnit(vprop);
}

export async function createDeviceStateRecord(params: {
  otype: string;
  oname: string;
  otime: string;
  state: string;
  source?: string;
  note?: string;
}): Promise<APIResponse> {
  const { otype, oname, otime, state, source, note } = params;
  const res: any = await postRequest({
    code: 'supply/setDeviceState',
    params: {
      otype,
      oname,
      otime,
      state,
      source,
      note,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
  };
}

const convertOriginalData = (
  originalData: any,
  db?: Database,
): DeviceStateData => {
  const { otype, oname, otime, state, source, note, creator, stime } =
    originalData;
  const id = makeObjectId(otype, oname);
  const typeTitle = db?.getPropertyInfo(otype)?.title ?? otype;
  const object = db?.getIndicator(otype, oname) ?? db?.getDeviceById(id);
  const title = object?.title ?? object?.title ?? '';

  let shape;
  let dataType: 'device' | 'indicator' | undefined;
  let ptype;
  let pname;
  if (object instanceof IndicatorObject) {
    if (object.ptype && object.pname) {
      shape = db?.getDeviceById(
        makeObjectId(object.ptype, object.pname),
      )?.shape;
    }
    dataType = 'indicator';
    ptype = object.ptype;
    pname = object.pname;
  } else if (object instanceof Device) {
    shape = object?.shape;
    dataType = 'device';
  }
  return {
    key: uuidv4(),
    id,
    title,
    typeTitle,
    otype,
    oname,
    otime,
    state: state !== 1, // 0: good, 1: bad
    source,
    note,
    creator,
    stime,
    shape,
    dataType,
    ptype,
    pname,
  };
};

const convertOriginalDataToList = (originalData?: any, db?: Database) =>
  originalData?.map((m: any) => convertOriginalData(m, db));

export async function getDeviceStateList(
  params: {
    startTime?: string;
    endTime?: string;
    otype?: string;
    oname?: string;
    pageSize?: number;
    current?: number;
    sourceList?: string[];
    ids?: string[];
  },
  db?: Database,
): Promise<GetDeviceStateListResponse> {
  const res: any = await postRequest({
    code: 'supply/getDeviceStateList',
    params: {
      start_time: params.startTime,
      end_time: params.endTime,
      otype: params.otype,
      oname: params.oname,
      ids: params.ids?.join(),
      pageSize: params.pageSize,
      current: params.current,
      source_list: params?.sourceList?.join(),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      list: convertOriginalDataToList(res.values.records, db),
      total: res.values?.total_count ?? 0,
    };
  }

  return {
    status: 'Fail',
    list: [],
    total: 0,
  };
}

const sorterServerFieldMap: Record<string, string> = {
  otime: 'otime',
  stime: 'stime',
};

export async function getDeviceStateListByDate(
  params: {
    startDate?: string | Dayjs;
    endDate?: string | Dayjs;
    otype?: string;
    oname?: string;
    sourceList?: string[];
    title?: string;
    state?: number;
    ids?: string[];
  } & PageParams,
  db?: Database,
): Promise<GetDeviceStateListResponse> {
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const res: any = await postRequest({
    code: 'supply/getDeviceStateList',
    params: {
      start_time: dayjs(params?.startDate).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(params?.endDate).format('YYYY-MM-DD 23:59:59'),
      otype: params.otype,
      oname: params.oname,
      ids: params.ids?.join(),
      pageSize: params.pageSize,
      current: params.current,
      source_list: params?.sourceList?.join(),
      title: params.title,
      state: params.state,
      sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
      sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      list: convertOriginalDataToList(res.values.records, db),
      total: res.values?.total_count ?? 0,
    };
  }

  return {
    status: 'Fail',
    list: [],
    total: 0,
  };
}

export async function getDeviceStateByDateTime(
  params: {
    dateTime: string;
    otype?: string;
    oname?: string;
    sourceList?: string[];
    status?: boolean;
  },
  db?: Database,
): Promise<GetDeviceStateResponse> {
  const res: any = await postRequest({
    code: 'supply/getRecentDeviceStateByTime',
    params: {
      time: params.dateTime,
      otype: params.otype,
      oname: params.oname,
      source_list: params?.sourceList?.join(),
      status: params.status,
    },
  });

  const data: Map<string, DeviceStateData> = new Map();
  if (res.json_ok) {
    res.values.records?.forEach((record: any) => {
      const { otype, oname } = record;
      const id = makeObjectId(otype, oname);
      const convertData = convertOriginalData(record, db);
      data.set(id, convertData);
    });

    return {
      status: 'Success',
      data,
    };
  }

  return {
    status: 'Fail',
    data,
  };
}
export async function getLatestDeviceStateRecords(
  params: {
    dateTime: string;
    status?: boolean;
  },
  db?: Database,
): Promise<GetDeviceStateResponse> {
  const res: any = await postRequest({
    code: 'supply/getLatestDeviceStateRecords',
    params: {
      time: params.dateTime,
      status: params.status,
    },
  });

  const data: Map<string, DeviceStateData> = new Map();
  if (res.json_ok) {
    res.values.records?.forEach((record: any) => {
      const { otype, oname } = record;
      const id = makeObjectId(otype, oname);
      const convertData = convertOriginalData(record, db);
      data.set(id, convertData);
    });

    return {
      status: 'Success',
      data,
    };
  }

  return {
    status: 'Fail',
    data,
  };
}

export async function getLatestScadaValues(db: Database) {
  const [resRaw, resModel, resDeviceState] = await Promise.all([
    postRequest({
      code: 'watergis/getLatestScadaRawValues',
    }),
    postRequest({
      code: 'watergis/getLatestScadaModelValues',
    }),
    getDeviceStateByDateTime({
      dateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    }),
  ]);

  const scadaModelTimeData: Map<string, ScadaModelTimeData> = new Map();

  if (resRaw.json_ok) {
    resRaw.values.forEach((item: any) => {
      const [otype, oname] = splitId(item.OID);
      const id = item.OID;
      const scadaVprop = item.VPROP ?? 'SDVAL';
      const scadaUnit = getUnit(otype, scadaVprop, db);

      const currentIndicatorData: ScadaModelTimeData = {
        id,
        oname,
        otype,
        scadaData: {
          oname,
          otype,
          vprop: scadaVprop,
          time: item.TIME,
          originalValue: item.VALUE,
          value: scadaUnit ? getUnitValue(scadaUnit, item.VALUE) : item.VALUE,
          unit: scadaUnit,
          latestDeviceState: resDeviceState.data.get(id)?.state !== false,
        },
        simulationData: undefined,
      };

      scadaModelTimeData.set(id, currentIndicatorData);
    });
  }

  if (resModel.json_ok) {
    resModel.values.forEach((item: any) => {
      const id = item.OID;
      const simulationUnit = getUnit(item.MTYPE, item.MPROP, db) ?? undefined;
      const existingData = scadaModelTimeData.get(id);
      if (existingData) {
        const currentIndicatorData = {
          oname: item.MNAME,
          otype: item.MTYPE,
          vprop: item.MPROP,
          time: item.TIME,
          originalValue: item.VALUE,
          value: simulationUnit
            ? getUnitValue(simulationUnit, item.VALUE)
            : item.VALUE,
          unit: simulationUnit,
        };

        existingData.simulationData = currentIndicatorData as DeviceTimeData;
        scadaModelTimeData.set(id, existingData);
      }
    });
  }

  if (resDeviceState.status === 'Success') {
    resDeviceState.data.forEach((item) => {
      if (!scadaModelTimeData.has(item.id)) {
        const currentIndicatorData: ScadaModelTimeData = {
          id: item.id,
          oname: item.oname,
          otype: item.otype,
          scadaData: {
            oname: item.oname,
            otype: item.otype,
            vprop: '',
            time: '',
            originalValue: undefined,
            value: undefined,
            unit: undefined,
            latestDeviceState: item.state !== false,
          },
          simulationData: undefined,
        };

        scadaModelTimeData.set(item.id, currentIndicatorData);
      }
    });
  }

  if (scadaModelTimeData.size > 0) {
    return {
      status: 'Success',
      scadaModelTimeData,
    };
  }

  return {
    status: 'Fail',
    scadaModelTimeData: new Map(),
  };
}

export default async function getDeviceTimeData(
  timeString: string,
  _timeState: boolean,
  db: Database,
): Promise<GetDeviceDataResponse> {
  const [res, resDeviceState]: [any, GetDeviceStateResponse] =
    await Promise.all([
      postRequestByView({
        code: 'watergis/getLastRawAndRefValues2',
        params: {
          time: timeString,
          ref_name: 'CALCULATION',
        },
      }),
      getDeviceStateByDateTime({
        dateTime: timeString,
      }),
    ]);

  if (res.json_ok && resDeviceState.status === 'Success') {
    const scadaModelTimeData: Map<string, ScadaModelTimeData> = new Map();
    res.values.forEach((item: any) => {
      const id = makeObjectId(item.OTYPE, item.ONAME);
      const scadaVprop = item.VPROP ?? 'SDVAL';
      const scadaUnit = getUnit(item.OTYPE, scadaVprop, db);
      const simulationUnit = item.CALCULATION
        ? getUnit(item.CALCULATION.OTYPE, item.CALCULATION.VPROP, db)
        : undefined;
      const currentIndicatorData: ScadaModelTimeData = {
        id,
        oname: item.ONAME,
        otype: item.OTYPE,
        scadaData: item._RAW_ && {
          oname: item.ONAME,
          otype: item.OTYPE,
          vprop: scadaVprop,
          time: item._RAW_.TIME,
          originalValue: item._RAW_.VALUE,
          value: scadaUnit
            ? getUnitValue(scadaUnit, item._RAW_.VALUE)
            : item._RAW_.VALUE,
          unit: scadaUnit,
          latestDeviceState: resDeviceState.data.get(id)?.state !== false,
        },
        simulationData: item.CALCULATION && {
          oname: item.CALCULATION.ONAME,
          otype: item.CALCULATION.OTYPE,
          vprop: item.CALCULATION.VPROP,
          time: item.CALCULATION.TIME,
          originalValue: item.CALCULATION.VALUE,
          value: simulationUnit
            ? getUnitValue(simulationUnit, item.CALCULATION.VALUE)
            : item.CALCULATION.VALUE,
          unit: simulationUnit,
        },
      };
      scadaModelTimeData.set(id, currentIndicatorData);
    });

    resDeviceState.data.forEach((item) => {
      if (!scadaModelTimeData.has(item.id)) {
        const currentIndicatorData: ScadaModelTimeData = {
          id: item.id,
          oname: item.oname,
          otype: item.otype,
          scadaData: {
            oname: item.oname,
            otype: item.otype,
            vprop: '',
            time: '',
            originalValue: undefined,
            value: undefined,
            unit: undefined,
            latestDeviceState: item.state !== false,
          },
          simulationData: undefined,
        };

        scadaModelTimeData.set(item.id, currentIndicatorData);
      }
    });

    return {
      status: 'Success',
      scadaModelTimeData,
    };
  }

  return {
    status: 'Fail',
  };
}
