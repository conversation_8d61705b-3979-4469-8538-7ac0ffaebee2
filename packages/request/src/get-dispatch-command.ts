/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeleteDispatchCommandParams,
  DispatchCommandCommandObject,
  DispatchCommandList,
  DispatchCommandListParams,
  DispatchCommandSendOrganization,
  DispatchCommandState,
  UpdateCommandReceiveInfo,
  UpdateCommandReplyInfo,
  UpdateCommandTimelyExecuteInfo,
} from '@waterdesk/data/dispatch-command/command-list';
import {
  CommandType,
  CreateCommandList,
  DeviceAction,
  NotificationType,
} from '@waterdesk/data/dispatch-command/create-command';
import { formatDayjsInObject } from '@waterdesk/data/string';
import { PageParams } from './api/api-request';
import {
  APIResponse,
  DefaultListBackendResponse,
  HasTotalCountBackendResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest } from './request';

export type GetDispatchCommandListStateCount = {
  [key in keyof typeof DispatchCommandState]?: number;
};

interface CreateDispatchCommandRequestInBackend {
  command_type?: CommandType; // 指令类型
  command_send_organization?: string; // 指令发送组织
  command_receive_organization?: string; // 指令执行组织
  command_object?: DeviceAction; // 指令对象
  command_state?: string; // 指令状态
  command_content?: string; // 指令内容
  command_plan_time?: string; // 指令计划执行时间
  command_note?: string; // 指令备注
  notification_method?: number; // 通知方式：1-系统弹窗 2-短信通知 3-系统弹窗加短信通知
  notification_user_ids?: string; // 通知用户id
  notification_department_ids?: string; // 通知部门id
  dispatch_suggest_id?: string;
  source_type?: number;
}

interface CreateDispatchCommandResponseInBackend {
  command_id?: string;
}

interface DeleteDispatchCommandRequestInBackend {
  command_id_list?: string; // 指令id列表
}

interface GetDispatchCommandListRequestInBackend extends PageParams {
  command_id?: string; // 指令id
  command_type?: CommandType; // 指令类型
  command_receive_organization?: string; // 指令执行组织
  send_time_start?: string; // 指令发送时间
  send_time_end?: string; // 指令发送时间
  receive_time_start?: string; // 指令接收时间
  receive_time_end?: string; // 指令接收时间
  command_state?: string; // 指令状态
  is_send_user?: boolean;
}

interface GetDispatchCommandListResponseInBackend {
  command_id: string; // 指令id
  command_type: CommandType; // 指令类型
  source_type: number; // 指令来源
  command_send_organization: DispatchCommandSendOrganization; // 指令发送组织
  command_receive_organization: string; // 指令执行组织
  command_object: DispatchCommandCommandObject; // 指令对象
  command_state: DispatchCommandState; // 指令状态
  command_content: string; // 指令内容
  command_plan_time: string; // 指令计划执行时间
  command_note: string; // 指令备注
  command_send_time: string; // 指令发送时间
  command_sender: string; // 指令发送人
  command_receive_time: string; // 指令接收时间
  command_receiver: string; // 指令接收人
  command_reply_time: string; // 指令回复时间
  command_reply_content: string; // 指令回复内容
  command_reply_people: string; // 指令回复人
  command_timely_execute: string; // 执行及时性
  command_timely_reply: number; // 回复及时性
  command_execute_time: string; // 指令执行时间
  command_execute_note: string; // 指令执行备注
  event_name: string; // 关联事件编号
  event_title: string; // 关联事件名称
  notification_department_names: string; // 通知部门名称
  notification_user_names: string; // 通知用户名称
  notification_method: number; // 通知方式
}

interface UpdateCommandReceiveInfoRequestInBackend {
  command_id?: string; // 指令id
  command_state?: DispatchCommandState; // 指令状态
}

interface UpdateCommandReplyInfoRequestInBackend {
  command_id?: string; // 指令id
  command_state?: DispatchCommandState; // 指令状态
  command_reply_content?: string; // 指令回复内容
}

interface UpdateCommandTimelyExecuteInfoRequestInBackend {
  command_id?: string; // 指令id
  command_timely_execute?: string; // 执行及时性
  command_execute_time?: string; // 指令执行时间
  command_execute_note?: string; // 指令执行备注
}

interface CreateDispatchCommandResponse extends APIResponse {
  commandId?: string;
}

interface GetDispatchCommandListResponse extends APIResponse {
  total: number;
  list: DispatchCommandList[];
  count: GetDispatchCommandListStateCount;
}

/** 创建调度指令 */
export const createDispatchCommand = async (
  formData?: CreateCommandList,
): Promise<CreateDispatchCommandResponse> => {
  const typeNumber = formData?.notificationType?.reduce((result, type) => {
    if (type === NotificationType.SYSTEM) return result + 1;
    if (type === NotificationType.SMS) return result + 2;
    if (type === NotificationType.WECOM) return result + 4;
    return result;
  }, 0);
  const params: CreateDispatchCommandRequestInBackend = {
    command_type: formData?.type,
    command_send_organization: formData?.sendOrganization,
    command_receive_organization: formData?.receiveOrganization,
    command_object: formData?.object,
    command_state: formData?.state?.toString() ?? '',
    command_content: JSON.stringify(formData?.content),
    command_plan_time: formData?.planTime,
    command_note: formData?.note,
    notification_method: typeNumber,
    notification_user_ids: formData?.notificationUser?.join(','),
    notification_department_ids: formData?.notificationDepartment?.join(','),
    dispatch_suggest_id: formData?.dispatchSuggestId,
    source_type: formData?.sourceType ?? 1, // 默认为1， 表示人工指令
  };

  const res: any = (await postRequest({
    code: 'watergis/createDispatchCommand',
    params,
  })) as DefaultListBackendResponse<CreateDispatchCommandResponseInBackend>;

  if (res?.json_ok) {
    return {
      commandId: res?.values?.command_id,
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 删除调度指令 */
export const deleteDispatchCommand = withApiResponseHandler(
  async (formData?: DeleteDispatchCommandParams) => {
    const params: DeleteDispatchCommandRequestInBackend = {
      command_id_list: formData?.idList?.toString(),
    };

    const res: any = await postRequest({
      code: 'watergis/deleteDispatchCommand',
      params,
    });

    return res;
  },
);

export const convertDispatchCommandList = (
  list: GetDispatchCommandListResponseInBackend[],
): DispatchCommandList[] =>
  list.map((i) => ({
    id: i.command_id,
    type: i.command_type,
    sourceType: i.source_type ?? 1,
    sendOrganization: i.command_send_organization,
    receiveOrganization: i.command_receive_organization,
    object: i.command_object,
    state: i.command_state,
    content: i.command_content,
    planTime: i.command_plan_time,
    note: i.command_note,
    sendTime: i.command_send_time,
    sender: i.command_sender,
    receiveTime: i.command_receive_time,
    receiver: i.command_receiver,
    replyTime: i.command_reply_time,
    replyContent: i.command_reply_content,
    replyPeople: i.command_reply_people,
    timelyExecute: i.command_timely_execute,
    executeTime: i.command_execute_time,
    executeNote: i.command_execute_note,
    eventName: i?.event_name,
    eventTitle: i?.event_title,
    timelyReply: i.command_timely_reply,
    notification: [i.notification_department_names, i.notification_user_names]
      .filter(Boolean)
      .join(','),
    notificationType: i.notification_method,
  }));

/** 查询调度指令列表 */
export const getDispatchCommandList = async (
  params: PageParams,
  formData?: DispatchCommandListParams,
): Promise<GetDispatchCommandListResponse> => {
  const requestParams: GetDispatchCommandListRequestInBackend = {
    ...params,
    command_id: formData?.id,
    command_type: formData?.type,
    command_receive_organization: formData?.receiveOrganization?.toString(),
    send_time_start: formData?.sendStartTime,
    send_time_end: formData?.sendEndTime,
    receive_time_start: formData?.receiveStartTime,
    receive_time_end: formData?.receiveEndTime,
    command_state: formData?.state?.toString(),
    is_send_user: formData?.filterCurrentUser,
  };

  const res = (await postRequest({
    code: 'watergis/queryDispatchCommandList',
    params: formatDayjsInObject(requestParams, true),
  })) as HasTotalCountBackendResponse<
    GetDispatchCommandListResponseInBackend,
    GetDispatchCommandListStateCount
  >;

  if (res.json_ok) {
    const list = convertDispatchCommandList(res.values?.records ?? []);

    let countData: GetDispatchCommandListStateCount = {};
    const keys = Object.keys(
      res?.values?.count ?? {},
    ) as (keyof typeof DispatchCommandState)[];

    keys.forEach((type) => {
      if (type in DispatchCommandState) {
        countData = {
          ...countData,
          [type]: res?.values?.count?.[type] ?? 0,
        };
      }
    });

    return {
      total: Number(res?.values?.total_count) || 0,
      list,
      status: 'Success',
      count: countData,
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
    count: {},
  };
};

/** 查询当前用户的调度指令列表 */
export const getDispatchCommandListByCurrentUser = async (
  params: PageParams,
  formData?: DispatchCommandListParams,
): Promise<GetDispatchCommandListResponse> => {
  const requestParams: GetDispatchCommandListRequestInBackend = {
    ...params,
    command_id: formData?.id,
    command_type: formData?.type,
    command_receive_organization: formData?.receiveOrganization?.toString(),
    send_time_start: formData?.sendStartTime,
    send_time_end: formData?.sendEndTime,
    receive_time_start: formData?.receiveStartTime,
    receive_time_end: formData?.receiveEndTime,
    command_state: formData?.state?.toString(),
    is_send_user: formData?.filterCurrentUser,
  };

  const res = (await postRequest({
    code: 'watergis/queryDispatchCommandListByCurrentUser',
    params: formatDayjsInObject(requestParams, true),
  })) as HasTotalCountBackendResponse<
    GetDispatchCommandListResponseInBackend,
    GetDispatchCommandListStateCount
  >;

  if (res.json_ok) {
    const list = convertDispatchCommandList(res.values?.records ?? []);

    let countData: GetDispatchCommandListStateCount = {};
    const keys = Object.keys(
      res?.values?.count ?? {},
    ) as (keyof typeof DispatchCommandState)[];

    keys.forEach((type) => {
      if (type in DispatchCommandState) {
        countData = {
          ...countData,
          [type]: res?.values?.count?.[type] ?? 0,
        };
      }
    });

    return {
      total: Number(res?.values?.total_count) || 0,
      list,
      status: 'Success',
      count: countData,
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
    count: {},
  };
};

/** 更新调度指令接收信息 */
export const updateDispatchCommandReceiveInfo = withApiResponseHandler(
  async (formData: UpdateCommandReceiveInfo) => {
    const params: UpdateCommandReceiveInfoRequestInBackend = {
      command_id: formData?.id,
      command_state: formData?.state,
    };

    const res: any = await postRequest({
      code: 'watergis/updateCommandReceiveInfo',
      params,
    });

    return res;
  },
);

/** 更新调度指令回复信息 */
export const updateDispatchCommandReplyInfo = withApiResponseHandler(
  async (formData: UpdateCommandReplyInfo) => {
    const params: UpdateCommandReplyInfoRequestInBackend = {
      command_id: formData?.id,
      command_state: formData?.state,
      command_reply_content: formData?.replyContent,
    };

    const res: any = await postRequest({
      code: 'watergis/updateCommandReplyInfo',
      params,
    });

    return res;
  },
);

/** 更新调度指令及时性信息 */
export const updateCommandExecuteTimelinessInfo = withApiResponseHandler(
  async (formData: UpdateCommandTimelyExecuteInfo) => {
    const params: UpdateCommandTimelyExecuteInfoRequestInBackend = {
      command_id: formData?.id,
      command_timely_execute: formData?.timelyExecute,
      command_execute_time: formData?.executeTime,
      command_execute_note: formData?.executeNote,
    };

    const res: any = await postRequest({
      code: 'watergis/updateCommandExecuteTimelinessInfo',
      params,
    });

    return res;
  },
);
