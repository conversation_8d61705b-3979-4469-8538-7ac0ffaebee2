/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeObjectId } from '@waterdesk/data/object-item';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface DmaCustomer {
  key: string;
  otype: string;
  oname: string;
  firstDmaId: string;
  secondDmaId: string;
  thirdDmaId: string;
  address: string;
}

export interface GetDmaCustomerResponse extends APIResponse {
  total?: number;
  dmaCustomers?: DmaCustomer[];
}

export async function getDmaCustomer(
  pageSize: number,
  currentPage: number,
  otype: string,
  oname: string,
  time: string,
): Promise<GetDmaCustomerResponse> {
  const data: any = await postRequest({
    code: 'supply/queryWaterMeterList',
    params: {
      pageSize,
      current: currentPage,
      dma_type: otype,
      dma_name: oname,
      time,
    },
  });

  if (data.json_ok) {
    const dmaCustomers: DmaCustomer[] = [];
    data.values.records.forEach((item: any) => {
      dmaCustomers.push({
        key: makeObjectId(item.otype, item.oname),
        otype: item.otype,
        oname: item.oname,
        firstDmaId: item.FIRST_DMA_ID,
        secondDmaId: item.SECOND_DMA_ID,
        thirdDmaId: item.THREE_DMA_ID,
        address: item.ADDRESS,
      });
    });
    return {
      status: 'Success',
      total: data.values.count,
      dmaCustomers,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
