/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { DmaRelatedDevice } from '@waterdesk/data/property/dma-related-devices-category';
import {
  HIGHLIGHT_VALVE_MANAGE_CLOSED,
  HIGHLIGHT_VALVE_MANAGE_OPEN,
} from '@waterdesk/data/style-config';
import {
  getUnitValue,
  getUnitValueWithSymbol,
} from '@waterdesk/data/unit-system';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetDmaRelatedDevicesResponse extends APIResponse {
  inDeviceList?: DmaRelatedDevice[];
  outDeviceList?: DmaRelatedDevice[];
}

function getDmaDevices(
  deviceList: any,
  type: 'in' | 'out',
  db: Database,
): DmaRelatedDevice[] {
  const devices: DmaRelatedDevice[] = [];
  deviceList.forEach((item: any) => {
    const id = makeObjectId(item.otype, item.oname);
    const device = db.getDeviceById(id);
    const deviceUnitKey = db
      .getPropertyInfo(item.otype)
      ?.getPropertyUnit('SUM_FLOW_CF');

    const indicatorUnitKey = db
      .getPropertyInfo(item.sd_type)
      ?.getPropertyUnit('SDVAL');

    if (device) {
      devices.push({
        id,
        otype: item.otype,
        oname: item.oname,
        shape: device.shape ?? '',
        shapeType: device.shape ? getShapeType(device.shape) : '',
        indicatorType: item.sd_type,
        indicatorName: item.sd_name,
        deviceTitle: device.title,
        highlightIcon: device.icon,
        highlightType:
          type === 'in'
            ? HIGHLIGHT_VALVE_MANAGE_CLOSED
            : HIGHLIGHT_VALVE_MANAGE_OPEN,
        historySupplyFlowNumber: deviceUnitKey
          ? (getUnitValue(deviceUnitKey, Number(item.sum_flow_cf)) as number)
          : 0,
        historySupplyFlow: deviceUnitKey
          ? getUnitValueWithSymbol(deviceUnitKey, Number(item.sum_flow_cf))
          : '-',
        supplyFlowNumber: indicatorUnitKey
          ? (getUnitValue(indicatorUnitKey, Number(item.sdval)) as number)
          : 0,
        supplyFlow: indicatorUnitKey
          ? getUnitValueWithSymbol(indicatorUnitKey, Number(item.sdval))
          : '-',
      });
    }
  });
  return devices;
}

export async function getDmaRelatedDevices(
  otype: string,
  oname: string,
  time: string,
  db: Database,
): Promise<GetDmaRelatedDevicesResponse> {
  const data: any = await postRequest({
    code: 'supply/queryDmaIORefList',
    params: {
      otype,
      oname,
      time,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      inDeviceList: getDmaDevices(data.dma_in, 'in', db),
      outDeviceList: getDmaDevices(data.dma_out, 'out', db),
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getThirdDmaRelatedDevices(
  otype: string,
  oname: string,
  time: string,
  db: Database,
): Promise<GetDmaRelatedDevicesResponse> {
  const data: any = await postRequest({
    code: 'supply/queryDma3IORefList',
    params: {
      otype,
      oname,
      time,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      inDeviceList: getDmaDevices(data.dma_in, 'in', db),
      outDeviceList: getDmaDevices(data.dma_out, 'out', db),
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
