/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface GetPipeOrValveInfoResponse extends APIResponse {
  data?: {
    shape?: string;
    otype: string;
    oname: string;
  };
}

export const getPipeOrValveByOName = async (params: {
  otype?: string;
  oname: string;
  time?: string;
}): Promise<GetPipeOrValveInfoResponse> => {
  const { otype, oname, time } = params;
  const res: any = await postRequestByView({
    code: 'supply/findPipeOrValveByONAME',
    params: {
      otype,
      oname,
      time,
    },
  });

  if (res.json_ok && res.value) {
    const { shape, otype, oname } = res.value;
    return {
      status: 'Success',
      data: {
        shape,
        otype,
        oname,
      },
    };
  }

  return {
    status: 'Fail',
  };
};

export const getJunctionOrHydrantByOName = async (params: {
  otype?: string;
  oname: string;
  time?: string;
}): Promise<GetPipeOrValveInfoResponse> => {
  const { otype, oname, time } = params;
  const res: any = await postRequestByView({
    code: 'supply/findJunctionTypedNodeByONAME',
    params: {
      otype,
      oname,
      time,
    },
  });

  if (res.json_ok && res.value) {
    const { shape, otype, oname } = res.value;
    return {
      status: 'Success',
      data: {
        shape,
        otype,
        oname,
      },
    };
  }

  return {
    status: 'Fail',
  };
};
