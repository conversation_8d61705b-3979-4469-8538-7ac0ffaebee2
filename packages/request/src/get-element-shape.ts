/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface GetElementShapeResponse extends APIResponse {
  shape?: string;
}

export const getElementShape = async (
  otype: string,
  oname: string,
): Promise<GetElementShapeResponse> => {
  const res: any = await postRequestByView({
    code: 'watergis/getElementShape',
    params: {
      otype,
      oname,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      shape: res.shape,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
