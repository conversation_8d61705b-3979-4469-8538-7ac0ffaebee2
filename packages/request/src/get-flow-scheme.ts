/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

interface FlowSchemeData {
  key: string;
  title: string;
  otype: string;
  diagramThumbUri: string;
  diagramContent: string;
  userName: string;
  createTime: string;
  note: string;
}

interface CreateFlowSchemeFormData {
  id: string;
  diagramName: string;
  diagramType: string;
  diagramThumbUri: string;
  diagramContent: {
    config: string;
    diagramThumbUri: string;
  };
  note?: string;
}

export async function getFlowSchemeById(params: {
  diagramId?: string;
  diagramName?: string;
  diagramType?: string;
}): Promise<DefaultListAPIResponse<FlowSchemeData>> {
  const data: any = await postRequest({
    code: 'watergis/queryConfigurationDiagramList',
    params: {
      diagram_id: params.diagramId,
      diagram_name: params.diagramName,
      diagram_type: params.diagramType,
    },
  });

  if (data.json_ok) {
    const flowSchemeDatas: FlowSchemeData[] = [];
    data.values.records.forEach((item: any) => {
      flowSchemeDatas.push({
        key: item.diagram_id,
        otype: item.diagram_type,
        title: item.diagram_name,
        diagramContent: item.diagram_content,
        diagramThumbUri: item.image_path,
        userName: item.user_name,
        createTime: item.create_time,
        note: item.note,
      });
    });
    return {
      status: 'Success',
      total: data.values.count,
      list: flowSchemeDatas,
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: data.json_msg,
  };
}

export async function getFlowScheme(
  pageSize: Number,
  current: number,
  startTime?: string,
  endTime?: string,
  diagramType?: string,
  diagramName?: string,
): Promise<DefaultListAPIResponse<FlowSchemeData>> {
  const data: any = await postRequest({
    code: 'watergis/queryConfigurationDiagramList',
    params: {
      pageSize,
      current,
      start_time: startTime,
      end_time: endTime,
      diagram_type: diagramType,
      diagram_name: diagramName,
    },
  });

  if (data.json_ok) {
    const flowSchemeDatas: FlowSchemeData[] = [];
    data.values.records.forEach((item: any) => {
      flowSchemeDatas.push({
        key: item.diagram_id,
        otype: item.diagram_type,
        title: item.diagram_name,
        diagramContent: item.diagram_content,
        diagramThumbUri: item.image_path,
        userName: item.user_name,
        createTime: item.create_time,
        note: item.note,
      });
    });
    return {
      status: 'Success',
      total: data.values.count,
      list: flowSchemeDatas,
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: data.json_msg,
  };
}

export async function createFlowScheme(
  params: CreateFlowSchemeFormData,
): Promise<APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/updateConfigurationDiagram',
    params: {
      diagram_id: params.id,
      diagram_name: params.diagramName,
      diagram_type: params.diagramType ?? '工艺图',
      diagram_content: JSON.stringify(params.diagramContent),
      image_path: params.diagramThumbUri,
      note: params.note,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
}

export async function deleteFlowScheme(
  id: string | undefined,
): Promise<APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/deleteConfigurationDiagram',
    params: {
      diagram_id_list: id,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
}

export async function getSchemeTypeList(): Promise<
  DefaultListAPIResponse<string>
> {
  const res: any = await postRequest({
    code: 'watergis/queryDiagramTypeList',
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          list: res.values.list ?? [],
          total: res.values.list?.length ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total: 0,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        list: [],
        total: 0,
      });
    }
  });
}
