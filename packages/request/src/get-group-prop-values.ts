/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import Device, {
  getValueQueryParameters,
  PropertyValue,
} from '@waterdesk/data/device';
import GisObject from '@waterdesk/data/gis-object';
import ModelObject from '@waterdesk/data/model-object';
import {
  WarnConfig,
  WarnConfigToInterface,
} from '@waterdesk/data/otype-vprop-config-data';
import { ValueGroupParams } from '@waterdesk/data/time-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest, postRequestByView } from './request';

export interface GetGroupPropValuesResponse extends APIResponse {
  values?: Map<string, PropertyValue>;
}

export interface GetScadaWarnConfigResponse extends APIResponse {
  values: WarnConfig;
}

/**
 *
 * @param otype 对象的otype
 * @param oname 对象的oname
 * @param valueGroupParams 获取对象的属性对象:
 * {
 *    key: {
 *       otype?: string; 指定的otype， 如果不指定， 默认使用` @param otype `参数
 *       oname?: string; 指定的oname， 如果不指定， 默认使用` @param otype `参数
 *       vprop: string; 属性名称
 *       rmode?: "@CALCULATION" | "CALCULATION";
 *    }
 * }
 * mock scadaObject: { otype: "SD_OBJECT", oname: "sd_name1", vprop: "sd_vprop1" }
 * mock modelObject: { otype: "MD_OBJECT", oname: "md_name1", vprop: "md_vprop1" }
 *
 * 场景 1: 通过对象获取对象的属性值(不区分监测对象和模型对象)
 * 获取监测对象属性值：getGroupPropValues("SD_OBJECT", "sd_name1", { key: { vprop: 'sd_vprop1' } }, time)
 * 获取模型对象属性值：getGroupPropValues("MD_OBJECT", "md_name1", { key: { vprop: 'md_vprop1' } }, time)
 *
 * 场景 2: 通过对象获取对象的关联对象属性值
 * getGroupPropValues("SD_OBJECT", "sd_name1", { key: { otype: "SD_OBJECT", oname: "sd_name1", vprop: 'sd_vprop1', remode: "CALCULATION" } }, time)
 *
 * 场景 3: 通过对象的关联对象获取对象的属性值
 * getGroupPropValues("MD_OBJECT", "md_vprop1", { key: { otype: "MD_OBJECT", oname: "md_name1", vprop: 'md_vprop1', remode: "@CALCULATION" } }, time)
 *
 * @param time 时间
 * @param viewId
 * @returns
 */
export async function getGroupPropValues(
  otype: string,
  oname: string,
  valueGroupParams: ValueGroupParams,
  time: string,
  viewId?: string,
): Promise<GetGroupPropValuesResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/getGroupPropValues',
    params: {
      time,
      otype,
      oname,
      value_group: JSON.stringify(valueGroupParams),
      view_id: viewId,
    },
  });

  if (data.json_ok && data.values) {
    const propValues: Map<string, PropertyValue> = new Map();
    Object.keys(valueGroupParams).forEach((paramName: string) => {
      const paramValue = data.values[paramName];
      const key = paramName;
      const value: PropertyValue = paramValue
        ? {
            oname: paramValue.ONAME,
            otype: paramValue.OTYPE,
            vprop: paramValue.vprop,
            // 如果 initialValue 为 null, 则使用 initialValue
            value:
              paramValue.value === undefined ? undefined : paramValue.value,
            otime: paramValue.otime ?? undefined,
          }
        : {
            oname: valueGroupParams[key].oname ?? oname ?? undefined,
            otype: valueGroupParams[key].otype ?? otype ?? undefined,
            vprop: valueGroupParams[key].vprop ?? undefined,
            value: undefined,
            otime: undefined,
          };
      propValues.set(key, value);
    });

    return {
      status: 'Success',
      values: propValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getAllGroupPropValues(
  object: Device | ModelObject,
  time: string,
  db: Database,
  viewId?: string,
): Promise<GetGroupPropValuesResponse> {
  const valueGroupParams = getValueQueryParameters(db, object);
  return getGroupPropValues(
    object.otype,
    object.oname,
    valueGroupParams,
    time,
    viewId,
  );
}

export async function getAllMapViewsGroupPropValues(
  object: Device | ModelObject,
  db: Database,
  // todo remove mapViews
  mapViews: any,
  time: string,
): Promise<Map<string, PropertyValue[]>> {
  const valueGroupParams = getValueQueryParameters(db, object);
  const generateObjectPropertiesRequests: Promise<GetGroupPropValuesResponse>[] =
    [];
  mapViews?.forEach((mapView: any) => {
    const currentItem = mapView?.selectionCollection.firstSelectedObject;
    if (mapView && currentItem) {
      let { oname } = currentItem;
      if (currentItem instanceof GisObject) {
        oname = object.oname;
      }
      generateObjectPropertiesRequests.push(
        getGroupPropValues(
          object.otype,
          oname,
          valueGroupParams,
          mapView.dateTime?.format('YYYY-MM-DD HH:mm:ss') ?? time,
          mapView.getViewId(),
        ),
      );
    }
  });
  return Promise.all(generateObjectPropertiesRequests).then((res) => {
    const propertyValues: Map<string, PropertyValue[]> = new Map();
    res.forEach((result) => {
      if (result.status === 'Success') {
        result.values?.forEach((propValue, key) => {
          const property = propertyValues.get(key);
          if (property) {
            property.push(propValue);
          } else {
            propertyValues.set(key, [propValue]);
          }
        });
      }
    });
    return propertyValues;
  });
}

export interface GetLinkDiameterResponse extends APIResponse {
  diameter?: number;
}

export async function getLinkDiameter(
  otype: string,
  oname: string,
): Promise<GetLinkDiameterResponse> {
  const valueGroupParams = { DIAMETER: { vprop: 'DIAMETER' } };
  const time = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const result = await getGroupPropValues(otype, oname, valueGroupParams, time);
  if (result.status === 'Success' && result.values) {
    const value = result.values.get('DIAMETER')?.value;
    if (value !== undefined) {
      const diameter = Number(value);
      return {
        status: 'Success',
        diameter,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: result.errorMessage,
  };
}

export async function savePropertyRequest(
  time: string,
  otype: string,
  oname: string,
  vprop: string,
  value: string | number | undefined | null,
): Promise<APIResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/modifyViewVPROP',
    params: {
      time,
      otype,
      oname,
      vprop,
      value,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getScadaWarnConfigList(
  otype: string,
  oname: string,
): Promise<GetScadaWarnConfigResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryScadaWarnConfigList',
    params: {
      otype,
      oname,
    },
  });

  if (data.json_ok) {
    const obj = data.values?.[0];

    if (obj) {
      Object.keys(obj ?? {}).forEach((key) => {
        if (typeof obj[key] !== 'object' || obj[key] === null) {
          delete obj[key];
        }
      });

      return {
        status: 'Success',
        values: obj,
      };
    }

    return {
      status: 'Success',
      values: {},
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    values: {},
  };
}

export async function updateScadaWarnConfigList(
  otype: string,
  oname: string,
  warnConfigList: WarnConfigToInterface,
) {
  const data: any = await postRequest({
    code: 'watergis/updateScadaWarnConfig',
    params: {
      otype,
      oname,
      warn_config_list: JSON.stringify(warnConfigList),
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
