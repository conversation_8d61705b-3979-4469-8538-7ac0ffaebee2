/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import { SCADA_SUMMARY_OBJECT } from '@waterdesk/data/const/system-const';
import GisObject from '@waterdesk/data/gis-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import { GroupTimeData, TimeData } from '@waterdesk/data/time-data';
import toObject, { formatNumber } from '@waterdesk/data/utils';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface GetGroupTimeValuesResponse extends APIResponse {
  values?: Map<string, GroupTimeData>;
}
export interface GetSimulationScoreValuesResponse extends APIResponse {
  values?: Map<string, number>;
}

function isMonth(dateString: string): boolean {
  return dateString.length < 8;
}

function setParamsByDates(
  multiDate: string[],
  params: Map<string, Map<string, string | number>>,
): Map<string, Map<string, string | number>> {
  const newParams: Map<string, Map<string, string | number>> = new Map();
  multiDate.forEach((date) => {
    let startDate = dayjs(date).format('YYYY-MM-DD 00:00:00');
    let endDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD 00:00:00');
    if (isMonth(date)) {
      startDate = dayjs(date).format('YYYY-MM-01 00:00:00');
      endDate = dayjs(date).add(1, 'month').format('YYYY-MM-01 00:00:00');
    }
    [...params.entries()].forEach((paramsMap) => {
      const valueMap = new Map(paramsMap[1]);
      valueMap.set('start_time', startDate);
      valueMap.set('end_time', endDate);
      newParams.set(`${paramsMap[0]}@${date}`, valueMap);
    });
  });
  return newParams;
}

function getValueGroupParameters(
  _object: IObjectItem,
  indicatorOType: string | undefined,
  indicatorOName: string | undefined,
  vprop: string,
  includeMinMax?: boolean,
  includeCorrelatedProps?: string[],
  timeStep?: number,
  multiDate?: string[],
  minMaxField?: { minField: string; maxField: string },
  forecast?: boolean,
  showModel = true,
): Record<string | number | symbol, unknown> {
  const params: Map<string, Map<string, string | number>> = new Map();
  const vpropItem: Map<string, string | number> = new Map();
  if (typeof timeStep === 'number' && timeStep > 60)
    vpropItem.set('step_second', timeStep);
  vpropItem.set('vprop', vprop);
  params.set(vprop, vpropItem);
  if (indicatorOType !== undefined && indicatorOName !== undefined) {
    const modelItem: Map<string, string | number> = new Map();
    modelItem.set('otype', indicatorOType);
    modelItem.set('oname', indicatorOName);

    if (Array.isArray(vprop)) {
      vprop.forEach((v) => {
        const newModelItem = new Map(modelItem);
        newModelItem.set('vprop', v);
        params.set(v, newModelItem);
      });
    } else {
      modelItem.set('vprop', vprop);
    }
    if (typeof timeStep === 'number' && timeStep > 60)
      modelItem.set('step_second', timeStep);
    if (forecast) {
      const newModelItem = new Map(modelItem);
      newModelItem.set('fc_ext', 'true');
      params.set('forecast', newModelItem);
    }
    // 模拟值
    const newModelItem = new Map(modelItem);
    newModelItem.set('rmode', '@CALCULATION');
    if (showModel) params.set('model', newModelItem);
  }

  if (includeMinMax) {
    const minField = minMaxField ? minMaxField.minField : 'ENVELOP_MIN';
    const maxField = minMaxField ? minMaxField.maxField : 'ENVELOP_MAX';
    const maxItem: Map<string, string | number> = new Map();
    const minItem: Map<string, string | number> = new Map();
    maxItem.set('vprop', maxField);
    minItem.set('vprop', minField);
    if (typeof timeStep === 'number' && timeStep > 60) {
      maxItem.set('step_second', timeStep);
      minItem.set('step_second', timeStep);
    }
    params.set('ENVELOP_MAX', maxItem);
    params.set('ENVELOP_MIN', minItem);
  }

  if (includeCorrelatedProps) {
    includeCorrelatedProps?.forEach((item) => {
      const prop: Map<string, string | number> = new Map();
      prop.set('vprop', item);
      if (typeof timeStep === 'number' && timeStep > 60)
        prop.set('step_second', timeStep);
      params.set(item, prop);
    });
  }

  if (multiDate) {
    return toObject(setParamsByDates(multiDate, params));
  }

  return toObject(params);
}

function getTimeDataValues(values: any[]): Map<string, GroupTimeData> {
  const timeDataValues: Map<string, GroupTimeData> = new Map();
  Object.entries(values).forEach((item: any) => {
    if (Array.isArray(item[1].values)) {
      const key = item[0].split('@')[0];
      const timeData: Array<TimeData> = [];
      item[1].values.forEach((data: any) => {
        if (typeof data.time === 'string') {
          if (typeof data.value === 'number')
            timeData.push({ time: data.time, value: data.value });
          else if (typeof data.value === 'string')
            timeData.push({ time: data.time, value: Number(data.value) });
        }
      });

      const timeDataValue = timeDataValues.get(key);
      if (timeDataValue) {
        timeDataValue.timeData.push(...timeData);
      } else {
        timeDataValues.set(key, {
          otype: item[1].OTYPE,
          oname: item[1].ONAME,
          vprop: item[1].VPROP,
          timeData,
        });
      }
    }
  });

  return timeDataValues;
}

export async function getGroupTimeValues(
  minMaxField: { minField: string; maxField: string } | undefined,
  object: IObjectItem,
  indicatorOType: string | undefined,
  indicatorOName: string | undefined,
  vprop: string,
  startDate: string,
  endDate: string,
  /**
   * @duplicated
   * 是否获取包络线, 和minMaxField由重复 mark一下
   */
  includeMinMax?: boolean,
  // 关联属性
  includeCorrelatedProps?: string[],
  /**
   * 数据步长
   */
  timeStep?: number,
  /**
   * @duplicated
   * 准备废弃, mark一下
   */
  multiDate?: string[],
  viewId?: string,
  /**
   * 按日取数还是按时刻取数
   */
  timeType?: 'day' | 'time',
  /**
   * 是否获取预测值
   */
  forecast?: boolean,
  showModel = true,
): Promise<GetGroupTimeValuesResponse> {
  let { oname, otype } = object;
  if (object instanceof GisObject && object.refModelObject) {
    oname = object.refModelObject.oname;
    otype = object.refModelObject.otype;
  } else if (indicatorOType !== undefined && indicatorOName !== undefined) {
    oname = indicatorOName;
    otype = indicatorOType;
  }

  const valueGroupParams = getValueGroupParameters(
    object,
    indicatorOType ?? otype,
    indicatorOName ?? oname,
    vprop,
    includeMinMax,
    includeCorrelatedProps,
    timeStep,
    multiDate,
    minMaxField,
    forecast,
    showModel,
  );

  const startTime =
    timeType === 'time'
      ? dayjs(startDate).format('YYYY-MM-DD HH:mm:ss')
      : dayjs(startDate).format('YYYY-MM-DD 00:00:00');

  const endTime =
    timeType === 'time'
      ? dayjs(endDate).format('YYYY-MM-DD HH:mm:ss')
      : dayjs(endDate).format('YYYY-MM-DD 23:59:59');

  const data: any = await postRequestByView({
    code: 'watergis/getGroupTimeValues',
    params: {
      start_time: startTime,
      end_time: endTime,
      otype,
      oname,
      value_group: JSON.stringify(valueGroupParams),
      full_value: true,
      zero_second: true,
      view_id: viewId,
    },
  });

  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

/**
 * @deprecated
 * @param minMaxField
 * @param object
 * @param indicatorOType
 * @param indicatorOName
 * @param vprop
 * @param startDate
 * @param endDate
 * @param includeMinMax
 * @param includeCorrelatedProps
 * @param timeStep
 * @param multiDate
 * @param timeType
 * @param getForecast
 * @returns
 */
export async function getAllGroupTimeValues(
  minMaxField: { minField: string; maxField: string } | undefined,
  object: IObjectItem,
  indicatorOType: string | undefined,
  indicatorOName: string | undefined,
  vprop: string | string[],
  startDate: string,
  endDate: string,
  // todo remove mapViews
  mapViews: any,
  includeMinMax?: boolean,
  includeCorrelatedProps?: string[],
  timeStep?: number,
  multiDate?: string[],
  timeType?: 'day' | 'time',
  getForecast?: boolean,
): Promise<GetGroupTimeValuesResponse[]> {
  const getAllGroupTimeValuesRequest: Promise<GetGroupTimeValuesResponse>[] =
    [];
  if (mapViews?.length) {
    mapViews?.forEach((mapView: any) => {
      if (mapView) {
        const startTime =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE && mapView.date
            ? mapView.date.format('YYYY-MM-DD')
            : startDate;
        const endTime =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE && mapView.date
            ? mapView.date.format('YYYY-MM-DD')
            : endDate;

        const selectObject =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE
            ? mapView.selectionCollection.firstSelectedObject
            : object;
        if (selectObject) {
          getAllGroupTimeValuesRequest.push(
            getGroupTimeValues(
              minMaxField,
              selectObject,
              indicatorOType,
              indicatorOName,
              Array.isArray(vprop) ? vprop[0] : vprop,
              startTime,
              endTime,
              includeMinMax,
              includeCorrelatedProps,
              timeStep,
              multiDate,
              mapView.getViewId(),
              timeType,
              getForecast,
            ),
          );
        }
      }
    });
  } else if (object) {
    getAllGroupTimeValuesRequest.push(
      getGroupTimeValues(
        minMaxField,
        object,
        indicatorOType,
        indicatorOName,
        Array.isArray(vprop) ? vprop[0] : vprop,
        startDate,
        endDate,
        includeMinMax,
        includeCorrelatedProps,
        timeStep,
        multiDate,
        undefined,
        timeType,
        getForecast,
      ),
    );
  }

  return Promise.all(getAllGroupTimeValuesRequest);
}

export async function getAssignObjectTimeValues(
  otype: string,
  oname: string,
  vprop: string,
  startDate: string,
  endDate: string,
): Promise<GetGroupTimeValuesResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/getGroupTimeValues',
    params: {
      start_time: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
      otype,
      oname,
      full_value: true,
      zero_second: true,
      value_group: JSON.stringify({
        [vprop]: {
          vprop,
        },
      }),
    },
  });
  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getAssignObjectTimeValuesByTime(
  otype: string,
  oname: string,
  vprop: string,
  startTime: string,
  endTime: string,
): Promise<GetGroupTimeValuesResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/getGroupTimeValues',
    params: {
      start_time: startTime,
      end_time: endTime,
      otype,
      oname,
      full_value: true,
      zero_second: true,
      value_group: JSON.stringify({
        [vprop]: {
          vprop,
        },
      }),
    },
  });
  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getSimulationScoreByTimeRange(
  startDate: string,
  endDate: string,
  solutionSimulationConfig?: {
    limit?: number;
    color?: string;
    otype?: string;
    oname?: string;
    vprop?: string;
  },
): Promise<GetSimulationScoreValuesResponse> {
  const otype = solutionSimulationConfig?.otype ?? 'SPY_SUMMARY';
  const oname = solutionSimulationConfig?.oname ?? 'SUMMARY';
  const vprop = solutionSimulationConfig?.vprop ?? 'MODEL_GB_SCORE';
  const data: any = await postRequestByView({
    code: 'watergis/getGroupTimeValues',
    params: {
      start_time: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
      otype,
      oname,
      full_value: true,
      zero_second: true,
      value_group: JSON.stringify({
        [vprop]: {
          vprop,
        },
      }),
    },
  });
  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);
    const scoreMap: Map<string, number> = new Map();
    timeDataValues.get('MODEL_GB_SCORE')?.timeData.forEach((item) => {
      const date = dayjs(item.time).format('YYYY-MM-DD');
      scoreMap.set(date, formatNumber(item.value, 2));
    });

    return {
      status: 'Success',
      values: scoreMap,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getGroupObjectsTimeValues(
  startDate: string,
  endDate: string,
  otype: string,
  oname: string,
  valueGroupParams?: {
    [key: string]: {
      otype?: string;
      oname?: string;
      vprop: string;
    };
  },
  viewId?: string,
  needOriginValue?: boolean,
): Promise<GetGroupTimeValuesResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/getGroupTimeValues',
    params: {
      start_time: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
      otype,
      oname,
      value_group: JSON.stringify(valueGroupParams),
      full_value: true,
      zero_second: true,
      view_id: viewId,
    },
  });

  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: needOriginValue ? data.values : timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getAllGroupObjectsTimeValues(
  startDate: string,
  endDate: string,
  // todo remove mapViews
  mapViews: any,
  valueGroupParams?: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  },
): Promise<GetGroupTimeValuesResponse[]> {
  const getAllGroupTimeValuesRequest: Promise<GetGroupTimeValuesResponse>[] =
    [];
  if (mapViews?.length) {
    mapViews.forEach((mapView: any) => {
      if (mapView) {
        const startTime =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE && mapView.date
            ? mapView.date.format('YYYY-MM-DD')
            : startDate;
        const endTime =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE && mapView.date
            ? mapView.date.format('YYYY-MM-DD')
            : endDate;
        getAllGroupTimeValuesRequest.push(
          getGroupObjectsTimeValues(
            startTime,
            endTime,
            '',
            '',
            valueGroupParams,
            mapView.getViewId(),
          ),
        );
      }
    });
  } else {
    getAllGroupTimeValuesRequest.push(
      getGroupObjectsTimeValues(startDate, endDate, '', '', valueGroupParams),
    );
  }
  return Promise.all(getAllGroupTimeValuesRequest);
}

export async function getForecastRealtimeFlow(
  date: string,
): Promise<TimeData[]> {
  const res = await getAssignObjectTimeValues(
    SCADA_SUMMARY_OBJECT.otype,
    SCADA_SUMMARY_OBJECT.oname,
    'TM_FLOW_FC1',
    dayjs(date).format('YYYY-MM-DD'),
    dayjs(date).format('YYYY-MM-DD'),
  );

  let values: Map<string, GroupTimeData> = new Map();
  if (res.status === 'Success' && res.values) {
    values = res.values;
  }
  return values.get('TM_FLOW_FC1')?.timeData || [];
}

export async function getForecastDayFlow(
  date: string,
): Promise<number | undefined> {
  const res = await getAssignObjectTimeValues(
    SCADA_SUMMARY_OBJECT.otype,
    SCADA_SUMMARY_OBJECT.oname,
    'DAY_FLOW_FC1',
    dayjs(date).format('YYYY-MM-DD'),
    dayjs(date).format('YYYY-MM-DD'),
  );

  let values: Map<string, GroupTimeData> = new Map();
  if (res.status === 'Success' && res.values) {
    values = res.values;
  }
  const timeData: TimeData[] = values.get('DAY_FLOW_FC1')?.timeData || [];
  if (timeData.length > 0) return timeData[0].value;
  return undefined;
}
