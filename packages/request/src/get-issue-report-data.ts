/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  IssueReportList,
  IssueReportListParams,
  IssueReportProcessState,
  QuestionType,
  UpdateIssueReportParams,
} from '@waterdesk/data/issue-report';
import { formatDayjsInObject } from '@waterdesk/data/string';
import { PageParams, SortMethod, sorterMethodMap } from './api/api-request';
import {
  APIResponse,
  DefaultListBackendResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest } from './request';

interface GetIssueReportListRequestInBackend extends PageParams {
  start_time?: string; // 开始时间
  end_time?: string; // 结束时间
  user_name?: string; // 创建人(模糊查询)
  is_process?: IssueReportProcessState; // 是否处理(-1.待处理 1.已处理)
  sort_by?: string;
  sort_method?: SortMethod;
}

interface GetIssueReportListResponseInBackend {
  otype: string; // 问题上报类型
  oname: string; // 问题上报编号
  title: string; // 问题上报名称
  shape: string; // shape
  question_type: QuestionType; // 问题类型
  content: string; // 事件内容
  create_user: string; // 创建人
  create_time: string; // 创建时间
  question_state: IssueReportProcessState; // 事件状态(-1:待处理, 1:已处理)
  process_content: string; // 处理描述
  update_user: string; // 更新人
  update_time: string; // 更新时间
}

interface UpdateQuestionReportRequestInBackend {
  otype?: string; // 问题上报类型
  oname?: string; // 问题上报编号
  title?: string; // 问题上报名称
  shape?: string; // shape
  question_type?: QuestionType; // 问题类型
  content?: string; // 事件内容
  question_state?: IssueReportProcessState; // 事件状态(-1:待处理, 1:已处理)
  process_content?: string; // 处理描述
}

interface GetIssueReportListResponse extends APIResponse {
  total: number;
  list: IssueReportList[];
}

const sorterServerFieldMap: Record<string, string> = {
  createTime: 'create_time',
  updateTime: 'update_time',
};

/** 查询问题上报列表 */
export const getIssueReportList = async (
  params: PageParams,
  formData?: IssueReportListParams,
): Promise<GetIssueReportListResponse> => {
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const requestParams: GetIssueReportListRequestInBackend = {
    current: params.current,
    pageSize: params.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    user_name: formData?.userName,
    is_process: formData?.isProcess,
    sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'watergis/queryQuestionReportList',
    params: formatDayjsParams,
  })) as DefaultListBackendResponse<GetIssueReportListResponseInBackend>;

  if (res.json_ok) {
    const list =
      res.values?.records?.map((i) => ({
        otype: i.otype,
        oname: i.oname,
        title: i.title,
        shape: i.shape,
        questionType: i.question_type,
        content: i.content,
        createUser: i.create_user,
        createTime: i.create_time,
        questionState: i.question_state,
        processContent: i.process_content,
        updateUser: i.update_user,
        updateTime: i.update_time,
      })) ?? [];

    return {
      total: Number(res?.values?.count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

/** 更新问题上报 */
export const updateIssueReport = withApiResponseHandler(
  async (formData: UpdateIssueReportParams) => {
    const newParams: UpdateQuestionReportRequestInBackend = {
      otype: formData.otype,
      oname: formData.oname,
      title: formData.title,
      shape: formData.shape,
      question_type: formData.questionType,
      content: formData.content,
      question_state: formData.questionState,
      process_content: formData.processContent,
    };

    const res: any = await postRequest({
      code: 'watergis/updateQuestionReport',
      params: {
        param_json: JSON.stringify(newParams),
      },
    });

    return res;
  },
);
