/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HighlightObject } from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface MainPipesResponse extends APIResponse {
  list?: HighlightObject[];
}

function formatMainPipeData(
  data: {
    DIAMETER: number;
    FLOW: number;
    VELOCITY: number;
    ROAD_NAME: string;
    oname: string;
    otype: string;
    shape: string;
  }[],
): HighlightObject[] {
  return (data ?? []).map((item) => ({
    id: makeObjectId(item.otype, item.oname),
    ...item,
    oname: item.oname,
    otype: item.otype,
    shape: item.shape,
    shapeType: getShapeType(item.shape),
  }));
}
export async function getMainPipes(
  extent: number[],
  time: string | undefined,
  diameter?: number,
  otypes?: string,
): Promise<MainPipesResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/getElemAndModelValueByBoundBox',
    params: {
      first_posx: extent[0],
      first_posy: extent[1],
      second_posx: extent[2],
      second_posy: extent[3],
      diameter,
      time,
      otypes,
    },
  });

  if (data.json_ok) {
    const result = formatMainPipeData(data.values);
    return {
      status: 'Success',
      list: result,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
