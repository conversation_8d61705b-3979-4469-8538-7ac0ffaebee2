/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ModelServiceItem } from '@waterdesk/data/system-usage-statistics';
import dayjs, { Dayjs } from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

interface GetModelServiceRequest {
  startDate: string | Dayjs;
  endDate: string | Dayjs;
  fetchTypeList?: string[];
}

interface GetModelServiceResponse extends APIResponse {
  values: ModelServiceItem[];
}

// 查询功能使用次数
export const getFunctionUsageCount = async (
  params: GetModelServiceRequest,
): Promise<GetModelServiceResponse> => {
  const paramsToSend = {
    start_time: dayjs(params.startDate).format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(params.endDate).format('YYYY-MM-DD 23:59:59'),
    operation_type_list: params.fetchTypeList?.toString(),
  };

  const res: any = await postRequest({
    code: 'portal/queryFunctionUsedCount',
    params: paramsToSend,
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      values: Object.entries(res.values).map(([name, value]) => ({
        name,
        value: Number(value),
      })),
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
    values: [],
  };
};

// 查询人员使用次数
export const getUserUsageCount = async (
  params: GetModelServiceRequest,
): Promise<GetModelServiceResponse> => {
  const paramsToSend = {
    start_time: dayjs(params.startDate).format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(params.endDate).format('YYYY-MM-DD 23:59:59'),
    operation_type_list: params.fetchTypeList?.toString(),
  };

  const res: any = await postRequest({
    code: 'portal/queryUserOperationCount',
    params: paramsToSend,
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      values: Object.entries(res.values).map(([name, value]) => ({
        name,
        value: Number(value),
      })),
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
    values: [],
  };
};
