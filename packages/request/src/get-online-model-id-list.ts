/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ModelInfo } from '@waterdesk/data/model-info';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetOnlineModelListResponse extends APIResponse {
  modelList?: ModelInfo[];
}

export async function getOnlineModelIdList(
  date: string,
): Promise<GetOnlineModelListResponse> {
  const res: any = await postRequest({
    code: 'watergis/getOnlineModelIdList',
    params: {
      time: `${date} 00:00:00`,
    },
  });
  if (res.json_ok && res.values && Array.isArray(res.values)) {
    const idList: ModelInfo[] = [];
    res.values.forEach((data: any) => {
      if (
        typeof data.model_id === 'string' &&
        typeof data.model_title === 'string'
      ) {
        idList.push({ id: data.model_id, title: data.model_title });
      }
    });

    return {
      status: 'Success',
      modelList: idList,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}
