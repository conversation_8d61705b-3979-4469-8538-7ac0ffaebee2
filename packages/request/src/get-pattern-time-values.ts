/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TimeData } from '@waterdesk/data/time-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetPatternTimeValuesResponse extends APIResponse {
  values?: Map<string, TimeData[]>;
}

function getTimeDataValues(values: any): Map<string, TimeData[]> {
  const timeDataValues: Map<string, TimeData[]> = new Map();
  Object.entries(values).forEach((entry: any) => {
    const [key, value] = entry;
    const timeData: Array<TimeData> = [];
    if (Array.isArray(value)) {
      value.forEach((data: any) => {
        if (typeof data.time === 'string') {
          if (typeof data.value === 'number')
            timeData.push({ time: data.time, value: data.value });
          else if (typeof data.value === 'string')
            timeData.push({ time: data.time, value: Number(data.value) });
        }
      });
    }
    timeDataValues.set(key, timeData);
  });

  return timeDataValues;
}

export async function getPatternTimeValues(
  date: string,
  patterns: string[],
  endDate?: string,
): Promise<GetPatternTimeValuesResponse> {
  const data: any = await postRequest({
    code: 'supply/getPatternValuesByTimeRange',
    params: {
      start_time: dayjs(date).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endDate ?? date).format('YYYY-MM-DD 23:59:00'),
      pattern_list: patterns.toString(),
    },
  });

  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
