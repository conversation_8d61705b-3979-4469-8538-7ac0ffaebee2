/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeletePlanProjectParams,
  PlanProjectList,
  PlanProjectListParams,
  ProjectState,
  ProjectType,
  UpdatePlanProjectParams,
} from '@waterdesk/data/plan-project';
import { formatDayjsInObject } from '@waterdesk/data/string';
import { PageParams, SortMethod } from './api/api-request';
import {
  APIResponse,
  DefaultListBackendResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest } from './request';

interface QueryPlanProjectListRequestInBackend extends PageParams {
  start_time?: string;
  end_time?: string;
  project_id?: string;
  project_start_time?: string;
  project_end_time?: string;
  creator?: string;
  department?: string;
  project_name?: string;
  project_content?: string;
  project_type?: string;
  project_state?: string;
  sort_by?: string;
  sort_method?: SortMethod;
}

interface QueryPlanProjectListResponseInBackend {
  // 工程id
  project_id: string;
  // 工程名称
  project_name: string;
  // 工程类型
  project_type: ProjectType;
  // 部门名称
  department: string;
  // 工程状态
  project_state: ProjectState;
  // 备注
  note: string;
  // 创建人
  creator: string;
  // 工程开始时间
  project_start_time: string;
  // 工程结束时间
  project_end_time: string;
  // 创建时间
  create_time: string;
  // 关联事件编号
  event_name: string;
  // 关联事件名称
  event_title: string;
  editable: number;
  file_list: string;
  json_detail: string;
}

interface GetPlanProjectListResponse extends APIResponse {
  total: number;
  list: PlanProjectList[];
}

interface UpdatePlanProjectRequestInBackend {
  // 工程id
  project_id?: string;
  // 工程名称
  project_name: string;
  // 工程类型
  project_type: string;
  // 工程部门id
  department_id?: string;
  // 工程部门名称
  department_name: string;
  // 工程状态
  project_state: string;

  user_name?: string;

  source?: string;
  // 关联事件编号
  event_id?: string;
  note?: string;
  json_detail?: string;
}

interface DeletePlanProjectRequestInBackend {
  project_id_list?: string;
}

export interface PlanFile {
  fileName: string;
  fileParams: string;
}

export const convertPlanProjectList = (
  data: QueryPlanProjectListResponseInBackend[],
) =>
  data.map((i) => {
    const jsonDetail = i.json_detail ? JSON.parse(i.json_detail) : {};
    const fileList = i.file_list ? JSON.parse(i.file_list) : [];
    return {
      systemProjectId: i?.project_id,
      projectName: i?.project_name,
      projectType: i?.project_type,
      departmentName: i?.department,
      projectState: i?.project_state,
      userName: i?.creator,
      startTime: i?.project_start_time,
      endTime: i?.project_end_time,
      createTime: i?.create_time,
      eventName: i?.event_name,
      eventTitle: i?.event_title,
      editable: !!i?.editable,
      fileList: fileList.map(
        (item: { file_name: string; file_param: string }) => ({
          fileName: item.file_name,
          fileParams: item.file_param,
        }),
      ),
      ...jsonDetail,
    };
  }) ?? [];

const sorterServerFieldMap: Record<string, string> = {
  startTime: 'project_start_time',
  endTime: 'project_end_time',
  createTime: 'create_time',
};

/** 获取计划工程列表 */
export const getPlanProjectList = async (
  params: PageParams,
  formData?: PlanProjectListParams,
  sort?: Record<string, SortMethod>,
): Promise<GetPlanProjectListResponse> => {
  const sortKey = Object.keys(sort ?? {})[0];
  const sortMethod = Object.values(sort ?? {})[0];
  const isSort = sortKey && sortMethod;

  const requestParams: QueryPlanProjectListRequestInBackend = {
    current: params.current,
    pageSize: params.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    project_id: formData?.systemProjectId,
    project_start_time: formData?.projectStartTime,
    project_end_time: formData?.projectEndTime,
    project_name: formData?.projectName,
    project_content: formData?.projectContent,
    project_type: formData?.projectType,
    project_state: formData?.projectState,
    creator: formData?.userName,
    department: formData?.departmentName,
    sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
    sort_method: isSort ? (sortMethod as SortMethod) : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'watergis/queryPlanProjectList',
    params: formatDayjsParams,
  })) as DefaultListBackendResponse<QueryPlanProjectListResponseInBackend>;

  if (res?.json_ok) {
    const list = convertPlanProjectList(res?.values?.records ?? []);

    return {
      total: Number(res?.values?.count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

/** 创建/更新计划工程 */
export const updatePlanProject = withApiResponseHandler(
  async (formData: UpdatePlanProjectParams & { appName: string }) => {
    const params: UpdatePlanProjectRequestInBackend = {
      project_id: formData.systemProjectId,
      project_name: formData.projectName ?? '',
      project_type: formData.projectType ?? '',
      department_name: formData.departmentName ?? '',
      project_state: formData.projectState ?? 'PLANNING',
      user_name: formData.userName ?? '',
      source: formData.appName,
      event_id: formData.eventId,
      note: formData.note ?? '',
      json_detail: formData.jsonDetail
        ? JSON.stringify(formData.jsonDetail)
        : '',
    };

    const formatDayjsParams = formatDayjsInObject(params);

    const res: any = await postRequest({
      code: 'watergis/updatePlanProject',
      params: formatDayjsParams,
    });

    return res;
  },
);

/** 删除计划工程 */
export const deletePlanProject = withApiResponseHandler(
  async (formData: DeletePlanProjectParams) => {
    const params: DeletePlanProjectRequestInBackend = {
      project_id_list: formData.projectIdList,
    };
    const res: any = await postRequest({
      code: 'watergis/deletePlanProject',
      params,
    });

    return res;
  },
);
