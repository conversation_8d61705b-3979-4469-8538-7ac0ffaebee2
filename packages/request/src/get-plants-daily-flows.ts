/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface GetPlantsDailyFlowsResponse extends APIResponse {
  values?: Map<string, number>;
}

function getTimeDataValues(values: any): Map<string, number> {
  const timeDataValues: Map<string, number> = new Map();
  Object.entries(values).forEach((entry: any) => {
    const [key, value] = entry;
    if (typeof value.value === 'number') timeDataValues.set(key, value.value);
    else if (typeof value.value === 'string')
      timeDataValues.set(key, Number(value.value));
  });

  return timeDataValues;
}

function getValueGroupParameters(plantIds: string[]): {} {
  const params: any = {};
  plantIds.forEach((plant) => {
    params[plant] = {
      otype: 'SDFOLD_FACT',
      oname: plant,
      vprop: 'CUMULATIVE_FLOW',
    };
  });

  return params;
}

export async function getPlantsDailyFlows(
  date: string,
  plantIds: string[],
): Promise<GetPlantsDailyFlowsResponse> {
  const valueGroupParams = getValueGroupParameters(plantIds);
  const data: any = await postRequestByView({
    code: 'watergis/getGroupPropValues',
    params: {
      // getGroupPropValues returns the value of the day before the given day
      time: dayjs(date).add(1, 'day').format('YYYY-MM-DD 00:00:00'),
      value_group: JSON.stringify(valueGroupParams),
    },
  });

  if (data.json_ok && data.values) {
    const timeDataValues = getTimeDataValues(data.values);

    return {
      status: 'Success',
      values: timeDataValues,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
