/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PollutionSpreadAnalysisItem } from '@waterdesk/data/solution';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface getPollutionSpreadAnalysisListParams {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  pageSize: number;
  /** 方案ID */
  solutionId?: string;
  /** 方案GUID */
  solutionGuid?: string;
  /** 分析标题 */
  analysisTitle?: string;
  /** 是否只查询当前用户 */
  onlyCurrentUser?: boolean;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向 */
  sortOrder?: 'ascend' | 'descend';
}

export const convertPollutionSpreadAnalysisList = (
  list: any[],
): PollutionSpreadAnalysisItem[] =>
  (list ?? []).map((item) => ({
    solutionId: item.solution_id,
    solutionGuid: item.solution_guid,
    analysisTitle: item.analysis_title,
    analysisNote: item.analysis_note,
    pollutionStartTime: item.pollution_start_time,
    pollutionEndTime: item.pollution_end_time,
    simulationEndTime: item.simulation_end_time,
    calcId: item.calc_id,
    calcTime: item.calc_time,
    createTime: item.create_time,
    updateTime: item.update_time,
    createUser: item.create_user,
    solutionName: item.solution_name,
  }));

export async function getPollutionSpreadAnalysisList(
  params: getPollutionSpreadAnalysisListParams,
): Promise<DefaultListAPIResponse<PollutionSpreadAnalysisItem>> {
  const res: any = await postRequest({
    code: 'supply/getPollutionSpreadAnalysisList',
    params: {
      page_num: params.current,
      page_size: params.pageSize,
      solution_id: params.solutionId,
      solution_guid: params.solutionGuid,
      analysis_title: params.analysisTitle,
      only_current_user: params.onlyCurrentUser,
      sort_field: params.sortField,
      sort_order: params.sortOrder,
    },
  });

  if (res.json_ok && res.data) {
    return {
      status: 'Success',
      total: res.data.total,
      list: convertPollutionSpreadAnalysisList(res.data.list),
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    total: 0,
    list: [],
  };
}

export interface deletePollutionSpreadAnalysisParams {
  /** 方案ID */
  solutionId?: string;
  /** 方案GUID */
  solutionGuid?: string;
}

export interface deletePollutionSpreadAnalysisResponse extends APIResponse {}

export async function deletePollutionSpreadAnalysis(
  params: deletePollutionSpreadAnalysisParams,
): Promise<deletePollutionSpreadAnalysisResponse> {
  if (!params.solutionId && !params.solutionGuid) {
    return {
      status: 'Fail',
      errorMessage: '方案ID和方案GUID至少需要提供一个',
    };
  }

  const res: any = await postRequest({
    code: 'supply/deletePollutionSpreadAnalysis',
    params: {
      solution_id: params.solutionId,
      solution_guid: params.solutionGuid,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}
