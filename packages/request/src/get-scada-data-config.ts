/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ScadaDataConfig } from '@waterdesk/data/otype-vprop-config-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetScadaDataConfigResponse extends APIResponse {
  data?: ScadaDataConfig;
}

export async function getScadaDataConfig(
  time?: string,
): Promise<GetScadaDataConfigResponse> {
  const res: any = await postRequest({
    code: 'watergis/getAssetOTYPEDefList',
    params: {
      time,
    },
  });

  if (res.json_ok && res.otypes) {
    const data: ScadaDataConfig = {};
    try {
      Object.entries(res.otypes).forEach((item: any) => {
        const [otype, value] = item;
        const formConfig = value?.prop_list ?? [];
        data[otype] = formConfig;
      });
    } catch (err) {
      console.log(err);
    }

    return {
      status: 'Success',
      data,
    };
  }

  return {
    status: 'Fail',
  };
}
