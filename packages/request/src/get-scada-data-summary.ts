/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { formatNumber } from '@waterdesk/data/utils';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

type ScadaReliabilityInfoItem = { reliability: string; count: number };

export interface GetScadaReliabilityInfoResponse extends APIResponse {
  list: ScadaReliabilityInfoItem[];
}

export interface GetScadaDataTimeInfoResponse extends APIResponse {
  data?: {
    startTime: string;
    endTime: string;
  };
}

export interface GetScadaDataSummaryInfoResponse extends APIResponse {
  data?: {
    days: number;
    reliabilityRate: number;
    startTime: string;
    endTime: string;
  };
}

export async function getScadaDataTimeInfo(
  otype: string,
  oname: string,
): Promise<GetScadaDataTimeInfoResponse> {
  const res: any = await postRequest({
    code: 'watergis/queryScadaDataTimeRange',
    params: {
      ptype: otype,
      pname: oname,
    },
  });
  if (res.json_ok && Array.isArray(res.values)) {
    const startTime = dayjs(res.values[0].first_time).format(
      'YYYY-MM-DD HH:mm:ss',
    );
    const endTime = dayjs(res.values[0].last_time).format(
      'YYYY-MM-DD HH:mm:ss',
    );

    return {
      status: 'Success',
      data: {
        startTime,
        endTime,
      },
    };
  }
  return {
    status: 'Success',
  };
}

export async function getScadaReliabilityInfo(
  otype: string,
  oname: string,
  vprop: string,
  startTime?: string,
  endTime?: string,
): Promise<GetScadaReliabilityInfoResponse> {
  const res: any = await postRequest({
    code: 'watergis/queryEvaluationSummaryList',
    params: {
      start_time: startTime,
      end_time: endTime,
      otype,
      oname,
      vprop,
    },
  });

  if (res.json_ok && Array.isArray(res.values)) {
    const list = res.values.map(
      ({ reliability, count }: any): ScadaReliabilityInfoItem => ({
        reliability,
        count,
      }),
    );

    return {
      status: 'Success',
      list,
    };
  }
  return {
    status: 'Fail',
    list: [],
  };
}

export async function getScadaDataSummaryInfo(
  otype: string,
  oname: string,
  vprop: string,
): Promise<GetScadaDataSummaryInfoResponse> {
  const [dataTimeInfoRes, reliabilityInfoRes] = await Promise.all([
    getScadaDataTimeInfo(otype, oname),
    getScadaReliabilityInfo(otype, oname, vprop),
  ]);

  if (
    dataTimeInfoRes.status === 'Fail' ||
    reliabilityInfoRes.status === 'Fail'
  ) {
    return {
      status: 'Fail',
    };
  }
  const days = dayjs(dataTimeInfoRes.data?.endTime).diff(
    dataTimeInfoRes.data?.startTime,
    'days',
  );
  const count = reliabilityInfoRes.list.reduce(
    (prev, curr) => prev + curr.count,
    0,
  );
  const memberCount = reliabilityInfoRes.list.reduce((prev, curr) => {
    if (curr.reliability === '4' || curr.reliability === '3') {
      return prev + curr.count;
    }
    return prev;
  }, 0);
  const reliabilityRate = formatNumber((memberCount / count) * 100, 1);

  return {
    status: 'Success',
    data: {
      days,
      reliabilityRate,
      startTime: dataTimeInfoRes.data?.startTime || '',
      endTime: dataTimeInfoRes.data?.endTime || '',
    },
  };
}
