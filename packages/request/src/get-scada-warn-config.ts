/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  BackendWarnConfigValue,
  CompleteScadaWarnConfig,
} from '@waterdesk/data/scada-warn-config';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

interface ScadaWarnConfigListResponse extends APIResponse {
  values: {
    total: number;
    list: CompleteScadaWarnConfig[];
  };
}

export const getAllScadaWarnConfigList = async (
  database: Database,
): Promise<ScadaWarnConfigListResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryScadaWarnConfigList',
  });

  if (data.json_ok) {
    const values = ((data.values as CompleteScadaWarnConfig[]) ?? [])
      .filter((item) => {
        const deviceInfo = database.getDevice(item.PTYPE, item.PNAME);
        if (deviceInfo && !deviceInfo.dataAccess) return false;
        return true;
      })
      .sort((a, b) => {
        const nameComparison = a.PNAME.localeCompare(b.PNAME);
        if (nameComparison !== 0) {
          return nameComparison;
        }

        return a.PTYPE.localeCompare(b.PTYPE);
      });

    return {
      status: 'Success',
      values: { total: values?.length ?? 0, list: values },
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    values: { total: 0, list: [] },
  };
};

export const updateScadaWarnConfigList = async (
  otype: string,
  oname: string,
  warnConfigList: BackendWarnConfigValue,
): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/updateScadaWarnConfig',
    params: {
      otype,
      oname,
      warn_config_list: JSON.stringify(warnConfigList),
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};
