/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  HighlightObject,
  ImpactedDma,
  ImpactedLink,
} from '@waterdesk/data/highlight-object';
import { SchedulingData } from '@waterdesk/data/scheduling-data';
import { HIGHLIGHT_VALVE_ANALYSIS_IMPACTED } from '@waterdesk/data/style-config';
import { APIResponse } from './api/api-response';
import { GetAsyncTaskStatusResponse } from './get-async-task';
import {
  formatValveAnalysisData,
  formatValveAnalysisDmaData,
  formatValveAnalysisLinksDataToMap,
} from './get-burst-flushing-data';
import { postRequestByView } from './request';

interface SchedulingParams {
  burstFlushingParams:
    | {
        otype: string;
        oname: string;
        flow: number;
      }
    | undefined;
  closedObject: { otype: string; oname: string }[];
  plantsData: {
    plantName: string;
    patternData: { pattern: string; value: number }[];
    mode?: string;
  }[];
}

export interface SchedulingResultData {
  impactedLinksObjects?: HighlightObject[];
  impactedDmaObjects?: ImpactedDma[];
  plantDatas?: { title: string; value: number }[];
  impactedLinksObjectsMap?: Map<string, ImpactedLink>;
}

export interface GetSchedulingResultDataResponse extends APIResponse {
  taskId?: string;
}

export interface GetSchedulingDataResponse extends APIResponse {
  schedulingData?: SchedulingData;
  modelTime?: string;
}

export interface GetSchedulingDataAsyncResponse
  extends GetAsyncTaskStatusResponse<SchedulingResultData> {}

export function formatAnalyseSchedulingData(values: any): SchedulingResultData {
  return {
    impactedLinksObjects: formatValveAnalysisData(
      values.back_links,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
    impactedLinksObjectsMap: formatValveAnalysisLinksDataToMap(
      values.back_links,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
    impactedDmaObjects: formatValveAnalysisDmaData(
      values.down_dma3,
      HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
    ),
    plantDatas: values.fact_list,
  };
}

export async function getSchedulingData(
  time: string,
): Promise<GetSchedulingDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/getCalcFactoryPattern',
    params: {
      time,
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (data.values?.[0]) {
          const schedulingData = new SchedulingData(data.values[0].fact_list);

          resolve({
            status: 'Success',
            schedulingData,
            modelTime: data.model_time,
          });
        }
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

export async function analyzeSchedulingData(
  time: string,
  params: SchedulingParams,
): Promise<GetSchedulingResultDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/calculateFactoryPattern',
    params: {
      model_time: time,
      calc_json: JSON.stringify(params),
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        resolve({
          status: 'Success',
          taskId: data.task_id,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}
