/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ApplyType,
  SchemeConfig,
  SchemeData,
} from '@waterdesk/data/scheme-config';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

interface EditSchemeInfo {
  id: string;
  schemeName: string;
  isFixedTime: boolean;
  share: boolean;
  note: string;
  schemeType: string;
}

export interface GetSchemeConfigResponse extends APIResponse {
  data?: SchemeData[];
}

export interface GetSchemeConfigDataResponse
  extends DefaultListAPIResponse<SchemeData> {}

export const querySchemeConfigList = async (
  type: ApplyType,
): Promise<GetSchemeConfigResponse> => {
  const res: any = await postRequest({
    code: 'supply/querySchemeConfigList',
    params: {
      apply_type: type,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const data = res.values.map(
          (item: any): SchemeData => ({
            id: item.scheme_id,
            userName: item.scheme_name,
            schemeName: item.scheme_name,
            applyType: item.user_name,
            schemeType: item.scheme_type,
            configValue: item.config_value,
            schemeShare: item.scheme_share,
            isFixedTime: item.is_fixed_time !== 0,
            remark: item.remark,
            createTime: item.create_time,
            createdByQueryUser: item.created_by_query_user,
          }),
        );
        resolve({
          status: 'Success',
          data,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getSchemeConfigData = async (
  type: ApplyType,
): Promise<GetSchemeConfigDataResponse> => {
  const schemeConfigList = await querySchemeConfigList(type);
  if (schemeConfigList.status === 'Success') {
    return {
      status: schemeConfigList.status,
      list: schemeConfigList.data ?? [],
      total: schemeConfigList.data?.length ?? 0,
    };
  }
  return { status: schemeConfigList.status, list: [], total: 0 };
};

export const addSchemeConfig = async (
  schemeConfig: SchemeConfig,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/addSchemeConfig',
    params: {
      scheme_name: schemeConfig.schemeName,
      apply_type: schemeConfig.applyType,
      scheme_type: schemeConfig.schemeType,
      config_value: schemeConfig.configValue,
      scheme_share: schemeConfig.schemeShare ? 1 : 0,
      is_fixed_time: schemeConfig.isFixedTime ? 1 : 0,
      remark: schemeConfig.remark,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const deleteSchemeConfig = async (id: string): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteSchemeConfig',
    params: {
      scheme_id_list: id,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const updateSchemeConfig = async (
  params: EditSchemeInfo,
): Promise<APIResponse> => {
  const { id, schemeName, share, isFixedTime, note, schemeType } = params;
  const res: any = await postRequest({
    code: 'supply/updateSchemeConfig',
    params: {
      scheme_id: id,
      scheme_name: schemeName,
      is_fixed_time: isFixedTime,
      share,
      remark: note,
      scheme_type: schemeType,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
