/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ScheduleInfo,
  ShiftInfo,
  ShiftScheduleListItem,
  ShiftScheduleLogInfo,
} from '@waterdesk/data/shift-schedule';
import { Dayjs } from 'dayjs';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetShiftInfoListRequest {
  shiftId?: string; // 班次ID
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  shiftDate?: string; // 班次时间
  shiftName?: string; // 班次名称
}

export interface GetScheduleInfoListRequest {
  schedulingId?: string; // 排班ID
  shiftId?: string; // 班次ID
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  shiftDate?: string; // 班次时间
  shiftName?: string; // 班次名称
  schedulingUser?: string; // 值班人
}

export interface BatchCreateOrUpdateShiftInfoRequest {
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  shiftConfig?: string; // 班次配置
}

export interface CreateOrUpdateShiftInfoRequest {
  shiftId?: string; // 班次ID
  shiftDate?: string; // 班次时间
  shiftName?: string; // 班次名称
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
}

interface CreateScheduleInfoRequest {
  shiftId?: string; // 班次ID
  scheduleUser?: string; // 值班人
  remark?: string; // 备注
}

interface CreateScheduleInfoResponse extends APIResponse {
  scheduleId?: string; // 排班ID
}

interface DeleteScheduleInfoRequest {
  scheduleIdList: string | string[]; // 排班ID列表
}

interface GetShiftScopeResponse extends APIResponse {
  values?: {
    maxShiftDate?: string; // 最大班次时间
    minShiftDate?: string; // 最小班次时间
  };
}

interface QueryShiftScheduleParams {
  startTime?: string;
  endTime?: string;
  schedulingUserName?: string;
  modifiedAfterHandover?: number;
}

/** 查询班次信息列表 */
export const getShiftInfoList = async (
  params: GetShiftInfoListRequest,
): Promise<DefaultListAPIResponse<ShiftInfo>> => {
  const res: any = await postRequest({
    code: 'watergis/queryShiftInfoList',
    params: {
      shift_id: params.shiftId,
      start_date: params.startDate,
      end_date: params.endDate,
      time: params.shiftDate,
      shift_classes: params.shiftName,
    },
  });

  if (res?.json_ok) {
    const list: ShiftInfo[] =
      res?.values?.records?.map((item: any) => ({
        shiftId: item.shift_id,
        shiftDate: item.shift_date,
        shiftName: item.shift_classes,
        startTime: item.start_time,
        endTime: item.end_time,
        createUser: item.create_user,
        createTime: item.create_time,
      })) ?? [];

    return {
      total: Number(res?.values?.count),
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 查询排班记录列表 */
export const getScheduleInfoList = async (
  params: GetScheduleInfoListRequest,
): Promise<DefaultListAPIResponse<ScheduleInfo>> => {
  const res: any = await postRequest({
    code: 'watergis/queryShiftSchedulingInfoList',
    params: {
      scheduling_id: params.schedulingId,
      shift_id: params.shiftId,
      start_date: params.startDate,
      end_date: params.endDate,
      time: params.shiftDate,
      shift_classes: params.shiftName,
      scheduling_user: params.schedulingUser,
    },
  });

  if (res?.json_ok) {
    const list: ScheduleInfo[] =
      res?.values?.records?.map((item: any) => ({
        scheduleId: item.scheduling_id,
        shiftId: item.shift_id,
        shiftDate: item.shift_date,
        shiftName: item.shift_classes,
        startTime: item.start_time,
        endTime: item.end_time,
        scheduleUser: item.scheduling_user_name,
        scheduleUserId: item.scheduling_user_id,
        remark: item.remark,
        createUser: item.create_user,
        createTime: item.create_time,
      })) ?? [];

    return {
      total: Number(res?.values?.count),
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 批量创建或更新班次信息 */
export const batchCreateOrUpdateShiftInfo = async (
  params: BatchCreateOrUpdateShiftInfoRequest,
) => {
  const res: any = await postRequest({
    code: 'watergis/batchCreateShiftInfo',
    params: {
      start_date: params.startDate,
      end_date: params.endDate,
      shifts_config: params.shiftConfig,
    },
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 创建排班记录 */
export const createScheduleInfo = async (
  params: CreateScheduleInfoRequest,
): Promise<CreateScheduleInfoResponse> => {
  const res: any = await postRequest({
    code: 'watergis/createShiftSchedulingInfo',
    params: {
      shift_id: params.shiftId,
      scheduling_user: params.scheduleUser,
      remark: params.remark,
    },
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      scheduleId: res?.values?.scheduling_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 删除排班记录 */
export const deleteScheduleInfo = async (
  params: DeleteScheduleInfoRequest,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'watergis/deleteShiftSchedulingInfo',
    params: {
      scheduling_id_list: params.scheduleIdList?.toString(),
    },
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

export const repeatUpdateSchedulingRecord = async ({
  repeatTime,
  scheduleTime,
  userList,
}: {
  repeatTime: [Dayjs, Dayjs];
  scheduleTime: [Dayjs, Dayjs];
  userList: string[];
}): Promise<APIResponse> => {
  const data: any = await postRequest({
    code: 'watergis/repeatUpdateSchedulingRecord',
    params: {
      repeat_start_date: repeatTime[0].format('YYYY-MM-DD 00:00:00'),
      repeat_end_date: repeatTime[1].format('YYYY-MM-DD 00:00:00'),
      update_start_date: scheduleTime[0].format('YYYY-MM-DD 00:00:00'),
      update_end_date: scheduleTime[1].format('YYYY-MM-DD 00:00:00'),
      user_id_list: userList.join(','),
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

/** 获取班次范围 */
export const getMinAndMaxDateScope = async ({
  startDate,
  endDate,
}: {
  startDate?: string;
  endDate?: string;
}): Promise<GetShiftScopeResponse> => {
  const data: any = await postRequest({
    code: 'watergis/queryMaxAndMinShiftDate',
    params: {
      start_date: startDate,
      end_date: endDate,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      values: {
        maxShiftDate: data.values.max_shift_date,
        minShiftDate: data.values.min_shift_date,
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};

export const getShiftSchedulingLog = async (params: {
  startTime?: string;
  endTime?: string;
  pageSize?: number;
  current?: number;
  schedulingUserName?: string;
}): Promise<DefaultListAPIResponse<ShiftScheduleLogInfo>> => {
  const { startTime, endTime, pageSize, current, schedulingUserName } = params;
  const res: any = await postRequest({
    code: 'watergis/queryShiftSchedulingLog',
    params: {
      start_time: startTime,
      end_time: endTime,
      page_size: pageSize,
      current,
      scheduling_user: schedulingUserName,
    },
  });

  if (res.json_ok) {
    const list: ShiftScheduleLogInfo[] = res.values.records.map(
      (item: any): ShiftScheduleLogInfo => ({
        logId: item.log_id,
        shiftDate: item.shift_date,
        shiftClasses: item.shift_classes,
        createTime: item.create_time,
        schedulingUser: item.scheduling_user,
        remark: item.remark,
        creator: item.creator,
        type: item.type,
        shiftClassesId: item.shift_classes_id,
      }),
    );

    return {
      status: 'Success',
      list,
      total: res.values.count,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
};

/**
 * 查询交接班及事件关联信息
 * @param params 查询参数
 * @returns 查询结果
 */
export const queryShiftScheduleList = async (
  params: QueryShiftScheduleParams,
): Promise<DefaultListAPIResponse<ShiftScheduleListItem>> => {
  const res: any = await postRequest({
    code: 'watergis/queryShiftWithEventsAndLogs',
    params: {
      start_time: params.startTime,
      end_time: params.endTime,
      scheduling_user_name: params.schedulingUserName,
      modified_after_handover: params.modifiedAfterHandover === 1,
    },
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      list: res.values?.list || [],
      total: res.values?.total || 0,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.error_message,
    list: [],
    total: 0,
  };
};
