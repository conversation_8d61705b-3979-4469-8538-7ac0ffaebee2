/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InitialIndicatorsValue } from '@waterdesk/data/mini-dashboard/simulation-dashboard-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSimulationIndicatorListResponse extends APIResponse {
  indicatorsValues?: Map<string, InitialIndicatorsValue[]>;
}

/**
 * 获取模拟指标数据
 * @param params
 * @returns
 */
export const getSimulationIndicatorList = async (params: {
  time: string;
  otype_list: string;
  vprop: string;
}): Promise<GetSimulationIndicatorListResponse> => {
  const res: any = await postRequest({
    code: 'watergis/getScadaQuotaPnameAndValues',
    params,
  });

  return new Promise((resolve) => {
    if (res.json_ok && res.values) {
      try {
        const indicatorsValues: Map<string, InitialIndicatorsValue[]> =
          new Map();
        Object.keys(res.values).forEach((indicatorsKey) => {
          if (!Array.isArray(res.values[indicatorsKey])) {
            indicatorsValues.set(indicatorsKey, []);
          } else {
            const values = res.values[indicatorsKey].map((item: any) => {
              if (
                typeof item.oname !== 'string' ||
                typeof item.pname !== 'string' ||
                typeof item.ptype !== 'string'
              ) {
                throw Error('数据格式不对');
              }
              return {
                otype: indicatorsKey,
                oname: item.oname,
                pname: item.pname,
                ptype: item.ptype,
                value: item.value,
              };
            });
            indicatorsValues.set(indicatorsKey, values);
          }
        });
        resolve({
          status: 'Success',
          indicatorsValues,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    } else {
      resolve({
        status: 'Fail',
        errorMessage: res.json_msg,
      });
    }
  });
};
