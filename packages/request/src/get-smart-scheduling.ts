/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { DeviceAction } from '@waterdesk/data/dispatch-command/create-command';
import {
  HistorySuggestScheduling,
  SchedulingOperateTimeData,
  transformPlantToOperateTimeData,
  transformScadaToOperateTimeData,
  transformToOperateTimeData,
} from '@waterdesk/data/scheduling-data';
import {
  ControlSchedulingSuggestionInfo,
  FlowForecast,
  HistoricalSchedulingSuggestionInfo,
  HistoricalSchedulingSuggestionListItem,
  ModelInfo,
  ModelSchedulingSuggestionInfo,
  ModelSchedulingSuggestionListItem,
  SchedulingStrategyInfo,
  SmartSchedulingType,
} from '@waterdesk/data/smart-scheduling-data';
import {
  LatestScheduleSimulationData,
  LatestTaskInfo,
  LatestTaskInfoListData,
  parseScheduleSimulationOperation,
  ScheduleSimulationItem,
  ScheduleSimulationListData,
  ScheduleSimulationListParams,
} from '@waterdesk/data/smart-scheduling-data-new';
import { TimeData } from '@waterdesk/data/time-data';
import { formatNumber } from '@waterdesk/data/utils';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSchedulingRecommendedValuesResponse extends APIResponse {
  values?: TimeData[];
}

export async function getSchedulingRecommendedValues(
  date: string,
): Promise<GetSchedulingRecommendedValuesResponse> {
  const data: any = await postRequest({
    code: 'supply/getStatisticsInfoByDate',
    params: {
      start_date: dayjs(date).format('YYYY-MM-DD'),
      end_date: dayjs(date).format('YYYY-MM-DD'),
    },
  });

  if (data.json_ok) {
    if (Array.isArray(data.values) && data.values.length === 1) {
      try {
        const recommendedValues: TimeData[] = [];
        const similarDays = JSON.parse(data.values[0].similar_days);
        if (Array.isArray(similarDays)) {
          similarDays.forEach((item: any) => {
            recommendedValues.push({
              time: item.date,
              value: item.value,
            });
          });
        }

        return {
          status: 'Success',
          values: recommendedValues,
        };
      } catch (err) {
        console.error(`getSchedulingRecommendedValues error: ${err}`);
        return {
          status: 'Fail',
          errorMessage: `${err}`,
        };
      }
    }

    return {
      status: 'Fail',
      errorMessage: 'No data found',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export interface GetHistorySchedulingValuesResponse extends APIResponse {
  values?: SchedulingOperateTimeData[];
}

export async function getHistorySchedulingValues(
  timeRange: [string, string],
  database: Database,
): Promise<GetHistorySchedulingValuesResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryScheduleStrategyList',
    params: {
      start_time: timeRange[0],
      end_time: timeRange[1],
    },
  });

  if (data.json_ok) {
    try {
      const historySchedulingValues: SchedulingOperateTimeData[] =
        transformToOperateTimeData(
          data.values.records.map((item: any) => ({
            startTime: item.start_time,
            endTime: item.end_time,
            data: JSON.parse(item.strategy_info),
          })),
          database,
        );

      return {
        status: 'Success',
        values: historySchedulingValues,
      };
    } catch (err) {
      console.error(`queryScheduleStrategyList error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export interface SchedulingInfo {
  base_value: number | string;
  target_value: number | string;
  description: string;
  end_time: string;
  main_control: string;
  oname: string;
  otype: string;
  start_time: string;
}
export interface MatchSuggestData {
  oname: string;
  otype: string;
  [DeviceAction.PRESSURE_ADJUSTMENT]?: SchedulingInfo[];
  [DeviceAction.FLOW_ADJUSTMENT]?: SchedulingInfo[];
  [DeviceAction.WATER_PUMP]?: SchedulingInfo[];
}
export type SuggestionSchedulingValues = {
  operationChangeData: {
    [key: string]: SchedulingOperateTimeData[];
  };
  matchScore: number;
  matchData: {
    previousData: MatchSuggestData[];
    suggestData: MatchSuggestData[];
  };
};
export interface GetSuggestionSchedulingValuesResponse extends APIResponse {
  list: HistoricalSchedulingSuggestionInfo[];
}

function parseStrategyInfo(strategyInfo: any): SchedulingStrategyInfo {
  return {
    otype: strategyInfo.otype,
    oname: strategyInfo.oname,
    startTime: strategyInfo.START_TIME,
    endTime: strategyInfo.END_TIME,
    pressureAdjustment:
      strategyInfo.PRESSURE_ADJUSTMENT?.map((pa: any) => ({
        otype: pa.otype,
        oname: pa.oname,
        description: pa.description,
        mainControl: pa.main_control,
        startTime: pa.start_time,
        endTime: pa.end_time,
        baseValue: pa.base_value,
        firstValue: pa.first_value,
        changeValue: pa.change_value,
        secondValue: pa.second_value,
      })) ?? [],
    flowAdjustment:
      strategyInfo.FLOW_ADJUSTMENT?.map((fa: any) => ({
        otype: fa.otype,
        oname: fa.oname,
        description: fa.description,
        mainControl: fa.main_control,
        startTime: fa.start_time,
        endTime: fa.end_time,
        baseValue: fa.base_value,
        firstValue: fa.first_value,
        changeValue: fa.change_value,
        secondValue: fa.second_value,
      })) ?? [],
    openEquivalentPump: !!strategyInfo.OPEN_EQUIVALENT_PUMP,
    equivalentPumpInfo: strategyInfo.EQUIVALENT_PUMP_INFO
      ? {
          pressure: strategyInfo.EQUIVALENT_PUMP_INFO.PRESSURE,
          flow: strategyInfo.EQUIVALENT_PUMP_INFO.FLOW,
        }
      : undefined,
    waterPump:
      strategyInfo.WATER_PUMP?.map((wp: any) => ({
        otype: wp.otype,
        oname: wp.oname,
        description: wp.description,
        mainControl: wp.main_control,
        startTime: wp.start_time,
        endTime: wp.end_time,
        baseValue: wp.base_value,
        firstValue: wp.first_value,
        changeValue: wp.change_value,
        secondValue: wp.second_value,
        frequencymode: wp.frequencymode,
      })) ?? [],
    info:
      strategyInfo.INFO?.map((info: any) => ({
        otype: info.otype,
        oname: info.oname,
        startTime: info.start_time,
        endTime: info.end_time,
        value: info.value,
        startMinutes: info.startMinutes,
        endMinutes: info.endMinutes,
      })) ?? [],
  };
}

function parseFlowForecast(flowFc: any): FlowForecast[] {
  return flowFc.map((item: any) => {
    const time = Object.keys(item)[0];
    return {
      time,
      value: item[time],
    };
  });
}

function parseModelInfo(modelInfo: any): ModelInfo | undefined {
  if (!modelInfo) return undefined;
  return {
    fileName: modelInfo.FileName,
    userName: modelInfo.User,
    note: modelInfo.Note,
  };
}

export function parseSmartSchedulingSuggestion(
  data: any,
): HistoricalSchedulingSuggestionInfo {
  return {
    suggestId: data.suggest_id,
    otime: data.otime,
    startTime: data.start_time,
    endTime: data.end_time,
    timeAlign: data.time_align,
    strategyInfo:
      JSON.parse(data.strategy_info ?? null)?.map(parseStrategyInfo) ?? [],
    matchStartTime: data.match_start_time,
    matchEndTime: data.match_end_time,
    matchTime: data.match_time,
    matchTimeAlign: data.match_time_align,
    matchStrategyInfo:
      JSON.parse(data.match_strategy_info ?? null)?.map(parseStrategyInfo) ??
      [],
    suggestStartTime: data.suggest_start_time,
    suggestEndTime: data.suggest_end_time,
    tmFlowFc: parseFlowForecast(JSON.parse(data.tm_flow_fc ?? null) ?? []),
    sumFlowFc: data.sum_flow_fc,
    matchScore: formatNumber(data.match_score ?? 0, 2),
    remark: data.remark,
    suggestDispatchInfo:
      JSON.parse(data.suggest_dispatch_info ?? null)?.map(parseStrategyInfo) ??
      [],
    suggestState: data.suggest_state,
    stime: data.stime,
    suggestAcceptState: data.suggest_accept_state,
    suggestUseState: data.suggest_use_state,
  };
}

function parseSmartSchedulingSuggestionByModel(
  data: any,
): ModelSchedulingSuggestionInfo {
  return {
    suggestId: data.suggest_id,
    otime: data.otime,
    startTime: data.start_time,
    endTime: data.end_time,
    timeAlign: data.time_align,
    strategyInfo:
      JSON.parse(data.strategy_info ?? null)?.map(parseStrategyInfo) ?? [],
    matchModelId: data.match_model_id,
    matchModelInfo: parseModelInfo(data.match_model_info),
    matchStartTime: data.match_start_time,
    matchEndTime: data.match_end_time,
    matchTime: data.match_time,
    matchTimeAlign: data.match_time_align,
    matchStrategyInfo:
      JSON.parse(data.match_strategy_info ?? null)?.map(parseStrategyInfo) ??
      [],
    suggestStartTime: data.suggest_start_time,
    suggestEndTime: data.suggest_end_time,
    tmFlowFc: parseFlowForecast(JSON.parse(data.tm_flow_fc ?? null) ?? []),
    sumFlowFc: data.sum_flow_fc,
    matchScore: formatNumber(data.match_score ?? 0, 2),
    remark: data.remark,
    suggestDispatchInfo:
      JSON.parse(data.suggest_dispatch_info ?? null)?.map(parseStrategyInfo) ??
      [],
    suggestState: data.suggest_state,
    stime: data.stime,
    suggestAcceptState: data.suggest_accept_state,
    suggestUseState: data.suggest_use_state,
  };
}

/**
 * 解析根据历史推荐的智能调度数据
 */
export function parseHistoricalSchedulingSuggestion(
  data: any[],
): HistoricalSchedulingSuggestionInfo[] {
  if (Array.isArray(data))
    return data.map((m) => parseSmartSchedulingSuggestion(m));

  return [];
}

// 查询最新的调度策略 - 根据历史
export async function getSmartScheduleFromHistory(
  time: string,
): Promise<{ list: HistoricalSchedulingSuggestionInfo[] } & APIResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryRecentDispatchSuggest',
    params: {
      time,
    },
  });

  if (data.json_ok) {
    try {
      const list = parseHistoricalSchedulingSuggestion(data.values);

      return {
        status: 'Success',
        list,
      };
    } catch (err) {
      console.error('调度策略(根据历史)解析失败：', err);
      return {
        list: [],
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    list: [],
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

/**
 * 解析根据强约束策略推荐的智能调度数据
 */
export function parseControlSchedulingSuggestion(
  data: any[],
): ControlSchedulingSuggestionInfo[] {
  if (Array.isArray(data))
    return data.map((m) => ({
      suggestId: m.suggest_id,
      otime: m.otime,
      stime: m.stime,
      triggerInfo: JSON.parse(m.trigger_info),
      suggestState: m.suggest_state,
      remark: m.remark,
      suggestDispatchInfo:
        JSON.parse(m.suggest_dispatch_info)?.map(parseStrategyInfo) ?? [],
    }));

  return [];
}

// 查询最新的调度策略 - 根据强约束策略
export async function getSmartScheduleFromControl(
  time: string,
): Promise<{ list: ControlSchedulingSuggestionInfo[] } & APIResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryRecentDispatchSuggestFromControl',
    params: {
      time,
    },
  });

  if (data.json_ok) {
    try {
      const list = parseControlSchedulingSuggestion(data.values);

      return {
        status: 'Success',
        list,
      };
    } catch (err) {
      console.error('调度策略(根据强约束策略)解析失败：', err);
      return {
        list: [],
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    list: [],
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

/**
 * 解析根据模型推荐的智能调度数据
 */
export function parseModelSchedulingSuggestion(
  data: any[],
): ModelSchedulingSuggestionInfo[] {
  if (Array.isArray(data))
    return data.map((m) => parseSmartSchedulingSuggestionByModel(m));

  return [];
}

// 查询最新的调度策略 - 根据模型
export async function getSmartScheduleFromModel(
  time: string,
): Promise<{ list: ModelSchedulingSuggestionInfo[] } & APIResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryRecentDispatchSuggestFromModel',
    params: {
      time,
    },
  });

  if (data.json_ok) {
    try {
      const list = parseModelSchedulingSuggestion(data.values);

      return {
        status: 'Success',
        list,
      };
    } catch (err) {
      console.error('调度策略(根据模型)解析失败：', err);
      return {
        list: [],
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    list: [],
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export interface GetHistoryPlantSchedulingValuesResponse extends APIResponse {
  values?: {
    schedulingOperateData: SchedulingOperateTimeData[];
    indicatorMap: {
      [key: string]: {
        otype: string;
        oname: string;
      };
    };
  };
}

export interface GetHistorySuggestionSchedulingValuesResponse
  extends APIResponse {
  values?: HistorySuggestScheduling[];
}

/**
 * 获取历史调度策略列表 - 根据历史的
 */
export async function getHistorySmartScheduleListFromHistory(params: {
  timeRange: [string, string];
  current?: number;
  pageSize?: number;
}): Promise<DefaultListAPIResponse<HistoricalSchedulingSuggestionListItem>> {
  const res: any = await postRequest({
    code: 'watergis/queryHistoryDispatchSuggest',
    params: {
      start_time: params.timeRange[0],
      end_time: params.timeRange[1],
      current: params.current,
      page_size: params.pageSize,
    },
  });
  if (res.json_ok) {
    try {
      const list: HistoricalSchedulingSuggestionListItem[] = res.values.map(
        (item: any): HistoricalSchedulingSuggestionListItem => ({
          id: uuidv4(),
          startTime: item.start_time,
          endTime: item.end_time,
          otime: item.otime,
          timeAlign: item.time_align,
          strategyInfo:
            JSON.parse(item.strategy_info ?? null)?.map(parseStrategyInfo) ??
            [],
          details: item.details?.map(parseSmartSchedulingSuggestion) ?? [],
        }),
      );
      return {
        status: 'Success',
        list,
        total: res.total ?? 0,
      };
    } catch (err) {
      console.error(`解析获取历史调度策略列表 - 根据历史的 error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        list: [],
        total: res.total ?? 0,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
}

/**
 * 获取历史调度策略列表 - 根据约束的
 */
export async function getHistorySmartScheduleListFromControl(params: {
  timeRange: [string, string];
  current?: number;
  pageSize?: number;
}): Promise<DefaultListAPIResponse<ControlSchedulingSuggestionInfo>> {
  const res: any = await postRequest({
    code: 'watergis/queryHistoryDispatchSuggestFromControl',
    params: {
      start_time: params.timeRange[0],
      end_time: params.timeRange[1],
      current: params.current,
      page_size: params.pageSize,
    },
  });
  if (res.json_ok) {
    try {
      const list: ControlSchedulingSuggestionInfo[] =
        parseControlSchedulingSuggestion(res.values);
      return {
        status: 'Success',
        list,
        total: res.total ?? 0,
      };
    } catch (err) {
      console.error(`解析获取的历史调度策略列表 - 根据约束的 error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        list: [],
        total: res.total ?? 0,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
}

/**
 * 获取历史调度策略列表 - 根据模型的
 */
export async function getHistorySmartScheduleListFromModel(params: {
  timeRange: [string, string];
  current?: number;
  pageSize?: number;
}): Promise<DefaultListAPIResponse<ModelSchedulingSuggestionListItem>> {
  const res: any = await postRequest({
    code: 'watergis/queryHistoryDispatchSuggestFromModel',
    params: {
      start_time: params.timeRange[0],
      end_time: params.timeRange[1],
      current: params.current ?? 1,
      page_size: params.pageSize ?? 20,
    },
  });
  if (res.json_ok) {
    try {
      const list: ModelSchedulingSuggestionListItem[] = res.values.map(
        (item: any): ModelSchedulingSuggestionListItem => ({
          id: uuidv4(),
          startTime: item.start_time,
          endTime: item.end_time,
          otime: item.otime,
          timeAlign: item.time_align,
          strategyInfo:
            JSON.parse(item.strategy_info ?? null)?.map(parseStrategyInfo) ??
            [],
          details:
            item.details?.map(parseSmartSchedulingSuggestionByModel) ?? [],
        }),
      );
      return {
        status: 'Success',
        list,
        total: res.total ?? 0,
      };
    } catch (err) {
      console.error(`解析获取历史调度策略列表 - 根据模型的 error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        list: [],
        total: res.total ?? 0,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
    total: 0,
  };
}

/**
 * 更新调度策略备注信息
 * @type SmartSchedulingType
 */
export async function updateSuggestScheduling(params: {
  id: string;
  type: SmartSchedulingType;
  remark?: string;
}): Promise<APIResponse> {
  const { id, remark, type } = params;
  // default HISTORY
  let codeUrl = 'watergis/updateDispatchSuggest';

  if (type === SmartSchedulingType.SCHEDULE) {
    codeUrl = 'watergis/updateDispatchSuggestRemarkFromControl';
  } else if (type === SmartSchedulingType.MODEL) {
    codeUrl = 'watergis/updateDispatchSuggestFromModel';
  }
  const data: any = await postRequest({
    code: codeUrl,
    params: {
      suggest_id: id,
      remark: remark ?? '',
    },
  });
  return {
    status: data.json_ok ? 'Success' : 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getHistorySchedulingPlantValues(
  otype: string,
  oname: string,
  timeRange: [string, string],
  database: Database,
): Promise<GetHistoryPlantSchedulingValuesResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryFactPumpStabilityList',
    params: {
      otype,
      oname,
      start_time: timeRange[0],
      end_time: timeRange[1],
    },
  });

  if (data.json_ok) {
    try {
      const formatData = transformPlantToOperateTimeData(
        data.values.records.map((item: any) => ({
          startTime: item.start_time,
          endTime: item.end_time,
          data: JSON.parse(item.base_info),
        })),
        database,
      );

      return {
        status: 'Success',
        values: formatData,
      };
    } catch (err) {
      console.error(`queryFactPumpStabilityList error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function getHistorySchedulingScadaValues(
  otype: string,
  oname: string,
  timeRange: [string, string],
  database: Database,
): Promise<GetHistorySchedulingValuesResponse> {
  const data: any = await postRequest({
    code: 'watergis/queryScadaStabilityList',
    params: {
      otype,
      oname,
      start_time: timeRange[0],
      end_time: timeRange[1],
    },
  });

  if (data.json_ok) {
    try {
      const historySchedulingValues: SchedulingOperateTimeData[] =
        transformScadaToOperateTimeData(
          data.values.records.map((item: any) => ({
            otype: item.otype,
            oname: item.oname,
            startTime: item.start_time,
            endTime: item.end_time,
            data: item.base_value,
          })),
          database,
        );

      return {
        status: 'Success',
        values: historySchedulingValues,
      };
    } catch (err) {
      console.error(`queryScadaStabilityList error: ${err}`);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

/**
 * 更新调度推荐采纳状态
 * @param params 参数对象，包含调度推荐采纳状态列表
 * @param params.list 调度推荐采纳状态列表，每项包含 suggestId 和 suggestAcceptState
 * @returns 返回 API 响应，包含状态和错误信息
 */
export async function updateDispatchSuggestAcceptStateList(params: {
  list: Array<{
    suggestId: string;
    suggestAcceptState: number;
  }>;
}): Promise<APIResponse> {
  const data: any = await postRequest({
    code: 'watergis/updateDispatchSuggestAcceptStateList',
    params: {
      suggest_accept_state_list: params.list.map((item) => ({
        suggest_id: item.suggestId,
        suggest_accept_state: item.suggestAcceptState,
      })),
    },
  });
  return {
    status: data.json_ok ? 'Success' : 'Fail',
    errorMessage: data.json_msg,
  };
}

// ========== 新的智能调度策略API（业务重构） ==========

/**
 * 查询智能调度策略列表
 * @param params 查询参数
 * @returns 返回调度策略列表和分页信息
 */
export async function getScheduleSimulationList(
  params: ScheduleSimulationListParams = {},
): Promise<{ data: ScheduleSimulationListData } & APIResponse> {
  const data: any = await postRequest({
    code: 'schedule/simulation/list',
    params: {
      task_id: params.taskId,
      model_id: params.modelId,
      otime_start: params.otimeStart,
      otime_end: params.otimeEnd,
      page_size: params.pageSize,
      page_num: params.pageNum,
    },
  });

  if (data.json_ok) {
    try {
      return {
        status: 'Success',
        data: {
          list: data.data.list.map((item: any) => ({
            otime: item.otime,
            modelId: item.modelId,
            targetStatus: item.targetStatus,
            operations: item.operations,
            forecastFlow: item.forecastFlow,
            taskId: item.taskId,
            stime: item.stime,
          })),
          total: data.data.total,
          pageNum: data.data.pageNum,
          pageSize: data.data.pageSize,
          pages: data.data.pages,
        },
      };
    } catch (err) {
      console.error('查询智能调度策略列表解析失败：', err);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        data: { list: [], total: 0 },
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    data: { list: [], total: 0 },
  };
}

/**
 * 获取最新的智能调度策略任务ID（按模型分组）
 * @returns 返回最新任务信息列表
 */
export async function getLatestScheduleTaskId(): Promise<
  { data: LatestTaskInfoListData } & APIResponse
> {
  const data: any = await postRequest({
    code: 'schedule/simulation/latest-task',
  });

  if (data.json_ok) {
    try {
      return {
        status: 'Success',
        data: {
          latestTaskInfoList: data.data.latestTaskInfoList.map((item: any) => ({
            taskId: item.taskId,
            modelId: item.modelId,
            stime: item.stime,
          })),
        },
      };
    } catch (err) {
      console.error('获取最新任务ID解析失败：', err);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        data: { latestTaskInfoList: [] },
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    data: { latestTaskInfoList: [] },
  };
}

/**
 * 获取最新的智能调度策略详情（多模型）
 * @returns 返回最新调度策略详情
 */
export async function getLatestScheduleSimulation(): Promise<
  { data: LatestScheduleSimulationData } & APIResponse
> {
  const data: any = await postRequest({
    code: 'schedule/simulation/latest',
  });

  if (data.json_ok) {
    try {
      return {
        status: 'Success',
        data: {
          latestTaskInfoList: data.data.latestTaskInfoList.map(
            (item: any): LatestTaskInfo => ({
              taskId: item.taskId,
              modelId: item.modelId,
              stime: item.stime,
            }),
          ),
          total: data.data.total,
          strategies: data.data.strategies.map(
            (item: any): ScheduleSimulationItem => ({
              otime: item.otime,
              oname: item.oname,
              otype: item.otype,
              modelId: item.modelId,
              operations: parseScheduleSimulationOperation(
                item.otype,
                item.oname,
                item.operations,
              ),
              forecastFlow: item.forecastFlow,
              taskId: item.taskId,
              stime: item.stime,
            }),
          ),
        },
      };
    } catch (err) {
      console.error('获取最新调度策略详情解析失败：', err);
      return {
        status: 'Fail',
        errorMessage: `${err}`,
        data: { latestTaskInfoList: [], total: 0, strategies: [] },
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
    data: { latestTaskInfoList: [], total: 0, strategies: [] },
  };
}
