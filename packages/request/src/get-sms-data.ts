/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  Importance,
  MsgStatus,
  SendSMSParams,
  SMSList,
  SMSListParams,
} from '@waterdesk/data/sms';
import { formatDayjsInObject } from '@waterdesk/data/string';
import { v4 as uuidv4 } from 'uuid';

import { PageParams, SortMethod, sorterMethodMap } from './api/api-request';
import {
  APIResponse,
  DefaultBackendResponse,
  DefaultListAPIResponse,
  DefaultListBackendResponse,
} from './api/api-response';
import { postRequest } from './request';

interface GetSMSListRequestInBackend extends PageParams {
  start_time?: string;
  end_time?: string;
  msg_type?: string;
  importance?: Importance;
  content?: string;
  note?: string;
  receiver?: string;
  msg_from?: string;
  sort_by?: string;
  sort_method?: SortMethod;
}

interface GetSMSListResponseInBackend {
  msg_id?: string; // 消息id
  msg_from?: string; // 消息来源
  msg_type?: string; // 消息类型
  msg_text?: string; // 消息内容
  create_time?: string; // 创建时间
  note?: string; // 备注
  importance?: Importance; // 重要程度
  user_name?: string; // 用户名
  msg_host?: string; // 消息发送商
  msg_send_type?: string; // 消息发送方式
  msg_send_channel?: string; // 消息发送渠道
  msg_status?: MsgStatus; // 发送状态
  send_time?: string; // 发送时间
  err_msg?: string; // 错误原因
}

interface SendSMSRequestInBackend {
  msg_from: string;
  msg_type?: string;
  msg_json: string;
  msg_text: string;
  importance: string;
  msg_creator?: string;
  msg_send_json?: string;
}

interface SendSMSResponseInBackend {
  msg_id?: string; // 消息id
}

interface SendSMSResponse extends APIResponse {
  msgId?: string; // 消息id
}

const sorterServerFieldMap: Record<string, string> = {
  createTime: 'create_time',
};

/** 获取短信列表 */
export const getSMSList = async (
  params: PageParams,
  formData?: SMSListParams,
): Promise<DefaultListAPIResponse<SMSList>> => {
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const requestParams: GetSMSListRequestInBackend = {
    current: params.current,
    pageSize: params.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    msg_type: formData?.channelType,
    importance: formData?.importance,
    content: formData?.content,
    note: formData?.note,
    receiver: formData?.receiver,
    msg_from: formData?.msgFrom,
    sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'watergis/queryMsgListPage',
    params: formatDayjsParams,
  })) as DefaultListBackendResponse<GetSMSListResponseInBackend>;

  if (res?.json_ok) {
    const list =
      res?.values?.records?.map((i) => ({
        msgId: i?.msg_id,
        msgFrom: i?.msg_from,
        channelType: i?.msg_type,
        msgText: i?.msg_text,
        createTime: i?.create_time,
        note: i?.note,
        importance: i?.importance,
        userName: i?.user_name,
        msgHost: i?.msg_host,
        msgSendType: i?.msg_send_type,
        msgSendChannel: i?.msg_send_channel,
        msgStatus: i?.msg_status,
        sendTime: i?.send_time,
        errMsg: i?.err_msg,
        uuid: uuidv4(),
      })) ?? [];

    return {
      total: Number(res?.values?.count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 发送短信 */
export const sendSMS = async (
  formData?: SendSMSParams,
  msgJsonType?: string,
): Promise<SendSMSResponse> => {
  const msgJson = {
    type: msgJsonType ?? 'CUSTOM_MSG',
    message: formData?.msgText ?? '',
  };

  const formParams: SendSMSRequestInBackend = {
    msg_from: formData?.msgFrom ?? 'CUSTOM',
    msg_type: formData?.channelType,
    msg_json: JSON.stringify(msgJson),
    msg_text: formData?.msgText ?? '',
    importance: formData?.importance ?? 'MEDIUM',
    msg_creator: formData?.msgCreator,
    msg_send_json: JSON.stringify(formData?.msgSendJson),
  };

  const res = (await postRequest({
    code: 'watergis/insertMessagePool',
    params: formParams,
  })) as DefaultBackendResponse<SendSMSResponseInBackend>;

  if (res?.json_ok) {
    return {
      msgId: res?.values?.msg_id,
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};
