/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SmsTemplate } from '@waterdesk/data/sms-template';
import { PageParams } from './api/api-request';
import {
  DefaultListAPIResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest } from './request';

export interface GetSmsTemplateListRequest {
  /** 短信模板类型 */
  type?: string;
  /** 短信模板名称 */
  name?: string;
}

export interface DeleteSmsTemplateRequest {
  /** 短信模板ID列表 */
  idList?: string[];
}

/** 获取短信模板 */
export const getSmsTemplateList = async (
  _params: PageParams,
  formData?: GetSmsTemplateListRequest,
): Promise<DefaultListAPIResponse<SmsTemplate>> => {
  const requestParams = {
    msg_type: formData?.type,
    msg_title: formData?.name,
  };

  const res: any = await postRequest({
    code: 'watergis/queryMsgTemplateUserList',
    params: requestParams,
  });

  if (res?.json_ok) {
    const responseData = res?.values?.records ?? [];
    const list: SmsTemplate[] = responseData?.map((item: any) => ({
      id: item.msg_template_id,
      type: item?.msg_type,
      name: item?.msg_title,
      content: item?.msg_format,
      users: item?.user_list?.map((user: any) => ({
        id: user.user_id,
        name: user.user_name,
        mobile: user.user_phone,
        department: user.department_name,
      })),
    }));

    return {
      list,
      total: res?.values?.total,
      status: 'Success',
    };
  }

  return {
    list: [],
    status: 'Fail',
    errorMessage: res?.json_msg,
    total: 0,
  };
};

/** 新建短信模板 */
export const createSmsTemplate = withApiResponseHandler(
  async (formData: SmsTemplate) => {
    const params = {
      msg_type: formData.type,
      msg_title: formData.name,
      msg_format: formData.content,
      msg_user: JSON.stringify(
        formData.users?.map((user) => ({
          user_id: user.id,
          user_phone: user.mobile,
        })),
      ),
    };

    const res: any = await postRequest({
      code: 'watergis/createMsgTemplateUserInfo',
      params,
    });

    return res;
  },
);

/** 编辑短信模板 */
export const updateSmsTemplate = withApiResponseHandler(
  async (formData: SmsTemplate) => {
    const params = {
      msg_template_id: formData.id,
      msg_type: formData.type,
      msg_title: formData.name,
      msg_format: formData.content,
      msg_user: JSON.stringify(
        formData.users?.map((user) => ({
          user_id: user.id,
          user_phone: user.mobile,
        })),
      ),
    };

    const res: any = await postRequest({
      code: 'watergis/updateMsgTemplateUserInfo',
      params,
    });

    return res;
  },
);

/** 删除短信模板 */
export const deleteSmsTemplate = withApiResponseHandler(
  async (formData: DeleteSmsTemplateRequest) => {
    const params = {
      msg_template_id_list: formData.idList?.toString(),
    };
    const res: any = await postRequest({
      code: 'watergis/deleteMsgTemplateUserInfo',
      params,
    });

    return res;
  },
);
