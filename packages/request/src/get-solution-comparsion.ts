/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  CompareSourceType,
  SolutionComparisonItem,
} from '@waterdesk/data/solution';
import dayjs from 'dayjs';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface saveSolutionComparisonRequest {
  /** 对比方案ID，更新时必填 */
  comparisonId?: string;
  /** 对比类型 */
  comparisonType: string;
  /** 基准方案ID */
  baseSolutionId: string;
  /** 测试方案ID */
  testSolutionId: string;
  /** 基准方案GUID */
  baseSolutionGuid: string;
  /** 测试方案GUID */
  testSolutionGuid: string;
  /** 对比方案标题 */
  comparisonTitle: string;
  /** 对比时间 */
  comparisonTime: string;
  /** 对比备注 */
  comparisonNote?: string;
  /** 对比条件 */
  comparisonCondition: string;
  /** 对比结果 */
  comparisonResult: string;
}

export interface saveSolutionComparisonResponse extends APIResponse {
  /** 对比方案ID */
  comparisonId?: string;
}

export interface getSolutionComparisonDetailResponse extends APIResponse {
  /** 对比方案ID */
  comparisonId?: string;
  /** 对比类型 */
  comparisonType?: string;
  /** 基准方案ID */
  baseSolutionId?: string;
  /** 测试方案ID */
  testSolutionId?: string;
  /** 基准方案GUID */
  baseSolutionGuid?: string;
  /** 测试方案GUID */
  testSolutionGuid?: string;
  /** 对比方案标题 */
  comparisonTitle?: string;
  /** 对比时间 */
  comparisonTime?: string;
  /** 对比备注 */
  comparisonNote?: string;
  /** 对比条件 */
  comparisonCondition?: string;
  /** 对比结果 */
  comparisonResult?: string;
}

export interface getSolutionComparisonListParams {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  pageSize: number;
  /** 对比ID */
  comparisonId?: string;
  /** 对比标题 */
  comparisonTitle?: string;
  /** 是否只查询当前用户 */
  onlyCurrentUser?: boolean;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向 */
  sortOrder?: 'ascend' | 'descend';
}

export interface deleteSolutionComparisonResponse extends APIResponse {}

export async function saveSolutionComparison(
  params: saveSolutionComparisonRequest,
): Promise<saveSolutionComparisonResponse> {
  const res: any = await postRequest({
    code: 'supply/saveSolutionComparison',
    params: {
      comparison_id: params.comparisonId,
      comparison_type:
        params.comparisonType === CompareSourceType.SOLUTION ? 0 : 1,
      base_solution_id: params.baseSolutionId,
      test_solution_id: params.testSolutionId,
      base_solution_guid: params.baseSolutionGuid,
      test_solution_guid: params.testSolutionGuid,
      comparison_title: params.comparisonTitle,
      comparison_time: params.comparisonTime,
      comparison_note: params.comparisonNote,
      comparison_condition: params.comparisonCondition,
      comparison_result: params.comparisonResult,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      comparisonId: res.data.comparison_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function getSolutionComparisonDetail(
  comparisonId: string,
): Promise<getSolutionComparisonDetailResponse> {
  if (!comparisonId) {
    return {
      status: 'Fail',
      errorMessage: '对比ID不能为空',
    };
  }

  const res: any = await postRequest({
    code: 'supply/getSolutionComparisonDetail',
    params: {
      comparison_id: comparisonId,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      comparisonId: res.data.comparison_id,
      comparisonType:
        res.data.comparison_type === 0
          ? CompareSourceType.SOLUTION
          : CompareSourceType.HISTORY,
      baseSolutionId: res.data.base_solution_id,
      testSolutionId: res.data.test_solution_id,
      baseSolutionGuid: res.data.base_solution_guid,
      testSolutionGuid: res.data.test_solution_guid,
      comparisonTitle: res.data.comparison_title,
      comparisonTime: res.data.comparison_time,
      comparisonNote: res.data.comparison_note,
      comparisonCondition: res.data.comparison_condition,
      comparisonResult: res.data.comparison_result,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export const convertSolutionComparisonList = (
  list: any[],
): SolutionComparisonItem[] =>
  (list ?? []).map((item) => {
    const comparisonType =
      item.comparison_type === 0
        ? CompareSourceType.SOLUTION
        : CompareSourceType.HISTORY;
    const testSolutionName =
      comparisonType === CompareSourceType.SOLUTION
        ? item.test_solution_name
        : dayjs(item.test_solution_id).format('YYYY-MM-DD');
    return {
      comparisonId: item.comparison_id,
      comparisonType,
      baseSolutionId: item.base_solution_id,
      testSolutionId: item.test_solution_id,
      baseSolutionGuid: item.base_solution_guid,
      testSolutionGuid: item.test_solution_guid,
      comparisonTitle: item.comparison_title,
      comparisonTime: item.comparison_time,
      comparisonNote: item.comparison_note,
      createTime: item.create_time,
      updateTime: item.update_time,
      createUser: item.create_user,
      baseSolutionName: item.base_solution_name,
      testSolutionName,
      eventsInfo: item.events_info,
    };
  });

export async function getSolutionComparisonList(
  params: getSolutionComparisonListParams,
): Promise<DefaultListAPIResponse<SolutionComparisonItem>> {
  const res: any = await postRequest({
    code: 'supply/getSolutionComparisonList',
    params: {
      page_num: params.current,
      page_size: params.pageSize,
      comparison_id: params.comparisonId,
      comparison_title: params.comparisonTitle,
      only_current_user: params.onlyCurrentUser,
      sort_field: params.sortField,
      sort_order: params.sortOrder,
    },
  });

  if (res.json_ok && res.data) {
    return {
      status: 'Success',
      total: res.data.total,
      list: convertSolutionComparisonList(res.data.list),
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    total: 0,
    list: [],
  };
}

export async function deleteSolutionComparison(
  comparisonId: string,
): Promise<deleteSolutionComparisonResponse> {
  if (!comparisonId) {
    return {
      status: 'Fail',
      errorMessage: '对比ID不能为空',
    };
  }

  const res: any = await postRequest({
    code: 'supply/deleteSolutionComparison',
    params: {
      comparison_id: comparisonId,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}
