/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeObjectId } from '@waterdesk/data/object-item';
import {
  QuerySolutionShareList,
  SolutionListItem,
  SolutionStatus,
  SolutionType,
} from '@waterdesk/data/solution';
import {
  getBaseSolutionInfoData,
  getSolutionDetail,
  SolutionBaseInfo,
  SolutionDetail,
  SolutionModifyItem,
  SolutionModifyType,
  UpdateSolutionShareListParams,
} from '@waterdesk/data/solution-detail';
import dayjs, { Dayjs } from 'dayjs';
import { PageParams, sorterMethodMap } from './api/api-request';
import {
  APIResponse,
  DefaultBackendResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest, postRequestByView } from './request';

export interface GetSolutionListResponse extends APIResponse {
  list: SolutionListItem[];
  total: number;
}

export interface GetAllSolutionListResponse extends APIResponse {
  list: SolutionListItem[];
  total: number;
}

export interface GetSolutionInfoResponse extends APIResponse {
  detail?: SolutionDetail;
  baseInfo?: SolutionBaseInfo;
}

export interface GetSolutionBaseInfoResponse extends APIResponse {
  baseInfo?: SolutionBaseInfo & { id: string };
  detail?: SolutionDetail;
}

export interface GetSolutionStateInfoResponse extends APIResponse {
  data: {
    status: string;
    statusMessage: string;
  };
}

export interface GetSolutionModifyListResponse extends APIResponse {
  list?: SolutionModifyItem[];
}

export interface QuerySolutionShareListResponseInBackend {
  DEPARTMENT: string[];
  USER: string[];
}

export interface QuerySolutionShareListAPIResponse extends APIResponse {
  data?: QuerySolutionShareList;
}

interface UpdateSolutionShareListRequestInBackend {
  /** 方案id */
  solution_id: string;
  /** 方案共享权限清单(json) */
  solution_share_json: string;
}

export interface SolutionBranch {
  solutionId: string;
  solutionGuid: string;
  solutionTitle: string;
  solutionStatus: SolutionStatus;
  solutionFromId: string;
  solutionStatusMessage: string;
  isSolutionDelete: boolean;
  isReadOnly: boolean;
}

export interface GetSolutionBranchListResponse extends APIResponse {
  list: SolutionBranch[];
}

export interface GetSolutionIdByGuidResponse extends APIResponse {
  solutionId?: string;
}

export const formatSolutionItem = (list: any[]): SolutionListItem[] =>
  (list ?? [])?.map((data) => ({
    id: data?.solution_id ?? '',
    name: data?.solution_title ?? '',
    sourceType: data?.solution_from ?? '',
    sourceSolutionId: data?.solution_from_id ?? '',
    sourceName: '',
    createTime: data?.create_time ?? '',
    startTime: data?.start_time ?? '',
    endTime: data?.end_time ?? '',
    createUser: data?.solution_user_name ?? '',
    note: data?.solution_note ?? '',
    status: data?.solution_status ?? '',
    statusMessage: data?.solution_status_msg ?? '',
    duration: 0,
    interval: data?.calculate_step ?? 0,
    isShared: data.solution_share === 1,
    createByMyself: data.is_belong,
    department: data.department_name ?? '',
    solutionType: data.solution_type ?? SolutionType.COMMON,
    isReadOnly: data.is_read_only ?? false,
    solutionGuid: data?.solution_guid ?? '',
    eventsInfo: data?.events_info ?? '',
  }));

const sorterServerFieldMap: Record<string, string> = {
  createTime: 'create_time',
};

export const getSolutionList = async (
  params: PageParams & {
    solutionName?: string;
    solutionType?: SolutionType[];
    solutionMode?: string;
    solutionFrom?: string;
    userName?: string;
    departmentId?: string;
    /** @deduplicate use guid instead of id */
    solutionFromId?: string;
    solutionFromGuid?: string;
  },
): Promise<GetSolutionListResponse> => {
  const {
    pageSize,
    current,
    sorter,
    solutionName,
    solutionType,
    solutionMode,
    solutionFrom,
    userName,
    departmentId,
    solutionFromId,
    solutionFromGuid,
  } = params;

  const sortKey = sorter?.field;
  const sortMethod = sorter?.order;
  const isSort = sortKey && sortMethod;

  const res: any = await postRequest({
    code: 'supply/querySolutionNamesList',
    params: {
      pageSize,
      current,
      solution_title: solutionName,
      solution_type: solutionType?.join(','),
      solution_from: solutionFrom,
      solution_mode: solutionMode ?? 'Supply',
      user_name: userName,
      department_id: departmentId,
      solution_from_id: solutionFromId,
      solution_from_guid: solutionFromGuid,
      sort_field: isSort ? sorterServerFieldMap[sortKey] : undefined,
      sort_order: isSort ? sorterMethodMap[sortMethod] : undefined,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const total = res?.values?.count ?? 0;
        const data: SolutionListItem[] = formatSolutionItem(
          res?.values?.records,
        );
        resolve({
          status: 'Success',
          list: data,
          total,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
        total: 0,
        list: [],
      });
    }
  });
};

export const getBranchSolutionList = async (params: {
  current: number;
  pageSize: number;
  solutionName?: string;
  solutionType?: SolutionType[];
  solutionFrom?: string;
  solutionMode?: string;
  userName?: string;
  departmentId?: string;
}): Promise<GetSolutionListResponse> => {
  const {
    pageSize,
    current,
    solutionName,
    solutionType,
    solutionFrom,
    solutionMode,
    userName,
    departmentId,
  } = params;
  const res: any = await postRequest({
    code: 'supply/querySolutionGroupList',
    params: {
      pageSize,
      current,
      solution_title: solutionName,
      solution_type: solutionType?.join(','),
      solution_from: solutionFrom,
      solution_mode: solutionMode ?? 'Supply',
      user_name: userName,
      department_id: departmentId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const total = res?.values?.count ?? 0;
        const data: SolutionListItem[] = formatSolutionItem(
          res?.values?.records,
        );

        resolve({
          status: 'Success',
          list: data,
          total,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
        total: 0,
        list: [],
      });
    }
  });
};

export const getAllSolutionList = async (
  params: {
    current: number;
    pageSize: number;
  },
  formValues: {
    solutionName?: string;
    userName?: string;
    source?: string;
    rangeTime?: [Dayjs, Dayjs];
    departmentId?: string;
  },
): Promise<GetAllSolutionListResponse> => {
  const { pageSize, current } = params;
  const { solutionName, userName, source, rangeTime, departmentId } =
    formValues;
  const startTime = rangeTime?.[0].format('YYYY-MM-DD HH:mm:ss');
  const endTime = rangeTime?.[1].format('YYYY-MM-DD HH:mm:ss');
  const res: any = await postRequest({
    code: 'supply/queryAllSolutionNamesList',
    params: {
      pageSize,
      current,
      solution_title: solutionName,
      user_name: userName,
      solution_from: source,
      start_time: startTime,
      end_time: endTime,
      department_id: departmentId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const total = res?.values?.count ?? 0;
        const data: SolutionListItem[] = formatSolutionItem(
          res?.values?.records,
        );
        resolve({
          status: 'Success',
          list: data,
          total,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
        total: 0,
        list: [],
      });
    }
  });
};

export const deleteSolutionById = async (
  solutionId: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteSolution',
    params: {
      solution_id: solutionId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const getSolutionInfo = async (
  solutionId?: string,
  solutionGuid?: string,
): Promise<GetSolutionInfoResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSolutionSummary',
    params: {
      solution_id: solutionId,
      solution_guid: solutionGuid,
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values && res.info) {
        const solutionDetail = getSolutionDetail(res.values);
        const baseInfo = getBaseSolutionInfoData(res.info);
        resolve({
          status: 'Success',
          detail: solutionDetail,
          baseInfo,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const getSolutionSummary = async (
  solutionId: string,
): Promise<GetSolutionBaseInfoResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSolutionSummary',
    params: {
      solution_id: solutionId,
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.info) {
        const baseInfo = getBaseSolutionInfoData(res.info);
        const detail = getSolutionDetail(res.values);
        resolve({
          status: 'Success',
          baseInfo: {
            ...baseInfo,
            id: solutionId,
          },
          detail,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export async function getSolutionStateInfo(
  solutionId: string,
): Promise<GetSolutionStateInfoResponse> {
  const res: any = await postRequest({
    code: 'supply/getSolutionState',
    params: {
      solution_id: solutionId,
    },
  });
  if (res.json_ok && res.values) {
    return {
      status: 'Success',
      data: {
        status: res.values.solution_status ?? '',
        statusMessage: res.values.solution_status_msg ?? '',
      },
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    data: {
      status: '',
      statusMessage: '',
    },
  };
}

export async function getSolutionCalcQueueCount(): Promise<
  {
    data: {
      count: number;
    };
  } & APIResponse
> {
  const res: any = await postRequest({
    code: 'watergis/querySolutionCalcQueueCount',
  });
  if (res.json_ok) {
    return {
      status: 'Success',
      data: {
        count: res.count ?? 0,
      },
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    data: {
      count: 0,
    },
  };
}

export async function getSolutionStateAndQueueCount(
  solutionId: string,
): Promise<
  {
    data: {
      status: string;
      statusMessage: string;
      queueCount: number;
    };
  } & APIResponse
> {
  const [stateInfoRes, queueCountRes] = await Promise.all([
    getSolutionStateInfo(solutionId),
    getSolutionCalcQueueCount(),
  ]);

  return {
    status: 'Success',
    data: {
      status: stateInfoRes.data.status,
      statusMessage: stateInfoRes.data.statusMessage,
      queueCount: queueCountRes.data.count,
    },
  };
}

export const deleteNetworkElement = async (params: {
  otype: string;
  oname: string;
}): Promise<APIResponse> => {
  const res: any = await postRequestByView({
    code: 'watergis/removeElement',
    params: {
      otype: params.otype,
      oname: params.oname,
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const solutionAddJunction = async (params: {
  elements: (
    | {
        oname: string;
        otype: string;
        shape: string;
        split_pipe?: { oname: string; otype: string };
      }
    | {
        start_otype: string;
        start_oname: string;
        end_otype: string;
        end_oname: string;
      }
  )[];
}): Promise<APIResponse> => {
  const res: any = await postRequestByView({
    code: 'watergis/createElementList',
    params: {
      elements: JSON.stringify(params.elements),
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

function getShapeMap(
  shapes: { oname: string; otype: string; shape: string }[],
): Map<string, string> {
  const shapeMap = new Map();
  shapes.forEach((item) => {
    shapeMap.set(makeObjectId(item.otype, item.oname), item.shape);
  });
  return shapeMap;
}

export const getSolutionModifyList = async (
  time?: string,
): Promise<GetSolutionModifyListResponse> => {
  const res: any = await postRequestByView({
    code: 'watergis/getElementModifyList',
    params: {
      time: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        const shapeMap = getShapeMap(
          Array.isArray(res.shapes) ? res.shapes : [],
        );
        const list: SolutionModifyItem[] = Array.isArray(res.values)
          ? res.values
              .map((item: any) => ({
                id: item.rec_id,
                time: item.otime,
                type: item.modify_type,
                otype: item.otype,
                oname: item.oname,
                vprop: item.vprop,
                originValue: item.org_value,
                value: item.new_value,
                shape: shapeMap.get(makeObjectId(item.otype, item.oname)),
                groupId: item.group_id,
                groupTime: item.group_time,
                isCurrent: !!Number(item.is_current),
              }))
              .filter(
                (item: SolutionModifyItem) =>
                  item.type === SolutionModifyType.CREATE ||
                  item.type === SolutionModifyType.DELETE ||
                  item.type === SolutionModifyType.SPLIT ||
                  item.type === SolutionModifyType.REF_CREATE ||
                  item.type === SolutionModifyType.VPROP,
              )
          : [];
        resolve({
          status: 'Success',
          list,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const updateSolutionStatusMsg = async (
  solutionId: string,
  message: string,
  status: SolutionStatus,
): Promise<APIResponse> => {
  const res: any = await postRequestByView({
    code: 'supply/updateSolutionStatusMsg',
    params: {
      solution_id: solutionId,
      solution_status_msg: message,
      solution_status: status,
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

/** 查询方案分享列表 */
export const querySolutionShareList = async (
  solutionId: string,
): Promise<QuerySolutionShareListAPIResponse> => {
  const res = (await postRequest({
    code: 'supply/querySolutionShareList',
    params: {
      solution_id: solutionId,
    },
  })) as DefaultBackendResponse<QuerySolutionShareListResponseInBackend>;

  if (res.json_ok) {
    return {
      status: 'Success',
      data: {
        department: res.values?.DEPARTMENT ?? [],
        user: res.values?.USER ?? [],
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

/** 更新方案分享列表 */
export const updateSolutionShareList = withApiResponseHandler(
  async (params: UpdateSolutionShareListParams) => {
    const newParams: UpdateSolutionShareListRequestInBackend = {
      solution_id: params.solutionId ?? '',
      solution_share_json: JSON.stringify({
        DEPARTMENT: params?.solutionShareJson?.department ?? [],
        USER: params?.solutionShareJson?.user ?? [],
      }),
    };
    const res: any = await postRequest({
      code: 'supply/updateSolutionShareList',
      params: newParams,
    });

    return res;
  },
);

/** 执行撤销/重做操作的参数接口 */
export interface ExecuteGroupOperationParams {
  /** 操作组id */
  groupId: string;
  /** 操作类型(undo/redo) */
  operationType: 'undo' | 'redo';
  /** 当前时间,不填为当前机器时间 */
  time?: string;
}

/** 执行撤销/重做操作 */
export const executeGroupOperation = withApiResponseHandler(
  async (params: ExecuteGroupOperationParams): Promise<APIResponse> => {
    const res = await postRequestByView({
      code: 'watergis/executeGroupOperation',
      params: {
        group_id: params.groupId,
        operation_type: params.operationType,
        time: params.time,
      },
    });

    return res;
  },
);

export const getSolutionBranchList = async (
  solutionId: string,
): Promise<GetSolutionBranchListResponse> => {
  const res = await postRequest({
    code: 'supply/querySolutionBranchList',
    params: {
      solution_id: solutionId,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      list: (res.values ?? []).map((item: any) => ({
        solutionId: item.solution_id,
        solutionGuid: item.solution_guid,
        solutionTitle: item.solution_title,
        solutionStatus: item.solution_status,
        solutionFromId: item.solution_from_id,
        solutionStatusMessage: item.solution_status_msg,
        isSolutionDelete: item.is_belong,
        isReadOnly: item.is_read_only,
      })),
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
  };
};

export const getSolutionIdByGuid = async (
  solutionGuid: string,
): Promise<GetSolutionIdByGuidResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSolutionIdByGuid',
    params: {
      solution_guid: solutionGuid,
    },
  });

  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          solutionId: res.values?.solution_id,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};
