/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  GetSystemLogParams,
  SystemLogDataItem,
} from '@waterdesk/data/system-log';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSystemLogResponse extends APIResponse {
  systemLogData?: {
    data: SystemLogDataItem[];
    total: number;
  };
}

export const getSystemLog = async (
  params: GetSystemLogParams,
): Promise<GetSystemLogResponse> => {
  const res: any = await postRequest({
    code: 'portal/querySystemLogPage',
    params: {
      page: params.page,
      rows: params.pageSize,
      start_time: params.startDate
        ? dayjs(params.startDate).format('YYYY-MM-DD 00:00:00')
        : undefined,
      end_time: params.endDate
        ? dayjs(params.endDate).format('YYYY-MM-DD 23:59:59')
        : undefined,
      key_word: params.keyWord,
      operation_type: params.operationType,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.rows) {
        const data =
          res.rows?.map((item: any) => ({
            addressId: item.ADDRESS_ID ?? '',
            applicationName: item.APPLICATION_NAME ?? '',
            applyId: item.APPLY_ID ?? '',
            operationDesc: item.OPERATION_DESC ?? '',
            // string as YYYY-DD-MM hh:mm:ss
            operationTime: item.OPERATION_TIME ?? '',
            operationType: item.OPERATION_TYPE ?? '',
            recordId: item.RECORD_ID ?? '',
            sourceDesc: item.SOURCE_DESC ?? '',
            userId: item.USER_ID ?? '',
            userName: item.USER_NAME ?? '',
          })) || [];
        resolve({
          status: 'Success',
          systemLogData: {
            data,
            total: res.total || 0,
          },
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
