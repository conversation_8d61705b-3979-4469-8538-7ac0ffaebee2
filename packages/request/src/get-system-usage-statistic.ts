/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  StatisticsLoginData,
  StatisticsLoginDataItem,
} from '@waterdesk/data/system-usage-statistics';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

interface LoginStatisticListRequestInBackEnd {
  start_time: string;
  end_time: string;
  department_id?: string;
}

export const getLoginStatisticList = async (params: {
  selectedMonth: string;
  department?: string;
}): Promise<APIResponse & { data: StatisticsLoginData }> => {
  const { selectedMonth, department } = params;
  const request: LoginStatisticListRequestInBackEnd = {
    start_time: dayjs(selectedMonth)
      .startOf('month')
      .format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(selectedMonth).endOf('month').format('YYYY-MM-DD 23:59:59'),
    department_id: department,
  };

  const res: any = await postRequest({
    code: 'portal/querySystemLoginSituation',
    params: request,
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      data: {
        loginTotalCount: res?.values?.LOGIN_COUNT,
        loginUserCount: res?.values?.LOGIN_USER_COUNT,
        data:
          res?.values?.data?.map(
            (item: any): StatisticsLoginDataItem => ({
              date: dayjs(item?.DATE)?.format('YYYY-MM-DD'),
              loginTotalCount: item?.LOGIN_COUNT,
              loginUserCount: item?.LOGIN_USER_COUNT,
            }),
          ) ?? [],
      },
    };
  }

  return {
    status: 'Fail',
    data: {
      loginTotalCount: 0,
      loginUserCount: 0,
      data: [],
    },
  };
};

export const getUsageStatisticList = async (params: {
  selectedMonth: string;
}) => {
  const { selectedMonth } = params;
  const request = {
    start_time: dayjs(selectedMonth)
      .startOf('month')
      .format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(selectedMonth).endOf('month').format('YYYY-MM-DD 23:59:59'),
  };

  const res: any = await postRequest({
    code: 'portal/querySystemUseSituation',
    params: request,
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      values: {
        usageSituations: {
          detailedScheme: res?.values?.use_situation?.COMPLETE_SOLUTION_COUNT,
          quickScheme: res?.values?.use_situation?.SIMPLE_SOLUTION_COUNT,
          warnProcessTimes: res?.values?.use_situation?.WARN_DEAL_COUNT,
          smsSendTimes: res?.values?.use_situation?.MESSAGE_SEND_COUNT,
        },
        dailyUsageSituations: res?.values?.daily_use_situation?.map(
          (item: any) => ({
            date: dayjs(item?.DATE)?.format('YYYY-MM-DD'),
            detailedScheme: item?.COMPLETE_SOLUTION_COUNT,
            quickScheme: item?.SIMPLE_SOLUTION_COUNT,
            warnProcessTimes: item?.WARN_DEAL_COUNT,
            smsSendTimes: item?.MESSAGE_SEND_COUNT,
          }),
        ),
      },
    };
  }

  return {
    status: 'Fail',
    values: {
      usageSituations: {
        detailedScheme: 0,
        quickScheme: 0,
        warnProcessTimes: 0,
        smsSendTimes: 0,
      },
      dailyUsageSituations: [],
    },
  };
};

export const getWarnStatisticList = async (params: {
  selectedMonth: string;
}) => {
  const { selectedMonth } = params;
  const request = {
    start_time: dayjs(selectedMonth)
      .startOf('month')
      .format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(selectedMonth).endOf('month').format('YYYY-MM-DD 23:59:59'),
  };

  const res: any = await postRequest({
    code: 'portal/querySystemWarnSituation',
    params: request,
  });

  if (res?.json_ok) {
    return {
      status: 'Success',
      values: {
        warnSituations: res?.values?.warn_situation,
        dailyWarnSituations: res?.values?.daily_warn_situation?.map(
          (item: any) => ({
            date: dayjs(item?.DATE)?.format('YYYY-MM-DD'),
            resolvedCount: item?.WARN_DEAL_COUNT,
            ...item,
          }),
        ),
      },
    };
  }

  return {
    status: 'Fail',
    values: {
      warnSituations: {},
    },
  };
};
