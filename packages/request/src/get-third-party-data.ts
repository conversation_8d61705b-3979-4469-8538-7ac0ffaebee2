/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ThirdPartyUserInfo,
  ThirdPartyVehicleInfo,
} from '@waterdesk/data/third-party-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

/**
 * 获取第三方用户信息
 * @param type 第三方系统类型
 * @returns 用户信息列表
 */
export async function getThirdPartyPersonnel(): Promise<
  APIResponse & { data: ThirdPartyUserInfo[] }
> {
  const res: any = await postRequest({
    code: 'thirdParty/api/personnel',
  });
  try {
    if (res.json_ok) {
      return {
        status: 'Success',
        errorMessage: '',
        data: res.data.list ?? [],
      };
    }
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      data: [],
    };
  } catch (error) {
    console.error('获取第三方用户信息失败:', error);
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      data: [],
    };
  }
}

/**
 * 获取第三方车辆信息
 * @param type 第三方系统类型
 * @returns 车辆信息列表
 */
export async function getThirdPartyVehicles(): Promise<
  APIResponse & { data: ThirdPartyVehicleInfo[] }
> {
  const res: any = await postRequest({
    code: 'thirdParty/api/vehicles',
  });
  try {
    if (res.json_ok) {
      return {
        status: 'Success',
        errorMessage: '',
        data: res.data.list ?? [],
      };
    }
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      data: [],
    };
  } catch (error) {
    console.error('获取第三方车辆信息失败:', error);
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      data: [],
    };
  }
}
