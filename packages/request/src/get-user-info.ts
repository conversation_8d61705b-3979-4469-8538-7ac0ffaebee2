/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MENU_TYPE, MENU_TYPE_PAGE, MenuInfo } from '@waterdesk/data/menu-data';
import { APIResponse } from './api/api-response';
import { UserInfoType } from './login';
import { postRequest } from './request';

export interface GetLoginUserInfoResponse extends APIResponse {
  // todo: define GlobalConfig
  globalConfig: any;
  permissionList?: MenuInfo[];
  userInfo?: UserInfoType;
  applicationId?: string;
  userDataAccess: string[];
}

export const getLoginUserInfo = async (params: {
  appId: string;
}): Promise<GetLoginUserInfoResponse> => {
  const res: any = await postRequest({
    code: 'portal/getLoginUserInfo',
    params: {
      application_id: params.appId,
    },
  });
  const resError: GetLoginUserInfoResponse = {
    status: 'Fail',
    errorMessage: res.json_msg,
    globalConfig: {} as any,
    userDataAccess: [],
  };

  if (!res.info) {
    return resError;
  }

  if (res.json_ok) {
    const menuList: MenuInfo[] = Array.isArray(res.menu_list)
      ? res.menu_list.map((item: any): MenuInfo => {
          const typeName =
            MENU_TYPE.find((enumItem) => enumItem.value === item.menu_type)
              ?.label || '';
          const dataMode = item.url_args ? item.url_args.data_mode : '';
          return {
            functionUrl: item.func_url || '',
            code: item.menu_code || '',
            functionKey: item.func_key || '',
            homepage: item.menu_homepage === 1,
            icon: item.menu_icon || '',
            id: item.menu_id || '',
            name: item.menu_name || '',
            parentId: item.menu_parent_id || '',
            showType: item.menu_showtype || '',
            type: item.menu_type || '',
            typeName,
            url: (item.func_url || item.menu_url) ?? '',
            dataMode: item?.menu_type === MENU_TYPE_PAGE ? dataMode : '',
          };
        })
      : [];
    const userInfo: UserInfoType = {
      userEmail: res.info?.USER_EMAIL,
      userName: res.info?.user_name,
      userPhone: res.info?.user_phone,
      userSex: res.info?.user_sex,
      departmentId: res.info?.department_id,
      departmentName: res.info?.department_name,
      externalUserInfo: res.info?.externalUserInfo,
    };
    return {
      status: 'Success',
      permissionList: menuList,
      userInfo,
      globalConfig: res.ui_config,
      applicationId: res.application_id,
      userDataAccess: res.data_access ?? [],
    };
  }
  return resError;
};
