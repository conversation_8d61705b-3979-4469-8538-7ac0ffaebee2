/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ValveAnalysisConfig } from '@waterdesk/data/app-config';
import {
  PLANT_TYPE,
  PUMPSTATION_TYPE,
} from '@waterdesk/data/const/system-const';
import Database from '@waterdesk/data/database';
import Device from '@waterdesk/data/device';
import { HighlightObject, ImpactedDma } from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import {
  AnalysisObjects,
  AnalysisResultData,
  formatAnalysisResultData,
  PlantStationInfo,
  QueryParams,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { AdvanceSettingType } from '@waterdesk/data/quick-analysis/solution-analysis-data';
import { HighlightStyleType } from '@waterdesk/data/style-config';
import { v4 as uuidv4 } from 'uuid';
import { APIResponse } from './api/api-response';
import { GetAsyncTaskStatusResponse } from './get-async-task';
import { downloadRequest, postRequest, postRequestByView } from './request';

export interface GetAnalysisDataResponse extends APIResponse {
  taskId?: string;
}
export interface GetWaterMeterListByDMAResponse extends APIResponse {
  data?: { [index: string]: WaterMeterInfo[] };
}

export interface GetAnalysisDataAsyncResponse
  extends GetAsyncTaskStatusResponse<AnalysisResultData> {}

export function formatValveAnalysisData(
  data: any[],
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): HighlightObject[] | ImpactedDma[] {
  const highlightObjects: HighlightObject[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shapeType: getShapeType(item.shape),
    });
  });
  return highlightObjects;
}

export function formatAnalysisData(
  values: any,
  valveAnalysisConfig: ValveAnalysisConfig,
  db: Database,
): AnalysisResultData {
  const responseData: AnalysisResultData = {
    waterOutageArea: [],
    waterOutageAreaUser: [],
    waterOutageAreaUserCount: 0,
    closedPipeline: [],
    valveList: [],
    affectedArea: [],
    affectedAreaUser: [],
    affectedAreaUserCount: 0,
    affectedPipeline: [],
    reversePipeline: [],
    closeNode: [],
    affectedNode: [],
    plantStation: {
      plants: [],
      pumpStations: [],
    },
    waterReverseAffectedArea: [],
    waterReverseAffectedAreaUser: [],
    waterReverseAffectedAreaUserCount: 0,
    energyConsumption: [],
  };
  responseData.waterOutageArea = formatAnalysisResultData(
    values?.close_dma3 ?? [],
    valveAnalysisConfig.waterOutageArea,
    db,
  );
  responseData.waterOutageAreaUser = formatAnalysisResultData(
    values?.close_watermeter ?? [],
    valveAnalysisConfig.waterOutageAreaUser,
    db,
  );
  responseData.waterOutageAreaUserCount = values?.close_watermeter_count ?? 0;
  responseData.closedPipeline = formatAnalysisResultData(
    values?.close_links ?? [],
    valveAnalysisConfig.closedPipeline,
    db,
  );
  responseData.valveList = formatAnalysisResultData(
    [...(values?.close_valves ?? [])] as [],
    valveAnalysisConfig.valveList,
    db,
  );
  responseData.affectedArea = formatAnalysisResultData(
    values?.down_dma3 ?? [],
    valveAnalysisConfig.affectedArea,
    db,
  );
  responseData.affectedAreaUser = formatAnalysisResultData(
    values?.down_watermeter ?? [],
    valveAnalysisConfig.affectedAreaUser,
    db,
  );
  responseData.affectedAreaUserCount = values?.down_watermeter_count ?? 0;
  responseData.affectedPipeline = formatAnalysisResultData(
    values?.effect_links ?? values?.back_links ?? [],
    valveAnalysisConfig.affectedPipeline,
    db,
  );
  responseData.reversePipeline = formatAnalysisResultData(
    values?.reverse_links ?? [],
    valveAnalysisConfig.reversePipeline,
    db,
  );
  // closeNode and affectedNode not config, otype get from values.close_nodes item
  responseData.closeNode = formatAnalysisResultData(
    values?.close_nodes ?? [],
    valveAnalysisConfig.waterOutageAreaUser,
    db,
  );
  responseData.affectedNode = formatAnalysisResultData(
    values?.down_nodes ?? [],
    valveAnalysisConfig.affectedAreaUser,
    db,
  );

  responseData.waterReverseAffectedArea = formatAnalysisResultData(
    values?.reverse_dma3 ?? [],
    valveAnalysisConfig.waterReverseAffectedArea,
    db,
  );

  responseData.waterReverseAffectedAreaUser = formatAnalysisResultData(
    values?.reverse_watermeter ?? [],
    valveAnalysisConfig.waterReverseAffectedAreaUser,
    db,
  );
  responseData.waterReverseAffectedAreaUserCount =
    values?.reverse_watermeter_count ?? 0;
  if (values.fact_values) {
    const plants: PlantStationInfo[][] = [];
    const pumpStations: PlantStationInfo[][] = [];
    Object.entries(values.fact_values).forEach((factoryStation) => {
      const [pname, value]: [string, any] = factoryStation;
      const plantDevice = db.getDeviceById(makeObjectId(PLANT_TYPE, pname));

      let pumpStationDevice: Device | undefined;
      if (!plantDevice) {
        pumpStationDevice = db.getDeviceById(
          makeObjectId(PUMPSTATION_TYPE, pname),
        );
      }

      const ptype = plantDevice ? PLANT_TYPE : PUMPSTATION_TYPE;
      const list: PlantStationInfo[] = [];
      value.forEach((item: any) => {
        const unitFormat = db.getUnitFormat(item.otype, item.vprop);
        const formatItem = {
          id: uuidv4(),
          ptype,
          pname,
          ptitle:
            (ptype === PLANT_TYPE
              ? plantDevice?.title
              : pumpStationDevice?.title) ?? '',
          otype: item.otype,
          vprop: item.vprop,
          title: item.title,
          oldValue: unitFormat
            ? unitFormat.getValue(item.old_value)
            : item.old_value,
          newValue: unitFormat
            ? unitFormat.getValue(item.new_value)
            : item.new_value,
          rowSpan: 0,
          unitFormat,
        };

        list.push(formatItem);
      });
      list.sort(
        (a, b) =>
          Math.abs(b.newValue - b.oldValue) - Math.abs(a.newValue - a.oldValue),
      );
      if (list.length > 0) {
        list[0].rowSpan = list.length;
      }
      if (ptype === PLANT_TYPE) {
        plants.push(list);
      } else {
        pumpStations.push(list);
      }
    });
    plants.sort(
      (a, b) =>
        b.reduce(
          (prev, curr) => prev + Math.abs(curr.newValue - curr.oldValue),
          0,
        ) -
        a.reduce(
          (prev, curr) => prev + Math.abs(curr.newValue - curr.oldValue),
          0,
        ),
    );
    pumpStations.sort(
      (a, b) =>
        b.reduce(
          (prev, curr) => prev + Math.abs(curr.newValue - curr.oldValue),
          0,
        ) -
        a.reduce(
          (prev, curr) => prev + Math.abs(curr.newValue - curr.oldValue),
          0,
        ),
    );
    responseData.plantStation = {
      plants: plants.flat(),
      pumpStations: pumpStations.flat(),
    };
  }
  return responseData;
}

export async function getAnalysisData(
  analysisObjects: AnalysisObjects,
  time?: string,
): Promise<GetAnalysisDataResponse> {
  const closeLinks = analysisObjects.closeList
    .filter((item) => item.oname)
    .map((item) => [item.otype, item.oname]);
  const openValves = analysisObjects.openList
    .filter((item) => item.oname)
    .map((item) => [item.otype, item.oname]);

  const res: any = await postRequestByView({
    code: 'supply/traverseValveClosed',
    params: {
      close_links: closeLinks.toString(),
      open_valves: openValves.toString(),
      time,
    },
  });

  return new Promise((resolve) => {
    if (res.json_ok) {
      try {
        if (res.no_model) {
          throw Error(`模型未计算`);
        }
        resolve({
          status: 'Success',
          taskId: res.task_id,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: res.json_msg,
    });
  });
}

/**
 *
 * @param json {"自定义key":{"otype": Array<oname>.join(',')}}
 * @param time
 * @returns
 */

export const downloadAnalysisResult = async (
  downloadFiles: QueryParams[],
  fileName: string,
  time?: string,
) => {
  const refJson: { [index: string]: {} } = {};
  downloadFiles.forEach(({ type, params }) => {
    refJson[type] = params;
  });
  const response: any = await downloadRequest({
    code: 'supply/exportWaterMeterList',
    params: {
      time,
      ref_json: JSON.stringify(refJson),
    },
  });
  try {
    const a = document.createElement('a');
    a.download = fileName;
    a.style.display = 'none';

    // 创建 Blob 对象的 URL
    const blobUrl = URL.createObjectURL(response);

    a.href = blobUrl;
    // 将链接添加到页面并触发点击
    document.body.appendChild(a);
    a.click();

    // 清理 Blob URL 和链接
    URL.revokeObjectURL(blobUrl);
    document.body.removeChild(a);
  } catch (err) {
    console.log(err);
  }
};

export async function getSolutionAnalysisResult(
  viewId: string,
  solutionId: string,
  comparId: string,
  solutionTime: string,
  comparTime: string,
  advanceSetting: Record<AdvanceSettingType, number>,
  unlimited?: boolean,
): Promise<GetAnalysisDataResponse> {
  const res: any = await postRequestByView({
    code: 'supply/compareSoluTimeResult',
    params: {
      view_id: viewId,
      time_1: solutionTime,
      time_2: comparTime,
      solu_1: solutionId,
      solu_2: comparId,
      bound_json: JSON.stringify({
        node_abs_press: advanceSetting.nodeAbsPress,
        node_low_press: advanceSetting.nodeLowPress,
        link_min_flow: advanceSetting.linkMinFlow,
        link_min_speed: advanceSetting.linkMinSpeed,
        link_min_diam: advanceSetting.linkMinDiam,
        link_min_change: advanceSetting.linkMinChange,
        link_max_change: advanceSetting.linkMaxChange,
        link_min_change_velocity: advanceSetting.linkMinChangeVelocity,
        link_max_change_velocity: advanceSetting.linkMaxChangeVelocity,
        link_reverse_min_velocity: advanceSetting.linkReverseMinVelocity,
      }),
      unlimited,
    },
  });

  return new Promise((resolve) => {
    if (res.json_ok) {
      try {
        if (res.no_model) {
          throw Error(`模型未计算`);
        }
        resolve({
          status: 'Success',
          taskId: res.task_id,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: res.json_msg,
    });
  });
}

export async function getWaterMeterListByDMA(
  params: QueryParams['params'],
  time?: string,
): Promise<GetWaterMeterListByDMAResponse> {
  const res: any = await postRequest({
    code: 'supply/getWaterMeterListByDma',
    params: {
      time,
      ref_json: JSON.stringify(params),
    },
  });
  return new Promise((resolve) => {
    if (res.json_ok) {
      try {
        const data: { [index: string]: WaterMeterInfo[] } = {};
        Object.entries(res.values ?? {}).forEach((item) => {
          const [key, value] = item;
          data[key] = Array.isArray(value)
            ? value.map((v) => ({
                key: uuidv4(),
                userName: v.NAME,
                waterMeterCode: v.ONAME,
                phone: (v.PAY_PHONE ??
                  v.OWNER_MOBILE_PHONE ??
                  v.OWNER_CONTACT_PHONE ??
                  v.SMS_PHONE ??
                  '') as string,
                address: v.ADDRESS ?? '',
              }))
            : [];
        });
        resolve({
          status: 'Success',
          data,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: res.json_msg,
    });
  });
}
