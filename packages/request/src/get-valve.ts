/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export const getValveList = async (params: {
  oname?: string;
  diameter?: number;
  current: number;
  pageSize: number;
}): Promise<
  DefaultListAPIResponse<{
    oname: string;
    diameter: number;
  }>
> => {
  const { oname, diameter, current, pageSize } = params;

  const res: any = await postRequest({
    code: 'supply/queryValveList',
    params: {
      oname,
      diameter,
      current,
      page_size: pageSize,
    },
  });

  if (res.json_ok) {
    const list = res.values?.list || [];
    return {
      status: 'Success',
      total: res.values?.total || 0,
      list,
    };
  }

  return {
    status: 'Success',
    total: res.values?.total || 0,
    list: [],
  };
};

export const fetchValveOpeningCurve = async (params: {
  otype: string;
  oname: string;
  time?: string;
}): Promise<
  {
    openingCurve: { x: number; y: number }[];
  } & APIResponse
> => {
  const { otype, oname, time } = params;

  const res: any = await postRequest({
    code: 'supply/queryValveOpeningCurve',
    params: {
      otype,
      oname,
      time,
    },
  });

  if (res.json_ok) {
    const list = res.values?.opening_curve || [];
    return {
      status: 'Success',
      openingCurve: list,
    };
  }

  return {
    status: 'Success',
    openingCurve: [],
  };
};
