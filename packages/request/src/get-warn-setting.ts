/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  WarningLevel,
  warningLevelMethodEnum,
} from '@waterdesk/data/warn/warn-level';
import {
  convertDisplayModeFromBackend,
  convertDisplayModeToBackend,
  QueryWarningShareList,
  QueryWarningShareListParams,
  UpdateWarningShareListParams,
  WarnSettingList,
  WarnSettingListParams,
  WarnSource,
} from '@waterdesk/data/warn-setting';
import {
  APIResponse,
  DefaultBackendResponse,
  DefaultListAPIResponse,
  withApiResponseHandler,
} from './api/api-response';
import { postRequest } from './request';

interface QueryWarningDefineListRequestInBackend {
  project_warning_id?: string;
}

interface QueryWarningDefineListResponseInBackend {
  // 警告定义id
  project_warning_id?: string;
  // 警告定义名称
  project_warning_title?: string;
  // 警告参数
  parameter?: string;
  // 警告类型
  type?: string;
  // 警告描述
  description?: string;
  // 展示方式
  display_mode?: string;
  // 工单发送部门
  work_order?: string;
  // 短信发送人
  message?: string;
  // 警告备注
  remark?: string;
  // 警告来源
  warn_source?: WarnSource;
  // 警告级别
  warning_level?: WarningLevel;
  // 报警短信模板
  msg_template?: string;
  // 报警企微模板
  wecom_template?: string;
  // 报警短信级别
  message_level?: WarningLevel;
  // 报警级别生效方法
  warning_level_method?: warningLevelMethodEnum;
}

interface UpdateWarningDefineRequestInBackend {
  // 警告定义id
  project_warning_id?: string;
  // 警告定义名称
  project_warning_title?: string;
  // 警告类型
  type?: string;
  // 警告参数
  parameter?: string;
  // 警告来源
  warn_source?: WarnSource;
  // 警告级别
  warning_level?: WarningLevel;
  // 展示方式
  display_mode?: string;
  // 警告描述
  description?: string;
  // 报警短信级别
  message_level?: WarningLevel;
  // 报警级别生效方法
  warning_level_method?: warningLevelMethodEnum;
}

interface QueryWarningShareListRequestInBackend {
  // 警告类型
  warn_type?: string;
  // 渠道类型
  channel_type?: string;
}

interface QueryWarningShareListResponseInBackend {
  DEPARTMENT: string[];
  USER: string[];
}

interface QueryWarningShareListAPIResponse extends APIResponse {
  data?: QueryWarningShareList;
}

interface UpdateWarningShareListRequestInBackend {
  // 警告类型
  warn_type?: string;
  // 警告分享权限清单(json)
  warning_share_json?: string;
  // 警告对象类型
  warn_otype?: string;
  // 警告对象ID
  warn_oname?: string;
  // 渠道类型
  channel_type?: string;
}

/** 查询警告类型设置列表 */
export const queryWarningDefineList = async (
  formData?: WarnSettingListParams,
): Promise<DefaultListAPIResponse<WarnSettingList>> => {
  const params: QueryWarningDefineListRequestInBackend = {
    project_warning_id: formData?.projectWarningId,
  };

  const res = (await postRequest({
    code: 'watergis/queryWarningDefineList',
    params,
  })) as DefaultBackendResponse<QueryWarningDefineListResponseInBackend[]>;

  if (res?.json_ok) {
    const list =
      res?.values?.map((i) => ({
        projectWarningId: i.project_warning_id ?? '',
        projectWarningTitle: i.project_warning_title ?? '',
        parameter: i.parameter ?? '',
        type: i.type ?? '',
        description: i.description ?? '',
        displayMode: convertDisplayModeFromBackend(i.display_mode),
        workOrder: i.work_order ?? '',
        message: i.message ?? '',
        remark: i.remark ?? '',
        warnSource: i.warn_source,
        warningLevel: i.warning_level,
        msgTemplate: i.msg_template ?? '',
        wecomTemplate: i.wecom_template ?? '',
        msgLevel: i.message_level,
        warningLevelMethod: i.warning_level_method,
      })) ?? [];

    return {
      total: Number(res?.values?.length) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

/** 更新警告类型设置 */
export const updateWarningDefine = withApiResponseHandler(
  async (formData?: WarnSettingList) => {
    const params: UpdateWarningDefineRequestInBackend = {
      project_warning_id: formData?.projectWarningId,
      project_warning_title: formData?.projectWarningTitle,
      type: formData?.type,
      parameter: formData?.parameter,
      warn_source: formData?.warnSource,
      warning_level: formData?.warningLevel,
      display_mode: convertDisplayModeToBackend(formData?.displayMode),
      description: formData?.description,
      message_level: formData?.msgLevel,
      warning_level_method: formData?.warningLevelMethod,
    };

    const res: any = await postRequest({
      code: 'watergis/updateWarningDefine',
      params,
    });

    return res;
  },
);

/** 查询警告分享权限列表 */
export const queryWarningShareList = async (
  formData?: QueryWarningShareListParams,
): Promise<QueryWarningShareListAPIResponse> => {
  const params: QueryWarningShareListRequestInBackend = {
    warn_type: formData?.warnType,
    channel_type: formData?.channelType,
  };

  const res = (await postRequest({
    code: 'watergis/queryWarningShareList',
    params,
  })) as DefaultBackendResponse<QueryWarningShareListResponseInBackend>;

  if (res?.json_ok) {
    return {
      status: 'Success',
      data: {
        department: res.values?.DEPARTMENT ?? [],
        user: res.values?.USER ?? [],
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

/** 更新警告分享权限列表 */
export const updateWarningShareList = withApiResponseHandler(
  async (formData?: UpdateWarningShareListParams) => {
    const newParams: UpdateWarningShareListRequestInBackend = {
      warn_type: formData?.warnType,
      channel_type: formData?.channelType,
      warning_share_json: JSON.stringify({
        DEPARTMENT: formData?.warningShareInfo?.department ?? [],
        USER: formData?.warningShareInfo?.user ?? [],
      }),
    };

    const res: any = await postRequest({
      code: 'watergis/updateWarningShareList',
      params: newParams,
    });

    return res;
  },
);
