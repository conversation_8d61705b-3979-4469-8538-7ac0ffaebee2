/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { makeId, makeObjectId } from '@waterdesk/data/object-item';
import {
  ChartWarnInfo,
  formatWarnDetailItem,
  getDurationDate,
  getDurationTime,
  WarnConfirmStatus,
  WarnDetail,
  WarnInfoItem,
  WarnInfoList,
  WarnPrimaryType,
  WarnSecondType,
  WarnStatusCounts,
} from '@waterdesk/data/warn';
import { WarnDisplayMode, WarnSettingList } from '@waterdesk/data/warn-setting';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetWarnListParams {
  startTime: string;
  endTime: string;
  type?: WarnPrimaryType | WarnPrimaryType[];
  indicatorType?: string;
  indicatorName?: string;
  vprop?: string;
  deviceType?: string;
  deviceName?: string;
  warnType?: string;
  warnLevel?: number;
  orderId?: string;
  warnSource?: string;
  deviceTitle?: string;
  warnTypeList?: { [key: string]: WarnSettingList };
  warnConfirmStatus?: WarnConfirmStatus;
  classId?: string;
  description?: string;
}

interface GetWarnListOfChartParams {
  startTime: string;
  endTime: string;
  type?: WarnPrimaryType | WarnPrimaryType[];
  indicatorType: string;
  indicatorName: string;
  vprop: string;
}

export interface GetWarnListResponse extends APIResponse {
  list: WarnInfoList;
  total: number;
  warnStatusCounts: WarnStatusCounts;
}

export interface GetWarnInfoResponse extends APIResponse {
  data?: WarnInfoItem;
}

export interface GetWarnSecondTypeResponse extends APIResponse {
  list: WarnSecondType[];
  total: number;
}

export const getWarnShapeMap = (
  shapes: any,
): Map<
  string,
  { otype: string; oname: string; shape: string; title: string }
> => {
  const shapeMap = new Map();
  if (Array.isArray(shapes)) {
    shapes.forEach((shapeInfo: any) => {
      const id = makeObjectId(shapeInfo.otype, shapeInfo.oname);
      shapeMap.set(id, shapeInfo);
    });
  }
  return shapeMap;
};

const dataAccessFilter = (db: Database, item: any) => {
  if (item.details?.length === 1) {
    const deviceInfo = db.getDevice(
      item.details[0].ptype,
      item.details[0].pname,
    );
    if (deviceInfo) {
      return deviceInfo.dataAccess;
    }
  }
  return true;
};

export const formatWarnInfoItem = (
  item: any,
  db: Database,
  warnTypeList?: GetWarnListParams['warnTypeList'],
  noDetail?: boolean,
): WarnInfoItem | undefined => {
  if (!dataAccessFilter(db, item)) return undefined;
  const id = makeId(item.type, item.warn_type);
  let displayMode: WarnDisplayMode[] = [];
  if (warnTypeList) {
    const warnType = warnTypeList?.[id];
    displayMode = warnType?.displayMode ?? [];
  }
  let details: WarnDetail[] = [];
  if (!noDetail) {
    (item.details as any[]).forEach((detailsItem: any) => {
      const warnInfo = item;
      warnInfo.displayMode = displayMode;
      const detail = detailsItem;
      detail.warn_id = item.warn_id;
      const warnDetail = formatWarnDetailItem(detailsItem, db, warnInfo);
      if (warnDetail) details.push(warnDetail);
    });
    details = details
      .filter((item: WarnDetail) => item.ptype && item.pname)
      .sort(
        (itemA: WarnDetail, itemB: WarnDetail) =>
          dayjs(itemB.startTime).valueOf() - dayjs(itemA.startTime).valueOf(),
      );
  }
  const warnEndTime =
    item.end_status === 0
      ? dayjs().format('YYYY-MM-DD HH:mm:ss')
      : item.end_time;

  return {
    details,
    id: item.warn_id ?? '',
    secondType: item.warn_type ?? '',
    secondTypeName: item.title ?? '',
    primaryType: item.type ?? '',
    rank: Number(item.level),
    startTime: item.start_time ?? '',
    endTime: warnEndTime,
    createTime: item.create_time ?? '',
    duration:
      item.warn_type === WarnPrimaryType.ASSESSMENT
        ? getDurationDate(item.start_time, warnEndTime)
        : getDurationTime(item.start_time, warnEndTime),
    description: item.main_reason ?? '',
    confirmStatus: item.operation_type ?? WarnConfirmStatus.NOT_CONFIRM,
    workOrderStatus: item.process_status ?? '',
    eventId: item.event_id ?? '',
    eventName: item.event_name ?? '',
    remark: item.remark ?? '',
    dealWithRemark: item.operation_desc,
    dealWithTime: item.operation_time ?? '',
    operator: item.operator,
    shelveTime: item.shelve_time,
    source: item.warn_source,
    classId: item.class_id,
    storageTime: item.stime ?? '',
    operationList: item.warning_confirm_process?.map((item: any) => ({
      time: item.operation_time,
      type: item.operation_type,
      desc: item.operation_desc,
      operator: item.operator,
    })),
    orderId: item.order_id,
    orderCode: item.order_code,
    orderTitle: item.order_title,
    displayMode,
    endStatus: item.end_status,
  };
};

/**
 * 批量格式化告警信息，内部过滤掉不合适的数据，确保不会返回包含 undefined 的数组
 */
export const formatWarnInfoList = (
  items: any[],
  db: Database,
  warnTypeList?: GetWarnListParams['warnTypeList'],
  noDetail?: boolean,
): WarnInfoList => {
  const result: WarnInfoList = [];
  items.forEach((item) => {
    const warnInfo = formatWarnInfoItem(item, db, warnTypeList, noDetail);
    if (warnInfo) {
      result.push(warnInfo);
    }
  });
  return result;
};

export const getWarningByTimeOverlap = async (
  params: GetWarnListOfChartParams,
): Promise<
  {
    list: ChartWarnInfo[];
  } & APIResponse
> => {
  const { startTime, endTime, type, indicatorType, indicatorName, vprop } =
    params;
  const res: any = await postRequest({
    code: 'watergis/queryWarningByTimeOverlap',
    params: {
      start_time: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
      type_list: type?.toString(),
      otype: indicatorType,
      oname: indicatorName,
      vprop,
    },
  });
  if (res.json_ok && Array.isArray(res.values.records)) {
    const warnList: ChartWarnInfo[] = res.values.records.map(
      (item: any): ChartWarnInfo => {
        const warnEndTime =
          item.end_status === 0
            ? dayjs().format('YYYY-MM-DD HH:mm:ss')
            : item.end_time;
        return {
          id: item.warn_id ?? '',
          secondTypeName: item.title ?? '',
          primaryType: item.type ?? '',
          rank: Number(item.level),
          startTime: item.start_time ?? '',
          endTime: warnEndTime,
          createTime: item.create_time ?? '',
          description: item.main_reason ?? '',
          remark: item.remark ?? '',
          endStatus: item.end_status,
        };
      },
    );
    return {
      status: 'Success',
      list: warnList,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
  };
};

const getWarnList = async (
  params: GetWarnListParams,
  db: Database,
  noDetail: boolean = false,
): Promise<GetWarnListResponse> => {
  const {
    startTime,
    endTime,
    type,
    indicatorType,
    indicatorName,
    vprop,
    deviceType,
    deviceName,
    warnType,
    warnLevel,
    orderId,
    warnSource,
    deviceTitle,
    warnTypeList,
    warnConfirmStatus,
    classId,
    description,
  } = params;
  const res: any = await postRequest({
    code: 'watergis/queryWarningList',
    params: {
      start_time: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
      type_list: type?.toString(),
      otype: indicatorType,
      oname: indicatorName,
      vprop,
      ptype: deviceType,
      pname: deviceName,
      project_warning_id_list: warnType?.toString(),
      warning_level: warnLevel,
      process_id: orderId,
      warn_source: warnSource,
      device_name: deviceTitle,
      operation_type: warnConfirmStatus,
      class_id: classId,
      description,
    },
  });
  const total = res?.values?.total_count ?? 0;
  const warnStatusCounts: WarnStatusCounts = {} as WarnStatusCounts;
  Object.keys(WarnConfirmStatus).forEach((key) => {
    warnStatusCounts[key as keyof typeof WarnConfirmStatus] =
      res?.values?.[key] ?? 0;
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && Array.isArray(res.values.records)) {
        const warnList = formatWarnInfoList(
          res.values.records,
          db,
          warnTypeList,
          noDetail,
        );
        resolve({
          status: 'Success',
          list: warnList,
          total,
          warnStatusCounts,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total,
          warnStatusCounts,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
        list: [],
        total,
        warnStatusCounts,
      });
    }
  });
};

export const getWarnListByDate = (
  params: GetWarnListParams,
  db: Database,
): Promise<GetWarnListResponse> =>
  getWarnList(
    {
      ...params,
      startTime: dayjs(params.startTime).format('YYYY-MM-DD 00:00:00'),
      endTime: dayjs(params.endTime).format('YYYY-MM-DD 23:59:59'),
    },
    db,
  );

export const getWarnListByDateTime = (
  params: GetWarnListParams,
  db: Database,
): Promise<GetWarnListResponse> =>
  getWarnList(
    {
      ...params,
      startTime: dayjs(params.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    db,
  );

export const getWarnListByDateTimeWithoutDetail = (
  params: GetWarnListParams,
  db: Database,
): Promise<GetWarnListResponse> =>
  getWarnList(
    {
      ...params,
      startTime: dayjs(params.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    db,
    true,
  );

export const confirmWarnInfo = async (params: {
  warnIdList: string[];
  confirmStatus: WarnConfirmStatus;
  note?: string;
  // shelve time as minutes
  shelveTime?: number;
  orderId?: string;
}): Promise<APIResponse> => {
  const { warnIdList, confirmStatus, orderId, note, shelveTime } = params;
  const res: any = await postRequest({
    code: 'watergis/updateWarningConfirmation',
    params: {
      warn_id_list: warnIdList.toString(),
      note: note ?? '',
      operation_type: confirmStatus,
      order_id: orderId,
      shelve_minutes: shelveTime,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const getWarnInfo = async (
  params: {
    warnId: string;
  },
  db: Database,
): Promise<GetWarnInfoResponse> => {
  const { warnId } = params;
  const res: any = await postRequest({
    code: 'watergis/queryWarningInfo',
    params: {
      warn_id: warnId,
    },
  });

  if (res.json_ok && res.values) {
    const data = formatWarnInfoItem(res.values, db);
    return {
      status: 'Success',
      data,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg as string,
    data: undefined,
  };
};
