/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { formatDayjsInObject } from '@waterdesk/data/string';
import { WaterOutageInfo } from '@waterdesk/data/water-outage-info-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetWaterOutageInfoListParams {
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  typeList?: string[]; // 停水类型列表
  title?: string; // 停水标题
  creator?: string; // 创建人
}

export interface GetWaterOutageInfoListResponse extends APIResponse {
  total: number;
  list: WaterOutageInfo[];
}

export interface GetNoticeInfoResponse extends APIResponse {
  noticeInfo?: WaterOutageInfo;
}

export const getWaterOutageInfoListByDate = async (
  params: GetWaterOutageInfoListParams,
): Promise<GetWaterOutageInfoListResponse> => {
  const requestParams = {
    start_time: dayjs(params?.startDate)
      .startOf('d')
      .format('YYYY-MM-DD HH:mm:ss'),
    end_time: dayjs(params?.endDate).endOf('d').format('YYYY-MM-DD HH:mm:ss'),
    event_type_list: params?.typeList?.toString(),
    event_title: params?.title,
    event_creator: params?.creator,
  };
  const formatDayjsParams = formatDayjsInObject(requestParams, true);
  const res = (await postRequest({
    code: 'supply/queryWaterNoticeList',
    params: formatDayjsParams,
  })) as any;

  if (res?.json_ok) {
    const values = Object.keys(res.values).map((key) => res.values[key]);

    return {
      status: 'Success',
      total: Number(values?.length) || 0,
      list: values?.map((item: any) => ({
        id: item?.event_number,
        oname: item?.event_number,
        otype: item?.event_type,
        title: item?.event_title,
        dmaList: item?.dma_list,
        waterMeterList: item?.watermeter_list,
        type: item?.event_type,
        creator: item?.event_creator,
        planStartTime: item?.plan_start_time,
        planEndTime: item?.plan_end_time,
        status: item?.event_status,
      })),
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: res?.json_msg,
  };
};

export interface SendWaterNoticeResponse extends APIResponse {
  taskId?: string;
}

export const sendWaterNotice = async (params: {
  id: string;
  systemName?: string;
}): Promise<SendWaterNoticeResponse> => {
  const res = (await postRequest({
    code: 'watergis/sendWaterNotice',
    params: {
      id: params.id,
      app_name: params.systemName,
    },
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      taskId: res.task_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

export const getWaterNoticeInfo = async (
  id: string,
): Promise<GetNoticeInfoResponse> => {
  const res = (await postRequest({
    code: 'supply/queryWaterNoticeInfo',
    params: {
      event_id: id,
    },
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      noticeInfo: {
        id: res.values?.event_id,
        oname: res.values?.event_number,
        otype: res.values?.event_type,
        title: res.values?.event_title,
        dmaList: res.values?.dma_list,
        waterMeterList: res.values?.watermeter_list,
        type: res.values?.event_type,
        creator: res.values?.event_creator,
        planStartTime: res.values?.plan_start_time,
        planEndTime: res.values?.plan_end_time,
        status: res.values?.event_status,
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};
