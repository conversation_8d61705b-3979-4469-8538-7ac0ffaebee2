/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { makeObjectId, splitObjectId } from '@waterdesk/data/object-item';
import { formatDayjsInObject } from '@waterdesk/data/string';
import {
  SampleList,
  WaterQualityList,
  WaterQualityReportList,
  WaterQualityReportListParams,
} from '@waterdesk/data/water-quality';
import { PageParams, SortMethod, sorterMethodMap } from './api/api-request';
import { APIResponse, DefaultBackendResponse } from './api/api-response';
import { postRequest } from './request';

interface QueryWaterQualityReportListRequest extends PageParams {
  start_time?: string;
  end_time?: string;
  station_name?: string;
  report_code?: string;
  sample_code?: string;
  parameter_name_list?: string;
  ptype?: string;
  pname?: string;
  qualified_list?: string;
  sort_by?: string;
  sort_method?: SortMethod;
}

interface QueryWaterQualityReportListResponseInBackend {
  report_id?: string;
  report_code?: string;
  report_time?: string;
  sample_code?: string;
  station_name?: string;
  station_address?: string;
  station_code?: string;
  client_name?: string;
  client_address?: string;
  client_contact?: string;
  client_phone?: string;
  create_by?: string;
  create_time?: string;
  update_by?: string;
  update_time?: string;
  ptype?: string;
  pname?: string;
  shape?: string;
  remark?: string;
}

interface QueryWaterQualitySampleListResponseInBackend {
  sample_code?: string;
  station_name?: string;
  report_code?: string;
  sample_time?: string;
  update_time?: string;
}

interface QueryWaterQualityListResponseInBackend {
  result_id: string | undefined;
  sample_code: string | undefined;
  sample_time: string | undefined;
  sample_type: string | undefined;
  sign_status: string | undefined;
  parameter_name: string | undefined;
  parameter_code: string | undefined;
  result: string | undefined;
  unit: string | undefined;
  evaluation_standard: string | undefined;
  limit_value: string | undefined;
  qualified: string | undefined;
  update_time: string | undefined;
  remark: string | undefined;
  report_code: string | undefined;
  station_name: string | undefined;
  ptype: string | undefined;
  pname: string | undefined;
  shape: string | undefined;
}

interface GetWaterQualityReportListResponse extends APIResponse {
  total: number;
  list: WaterQualityReportList[];
}

interface GetWaterQualityListResponse extends APIResponse {
  total: number;
  list: WaterQualityList[];
}

interface GetWaterQualitySampleListResponse extends APIResponse {
  total: number;
  list: SampleList[];
}

const formatParam = (param: string | undefined) => {
  if (typeof param === 'string') {
    return param.trim();
  }
  return param;
};

const convertWaterQualityReportList = (
  data: QueryWaterQualityReportListResponseInBackend[],
  db: Database,
) =>
  data.map((i) => {
    const device = db.getDeviceById(makeObjectId(i.ptype ?? '', i.pname ?? ''));
    return {
      reportId: i?.report_id,
      reportCode: i?.report_code,
      reportTime: i?.report_time,
      sampleCode: i?.sample_code,
      stationName: i?.station_name,
      stationAddress: i?.station_address,
      stationCode: i?.station_code,
      clientName: i?.client_name,
      clientAddress: i?.client_address,
      clientContact: i?.client_contact,
      clientPhone: i?.client_phone,
      createBy: i?.create_by,
      createTime: i?.create_time,
      updateBy: i?.update_by,
      updateTime: i?.update_time,
      ptype: i?.ptype,
      pname: i?.pname,
      deviceTitle: device?.title,
      shape: i?.shape,
      remark: i?.remark,
    };
  }) ?? [];

const convertWaterQualitySampleList = (
  data: QueryWaterQualitySampleListResponseInBackend[],
) =>
  data.map((i) => ({
    stationName: i.station_name,
    sampleCode: i.sample_code,
    sampleTime: i.sample_time,
    updateTime: i.update_time,
    reportCode: i.report_code,
  })) ?? [];

const convertWaterQualityList = (
  data: QueryWaterQualityListResponseInBackend[],
  db: Database,
) =>
  data.map((i) => {
    const device = db.getDeviceById(makeObjectId(i.ptype ?? '', i.pname ?? ''));
    return {
      resultId: i?.result_id,
      sampleCode: i?.sample_code,
      sampleTime: i?.sample_time,
      sampleType: i?.sample_type,
      signStatus: i?.sign_status,
      parameterName: i?.parameter_name,
      parameterCode: i?.parameter_code,
      result: i?.result,
      unit: i?.unit,
      evaluationStandard: i?.evaluation_standard,
      limitValue: i?.limit_value,
      qualified: i?.qualified,
      updateTime: i?.update_time,
      remark: i?.remark,
      reportCode: i?.report_code,
      stationName: i?.station_name,
      ptype: i?.ptype,
      pname: i?.pname,
      deviceTitle: device?.title,
      shape: i?.shape,
    };
  }) ?? [];

const waterQualitySorterServerFieldMap: Record<string, string> = {
  createTime: 'create_time',
};

/** 获取水质报告列表 */
export const getWaterQualityReportList = async (
  params: PageParams,
  db: Database,
  formData?: WaterQualityReportListParams,
): Promise<GetWaterQualityReportListResponse> => {
  let ptype = '';
  let pname = '';
  if (formData?.deviceId) {
    [ptype, pname] = splitObjectId(formData.deviceId);
  }

  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const requestParams: QueryWaterQualityReportListRequest = {
    current: params.current,
    pageSize: params.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    station_name: formatParam(formData?.stationName),
    report_code: formatParam(formData?.reportCode),
    sample_code: formatParam(formData?.sampleCode),
    ptype,
    pname,
    sort_by: isSort
      ? waterQualitySorterServerFieldMap[sortKey] || sortKey
      : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'supply/queryQualityAnalysisReportList',
    params: formatDayjsParams,
  })) as DefaultBackendResponse<{
    records: QueryWaterQualityReportListResponseInBackend[];
    total_count: number;
  }>;

  if (res?.json_ok) {
    const list = convertWaterQualityReportList(res?.values?.records ?? [], db);

    return {
      total: Number(res?.values?.total_count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

const waterQualityListSorterServerFieldMap: Record<string, string> = {
  sampleTime: 'sample_time',
};

/** 获取水质检测列表 */
export const getWaterQualityList = async (
  db: Database,
  params?: PageParams,
  formData?: WaterQualityReportListParams,
): Promise<GetWaterQualityListResponse> => {
  let ptype = '';
  let pname = '';
  if (formData?.deviceId) {
    [ptype, pname] = splitObjectId(formData.deviceId);
  }

  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const requestParams: QueryWaterQualityReportListRequest = {
    current: params?.current,
    pageSize: params?.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    qualified_list: formData?.qualified,
    station_name: formatParam(formData?.stationName),
    parameter_name_list: formatParam(formData?.parameterName),
    report_code: formatParam(formData?.reportCode),
    sample_code: formatParam(formData?.sampleCode),
    ptype,
    pname,
    sort_by: isSort
      ? waterQualityListSorterServerFieldMap[sortKey] || sortKey
      : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'supply/queryQualityAnalysisResultList',
    params: formatDayjsParams,
  })) as DefaultBackendResponse<{
    records: QueryWaterQualityListResponseInBackend[];
    total_count: number;
  }>;

  if (res?.json_ok) {
    const list = convertWaterQualityList(res?.values?.records ?? [], db);

    return {
      total: Number(res?.values?.total_count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

const waterSampleSorterServerFieldMap: Record<string, string> = {
  sampleTime: 'sample_time',
};

/** 获取检测样品列表 */
export const getWaterSampleList = async (
  params: PageParams,
  formData?: WaterQualityReportListParams,
): Promise<GetWaterQualitySampleListResponse> => {
  let ptype = '';
  let pname = '';
  if (formData?.deviceId) {
    [ptype, pname] = splitObjectId(formData.deviceId);
  }

  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;

  const requestParams: QueryWaterQualityReportListRequest = {
    current: params.current,
    pageSize: params.pageSize,
    start_time: formData?.startTime,
    end_time: formData?.endTime,
    station_name: formatParam(formData?.stationName),
    report_code: formatParam(formData?.reportCode),
    sample_code: formatParam(formData?.sampleCode),
    ptype,
    pname,
    sort_by: isSort
      ? waterSampleSorterServerFieldMap[sortKey] || sortKey
      : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };

  const formatDayjsParams = formatDayjsInObject(requestParams, true);

  const res = (await postRequest({
    code: 'supply/queryQualityAnalysisResultGroupList',
    params: formatDayjsParams,
  })) as DefaultBackendResponse<{
    records: QueryWaterQualitySampleListResponseInBackend[];
    total_count: number;
  }>;

  if (res?.json_ok) {
    const list = convertWaterQualitySampleList(res?.values?.records ?? []);

    return {
      total: Number(res?.values?.total_count) || 0,
      list,
      status: 'Success',
    };
  }

  return {
    total: 0,
    list: [],
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
