/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ConvertedWeatherData,
  convertWeatherData,
} from '@waterdesk/data/dispatch-log/weather';
import { WeatherData, WeatherInfo } from '@waterdesk/data/weather-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { getGroupObjectsTimeValues } from './get-group-time-values';

export interface GetWeatherDataResponse extends APIResponse {
  data?: WeatherData;
}

export type GetWeatherAndTemperatureResponse = {
  data: ConvertedWeatherData;
} & APIResponse;

export async function getWeatherData(
  time: string,
  viewId?: string,
): Promise<GetWeatherDataResponse> {
  const vprops: string[] = [
    'TEMPERATURE_MIN',
    'TEMPERATURE_MAX',
    'DAY_WEATHER',
  ];
  const valueGroupParams: {
    [key: string]: {
      otype?: string;
      oname?: string;
      vprop: string;
    };
  } = {};
  vprops.forEach((item) => {
    valueGroupParams[item] = {
      vprop: item,
    };
  });
  const endDate = dayjs(time).add(8, 'day').format('YYYY-MM-DD 00:00');
  const res = await getGroupObjectsTimeValues(
    time,
    endDate,
    'SYS_WEATHER',
    'WEATHER',
    valueGroupParams,
    viewId,
    true,
  );
  if (res.status === 'Success') {
    try {
      const weatherData: any = res.values;
      const forecast: WeatherInfo[] = [];
      const maxTemperatureData = weatherData.TEMPERATURE_MAX.values ?? [];
      const minTemperatureData = weatherData.TEMPERATURE_MIN.values ?? [];
      const weatherDatas = weatherData.DAY_WEATHER.values ?? [];
      const weatherDataLength =
        weatherDatas.length > 7 ? 7 : weatherDatas.length;
      for (let i = 0; i < weatherDataLength; i += 1) {
        forecast.push({
          maxTemperature: maxTemperatureData[i].value.toString(),
          minTemperature: minTemperatureData[i].value.toString(),
          weather: weatherDatas[i].value.toString(),
          weatherIconUrl: '',
          date: dayjs(time).add(i, 'day').format('YYYY-MM-DD'),
        });
      }

      if (forecast.length > 0) {
        const currentDay = forecast[0];

        return {
          status: 'Success',
          data: {
            currentDay,
            forecast,
          },
        };
      }
      return {
        status: 'Fail',
        errorMessage: res.errorMessage,
      };
    } catch {
      return {
        status: 'Fail',
        errorMessage: res.errorMessage,
      };
    }
  }

  return {
    status: 'Fail',
    errorMessage: res.errorMessage,
  };
}

export async function getWeatherAndTemperature(
  start: string,
  end: string,
): Promise<GetWeatherAndTemperatureResponse> {
  const res = await getGroupObjectsTimeValues(
    start,
    end,
    'SYS_WEATHER',
    'WEATHER',
    {
      DAY_WEATHER: { vprop: 'DAY_WEATHER' },
      TEMPERATURE_MAX: { vprop: 'TEMPERATURE_MAX' },
      TEMPERATURE_MIN: { vprop: 'TEMPERATURE_MIN' },
    },
    undefined,
    true,
  );

  if (res.status === 'Success') {
    return { status: 'Success', data: convertWeatherData(res.values) };
  }
  return { status: 'Fail', data: {} };
}
