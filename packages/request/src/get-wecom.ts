/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { getRequest } from './request';

export interface WeComConfig {
  appId: string;
  timestamp: string;
  nonceStr: string;
  ticket: string;
  signature: string;
}
export interface GetWeComConfigResponse extends APIResponse {
  weComConfig?: WeComConfig;
}

export default async function getWeComConfig(): Promise<GetWeComConfigResponse> {
  const url = encodeURIComponent(globalThis.location.href.split('#')[0]);
  const res: any = await getRequest({
    code: 'portal/getWeixinSign',
    params: {
      url,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const { timestamp, nonce, ticket, sign } = res.value;
        const weComConfig = {
          appId: res.value.app_id,
          timestamp,
          nonceStr: nonce,
          ticket,
          signature: sign,
        };
        resolve({
          status: 'Success',
          weComConfig,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
}
