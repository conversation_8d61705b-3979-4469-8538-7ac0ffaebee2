/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { formatDayjsInObject } from '@waterdesk/data/string';
import {
  AddWorkOrder,
  WorkOrder,
  WorkOrderRemindStatus,
  WorkOrderRepairDetail,
} from '@waterdesk/data/work-order';
import dayjs from 'dayjs';
import { PageParams, sorterMethodMap } from './api/api-request';
import { APIResponse } from './api/api-response';
import { postRequest, uploadRequest } from './request';

export interface GetWorkOrderListParams extends PageParams {
  startDate?: dayjs.Dayjs; // 开始日期
  endDate?: dayjs.Dayjs; // 结束日期
  statusList?: string[]; // 工单状态
  orderId?: string; // 工单Id
  orderCode?: string; // 工单编号
  title?: string; // 工单标题
  typeList?: string[]; // 工单类型
  levelList?: string[]; // 工单等级
  sourceList?: string[]; // 工单来源
}

export interface GetWorkOrderListResponse extends APIResponse {
  total: number;
  list: WorkOrder[];
}

export interface AddWorkOrderResponse extends APIResponse {
  orderId?: string;
}

export interface GetWorkOrderRepairDetailListParams extends PageParams {
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  code?: string; // 工单编号
  content?: string; // 工单内容
  ptype?: string;
  pname?: string;
  otype?: string;
}

export const convertWorkOrderList = (data: any): WorkOrder[] =>
  data?.map((item: any) => ({
    title: item?.title,
    shape: item?.shape,
    creator: item?.creator,
    createTime: item?.create_time,
    orderCreateTime: item?.order_create_time,
    id: item?.id,
    code: item?.code,
    status: item?.status,
    statusName: item?.status_name,
    type: item?.type,
    category: item?.category,
    content: item?.content,
    description: item?.description,
    address: item?.address,
    source: item?.source,
    level: item?.level,
    levelName: item?.level_name,
    department: item?.department,
    opinion: item?.opinion,
    acceptTime: item?.accept_time,
    arriveTime: item?.arrive_time,
    finishTime: item?.finish_time,
    stime: item?.stime,
    updateTime: item?.update_time,
    deviceList: item?.device_list,
    remark: item?.remark,
    warningList: item?.warning_list,
    innerId: item?.inner_id,
    eventName: item?.event_name,
    eventTitle: item?.event_title,
    remindState: item?.remind_state,
    communityName: item?.community_name,
  }));

export const getWorkOrderList = async (params: GetWorkOrderListParams) => {
  const requestParams = {
    order_id: params?.orderId,
  };

  const res = (await postRequest({
    code: 'watergis/queryWorkOrderList',
    params: requestParams,
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      total: Number(res?.values?.count) || 0,
      list: convertWorkOrderList(res?.values?.order_list),
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: res?.json_msg,
  };
};

const sorterServerFieldMap: Record<string, string> = {
  orderCreateTime: 'order_create_time',
  acceptTime: 'accept_time',
  finishTime: 'finish_time',
};

export const getWorkOrderListByDate = async (
  params: GetWorkOrderListParams,
): Promise<GetWorkOrderListResponse> => {
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;
  const requestParams = {
    current: params?.current,
    pageSize: params?.pageSize,
    start_time: dayjs(params?.startDate).format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(params?.endDate).format('YYYY-MM-DD 23:59:59'),
    order_code: params?.orderCode,
    status_list: params?.statusList?.toString(),
    title: params?.title,
    type_list: params?.typeList?.toString(),
    level_list: params?.levelList?.toString(),
    source_list: params?.sourceList?.toString(),
    sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };
  const formatDayjsParams = formatDayjsInObject(requestParams, true);
  const res = (await postRequest({
    code: 'watergis/queryWorkOrderList',
    params: formatDayjsParams,
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      total: Number(res?.values?.count) || 0,
      list: convertWorkOrderList(res?.values?.order_list),
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: res?.json_msg,
  };
};

export const addWorkOrder = async (
  workOrder: AddWorkOrder,
): Promise<AddWorkOrderResponse> => {
  const {
    deviceList,
    formValuesJson,
    deviceErrorTime,
    finishTimeLimit,
    picFile,
    ...rest
  } = workOrder;

  const formData = new FormData();

  if (picFile) {
    picFile.forEach((file) => {
      formData.append('pic_file', file);
    });
  }

  (Object.keys(rest) as (keyof typeof rest)[]).forEach((key) => {
    const value = rest[key];
    if (value !== undefined) {
      formData.append(key, value);
    }
  });

  formData.append('device_list', deviceList?.join(',') ?? '');
  formData.append('form_values_json', formValuesJson ?? '');
  formData.append('device_error_time', deviceErrorTime ?? '');
  formData.append('finish_time_limit', finishTimeLimit ?? '');

  const res = (await uploadRequest({
    code: 'supply/createWorkOrder',
    params: formData,
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      orderId: res.orderId,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
  };
};

const orderRepairSorterServerFieldMap: Record<string, string> = {
  orderCreateTime: 'order_create_time',
  errorTime: 'otime',
  finishTime: 'finish_time',
};

export const getWorkOrderRepairDetailListByDate = async (
  params: GetWorkOrderRepairDetailListParams,
) => {
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;
  const requestParams = {
    current: params?.current,
    pageSize: params?.pageSize,
    start_time: dayjs(params?.startDate).format('YYYY-MM-DD 00:00:00'),
    end_time: dayjs(params?.endDate).format('YYYY-MM-DD 23:59:59'),
    code: params?.code,
    content: params?.content,
    ptype: params?.ptype,
    pname: params?.pname,
    otype: params?.otype,
    sort_by: isSort
      ? orderRepairSorterServerFieldMap[sortKey] || sortKey
      : undefined,
    sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
  };
  const formatDayjsParams = formatDayjsInObject(requestParams, true);
  const res = (await postRequest({
    code: 'supply/queryWorkOrderRepairsDetails',
    params: formatDayjsParams,
  })) as any;

  if (res?.json_ok) {
    const list: WorkOrderRepairDetail[] = res?.values?.records.map(
      (item: any) => ({
        otype: item?.otype,
        oname: item?.oname,
        content: item?.content,
        errorTime: item?.otime,
        orderCreateTime: item?.order_create_time,
        finishTime: item?.finish_time,
        code: item?.code,
        creator: item?.creator,
      }),
    );
    return {
      status: 'Success',
      total: Number(res?.values?.total_count) || 0,
      list,
    };
  }

  return {
    status: 'Fail',
    total: 0,
    list: [],
    errorMessage: res?.json_msg,
  };
};

export const getWorkOrderCountByRemindState = async (
  remindStateList: WorkOrderRemindStatus[],
): Promise<{ count: number } & APIResponse> => {
  const res = (await postRequest({
    code: 'watergis/getWorkOrderCountByRemindState',
    params: {
      remind_state_list: remindStateList?.toString(),
    },
  })) as any;

  if (res?.json_ok) {
    return {
      status: 'Success',
      count: res?.values?.count ?? 0,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res?.json_msg,
    count: 0,
  };
};
