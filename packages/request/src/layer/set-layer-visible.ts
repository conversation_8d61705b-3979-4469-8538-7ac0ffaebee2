/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  LegendGroupData,
  LegendGroupDataCollection,
} from '@waterdesk/data/legend-data';
import { APIResponse } from '../api/api-response';
import { postRequestByView } from '../request';

export interface SetLayerVisibleResponse extends APIResponse {
  legendData?: LegendGroupData[];
}

export const setLayerVisible = async (params: {
  time: string;
  otypeName: string[];
  visible: boolean;
  viewId?: string;
}): Promise<SetLayerVisibleResponse> => {
  const { time, otypeName, visible, viewId } = params;
  const data: any = await postRequestByView({
    code: 'watergis/setLayerVisible',
    params: {
      is_visible: visible ? 1 : 0,
      otype_list: otypeName.join(),
      time,
      view_id: viewId,
    },
  });

  if (data.json_ok && data.grade_inst) {
    const legendData: LegendGroupDataCollection =
      new LegendGroupDataCollection();
    legendData.initialize(data.grade_inst, data.theme_styles);
    return {
      status: 'Success',
      legendData: legendData.legendDataCollection,
    };
  }
  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
};
