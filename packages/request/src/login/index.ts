/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MenuInfo } from '@waterdesk/data/menu-data';
import { registerSystemFeatures } from '@waterdesk/data/system-feature';
import { AES, enc } from 'crypto-js';
import JSEncrypt from 'jsencrypt';
import { APIResponse } from '../api/api-response';
import { postRequest } from '../request';

const PUBLIC_KEY =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8Qa+e4QjNlfSR55RGHeltMDqYJR1UCCpWzqY0xV9rgcGKXB3PzvBwn5PUUzPehGHHDXrpU4/gmcDZCjJly9hoI9NPKtjhi3bRFV7aHX0B4ms0S2ImYKtdRR7uSQfJPQFwr6fVOCs2bcPcJMYmSuJZXRKO/Fr7ZD5Uhlkcg5KGzQIDAQAB';

export function pwdEncrypt(pwd: string, publicKey?: string): string {
  const jsEncrypt = new JSEncrypt({});
  jsEncrypt.setPublicKey(publicKey ?? PUBLIC_KEY);
  const encrypted = jsEncrypt.encrypt(pwd);
  if (encrypted === false) return pwd;

  return encrypted;
}

export function encrypt(keyWord: string) {
  return AES.encrypt(keyWord, 'info-water').toString();
}
export function decrypt(keyWord: string): string {
  return AES.decrypt(keyWord, 'info-water').toString(enc.Utf8);
}

export const pingLoginRequest = () => {
  setTimeout(() => {
    postRequest({
      code: 'portal/pingLogin',
      params: {},
    }).then((res: any) => {
      if (res.json_ok) {
        pingLoginRequest();
      }
    });
  }, 300000);
};

/** 第三方关联信息 */
export interface ExternalUserInfo {
  canLogin?: boolean;
  dept?: string;
  id?: string;
  loginName?: string;
  mobile?: string;
}

export interface UserInfoType {
  userEmail: string;
  userName: string;
  userPhone: string;
  userSex: string;
  departmentId: string;
  departmentName: string;
  externalUserInfo?: ExternalUserInfo;
}

export interface LoginResponse extends APIResponse {
  sessionId?: string;
  passwordTimeout?: boolean;
  isInitialPassword?: boolean;
  loginErrorTimes?: number;
  accessToken?: string;
}

export async function requestSSOLogin(
  code: string,
  appId: string,
  requestUrl?: string,
  type?: string,
): Promise<LoginResponse> {
  const params = {
    code,
    source_from: type,
    application_id: appId,
  };

  return postRequest({
    code: requestUrl ?? 'public/watergis/ssoLogin',
    params,
  }).then((res: any) => {
    if (res.json_ok) {
      return {
        status: 'Success',
        sessionId: res.session_id,
        accessToken: res.access_token,
      };
    }

    return {
      status: 'Fail',
      errorMessage: '用户名或密码错误',
    };
  });
}

export async function requestLogin(
  username: string,
  password: string,
  appId: string,
  type?: string,
  randomString?: string,
  captcha?: string,
  loginType?: string,
): Promise<LoginResponse> {
  const params = {
    username,
    password:
      loginType === 'CAS'
        ? enc.Base64.stringify(enc.Utf8.parse(password))
        : pwdEncrypt(password),
    source_from: type,
    application_id: appId,
    valid_pic_key: randomString,
    valid_pic_code: captcha,
  };

  return postRequest({
    code: loginType === 'CAS' ? 'public/portal/casLoginNH' : 'login',
    params,
  }).then((res: any) => {
    if (res.json_ok) {
      return {
        status: 'Success',
        passwordTimeout: res.password_time_out,
        sessionId: res.session_id,
        isInitialPassword: res.is_update_password,
      };
    }

    return {
      status: 'Fail',
      errorMessage: res.json_msg,
    };
  });
}

export async function logout(params: {
  ssoLoginUrl?: string;
}): Promise<APIResponse> {
  const res: any = await postRequest({
    code: '/logout',
  });

  if (params.ssoLoginUrl) {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      const res: any = await postRequest({
        code: 'public/watergis/ssoLogout',
        params: {
          access_token: accessToken,
        },
      });
      window.location.href = params.ssoLoginUrl;
      return {
        status: 'Fail',
        errorMessage: res.json_msg,
      };
    }
  }

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export function setPermissionList(menuList: Array<MenuInfo>) {
  const permissionFeatureList: MenuInfo[] = [];
  menuList.forEach((item) => {
    let dataMode = '';
    if (item.parentId === '') {
      dataMode = 'default';
      if (item.dataMode) {
        dataMode = item.dataMode;
      }
    }
    const menuInfo: MenuInfo = {
      ...item,
      parentId: item.parentId ?? 'default',
      dataMode,
    };
    permissionFeatureList.push(menuInfo);
  });
  registerSystemFeatures(permissionFeatureList);
}

export function onLoginSuccess(response: LoginResponse) {
  if (response?.sessionId) localStorage.setItem('token', response?.sessionId);
  if (response?.accessToken)
    localStorage.setItem('accessToken', response?.accessToken);

  pingLoginRequest();
}
