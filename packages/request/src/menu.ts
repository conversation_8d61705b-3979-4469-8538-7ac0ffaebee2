/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MENU_TYPE,
  MenuInfo,
  MenuList,
  UpdateMenuInfoParams,
} from '@waterdesk/data/menu-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetMenuListResponse extends APIResponse {
  menuList?: MenuList;
}

export const getMenuList = async (
  iconBaseUrl: string,
  appId?: string,
): Promise<GetMenuListResponse> => {
  const res: any = await postRequest({
    code: 'portal/getMenuList',
    params: {
      application_id: appId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const menuList: MenuList = Array.isArray(res.value?.menuList)
          ? res.value?.menuList.map((item: any): MenuInfo => {
              const typeName =
                MENU_TYPE.find((enumItem) => enumItem.value === item.MENU_TYPE)
                  ?.label || '';
              return {
                application: item.MENU_APPLICATION ?? '',
                code: item.MENU_CODE ?? '',
                functionKey: item.MENU_CODE ?? '',
                homepage: item.MENU_HOMEPAGE === '1',
                icon: item.MENU_ICON ? `${iconBaseUrl}${item.MENU_ICON}` : '',
                id: item.MENU_ID ?? '',
                name: item.MENU_NAME ?? '',
                parentId: item.MENU_PARENT_ID ?? '',
                note: item.MENU_NOTE ?? '',
                order: Number(item.MENU_ORDER),
                showType: item.MENU_SHOWTYPE ?? '',
                type: item.MENU_TYPE ?? '',
                url: decodeURIComponent(item?.MENU_URL ?? ''),
                status: item?.MENU_STATE_CODE === '1',
                typeName,
                dataMode: '',
                functionUrl: '',
              };
            })
          : [];
        resolve({
          status: 'Success',
          menuList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const deleteMenuInfo = async (id: string): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/deleteMenuInfo',
    params: {
      menu_id: id,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const editMenuInfo = async (
  params: Partial<UpdateMenuInfoParams>,
  editType: 'add' | 'update',
): Promise<APIResponse> => {
  const {
    id,
    applicationId,
    name,
    type,
    code,
    order,
    state,
    homepage,
    parentId,
    note,
    url,
    showType,
    icon,
    functionList,
  } = params;
  const functionInfoList = functionList?.map((item) => ({
    menu_name: item.name,
    menu_code: item.code,
    menu_id: item.id,
  }));
  const res: any = await postRequest({
    code: editType === 'add' ? 'portal/addMenuInfo' : 'portal/updateMenuInfo',
    params: {
      menu_id: id,
      menu_parent_id: parentId,
      menu_application: applicationId,
      menu_name: name,
      menu_icon: icon,
      menu_type: type,
      menu_code: code,
      menu_url: encodeURIComponent(url ?? ''),
      menu_show_type: showType,
      menu_order: order,
      menu_state_code: state ? 1 : 0,
      menu_homepage: homepage ? 1 : 0,
      menu_note: note,
      function_json_list: JSON.stringify(functionInfoList),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const addMenuInfo = async (
  params: {
    id?: string;
  } & Omit<UpdateMenuInfoParams, 'id'>,
): Promise<APIResponse> => editMenuInfo(params, 'add');

export const updateMenuInfo = async (
  params: UpdateMenuInfoParams,
): Promise<APIResponse> => editMenuInfo(params, 'update');
