/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  GetModelOperateLogParams,
  ModelOperateLog,
} from '@waterdesk/data/model-operate-log-data';
import { makeObjectId } from '@waterdesk/data/object-item';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetModelOperateLogResponse extends APIResponse {
  list: ModelOperateLog[];
  total: number;
}

const getModelOperateLogList = (list?: any): ModelOperateLog[] => {
  if (!Array.isArray(list)) return [];
  return list.map((item: any) => ({
    id: `${item.create_time}${makeObjectId(item.model_id, item.rec_type)}`,
    createTime: item.create_time
      ? dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss')
      : '',
    modelId: item.model_id,
    modelType: item.rec_type,
    operateType: item.operate_type,
    startTime: item.start_time || '',
    endTime: item.end_time || '',
    remark: item.remark || '',
    userName: item.user_name || '',
  }));
};

export const getModelOperateLog = async (
  params: GetModelOperateLogParams,
): Promise<GetModelOperateLogResponse> => {
  const {
    current,
    pageSize,
    startTime,
    endTime,
    operateType,
    modelType,
    userName,
  } = params;
  const res: any = await postRequest({
    code: 'supply/queryModelOperateLogList',
    params: {
      current,
      pageSize,
      start_time: startTime
        ? dayjs(startTime).format('YYYY-MM-DD 00:00:00')
        : undefined,
      end_time: endTime
        ? dayjs(endTime).add(1, 'day').format('YYYY-MM-DD 00:00:00')
        : undefined,
      operate_type: operateType,
      rec_type: modelType,
      user_name: userName,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values.records) {
        const list: ModelOperateLog[] = getModelOperateLogList(
          res.values.records,
        );
        resolve({
          status: 'Success',
          list,
          total: res.values.count ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        total: 0,
        list: [],
      });
    }
  });
};
