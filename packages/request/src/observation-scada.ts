/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ObservationBelong,
  ObservationScadaBaseItem,
  ObservationType,
} from '@waterdesk/data/observation-scada-data';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

interface GetObservationScadaListResponse extends APIResponse {
  list?: ObservationScadaBaseItem[];
}

interface CreateObservationScadaResponse extends APIResponse {
  id?: string;
}

export interface AddObservationParams {
  otype: string;
  oname: string;
  vprop: string;
  observationType: ObservationType;
  description: string;
  max?: number;
  min?: number;
  isPublic?: boolean;
  observeBelong?: ObservationBelong;
}

const processObservationData = (values: any[]): ObservationScadaBaseItem[] => {
  const groupedMap = new Map<string, ObservationScadaBaseItem>();

  values.forEach((item: any) => {
    const key = `${item.otype}_${item.oname}_${item.vprop}_${item.observe_public}`;

    if (!groupedMap.has(key)) {
      groupedMap.set(key, {
        id: item.observe_id,
        otype: item.otype,
        oname: item.oname,
        vprop: item.vprop,
        type: item.observe_type,
        description: item.description,
        isPublic: item.observe_public === 1,
        createTime: item.create_time,
        updateTime: item.modify_time,
        observationValues: [],
      });
    }

    const group = groupedMap.get(key)!;
    group.observationValues.push({
      id: item.observe_id,
      startTime: item.start_time
        ? dayjs().format(`YYYY-MM-DD ${item.start_time}`)
        : '',
      endTime: item.end_time
        ? dayjs().format(`YYYY-MM-DD ${item.end_time}`)
        : '',
      max: item.upper_limit,
      min: item.lower_limit,
    });
  });

  return Array.from(groupedMap.values());
};

export const getObservationScadaList = async (
  observeBelong?: ObservationBelong,
): Promise<GetObservationScadaListResponse> => {
  const res: any = await postRequest({
    code: 'supply/queryObservationPointList',
    params: {
      observe_belong: observeBelong || undefined,
    },
  });

  if (res.json_ok && Array.isArray(res.values)) {
    const list = processObservationData(res.values);
    return {
      status: 'Success',
      list,
    };
  }

  return {
    status: 'Fail',
    list: [],
    errorMessage: res.json_msg,
  };
};

export const requestUpdateObservationScadaInfo = async (
  id: string,
  isPublic: boolean,
  startTime?: string,
  endTime?: string,
  max?: number,
  min?: number,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/updateObservationPointInfo',
    params: {
      observe_id_list: id,
      field_list: 'upper_limit,lower_limit,start_time,end_time,observe_public',
      start_time: startTime,
      end_time: endTime,
      upper_limit: max,
      lower_limit: min,
      observe_public: isPublic ? 1 : 0,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const requestAddObservationScada = async (
  params: AddObservationParams,
): Promise<CreateObservationScadaResponse> => {
  const {
    otype,
    oname,
    vprop,
    description,
    observationType,
    min,
    max,
    observeBelong,
  } = params;
  const res: any = await postRequest({
    code: 'supply/addObservationPoint',
    params: {
      otype,
      oname,
      vprop,
      description,
      observe_type: observationType,
      upper_limit: max,
      lower_limit: min,
      isPublic: 0,
      observe_belong: observeBelong || undefined,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      id: res.values.observe_id,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const requestDeleteObservationScada = async (
  deleteIds: string[],
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/deleteObservationPoint',
    params: {
      observe_id_list: deleteIds.join(','),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
