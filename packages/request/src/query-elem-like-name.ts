/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SearchElementInfo } from '@waterdesk/data/search-element-info';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface QueryElemLikeNameResponse extends APIResponse {
  elements?: Array<SearchElementInfo>;
}

export async function queryElemLikeName(
  time: string,
  name: string,
): Promise<QueryElemLikeNameResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/queryElemLikeName',
    params: {
      time,
      name,
      count: 5,
    },
  });

  if (data.json_ok && Array.isArray(data.values)) {
    const elements: Array<SearchElementInfo> = [];
    data.values.forEach((item: any) => {
      const { ONAME, OTYPE, TITLE, SHAPE, VPROP, VALUE } = item;
      if (ONAME && OTYPE && TITLE && SHAPE && VPROP && VALUE) {
        elements.push({
          oname: ONAME,
          otype: OTYPE,
          title: TITLE,
          shape: SHAPE,
          vprop: VPROP,
          value: VALUE,
          infoType: 'Object',
        });
      }
    });

    return {
      status: 'Success',
      elements,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
