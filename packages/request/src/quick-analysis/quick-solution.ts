/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  QuickAnalysisType,
  QuickSolutionData,
  ResultReviewStatus,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { APIResponse, DefaultListAPIResponse } from '../api/api-response';
import { postRequest } from '../request';

export interface CreateQuickSolutionRes extends APIResponse {}

export interface GetQuickSolutionListRes
  extends DefaultListAPIResponse<QuickSolutionData> {}

export interface GetQuickSolutionResultRes extends APIResponse {
  result?: {
    [index: string]: any;
  };
}

export async function createQuickSolution(params: {
  type: QuickAnalysisType;
  title: string;
  source: string;
  time: string;
  analysisParams: string;
  analysisResult: string;
  reviewState: ResultReviewStatus;
  note?: string;
}): Promise<CreateQuickSolutionRes> {
  const {
    type,
    title,
    source,
    time,
    analysisParams,
    analysisResult,
    reviewState,
    note,
  } = params;
  const res: any = await postRequest({
    code: 'watergis/createQuickSolution',
    params: {
      quick_solu_type: type,
      title,
      source,
      simulation_time: time,
      simulation_condition: analysisParams,
      simulation_result: analysisResult,
      note,
      review_state: reviewState,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function getQuickSolutionList(params: {
  startTime: string;
  endTime: string;
  current?: number;
  pageSize?: number;
  reviewStatus?: ResultReviewStatus;
}): Promise<GetQuickSolutionListRes> {
  const { startTime, endTime, current, pageSize, reviewStatus } = params;
  const res: any = await postRequest({
    code: 'watergis/queryQuickSolutionList',
    params: {
      create_time_s: startTime,
      create_time_e: endTime,
      current,
      pageSize,
      review_status: reviewStatus,
    },
  });
  if (res.json_ok) {
    const list: QuickSolutionData[] =
      res.values?.records?.map((m: any) => ({
        id: m.quick_solu_id,
        type: m.quick_solu_type,
        title: m.title,
        source: m.source,
        time: m.simulation_time,
        analysisParams: m.simulation_condition,
        creator: m.creator_name,
        creatorId: m.creator_id,
        reviewer: m.reviewer,
        reviewState: m.review_status,
        reviewTime: m.review_time,
        solutionShare: m.solution_share,
        createTime: m.create_time,
        updateTime: m.update_time,
        note: m.note,
        noticeId: m.notice_id,
        noticeDetails: m.notice_details,
      })) ?? [];
    return {
      status: 'Success',
      list,
      total: res.values?.total_count ?? 0,
    };
  }
  return {
    status: 'Fail',
    list: [],
    total: 0,
    errorMessage: res.json_msg,
  };
}

export async function getQuickSolutionResult(
  id: string,
): Promise<GetQuickSolutionResultRes> {
  const res: any = await postRequest({
    code: 'watergis/queryQuickSolutionResult',
    params: {
      quick_solu_id: id,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      result: res.simulation_result,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function deleteQuickSolution(id: string): Promise<APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/deleteQuickSolution',
    params: {
      quick_solu_id: id,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function updateQuickSolution(
  id: string,
  reviewStatus?: number,
  title?: string,
  note?: string,
): Promise<APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/updateQuickSolutionResult',
    params: {
      id,
      reviewStatus,
      title,
      note,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function reviewQuickSolution(
  id: string,
  state: ResultReviewStatus,
): Promise<APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/updateQuickSolutionReviewStatus',
    params: {
      quick_solu_id: id,
      review_status: state,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function getQuickSolutionCount(
  reviewStatus?: ResultReviewStatus,
): Promise<{ count: number } & APIResponse> {
  const res: any = await postRequest({
    code: 'watergis/getQuickSolutionCount',
    params: {
      reviewStatus,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      count: res.data.count,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    count: 0,
  };
}
