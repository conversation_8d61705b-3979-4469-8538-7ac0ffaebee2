/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ReportConfig } from '@waterdesk/data/app-config';
import dayjs from 'dayjs';

import { APIResponse } from './api/api-response';
import { pwdEncrypt } from './login';
import { postRequest } from './request';

export interface GetReportUrl extends APIResponse {
  url?: string;
}

export const getRealReportUrl = async (url: string): Promise<GetReportUrl> => {
  const res: any = await postRequest({
    code: 'public/portal/getReportUrlNH',
    params: {
      redirect_url: decodeURIComponent(url) ?? '',
      current_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res) {
        resolve({
          status: 'Success',
          url: res.reportURL,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getReportToken = async (
  url: string,
  userName: string,
  reportConfig?: ReportConfig,
): Promise<GetReportUrl> => {
  if (
    !reportConfig?.requestUrl ||
    typeof reportConfig.requestUrl !== 'string'
  ) {
    return new Promise((resolve) => {
      resolve({
        status: 'Fail',
        errorMessage: 'Invalid or missing request URL in reportConfig.',
      });
    });
  }

  let name = userName;
  if (reportConfig?.RSA) {
    name = pwdEncrypt(userName, reportConfig.RSA);
  }
  const res: any = await fetch(reportConfig.requestUrl, {
    method: 'POST',
    body: JSON.stringify({
      username: name,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
  const response = await res.json();
  try {
    if (response) {
      const { token } = response;
      return {
        status: 'Success',
        url: decodeURIComponent(url) + token,
      };
    }
    return {
      status: 'Fail',
      errorMessage: res.json_msg,
    };
  } catch (err) {
    return {
      status: 'Fail',
      errorMessage: err as unknown as string,
    };
  }
};
