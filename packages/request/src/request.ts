/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export function getPermission(): Record<string, boolean> {
  const localPermission: string | null = localStorage.getItem('permission');
  let permission: { [key: string]: boolean } = {};
  if (!localPermission) {
    permission = { login: true };
  } else {
    permission = JSON.parse(localPermission);
    permission.login = true;
    permission['portal/pingLogin'] = true;
    permission.getVersion = true;
  }
  return permission;
}

function getFormData(params?: any): FormData {
  if (params instanceof FormData) return params;

  const formData = new FormData();
  if (params)
    Object.entries(params).forEach(([key, value]: any) => {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object')
          formData.append(key, JSON.stringify(value));
        else formData.append(key, value);
      }
    });
  return formData;
}

async function parseResponse(
  response: Response,
  responseType: 'json' | 'blob' = 'json',
): Promise<any> {
  switch (responseType) {
    case 'blob':
      return response.blob();
    case 'json':
    default:
      return response.json();
  }
}

interface RequestOptions {
  url: string;
  baseUrl?: string;
  body?: RequestInit['body'];
  method: RequestInit['method'];
  headers?: RequestInit['headers'];
  responseType?: 'json' | 'blob';
  urlSearchParams?: URLSearchParams;
}

export type ResponseBodyJson = any;

type RequestInterceptor = {
  name: string;
  handler: (
    request: Request,
    // eslint-disable-next-line no-use-before-define
    instance: RequestApi,
  ) => Promise<Request | boolean>;
};

type ResponseInterceptor = {
  name: string;
  handler: (
    response: Response,
    responseBody: ResponseBodyJson,
    // eslint-disable-next-line no-use-before-define
    instance: RequestApi,
  ) => Promise<Response | boolean>;
};
class RequestApi {
  private _baseUrl: string | undefined;

  private _time: string | undefined;

  private _viewId: string | undefined;

  private _requestInterceptors: Map<string, RequestInterceptor> | undefined;

  private _responseInterceptors: Map<string, ResponseInterceptor> | undefined;

  private _commonSearchParams: URLSearchParams | undefined;

  // eslint-disable-next-line no-use-before-define
  private static _instance: RequestApi;

  initialize(options: {
    baseUrl: string;
    requestInterceptors?: Array<RequestInterceptor>;
    responseInterceptors?: Array<ResponseInterceptor>;
  }) {
    this._baseUrl = options.baseUrl;
    if (options.requestInterceptors?.length)
      this.injectRequestInterceptors(options.requestInterceptors);

    if (options.responseInterceptors?.length)
      this.injectResponseInterceptors(options.responseInterceptors);
  }

  static getInstance(): RequestApi {
    if (RequestApi._instance == null) {
      RequestApi._instance = new RequestApi();
    }

    return RequestApi._instance;
  }

  private async runRequestInterceptors(
    request: Request,
  ): Promise<Request | boolean> {
    if (this._requestInterceptors?.size) {
      const arr = Array.from(this._requestInterceptors.values());
      for (let p = 0; p < arr.length; p += 1) {
        // eslint-disable-next-line no-await-in-loop
        const interceptorResolve = await arr[p].handler(
          request,
          RequestApi._instance,
        );
        if (typeof interceptorResolve === 'boolean')
          return Promise.reject(request);
        // eslint-disable-next-line no-param-reassign
        request = interceptorResolve;
      }
    }
    return Promise.resolve(request);
  }

  private async runResponseInterceptors(
    response: Response,
    responseBody: ResponseBodyJson,
  ): Promise<Response | boolean> {
    if (this._responseInterceptors?.size) {
      const arr = Array.from(this._responseInterceptors.values());
      for (let p = 0; p < arr.length; p += 1) {
        // eslint-disable-next-line no-await-in-loop
        const interceptorResolve = await arr[p].handler(
          response,
          responseBody,
          RequestApi._instance,
        );
        if (typeof interceptorResolve === 'boolean')
          return Promise.reject(response);
        // eslint-disable-next-line no-param-reassign
        response = interceptorResolve;
      }
    }
    return Promise.resolve(response);
  }

  injectRequestInterceptors(interceptors: RequestInterceptor[]): void {
    if (!this._requestInterceptors) this._requestInterceptors = new Map();
    interceptors.forEach((interceptor) => {
      this._requestInterceptors!.set(interceptor.name, interceptor);
    });
  }

  injectResponseInterceptors(interceptors: ResponseInterceptor[]): void {
    if (!this._responseInterceptors) this._responseInterceptors = new Map();

    interceptors.forEach((interceptor) => {
      this._responseInterceptors!.set(interceptor.name, interceptor);
    });
  }

  private async request(options: RequestOptions) {
    try {
      const urlSearchParams = options.urlSearchParams ?? new URLSearchParams();
      if (this._commonSearchParams) {
        Array.from(this._commonSearchParams.entries()).forEach(
          ([key, value]) => {
            urlSearchParams.append(key, value);
          },
        );
      }
      const params = urlSearchParams.toString()
        ? `?${urlSearchParams.toString()}`
        : '';
      const url = new URL(
        `${options.baseUrl ?? this._baseUrl}${options.url}${params}`,
      );
      let request = new Request(url, {
        method: options.method,
        body: options.body,
        headers: options.headers,
      });

      // run requestInterceptors
      const resRequest = await this.runRequestInterceptors(request);

      if (typeof resRequest === 'boolean') return Promise.reject(resRequest);
      request = resRequest;

      const response = await fetch(request);

      const res = await parseResponse(response, options.responseType);
      // run responseInterceptors
      const resResponse = await this.runResponseInterceptors(response, res);

      if (typeof resResponse === 'boolean') return Promise.reject(response);

      return Promise.resolve(res);
    } catch (err) {
      console.error('Fetch request error:', err);
      return err;
    }
  }

  async postRequest(options: { code: string; params?: any; baseUrl?: string }) {
    try {
      const formData = getFormData(options.params);

      const res = await this.request({
        url: options.code,
        baseUrl: options.baseUrl,
        method: 'post',
        body: formData,
        responseType: 'json',
      });

      return res;
    } catch (err) {
      console.error(`Fetch ${options.code} error: ${err}`);
      return err;
    }
  }

  async downloadRequest(options: {
    code: string;
    params?: any;
    baseUrl?: string;
  }) {
    try {
      const formData = getFormData(options.params);

      const res = await this.request({
        url: options.code,
        baseUrl: options.baseUrl,
        method: 'post',
        body: formData,
        responseType: 'blob',
      });

      return res;
    } catch (err) {
      console.error(`Fetch ${options.code} error: ${err}`);
      return err;
    }
  }

  async getRequest(options: { code: string; params?: any; baseUrl?: string }) {
    try {
      const urlSearchParams = new URLSearchParams(options.params);

      const res = await this.request({
        url: options.code,
        baseUrl: options.baseUrl,
        method: 'get',
        responseType: 'json',
        urlSearchParams,
      });

      return res;
    } catch (err) {
      console.error(`Fetch ${options.code} error: ${err}`);
      return err;
    }
  }

  async postRequestByView(options: {
    code: string;
    params?: { view_id?: string; time?: string; [index: string]: any };
    baseUrl?: string;
  }) {
    try {
      const viewId = options.params?.view_id ?? this._viewId;
      const newParams = options.params ? options.params : {};

      // inject time and view id in params
      newParams.time = newParams.time ?? this._time;
      newParams.view_id = viewId;

      const response = await this.postRequest({
        ...options,
        params: newParams,
      });
      return response;
    } catch (err) {
      console.error(`Fetch ${options.code} error: ${err}`);
      return err;
    }
  }

  addCommonSearchParams(object: { [index: string]: string }): void {
    if (!this._commonSearchParams)
      this._commonSearchParams = new URLSearchParams();
    Object.entries(object).forEach(([key, value]) => {
      this._commonSearchParams!.set(key, value);
    });
  }

  getCommonSearchParams<T extends string>(paramName: string): T | undefined {
    return this._commonSearchParams?.get(paramName) as T | undefined;
  }

  set time(time: string) {
    this._time = time;
  }

  set viewId(viewId: string | undefined) {
    this._viewId = viewId;
  }

  get baseUrl(): string | undefined {
    return this._baseUrl;
  }
}

function getRequestApi(): RequestApi {
  return RequestApi.getInstance();
}

export const requestApi = getRequestApi();

export const postRequest = requestApi.postRequest.bind(requestApi);
export const downloadRequest = requestApi.downloadRequest.bind(requestApi);
export const getRequest = requestApi.getRequest.bind(requestApi);
export const uploadRequest = requestApi.postRequest.bind(requestApi);
export const postRequestByView = requestApi.postRequestByView.bind(requestApi);
