/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ScadaDeviceItem,
  ScadaGroupListItem,
} from '@waterdesk/data/scada-tree-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface getScadaGroupListResponse extends APIResponse {
  list: ScadaGroupListItem[];
}

export interface getScadaDeviceListResponse extends APIResponse {
  list: ScadaDeviceItem[];
}

export const getScadaGroupList =
  async (): Promise<getScadaGroupListResponse> => {
    const res: any = await postRequest({
      code: 'watergis/getAssetTree',
    });

    if (res.json_ok && Array.isArray(res.values)) {
      return {
        status: 'Success',
        list: res.values,
      };
    }

    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      list: [],
    };
  };

export const getScadaDeviceList =
  async (): Promise<getScadaDeviceListResponse> => {
    const res: any = await postRequest({
      code: 'watergis/getAssetScadas',
    });

    if (res.json_ok && Array.isArray(res.values)) {
      return {
        status: 'Success',
        list: res.values,
      };
    }

    return {
      status: 'Fail',
      errorMessage: res.json_msg,
      list: [],
    };
  };

export const addScadaTreeNode = async (params: {
  nodeTitle: string;
  parentId: string | undefined;
  targetId: string | undefined;
}): Promise<APIResponse> => {
  const { nodeTitle, parentId, targetId } = params;
  const res: any = await postRequest({
    code: 'watergis/addAssetTree',
    params: {
      node_parent_id: parentId,
      prev_node_id: targetId,
      node_title: nodeTitle,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const editScadaTreeNode = async (
  nodeId: string,
  nodeTitle: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'watergis/updateAssetTree',
    params: {
      node_id: nodeId,
      node_title: nodeTitle,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const addScadaDevice = async (
  nodeId: string,
  devices: { otype: string; oname: string }[],
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'watergis/updateAssetScadas',
    params: {
      node_id: nodeId,
      scada_json_list: JSON.stringify(devices),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const deleteScadaTreeNode = async (
  nodeId: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'watergis/deleteAssetTree',
    params: {
      node_id: nodeId,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const moveScadaTreeNode = async (params: {
  nodeId: string | number;
  parentId?: string | number;
  targetId?: string | number;
}): Promise<APIResponse> => {
  const { nodeId, parentId, targetId } = params;
  const res: any = await postRequest({
    code: 'watergis/moveAssetTree',
    params: {
      node_id: nodeId,
      node_parent_id: parentId,
      prev_node_id: targetId,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
