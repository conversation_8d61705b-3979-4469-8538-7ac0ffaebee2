/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  HandoverOperationLogItem,
  ShiftHandoverInfo,
  ShiftHandoverState,
} from '@waterdesk/data/schedule-log/shift-handover';
import dayjs from 'dayjs';
import { PageParams } from '../api/api-request';
import { APIResponse, DefaultListAPIResponse } from '../api/api-response';
import { postRequest } from '../request';

export interface GetShiftHandoverInfoRequest {
  id?: string; // 交接班 ID
  fromShiftId?: string; // 交班班次 ID
  toShiftId?: string; // 接班班次 ID
  toUserId?: string; // 接班人 ID
  needHandover?: boolean; // 是否需要交接
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
}

export interface CreateOrUpdateShiftHandoverRequest {
  id?: string; // 交接班 ID
  fromShiftId?: string; // 交班班次 ID
  toShiftId?: string; // 接班班次 ID
  content?: string; // 交接工作内容
  planContent?: string; // 计划工作内容
  noteContent?: string; // 注意事项
  remark?: string; // 备注
  state?: ShiftHandoverState; // 交接班状态
  handoverTime?: string; // 交接班时间
}

export interface CreateOrUpdateShiftHandoverResponse {
  id?: string; // 交接班 ID
}

/** 查询交接班信息 */
export const getShiftHandoverInfo = async (
  params: GetShiftHandoverInfoRequest & PageParams,
): Promise<DefaultListAPIResponse<ShiftHandoverInfo>> => {
  const data: any = await postRequest({
    code: 'watergis/queryHandoverInfoList',
    params: {
      current: params.current,
      pageSize: params.pageSize,
      handover_id: params.id,
      from_shift_id: params.fromShiftId,
      to_shift_id: params.toShiftId,
      to_user_id: params.toUserId,
      need_handover: params.needHandover,
      start_date: params.startTime,
      end_date: params.endTime,
    },
  });

  if (data.json_ok) {
    const list = data.values.records.map(
      (item: any): ShiftHandoverInfo => ({
        id: item.handover_id,
        content: item.handover_content,
        planContent: item.plan_content,
        noteContent: item.note_content,
        remark: item.remark,
        state: item.handover_state,
        creator: item.user_name,
        createTime: item.create_time,
        fromShiftId: item.from_shift_id,
        fromShiftName: item.from_shift_classes,
        fromShiftDate: item.from_shift_date,
        fromShiftStartTime: item.from_start_time,
        fromShiftEndTime: item.from_end_time,
        fromUser: item.handover_from_user_name,
        fromUserId: item.handover_from_operator,
        toShiftId: item.to_shift_id,
        toShiftName: item.to_shift_classes,
        toShiftDate: item.to_shift_date,
        toShiftStartTime: item.to_start_time,
        toShiftEndTime: item.to_end_time,
        toUser: item.handover_to_user_name,
        toUserId: item.handover_to_operator,
        handoverTime: item.handover_time,
        takeoverTime: item.takeover_time,
      }),
    );
    return {
      status: 'Success',
      list,
      total: data.values.count,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    list: [],
    total: 0,
  };
};

/** 创建或更新交接班信息 */
export const createOrUpdateShiftHandover = async (
  params: CreateOrUpdateShiftHandoverRequest,
): Promise<CreateOrUpdateShiftHandoverResponse & APIResponse> => {
  let { handoverTime } = params;
  if (!handoverTime && params.state === ShiftHandoverState.CONFIRMED) {
    handoverTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }
  const data: any = await postRequest({
    code: 'watergis/createOrUpdateHandoverInfo',
    params: {
      handover_id: params.id,
      from_shift_id: params.fromShiftId,
      to_shift_id: params.toShiftId,
      handover_content: params.content,
      plan_content: params.planContent,
      note_content: params.noteContent,
      remark: params.remark,
      handover_state: params.state,
      handover_time: handoverTime,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      id: data.values.handover_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
  };
};

export const getHandoverLogsById = async (params: {
  id: string;
}): Promise<
  {
    list: HandoverOperationLogItem[];
  } & APIResponse
> => {
  const data: any = await postRequest({
    code: 'watergis/getHandoverLogsById',
    params: {
      handover_id: params.id,
    },
  });

  if (data.json_ok) {
    const list = data.values.map(
      (item: any): HandoverOperationLogItem => ({
        logId: item.log_id,
        operationType: item.operation_type,
        operationTime: item.operation_time,
        operatorId: item.operator_id,
        operatorName: item.operator_name,
        beforeData: item.before_data,
        afterData: item.after_data,
      }),
    );
    return {
      status: 'Success',
      list,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data?.json_msg,
    list: [],
  };
};

/** 查询当前用户是否为当班人员 */
export const checkCurrentUserIsOnDuty = async (
  shiftId: string,
): Promise<boolean> => {
  const data: any = await postRequest({
    code: 'watergis/queryIsBeOnDutyStateByShiftId',
    params: {
      shift_id: shiftId,
    },
  });

  if (data?.json_ok) {
    return data?.values?.is_be_on_duty;
  }

  return false;
};

/** 查询当前用户交接班完成是否需要退出 */
export const checkCurrentUserIsNeedLogout = async (
  shiftId: string,
): Promise<boolean> => {
  const data: any = await postRequest({
    code: 'watergis/queryNeedExitStateByShiftId',
    params: {
      shift_id: shiftId,
    },
  });

  if (data?.json_ok) {
    return data?.values?.need_exit;
  }

  return false;
};

/** 查询当前用户是否需要交班 */
export const checkUserForShiftHandoff = async (
  shiftId: string,
): Promise<boolean> => {
  const data: any = await postRequest({
    code: 'watergis/queryNeedHandoverStateByShiftId',
    params: {
      shift_id: shiftId,
    },
  });

  if (data?.json_ok) {
    return data?.values?.need_handover;
  }

  return false;
};
