/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { postRequest } from './request';

/** 发送OA请求参数 */
export interface SendOAParams {
  /** 方案对比url */
  comparisonUrl: string;
  /** 停水用户数量 */
  waterOutageUserCount?: number;
  /** 受影响用户数量 */
  affectedUserCount?: number;
}

/** 发送OA响应数据 */
export interface SendOAResponse extends APIResponse {
  /** OA_id */
  oaId?: string;
}

/**
 * 发送OA
 * @param params 发送OA参数
 * @returns 发送OA响应
 */
export async function sendOA(params: SendOAParams): Promise<SendOAResponse> {
  const data: any = await postRequest({
    code: 'supply/sendOA',
    params: {
      comparison_url: params.comparisonUrl,
      water_outage_user_count: params.waterOutageUserCount,
      affected_user_count: params.affectedUserCount,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
      oaId: data.OA_id,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
