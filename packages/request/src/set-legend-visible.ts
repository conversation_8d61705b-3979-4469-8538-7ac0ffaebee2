/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  LegendGroupData,
  LegendGroupDataCollection,
} from '@waterdesk/data/legend-data';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface SetLegendVisibleResponse extends APIResponse {
  legendData?: LegendGroupData[];
  deviceColorData?: Map<string, string>;
}

export async function setLegendVisible(
  time: string,
  legendName: string,
  legendId: string | number,
  visible: boolean,
  viewId?: string,
): Promise<SetLegendVisibleResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/setLegendVisible',
    params: {
      time,
      legend: legendName,
      legend_id_list: legendId,
      legend_visible: visible,
      view_id: viewId,
    },
  });

  if (data.json_ok && data.grade_inst) {
    const legendData: LegendGroupDataCollection =
      new LegendGroupDataCollection();
    legendData.initialize(data.grade_inst, data.theme_styles);
    return {
      status: 'Success',
      legendData: legendData.legendDataCollection,
      deviceColorData: legendData.deviceColorData,
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
