/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  LegendGroupData,
  LegendGroupDataCollection,
} from '@waterdesk/data/legend-data';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface SetViewThemeTimeResponse extends APIResponse {
  legendData?: LegendGroupData[];
  deviceColorData?: Map<string, string>;
}

export async function setViewThemeTime(params: {
  /** string as YYYY-MM-DD HH:mm:ss */
  time?: string;
  force?: boolean;
  /** 默认：历史；预测：_FC1 */
  fc_ext?: '_FC1';
  view_id?: string;
}): Promise<SetViewThemeTimeResponse> {
  try {
    const data: any = await postRequestByView({
      code: 'watergis/setViewThemeTime',
      params,
    });

    if (data.json_ok && data.grade_inst) {
      const legendData: LegendGroupDataCollection =
        new LegendGroupDataCollection();
      legendData.initialize(data.grade_inst, data.theme_styles);
      return {
        status: 'Success',
        legendData: legendData.legendDataCollection,
        deviceColorData: legendData.deviceColorData,
      };
    }

    return {
      status: 'Fail',
      errorMessage: data.json_msg,
    };
  } catch (err) {
    return {
      status: 'Fail',
      errorMessage: err as string,
    };
  }
}
