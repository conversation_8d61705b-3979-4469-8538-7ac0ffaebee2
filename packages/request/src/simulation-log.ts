/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import type {
  LastSimulationInfo,
  SimulationLogItem,
  SimulationModelDetail,
  SimulationModelState,
} from '@waterdesk/data/simulation-log/simulation-log-data';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSimulationLogResponse extends APIResponse {
  simulationLogList?: SimulationLogItem[];
}

export interface GetSimulationLogDetailsResponse extends APIResponse {
  details?: SimulationModelDetail[];
}

export interface GetLastSimulationResultResponse extends APIResponse {
  info?: LastSimulationInfo;
}

export const getSimulationLogList = async (params: {
  /** string as YYYY-MM-DD */
  date: string;
}): Promise<GetSimulationLogResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSimulationLog',
    params,
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const simulationLogList = res.values.map((item: any) => {
          if (typeof item.model_id !== 'string') {
            throw Error(
              `模型id应该是一个string， 而不是${typeof item.model_id}`,
            );
          }
          const states = item.states.map((items: any) => ({
            state: items.state ?? '',
            modelTime: items.model_time ?? '',
            strongConditionCount: items.strong_condition_count ?? 0,
            strongConditionMissCount: items.strong_condition_miss_count ?? 0,
            calculationTime: items.calculation_time ?? '',
          }));
          return {
            modelId: item.model_id ?? '',
            modelName: item.model_name ?? '',
            states,
          };
        });
        resolve({
          status: 'Success',
          simulationLogList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getLastSimulationResult = async (
  /** string as YYYY-MM-DD hh:mm:ss */
  dateTime: string,
): Promise<GetLastSimulationResultResponse> => {
  const res: any = await postRequest({
    code: 'supply/getCalcResultLog',
    params: {
      time: dateTime,
    },
  });

  if (res.json_ok && res.value && res.value?.calculation_time) {
    const { value } = res;
    const state: SimulationModelState = {
      state: value.state ?? '',
      modelTime: value.model_time ?? '',
      strongConditionCount: value.total_count ?? 0,
      strongConditionMissCount: value.miss_count ?? 0,
      calculationTime: value.calculation_time ?? '',
    };
    const info: LastSimulationInfo = {
      modelId: value.model_id ?? '',
      modelName: value.model_name ?? '',
      ...state,
    };
    return {
      info,
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const getSimulationLogDetails = async (
  params: {
    model_id: string;
    /** string as YYYY-MM-DD HH:mm:ss */
    time: string;
  },
  db: Database,
): Promise<GetSimulationLogDetailsResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSimulationConditionDetails',
    params,
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const details = res.values.map((item: any): SimulationModelDetail => {
          let deviceTitle = '';
          let indicatorTitle: string = item.oname;
          const indicator = db.getIndicator(item.otype, item.oname);
          if (indicator !== undefined) {
            if (
              indicator.ptype !== undefined &&
              indicator.pname !== undefined
            ) {
              const device = db.getDevice(indicator.ptype, indicator.pname);
              if (typeof device !== 'undefined') deviceTitle = device.title;
            }
            if (indicator.title) indicatorTitle = indicator.title;
            else indicatorTitle = indicator.indicatorType?.title || '';
          }
          return {
            otype: item.otype ?? '',
            oname: item.oname ?? '',
            value: item.value ?? 0,
            dataMode: item.data_mode ?? '',
            dataSampling: item.data_sampling ?? '',
            duration: item.duration ?? 0,
            dataDetails: item.data_details || [],
            otypeTitle: indicatorTitle,
            deviceTitle,
            isStrongCondition: item.primary ?? false,
          };
        });
        resolve({
          status: 'Success',
          details,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
