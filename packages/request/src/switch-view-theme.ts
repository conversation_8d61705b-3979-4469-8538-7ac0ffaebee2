/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeviceThemeData,
  getDeviceThemeData,
} from '@waterdesk/data/device-theme';
import {
  LegendGroupData,
  LegendGroupDataCollection,
} from '@waterdesk/data/legend-data';
import { APIResponse } from './api/api-response';
import { postRequestByView } from './request';

export interface SwitchViewThemeResponse extends APIResponse {
  legendData?: LegendGroupData[];
  deviceColorData?: Map<string, string>;
  deviceTypeRatioData?: Map<string, [number | undefined, number | undefined]>; // key is device otype
  deviceThemeData?: DeviceThemeData;
}

function getRatioMap(
  themeRatio: any,
): Map<string, [number | undefined, number | undefined]> | undefined {
  if (!themeRatio) return undefined;
  const themeRatioMap: Map<string, [number | undefined, number | undefined]> =
    new Map();
  Object.keys(themeRatio).forEach((otype: string) => {
    themeRatioMap.set(otype, [
      themeRatio[otype].min_ratio,
      themeRatio[otype].max_ratio,
    ]);
  });
  return themeRatioMap;
}

export async function switchViewTheme(
  time: string,
  themeNames: string[],
  invisibleLayers: string[],
  viewId?: string,
): Promise<SwitchViewThemeResponse> {
  if (!viewId)
    return {
      status: 'Fail',
      errorMessage: '当前没有 ViewId',
    };
  const data: any = await postRequestByView({
    code: 'watergis/switchViewTheme',
    params: {
      time,
      legend_hide_json: {},
      theme_id_list: themeNames.toString(),
      hide_layers: invisibleLayers.toString(),
      view_id: viewId,
    },
  });

  if (data.json_ok && data.grade_inst) {
    const legendData: LegendGroupDataCollection =
      new LegendGroupDataCollection();
    legendData.initialize(data.grade_inst, data.theme_styles);
    return {
      status: 'Success',
      legendData: legendData.legendDataCollection,
      deviceColorData: legendData.deviceColorData,
      deviceTypeRatioData: getRatioMap(data.theme_ratio),
      deviceThemeData: getDeviceThemeData(data.theme_styles),
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}
