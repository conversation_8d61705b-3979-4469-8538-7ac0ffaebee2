/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SystemIconData, SystemIconType } from '@waterdesk/data/system-icon';
import { UploadFileType } from '@waterdesk/data/upload';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest, uploadRequest } from './request';

export interface GetSystemIconListResponse extends APIResponse {
  iconList?: string[];
}

export interface UploadSystemIconResponse extends APIResponse {
  uuid?: string;
}

export interface UploadSystemFileResponse extends APIResponse {
  uuid?: string;
}

export interface DeleteSystemIconResponse extends APIResponse {}

/**
 * @deprecated
 */
export const getSystemIconListOld =
  async (): Promise<GetSystemIconListResponse> => {
    const res: any = await postRequest({
      code: 'portal/getIconFileList',
    });
    return new Promise((resolve) => {
      try {
        if (res.json_ok && res.icons) {
          resolve({
            status: 'Success',
            iconList: Array.isArray(res.icons) ? res.icons : [],
          });
        } else {
          resolve({
            status: 'Fail',
            errorMessage: res.json_msg,
          });
        }
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    });
  };

/**
 * @deprecated
 */
export const uploadSystemIconOld = async (params: {
  iconName: string;
  iconFile: Blob;
}): Promise<UploadSystemIconResponse> => {
  const { iconName, iconFile } = params;
  const formData: FormData = new FormData();
  formData.append('icon_name', iconName);
  formData.append('icon_file', iconFile);
  const res: any = await uploadRequest({
    code: 'portal/addIconFile',
    params: formData,
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

/**
 * @deprecated
 */
export const deleteSystemIconOld = async (params: {
  iconName: string;
}): Promise<DeleteSystemIconResponse> => {
  const res: any = await postRequest({
    code: 'portal/removeIconFile',
    params: {
      icon_name: params.iconName,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getSystemIconList = async (
  typeList?: SystemIconType[],
  current?: number,
  pageSize?: number,
): Promise<DefaultListAPIResponse<SystemIconData>> => {
  const res: any = await postRequest({
    code: 'portal/getImageList',
    params: { type_list: typeList?.join(), current, page_size: pageSize },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.data) {
        resolve({
          status: 'Success',
          list: res.data.list ?? [],
          total: res.data.total ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total: 0,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        list: [],
        total: 0,
      });
    }
  });
};

export const uploadSystemIcon = async (params: {
  fileName: string;
  fileType: SystemIconType;
  file: Blob;
}): Promise<UploadSystemIconResponse> => {
  const { fileName, fileType, file } = params;
  const formData: FormData = new FormData();
  formData.append('fileName', fileName);
  formData.append('fileType', fileType);
  formData.append('file', file);
  const res: any = await uploadRequest({
    code: 'portal/uploadImage',
    params: formData,
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          uuid: res.json_msg,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const deleteSystemIcon = async (
  uuid: string,
): Promise<DeleteSystemIconResponse> => {
  const res: any = await postRequest({
    code: 'portal/deleteImage',
    params: {
      uuid,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const uploadSystemFile = async (params: {
  fileName: string;
  fileType: UploadFileType;
  file: Blob;
}): Promise<UploadSystemFileResponse> => {
  const { fileName, fileType, file } = params;
  const formData: FormData = new FormData();
  formData.append('fileName', fileName);
  formData.append('fileType', fileType);
  formData.append('file', file);
  const res: any = await uploadRequest({
    code: 'portal/uploadFile',
    params: formData,
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          uuid: res.json_msg,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
