/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  GetSystemTaskParams,
  getDuration,
  SystemKettleLogItem,
  SystemKettleLogList,
  SystemTaskList,
} from '@waterdesk/data/system-kettle-log';
import dayjs from 'dayjs';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSystemTaskListResponse extends APIResponse {
  taskList?: SystemTaskList;
}

export interface GetSystemKettleLogListResponse
  extends APIResponse,
    DefaultListAPIResponse<SystemKettleLogItem> {}

export interface GetSystemTaskDetailResponse extends APIResponse {
  taskDetailText?: string;
  taskStatus?: SystemKettleLogItem['taskStatus'];
}

const getKettleLogList = (list?: any): SystemKettleLogList => {
  if (!Array.isArray(list)) return [];
  return list.map((item: any) => ({
    taskId: item.run_id ?? '',
    startTime: item.start_time
      ? dayjs(item.start_time).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    endTime: item.end_time
      ? dayjs(item.end_time).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    taskName: item.job_title ?? '',
    taskType: item.job_name ?? '',
    taskStatus: item.run_state ?? '',
    retryCount: item.retry_count ? Number(item.retry_count) : 0,
    /** string as YYYY-MM-DD hh:mm:ss */
    cronTime: item.cron_time
      ? dayjs(item.cron_time).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    duration: getDuration(item.start_time, item.end_time),
  }));
};

export const getSystemTaskList =
  async (): Promise<GetSystemTaskListResponse> => {
    const res: any = await postRequest({
      code: 'elasticjob/queryJobNameList',
    });
    return new Promise((resolve) => {
      try {
        if (res.json_ok && res.values) {
          resolve({
            status: 'Success',
            taskList: Array.isArray(res.values) ? res.values : [],
          });
        } else {
          resolve({
            status: 'Fail',
            errorMessage: res.json_msg,
          });
        }
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    });
  };

export const getSystemKettleLogListByDate = async (
  params: GetSystemTaskParams,
): Promise<GetSystemKettleLogListResponse> => {
  const {
    current,
    pageSize,
    startTime,
    endTime,
    taskStatus,
    taskName,
    taskText,
  } = params;
  const res: any = await postRequest({
    code: 'elasticjob/queryJobLogPage',
    params: {
      current,
      pageSize,
      start_time: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endTime).add(1, 'day').format('YYYY-MM-DD 00:00:00'),
      run_state: taskStatus,
      job_log: taskText,
      job_name: taskName,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.rows) {
        const list: SystemKettleLogList = getKettleLogList(res.rows);
        resolve({
          status: 'Success',
          list,
          total: res.total ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: res.total ?? 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        total: res.total ?? 0,
        list: [],
      });
    }
  });
};

export const getSystemKettleLogListByDateTime = async (
  params: GetSystemTaskParams,
): Promise<GetSystemKettleLogListResponse> => {
  const {
    current,
    pageSize,
    startTime,
    endTime,
    taskStatus,
    taskName,
    taskText,
  } = params;
  const res: any = await postRequest({
    code: 'elasticjob/queryJobLogPage',
    params: {
      current,
      pageSize,
      start_time: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
      run_state: taskStatus,
      job_log: taskText,
      job_name: taskName,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.rows) {
        const list: SystemKettleLogList = getKettleLogList(res.rows);
        resolve({
          status: 'Success',
          list,
          total: res.total ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: res.total ?? 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        total: res.total ?? 0,
        list: [],
      });
    }
  });
};

export const getSystemKettleLastLogList =
  async (): Promise<GetSystemKettleLogListResponse> => {
    const res: any = await postRequest({
      code: 'elasticjob/queryLastJobLog',
    });
    return new Promise((resolve) => {
      try {
        if (res.json_ok && res.values) {
          const list: SystemKettleLogList = getKettleLogList(res.values);
          resolve({
            status: 'Success',
            list,
            total: res.total ?? 0,
          });
        } else {
          resolve({
            status: 'Fail',
            errorMessage: res.json_msg,
            list: [],
            total: res.total ?? 0,
          });
        }
      } catch (err) {
        resolve({
          list: [],
          status: 'Fail',
          errorMessage: err as unknown as string,
          total: res.total ?? 0,
        });
      }
    });
  };

export const getSystemTaskDetail = async (params: {
  taskId: string;
}): Promise<GetSystemTaskDetailResponse> => {
  const res: any = await postRequest({
    code: 'elasticjob/queryJobLogDetail',
    params: {
      run_id: params.taskId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          taskDetailText: res.log_text || '',
          taskStatus: res.log_status,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
