/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeviceModifiedLog,
  DeviceModifiedLogSummary,
  GetDeviceModifiedParams,
  GetDeviceModifiedSummaryParams,
} from '@waterdesk/data/device-modified-data';
import { makeObjectId } from '@waterdesk/data/object-item';
import dayjs from 'dayjs';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetDeviceModifiedLogResponse extends APIResponse {
  list: DeviceModifiedLog[];
  total: number;
}

export interface GetDeviceModifiedLogSummaryResponse extends APIResponse {
  list: DeviceModifiedLogSummary[];
  total: number;
}

const getDeviceModifiedLogList = (list?: any): DeviceModifiedLog[] => {
  if (!Array.isArray(list)) return [];
  return list.map((item: any) => ({
    id: `${item.otime}${makeObjectId(item.otype, item.oname)}`,
    updateTime: item.otime
      ? dayjs(item.otime).format('YYYY-MM-DD HH:mm:ss')
      : '',
    otype: item.otype,
    oname: item.oname,
    operationType: item.operation_type,
    modifyType: item.modify_type,
    description: item.description,
    source: item.source || '',
    operator: item.operator || '',
    remark: item.remark || '',
  }));
};

export const getDeviceModifiedLog = async (
  params: GetDeviceModifiedParams,
): Promise<GetDeviceModifiedLogResponse> => {
  const {
    current,
    pageSize,
    startTime,
    endTime,
    operationType,
    modifyType,
    deviceCode,
  } = params;
  const res: any = await postRequest({
    code: 'supply/queryModifyLogList',
    params: {
      current,
      pageSize,
      start_time: startTime
        ? dayjs(startTime).format('YYYY-MM-DD 00:00:00')
        : undefined,
      end_time: endTime
        ? dayjs(endTime).add(1, 'day').format('YYYY-MM-DD 00:00:00')
        : undefined,
      operation_type: operationType,
      modify_type: modifyType,
      oname: deviceCode,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values.records) {
        const list: DeviceModifiedLog[] = getDeviceModifiedLogList(
          res.values.records,
        );
        resolve({
          status: 'Success',
          list,
          total: res.values.count ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        total: 0,
        list: [],
      });
    }
  });
};

const getSummaryList = (list?: any): DeviceModifiedLogSummary[] => {
  if (!Array.isArray(list)) return [];
  return list.map((item: any) => ({
    id: `${item.otime}@${item.operation_type}@${item.modify_type}`,
    updatedTime: item.otime
      ? dayjs(item.otime).format('YYYY-MM-DD HH:mm:ss')
      : '',
    operationType: item.operation_type,
    modifyType: item.modify_type,
    count: item.count,
    description: item.modify_desc,
  }));
};

export const getDeviceModifiedLogSummary = async (
  params: GetDeviceModifiedSummaryParams,
): Promise<GetDeviceModifiedLogSummaryResponse> => {
  const { current, pageSize, startTime, endTime, operationType, modifyType } =
    params;
  const res: any = await postRequest({
    code: 'supply/queryModifySummaryLogList',
    params: {
      current,
      pageSize,
      start_time: dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(endTime).add(1, 'day').format('YYYY-MM-DD 00:00:00'),
      operation_type: operationType,
      modify_type: modifyType,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values.records) {
        const list: DeviceModifiedLogSummary[] = getSummaryList(
          res.values.records,
        );
        resolve({
          status: 'Success',
          list,
          total: res.values.count ?? 0,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          total: 0,
          list: [],
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        total: 0,
        list: [],
      });
    }
  });
};
