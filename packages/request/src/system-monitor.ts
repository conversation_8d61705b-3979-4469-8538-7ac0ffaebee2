/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getDuration } from '@waterdesk/data/system-kettle-log';
import { ServerInfo, SystemInfo } from '@waterdesk/data/system-monitor';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetServerStateResponse extends APIResponse {
  serverInfoList?: ServerInfo[];
  systemInfoList?: SystemInfo[];
}

export const getServerState = async (): Promise<GetServerStateResponse> => {
  const res: any = await postRequest({
    code: 'portal/getSystemRunningState',
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        const serverInfoList = Array.isArray(res?.host_list)
          ? res.host_list.map(
              (item: any): ServerInfo => ({
                id: item.host_id ?? '',
                totalMemory: item.total_memory ?? '',
                usedMemory: item?.used_memory ?? '',
                cpuLoad: item?.cpu_load ?? '',
                updateTime: item?.update_time ?? '',
              }),
            )
          : [];

        const systemInfoList = Array.isArray(res?.task_list)
          ? res.task_list.map(
              (item: any): SystemInfo => ({
                id: item.task_id ?? '',
                state: item.task_state ?? '',
                startTime: item?.start_time ?? '',
                executeTime: item?.execute_time ?? '',
                duration: getDuration(item.start_time, item.execute_time),
              }),
            )
          : [];
        resolve({
          status: 'Success',
          serverInfoList,
          systemInfoList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};
