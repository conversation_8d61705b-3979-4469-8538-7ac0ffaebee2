/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SystemRoleItem, SystemRoleList } from '@waterdesk/data/system-role';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetSystemRoleResponse extends APIResponse {
  roleList?: SystemRoleList;
}

export interface GetFunctionByRoleResponse extends APIResponse {
  functionIds?: string[];
}

export const getSystemRoleList = async (
  keyword?: string,
): Promise<GetSystemRoleResponse> => {
  const res: any = await postRequest({
    code: 'portal/getRoleList',
    params: {
      keyword,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const roleList: SystemRoleList = (
          Array.isArray(res.value?.roleList) ? res.value.roleList : []
        ).map(
          (item: any): SystemRoleItem => ({
            roleId: item.ROLE_ID ?? '',
            roleName: item.ROLE_NAME ?? '',
            roleDescription: item.ROLE_DESC ?? '',
          }),
        );
        resolve({
          status: 'Success',
          roleList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const addSystemRole = async (
  roleName: string,
  roleDescription?: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/createRole',
    params: {
      role_name: roleName,
      role_desc: roleDescription,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const deleteSystemRole = async (
  roleId: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/deleteRole',
    params: {
      role_id: roleId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const updateSystemRole = async (
  roleId: string,
  roleName: string,
  roleDescription?: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateRole',
    params: {
      role_id: roleId,
      role_name: roleName,
      role_desc: roleDescription,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getFunctionIdByRole = async (
  roleId: string,
): Promise<GetFunctionByRoleResponse> => {
  const res: any = await postRequest({
    code: 'portal/selectRoleAndFunctionByRoleId',
    params: {
      role_id: roleId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        const functionIds: string[] = (
          Array.isArray(res.values) ? res.values : []
        )
          .map((item: any): string => item.FUNC_ID ?? undefined)
          .filter((id: string | undefined) => id !== undefined);
        resolve({
          status: 'Success',
          functionIds,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const updateSystemRoleFunction = async (
  roleId: string,
  functionIds: string[],
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateFunctionRoleRelation',
    params: {
      role_id: roleId,
      func_id_list: functionIds.join(),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const getDataAccessByRole = async (
  roleId: string,
): Promise<{ list: string[] } & APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/getDataAccessById',
    params: {
      role_id: roleId,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
      list: res.values?.list ?? [],
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    list: [],
  };
};

export const updateDataAccessByRole = async (
  roleId: string,
  dataAccessList: string[],
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateDataAccess',
    params: {
      role_id: roleId,
      data_access_ids: dataAccessList.join() ?? '',
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
