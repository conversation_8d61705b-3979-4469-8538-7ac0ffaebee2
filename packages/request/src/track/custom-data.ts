/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  DownTrackWaterMeterData,
  getRangeValue,
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
  TrackScada,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import {
  HIGHLIGHT_TRACK,
  HighlightStyleType,
} from '@waterdesk/data/style-config';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { APIResponse } from '../api/api-response';
import { postRequestByView } from '../request';
import { formatDmaData, formatObjectData } from './get-track-data';

export interface GetCustomTrackDataResponse extends APIResponse {
  defaultHighlightDatas?: HighlightObject[];
  upTrack?: {
    upDmaDatas?: TrackDownDma[];
    upLinkDatasMap?: Map<string, TrackDownLink>;
    upLinkDatas?: HighlightObject[];
    upScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  downTrack?: {
    downDmaDatas?: TrackDownDma[];
    downLinkDatasMap?: Map<string, TrackDownLink>;
    downLinkDatas?: HighlightObject[];
    downScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  mapViewName?: string;
}

function formatValue(
  otypeProperty: PropertyInfo | undefined,
  vprop: string,
  value: string | number,
) {
  if (otypeProperty) {
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    if (unitKey) {
      const unit = getUnitFormat(unitKey);
      if (unit) {
        return unit.getValue(value) ?? '';
      }
    }
  }
  return value;
}

function formatLinksDataToMap(
  data: any[],
  db: Database,
  highlightStyleType?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, TrackDownLink> {
  const linkObjects: Map<string, TrackDownLink> = new Map();

  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const otypeProperty = db.getPropertyInfo(item.otype);
    const id = `${item.ROAD_NAME}@${item.DIAMETER}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: TrackDownLink = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      highlightIcon: icon,
      highlightType: highlightStyleType ?? HIGHLIGHT_TRACK,
      shape: item.shape,
      ratio: item.usage,
      highlightObjects: [],
      shapeType: getShapeType(item.shape),
      highlightColor: item.highlightColor,

      diameter: 0,
      length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
      FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
      VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
      UNITHEADLOSS: formatValue(
        otypeProperty,
        'UNITHEADLOSS',
        item.UNITHEADLOSS,
      ),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_V',
        item.CUMULATIVE_TIME_V,
      ),
      LAST_CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_V',
        item.LAST_CUMULATIVE_TIME_V,
      ),
      VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
      LAST_VELOCITY_Q: formatValue(
        otypeProperty,
        'LAST_VELOCITY_Q',
        item.LAST_VELOCITY_Q,
      ),
      DIRECT_FLOW: formatValue(otypeProperty, 'DIRECT_FLOW', item.DIRECT_FLOW),
      CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_F',
        item.CUMULATIVE_TIME_F,
      ),
      LAST_CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_F',
        item.LAST_CUMULATIVE_TIME_F,
      ),
      LAST_DIRECT_FLOW: formatValue(
        otypeProperty,
        'LAST_DIRECT_FLOW',
        item.LAST_DIRECT_FLOW,
      ),
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      DIAMETER: item.DIAMETER,
      USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
    };
    if (comboObject) {
      comboObject.length += item.LENGTH;
      comboObject.length = formatValue(
        otypeProperty,
        'LENGTH',
        comboObject.length,
      ) as number;
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        oname: item.oname,
        otype: item.otype,
        shape: item.shape,
        shapeType: getShapeType(item.shape),
        highlightObjects: [highlightObject],

        diameter: item.diameter,
        length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
        FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
        VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
        UNITHEADLOSS: formatValue(
          otypeProperty,
          'UNITHEADLOSS',
          item.UNITHEADLOSS,
        ),
        AGE: formatValue(otypeProperty, 'AGE', item.AGE),
        QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
        CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_V',
          item.CUMULATIVE_TIME_V,
        ),
        LAST_CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_V',
          item.LAST_CUMULATIVE_TIME_V,
        ),
        VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
        LAST_VELOCITY_Q: formatValue(
          otypeProperty,
          'LAST_VELOCITY_Q',
          item.LAST_VELOCITY_Q,
        ),
        DIRECT_FLOW: formatValue(
          otypeProperty,
          'DIRECT_FLOW',
          item.DIRECT_FLOW,
        ),
        CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_F',
          item.CUMULATIVE_TIME_F,
        ),
        LAST_CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_F',
          item.LAST_CUMULATIVE_TIME_F,
        ),
        LAST_DIRECT_FLOW: formatValue(
          otypeProperty,
          'LAST_DIRECT_FLOW',
          item.LAST_DIRECT_FLOW,
        ),
        ROAD_NAME: item.ROAD_NAME,
        LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        DIAMETER: item.DIAMETER,
        ratio: item.usage,
        USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
      });
    }
  });

  const newLinkObjects: Map<string, TrackDownLink> = new Map();

  linkObjects.forEach((item, key) => {
    const fieldRangeData = {
      ratio: getRangeValue(item.highlightObjects, 'USAGE'),
      shape: item.shape, // comboMutiLineString(item.highlightObjects),
      FLOW: getRangeValue(item.highlightObjects, 'FLOW'),
      FLOW_ABS: getRangeValue(item.highlightObjects, 'FLOW_ABS'),
      VELOCITY: getRangeValue(item.highlightObjects, 'VELOCITY'),
      UNITHEADLOSS: getRangeValue(item.highlightObjects, 'UNITHEADLOSS'),
      AGE: getRangeValue(item.highlightObjects, 'AGE'),
      QUALITY: getRangeValue(item.highlightObjects, 'QUALITY'),
      CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_V',
      ),
      LAST_CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_V',
      ),
      VELOCITY_Q: getRangeValue(item.highlightObjects, 'VELOCITY_Q'),
      LAST_VELOCITY_Q: getRangeValue(item.highlightObjects, 'LAST_VELOCITY_Q'),
      DIRECT_FLOW: getRangeValue(item.highlightObjects, 'DIRECT_FLOW'),
      CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_F',
      ),
      LAST_CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_F',
      ),
      LAST_DIRECT_FLOW: getRangeValue(
        item.highlightObjects,
        'LAST_DIRECT_FLOW',
      ),
      DIAMETER: getRangeValue(item.highlightObjects, 'DIAMETER'),
    };
    newLinkObjects.set(key, {
      oname: item.oname,
      otype: item.otype,
      id: item.id,
      highlightIcon: item.highlightIcon,
      highlightType: item.highlightType,
      highlightColor: '',
      shape: fieldRangeData.shape,
      ratio: fieldRangeData.ratio,
      highlightObjects: item.highlightObjects,
      shapeType: item.shapeType,

      diameter: 0,
      length: item.length,
      FLOW: fieldRangeData.FLOW,
      FLOW_ABS: fieldRangeData.FLOW_ABS,
      VELOCITY: fieldRangeData.VELOCITY,
      UNITHEADLOSS: fieldRangeData.UNITHEADLOSS,
      AGE: fieldRangeData.AGE,
      QUALITY: fieldRangeData.QUALITY,
      CUMULATIVE_TIME_V: fieldRangeData.CUMULATIVE_TIME_V,
      LAST_CUMULATIVE_TIME_V: fieldRangeData.LAST_CUMULATIVE_TIME_V,
      VELOCITY_Q: fieldRangeData.VELOCITY_Q,
      LAST_VELOCITY_Q: fieldRangeData.LAST_VELOCITY_Q,
      DIRECT_FLOW: fieldRangeData.DIRECT_FLOW,
      CUMULATIVE_TIME_F: fieldRangeData.CUMULATIVE_TIME_F,
      LAST_CUMULATIVE_TIME_F: fieldRangeData.LAST_CUMULATIVE_TIME_F,
      LAST_DIRECT_FLOW: fieldRangeData.LAST_DIRECT_FLOW,
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.length,
      DIAMETER: item.DIAMETER,
      USAGE: fieldRangeData.ratio,
    });
  });

  return newLinkObjects;
}

function formatWaterMeterData(
  data: any[],
  db: Database,
  _highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): DownTrackWaterMeterData[] {
  const highlightObjects: DownTrackWaterMeterData[] = [];
  data.forEach((item) => {
    const icon =
      item.highlightIcon ?? db.icons.get(item.otype) ?? highlightIcon;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: 'track',
      highlightColor: '',
      shapeType: getShapeType(item.shape),

      NAME: item.NAME,
      SOURCE_PERCENTAGE: formatValue(
        otypeProperty,
        'SOURCE_PERCENTAGE',
        item.source_percentage,
      ),
      ADDRESS: item.ADDRESS,
      BOOK_ID: item.BOOK_ID,
      FIRST_DMA_ID: item.FIRST_DMA_ID,
      CARD_STAT: formatValue(otypeProperty, 'CARD_STAT', item.CARD_STAT),
      USE_MODE: formatValue(otypeProperty, 'USE_MODE', item.USE_MODE),
      CUST_TYPE: formatValue(otypeProperty, 'CUST_TYPE', item.CUST_TYPE),
      DIAMETER: formatValue(otypeProperty, 'DIAMETER', item.DIAMETER),
      PAY_TYPE: formatValue(otypeProperty, 'PAY_TYPE', item.PAY_TYPE),
      SECOND_DMA_ID: item.SECOND_DMA_ID,
      THREE_DMA_ID: item.THREE_DMA_ID,
      USER_METER_TYPE: formatValue(
        otypeProperty,
        'USER_METER_TYPE',
        item.USER_METER_TYPE,
      ),
      TOTAL_FLOW: formatValue(otypeProperty, 'TOTAL_FLOW', item.TOTAL_FLOW),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
    });
  });
  return highlightObjects;
}

function formatScadaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): TrackScada[] {
  const highlightScadaObjects: TrackScada[] = [];
  data.forEach((item: any) => {
    const indicator = db.getIndicator(item.otype, item.oname);
    const otypeProperty = db.getPropertyInfo(item.otype);

    if (indicator?.ptype && indicator.pname) {
      const device = db.getDevice(indicator.ptype, indicator.pname);
      if (device) {
        highlightScadaObjects.push({
          oname: device.oname,
          otype: device.otype,
          id: device.id,
          shape: device.shape ?? '',
          highlightIcon: device.icon ?? highlightIcon,
          highlightType: highlightColor,
          shapeType: getShapeType(item.shape),
          title: device.title,
          scadaValue: formatValue(otypeProperty, 'SDVAL', item.sdval),
          usage: item.usage,
          modelValue: formatValue(otypeProperty, 'SDVAL', item.value),
          percent: item.source_percentage,
        });
      }
    }
  });
  return highlightScadaObjects;
}

function getResultData(
  data: any,
  highlightType: HighlightStyleType,
  db: Database,
) {
  const linkDatas = data?.links
    ? formatObjectData(data.links, highlightType)
    : [];

  const dmaDatas = data?.dma ? formatDmaData(data.dma, db, highlightType) : [];

  const waterMeterDatas = data?.watermeter
    ? formatWaterMeterData(data.watermeter, db, highlightType)
    : [];
  const linksObjectsMap = data?.links
    ? formatLinksDataToMap(data.links, db, highlightType)
    : new Map();

  const scadaDatas = data?.scada
    ? formatScadaData(data.scada, db, highlightType)
    : [];

  return {
    linkDatas,
    linksObjectsMap,
    dmaDatas,
    waterMeterDatas,
    scadaDatas,
  };
}

export async function getCustomTrack(
  otypeOnames: string,
  mergeType: string,
  direction: string,
  minDiameter: number,
  minFlow: number,
  db: Database,
  time?: string,
  // todo remove mapView
  mapView?: any,
): Promise<GetCustomTrackDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/traverseFlowUserDefine',
    params: {
      onames: otypeOnames,
      merge_mode: mergeType,
      dir_mode: direction,
      min_flow: minFlow,
      min_diameter: minDiameter,
      time,
      view_id: mapView?.getViewId(),
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        const resultData = {
          upTrack: getResultData(data.up_values, 'track', db),
          downTrack: getResultData(data.down_values, 'track', db),
        };

        const highlightObjects: HighlightObject[] = [
          ...resultData.upTrack.scadaDatas,
          ...resultData.downTrack.scadaDatas,
          ...resultData.upTrack.dmaDatas,
          ...resultData.downTrack.dmaDatas,
        ];

        const currentMapView = mapView;
        if (currentMapView) {
          const downLink: TrackDownLink[] = [];
          (resultData.downTrack.linksObjectsMap ?? new Map()).forEach(
            (item) => {
              downLink.push(...item.highlightObjects);
            },
          );

          const upLink: TrackDownLink[] = [];
          (resultData.upTrack.linksObjectsMap ?? new Map()).forEach((item) => {
            upLink.push(...item.highlightObjects);
          });

          currentMapView.trackHighlightObject = {
            downDma: resultData.downTrack.dmaDatas,
            upDma: resultData.upTrack.dmaDatas,
            downLink,
            upLink,
          };
        }

        resolve({
          status: 'Success',
          defaultHighlightDatas: highlightObjects,
          upTrack: {
            upScadaDatas: resultData.upTrack.scadaDatas,
            upLinkDatas: resultData.upTrack.linkDatas,
            upDmaDatas: resultData.upTrack.dmaDatas,
            upLinkDatasMap: resultData.upTrack.linksObjectsMap,
            waterMeterDatas: resultData.upTrack.waterMeterDatas,
          },
          downTrack: {
            downScadaDatas: resultData.downTrack.scadaDatas,
            downLinkDatas: resultData.downTrack.linkDatas,
            downDmaDatas: resultData.downTrack.dmaDatas,
            downLinkDatasMap: resultData.downTrack.linksObjectsMap,
            waterMeterDatas: resultData.downTrack.waterMeterDatas,
          },
          mapViewName: mapView?.mapViewName,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}
