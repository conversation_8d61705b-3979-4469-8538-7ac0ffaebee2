/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  DownTrackWaterMeterData,
  getRangeValue,
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
  TrackScada,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import {
  HIGHLIGHT_CUSTOM,
  HighlightStyleType,
} from '@waterdesk/data/style-config';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { APIResponse } from '../api/api-response';
import { formatValveAnalysisData } from '../get-valve-analysis-data';
import { postRequestByView } from '../request';

function formatValue(
  otypeProperty: PropertyInfo | undefined,
  vprop: string,
  value: string | number,
) {
  if (otypeProperty) {
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    if (unitKey) {
      const unit = getUnitFormat(unitKey);
      if (unit) {
        return unit.getValue(value) ?? '';
      }
    }
  }
  return value;
}

function formatLinksDataToMap(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, TrackDownLink> {
  const linkObjects: Map<string, TrackDownLink> = new Map();

  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);
    const id = `${item.ROAD_NAME}@${item.DIAMETER}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: TrackDownLink = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shape: item.shape,
      ratio: item.usage,
      highlightObjects: [],
      shapeType: getShapeType(item.shape),

      diameter: 0,
      length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
      FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
      VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
      UNITHEADLOSS: formatValue(
        otypeProperty,
        'UNITHEADLOSS',
        item.UNITHEADLOSS,
      ),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_V',
        item.CUMULATIVE_TIME_V,
      ),
      LAST_CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_V',
        item.LAST_CUMULATIVE_TIME_V,
      ),
      VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
      LAST_VELOCITY_Q: formatValue(
        otypeProperty,
        'LAST_VELOCITY_Q',
        item.LAST_VELOCITY_Q,
      ),
      DIRECT_FLOW: formatValue(otypeProperty, 'DIRECT_FLOW', item.DIRECT_FLOW),
      CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_F',
        item.CUMULATIVE_TIME_F,
      ),
      LAST_CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_F',
        item.LAST_CUMULATIVE_TIME_F,
      ),
      LAST_DIRECT_FLOW: formatValue(
        otypeProperty,
        'LAST_DIRECT_FLOW',
        item.LAST_DIRECT_FLOW,
      ),
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      DIAMETER: item.DIAMETER,
      USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
    };
    if (comboObject) {
      comboObject.length += item.LENGTH;
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        oname: item.oname,
        otype: item.otype,
        shape: item.shape,
        shapeType: getShapeType(item.shape),
        highlightObjects: [highlightObject],

        diameter: item.diameter,
        length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
        FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
        VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
        UNITHEADLOSS: formatValue(
          otypeProperty,
          'UNITHEADLOSS',
          item.UNITHEADLOSS,
        ),
        AGE: formatValue(otypeProperty, 'AGE', item.AGE),
        QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
        CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_V',
          item.CUMULATIVE_TIME_V,
        ),
        LAST_CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_V',
          item.LAST_CUMULATIVE_TIME_V,
        ),
        VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
        LAST_VELOCITY_Q: formatValue(
          otypeProperty,
          'LAST_VELOCITY_Q',
          item.LAST_VELOCITY_Q,
        ),
        DIRECT_FLOW: formatValue(
          otypeProperty,
          'DIRECT_FLOW',
          item.DIRECT_FLOW,
        ),
        CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_F',
          item.CUMULATIVE_TIME_F,
        ),
        LAST_CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_F',
          item.LAST_CUMULATIVE_TIME_F,
        ),
        LAST_DIRECT_FLOW: formatValue(
          otypeProperty,
          'LAST_DIRECT_FLOW',
          item.LAST_DIRECT_FLOW,
        ),
        ROAD_NAME: item.ROAD_NAME,
        LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        DIAMETER: item.DIAMETER,
        ratio: item.usage,
        USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
      });
    }
  });

  const newLinkObjects: Map<string, TrackDownLink> = new Map();

  linkObjects.forEach((item, key) => {
    const fieldRangeData = {
      ratio: getRangeValue(item.highlightObjects, 'USAGE'),
      shape: item.shape, // comboMutiLineString(item.highlightObjects),
      FLOW: getRangeValue(item.highlightObjects, 'FLOW'),
      FLOW_ABS: getRangeValue(item.highlightObjects, 'FLOW_ABS'),
      VELOCITY: getRangeValue(item.highlightObjects, 'VELOCITY'),
      UNITHEADLOSS: getRangeValue(item.highlightObjects, 'UNITHEADLOSS'),
      AGE: getRangeValue(item.highlightObjects, 'AGE'),
      QUALITY: getRangeValue(item.highlightObjects, 'QUALITY'),
      CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_V',
      ),
      LAST_CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_V',
      ),
      VELOCITY_Q: getRangeValue(item.highlightObjects, 'VELOCITY_Q'),
      LAST_VELOCITY_Q: getRangeValue(item.highlightObjects, 'LAST_VELOCITY_Q'),
      DIRECT_FLOW: getRangeValue(item.highlightObjects, 'DIRECT_FLOW'),
      CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_F',
      ),
      LAST_CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_F',
      ),
      LAST_DIRECT_FLOW: getRangeValue(
        item.highlightObjects,
        'LAST_DIRECT_FLOW',
      ),
      DIAMETER: getRangeValue(item.highlightObjects, 'DIAMETER'),
    };
    newLinkObjects.set(key, {
      oname: item.oname,
      otype: item.otype,
      id: item.id,
      highlightIcon: item.highlightIcon,
      highlightType: item.highlightType,
      highlightColor: '',
      shape: fieldRangeData.shape,
      ratio: fieldRangeData.ratio,
      highlightObjects: item.highlightObjects,
      shapeType: item.shapeType,

      diameter: 0,
      length: item.length,
      FLOW: fieldRangeData.FLOW,
      FLOW_ABS: fieldRangeData.FLOW_ABS,
      VELOCITY: fieldRangeData.VELOCITY,
      UNITHEADLOSS: fieldRangeData.UNITHEADLOSS,
      AGE: fieldRangeData.AGE,
      QUALITY: fieldRangeData.QUALITY,
      CUMULATIVE_TIME_V: fieldRangeData.CUMULATIVE_TIME_V,
      LAST_CUMULATIVE_TIME_V: fieldRangeData.LAST_CUMULATIVE_TIME_V,
      VELOCITY_Q: fieldRangeData.VELOCITY_Q,
      LAST_VELOCITY_Q: fieldRangeData.LAST_VELOCITY_Q,
      DIRECT_FLOW: fieldRangeData.DIRECT_FLOW,
      CUMULATIVE_TIME_F: fieldRangeData.CUMULATIVE_TIME_F,
      LAST_CUMULATIVE_TIME_F: fieldRangeData.LAST_CUMULATIVE_TIME_F,
      LAST_DIRECT_FLOW: fieldRangeData.LAST_DIRECT_FLOW,
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.LENGTH,
      DIAMETER: item.DIAMETER,
      USAGE: fieldRangeData.ratio,
    });
  });

  return newLinkObjects;
}

function formatDmaData(
  data: any[],
  db: Database,
  highlightStyleType?: HighlightStyleType,
  highlightIcon?: string,
): TrackDownDma[] {
  const highlightObjects: TrackDownDma[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: highlightStyleType ?? HIGHLIGHT_CUSTOM,
      highlightColor: item.highlightColor,
      shapeType: getShapeType(item.shape),

      SOURCE_PERCENTAGE: formatValue(
        otypeProperty,
        'SOURCE_PERCENTAGE',
        item.source_percentage,
      ),
      TITLE: formatValue(otypeProperty, 'TITLE', item.TITLE),
      POPULATION: formatValue(otypeProperty, 'POPULATION', item.POPULATION),
      AREA: formatValue(otypeProperty, 'AREA', item.AREA),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
      USER_METER_COUNT: formatValue(
        otypeProperty,
        'USER_METER_COUNT',
        item.USER_METER_COUNT,
      ),
      MAX_FLOW_TIME: formatValue(
        otypeProperty,
        'MAX_FLOW_TIME',
        item.MAX_FLOW_TIME,
      ),
      MIN_NMF_TIME: formatValue(
        otypeProperty,
        'MIN_NMF_TIME',
        item.MIN_NMF_TIME,
      ),
      MIN_NMF: formatValue(otypeProperty, 'MIN_NMF', item.MIN_NMF),
      AVERAGE_NMF: formatValue(otypeProperty, 'AVERAGE_NMF', item.AVERAGE_NMF),
      IN_FLOW_DAY: formatValue(otypeProperty, 'IN_FLOW_DAY', item.IN_FLOW_DAY),
      OUT_FLOW_DAY: formatValue(
        otypeProperty,
        'OUT_FLOW_DAY',
        item.OUT_FLOW_DAY,
      ),
      MAX_FLOW: formatValue(otypeProperty, 'MAX_FLOW', item.MAX_FLOW),
      AVERAGE_FLOW: formatValue(
        otypeProperty,
        'AVERAGE_FLOW',
        item.AVERAGE_FLOW,
      ),
      AVERAGE_PRESSURE: formatValue(
        otypeProperty,
        'AVERAGE_PRESSURE',
        item.AVERAGE_PRESSURE,
      ),
      DAY_FLOW: formatValue(otypeProperty, 'DAY_FLOW', item.DAY_FLOW),
      ABNORMAL_STATUS: formatValue(
        otypeProperty,
        'ABNORMAL_STATUS',
        item.ABNORMAL_STATUS,
      ),
      IN_FLOW: formatValue(otypeProperty, 'IN_FLOW', item.IN_FLOW),
      OUT_FLOW: formatValue(otypeProperty, 'OUT_FLOW', item.OUT_FLOW),
      TOTAL_FLOW: formatValue(otypeProperty, 'TOTAL_FLOW', item.TOTAL_FLOW),
      WATERMETER_NUM: formatValue(
        otypeProperty,
        'WATERMETER_NUM',
        item.WATERMETER_NUM,
      ),
      REALFLOW: formatValue(otypeProperty, 'REALFLOW', item.REALFLOW),
    });
  });
  return highlightObjects;
}

function formatWaterMeterData(
  data: any[],
  db: Database,
  _highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): DownTrackWaterMeterData[] {
  const highlightObjects: DownTrackWaterMeterData[] = [];
  data.forEach((item) => {
    const icon =
      item.highlightIcon ?? db.icons.get(item.otype) ?? highlightIcon;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: 'track',
      highlightColor: '',
      shapeType: getShapeType(item.shape),

      NAME: item.NAME,
      SOURCE_PERCENTAGE: formatValue(
        otypeProperty,
        'SOURCE_PERCENTAGE',
        item.source_percentage,
      ),
      ADDRESS: item.ADDRESS,
      BOOK_ID: item.BOOK_ID,
      FIRST_DMA_ID: item.FIRST_DMA_ID,
      CARD_STAT: formatValue(otypeProperty, 'CARD_STAT', item.CARD_STAT),
      USE_MODE: formatValue(otypeProperty, 'USE_MODE', item.USE_MODE),
      CUST_TYPE: formatValue(otypeProperty, 'CUST_TYPE', item.CUST_TYPE),
      DIAMETER: formatValue(otypeProperty, 'DIAMETER', item.DIAMETER),
      PAY_TYPE: formatValue(otypeProperty, 'PAY_TYPE', item.PAY_TYPE),
      SECOND_DMA_ID: item.SECOND_DMA_ID,
      THREE_DMA_ID: item.THREE_DMA_ID,
      USER_METER_TYPE: formatValue(
        otypeProperty,
        'USER_METER_TYPE',
        item.USER_METER_TYPE,
      ),
      TOTAL_FLOW: formatValue(otypeProperty, 'TOTAL_FLOW', item.TOTAL_FLOW),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
    });
  });
  return highlightObjects;
}

function formatScadaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
) {
  const highlightScadaObjects: TrackScada[] = [];
  data.forEach((item: any) => {
    const indicator = db.getIndicator(item.otype, item.oname);
    const otypeProperty = db.getPropertyInfo(item.otype);

    if (indicator?.ptype && indicator.pname) {
      const device = db.getDevice(indicator.ptype, indicator.pname);
      if (device) {
        highlightScadaObjects.push({
          oname: device.oname,
          otype: device.otype,
          id: device.id,
          indicatorType: indicator.otype,
          indicatorName: indicator.oname,
          indicatorId: indicator.id,
          shape: device.shape ?? '',
          highlightIcon: device.icon ?? highlightIcon,
          highlightType: highlightColor,
          shapeType: getShapeType(item.shape),
          title: device.title,
          scadaValue: formatValue(otypeProperty, 'SDVAL', item.sdval),
          usage: item.usage,
          modelValue: formatValue(otypeProperty, 'SDVAL', item.value),
          percent: item.source_percentage,
        });
      }
    }
  });
  return highlightScadaObjects;
}

function getResultData(
  data: any,
  db: Database,
  highlightType?: HighlightStyleType,
) {
  const downLinkDatas = data?.links
    ? formatValveAnalysisData(data.links, highlightType)
    : [];
  const downDmaDatas = data?.dma3
    ? formatDmaData(data.dma3, db, highlightType)
    : [];
  const downWaterMeterDatas = data?.watermeter
    ? formatWaterMeterData(data.watermeter, db, highlightType)
    : [];
  const downLinkDatasMap = data?.links
    ? formatLinksDataToMap(data.links, db, highlightType)
    : new Map();

  const downScadaDatas = data?.scada
    ? formatScadaData(data.scada, db, highlightType)
    : [];

  return {
    downLinkDatas,
    downDmaDatas,
    downLinkDatasMap,
    downWaterMeterDatas,
    downScadaDatas,
  };
}

export interface GetTrackDownDataResponse extends APIResponse {
  downDmaDatas?: TrackDownDma[];
  downLinkDatasMap?: Map<string, TrackDownLink>;
  downLinkDatas?: HighlightObject[];
  downScadaDatas?: TrackScada[];
  defaultHighlightDatas?: HighlightObject[];
  downWaterMeterDatas?: HighlightObject[];
  mapViewName?: string;
}

export async function getDownTrack(
  oname: string,
  otype: string,
  minDiameter: number,
  minFlow: number,
  db: Database,
  time?: string,
  // todo: remove mapView
  mapView?: any,
): Promise<GetTrackDownDataResponse> {
  const currentMapView = mapView;
  const data: any = await postRequestByView({
    code: 'supply/traverseFlowDownUsage',
    params: {
      time,
      oname,
      otype,
      min_flow: minFlow,
      min_diameter: minDiameter,
      view_id: mapView?.getViewId(),
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        const resultData = getResultData(data.values, db, HIGHLIGHT_CUSTOM);

        const highlightObjects: HighlightObject[] = [
          ...resultData.downScadaDatas,
          ...resultData.downDmaDatas,
        ];

        if (currentMapView) {
          const downImpactedLinksAllObjects: TrackDownLink[] = [];
          (resultData.downLinkDatasMap ?? new Map()).forEach((item) => {
            downImpactedLinksAllObjects.push(...item.highlightObjects);
          });

          currentMapView.trackHighlightObject = {
            waterMeter: resultData.downWaterMeterDatas,
            downDma: resultData.downDmaDatas,
            downLink: downImpactedLinksAllObjects,
          };
        }

        resolve({
          status: 'Success',
          defaultHighlightDatas: highlightObjects,
          downScadaDatas: resultData.downScadaDatas,
          downLinkDatas: resultData.downLinkDatas,
          downDmaDatas: resultData.downDmaDatas,
          downLinkDatasMap: resultData.downLinkDatasMap,
          downWaterMeterDatas: resultData.downWaterMeterDatas,
          mapViewName: mapView?.mapViewName,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}
