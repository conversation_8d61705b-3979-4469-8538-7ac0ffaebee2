/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  HighlightObject,
  ImpactedDma,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import { HighlightStyleType } from '@waterdesk/data/style-config';
import {
  TRACK_CUSTOM,
  TRACK_DOWN,
  TRACK_POLLUTION,
  TRACK_UP,
  TrackType,
} from '@waterdesk/data/track-data';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { APIResponse } from '../api/api-response';
import { postRequestByView } from '../request';

export interface GetTrackDataResponse extends APIResponse {
  trackedLinks?: HighlightObject[];
}

export interface GetRecommendResponse extends APIResponse {
  flow: number;
  diameter: number;
}

export function formatObjectData(
  data: any[],
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): HighlightObject[] | ImpactedDma[] {
  const highlightObjects: HighlightObject[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shapeType: getShapeType(item.shape),
    });
  });
  return highlightObjects;
}

export async function getTrackData(
  direction: string,
  otypeOnames: string,
  minDiameter: number,
  minFlow: number,
): Promise<GetTrackDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/traversePipeFlowDirection',
    params: {
      src_oname: otypeOnames,
      min_diameter: minDiameter,
      min_flow: minFlow,
      flow_dir: direction,
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (!Array.isArray(data.values.links) && data.values.links !== null) {
          throw Error(`数据格式有误`);
        }

        const highlightObjects: HighlightObject[] = [];
        data.values.links.forEach((item: any) => {
          highlightObjects.push({
            oname: item.oname,
            otype: item.otype,
            id: makeObjectId(item.otype, item.oname),
            shape: item.shape,
            highlightIcon: item.highlightIcon,
            highlightType: 'track',
            shapeType: getShapeType(item.shape),
          });
        });
        resolve({
          status: 'Success',
          trackedLinks: highlightObjects,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

export async function resetHighlight(
  time?: string,
  viewId?: string,
): Promise<APIResponse> {
  const data: any = await postRequestByView({
    code: 'watergis/clearThemeDisable',
    params: {
      time,
      view_id: viewId,
    },
  });
  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

function formatValue(
  otypeProperty: PropertyInfo | undefined,
  vprop: string,
  value: string | number,
) {
  if (otypeProperty) {
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    if (unitKey) {
      const unit = getUnitFormat(unitKey);
      if (unit) {
        return unit.getValue(value) ?? '';
      }
    }
  }
  return value;
}

function getRangeValue(data: any, field: keyof TrackDownLink): string {
  let minValue = 0;
  let maxValue = 0;
  data.forEach((item: { [x: string]: number }) => {
    if (item[field] && minValue > item[field]) {
      minValue = item[field];
    }
    if (item[field] && maxValue < item[field]) {
      maxValue = item[field];
    }
  });
  return `${minValue}~${maxValue}`;
}

export function formatLinksDataToMap(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, TrackDownLink> {
  const linkObjects: Map<string, TrackDownLink> = new Map();

  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);
    const id = `${item.ROAD_NAME}@${item.DIAMETER}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: TrackDownLink = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shape: item.shape,
      ratio: item.usage,
      highlightObjects: [],
      shapeType: getShapeType(item.shape),

      diameter: 0,
      length: 0,
      USAGE: '',
      FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
      FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
      VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
      UNITHEADLOSS: formatValue(
        otypeProperty,
        'UNITHEADLOSS',
        item.UNITHEADLOSS,
      ),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_V',
        item.CUMULATIVE_TIME_V,
      ),
      LAST_CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_V',
        item.LAST_CUMULATIVE_TIME_V,
      ),
      VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
      LAST_VELOCITY_Q: formatValue(
        otypeProperty,
        'LAST_VELOCITY_Q',
        item.LAST_VELOCITY_Q,
      ),
      DIRECT_FLOW: formatValue(otypeProperty, 'DIRECT_FLOW', item.DIRECT_FLOW),
      CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_F',
        item.CUMULATIVE_TIME_F,
      ),
      LAST_CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_F',
        item.LAST_CUMULATIVE_TIME_F,
      ),
      LAST_DIRECT_FLOW: formatValue(
        otypeProperty,
        'LAST_DIRECT_FLOW',
        item.LAST_DIRECT_FLOW,
      ),
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.LENGTH,
      DIAMETER: item.DIAMETER,
    };
    if (comboObject) {
      comboObject.length += item.LENGTH;
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        oname: item.oname,
        otype: item.otype,
        shape: item.shape,
        shapeType: getShapeType(item.shape),
        highlightObjects: [highlightObject],

        diameter: item.diameter,
        length: item.LENGTH,
        USAGE: '',
        FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
        FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
        VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
        UNITHEADLOSS: formatValue(
          otypeProperty,
          'UNITHEADLOSS',
          item.UNITHEADLOSS,
        ),
        AGE: formatValue(otypeProperty, 'AGE', item.AGE),
        QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
        CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_V',
          item.CUMULATIVE_TIME_V,
        ),
        LAST_CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_V',
          item.LAST_CUMULATIVE_TIME_V,
        ),
        VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
        LAST_VELOCITY_Q: formatValue(
          otypeProperty,
          'LAST_VELOCITY_Q',
          item.LAST_VELOCITY_Q,
        ),
        DIRECT_FLOW: formatValue(
          otypeProperty,
          'DIRECT_FLOW',
          item.DIRECT_FLOW,
        ),
        CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_F',
          item.CUMULATIVE_TIME_F,
        ),
        LAST_CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_F',
          item.LAST_CUMULATIVE_TIME_F,
        ),
        LAST_DIRECT_FLOW: formatValue(
          otypeProperty,
          'LAST_DIRECT_FLOW',
          item.LAST_DIRECT_FLOW,
        ),
        ROAD_NAME: item.ROAD_NAME,
        LENGTH: item.LENGTH,
        DIAMETER: item.DIAMETER,
        ratio: item.usage,
      });
    }
  });

  const newLinkObjects: Map<string, TrackDownLink> = new Map();

  linkObjects.forEach((item, key) => {
    const fieldRangeData = {
      ratio: getRangeValue(item.highlightObjects, 'USAGE'),
      shape: item.shape, // comboMutiLineString(item.highlightObjects),
      FLOW: getRangeValue(item.highlightObjects, 'FLOW'),
      FLOW_ABS: getRangeValue(item.highlightObjects, 'FLOW_ABS'),
      VELOCITY: getRangeValue(item.highlightObjects, 'VELOCITY'),
      UNITHEADLOSS: getRangeValue(item.highlightObjects, 'UNITHEADLOSS'),
      AGE: getRangeValue(item.highlightObjects, 'AGE'),
      QUALITY: getRangeValue(item.highlightObjects, 'QUALITY'),
      CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_V',
      ),
      LAST_CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_V',
      ),
      VELOCITY_Q: getRangeValue(item.highlightObjects, 'VELOCITY_Q'),
      LAST_VELOCITY_Q: getRangeValue(item.highlightObjects, 'LAST_VELOCITY_Q'),
      DIRECT_FLOW: getRangeValue(item.highlightObjects, 'DIRECT_FLOW'),
      CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_F',
      ),
      LAST_CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_F',
      ),
      LAST_DIRECT_FLOW: getRangeValue(
        item.highlightObjects,
        'LAST_DIRECT_FLOW',
      ),
      DIAMETER: getRangeValue(item.highlightObjects, 'DIAMETER'),
    };
    newLinkObjects.set(key, {
      oname: item.oname,
      otype: item.otype,
      id: item.id,
      highlightIcon: item.highlightIcon,
      highlightType: item.highlightType,
      highlightColor: '',
      shape: fieldRangeData.shape,
      ratio: fieldRangeData.ratio,
      highlightObjects: item.highlightObjects,
      shapeType: item.shapeType,

      diameter: 0,
      length: item.length,
      USAGE: '',
      FLOW: fieldRangeData.FLOW,
      FLOW_ABS: fieldRangeData.FLOW_ABS,
      VELOCITY: fieldRangeData.VELOCITY,
      UNITHEADLOSS: fieldRangeData.UNITHEADLOSS,
      AGE: fieldRangeData.AGE,
      QUALITY: fieldRangeData.QUALITY,
      CUMULATIVE_TIME_V: fieldRangeData.CUMULATIVE_TIME_V,
      LAST_CUMULATIVE_TIME_V: fieldRangeData.LAST_CUMULATIVE_TIME_V,
      VELOCITY_Q: fieldRangeData.VELOCITY_Q,
      LAST_VELOCITY_Q: fieldRangeData.LAST_VELOCITY_Q,
      DIRECT_FLOW: fieldRangeData.DIRECT_FLOW,
      CUMULATIVE_TIME_F: fieldRangeData.CUMULATIVE_TIME_F,
      LAST_CUMULATIVE_TIME_F: fieldRangeData.LAST_CUMULATIVE_TIME_F,
      LAST_DIRECT_FLOW: fieldRangeData.LAST_DIRECT_FLOW,
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.length,
      DIAMETER: item.DIAMETER,
    });
  });

  return newLinkObjects;
}

export function formatDmaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): TrackDownDma[] {
  const highlightObjects: TrackDownDma[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'custom',
      highlightColor: '',
      shapeType: getShapeType(item.shape),

      TITLE: formatValue(otypeProperty, 'TITLE', item.TITLE),
      POPULATION: formatValue(otypeProperty, 'POPULATION', item.POPULATION),
      AREA: formatValue(otypeProperty, 'AREA', item.AREA),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
      USER_METER_COUNT: formatValue(
        otypeProperty,
        'USER_METER_COUNT',
        item.USER_METER_COUNT,
      ),
      MAX_FLOW_TIME: formatValue(
        otypeProperty,
        'MAX_FLOW_TIME',
        item.MAX_FLOW_TIME,
      ),
      MIN_NMF_TIME: formatValue(
        otypeProperty,
        'MIN_NMF_TIME',
        item.MIN_NMF_TIME,
      ),
      MIN_NMF: formatValue(otypeProperty, 'MIN_NMF', item.MIN_NMF),
      AVERAGE_NMF: formatValue(otypeProperty, 'AVERAGE_NMF', item.AVERAGE_NMF),
      IN_FLOW_DAY: formatValue(otypeProperty, 'IN_FLOW_DAY', item.IN_FLOW_DAY),
      OUT_FLOW_DAY: formatValue(
        otypeProperty,
        'OUT_FLOW_DAY',
        item.OUT_FLOW_DAY,
      ),
      MAX_FLOW: formatValue(otypeProperty, 'MAX_FLOW', item.MAX_FLOW),
      AVERAGE_FLOW: formatValue(
        otypeProperty,
        'AVERAGE_FLOW',
        item.AVERAGE_FLOW,
      ),
      AVERAGE_PRESSURE: formatValue(
        otypeProperty,
        'AVERAGE_PRESSURE',
        item.AVERAGE_PRESSURE,
      ),
      DAY_FLOW: formatValue(otypeProperty, 'DAY_FLOW', item.DAY_FLOW),
      ABNORMAL_STATUS: formatValue(
        otypeProperty,
        'ABNORMAL_STATUS',
        item.ABNORMAL_STATUS,
      ),
      IN_FLOW: formatValue(otypeProperty, 'IN_FLOW', item.IN_FLOW),
      OUT_FLOW: formatValue(otypeProperty, 'OUT_FLOW', item.OUT_FLOW),
      WATERMETER_NUM: formatValue(
        otypeProperty,
        'WATERMETER_NUM',
        item.WATERMETER_NUM,
      ),
      REALFLOW: formatValue(otypeProperty, 'REALFLOW', item.REALFLOW),
    });
  });
  return highlightObjects;
}

export function getImpactedData(
  data: any,
  db: Database,
  highlightType: HighlightStyleType,
) {
  const impactedLinksObjects = data?.links
    ? formatObjectData(data.links, highlightType)
    : [];
  const impactedDmaObjects = data?.dma3
    ? formatDmaData(data.dma3, db, highlightType)
    : [];
  const impactedLinksObjectsMap = data?.links
    ? formatLinksDataToMap(data.links, db, highlightType)
    : new Map();
  return {
    impactedLinksObjects,
    impactedDmaObjects,
    impactedLinksObjectsMap,
  };
}

export interface GetCustomTrackDataResponse extends APIResponse {
  defaultHighlightDatas?: HighlightObject[];
  upTrack?: {
    impactedDmaObjects?: TrackDownDma[];
    impactedLinksObjectsMap?: Map<string, TrackDownLink>;
    impactedLinksObjects?: HighlightObject[];
    impactedScadas?: HighlightObject[];
  };
  downTrack?: {
    impactedDmaObjects?: TrackDownDma[];
    impactedLinksObjectsMap?: Map<string, TrackDownLink>;
    impactedLinksObjects?: HighlightObject[];
    impactedScadas?: HighlightObject[];
  };
}

export async function getCustomTrack(
  otypeOnames: string,
  mergeType: string,
  direction: string,
  minDiameter: number,
  minFlow: number,
  db: Database,
): Promise<GetCustomTrackDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/traverseFlowUserDefine',
    params: {
      onames: otypeOnames,
      merge_mode: mergeType,
      dir_mode: direction,
      min_flow: minFlow,
      min_diameter: minDiameter,
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        const resultData = {
          upTrack: getImpactedData(data.up_values, db, 'track'),
          downTrack: getImpactedData(data.down_values, db, 'track'),
        };

        const highlightScadaObjects: HighlightObject[] = [];
        data.up_values?.scada.forEach((item: any) => {
          const device = db.getDevice(item.otype, item.oname);
          if (device) {
            highlightScadaObjects.push({
              oname: item.oname,
              otype: item.otype,
              id: device.id,
              shape: device.shape ?? '',
              highlightIcon: device.highlightIcon,
              highlightType: 'track',
              shapeType: getShapeType(item.shape),
            });
          }
        });

        const highlightObjects: HighlightObject[] = [
          ...highlightScadaObjects,
          ...resultData.upTrack.impactedDmaObjects,
          ...resultData.downTrack.impactedDmaObjects,
        ];

        resolve({
          status: 'Success',
          defaultHighlightDatas: highlightObjects,
          upTrack: {
            impactedScadas: highlightScadaObjects,
            impactedLinksObjects: resultData.upTrack.impactedLinksObjects,
            impactedDmaObjects: resultData.upTrack.impactedDmaObjects,
            impactedLinksObjectsMap: resultData.upTrack.impactedLinksObjectsMap,
          },
          downTrack: {
            impactedScadas: highlightScadaObjects,
            impactedLinksObjects: resultData.downTrack.impactedLinksObjects,
            impactedDmaObjects: resultData.downTrack.impactedDmaObjects,
            impactedLinksObjectsMap:
              resultData.downTrack.impactedLinksObjectsMap,
          },
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

function getRecommendTrackType(trackType: TrackType): string {
  switch (trackType) {
    case TRACK_UP:
      return 'traverseFlowUpUsage';
    case TRACK_POLLUTION:
      return 'traverseFlowUpQual';
    case TRACK_CUSTOM:
      return 'traverseFlowUserDefine';
    case TRACK_DOWN:
    default:
      return 'traverseFlowDownUsage';
  }
}

export async function getRecommendByTrack(
  otype: string,
  oname: string,
  trackType: TrackType,
  time?: string,
  dataMode?: string,
): Promise<GetRecommendResponse> {
  const res: any = await postRequestByView({
    code: 'supply/recommendFlowByTrav',
    params: {
      otype,
      oname,
      trav_mode: getRecommendTrackType(trackType),
      time,
      data_mode: dataMode,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
      flow: res?.values?.flow,
      diameter: res?.values?.diameter,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
    flow: 0,
    diameter: 0,
  };
}
