/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  DownTrackWaterMeterData,
  getRangeValue,
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
  TrackScada,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import { HighlightStyleType } from '@waterdesk/data/style-config';
import {
  convertMapToSerializable,
  convertSerializableToMap,
  ResultData,
} from '@waterdesk/data/track-data';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { APIResponse } from '../api/api-response';
import { GetAsyncTaskStatusResponse } from '../get-async-task';
import { postRequestByView } from '../request';
import { formatObjectData } from './get-track-data';

export interface PollutionTrackData {
  defaultHighlightDatas?: HighlightObject[];
  pollutedSource?: {
    pollutedSourceDmaDatas?: TrackDownDma[];
    pollutedSourceLinkDatasMap?: Map<string, TrackDownLink>;
    pollutedSourceLinkDatas?: HighlightObject[];
    pollutedSourceScadaDatas?: HighlightObject[];
    waterMeterDatas?: HighlightObject[];
  };
  polluted?: {
    pollutedDmaDatas?: TrackDownDma[];
    pollutedLinkDatasMap?: Map<string, TrackDownLink>;
    pollutedLinkDatas?: HighlightObject[];
    pollutedScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  unpolluted?: {
    unpollutedDmaDatas?: TrackDownDma[];
    unpollutedLinkDatasMap?: Map<string, TrackDownLink>;
    unpollutedLinkDatas?: HighlightObject[];
    unpollutedScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  mapViewName?: string;
}

export interface GetPollutionTrackDataAsyncResponse
  extends GetAsyncTaskStatusResponse<PollutionTrackData> {}

export interface GetPollutionTrackDataResponse extends APIResponse {
  defaultHighlightDatas?: HighlightObject[];
  pollutedSource?: {
    pollutedSourceDmaDatas?: TrackDownDma[];
    pollutedSourceLinkDatasMap?: Map<string, TrackDownLink>;
    pollutedSourceLinkDatas?: HighlightObject[];
    pollutedSourceScadaDatas?: HighlightObject[];
    waterMeterDatas?: HighlightObject[];
  };
  polluted?: {
    pollutedDmaDatas?: TrackDownDma[];
    pollutedLinkDatasMap?: Map<string, TrackDownLink>;
    pollutedLinkDatas?: HighlightObject[];
    pollutedScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  unpolluted?: {
    unpollutedDmaDatas?: TrackDownDma[];
    unpollutedLinkDatasMap?: Map<string, TrackDownLink>;
    unpollutedLinkDatas?: HighlightObject[];
    unpollutedScadaDatas?: TrackScada[];
    waterMeterDatas?: HighlightObject[];
  };
  mapViewName?: string;
}

export interface GetPollutionSpreadResponse extends APIResponse {
  taskId?: string;
}

export interface SavePollutionSpreadAnalysisRequest {
  /** 方案ID */
  solutionId: string;
  /** 方案GUID */
  solutionGuid: string;
  /** 污染源对象列表 */
  objects: Array<{
    id: string;
    type: string;
  }>;
  /** 污染注入开始时间 */
  pollutionStartTime: string;
  /** 污染注入结束时间 */
  pollutionEndTime: string;
  /** 模拟结束时间 */
  simulationEndTime: string;
  /** 模拟扩散时间 */
  diffusionTime: string;
  /** 分析标题 */
  analysisTitle: string;
  /** 备注 */
  analysisNote?: string;
  /** 分析结果 */
  analysisResult: ResultData[];
  /** 计算任务ID */
  calcId?: string;
  /** 计算完成时间 */
  calcTime?: string;
}

export interface SavePollutionSpreadAnalysisResponse extends APIResponse {
  data?: {
    solution_id: string;
  };
}

export interface GetPollutionSpreadAnalysisDetailResponse extends APIResponse {
  /** 方案ID */
  solutionId?: string;
  /** 方案GUID */
  solutionGuid?: string;
  /** 污染源对象列表 */
  objects?: Array<{
    id: string;
    type: string;
  }>;
  /** 污染注入开始时间 */
  pollutionStartTime?: string;
  /** 污染注入结束时间 */
  pollutionEndTime?: string;
  /** 模拟结束时间 */
  simulationEndTime?: string;
  /** 模拟扩散时间 */
  diffusionTime?: string;
  /** 分析标题 */
  analysisTitle?: string;
  /** 备注 */
  analysisNote?: string;
  /** 分析结果 */
  analysisResult?: any;
  /** 计算任务ID */
  calcId?: string;
  /** 计算完成时间 */
  calcTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 已保存的时间点列表 */
  savedTimes?: string[];
}

function formatValue(
  otypeProperty: PropertyInfo | undefined,
  vprop: string,
  value: string | number,
) {
  if (otypeProperty) {
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    if (unitKey) {
      const unit = getUnitFormat(unitKey);
      if (unit) {
        return unit.getValue(value) ?? '';
      }
    }
  }
  return value;
}

function formatLinksDataToMap(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, TrackDownLink> {
  const linkObjects: Map<string, TrackDownLink> = new Map();

  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);
    const id = `${item.ROAD_NAME}@${item.DIAMETER}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: TrackDownLink = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shape: item.shape,
      ratio: item.usage,
      highlightObjects: [],
      shapeType: getShapeType(item.shape),

      diameter: 0,
      length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
      FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
      VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
      UNITHEADLOSS: formatValue(
        otypeProperty,
        'UNITHEADLOSS',
        item.UNITHEADLOSS,
      ),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_V',
        item.CUMULATIVE_TIME_V,
      ),
      LAST_CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_V',
        item.LAST_CUMULATIVE_TIME_V,
      ),
      VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
      LAST_VELOCITY_Q: formatValue(
        otypeProperty,
        'LAST_VELOCITY_Q',
        item.LAST_VELOCITY_Q,
      ),
      DIRECT_FLOW: formatValue(otypeProperty, 'DIRECT_FLOW', item.DIRECT_FLOW),
      CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_F',
        item.CUMULATIVE_TIME_F,
      ),
      LAST_CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_F',
        item.LAST_CUMULATIVE_TIME_F,
      ),
      LAST_DIRECT_FLOW: formatValue(
        otypeProperty,
        'LAST_DIRECT_FLOW',
        item.LAST_DIRECT_FLOW,
      ),
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      DIAMETER: item.DIAMETER,
      USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
    };
    if (comboObject) {
      comboObject.length += item.LENGTH;
      comboObject.length = formatValue(
        otypeProperty,
        'LENGTH',
        comboObject.length,
      ) as number;
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        oname: item.oname,
        otype: item.otype,
        shape: item.shape,
        shapeType: getShapeType(item.shape),
        highlightObjects: [highlightObject],

        diameter: item.diameter,
        length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
        FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
        VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
        UNITHEADLOSS: formatValue(
          otypeProperty,
          'UNITHEADLOSS',
          item.UNITHEADLOSS,
        ),
        AGE: formatValue(otypeProperty, 'AGE', item.AGE),
        QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
        CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_V',
          item.CUMULATIVE_TIME_V,
        ),
        LAST_CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_V',
          item.LAST_CUMULATIVE_TIME_V,
        ),
        VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
        LAST_VELOCITY_Q: formatValue(
          otypeProperty,
          'LAST_VELOCITY_Q',
          item.LAST_VELOCITY_Q,
        ),
        DIRECT_FLOW: formatValue(
          otypeProperty,
          'DIRECT_FLOW',
          item.DIRECT_FLOW,
        ),
        CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_F',
          item.CUMULATIVE_TIME_F,
        ),
        LAST_CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_F',
          item.LAST_CUMULATIVE_TIME_F,
        ),
        LAST_DIRECT_FLOW: formatValue(
          otypeProperty,
          'LAST_DIRECT_FLOW',
          item.LAST_DIRECT_FLOW,
        ),
        ROAD_NAME: item.ROAD_NAME,
        LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        DIAMETER: item.DIAMETER,
        ratio: item.usage,
        USAGE: formatValue(otypeProperty, 'USAGE', item.usage),
      });
    }
  });

  const newLinkObjects: Map<string, TrackDownLink> = new Map();

  linkObjects.forEach((item, key) => {
    const fieldRangeData = {
      ratio: getRangeValue(item.highlightObjects, 'USAGE'),
      shape: item.shape, // comboMutiLineString(item.highlightObjects),
      FLOW: getRangeValue(item.highlightObjects, 'FLOW'),
      FLOW_ABS: getRangeValue(item.highlightObjects, 'FLOW_ABS'),
      VELOCITY: getRangeValue(item.highlightObjects, 'VELOCITY'),
      UNITHEADLOSS: getRangeValue(item.highlightObjects, 'UNITHEADLOSS'),
      AGE: getRangeValue(item.highlightObjects, 'AGE'),
      QUALITY: getRangeValue(item.highlightObjects, 'QUALITY'),
      CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_V',
      ),
      LAST_CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_V',
      ),
      VELOCITY_Q: getRangeValue(item.highlightObjects, 'VELOCITY_Q'),
      LAST_VELOCITY_Q: getRangeValue(item.highlightObjects, 'LAST_VELOCITY_Q'),
      DIRECT_FLOW: getRangeValue(item.highlightObjects, 'DIRECT_FLOW'),
      CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_F',
      ),
      LAST_CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_F',
      ),
      LAST_DIRECT_FLOW: getRangeValue(
        item.highlightObjects,
        'LAST_DIRECT_FLOW',
      ),
      DIAMETER: getRangeValue(item.highlightObjects, 'DIAMETER'),
    };
    newLinkObjects.set(key, {
      oname: item.oname,
      otype: item.otype,
      id: item.id,
      highlightIcon: item.highlightIcon,
      highlightType: item.highlightType,
      highlightColor: '',
      shape: fieldRangeData.shape,
      ratio: fieldRangeData.ratio,
      highlightObjects: item.highlightObjects,
      shapeType: item.shapeType,

      diameter: 0,
      length: item.length,
      FLOW: fieldRangeData.FLOW,
      FLOW_ABS: fieldRangeData.FLOW_ABS,
      VELOCITY: fieldRangeData.VELOCITY,
      UNITHEADLOSS: fieldRangeData.UNITHEADLOSS,
      AGE: fieldRangeData.AGE,
      QUALITY: fieldRangeData.QUALITY,
      CUMULATIVE_TIME_V: fieldRangeData.CUMULATIVE_TIME_V,
      LAST_CUMULATIVE_TIME_V: fieldRangeData.LAST_CUMULATIVE_TIME_V,
      VELOCITY_Q: fieldRangeData.VELOCITY_Q,
      LAST_VELOCITY_Q: fieldRangeData.LAST_VELOCITY_Q,
      DIRECT_FLOW: fieldRangeData.DIRECT_FLOW,
      CUMULATIVE_TIME_F: fieldRangeData.CUMULATIVE_TIME_F,
      LAST_CUMULATIVE_TIME_F: fieldRangeData.LAST_CUMULATIVE_TIME_F,
      LAST_DIRECT_FLOW: fieldRangeData.LAST_DIRECT_FLOW,
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.length,
      DIAMETER: item.DIAMETER,
      USAGE: fieldRangeData.ratio,
    });
  });

  return newLinkObjects;
}

export function formatDmaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): TrackDownDma[] {
  const highlightObjects: TrackDownDma[] = [];
  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: color ?? 'custom',
      highlightColor: '',
      shapeType: getShapeType(item.shape),

      TITLE: formatValue(otypeProperty, 'TITLE', item.TITLE),
      POPULATION: formatValue(otypeProperty, 'POPULATION', item.POPULATION),
      AREA: formatValue(otypeProperty, 'AREA', item.AREA),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
      USER_METER_COUNT: formatValue(
        otypeProperty,
        'USER_METER_COUNT',
        item.USER_METER_COUNT,
      ),
      MAX_FLOW_TIME: formatValue(
        otypeProperty,
        'MAX_FLOW_TIME',
        item.MAX_FLOW_TIME,
      ),
      MIN_NMF_TIME: formatValue(
        otypeProperty,
        'MIN_NMF_TIME',
        item.MIN_NMF_TIME,
      ),
      MIN_NMF: formatValue(otypeProperty, 'MIN_NMF', item.MIN_NMF),
      AVERAGE_NMF: formatValue(otypeProperty, 'AVERAGE_NMF', item.AVERAGE_NMF),
      IN_FLOW_DAY: formatValue(otypeProperty, 'IN_FLOW_DAY', item.IN_FLOW_DAY),
      OUT_FLOW_DAY: formatValue(
        otypeProperty,
        'OUT_FLOW_DAY',
        item.OUT_FLOW_DAY,
      ),
      MAX_FLOW: formatValue(otypeProperty, 'MAX_FLOW', item.MAX_FLOW),
      AVERAGE_FLOW: formatValue(
        otypeProperty,
        'AVERAGE_FLOW',
        item.AVERAGE_FLOW,
      ),
      AVERAGE_PRESSURE: formatValue(
        otypeProperty,
        'AVERAGE_PRESSURE',
        item.AVERAGE_PRESSURE,
      ),
      DAY_FLOW: formatValue(otypeProperty, 'DAY_FLOW', item.DAY_FLOW),
      ABNORMAL_STATUS: formatValue(
        otypeProperty,
        'ABNORMAL_STATUS',
        item.ABNORMAL_STATUS,
      ),
      IN_FLOW: formatValue(otypeProperty, 'IN_FLOW', item.IN_FLOW),
      OUT_FLOW: formatValue(otypeProperty, 'OUT_FLOW', item.OUT_FLOW),
      WATERMETER_NUM: formatValue(
        otypeProperty,
        'WATERMETER_NUM',
        item.WATERMETER_NUM,
      ),
      REALFLOW: formatValue(otypeProperty, 'REALFLOW', item.REALFLOW),
    });
  });
  return highlightObjects;
}

function formatWaterMeterData(
  data: any[],
  db: Database,
  _highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): DownTrackWaterMeterData[] {
  const highlightObjects: DownTrackWaterMeterData[] = [];
  data.forEach((item) => {
    const icon =
      item.highlightIcon ?? db.icons.get(item.otype) ?? highlightIcon;
    const otypeProperty = db.getPropertyInfo(item.otype);

    highlightObjects.push({
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      shape: item.shape,
      highlightIcon: icon,
      highlightType: 'track',
      highlightColor: '',
      shapeType: getShapeType(item.shape),

      NAME: item.NAME,
      SOURCE_PERCENTAGE: formatValue(
        otypeProperty,
        'SOURCE_PERCENTAGE',
        item.source_percentage,
      ),
      ADDRESS: item.ADDRESS,
      BOOK_ID: item.BOOK_ID,
      FIRST_DMA_ID: item.FIRST_DMA_ID,
      CARD_STAT: formatValue(otypeProperty, 'CARD_STAT', item.CARD_STAT),
      USE_MODE: formatValue(otypeProperty, 'USE_MODE', item.USE_MODE),
      CUST_TYPE: formatValue(otypeProperty, 'CUST_TYPE', item.CUST_TYPE),
      DIAMETER: formatValue(otypeProperty, 'DIAMETER', item.DIAMETER),
      PAY_TYPE: formatValue(otypeProperty, 'PAY_TYPE', item.PAY_TYPE),
      SECOND_DMA_ID: item.SECOND_DMA_ID,
      THREE_DMA_ID: item.THREE_DMA_ID,
      USER_METER_TYPE: formatValue(
        otypeProperty,
        'USER_METER_TYPE',
        item.USER_METER_TYPE,
      ),
      TOTAL_FLOW: formatValue(otypeProperty, 'TOTAL_FLOW', item.TOTAL_FLOW),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      TOTALHEAD: formatValue(otypeProperty, 'TOTALHEAD', item.TOTALHEAD),
      PRESSURE: formatValue(otypeProperty, 'PRESSURE', item.PRESSURE),
      WATERSOURCE: formatValue(otypeProperty, 'WATERSOURCE', item.WATERSOURCE),
    });
  });
  return highlightObjects;
}

function formatScadaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
) {
  const highlightScadaObjects: TrackScada[] = [];
  data.forEach((item: any) => {
    const indicator = db.getIndicator(item.otype, item.oname);
    const otypeProperty = db.getPropertyInfo(item.otype);

    if (indicator?.ptype && indicator.pname) {
      const device = db.getDevice(indicator.ptype, indicator.pname);
      if (device) {
        highlightScadaObjects.push({
          oname: device.oname,
          otype: device.otype,
          id: device.id,
          shape: device.shape ?? '',
          highlightIcon: device.icon ?? highlightIcon,
          highlightType: highlightColor,
          shapeType: getShapeType(item.shape),
          title: device.title,
          scadaValue: formatValue(otypeProperty, 'SDVAL', item.sdval),
          usage: item.usage,
          modelValue: formatValue(otypeProperty, 'SDVAL', item.value),
          percent: item.source_percentage,
        });
      }
    }
  });
  return highlightScadaObjects;
}

function getResultData(
  data: any,
  highlightType: HighlightStyleType,
  db: Database,
) {
  const linkDatas = data?.links
    ? formatObjectData(data.links, highlightType)
    : [];

  const dmaDatas = data?.dma3
    ? formatDmaData(data.dma3, db, highlightType)
    : [];

  const waterMeterDatas = data?.watermeter
    ? formatWaterMeterData(data.watermeter, db, highlightType)
    : [];
  const linksObjectsMap = data?.links
    ? formatLinksDataToMap(data.links, db, highlightType)
    : new Map();

  const scadaDatas = data?.scada
    ? formatScadaData(data.scada, db, highlightType)
    : [];

  return {
    linkDatas,
    linksObjectsMap,
    dmaDatas,
    waterMeterDatas,
    scadaDatas,
  };
}

export async function getPollutionTrack(
  pollutedObjects: string,
  unPollutedObjects: string,
  minDiameter: number,
  minFlow: number,
  db: Database,
  time?: string,
  // todo: remove mapView
  mapView?: any,
): Promise<GetPollutionTrackDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/traverseFlowUpQual',
    params: {
      polluted_objects: pollutedObjects,
      unpolluted_objects: unPollutedObjects,
      min_flow: minFlow,
      min_diameter: minDiameter,
      time,
      view_id: mapView?.getViewId(),
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        const resultData = {
          pollutedSource: getResultData(data.source, 'track', db),
          polluted: getResultData(data.polluted, 'track', db),
          unpolluted: getResultData(data.unpolluted, 'track', db),
        };

        const highlightObjects: HighlightObject[] = [
          ...resultData.polluted.scadaDatas,
          ...resultData.unpolluted.scadaDatas,
          ...resultData.polluted.dmaDatas,
          ...resultData.unpolluted.dmaDatas,
        ];

        const currentMapView = mapView;
        if (currentMapView) {
          const downLink: TrackDownLink[] = [];
          (resultData.polluted.linksObjectsMap ?? new Map()).forEach((item) => {
            downLink.push(...item.highlightObjects);
          });

          const upLink: TrackDownLink[] = [];
          (resultData.pollutedSource.linksObjectsMap ?? new Map()).forEach(
            (item) => {
              upLink.push(...item.highlightObjects);
            },
          );

          currentMapView.trackHighlightObject = {
            downDma: resultData.polluted.dmaDatas,
            upDma: resultData.pollutedSource.dmaDatas,
            downLink,
            upLink,
          };
        }

        resolve({
          status: 'Success',
          defaultHighlightDatas: highlightObjects,
          pollutedSource: {
            pollutedSourceScadaDatas: resultData.pollutedSource.scadaDatas,
            pollutedSourceLinkDatas: resultData.pollutedSource.linkDatas,
            pollutedSourceDmaDatas: resultData.pollutedSource.dmaDatas,
            pollutedSourceLinkDatasMap:
              resultData.pollutedSource.linksObjectsMap,
            waterMeterDatas: resultData.pollutedSource.waterMeterDatas,
          },
          polluted: {
            pollutedScadaDatas: resultData.polluted.scadaDatas,
            pollutedLinkDatas: resultData.polluted.linkDatas,
            pollutedDmaDatas: resultData.polluted.dmaDatas,
            pollutedLinkDatasMap: resultData.polluted.linksObjectsMap,
            waterMeterDatas: resultData.polluted.waterMeterDatas,
          },
          unpolluted: {
            unpollutedScadaDatas: resultData.unpolluted.scadaDatas,
            unpollutedLinkDatas: resultData.unpolluted.linkDatas,
            unpollutedDmaDatas: resultData.unpolluted.dmaDatas,
            unpollutedLinkDatasMap: resultData.unpolluted.linksObjectsMap,
            waterMeterDatas: resultData.unpolluted.waterMeterDatas,
          },
          mapViewName: mapView?.mapViewName,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

export async function analyzeSolutionPollutionDiffusion({
  startTime,
  endTime,
}: {
  startTime: string;
  endTime: string;
}): Promise<GetPollutionSpreadResponse> {
  const data: any = await postRequestByView({
    code: 'supply/getPollutionSpreadByView',
    params: {
      start_time: startTime,
      end_time: endTime,
    },
  });

  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (data.no_model) {
          throw Error(`模型未计算`);
        }
        resolve({
          status: 'Success',
          taskId: data.task_id,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}

export function getPollutionSpreadResult(
  data: any,
  db: Database,
  mapView?: any,
): PollutionTrackData {
  const resultData = {
    polluted: getResultData(data.polluted, 'track', db),
  };

  const highlightObjects: HighlightObject[] = [
    ...resultData.polluted.scadaDatas,
    ...resultData.polluted.dmaDatas,
  ];

  const currentMapView = mapView;
  if (currentMapView) {
    const downLink: TrackDownLink[] = [];
    (resultData.polluted.linksObjectsMap ?? new Map()).forEach((item) => {
      downLink.push(...item.highlightObjects);
    });

    currentMapView.trackHighlightObject = {
      downDma: resultData.polluted.dmaDatas,
      downLink,
    };
  }

  return {
    defaultHighlightDatas: highlightObjects,
    polluted: {
      pollutedScadaDatas: resultData.polluted.scadaDatas,
      pollutedLinkDatas: resultData.polluted.linkDatas,
      pollutedDmaDatas: resultData.polluted.dmaDatas,
      pollutedLinkDatasMap: resultData.polluted.linksObjectsMap,
      waterMeterDatas: resultData.polluted.waterMeterDatas,
    },
    mapViewName: mapView?.mapViewName,
  };
}

export async function savePollutionSpreadAnalysis(
  params: SavePollutionSpreadAnalysisRequest,
): Promise<SavePollutionSpreadAnalysisResponse> {
  // 转换analysisResult中的Map为可序列化格式
  const serializedAnalysisResult = convertMapToSerializable(
    params.analysisResult,
  );
  // 直接使用JSON.stringify确保序列化结果正确
  const serializedResultString = JSON.stringify(serializedAnalysisResult);

  const res: any = await postRequestByView({
    code: 'supply/savePollutionSpreadAnalysis',
    params: {
      solution_id: params.solutionId,
      solution_guid: params.solutionGuid,
      objects: params.objects,
      pollution_start_time: params.pollutionStartTime,
      pollution_end_time: params.pollutionEndTime,
      simulation_end_time: params.simulationEndTime,
      diffusion_time: params.diffusionTime,
      analysis_title: params.analysisTitle,
      analysis_note: params.analysisNote,
      analysis_result: serializedResultString,
      calc_id: params.calcId,
      calc_time: params.calcTime,
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      data: {
        solution_id: res.data.solution_id,
      },
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}

export async function getPollutionSpreadAnalysisDetail(
  solutionId: string,
  diffusionTime: string,
): Promise<GetPollutionSpreadAnalysisDetailResponse> {
  if (!solutionId) {
    return {
      status: 'Fail',
      errorMessage: '方案ID不能为空',
    };
  }

  if (!diffusionTime) {
    return {
      status: 'Fail',
      errorMessage: '扩散时间不能为空',
    };
  }

  const res: any = await postRequestByView({
    code: 'supply/getPollutionSpreadAnalysisDetail',
    params: {
      solution_id: solutionId,
      diffusion_time: diffusionTime,
    },
  });

  if (res.json_ok) {
    // 先解析JSON字符串为JavaScript对象
    let parsedResult;
    try {
      parsedResult = res.data.analysis_result
        ? JSON.parse(res.data.analysis_result)
        : undefined;
    } catch (error) {
      console.error('解析analysis_result失败:', error);
      parsedResult = undefined;
    }

    // 然后将解析后的对象中的特殊数组结构转换回Map
    const analysisResult = parsedResult
      ? convertSerializableToMap(parsedResult)
      : undefined;

    return {
      status: 'Success',
      solutionId: res.data.solution_id,
      solutionGuid: res.data.solution_guid,
      objects: JSON.parse(res.data.objects),
      pollutionStartTime: res.data.pollution_start_time,
      pollutionEndTime: res.data.pollution_end_time,
      simulationEndTime: res.data.simulation_end_time,
      diffusionTime: res.data.diffusion_time,
      analysisTitle: res.data.analysis_title,
      analysisNote: res.data.analysis_note,
      analysisResult,
      calcId: res.data.calc_id,
      calcTime: res.data.calc_time,
      createTime: res.data.create_time,
      updateTime: res.data.update_time,
      savedTimes: res.data.saved_times,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
}
