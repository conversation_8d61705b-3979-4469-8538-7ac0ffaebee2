/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  getRangeValue,
  HighlightObject,
  TrackDownDma,
  TrackScada,
  TrackUpLink,
} from '@waterdesk/data/highlight-object';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import { HighlightStyleType } from '@waterdesk/data/style-config';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { APIResponse } from '../api/api-response';
import { postRequestByView } from '../request';
import { formatObjectData } from './get-track-data';

export interface GetUpTrackDataResponse extends APIResponse {
  upDmaDatas?: TrackDownDma[];
  upLinkDatasMap?: Map<string, TrackUpLink>;
  upLinkDatas?: HighlightObject[];
  upScadaDatas?: TrackScada[];
  defaultHighlightDatas?: HighlightObject[];
  mapViewName?: string;
}

function formatValue(
  otypeProperty: PropertyInfo | undefined,
  vprop: string,
  value: string | number,
) {
  if (otypeProperty) {
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    if (unitKey) {
      const unit = getUnitFormat(unitKey);
      if (unit) {
        return unit.getValue(value) ?? '';
      }
    }
  }
  return value;
}

function formatLinksDataToMap(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
): Map<string, TrackUpLink> {
  const linkObjects: Map<string, TrackUpLink> = new Map();

  data.forEach((item) => {
    const icon = item.highlightIcon ?? highlightIcon;
    const color = item.highlightColor ?? highlightColor;
    const otypeProperty = db.getPropertyInfo(item.otype);
    const id = `${item.ROAD_NAME}@${item.DIAMETER}`;
    const comboObject = linkObjects.get(id);
    const highlightObject: TrackUpLink = {
      oname: item.oname,
      otype: item.otype,
      id: makeObjectId(item.otype, item.oname),
      highlightIcon: icon,
      highlightType: color ?? 'track',
      shape: item.shape,
      highlightObjects: [],
      shapeType: getShapeType(item.shape),

      diameter: 0,
      length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
      FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
      VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
      UNITHEADLOSS: formatValue(
        otypeProperty,
        'UNITHEADLOSS',
        item.UNITHEADLOSS,
      ),
      AGE: formatValue(otypeProperty, 'AGE', item.AGE),
      QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
      CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_V',
        item.CUMULATIVE_TIME_V,
      ),
      LAST_CUMULATIVE_TIME_V: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_V',
        item.LAST_CUMULATIVE_TIME_V,
      ),
      VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
      LAST_VELOCITY_Q: formatValue(
        otypeProperty,
        'LAST_VELOCITY_Q',
        item.LAST_VELOCITY_Q,
      ),
      DIRECT_FLOW: formatValue(otypeProperty, 'DIRECT_FLOW', item.DIRECT_FLOW),
      CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'CUMULATIVE_TIME_F',
        item.CUMULATIVE_TIME_F,
      ),
      LAST_CUMULATIVE_TIME_F: formatValue(
        otypeProperty,
        'LAST_CUMULATIVE_TIME_F',
        item.LAST_CUMULATIVE_TIME_F,
      ),
      LAST_DIRECT_FLOW: formatValue(
        otypeProperty,
        'LAST_DIRECT_FLOW',
        item.LAST_DIRECT_FLOW,
      ),
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
      DIAMETER: item.DIAMETER,
    };
    if (comboObject) {
      comboObject.length += item.LENGTH;
      comboObject.highlightObjects.push(highlightObject);
    } else {
      linkObjects.set(id, {
        id,
        oname: item.oname,
        otype: item.otype,
        shape: item.shape,
        shapeType: getShapeType(item.shape),
        highlightObjects: [highlightObject],

        diameter: item.diameter,
        length: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        FLOW: formatValue(otypeProperty, 'FLOW', item.FLOW),
        FLOW_ABS: formatValue(otypeProperty, 'FLOW_ABS', item.FLOW_ABS),
        VELOCITY: formatValue(otypeProperty, 'VELOCITY', item.VELOCITY),
        UNITHEADLOSS: formatValue(
          otypeProperty,
          'UNITHEADLOSS',
          item.UNITHEADLOSS,
        ),
        AGE: formatValue(otypeProperty, 'AGE', item.AGE),
        QUALITY: formatValue(otypeProperty, 'QUALITY', item.QUALITY),
        CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_V',
          item.CUMULATIVE_TIME_V,
        ),
        LAST_CUMULATIVE_TIME_V: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_V',
          item.LAST_CUMULATIVE_TIME_V,
        ),
        VELOCITY_Q: formatValue(otypeProperty, 'VELOCITY_Q', item.VELOCITY_Q),
        LAST_VELOCITY_Q: formatValue(
          otypeProperty,
          'LAST_VELOCITY_Q',
          item.LAST_VELOCITY_Q,
        ),
        DIRECT_FLOW: formatValue(
          otypeProperty,
          'DIRECT_FLOW',
          item.DIRECT_FLOW,
        ),
        CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'CUMULATIVE_TIME_F',
          item.CUMULATIVE_TIME_F,
        ),
        LAST_CUMULATIVE_TIME_F: formatValue(
          otypeProperty,
          'LAST_CUMULATIVE_TIME_F',
          item.LAST_CUMULATIVE_TIME_F,
        ),
        LAST_DIRECT_FLOW: formatValue(
          otypeProperty,
          'LAST_DIRECT_FLOW',
          item.LAST_DIRECT_FLOW,
        ),
        ROAD_NAME: item.ROAD_NAME,
        LENGTH: formatValue(otypeProperty, 'LENGTH', item.LENGTH) as number,
        DIAMETER: item.DIAMETER,
      });
    }
  });

  const newLinkObjects: Map<string, TrackUpLink> = new Map();
  linkObjects.forEach((item, key) => {
    const fieldRangeData = {
      shape: item.shape, // comboMutiLineString(item.highlightObjects),
      FLOW: getRangeValue(item.highlightObjects, 'FLOW'),
      FLOW_ABS: getRangeValue(item.highlightObjects, 'FLOW_ABS'),
      VELOCITY: getRangeValue(item.highlightObjects, 'VELOCITY'),
      UNITHEADLOSS: getRangeValue(item.highlightObjects, 'UNITHEADLOSS'),
      AGE: getRangeValue(item.highlightObjects, 'AGE'),
      QUALITY: getRangeValue(item.highlightObjects, 'QUALITY'),
      CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_V',
      ),
      LAST_CUMULATIVE_TIME_V: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_V',
      ),
      VELOCITY_Q: getRangeValue(item.highlightObjects, 'VELOCITY_Q'),
      LAST_VELOCITY_Q: getRangeValue(item.highlightObjects, 'LAST_VELOCITY_Q'),
      DIRECT_FLOW: getRangeValue(item.highlightObjects, 'DIRECT_FLOW'),
      CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'CUMULATIVE_TIME_F',
      ),
      LAST_CUMULATIVE_TIME_F: getRangeValue(
        item.highlightObjects,
        'LAST_CUMULATIVE_TIME_F',
      ),
      LAST_DIRECT_FLOW: getRangeValue(
        item.highlightObjects,
        'LAST_DIRECT_FLOW',
      ),
      DIAMETER: getRangeValue(item.highlightObjects, 'DIAMETER'),
    };
    newLinkObjects.set(key, {
      oname: item.oname,
      otype: item.otype,
      id: item.id,
      highlightIcon: item.highlightIcon,
      highlightType: item.highlightType,
      highlightColor: '',
      shape: fieldRangeData.shape,
      highlightObjects: item.highlightObjects,
      shapeType: item.shapeType,
      length: item.length,

      FLOW: fieldRangeData.FLOW,
      FLOW_ABS: fieldRangeData.FLOW_ABS,
      VELOCITY: fieldRangeData.VELOCITY,
      UNITHEADLOSS: fieldRangeData.UNITHEADLOSS,
      AGE: fieldRangeData.AGE,
      QUALITY: fieldRangeData.QUALITY,
      CUMULATIVE_TIME_V: fieldRangeData.CUMULATIVE_TIME_V,
      LAST_CUMULATIVE_TIME_V: fieldRangeData.LAST_CUMULATIVE_TIME_V,
      VELOCITY_Q: fieldRangeData.VELOCITY_Q,
      LAST_VELOCITY_Q: fieldRangeData.LAST_VELOCITY_Q,
      DIRECT_FLOW: fieldRangeData.DIRECT_FLOW,
      CUMULATIVE_TIME_F: fieldRangeData.CUMULATIVE_TIME_F,
      LAST_CUMULATIVE_TIME_F: fieldRangeData.LAST_CUMULATIVE_TIME_F,
      LAST_DIRECT_FLOW: fieldRangeData.LAST_DIRECT_FLOW,
      ROAD_NAME: item.ROAD_NAME,
      LENGTH: item.LENGTH,
      DIAMETER: fieldRangeData.DIAMETER,
      WATERSOURCE1: item.WATERSOURCE1,
      WATERSOURCE2: item.WATERSOURCE2,
      WATERSOURCE3: item.WATERSOURCE3,
      WATERSOURCE4: item.WATERSOURCE4,
      WATERSOURCE5: item.WATERSOURCE5,
      WATERSOURCE6: item.WATERSOURCE6,
      WATERSOURCE7: item.WATERSOURCE7,
      WATERSOURCE8: item.WATERSOURCE8,
      WATERSOURCE9: item.WATERSOURCE9,
      WATERSOURCE10: item.WATERSOURCE10,
    });
  });

  return newLinkObjects;
}

function formatScadaData(
  data: any[],
  db: Database,
  highlightColor?: HighlightStyleType,
  highlightIcon?: string,
) {
  const highlightScadaObjects: TrackScada[] = [];
  data.forEach((item: any) => {
    const indicator = db.getIndicator(item.otype, item.oname);
    const otypeProperty = db.getPropertyInfo(item.otype);

    if (indicator?.ptype && indicator.pname) {
      const device = db.getDevice(indicator.ptype, indicator.pname);
      if (device) {
        highlightScadaObjects.push({
          oname: device.oname,
          otype: device.otype,
          indicatorType: indicator.otype,
          indicatorName: indicator.oname,
          indicatorId: indicator.id,
          id: device.id,
          shape: device.shape ?? '',
          highlightIcon: device.icon ?? highlightIcon,
          highlightType: highlightColor,
          shapeType: getShapeType(item.shape),
          title: device.title,
          scadaValue: formatValue(otypeProperty, 'SDVAL', item.sdval),
          usage: item.usage,
          modelValue: formatValue(otypeProperty, 'SDVAL', item.value),
          percent: item.source_percentage,
        });
      }
    }
  });
  return highlightScadaObjects;
}

function getResultData(
  data: any,
  db: Database,
  highlightType: HighlightStyleType,
) {
  const upLinkDatas = data?.links
    ? formatObjectData(data.links, highlightType)
    : [];
  const upLinkDatasMap = data?.links
    ? formatLinksDataToMap(data.links, db, highlightType)
    : new Map();

  const upScadaDatas = data?.scada
    ? formatScadaData(data.scada, db, highlightType)
    : [];
  return {
    upLinkDatas,
    upLinkDatasMap,
    upScadaDatas,
  };
}

export async function getUpTrack(
  oname: string,
  otype: string,
  minDiameter: number,
  minFlow: number,
  db: Database,
  time?: string,
  // todo remove mapView
  mapView?: any,
): Promise<GetUpTrackDataResponse> {
  const data: any = await postRequestByView({
    code: 'supply/traverseFlowUpUsage',
    params: {
      oname,
      otype,
      min_flow: minFlow,
      min_diameter: minDiameter,
      time,
      view_id: mapView?.getViewId(),
    },
  });
  return new Promise((resolve) => {
    if (data.json_ok) {
      try {
        if (!Array.isArray(data.values.links) && data.values.links !== null) {
          throw Error(`数据格式有误`);
        }

        const resultData = getResultData(data.values, db, 'track');

        const highlightObjects: HighlightObject[] = [
          ...resultData.upScadaDatas,
        ];

        const currentMapView = mapView;
        if (currentMapView) {
          const upLink: TrackUpLink[] = [];
          (resultData.upLinkDatasMap ?? new Map()).forEach((item) => {
            upLink.push(...item.highlightObjects);
          });

          currentMapView.trackHighlightObject = {
            upLink,
          };
        }

        resolve({
          status: 'Success',
          defaultHighlightDatas: highlightObjects,
          upLinkDatas: resultData.upLinkDatas,
          upLinkDatasMap: resultData.upLinkDatasMap,
          upScadaDatas: resultData.upScadaDatas,
          mapViewName: mapView?.mapViewName,
        });
      } catch (err) {
        resolve({
          status: 'Fail',
          errorMessage: err as unknown as string,
        });
      }
    }
    resolve({
      status: 'Fail',
      errorMessage: data.json_msg,
    });
  });
}
