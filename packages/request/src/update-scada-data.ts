/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface UpdateScadaDataResponse extends APIResponse {}

export async function updateScadaData(
  otype: string,
  oname: string,
  values: {
    vprop: string;
    value: any;
  }[],
): Promise<UpdateScadaDataResponse> {
  const res: any = await postRequest({
    code: 'watergis/updateScadaVPROP',
    params: {
      otype,
      oname,
      vprop_json_list: JSON.stringify(values),
    },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
  };
}
