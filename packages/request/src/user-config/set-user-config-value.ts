/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { APIResponse } from '../api/api-response';
import { postRequest } from '../request';

type LabelMode = string | undefined;
type Theme = string;

export async function setUserConfigValue(
  type: string,
  key: string,
  value: string | undefined,
  appId: string,
): Promise<APIResponse> {
  const data: any = await postRequest({
    code: 'portal/setUserConfigValue',
    params: {
      type,
      key,
      value,
      application_id: appId,
    },
  });

  if (data.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: data.json_msg,
  };
}

export async function saveSearchHistory(historyItems: string[], appId: string) {
  await setUserConfigValue(
    'userConfig',
    'historySearchWord',
    JSON.stringify(historyItems),
    appId,
  );
}

export async function saveThemeName(theme: Theme, appId: string) {
  await setUserConfigValue('userConfig', 'themeColorVars', theme, appId);
}

export async function saveOverlayLabelTitle(
  labelTitle: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'overlayLabelTitle',
    JSON.stringify(labelTitle),
    appId,
  );
}

export async function saveOverlayLabelIndicator(
  labelIndicator: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'overlayLabelIndicator',
    JSON.stringify(labelIndicator),
    appId,
  );
}

export async function saveOverlayLabelSimulation(
  labelSimulation: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'overlayLabelSimulation',
    JSON.stringify(labelSimulation),
    appId,
  );
}

export async function saveOverlayLabelMode(
  labelMode: LabelMode,
  appId: string,
) {
  await setUserConfigValue('userConfig', 'overlayLabelMode', labelMode, appId);
}

export async function saveAppOverlayLabelTitle(
  labelTitle: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'appOverlayLabelTitle',
    JSON.stringify(labelTitle),
    appId,
  );
}

export async function saveAppOverlayLabelIndicator(
  labelIndicator: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'appOverlayLabelIndicator',
    JSON.stringify(labelIndicator),
    appId,
  );
}

export async function saveAppOverlayLabelSimulation(
  labelSimulation: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'appOverlayLabelSimulation',
    JSON.stringify(labelSimulation),
    appId,
  );
}

export async function saveAppOverlayLabelMode(
  labelMode: LabelMode,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'appOverlayLabelMode',
    labelMode,
    appId,
  );
}

export async function saveChartShowWarnMark(
  chartShowWarnMark: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'chartShowWarnMark',
    JSON.stringify(chartShowWarnMark),
    appId,
  );
}

export async function saveMaxLimitation(
  chartMaxLimitation: boolean,
  appId: string,
) {
  await setUserConfigValue(
    'userConfig',
    'chartMaxLimitation',
    JSON.stringify(chartMaxLimitation),
    appId,
  );
}

export async function saveShowRange(chartShowRange: boolean, appId: string) {
  await setUserConfigValue(
    'userConfig',
    'chartShowRange',
    JSON.stringify(chartShowRange),
    appId,
  );
}

export async function saveTablePagination(
  key: string,
  pageSize: number,
  appId: string,
) {
  await setUserConfigValue('userConfig', key, JSON.stringify(pageSize), appId);
}

export async function saveTableConfig(
  key: string,
  config: Record<string, any>,
  appId: string,
) {
  await setUserConfigValue('userConfig', key, JSON.stringify(config), appId);
}

export async function saveActiveRightPanel(
  key: string,
  activePanel: string,
  appId: string,
) {
  await setUserConfigValue('userConfig', key, activePanel, appId);
}
