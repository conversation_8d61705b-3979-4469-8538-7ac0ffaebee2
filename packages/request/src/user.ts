/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SystemRoleItem, SystemRoleList } from '@waterdesk/data/system-role';
import { UserInfo, UserList } from '@waterdesk/data/system-user';
import dayjs from 'dayjs';
import { PageParams } from './api/api-request';
import { APIResponse } from './api/api-response';
import { pwdEncrypt } from './login';
import { postRequest } from './request';

export interface GetUserListResponse extends APIResponse {
  list: UserList;
  total: number;
}
export interface GetRolesByUserResponse extends APIResponse {
  roleList?: SystemRoleList;
}

export interface ResetPasswordResponse extends APIResponse {
  initialPassword?: string;
}

export const getUserList = async (
  params?: PageParams,
  keyword?: string,
  roleIdList?: string[],
): Promise<GetUserListResponse> => {
  const res: any = await postRequest({
    code: 'portal/queryUserPage',
    params: {
      keyword,
      page: params?.current ?? 1,
      rows: params?.pageSize ?? 9999,
      role_id_list: roleIdList ? roleIdList.join(',') : undefined,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.rows) {
        const userList = (Array.isArray(res.rows) ? res.rows : []).map(
          (item: any): UserInfo => ({
            id: item.USER_ID ?? '',
            name: item.USER_NAME ?? '',
            loginName: item.USER_LOGIN_NAME ?? '',
            sex: item.USER_SEX ?? '',
            phone: item.USER_PHONE ?? '',
            email: item.USER_EMAIL ?? '',
            stateCode: item.USER_STATE_CODE ?? '',
            note: item.NOTE ?? '',
            createTime: item.CREATE_TIME
              ? dayjs(item.CREATE_TIME).format('YYYY-MM-DD HH:mm:ss')
              : '',
            updateTime: item.UPDATE_TIME
              ? dayjs(item.UPDATE_TIME).format('YYYY-MM-DD HH:mm:ss')
              : '',
            lockedTime: item.LOCKED_TIME
              ? dayjs(item.LOCKED_TIME).format('YYYY-MM-DD HH:mm:ss')
              : '',
            loginErrTimes: item.LOGIN_ERR_TIMES ?? '',
            superUser: item.SUPER_USER ?? '',
            departmentName: item.DEPARTMENT_NAME ?? '',
            department: item.USER_PART ?? '',
          }),
        );
        resolve({
          status: 'Success',
          list: userList,
          total: Number(res.total),
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total: 0,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
        list: [],
        total: 0,
      });
    }
  });
};

export const createUser = async (
  name: string,
  loginName: string,
  password: string,
  sex?: string,
  phone?: string,
  note?: string,
  department?: string,
  email?: string,
  role?: string[],
): Promise<ResetPasswordResponse> => {
  const res: any = await postRequest({
    code: 'portal/createUser',
    params: {
      user_name: name,
      user_login_name: loginName,
      user_pwd: pwdEncrypt(password),
      user_part: department,
      user_sex: sex,
      user_phone: phone,
      user_email: email,
      note,
      user_roles: role?.toString(),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          initialPassword: res.initial_password,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const deleteUser = async (id: string): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/deleteUser',
    params: {
      user_id: id,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

export const updateCurrentUserPassword = async (
  oldPassword: string,
  newPassword: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateCurrentUserPassword',
    params: {
      old_user_pass: pwdEncrypt(oldPassword),
      new_user_pass: pwdEncrypt(newPassword),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const updateOtherUserPassword = async (
  id: string,
  oldPassword: string,
  newPassword: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateOtherUserPassword',
    params: {
      user_id: id,
      old_user_pass: pwdEncrypt(oldPassword),
      new_user_pass: pwdEncrypt(newPassword),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const resetUserPassword = async (
  id: string,
): Promise<ResetPasswordResponse> => {
  const res: any = await postRequest({
    code: 'portal/resetPassword',
    params: {
      user_id: id,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          initialPassword: res.initial_password,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const updateNewPassword = async (
  oldPassword: string,
  newPassword: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateCurrentUserPassword',
    params: {
      old_user_pass: pwdEncrypt(oldPassword),
      new_user_pass: pwdEncrypt(newPassword),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const updateUserInfo = async (
  id: string,
  name?: string,
  sex?: string,
  phone?: string,
  note?: string,
  department?: string,
  email?: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateUser',
    params: {
      user_id: id,
      user_name: name,
      user_part: department,
      user_sex: sex,
      user_phone: phone,
      user_email: email,
      note,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const getRolesByUser = async (
  userId: string,
): Promise<GetRolesByUserResponse> => {
  const res: any = await postRequest({
    code: 'portal/selectUserAndRoleByUserId',
    params: {
      user_id: userId,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.value) {
        const roleList = (
          Array.isArray(res.value.userAndRoleList)
            ? res.value.userAndRoleList
            : []
        ).map(
          (item: any): SystemRoleItem => ({
            roleId: item.ROLE_ID ?? '',
            roleName: item.ROLE_NAME ?? '',
          }),
        );
        resolve({
          status: 'Success',
          roleList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};

export const updateRolesByUser = async (
  userId: string,
  roleList: string[],
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'portal/updateUserRoleRelation',
    params: {
      user_id: userId,
      role_id_list: roleList.join(','),
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as string,
      });
    }
  });
};
