/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { postRequest } from '../request';

interface CreateUserParams {
  user_name: string;
  user_login_name: string;
  user_pwd: string;
  user_sex: string;
  user_phone: string;
  note: string;
  user_part?: string;
  user_email?: string;
}

interface UpdateUserParams {
  user_id: string;
  user_name: string;
  user_part: string;
  user_sex: string;
  user_phone: string;
  user_email: string;
  note: string;
}

export const refreshUsers = (keyword?: { USER_NAME: string }): any =>
  postRequest({
    code: 'portal/queryUserPage',
    params: {
      keyword,
      page: '1',
      rows: '50',
    },
  });

export const createUser = (params: CreateUserParams): any =>
  postRequest({
    code: 'portal/createUser',
    params,
  });

export const updateUser = (params: UpdateUserParams): any =>
  postRequest({
    code: 'portal/updateUser',
    params,
  });

export const deleteUser = (userId: string) =>
  postRequest({
    code: 'portal/deleteUser',
    params: {
      user_id: userId,
    },
  });

export const updateUserRoleRelation = (
  userId: string,
  selectRoleId: string,
): any =>
  postRequest({
    code: 'portal/updateUserRoleRelation',
    params: {
      user_id: userId,
      role_id_list: selectRoleId,
    },
  });

export const getUserRoleRelation = (userId: string): any =>
  postRequest({
    code: 'portal/selectUserAndRoleByUserId',
    params: {
      user_id: userId,
    },
  });

export const updateOtherUserPassword = (
  userId: string,
  oldPwd: string,
  newPwd: string,
): any =>
  postRequest({
    code: 'portal/updateOtherUserPassword',
    params: {
      user_id: userId,
      old_user_pass: oldPwd,
      new_user_pass: newPwd,
    },
  });

export const updateCurrentUserPassword = (
  oldPwd: string,
  newPwd: string,
): any =>
  postRequest({
    code: 'portal/updateCurrentUserPassword',
    params: {
      old_user_pass: oldPwd,
      new_user_pass: newPwd,
    },
  });
