/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { makeObjectId } from '@waterdesk/data/object-item';
import {
  parseValveOperationGroups,
  parseValveOperationList,
  SetValveOperationValueParams,
  SmartValveInfo,
  UpdateValveGroupDetailsInfo,
  ValveOperationGroup,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import dayjs from 'dayjs';
import { PageParams, sorterMethodMap } from './api/api-request';
import { APIResponse, DefaultListAPIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetValveOperationListResponse extends APIResponse {
  valveOperationList?: ValveOperationValue[];
}

export interface GetValveOperationGroupListResponse
  extends DefaultListAPIResponse<ValveOperationGroup> {
  list: ValveOperationGroup[];
  total: number;
}

export interface GetSmartValveListResponse extends APIResponse {
  smartValveList?: SmartValveInfo[];
}

export interface SetValveOperationResponse extends APIResponse {
  taskId?: string;
}

export const getValveOperationList = async (params: {
  /** string as YYYY-MM-DD */
  startDate?: string;
  endDate?: string;
  oname?: string;
}): Promise<GetValveOperationListResponse> => {
  const { startDate, endDate, oname } = params;
  const res: any = await postRequest({
    code: 'supply/getSdValveState',
    params: {
      start_time: startDate,
      end_time: endDate,
      oname,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const valveOperationList = parseValveOperationList(res.values);
        resolve({
          status: 'Success',
          valveOperationList,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

const sorterServerFieldMap: Record<string, string> = {
  startTime: 'start_time',
  endTime: 'end_time',
};

const queryValveOperationGroupList = async (
  params: {
    /** string as YYYY-MM-DD HH:mm:ss */
    startTime?: string;
    endTime?: string;
    title?: string;
    groupId?: string;
    status?: string;
    valveCode?: string;
  } & PageParams,
): Promise<GetValveOperationGroupListResponse> => {
  const {
    startTime,
    endTime,
    title,
    groupId,
    status,
    valveCode,
    pageSize,
    current,
  } = params;
  const sortKey = params?.sorter?.field;
  const sortMethod = params?.sorter?.order;
  const isSort = sortKey && sortMethod;
  const res: any = await postRequest({
    code: 'supply/queryValveOperationGroupList',
    params: {
      start_time: startTime
        ? dayjs(startTime).startOf('D').format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      end_time: endTime
        ? dayjs(endTime).endOf('D').format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      title,
      group_id: groupId,
      status,
      valve_code: valveCode,
      page_size: pageSize,
      current,
      sort_by: isSort ? sorterServerFieldMap[sortKey] : undefined,
      sort_method: isSort ? sorterMethodMap[sortMethod] : undefined,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok && res.values) {
        const valveOperationGroupList = parseValveOperationGroups(
          res.values.data,
        );
        resolve({
          status: 'Success',
          list: valveOperationGroupList,
          total: res.values.total,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
          list: [],
          total: 0,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
        list: [],
        total: 0,
      });
    }
  });
};

export const getValveOperationGroupList = async (
  params: {
    startTime: string;
    endTime: string;
    title?: string;
    groupId?: string;
    status?: string;
    valveCode?: string;
  } & PageParams,
): Promise<GetValveOperationGroupListResponse> =>
  queryValveOperationGroupList(params);

export const queryValveOperationGroupListLikeTitle = async (params: {
  title: string;
  pageSize?: number;
  current?: number;
}): Promise<GetValveOperationGroupListResponse> =>
  queryValveOperationGroupList({
    pageSize: 15,
    current: 1,
    ...params,
  });

export const setValveOperationValue = async (
  params: SetValveOperationValueParams & { userName: string },
): Promise<SetValveOperationResponse> => {
  const res: any = await postRequest({
    code: 'supply/setSdValveState',
    params: {
      ...params,
      rec_id: params.id,
      // todo: remove create_user
      create_user: params.userName,
    },
  });
  return new Promise((resolve) => {
    try {
      if (res.json_ok) {
        resolve({
          status: 'Success',
          taskId: res.task_id,
        });
      } else {
        resolve({
          status: 'Fail',
          errorMessage: res.json_msg,
        });
      }
    } catch (err) {
      resolve({
        status: 'Fail',
        errorMessage: err as unknown as string,
      });
    }
  });
};

/**
 *
 * @param time string as YYYY-MM-DD HH:mm:ss
 */
export const getSmartValveList = async (
  db: Database,
  time?: string,
): Promise<GetSmartValveListResponse> => {
  const res: any = await postRequest({
    code: 'supply/getSmartValveList',
    params: {
      time,
    },
  });

  if (res?.json_ok && res.smart_valve) {
    const list = Array.isArray(res.smart_valve)
      ? res.smart_valve
          .map((item: any): SmartValveInfo | undefined => {
            const indicator = db.getIndicator(item.ptype, item.pname);
            if (indicator?.ptype && indicator.pname) {
              const deviceId = makeObjectId(indicator.ptype, indicator.pname);
              const device = db.getDeviceById(deviceId);
              return {
                id: makeObjectId(item.ptype, item.pname),
                oname: item.oname,
                otype: item.otype,
                pname: item.pname,
                ptype: item.ptype,
                deviceName: indicator.pname,
                deviceType: indicator.ptype,
                deviceTitle: device?.title || '',
                indicatorName: indicator.oname,
                indicatorType: indicator.otype,
                indicatorTitle:
                  indicator.title ?? (indicator.indicatorType?.title || ''),
              };
            }
            return undefined;
          })
          .filter(
            (item: SmartValveInfo | undefined) => typeof item !== 'undefined',
          )
      : [];
    return {
      status: 'Success',
      smartValveList: list,
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const addSmartValve = async (
  valveName: string,
  indicatorName: string,
  indicatorType: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'supply/addSmartValveInfo',
    params: {
      oname: valveName,
      ptype: indicatorType,
      pname: indicatorName,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

/**
 *
 * @param valveGroupId
 * @param impact impact is the json of the original result of valve analysis result
 * @returns
 */
export const updateValveOperationGroup = async (
  valveGroupId: string,
  params: {
    title?: string;
    impact?: { [index: string]: any };
  },
): Promise<APIResponse> => {
  const { title, impact } = params;
  const res: any = await postRequest({
    code: 'supply/updateValveOperationGroup',
    params: {
      id: valveGroupId,
      impact: impact ? JSON.stringify(impact) : undefined,
      title,
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const updateValveOperationGroupDetails = async (
  list: UpdateValveGroupDetailsInfo[],
) => {
  const res: any = await postRequest({
    code: 'supply/updateValveOperationGroupDetails',
    params: {
      list: JSON.stringify(list),
    },
  });
  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }
  return {
    status: 'Fail',
    errorMessage: 'res.json_msg',
  };
};
