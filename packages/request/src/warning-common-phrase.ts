/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { WarningCommonPhrase } from '@waterdesk/data/warning-common-phrase';
import { APIResponse } from './api/api-response';
import { postRequest } from './request';

export interface GetWarningCommonPhraseListParams {
  phraseType?: string;
  page?: number;
  size?: number;
}

export interface GetWarningCommonPhraseListResponse extends APIResponse {
  data?: {
    list: WarningCommonPhrase[];
    total: number;
  };
}

export interface AddWarningCommonPhraseParams {
  phraseContent: string;
  phraseType: string;
}

export interface AddWarningCommonPhraseResponse extends APIResponse {
  data?: {
    id: string;
  };
}

export interface UpdateWarningCommonPhraseParams {
  id: string;
  phraseContent: string;
  phraseType: string;
}

export interface BatchSortWarningCommonPhraseParams {
  sortList: Array<{
    id: string;
    sortOrder: number;
  }>;
}

export interface BatchSortWarningCommonPhraseResponse extends APIResponse {
  data?: {
    message: string;
    updatedCount: number;
  };
}

export const getWarningCommonPhraseList = async (
  params: GetWarningCommonPhraseListParams,
): Promise<GetWarningCommonPhraseListResponse> => {
  const res: any = await postRequest({
    code: 'warning/common-phrase/list',
    params,
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      data: res.data,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const addWarningCommonPhrase = async (
  params: AddWarningCommonPhraseParams,
): Promise<AddWarningCommonPhraseResponse> => {
  const res: any = await postRequest({
    code: 'warning/common-phrase/add',
    params,
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      data: res.data,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const updateWarningCommonPhrase = async (
  params: UpdateWarningCommonPhraseParams,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'warning/common-phrase/update',
    params,
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const deleteWarningCommonPhrase = async (
  id: string,
): Promise<APIResponse> => {
  const res: any = await postRequest({
    code: 'warning/common-phrase/delete',
    params: { id },
  });

  if (res.json_ok) {
    return {
      status: 'Success',
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};

export const batchSortWarningCommonPhrase = async (
  params: BatchSortWarningCommonPhraseParams,
): Promise<BatchSortWarningCommonPhraseResponse> => {
  const res: any = await postRequest({
    code: 'warning/common-phrase/batch-sort',
    params,
  });

  if (res.json_ok) {
    return {
      status: 'Success',
      data: res.data,
    };
  }

  return {
    status: 'Fail',
    errorMessage: res.json_msg,
  };
};
