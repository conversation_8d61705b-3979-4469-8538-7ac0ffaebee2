{"extends": ["../../eslint.base.json"], "plugins": ["react"], "rules": {"react/jsx-filename-extension": [2, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "react/require-default-props": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": ["**/*.stories.*", "**/.storybook/**/*.*"], "peerDependencies": true, "packageDir": [".", "../../"]}], "react/jsx-props-no-spreading": "off", "react/function-component-definition": [2, {"namedComponents": ["function-declaration", "arrow-function"]}], "no-param-reassign": [2, {"props": true, "ignorePropertyModificationsFor": ["state"]}], "react/prop-types": "off"}, "settings": {"import/resolver": {"typescript": {"project": ["./tsconfig.json"]}, "node": {"paths": ["."], "extensions": [".js", ".jsx", ".ts", ".tsx", ".d.ts"]}}}}