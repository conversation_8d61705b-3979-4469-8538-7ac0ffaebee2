#!/usr/bin/env sh

COPYRIGHT_NOTICE=$(cat copyright.txt)
SOURCE_CODE_DIR="src"
FILE_EXTENSIONS=".js .jsx .ts .tsx"

# Iterate over the files in the commit
git diff --cached --name-only --diff-filter=ACM | while read FILE_PATH; do
    FILE_EXTENSION="${FILE_PATH##*.}"
    
    # Check if the file is in the specified directory and has the allowed extensions
    if [[ "$FILE_PATH" == "$SOURCE_CODE_DIR"* && " $FILE_EXTENSIONS" == *"$FILE_EXTENSION"* ]]; then
        # Check if the file does not already contain the notice
        if ! grep -q "$COPYRIGHT_NOTICE" "$FILE_PATH"; then
            # Add the notice at the beginning of the file
            awk -v notice="$COPYRIGHT_NOTICE" 'BEGIN {print notice; print ""} {print}' "$FILE_PATH" > temp && mv temp "$FILE_PATH"
            echo "Added copyright notice to $FILE_PATH"
            git add "$FILE_PATH"
        fi
    fi
done






