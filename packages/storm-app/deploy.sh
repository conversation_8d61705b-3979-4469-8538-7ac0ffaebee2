#!/bin/bash

# 提示用户输入目标文件夹名称
read -p "Please enter the target folder name: " TARGET_FOLDER

# 检查是否输入了有效的文件夹名称
if [ -z "$TARGET_FOLDER" ]; then
  echo "No target folder name provided. Exiting."
  exit 1
fi

# 定义本地路径和远程共享路径
LOCAL_PATH="./build"
SMB_PATH="/Volumes/nginx-1.18.0/html/$TARGET_FOLDER"

# 挂载远程共享路径
if ! mount | grep -q "$SMB_PATH"; then
  sudo mkdir -p "$SMB_PATH"
  sudo mount_smbfs //huishui@192.168.123.43/D/nginx-1.18.0/html/$TARGET_FOLDER "$SMB_PATH"
fi

# 确保挂载成功
if [ $? -ne 0 ]; then
  echo "Failed to mount the SMB share."
  exit 1
fi

# 删除目标文件夹中的所有文件和子文件夹，除了 global-config.js
echo "Removing all files except global-config.js from the target directory..."
sudo find "$SMB_PATH" -mindepth 1 ! -name 'global-config.js' -exec sudo rm -rf {} +

# 遍历本地文件夹，并复制文件
for file in "$LOCAL_PATH"/*; do
  filename=$(basename "$file")
  
  # 跳过 global-config.js 文件
  if [ "$filename" == "global-config.js" ]; then
    echo "Skipping $filename"
    continue
  fi
  
  # 复制文件到远程共享文件夹
  echo "Copying $filename to the target directory..."
  sudo cp -R "$file" "$SMB_PATH"
done

# 可选：卸载远程共享路径
sudo umount "$SMB_PATH"

echo "Files have been successfully copied to the remote path, except global-config.js."