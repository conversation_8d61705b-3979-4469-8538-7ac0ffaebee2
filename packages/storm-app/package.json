{"name": "waterdesk-app", "version": "3.1.0", "private": true, "type": "module", "dependencies": {"@waterdesk/data": "*", "@waterdesk/request": "*", "@waterdesk/core": "*", "@wecom/jssdk": "^1.4.5", "antd-mobile": "^5.39.0"}, "scripts": {"start": "vite", "dev": "vite", "prebuild": "node update-version.js", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" vite build", "deploy": "cross-env GENERATE_SOURCEMAP=false yarn build", "build-deploy": "yarn deploy && ./deploy.sh", "preview": "vite preview", "test": "vitest", "jest": "jest", "coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "cd ../.. && husky install packages/storm-web", "prettier:write": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,json,md}\"", "lint:fix": "eslint --fix \"./src/**/*.{ts,js,tsx,jsx}\"", "prettier": "prettier --check \"./src/**/*.{js,jsx,ts,tsx,json,md}\"", "lint": "eslint --fix-dry-run \"./src/**/*.{ts,js,tsx,jsx}\"", "build:tsc": "tsc --build"}, "main": "index.ts", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "."}