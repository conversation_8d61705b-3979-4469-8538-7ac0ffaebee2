/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Helmet, HelmetProvider } from 'react-helmet-async';
import { useSelector } from 'react-redux';
import { RouterProvider } from 'react-router';
import routes from 'src/app/core/router';
import { GlobalStyle } from 'src/styles/global-style';
import LoadingProgress from './components/common/loading-progress';
import { SYSTEM_CONFIG } from './config';
import { useBaseSlice } from './store/base';
import { selectLoading } from './store/base/selectors';
import {
  AntdThemeProvider,
  StyledThemeProvider,
} from './styles/theme/theme-provider-adapter';

function App() {
  useBaseSlice();

  const loading = useSelector(selectLoading);

  return (
    <HelmetProvider>
      <AntdThemeProvider>
        <StyledThemeProvider>
          <Helmet
            titleTemplate={`%s - ${SYSTEM_CONFIG.systemName}`}
            defaultTitle={SYSTEM_CONFIG.systemName}
          />
          <RouterProvider router={routes} />
          <LoadingProgress loading={loading} />
          <GlobalStyle />
        </StyledThemeProvider>
      </AntdThemeProvider>
    </HelmetProvider>
  );
}

export default App;
