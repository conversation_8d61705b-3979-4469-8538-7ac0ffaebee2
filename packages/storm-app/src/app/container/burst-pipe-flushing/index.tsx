/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import {
  HighlightObject,
  ImpactedLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  AnalysisResultData,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  getAllSelected,
  getAllSelectedByMergeRoadName,
  getAnalysisResultHighlightDataTheme,
  getAnalysisResultHightData,
  getAnalysisThemeConfig,
  getAnalysisUiConfig,
  getAnalysisUiData,
  getHoverHighlightData,
  mergeSameRoadNameAndDiameter,
  QueryParams,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Importance, MsgFrom, SendSMSParams } from '@waterdesk/data/sms';
import { HIGHLIGHT_VALVE_ANALYSIS_IMPACTED } from '@waterdesk/data/style-config';
import { FeatureCode } from '@waterdesk/data/system-feature';
import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { formatNumber } from '@waterdesk/data/utils';
import { AsyncTaskStatus } from '@waterdesk/request/get-async-task';
import {
  getBurstFlushingData,
  getPipeBreakSuggestFlow,
} from '@waterdesk/request/get-burst-flushing-data';
import { sendSMS } from '@waterdesk/request/get-sms-data';
import {
  formatAnalysisData,
  GetAnalysisDataAsyncResponse,
  getWaterMeterListByDMA,
} from '@waterdesk/request/get-valve-analysis-data';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  InputNumber,
  message,
  Progress,
  Row,
  Slider,
  Space,
  Spin,
} from 'antd';
import { NavBar, Popup } from 'antd-mobile';
import { Key, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsyncLocate } from 'src/app/hooks/use-async-locate';
import useFeatureFlags from 'src/app/hooks/use-feature-flags';
import useLocalStorageAsyncTask from 'src/app/hooks/use-local-storage-tasks';
import { curDb, hostApp } from 'src/app/host-app';
import { baseActions, useBaseSlice } from 'src/store/base';
import { highlightActions } from 'src/store/highlight';
import { leftWrapperActions } from 'src/store/left-wrapper';
import { selectLeftContainerTypeWrapper } from 'src/store/left-wrapper/selector';
import { MENU_TOOLS_BURST_PIPE } from 'src/store/left-wrapper/types';
import { schedulingActions } from 'src/store/scheduling';
import { selectSelectionItem } from 'src/store/selection/selector';
import { useTimelineSlice } from 'src/store/time-line';
import {
  selectTimelineDate,
  selectTimelineTime,
} from 'src/store/time-line/selectors';
import AnalysisResult from '../../../components/analyse-result/analysis-result';

interface ObjectFormItem {
  oname: string;
  otype: string;
  shape: string;
  otypeTitle: string;
  key: string;
  name: number;
  highlightColor?: string;
  highlightIcon?: string;
}

interface FormItem {
  otypeOnames: ObjectFormItem;
}

function getFlowVelocity(flow: number, diameter: number): string | number {
  const r = diameter / 1000 / 2;
  const area = Math.PI * r * r;
  return formatNumber(flow / 3600 / area, 1);
}

export default function BurstPipeFlushingContainer() {
  useBaseSlice();
  useTimelineSlice();
  const { locate } = useAsyncLocate();
  const { valveAnalysisConfig } = hostApp().appConfig;
  const { state: hasSendSMS } = useFeatureFlags(FeatureCode.QUICK_SOLUTION_SMS);

  const [messageApi, contextHolder] = message.useMessage();

  const dispatch = useDispatch();
  const { token } = useToken();
  const leftContainerType = useSelector(selectLeftContainerTypeWrapper);
  const selectedItems = useSelector(selectSelectionItem);
  const timelineDate = useSelector(selectTimelineDate);
  const timelineTime = useSelector(selectTimelineTime);
  const modalForm = useRef<FormInstance<FormItem>>(null);
  const [selectable, setSelectable] = useState<boolean>(true);
  const [objectFormData, setObjectFormData] = useState<
    ObjectFormItem | undefined
  >(undefined);
  const [flushingFlow, setFlushingFlow] = useState<number>(0);
  const [flushingFlowVelocity, setFlushingFlowVelocity] = useState<
    string | number
  >('-');
  const [maxFlushingFlow, setMaxFlushingFlow] = useState<number>(0);
  const [nodeInfo, setNodeInfo] = useState<
    | {
        pipeDiameter: number;
        pressure: number;
        nodeOname: string;
        nodeType: string;
      }
    | undefined
  >(undefined);
  const [currentHighlightObjects, setCurrentHighlightObjects] = useState<
    HighlightObject[]
  >([]);

  const [tableSelectedKeys, setTableSelectedKeys] = useState<
    Partial<Record<AnalysisUiConfigType, React.Key[]>>
  >({});

  const [dimensionVisible, setDimensionVisible] = useState<boolean>(false);
  const [highlightNode, setHighlightNode] = useState<boolean>(false);

  const [result, setResult] = useState<{
    burstFlushingDatas: HighlightObject[];
    impactedLinksObjectsMap: Map<string, ImpactedLink>;
  }>({
    burstFlushingDatas: [],
    impactedLinksObjectsMap: new Map(),
  });

  const [resultVisible, setResultVisible] = useState<boolean>(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [analysisThemesConfig, setAnalysisThemesConfig] = useState<
    Partial<Record<AnalysisUiConfigType, LegendGroupData[]>>
  >({});

  const [activeThemes, setActiveThemes] = useState<
    Partial<Record<AnalysisUiConfigType, string>>
  >({});

  const [analysisData, setAnalysisData] = useState<
    AnalysisResultData | undefined
  >(undefined);

  const { burstPipeAnalysisConfig: analysisConfig } = hostApp().appConfig;

  const onClose = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: undefined,
      }),
    );
  };

  const {
    progress,
    taskId,
    add,
    message: taskProcessMag,
  } = useLocalStorageAsyncTask('burstPipeFlushingTask', {
    formatValues: (values) =>
      formatAnalysisData(values, analysisConfig!, curDb()),
    callback: (res: GetAnalysisDataAsyncResponse) => {
      const { values, taskStatus } = res;
      if (taskStatus === AsyncTaskStatus.SUCCESS) {
        setAnalysisData(values);
      }
    },
    progressStep: hostApp().appConfig.progressStep,
    progressFixed: true,
  });

  const getStep = (value: number): number => {
    if (value > 500) {
      return 1;
    }
    if (value > 50) {
      return 0.1;
    }
    return 0.01;
  };

  const onFlushingFlowChange = (newValue: number | null) => {
    let flow = 0;
    if (newValue) {
      flow = newValue;
    }
    setFlushingFlow(flow);
    if (nodeInfo) {
      setFlushingFlowVelocity(getFlowVelocity(flow, nodeInfo.pipeDiameter));
    }
  };

  const onSelectMapObject = (objectData: ObjectFormItem | undefined) => {
    if (!objectData) {
      return;
    }
    modalForm.current?.setFieldsValue({
      otypeOnames: objectData,
    });
    getPipeBreakSuggestFlow(
      getDateTimeFromValue(timelineTime, timelineDate).format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      objectData.otype,
      objectData.oname,
    ).then((res) => {
      if (res.status === 'Success') {
        const flow: number = res.flow ? formatNumber(res.flow, 1) : 0;
        setFlushingFlow(flow);
        setFlushingFlowVelocity(
          getFlowVelocity(flow, res.pipeDiameter as number),
        );
        setMaxFlushingFlow(Math.round(flow * 2));
        setNodeInfo({
          pipeDiameter: res.pipeDiameter as number,
          pressure: res.pressure as number,
          nodeType: res.nodeType as string,
          nodeOname: res.nodeOname as string,
        });
      }
    });
    setObjectFormData(objectData);
  };

  useEffect(() => {
    if (selectable && leftContainerType === MENU_TOOLS_BURST_PIPE) {
      const selectionCollection =
        hostApp().getMainMapView()?.selectionCollection;
      const selectedObject = selectionCollection?.selectedObjects[0];
      if (selectedObject) {
        const itemProperty = curDb().propertyInfos.get(selectedObject.otype);
        const objectData: ObjectFormItem = {
          oname: selectedObject.oname,
          otype: selectedObject.otype,
          shape: selectedObject.shape ?? '',
          highlightIcon: selectedObject.highlightIcon,
          highlightColor: 'select',
          otypeTitle: itemProperty ? itemProperty.title : selectedObject.otype,
          key: selectedObject.oname,
          name: 0,
        };
        onSelectMapObject(objectData);
      }
    }
  }, [selectedItems, leftContainerType]);

  const analyseBurstPipeFlushing = async () => {
    if (!objectFormData) {
      messageApi.info('请至少选择一个对象再分析!');
      return;
    }
    const otypeOnames: string[][] = [];
    if (objectFormData.oname) {
      otypeOnames.push([objectFormData.otype, objectFormData.oname]);
    }
    if (nodeInfo) {
      const res = await getBurstFlushingData(
        getDateTimeFromValue(timelineTime, timelineDate).format(
          'YYYY-MM-DD HH:mm:ss',
        ),
        nodeInfo.nodeType,
        nodeInfo.nodeOname,
        flushingFlow,
      );
      if (res.status === 'Success' && res.taskId) {
        add(res.taskId);
      }
    }
  };

  const hideFeatureTooltipImportant = () => {
    dispatch(
      baseActions.updateHideFeatureTooltipImportant({
        visible: false,
      }),
    );
    hostApp().redrawMapView();
  };

  const resetHideFeatureTooltipImportant = () => {
    dispatch(baseActions.updateHideFeatureTooltipImportant({}));
    hostApp().redrawMapView();
  };

  const onSubmit = () => {
    dispatch(
      highlightActions.clearHighlight({ highlightLayerName: 'valveAnalyse' }),
    );
    hideFeatureTooltipImportant();
    analyseBurstPipeFlushing();
  };

  const onReset = () => {
    modalForm.current?.setFieldsValue({});
    setSelectable(true);
    setCurrentHighlightObjects([]);
    setFlushingFlow(0);
    setFlushingFlowVelocity(0);
    setObjectFormData(undefined);
    setResult({
      burstFlushingDatas: [],
      impactedLinksObjectsMap: new Map(),
    });
    setSelectedRowKeys([]);
    resetHideFeatureTooltipImportant();

    dispatch(
      schedulingActions.burstFlushingFormDataChanged({
        burstFlushingFormData: undefined,
      }),
    );
    dispatch(
      highlightActions.clearHighlight({ highlightLayerName: 'valveAnalyse' }),
    );
    dispatch(highlightActions.resetHighlight());
  };

  useEffect(() => {
    const currentSelectedObject = modalForm.current?.getFieldsValue();

    if (
      leftContainerType === MENU_TOOLS_BURST_PIPE &&
      currentSelectedObject &&
      Object.keys(currentSelectedObject).length > 0
    ) {
      dispatch(
        highlightActions.updateHighlight({
          highlight: { valveAnalyse: currentHighlightObjects ?? [] },
        }),
      );
    }
  }, [leftContainerType]);

  const onChangeLink = (type: string, e: React.Key[]) => {
    let impactedLinkKeys = selectedRowKeys;
    if (type === 'impactedLink') {
      impactedLinkKeys = e;
    }

    const highlightObjects: HighlightObject[] = [];
    highlightObjects.push(...result.burstFlushingDatas);
    impactedLinkKeys.forEach((id) => {
      const roadObject = result.impactedLinksObjectsMap.get(id as string);
      if (roadObject) {
        roadObject.highlightObjects.forEach((item) => {
          highlightObjects.push({
            ...item,
            highlightType: HIGHLIGHT_VALVE_ANALYSIS_IMPACTED,
          });
        });
      }
    });

    setCurrentHighlightObjects(highlightObjects);
    dispatch(
      highlightActions.clearHighlight({ highlightLayerName: 'valveAnalyse' }),
    );
    dispatch(
      highlightActions.updateHighlight({
        highlight: { valveAnalyse: highlightObjects ?? [] },
      }),
    );
  };

  const handleSetActiveThemes = (panelKey: string, value?: string) => {
    setActiveThemes((state) => ({
      ...state,
      [panelKey]: value,
    }));
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onSelectRow = (e: React.Key[]) => {
    setSelectedRowKeys(e);
    onChangeLink('impactedLink', e);
  };

  const positionToMapView = (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => {
    hostApp()
      .getMainMapView()
      ?.selectAndNavigate(
        highlightObject.otype,
        highlightObject.oname,
        highlightObject.shape,
      );
  };

  const handleHighlightHover = (
    hoverObject: AnalysisUiDataItem | undefined,
  ) => {
    const hoverObjectHighlightData = {
      analysisResultHover: getHoverHighlightData(
        typeof hoverObject === 'undefined' ? [] : [hoverObject],
        curDb(),
      ),
    };
    dispatch(
      highlightActions.updateHighlight({
        highlight: {
          ...hoverObjectHighlightData,
        },
      }),
    );
  };

  // 分析结果高亮
  const handleHighlightAnalysisResult = (
    analysisResult: AnalysisResultData,
    theme: Partial<Record<AnalysisUiConfigType, string>>,
    dimension: boolean,
    highlightNode: boolean,
  ) => {
    if (typeof analysisConfig === 'undefined') {
      messageApi.warning('主题配置加载失败');
    } else {
      const analysisResultHighlightDataTheme: Partial<
        Record<AnalysisUiConfigType, LegendGroupData>
      > = getAnalysisResultHighlightDataTheme(theme, analysisThemesConfig);

      const analysisResultHighlightData = getAnalysisResultHightData(
        curDb(),
        analysisResult,
        analysisConfig,
        analysisResultHighlightDataTheme,
        dimension,
        highlightNode,
      );
      dispatch(
        highlightActions.updateHighlight({
          highlight: {
            ...analysisResultHighlightData,
          },
        }),
      );
    }
  };

  const handleSetTableSelectedKeys = (
    keys: Key[],
    tableKey: AnalysisUiConfigType,
  ) => {
    setTableSelectedKeys((state) => ({
      ...state,
      [tableKey]: keys,
    }));
  };

  useEffect(() => {
    if (analysisConfig && resultVisible) {
      const analysisUIConfig = getAnalysisUiConfig(analysisConfig);
      Object.entries(analysisUIConfig).forEach((item) => {
        const [panelKey, config] = item;
        if (config) {
          const analysisThemeConfig = getAnalysisThemeConfig(
            config.otype,
            config.themes,
            curDb(),
          );
          setAnalysisThemesConfig((state) => ({
            ...state,
            [panelKey]: analysisThemeConfig,
          }));
          handleSetActiveThemes(panelKey, analysisThemeConfig?.[0].name);
        }
      });
    }
  }, [analysisConfig, resultVisible]);

  useEffect(() => {
    if (analysisData) {
      const selectedKeys: typeof tableSelectedKeys = {};
      const analysisUiData = getAnalysisUiData(analysisData);
      Object.entries(analysisUiData).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            selectedKeys[key as AnalysisUiConfigType] = value.map(
              (item) => item.oname,
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            selectedKeys[key as AnalysisUiConfigType] =
              mergeSameRoadNameAndDiameter(value).map((item) => item.oname);
            break;
          default:
            break;
        }
      });

      setTableSelectedKeys(selectedKeys);
    }
  }, [analysisData]);

  useEffect(() => {
    if (analysisData) {
      const customResult: AnalysisResultData = { ...analysisData };
      const analysisUiData = getAnalysisUiData(customResult);
      Object.entries(analysisUiData).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            customResult[key as AnalysisUiConfigType] = getAllSelected(
              (tableSelectedKeys[key as AnalysisUiConfigType] ??
                []) as string[],
              value,
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            customResult[key as AnalysisUiConfigType] =
              getAllSelectedByMergeRoadName(
                (tableSelectedKeys[key as AnalysisUiConfigType] ??
                  []) as string[],
                value,
              );
            break;
          default:
            break;
        }
      });
      handleHighlightAnalysisResult(
        customResult,
        activeThemes,
        dimensionVisible,
        highlightNode,
      );
    }
  }, [
    analysisData,
    dimensionVisible,
    activeThemes,
    tableSelectedKeys,
    highlightNode,
  ]);

  const handleSendSMS = async (
    msgFrom: MsgFrom,
    importance: Importance,
    msgText: string,
    dataSource: WaterMeterInfo[],
  ) =>
    new Promise<void>((resolve) => {
      const maxSendSMSCount = valveAnalysisConfig?.maxSendSMSCount ?? 0;
      if (dataSource.length >= maxSendSMSCount) {
        messageApi.warning(`发送数量超出最大限制：${maxSendSMSCount}条`);
        resolve();
        return;
      }
      const newValues: SendSMSParams = {
        msgFrom,
        importance,
        msgText,
        msgSendJson: dataSource.map((item) => ({
          msg_send_type: 'TELEPHONE',
          msg_send_code: item.phone,
        })),
      };

      sendSMS(newValues, MsgFrom.WATER_SHUTOFF_MSG).then((res) => {
        if (res.status === 'Success') {
          messageApi.success('发送成功');
        } else {
          messageApi.error('发送失败');
        }
        resolve();
      });
    });

  const fetchWaterMeterList = async (params: QueryParams['params']) => {
    const res = await getWaterMeterListByDMA(params);
    if (res.status === 'Success') {
      return res.data ?? {};
    }
    return {};
  };

  useEffect(() => {
    if (analysisData) {
      setResultVisible(true);
    }
  }, [analysisData]);
  return {
    burstPipeForm: (
      <Popup
        position="right"
        visible={leftContainerType === MENU_TOOLS_BURST_PIPE}
      >
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
          }}
        >
          <NavBar
            onBack={onClose}
            style={{
              backgroundColor: token.colorBgContainer,
              '--border-bottom': `1px solid ${token.colorBorder}`,
            }}
          >
            爆管冲洗
          </NavBar>
          <Spin
            spinning={!!taskId}
            delay={500}
            tip={
              <>
                <span>{taskProcessMag || '开始分析'}:</span>
                <Progress
                  style={{ width: '50%' }}
                  percent={progress}
                />
              </>
            }
          >
            <div style={{ position: 'relative', padding: '10px' }}>
              <Form
                style={{
                  position: 'initial',
                  marginBottom: '50px',
                }}
                preserve={false}
                onFinish={onSubmit}
                ref={modalForm}
                className="selectObjectFrom"
              >
                <Form.Item
                  label="对象ID: "
                  key="object"
                  style={{ marginBottom: token.marginXXS }}
                >
                  {objectFormData && (
                    <Space>
                      <Form.Item
                        noStyle
                        name={`id${objectFormData.key}`}
                        initialValue={objectFormData.oname}
                        style={{ width: 100 }}
                      >
                        <Input
                          className="form-input"
                          readOnly
                          onClick={() => positionToMapView(objectFormData)}
                          prefix={objectFormData.otypeTitle}
                          variant="borderless"
                        />
                      </Form.Item>
                    </Space>
                  )}
                </Form.Item>
                <Form.Item
                  label="冲洗流量(m³/h)"
                  style={{ marginBottom: token.marginXXS }}
                >
                  <InputNumber
                    min={0}
                    max={maxFlushingFlow}
                    value={flushingFlow}
                    onChange={onFlushingFlowChange}
                    variant="borderless"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item
                  label="冲洗流速(m/s)"
                  style={{ marginBottom: token.marginXXS }}
                >
                  <InputNumber
                    value={flushingFlowVelocity}
                    readOnly
                    variant="borderless"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Row justify="space-around">
                  <Col span={18}>
                    <Slider
                      min={0}
                      max={maxFlushingFlow}
                      step={getStep(maxFlushingFlow)}
                      marks={{
                        0: '0 m³/h',
                        [maxFlushingFlow]: `${maxFlushingFlow} m³/h`,
                      }}
                      onChange={onFlushingFlowChange}
                      value={
                        typeof flushingFlow === 'number' ? flushingFlow : 0
                      }
                    />
                  </Col>
                </Row>
                <Form.Item>
                  <Space
                    direction="vertical"
                    style={{ width: '100%' }}
                  >
                    <Button
                      type="primary"
                      htmlType="submit"
                      block
                    >
                      分析
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </Spin>
          {contextHolder}
        </div>
      </Popup>
    ),
    burstPipeResult:
      resultVisible && analysisData ? (
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
          }}
        >
          <NavBar
            onBack={() => {
              setResultVisible(false);
              onReset();
            }}
            style={{
              backgroundColor: token.colorBgContainer,
              '--border-bottom': `1px solid ${token.colorBorder}`,
            }}
          >
            冲洗结果
          </NavBar>
          <AnalysisResult
            canSendSMS={hasSendSMS}
            highlightNodeFunc={analysisConfig?.highlightNodeFunc ?? false}
            highlightNode={highlightNode}
            setHighlightNode={(visible) => setHighlightNode(visible)}
            analysisConfig={analysisConfig}
            analysisData={analysisData}
            dimension={dimensionVisible}
            activeThemes={activeThemes}
            analysisThemesConfig={analysisThemesConfig}
            tableSelectedKeys={tableSelectedKeys}
            setTableSelectedKeys={handleSetTableSelectedKeys}
            handleSwitchDimensionVisible={(visible) =>
              setDimensionVisible(visible)
            }
            getWaterMeterList={fetchWaterMeterList}
            onSendSMS={handleSendSMS}
            handleActiveOnChange={handleSetActiveThemes}
            handleLocate={locate}
            handleHover={handleHighlightHover}
            db={curDb()}
          />
        </div>
      ) : null,
  };
}
