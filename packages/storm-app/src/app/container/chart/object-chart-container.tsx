/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import {
  MAP_VIEW_NAME_ONLINE,
  MAP_VIEW_NAME_SOLUTION,
} from '@waterdesk/data/const/map';
import GisObject from '@waterdesk/data/gis-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import { GroupTimeData } from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { getMarkListByDate } from '@waterdesk/request/get-chart-mark';
import { getAllGroupTimeValues } from '@waterdesk/request/get-group-time-values';
import { getWarnListByDate } from '@waterdesk/request/get-warn';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useWarningConfig from 'src/app/core/containers/warning/use-warning-config';
import { curDb, hostApp } from 'src/app/host-app';
import { baseActions, useBaseSlice } from 'src/store/base';
import {
  selectChartDateUpdateTime,
  selectChartEndDate,
  selectChartStartDate,
  selectWarnTypeList,
} from 'src/store/base/selectors';
import { selectionActions, useSelectionSlice } from 'src/store/selection';
import { selectSelectionState } from 'src/store/selection/selector';
import { selectTheme } from 'src/store/theme/selector';
import { selectTimelineDate } from 'src/store/time-line/selectors';
import { userConfigActions } from 'src/store/user-config';
import {
  selectChartWarnMark,
  selectGlobalConfig,
  selectShowRange,
} from 'src/store/user-config/selector';
import ObjectChart from '../../../components/chart/object-chart';

interface Props {
  showToolbox?: boolean;
  visible?: boolean;
}
export default function ObjectChartContainer(props: Props) {
  const { showToolbox, visible } = props;
  useBaseSlice();
  useSelectionSlice();

  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [selectedObjects, setSelectedObjects] = useState<Array<IObjectItem>>(
    [],
  );
  const [title, setTitle] = useState<string>('无选中对象');
  const [currentChartCode, setCurrentChartCode] = useState<
    string | undefined
  >();

  const dispatch = useDispatch();
  const globalConfig = useSelector(selectGlobalConfig);
  const { items, indicatorType, indicatorName, vprop, chartCode } =
    useSelector(selectSelectionState);
  const chartStartDate = useSelector(selectChartStartDate);
  const chartEndDate = useSelector(selectChartEndDate);
  const chartDateUpdateTime = useSelector(selectChartDateUpdateTime);
  const currentDate = useSelector(selectTimelineDate);
  const theme = useSelector(selectTheme);
  const chartWarnMark = useSelector(selectChartWarnMark);
  const showRange = useSelector(selectShowRange);
  const warnTypeList = useSelector(selectWarnTypeList);

  const { nonAssessmentWarnTypes } = useWarningConfig();

  const { schemeType = [] } = globalConfig?.schemeConfig ?? {};

  const getObjectTimeValues = async (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string | string[],
    startDate: string,
    endDate: string,
    includeMinMax?: boolean,
    includeCorrelatedProps?: string[],
  ): Promise<Map<string, GroupTimeData>[]> => {
    const res = await getAllGroupTimeValues(
      globalConfig?.envelopMinMaxField,
      object,
      indicatorOType,
      indicatorOName,
      vprop,
      startDate,
      endDate,
      hostApp().getMapViews(),
      includeMinMax,
      includeCorrelatedProps,
    );

    const values: Map<string, GroupTimeData>[] = [];
    res.forEach((result) => {
      if (result.status === 'Success' && result.values) {
        values.push(result.values);
      }
    });

    return values;
  };

  const fetchWarnList = async (
    startDate: Dayjs,
    endDate: Dayjs,
    otype: string,
    oname: string,
    vprop: 'SDVAL',
  ): Promise<WarnInfoList> => {
    const responseData = await getWarnListByDate(
      {
        startTime: startDate.format('YYYY-MM-DD'),
        endTime: endDate.format('YYYY-MM-DD'),
        indicatorType: otype,
        indicatorName: oname,
        vprop,
        type: nonAssessmentWarnTypes,
        warnTypeList,
      },
      curDb(),
    );
    return responseData.list;
  };

  const fetchMarkList = async (
    startDate: string,
    endDate: string,
    otype: string,
    oname: string,
    vprop: string,
  ): Promise<MarkInfoList> => {
    const responseData = await getMarkListByDate({
      startDate,
      endDate,
      otypeList: [otype],
      onameList: [oname],
      vpropList: [vprop],
    });
    return responseData.list ?? [];
  };

  useEffect(() => {
    if (items && items.length > 0) {
      const selectionCollection =
        hostApp().getMainMapView()?.selectionCollection;
      if (selectionCollection)
        setSelectedObjects(
          selectionCollection.chartObjects.map((m) => m.object),
        );
      else setSelectedObjects([]);

      const selectedItem: IObjectItem | undefined =
        selectionCollection?.firstChartObject;
      if (selectedItem) {
        if (selectedItem instanceof GisObject) {
          setTitle(`${selectedItem.layerName}: ${selectedItem.title}`);
        } else {
          const propertyInfo = curDb().getPropertyInfo(selectedItem.otype);
          if (propertyInfo)
            setTitle(`${propertyInfo.title}: ${selectedItem.title}`);
          else setTitle(selectedItem.title);
        }
      }
    } else setTitle('无选中对象');

    setCurrentChartCode(chartCode);
  }, [chartCode, currentDate]);

  useEffect(() => {
    setDarkMode(theme === 'dark');
  }, [theme]);

  const handleShowWarnMark = (checked: boolean) => {
    dispatch(
      userConfigActions.updateChartWarnMark({
        chartWarnMark: checked,
      }),
    );
  };

  const handleStartDateChange = (value: string) => {
    dispatch(
      baseActions.updateChartDate({
        startDate: dayjs(value).format('YYYY-MM-DD'),
        endDate: chartEndDate,
      }),
    );
  };
  const handleEndDateChange = (value: string) => {
    dispatch(
      baseActions.updateChartDate({
        startDate: chartStartDate,
        endDate: dayjs(value).format('YYYY-MM-DD'),
      }),
    );
  };

  const handleShowRangeArea = (checked: boolean) => {
    dispatch(
      userConfigActions.updateShowRange({
        chartShowMaxMinRange: checked,
      }),
    );
  };

  const handleChangeIndicator = (
    indicatorType: string,
    indicatorName: string,
    vprop?: string,
  ) => {
    dispatch(
      selectionActions.propertyChanged({
        indicatorType,
        indicatorName,
        vprop,
      }),
    );
  };

  return (
    <ObjectChart
      title={title}
      chartCode={currentChartCode}
      selectedObjects={selectedObjects}
      indicatorType={indicatorType}
      indicatorName={indicatorName}
      defaultStartDate={chartStartDate}
      defaultEndDate={chartEndDate}
      dateUpdateTime={chartDateUpdateTime}
      startDateChange={handleStartDateChange}
      endDateChange={handleEndDateChange}
      vprop={vprop}
      currentDate={currentDate}
      getObjectTimeValues={getObjectTimeValues}
      darkMode={darkMode}
      showWarnMark={chartWarnMark}
      showRange={showRange}
      database={curDb()}
      showToolbox={showToolbox}
      showExtraTools={
        hostApp().getMainMapView()?.mapViewName === MAP_VIEW_NAME_ONLINE
      }
      defaultChainBase={
        hostApp().getMainMapView()?.mapViewName === MAP_VIEW_NAME_SOLUTION
      }
      handleShowRangeArea={handleShowRangeArea}
      handleShowWarnMark={handleShowWarnMark}
      getWarnList={fetchWarnList}
      selectedModelLegend={globalConfig?.chartConfig.online.showModel ?? true}
      defaultShowAsLineType={globalConfig?.chartConfig.online.showAsLineType}
      handleSelectIndicator={handleChangeIndicator}
      getMarkList={fetchMarkList}
      schemeType={schemeType}
      visible={visible}
    />
  );
}
