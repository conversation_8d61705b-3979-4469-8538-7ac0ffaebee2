/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import { SCADA_SUMMARY_OBJECT } from '@waterdesk/data/const/system-const';
import {
  AssessmentDataScoreItem,
  AssessmentInfo,
  DashboardFormConfig,
  DeviceDashboardCollection,
  getScoreData,
  SearchForm,
} from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import {
  GetDeviceAssessmentsResponse,
  getDeviceAssessments,
  getDeviceScore,
} from '@waterdesk/request/get-device-assessments';
import { Collapse, Form, Spin } from 'antd';
import { Card, Selector } from 'antd-mobile';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import Details from 'src/components/mini-dashboard/devices-dashboard/details';
import ScoreList from 'src/components/mini-dashboard/devices-dashboard/score-list';
import { SearchPanelHeader } from 'src/components/mini-dashboard/devices-dashboard/style';
import Summary from 'src/components/mini-dashboard/devices-dashboard/summary';
import MiniDashboardWrap from 'src/components/mini-dashboard/mini-dashboard-wrap';
import { baseActions, useBaseSlice } from 'src/store/base';
import { selectDeviceUpdateDate } from 'src/store/base/selectors';
import { highlightActions } from 'src/store/highlight';
import { selectCurrentSceneTitle } from 'src/store/scenes/selectors';
import { useTimelineSlice } from 'src/store/time-line';
import {
  selectTimelineDate,
  selectTimelineDateTime,
} from 'src/store/time-line/selectors';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import { PanelTitle, RunningStateContainerWrapper } from './style';

interface Props {
  open: boolean;
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Panel } = Collapse;

const DevicesDashboardContainer = (props: Props) => {
  useBaseSlice();
  useTimelineSlice();
  const { open } = props;

  const dispatch = useDispatch();

  const collectionRef = useRef<DeviceDashboardCollection>(
    new DeviceDashboardCollection(),
  );
  const timelineDate = useSelector(selectTimelineDate);
  const timelineDateTimeString = useSelector(selectTimelineDateTime);
  const deviceUpdateDate = useSelector(selectDeviceUpdateDate);
  const [scoreData, setScoreData] = useState<AssessmentDataScoreItem[]>([]);
  const [formConfig, setFormConfig] = useState<
    | {
        formItemlist: SearchForm[];
        formValues: DashboardFormConfig | undefined;
        formConfigDate: string | undefined;
      }
    | undefined
  >(undefined);
  const currentSceneTitle = useSelector(selectCurrentSceneTitle);
  const [assessmentData, setAssessmentData] = useState<AssessmentInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const getAssessmentData = (params: DashboardFormConfig): void => {
    const data = collectionRef.current.generateAssessmentData({ ...params });
    setAssessmentData(data);
  };

  const fetchAssessmentDeviceList = async () => {
    try {
      setLoading(true);
      const { otypeList = [], vpropList = [] } =
        hostApp().appConfig.assessmentDevice.dataSource;
      const res: GetDeviceAssessmentsResponse = await getDeviceAssessments({
        otype_list: otypeList.join(','),
        vprop_list: vpropList.join(','),
        time: timelineDate,
      });
      if (res.status === 'Success' && res.devices) {
        collectionRef.current.initialize(
          curDb(),
          res.devices,
          hostApp().appConfig.assessmentDevice.formConfig,
        );
        const formList = collectionRef.current.generateSearchFormList(curDb());
        const values: { [index: string]: string[] } = {};
        formList.forEach(({ initialValues, field }) => {
          values[field as string] = initialValues;
        });

        setFormConfig((state) => {
          if (typeof state === 'undefined') {
            return {
              formItemlist: formList,
              formValues: values,
              formConfigDate: deviceUpdateDate,
            };
          }
          return {
            ...state,
            formConfigDate: deviceUpdateDate,
          };
        });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const handleLocate = (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => {
    const { otype, oname, shape } = locateObj;
    const mapView = hostApp().getMapView(MAP_VIEW_NAME_ONLINE);
    if (mapView) {
      mapView.selectAndNavigate(otype, oname, shape);
    }
  };

  const handleHover = (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => {
    dispatch(highlightActions.updateHoverObject({ hoverObject: locateObj }));
  };

  const handleSetFormValues = (formValues: DashboardFormConfig | undefined) => {
    setFormConfig((state) => {
      if (state) {
        return {
          ...state,
          formValues,
        };
      }
      return state;
    });
  };

  useEffect(() => {
    if (open) {
      getAssessmentData(formConfig?.formValues ?? {});
    }
  }, [open, formConfig]);

  useEffect(() => {
    if (typeof deviceUpdateDate === 'undefined') return;
    fetchAssessmentDeviceList();
  }, [deviceUpdateDate]);

  const [selectRow, setSelectRow] = useState<AssessmentInfo | null>(null);

  const handleFormValuesChange = (
    _: string,
    allValues: DashboardFormConfig,
  ) => {
    if (formConfig) {
      handleSetFormValues(allValues);
    }
  };

  const detailsData = useMemo(() => selectRow?.devices || [], [selectRow]);

  const fetchAssessmentScoreList = async () => {
    const vprops = hostApp().appConfig.assessmentDevice.score.map(
      (scoreItem) => scoreItem.vprop,
    );
    const res = await getDeviceScore(vprops, timelineDate);

    const scereData = getScoreData(
      res.data.values,
      hostApp().appConfig.assessmentDevice.score,
      timelineDateTimeString,
      curDb(),
    );

    setScoreData(scereData);
  };

  const handleOpenChart = (vprop: string) => {
    dispatch(
      baseActions.propertyChartAction({
        arg: {
          indicatorType: undefined,
          indicatorName: undefined,
          pinnedItem: SCADA_SUMMARY_OBJECT,
          vprop,
        },
      }),
    );
  };

  useEffect(() => {
    if (open) {
      fetchAssessmentScoreList();
    }
  }, [open]);

  return (
    <MiniDashboardWrap open={open}>
      <RunningStateContainerWrapper>
        <PanelTitle level={5}>{currentSceneTitle}</PanelTitle>
        <Spin
          spinning={loading ?? false}
          delay={300}
        >
          <Card>
            <SpecificPrefixH2 style={{ margin: '4px' }}>
              <span>分数</span>
            </SpecificPrefixH2>
            <ScoreList
              dataSource={scoreData}
              handleOpenChart={handleOpenChart}
            />
          </Card>
          <Card
            title={
              <SpecificPrefixH2 style={{ margin: '4px' }}>
                <span>汇总</span>
              </SpecificPrefixH2>
            }
          >
            <Collapse bordered={false}>
              {formConfig && formConfig.formItemlist.length > 0 ? (
                <Panel
                  header={<SearchPanelHeader>筛选条件</SearchPanelHeader>}
                  key="search"
                  className="collapse-custom-panel"
                >
                  <Form
                    {...formItemLayout}
                    name="device-form"
                    onValuesChange={handleFormValuesChange}
                    initialValues={formConfig.formValues}
                  >
                    {formConfig.formItemlist.map(
                      ({ fieldName, field, multiple, options }) => (
                        <Form.Item
                          key={field}
                          label={fieldName}
                          name={field}
                          style={{ marginBottom: '10px' }}
                        >
                          <Selector
                            columns={3}
                            options={options.map((item) => ({
                              label: item.label,
                              value: item.value as string | number,
                            }))}
                            defaultValue={['2', '3']}
                            multiple={!!multiple}
                          />
                          {/* <Select
                            mode={multiple ? 'multiple' : undefined}
                            options={options}
                          /> */}
                        </Form.Item>
                      ),
                    )}
                  </Form>
                </Panel>
              ) : null}
            </Collapse>
          </Card>
          <Card>
            <Summary
              data={assessmentData}
              selectRow={selectRow}
              setSelectRow={(selectRow) => setSelectRow(selectRow)}
            />
          </Card>
          <Card>
            <SpecificPrefixH2 style={{ margin: '4px' }}>
              <span>详情 {selectRow ? `(${selectRow?.statusName})` : ''}</span>
            </SpecificPrefixH2>
            <Details
              data={detailsData}
              onLocate={handleLocate}
              onHover={handleHover}
            />
          </Card>
        </Spin>
      </RunningStateContainerWrapper>
    </MiniDashboardWrap>
  );
};

export default DevicesDashboardContainer;
