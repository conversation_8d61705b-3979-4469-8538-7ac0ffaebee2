/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LoadingOutlined } from '@ant-design/icons';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { IObjectItem } from '@waterdesk/data/object-item';
import { SceneType } from '@waterdesk/data/scene';
import { SolutionStatus } from '@waterdesk/data/solution';
import { SolutionDetail } from '@waterdesk/data/solution-detail';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import {
  ObjectFormItem,
  TRACK_CUSTOM,
  TRACK_DOWN,
  TRACK_POLLUTION,
  TRACK_UP,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import {
  AsyncTaskStatus,
  GetAsyncTaskStatusResponse,
} from '@waterdesk/request/get-async-task';
import { getSolutionStateAndQueueCount } from '@waterdesk/request/get-solution-data';
import {
  Alert,
  Button,
  Col,
  Form,
  message,
  Row,
  Switch,
  Tabs,
  TabsProps,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsyncLocate } from 'src/app/hooks/use-async-locate';
import useLocalStorageAsyncTask from 'src/app/hooks/use-local-storage-tasks';
import { curDb, hostApp } from 'src/app/host-app';
import { useSolutionIds } from 'src/app/pages/solution/use-solution-ids';
import DrawerHeader from 'src/components/common/drawer-header/drawer-header';
import LegendItem from 'src/components/legend/legend-item';
import { LegendItemTitle } from 'src/components/legend/style';
import PollutionSpreadAnalysisForm, {
  PollutionSpreadAnalysisFormValues,
} from 'src/components/quick-analysis/pollution-spread-analysis-form';
import { baseActions } from 'src/store/base';
import { highlightActions, useHighlightSlice } from 'src/store/highlight';
import { selectHighlightThemeConfig } from 'src/store/highlight/selectors';
import { scenesActions } from 'src/store/scenes';
import { useSelectionSlice } from 'src/store/selection';
import { selectSelectionItem } from 'src/store/selection/selector';
import { timelineActions } from 'src/store/time-line';
import PollutionSpreadAnalysisResult from './pollution-spread-analysis-result';
import { PollutionSpreadAnalysisWrapper } from './style';

interface PollutionSpreadAnalysisContainerProps {
  open: boolean;
  solutionDetail: SolutionDetail;
  fetchSolutionInfo: (solutionId: string) => void;
  currentSceneId: SceneType | undefined;
  calcId?: string;
  calcTime?: string;
}

type ActiveTabType = 'form' | 'result';

export default function PollutionSpreadAnalysisContainer({
  open,
  currentSceneId,
  solutionDetail,
  fetchSolutionInfo,
  calcId,
  calcTime,
}: PollutionSpreadAnalysisContainerProps) {
  useHighlightSlice();
  useSelectionSlice();
  useHighlightSlice();

  const [messageApi, contextHolder] = message.useMessage();
  const dispatch = useDispatch();
  const selectedItems = useSelector(selectSelectionItem);
  const highlightThemeConfig = useSelector(selectHighlightThemeConfig);
  const [form] = Form.useForm<PollutionSpreadAnalysisFormValues>();
  // const [searchParams, setSearchParams] = useSearchParams();

  const [activeTab, setActiveTab] = useState<ActiveTabType>('form');
  const [selectable, setSelectable] = useState<boolean>(true);
  const [targetObject, setTargetObject] = useState<IObjectItem | undefined>(
    undefined,
  );
  const [analyzing, setAnalyzing] = useState<boolean>(false);
  const [hasExistingResults, setHasExistingResults] = useState<boolean>(false);
  const [originalNode, setOriginalNode] = useState<
    | {
        otype: string;
        oname: string;
        shape?: string;
      }
    | undefined
  >(undefined);
  const [analysisParams, setAnalysisParams] = useState<{
    pollutionStartTime?: string;
    pollutionEndTime?: string;
    simulationEndTime?: string;
  }>({
    pollutionStartTime: '',
    pollutionEndTime: '',
    simulationEndTime: '',
  });
  const [dimensionVisible, setDimensionVisible] = useState<boolean>(false);
  const [tempLegendData, setTempLegendData] = useState<
    (LegendGroupData & { key: string })[]
  >([]);
  const [echartsLinesVisible, setEchartsLinesVisible] = useState<boolean>(true);

  const { locate: handleLocate } = useAsyncLocate();
  const mapView = hostApp().getMainMapView();

  const { solutionId = '', solutionGuid = '' } = useSolutionIds();

  const handleSetSelectable = (selectable: boolean) => {
    setSelectable(selectable);
  };

  const clearHighlightSelectedObject = () => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: 'pollutionAnalysisSelectedObject',
      }),
    );
  };

  const removeObject = () => {
    setTargetObject(undefined);
    clearHighlightSelectedObject();
  };

  const resetHideFeatureTooltipImportant = () => {
    dispatch(baseActions.updateHideFeatureTooltipImportant({}));
    hostApp().redrawMapView();
  };

  const clearHighlight = (highlightLayerName?: string) => {
    dispatch(highlightActions.clearEchartsLines());
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName,
      }),
    );
    dispatch(
      highlightActions.clearThemeConfig({
        highlightLayerName,
      }),
    );
  };

  const handleReset = () => {
    setActiveTab('form');
    handleSetSelectable(true);
    clearHighlight();
    dispatch(highlightActions.resetHighlight());
    resetHideFeatureTooltipImportant();
    removeObject();
  };

  const fetchSimulationStatus = async (
    taskId: string,
  ): Promise<GetAsyncTaskStatusResponse> => {
    const simulationStatusRes = await getSolutionStateAndQueueCount(taskId);
    return new Promise((resolve) => {
      if (simulationStatusRes.status === 'Success') {
        const { status, statusMessage, queueCount } = simulationStatusRes.data;
        resolve({
          status: 'Success',
          taskStatus:
            status === SolutionStatus.IDLE || status === SolutionStatus.ERROR
              ? AsyncTaskStatus.SUCCESS
              : AsyncTaskStatus.IN_PROGRESS,
          message:
            status === 'WAIT_CALC' && queueCount > 0
              ? `等待计算,当前排队${queueCount}个`
              : statusMessage,
        });
      } else {
        resolve({
          status: 'Success',
          taskStatus: AsyncTaskStatus.IN_PROGRESS,
        });
      }
    });
  };

  const handleTaskComplete = (res: GetAsyncTaskStatusResponse) => {
    if (solutionId && res.taskStatus === AsyncTaskStatus.SUCCESS) {
      fetchSolutionInfo(solutionId);
    }
  };

  const { taskId, message: taskProgressMessage } = useLocalStorageAsyncTask(
    solutionId as string,
    {
      enable: true,
      asyncQueryRequest: fetchSimulationStatus,
      delay: 2000,
      callback: (res) => {
        const mapView = hostApp().getMainMapView();
        const viewId = mapView?.getViewId();
        if (typeof viewId === 'undefined') return;
        dispatch(timelineActions.setViewThemeTime());
        fetchSolutionInfo(solutionId as string);
        if (res.taskStatus === AsyncTaskStatus.SUCCESS) {
          messageApi.success('分析完成');

          setActiveTab('result');
          setAnalyzing(false);

          handleTaskComplete(res);
        } else {
          messageApi.error('分析失败');
          setAnalyzing(false);
        }
      },
    },
  );

  const handleHighlightSelectedObject = (object: IObjectItem | undefined) => {
    if (!object) {
      clearHighlightSelectedObject();
      return;
    }

    // 高亮选中的对象
    const highlightData = [
      {
        otype: object.otype,
        oname: object.oname,
        shape: object.shape || '',
        style: {
          fillColor: '#3d76fb',
          strokeColor: '#3d76fb',
          strokeWidth: 2,
          radius: 8,
        },
      },
    ];

    dispatch(
      highlightActions.updateHighlight({
        highlight: {
          pollutionAnalysisSelectedObject: highlightData,
        },
      }),
    );
  };

  const positionToMapView = (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => {
    if (highlightObject.shape)
      hostApp()
        .getMainMapView()
        ?.selectAndNavigate(
          highlightObject.otype,
          highlightObject.oname,
          highlightObject.shape,
        );
  };

  const handleHover = (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => {
    dispatch(
      highlightActions.updateHoverObject({
        hoverObject: highlightObject,
      }),
    );
  };

  // 处理分析完成后的时间更新
  const handleAnalysisComplete = (time: Dayjs) => {
    // 更新时间轴的日期和时间
    dispatch(
      timelineActions.updateTimelineDate({
        timelineDate: time.format('YYYY-MM-DD'),
      }),
    );
    dispatch(
      timelineActions.updateTimelineTime({
        timelineTime: getValueFromDateTime(time),
      }),
    );
  };

  const highlight = (
    highlightObject: {
      waterMeter?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      downScada?: HighlightObject[];
      upScada?: HighlightObject[];
      sourceLink?: HighlightObject[];
    },
    themeConfig?: {
      waterMeter?: LegendGroupData;
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
    mapViewName?: string,
  ) => {
    if (themeConfig) {
      dispatch(
        highlightActions.updateThemeConfig({
          themeConfig,
        }),
      );
    }

    const mainMapView = hostApp().getMainMapView();

    const highlightObj = {
      highlight: highlightObject,
      highlightMapName: mapViewName ?? mainMapView?.mapViewName,
    };
    dispatch(highlightActions.updateHighlight(highlightObj));
  };

  let trackConfig: TrackConfig | undefined;
  const getTrackConfig = (type: TrackType) => {
    let trackConfigData;
    switch (type) {
      case TRACK_UP:
        trackConfigData = hostApp().appConfig.trackUpConfig;
        break;
      case TRACK_DOWN:
        trackConfigData = hostApp().appConfig.trackDownConfig;
        break;
      case TRACK_POLLUTION:
        trackConfigData = hostApp().appConfig.trackPollutionConfig;
        break;
      case TRACK_CUSTOM:
        trackConfigData = hostApp().appConfig.trackCustomConfig;
        break;
      default:
        trackConfigData = hostApp().appConfig.trackColumnsConfig;
    }
    if (!trackConfigData)
      trackConfigData = hostApp().appConfig.trackColumnsConfig;
    if (!trackConfig) {
      trackConfig = new TrackConfig(curDb(), trackConfigData, type);
    }
    return trackConfig;
  };

  const getDmaSwitchData = (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => {
    const theme = themeMap.get(collapse);
    const themeConfig = theme?.find((item) => item.name === value);
    const newHighlightData:
      | TrackDownDma[]
      | TrackDownLink[]
      | HighlightObject[] = [];
    dataSource.forEach((item: any) => {
      newHighlightData.push({
        ...item,
        highlightType: 'custom',
        highlightShowMark: dimensionVisible,
        highlightTextBgColor: '#ffffff',
      });
    });
    const data = {
      highlightData: newHighlightData,
      highlightTheme: themeConfig,
    };
    return data;
  };

  const getLinkSwitchData = (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => {
    const theme = themeMap.get(collapse);
    const themeConfig = theme?.find((item) => item.name === value);
    if (themeConfig) {
      const newHighlightData:
        | TrackDownDma[]
        | TrackDownLink[]
        | HighlightObject[] = [];
      dataSource.forEach((item) => {
        item.highlightObjects.forEach((object: any) => {
          const newObject: TrackDownLink = {
            ...object,
            highlightType: 'custom',
            highlightShowMark: dimensionVisible,
          };
          newHighlightData.push(newObject);
        });
      });
      return {
        highlightData: newHighlightData,
        highlightTheme: themeConfig,
      };
    }
    return undefined;
  };

  useEffect(() => {
    if (selectable && mapView && activeTab === 'form') {
      const selectionCollection = mapView?.selectionCollection;
      if (
        selectionCollection &&
        selectionCollection.selectedObjects.length > 0
      ) {
        const lastSelectedObject =
          selectionCollection.selectedObjects[
            selectionCollection.selectedObjects.length - 1
          ];

        // 只允许选择节点对象
        if (
          lastSelectedObject &&
          lastSelectedObject.otype === 'WDM_JUNCTIONS'
        ) {
          // 如果选择了新的节点，且与原始节点不同，则禁用结果页
          if (
            originalNode &&
            (lastSelectedObject.oname !== originalNode.oname ||
              lastSelectedObject.otype !== originalNode.otype ||
              lastSelectedObject.shape !== originalNode.shape)
          ) {
            setHasExistingResults(false);
          }
          setTargetObject(lastSelectedObject);
        }
      }
    }
  }, [selectedItems, mapView, activeTab, selectable, originalNode]);

  useEffect(() => {
    handleHighlightSelectedObject(targetObject);
  }, [targetObject]);

  useEffect(() => {
    const tempLegendDatas: (LegendGroupData & { key: string })[] = [];
    if (highlightThemeConfig) {
      Object.keys(highlightThemeConfig)?.forEach((item) => {
        const legendData = highlightThemeConfig[item];
        if (legendData) {
          tempLegendDatas.push({ ...legendData, key: item });
        }
      });
    }
    setTempLegendData(tempLegendDatas);
  }, [highlightThemeConfig]);

  useEffect(() => {
    if (echartsLinesVisible) {
      dispatch(highlightActions.updateEchartsLines());
    } else {
      dispatch(highlightActions.clearEchartsLines());
    }
  }, [echartsLinesVisible, highlightThemeConfig]);

  useEffect(() => {
    // 检查是否有已计算的结果
    if (
      solutionDetail?.qualityChangeSettings?.length > 0 &&
      solutionDetail.calculateStartTime &&
      solutionDetail.calculateEndTime
    ) {
      setHasExistingResults(true);
      const originalObject = {
        otype: solutionDetail.qualityChangeSettings[0].otype,
        oname: solutionDetail.qualityChangeSettings[0].id,
        shape: solutionDetail.qualityChangeSettings[0].shape,
      };
      setOriginalNode(originalObject);
      setTargetObject({
        ...originalObject,
      } as IObjectItem);
      setAnalysisParams({
        pollutionStartTime: solutionDetail.calculateStartTime,
        pollutionEndTime: solutionDetail.qualityChangeSettings[0].endTime,
        simulationEndTime: solutionDetail.calculateEndTime,
      });
    }
  }, [solutionDetail]);

  useEffect(() => {
    if (solutionDetail) {
      form.setFieldsValue({
        targetId: targetObject?.oname || '',
        pollutionStartTime: dayjs().set('hour', 0).set('minute', 0),
        pollutionEndTime: dayjs().set('hour', 1).set('minute', 0),
        simulationEndTime: dayjs().set('hour', 8).set('minute', 0),
      });
    }
  }, [form, solutionDetail, targetObject]);

  useEffect(
    () => () => {
      handleReset();
    },
    [],
  );

  const items: TabsProps['items'] = [
    {
      key: 'form',
      label: '分析条件',
      children: (
        <PollutionSpreadAnalysisForm
          form={form}
          canEdit={selectable}
          targetObject={targetObject}
          handleLocate={(otype, oname, shape) => {
            handleLocate(
              otype,
              oname,
              shape,
              undefined,
              undefined,
              undefined,
              'navigate',
            );
          }}
          date={solutionDetail?.baseDayInfo.date}
        />
      ),
    },
    {
      key: 'result',
      label: '模拟结果',
      children: (
        <PollutionSpreadAnalysisResult
          db={curDb()}
          dimension={dimensionVisible}
          targetObject={targetObject}
          pollutionStartTime={analysisParams.pollutionStartTime ?? ''}
          pollutionEndTime={analysisParams.pollutionEndTime ?? ''}
          simulationEndTime={analysisParams.simulationEndTime ?? ''}
          solutionId={solutionId}
          solutionGuid={solutionGuid}
          positionToMapView={positionToMapView}
          handleHover={handleHover}
          highlight={highlight}
          trackResultConfig={getTrackConfig}
          getDmaSwitchData={getDmaSwitchData}
          getLinkSwitchData={getLinkSwitchData}
          onAnalysisComplete={handleAnalysisComplete}
          calcId={calcId}
          calcTime={calcTime}
          clearHighlight={clearHighlight}
        />
      ),
      disabled:
        analyzing ||
        !!taskId ||
        !hasExistingResults ||
        !analysisParams.pollutionStartTime,
    },
  ];

  useEffect(() => {
    if (currentSceneId && open && activeTab === 'result') {
      // 切换到节点污染物主题
      dispatch(
        scenesActions.updateSceneCurrentTheme({
          themeSectionType: 'network',
          currentTheme: {
            value: 'JUNCTION_POLLUTANT',
            label: '节点污染物',
          },
        }),
      );
    }
  }, [activeTab, open, currentSceneId]);

  return (
    <PollutionSpreadAnalysisWrapper>
      <DrawerHeader
        title="污染物扩散分析"
        subTitle={
          hasExistingResults ? undefined : '地图中选择需要分析的节点对象'
        }
      />
      {hasExistingResults && activeTab === 'form' && !analyzing && (
        <Alert
          style={{ margin: '10px' }}
          message={
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>所选节点已计算过，可直接分析</span>
              <Button
                type="primary"
                onClick={() => setActiveTab('result')}
              >
                前往
              </Button>
            </div>
          }
          type="info"
          showIcon
        />
      )}
      {taskId ? (
        <Alert
          style={{ margin: '10px' }}
          message={taskProgressMessage ?? '方案正在计算中...'}
          type="info"
          showIcon
          icon={<LoadingOutlined />}
        />
      ) : null}
      <Tabs
        style={{ padding: '10px 0', margin: '0 10px' }}
        activeKey={activeTab}
        onTabClick={(activeKey) => setActiveTab(activeKey as ActiveTabType)}
        items={items}
      />
      {tempLegendData.length > 0 ? (
        <div className="temp-legend">
          <LegendItemTitle style={{ marginBottom: 0 }}>
            <Row>
              <Col span={12}>显示标注</Col>
              <Col span={12}>
                <Switch
                  checked={dimensionVisible}
                  onChange={(checked) => setDimensionVisible(checked)}
                />
              </Col>
            </Row>
            <Row>
              <Col span={12}>水流动画</Col>
              <Col span={12}>
                <Switch
                  checked={echartsLinesVisible}
                  onChange={(checked) => setEchartsLinesVisible(checked)}
                />
              </Col>
            </Row>
          </LegendItemTitle>
          {tempLegendData.map((legendDataItem) => (
            <Row key={legendDataItem.key}>
              <LegendItem groupData={legendDataItem} />
            </Row>
          ))}
        </div>
      ) : null}
      {contextHolder}
    </PollutionSpreadAnalysisWrapper>
  );
}
