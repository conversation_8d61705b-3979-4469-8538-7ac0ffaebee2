/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PauseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import Database from '@waterdesk/data/database';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { IObjectItem } from '@waterdesk/data/object-item';
import { SHOW_POLLUTION_SPREAD_ANALYSIS_PARAM } from '@waterdesk/data/solution';
import {
  ObjectFormItem,
  ResultData,
  TRACK_POLLUTION_SPREAD,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import { AsyncTaskStatus } from '@waterdesk/request/get-async-task';
import {
  GetPollutionTrackDataAsyncResponse,
  getPollutionSpreadAnalysisDetail,
  getPollutionSpreadResult,
  savePollutionSpreadAnalysis,
} from '@waterdesk/request/track/pollution-data';
import { Button, Form, Input, Modal, message, Space, Spin } from 'antd';
import { SliderMarks } from 'antd/es/slider';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router';
import useLocalStorageAsyncTask from 'src/app/hooks/use-local-storage-tasks';
import { curDb, hostApp } from 'src/app/host-app';
import TrackResult from 'src/components/network-track/track-result';
import {
  AnalysisResultContainer,
  CalcTaskInfo,
  DiffusionTimeDisplay,
  DiffusionTimeLabel,
  DiffusionTimeRow,
  ResultArea,
  SavedAnalysisInfoBox,
  SavedAnalysisNote,
  SavedAnalysisTitle,
  SavedTimeConnectionLine,
  SavedTimePoint,
  SliderContainer,
  StyledAlert,
  StyledSlider,
  TimeSelectionArea,
} from './style';

// 播放速度常量（毫秒）
const PLAY_SPEEDS = {
  SPEED_1X: 6000, // 1x 速度
  SPEED_2X: 3000, // 2x 速度
} as const;

type PlaySpeed = (typeof PLAY_SPEEDS)[keyof typeof PLAY_SPEEDS];

interface PollutionSpreadAnalysisResultProps {
  db: Database;
  dimension: boolean;
  targetObject: IObjectItem | undefined;
  pollutionStartTime: string;
  pollutionEndTime: string;
  simulationEndTime: string;
  solutionId: string;
  solutionGuid: string;
  positionToMapView: (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => void;
  handleHover: (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => void;
  highlight: (
    highlightObject: {
      selectedPollutedObject?: HighlightObject[];
      selectedUnPollutedObject?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
    },
    themeConfig?: {
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => void;
  trackResultConfig: (type: TrackType) => TrackConfig;
  getDmaSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => any;
  getLinkSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => any;
  onAnalysisComplete?: (time: Dayjs) => void;
  calcId?: string;
  calcTime?: string;
  clearHighlight: () => void;
}

const PollutionSpreadAnalysisResult = ({
  db,
  dimension,
  targetObject,
  pollutionStartTime,
  pollutionEndTime,
  simulationEndTime,
  solutionId,
  solutionGuid,
  positionToMapView,
  handleHover,
  highlight,
  trackResultConfig,
  getDmaSwitchData,
  getLinkSwitchData,
  onAnalysisComplete,
  calcId,
  calcTime,
  clearHighlight,
}: PollutionSpreadAnalysisResultProps) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const showPollutionSpreadAnalysis = searchParams.get(
    SHOW_POLLUTION_SPREAD_ANALYSIS_PARAM,
  );

  const [resultData, setResultData] = useState<ResultData[]>([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saving, setSaving] = useState(false);
  const [saveForm] = Form.useForm();
  const [savedAnalysisTitle, setSavedAnalysisTitle] = useState<string>('');
  const [savedAnalysisNote, setSavedAnalysisNote] = useState<string>('');
  const [loadingAnalysis, setLoadingAnalysis] = useState(false);
  const [diffusionTime, setDiffusionTime] = useState<Dayjs | null>(
    simulationEndTime ? dayjs(simulationEndTime) : null,
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const playIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [playSpeed, setPlaySpeed] = useState<PlaySpeed>(PLAY_SPEEDS.SPEED_1X);
  const currentSliderValueRef = useRef<number>(0);
  const [savedTimes, setSavedTimes] = useState<string[]>([]);

  // 获取基准日期（使用污染开始时间的日期部分）
  const getBaseDate = (): Dayjs => {
    if (pollutionStartTime) {
      return dayjs(pollutionStartTime).startOf('day');
    }
    return dayjs().startOf('day'); // 默认情况下使用当天
  };

  // 获取时间轴的最小值（分钟数，相对于基准日期的0点）
  const getMinutes = (timeStr: string | null | undefined): number => {
    if (!timeStr) return 0;
    const baseDate = getBaseDate();
    const time = dayjs(timeStr);
    const minutes = time.diff(baseDate, 'minute');
    // 对齐到最近的5分钟
    return Math.floor(minutes / 5) * 5;
  };

  // 计算时间轴范围
  const minValue = getMinutes(pollutionStartTime) || 0;
  const maxValue = getMinutes(simulationEndTime) || 1440; // 默认24小时的分钟数

  // 从分钟数创建对应的时间对象
  const createTimeFromMinutes = (minutes: number | undefined): Dayjs => {
    const mins = minutes || 0;
    const baseDate = getBaseDate();
    return baseDate.add(mins, 'minute');
  };

  // 创建标记点
  const getTimeMarks = (): SliderMarks => {
    // 基础标记点
    const marks: SliderMarks = {};

    // 只有在有效时间范围内添加标记
    if (pollutionStartTime && simulationEndTime) {
      // 调整到最近的5分钟刻度
      const roundedMinValue = Math.floor(minValue / 5) * 5;
      const roundedMaxValue = Math.ceil(maxValue / 5) * 5;

      // 添加起始和结束标记
      marks[roundedMinValue] = {
        style: { color: '#1890ff', transform: 'translateX(-40%)' },
        label: createTimeFromMinutes(roundedMinValue).format('HH:mm'),
      };

      marks[roundedMaxValue] = {
        style: { color: '#1890ff', transform: 'translateX(-60%)' },
        label: createTimeFromMinutes(roundedMaxValue).format('HH:mm'),
      };

      const totalPoints = 5;
      const middlePoints = totalPoints - 2;
      const range = roundedMaxValue - roundedMinValue;
      const interval = Math.floor(range / (middlePoints + 1));

      // 添加中间标记点
      Array.from({ length: middlePoints }).forEach((_, i) => {
        const markValue = roundedMinValue + interval * (i + 1);
        // 确保标记值对齐到5分钟
        const alignedMarkValue = Math.round(markValue / 5) * 5;

        // 避免与边界标记太近
        if (
          alignedMarkValue - roundedMinValue >= 20 &&
          roundedMaxValue - alignedMarkValue >= 20
        ) {
          marks[alignedMarkValue] = {
            style: { color: '#666', transform: 'translateX(-40%)' },
            label: createTimeFromMinutes(alignedMarkValue).format('HH:mm'),
          };
        }
      });
    }

    return marks;
  };

  // 清空所有分析结果相关的状态
  const clearAnalysisStates = () => {
    setResultData([]);
    setSavedAnalysisTitle('');
    setSavedAnalysisNote('');
    setSavedTimes([]);
    saveForm.resetFields();

    // 清除地图上的高亮效果
    clearHighlight();
  };

  const { taskId, message: taskProcessMsg } = useLocalStorageAsyncTask(
    'pollutionSpreadAnalysisTask',
    {
      enable: true,
      progressStep: hostApp().appConfig.progressStep,
      progressFixed: true,
      formatValues: (values) =>
        getPollutionSpreadResult(values, db, hostApp().getMainMapView()),
      callback: (res: GetPollutionTrackDataAsyncResponse) => {
        const { values, taskStatus, errorMessage } = res;
        if (taskStatus === AsyncTaskStatus.SUCCESS && values) {
          const resultData = {
            downDmaDatas: values.polluted?.pollutedDmaDatas,
            downLinkDatasMap: values.polluted?.pollutedLinkDatasMap,
            downLinkDatas: values.polluted?.pollutedLinkDatas,
            downScadaDatas: values.polluted?.pollutedScadaDatas,
            downWaterMeterDatas: values.polluted?.waterMeterDatas,
            linkSourceDatas: values.pollutedSource?.pollutedSourceLinkDatas,
            linkSourceDatasMap:
              values.pollutedSource?.pollutedSourceLinkDatasMap,
          };

          setResultData([resultData]);
          // 分析成功后，更新时间轴时间
          if (diffusionTime && onAnalysisComplete) {
            onAnalysisComplete(diffusionTime);
          }
        } else if (taskStatus === AsyncTaskStatus.FAIL) {
          messageApi.error(
            `分析失败${errorMessage ? `: ${errorMessage}` : ''}`,
          );
        }
      },
    },
  );

  const loadAnalysisResult = async (time?: Dayjs) => {
    if (!solutionId) return;

    try {
      setLoadingAnalysis(true);

      const currentDiffusionTime = (time || diffusionTime)?.format(
        'YYYY-MM-DD HH:mm:ss',
      );
      if (!currentDiffusionTime) return;

      const response = await getPollutionSpreadAnalysisDetail(
        solutionId,
        currentDiffusionTime,
      );

      if (response.status === 'Success') {
        const {
          analysisResult,
          analysisTitle,
          analysisNote,
          savedTimes: responseSavedTimes,
        } = response;

        if (responseSavedTimes && Array.isArray(responseSavedTimes)) {
          const timeStrings = responseSavedTimes
            .map((item: any) =>
              typeof item === 'string'
                ? item
                : item.saved_time || item.savedTime,
            )
            .filter(Boolean);
          setSavedTimes(timeStrings);
        }

        // 无论当前时间点是否有分析结果数据，都设置标题和备注
        if (analysisTitle || analysisNote) {
          setSavedAnalysisTitle(analysisTitle || '');
          setSavedAnalysisNote(analysisNote || '');

          // 设置保存表单的值
          saveForm.setFieldsValue({
            title: analysisTitle,
            note: analysisNote,
          });

          // 更新 URL 参数
          searchParams.set(SHOW_POLLUTION_SPREAD_ANALYSIS_PARAM, 'true');
          setSearchParams(searchParams);
        }

        const hasAnalysisResult =
          analysisResult &&
          Array.isArray(analysisResult) &&
          analysisResult.length > 0;

        if (hasAnalysisResult) {
          setResultData(analysisResult);

          const currentMapView = hostApp().getMainMapView();
          if (currentMapView && analysisResult[0]) {
            const result = analysisResult[0];
            // 处理下游管道数据
            const downLink: HighlightObject[] = [];
            (result.downLinkDatasMap ?? new Map()).forEach((item: any) => {
              if (
                item.highlightObjects &&
                Array.isArray(item.highlightObjects)
              ) {
                downLink.push(...item.highlightObjects);
              }
            });

            // 处理源头管道数据
            const sourceLink: HighlightObject[] = [];
            (result.linkSourceDatasMap ?? new Map()).forEach((item: any) => {
              if (
                item.highlightObjects &&
                Array.isArray(item.highlightObjects)
              ) {
                sourceLink.push(...item.highlightObjects);
              }
            });

            // 设置 trackHighlightObject
            currentMapView.trackHighlightObject = {
              downDma: result.downDmaDatas || [],
              downLink,
              sourceLink,
            };
          }

          // 当加载保存的分析结果时，检查计算任务信息
          if (response.calcId && response.calcTime && calcId && calcTime) {
            if (response.calcId !== calcId || response.calcTime !== calcTime) {
              messageApi.warning(
                '当前方案计算结果已更新，保存的分析结果可能已过时',
              );
            }
          }
        } else {
          // 只清除结果数据，不清除标题和备注
          setResultData([]);

          console.log('没有分析结果');

          // 清除水流动画和所有污染物显示
          const currentMapView = hostApp().getMainMapView();
          if (currentMapView) {
            // 先将trackHighlightObject设为空对象，确保所有高亮都被清除
            currentMapView.trackHighlightObject = {};
          }

          // 调用clearHighlight以确保完全清除
          clearHighlight();
        }
      } else {
        // API调用失败时才清除所有状态
        clearAnalysisStates();
      }
    } catch (error) {
      console.error('加载分析结果失败:', error);
      clearAnalysisStates();
      messageApi.error('加载分析结果失败');
    } finally {
      setLoadingAnalysis(false);
    }
  };

  const handleSaveModalOk = async () => {
    try {
      if (!targetObject || !diffusionTime) {
        messageApi.warning('请先进行影响分析');
        return;
      }

      setSaving(true);
      const saveFormValues = await saveForm.validateFields();

      const requestData = {
        solutionId,
        solutionGuid,
        objects: [
          {
            id: targetObject.oname,
            type: targetObject.otype,
          },
        ],
        pollutionStartTime,
        pollutionEndTime,
        simulationEndTime,
        diffusionTime: diffusionTime.format('YYYY-MM-DD HH:mm:ss'),
        analysisTitle: saveFormValues.title,
        analysisNote: saveFormValues.note,
        analysisResult: resultData,
        calcId,
        calcTime,
      };

      const response = await savePollutionSpreadAnalysis(requestData);
      if (response.status === 'Success' && response.data?.solution_id) {
        messageApi.success('保存分析结果成功');
        setSavedAnalysisTitle(saveFormValues.title);
        setSavedAnalysisNote(saveFormValues.note || '');
        setSaveModalVisible(false);

        searchParams.set(SHOW_POLLUTION_SPREAD_ANALYSIS_PARAM, 'true');
        setSearchParams(searchParams);
      } else {
        messageApi.error(response.errorMessage || '保存分析结果失败');
      }
    } catch (error) {
      messageApi.error(`保存分析结果失败:${error}`);
    } finally {
      setSaving(false);
    }
  };

  const handleSaveModalCancel = () => {
    setSaveModalVisible(false);
    saveForm.resetFields();
  };

  // 处理滑块变化（拖动过程中）
  const handleSliderChange = (value: number) => {
    // 确保值是5的倍数
    const alignedValue = Math.round(value / 5) * 5;
    const newTime = createTimeFromMinutes(alignedValue);
    setDiffusionTime(newTime);
    // 在拖动过程中不触发分析完成回调，不发送请求
  };

  // 处理滑块变化完成（拖动结束）
  const handleSliderChangeComplete = (value: number) => {
    const alignedValue = Math.round(value / 5) * 5;

    // 创建时间对象
    const newTime = createTimeFromMinutes(alignedValue);
    loadAnalysisResult(newTime);

    // 只在拖动完成时触发分析完成回调，发送请求
    if (onAnalysisComplete) {
      onAnalysisComplete(newTime);
    }
  };

  const getCurrentSliderValue = (): number => {
    if (!diffusionTime) return minValue;
    const value = getMinutes(diffusionTime.format('YYYY-MM-DD HH:mm:ss'));
    currentSliderValueRef.current = value;
    return value;
  };

  // 计算已保存时间点在滑块上的百分比位置
  const savedTimePositions = useMemo((): Array<{
    time: string;
    position: number;
    minutes: number;
    pointType: 'isolated' | 'start' | 'middle' | 'end';
  }> => {
    if (!savedTimes.length || !pollutionStartTime || !simulationEndTime) {
      return [];
    }

    const range = maxValue - minValue;
    if (range <= 0) return [];

    const positions: Array<{
      time: string;
      position: number;
      minutes: number;
      pointType: 'isolated' | 'start' | 'middle' | 'end';
    }> = [];

    savedTimes.forEach((savedTime) => {
      const savedMinutes = getMinutes(savedTime);
      if (savedMinutes >= minValue && savedMinutes <= maxValue) {
        positions.push({
          time: savedTime,
          position: ((savedMinutes - minValue) / range) * 100,
          minutes: savedMinutes,
          pointType: 'isolated',
        });
      }
    });

    positions.sort((a, b) => a.minutes - b.minutes); // 按时间排序

    // 标记连续时间段
    let i = 0;
    while (i < positions.length) {
      let j = i;
      // 找到连续的时间点序列
      while (
        j < positions.length - 1 &&
        positions[j + 1].minutes - positions[j].minutes === 5
      ) {
        j += 1;
      }

      // 如果找到了连续序列（至少2个点）
      if (j > i) {
        // 标记起始点
        positions[i].pointType = 'start';
        // 标记中间点
        for (let k = i + 1; k < j; k += 1) {
          positions[k].pointType = 'middle';
        }
        // 标记结束点
        positions[j].pointType = 'end';
        i = j + 1;
      } else {
        // 孤立点保持 'isolated'
        i += 1;
      }
    }

    return positions;
  }, [savedTimes, minValue, maxValue]);

  // 获取相邻时间点的连接线
  const connectionLines = useMemo((): Array<{
    startPosition: number;
    endPosition: number;
    pointCount: number;
  }> => {
    const positions = savedTimePositions;
    const lines: Array<{
      startPosition: number;
      endPosition: number;
      pointCount: number;
    }> = [];

    let i = 0;
    while (i < positions.length) {
      let j = i;
      // 找到连续的时间点序列
      while (
        j < positions.length - 1 &&
        positions[j + 1].minutes - positions[j].minutes === 5
      ) {
        j += 1;
      }

      // 如果找到了连续序列（至少2个点）
      if (j > i) {
        const pointCount = j - i + 1; // 连接的点数
        lines.push({
          startPosition: positions[i].position,
          endPosition: positions[j].position,
          pointCount,
        });
        i = j + 1;
      } else {
        i += 1;
      }
    }

    return lines;
  }, [savedTimePositions]);

  const handleAutoPlayChange = () => {
    const currentValue = currentSliderValueRef.current;
    let newTime = createTimeFromMinutes(currentValue);

    if (currentValue >= maxValue) {
      currentSliderValueRef.current = minValue;
      newTime = createTimeFromMinutes(minValue);
    } else {
      currentSliderValueRef.current = currentValue + 5;
      newTime = createTimeFromMinutes(currentValue + 5);
    }
    setDiffusionTime(newTime);
    loadAnalysisResult(newTime);

    if (onAnalysisComplete) {
      onAnalysisComplete(newTime);
    }
  };

  const stopPlay = () => {
    if (playIntervalRef.current) {
      clearInterval(playIntervalRef.current);
      playIntervalRef.current = null;
    }
    setIsPlaying(false);
  };

  const startPlay = () => {
    if (!pollutionStartTime || !simulationEndTime) return;

    stopPlay();
    setIsPlaying(true);

    handleAutoPlayChange();

    playIntervalRef.current = setInterval(() => {
      const currentValue = currentSliderValueRef.current;
      if (currentValue >= maxValue) {
        setIsPlaying(false);
        stopPlay();
      } else {
        handleAutoPlayChange();
      }
    }, playSpeed);
  };

  const togglePlaySpeed = () => {
    setPlaySpeed((prevSpeed) =>
      prevSpeed === PLAY_SPEEDS.SPEED_1X
        ? PLAY_SPEEDS.SPEED_2X
        : PLAY_SPEEDS.SPEED_1X,
    );

    // 如果正在播放，重新初始化播放间隔
    if (isPlaying) {
      stopPlay();
      startPlay();
    }
  };

  useEffect(() => {
    if (showPollutionSpreadAnalysis && solutionId && diffusionTime) {
      loadAnalysisResult();
      if (onAnalysisComplete) {
        onAnalysisComplete(diffusionTime);
      }
    }
  }, [showPollutionSpreadAnalysis, solutionId]);

  useEffect(
    () => () => {
      clearAnalysisStates();
    },
    [],
  );

  // 当污染物注入开始时间或模拟结束时间变化时，重置滑块值
  useEffect(() => {
    if (pollutionStartTime) {
      // 默认选择污染物起始时间作为初始时间点
      const initialValue = getMinutes(pollutionStartTime);
      const newTime = createTimeFromMinutes(initialValue);
      setDiffusionTime(newTime);
    }
  }, [pollutionStartTime]);

  useEffect(() => {
    if (simulationEndTime) {
      const alignedEndTime = getMinutes(simulationEndTime);
      setDiffusionTime(createTimeFromMinutes(alignedEndTime));
    }
  }, [simulationEndTime]);

  return (
    <AnalysisResultContainer>
      <Spin
        spinning={!!taskId || loadingAnalysis}
        delay={500}
        tip={
          <div>
            <span>
              {loadingAnalysis
                ? '加载分析结果...'
                : (taskProcessMsg ?? '分析中')}
            </span>
          </div>
        }
      >
        <div>
          {showPollutionSpreadAnalysis && savedAnalysisTitle && (
            <SavedAnalysisInfoBox>
              <SavedAnalysisTitle>{savedAnalysisTitle}</SavedAnalysisTitle>
              {savedAnalysisNote && (
                <SavedAnalysisNote>{savedAnalysisNote}</SavedAnalysisNote>
              )}
            </SavedAnalysisInfoBox>
          )}
          <StyledAlert
            description={
              <div>
                <p>节点 {targetObject?.oname}</p>
                <p>
                  污染物注入时间：
                  {pollutionStartTime
                    ? dayjs(pollutionStartTime).format('HH:mm')
                    : '-'}{' '}
                  至{' '}
                  {pollutionEndTime
                    ? dayjs(pollutionEndTime).format('HH:mm')
                    : '-'}
                  <br />
                  模拟结束时间：
                  {simulationEndTime
                    ? dayjs(simulationEndTime).format('HH:mm')
                    : '-'}
                </p>
                {calcId && calcTime && (
                  <CalcTaskInfo>
                    计算任务ID: {calcId}
                    <br />
                    计算完成时间:{' '}
                    {dayjs(calcTime).format('YYYY-MM-DD HH:mm:ss')}
                  </CalcTaskInfo>
                )}
                <TimeSelectionArea>
                  <DiffusionTimeRow>
                    <DiffusionTimeLabel>模拟扩散时间：</DiffusionTimeLabel>
                    <Space>
                      <DiffusionTimeDisplay>
                        {diffusionTime?.format('HH:mm')}
                      </DiffusionTimeDisplay>
                      <Button
                        type={isPlaying ? 'primary' : 'default'}
                        icon={
                          isPlaying ? (
                            <PauseCircleOutlined />
                          ) : (
                            <PlayCircleOutlined />
                          )
                        }
                        size="small"
                        onClick={isPlaying ? stopPlay : startPlay}
                        disabled={!pollutionStartTime || !simulationEndTime}
                      />
                      {isPlaying && (
                        <Button
                          type="primary"
                          onClick={togglePlaySpeed}
                          size="small"
                        >
                          {playSpeed === PLAY_SPEEDS.SPEED_1X ? '1x' : '2x'}
                        </Button>
                      )}
                    </Space>
                  </DiffusionTimeRow>
                  <SliderContainer>
                    {connectionLines.map((line) => (
                      <SavedTimeConnectionLine
                        key={`line-${line.startPosition}-${line.endPosition}`}
                        startPosition={line.startPosition}
                        endPosition={line.endPosition}
                      />
                    ))}
                    {savedTimePositions
                      .filter((item) => item.pointType !== 'middle')
                      .map((item) => (
                        <SavedTimePoint
                          key={item.time}
                          position={item.position}
                        />
                      ))}
                    <StyledSlider
                      min={minValue}
                      max={maxValue}
                      step={5}
                      value={getCurrentSliderValue()}
                      onChange={handleSliderChange}
                      onChangeComplete={handleSliderChangeComplete}
                      marks={getTimeMarks()}
                      tooltip={{
                        formatter: (value) => {
                          const timeObj = createTimeFromMinutes(value);
                          return timeObj.format('HH:mm');
                        },
                        placement: 'top',
                      }}
                      disabled={
                        !pollutionStartTime || !simulationEndTime || isPlaying
                      }
                      included
                    />
                  </SliderContainer>
                </TimeSelectionArea>
              </div>
            }
          />
          {/* <ButtonArea>
            <Button
              type="primary"
              onClick={handleAnalyze}
              loading={loading}
              disabled={isPlaying}
            >
              影响分析
            </Button>
            {hasSavedAnalysis && (
              <Button
                type="primary"
                onClick={handleSaveAnalysis}
                disabled={resultData.length === 0 || isPlaying}
              >
                保存分析结果
              </Button>
            )}
          </ButtonArea> */}

          {resultData.length > 0 ? (
            <ResultArea>
              <TrackResult
                db={curDb()}
                dimension={dimension}
                resultData={resultData[0]}
                type={TRACK_POLLUTION_SPREAD}
                positionToMapView={positionToMapView}
                handleHover={handleHover}
                highlight={highlight}
                trackResultConfig={trackResultConfig}
                getDmaSwitchData={getDmaSwitchData}
                getLinkSwitchData={getLinkSwitchData}
                // style={{
                //   maxHeight: 'calc(100vh - 160px)',
                //   overflow: 'auto',
                // }}
              />
            </ResultArea>
          ) : null}
        </div>
        {contextHolder}
      </Spin>

      <Modal
        title="保存污染物扩散分析结果"
        open={saveModalVisible}
        onCancel={handleSaveModalCancel}
        confirmLoading={saving}
        footer={[
          <Button
            key="cancel"
            onClick={handleSaveModalCancel}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={saving}
            onClick={handleSaveModalOk}
          >
            保存分析结果
          </Button>,
        ]}
      >
        <Form
          form={saveForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="标题"
            required
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item
            name="note"
            label="备注"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入备注"
            />
          </Form.Item>
        </Form>
      </Modal>
    </AnalysisResultContainer>
  );
};

export default PollutionSpreadAnalysisResult;
