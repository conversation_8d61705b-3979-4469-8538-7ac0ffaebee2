/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Alert, Slider } from 'antd';
import styled from 'styled-components';

export const PollutionSpreadAnalysisWrapper = styled.div`
  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .ant-tabs-content {
    height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .resetBtn {
    margin-left: 8px;
  }

  .temp-legend {
    position: absolute;
    right: -240px;
    bottom: 0;
    width: 240px;
    padding: 0 10px 10px;
    border-radius: 5px;
    background: ${({ theme }) => theme.colorBgElevated};
  }
`;

export const AnalysisResultContainer = styled.div`
  padding: 5px;
`;

export const SavedAnalysisInfoBox = styled.div`
  margin-bottom: 16px;
  padding: 12px;
  background: #f0f5ff;
  border-radius: 4px;
  border: 1px solid #d9e3ff;
`;

export const SavedAnalysisTitle = styled.div`
  font-weight: bold;
  font-size: 16px;
`;

export const SavedAnalysisNote = styled.div`
  margin-top: 8px;
  color: #666;
`;

export const CalcTaskInfo = styled.p`
  font-size: 12px;
  color: #666;
`;

export const TimeSelectionArea = styled.div`
  margin-top: 16px;
`;

export const DiffusionTimeRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

export const DiffusionTimeLabel = styled.span`
  margin-right: 8px;
`;

export const DiffusionTimeDisplay = styled.span`
  font-weight: bold;
  min-width: 80px;
`;

export const SliderContainer = styled.div`
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px 20px 30px 20px;
  background: #fafafa;
  margin-bottom: 15px;
  position: relative;
`;

export const StyledSlider = styled(Slider)`
  margin-top: 15px;
`;

export const AlertContainer = styled.div`
  margin: 16px 0px;
`;

export const StyledAlert = styled(Alert)`
  margin: 16px 0px;
`;

export const ButtonArea = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
`;

export const ResultArea = styled.div`
  margin-top: 20px;
`;

export const TrackResultContainer = styled.div`
  max-height: calc(100vh - 160px);
  overflow: auto;
`;

export const SavedTimeConnectionLine = styled.div<{
  startPosition: number;
  endPosition: number;
}>`
  position: absolute;
  top: 31px;
  left: ${(props) => 9.5 + (props.startPosition * 81) / 100}%;
  width: ${(props) => ((props.endPosition - props.startPosition) * 81) / 100}%;
  height: 4px;
  background-color: #832ee4;
  z-index: 3;
  transform: translateY(-50%);
  pointer-events: none;
  border-radius: 2px;
`;

export const SavedTimePoint = styled.div<{ position: number }>`
  position: absolute;
  top: 27px;
  left: ${(props) => 9.5 + (props.position * 81) / 100}%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #832ee4;
  z-index: 2;
  box-shadow: 0 0 0 1px #fff;
  pointer-events: none;
`;
