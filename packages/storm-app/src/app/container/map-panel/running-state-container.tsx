/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InfoCircleTwoTone, LineChartOutlined } from '@ant-design/icons';
import { getHelpDescription } from '@waterdesk/data/const/help';
import {
  MODEL_SUMMARY_OBJECT,
  SCADA_SUMMARY_OBJECT,
} from '@waterdesk/data/const/system-const';
import InvisibleObject from '@waterdesk/data/invisible-object';
import { totalWaterSupply } from '@waterdesk/data/mini-dashboard/running-state-data';
import { DmaRelatedDevice } from '@waterdesk/data/property/dma-related-devices-category';
import { GroupTimeData, TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { formatNumber } from '@waterdesk/data/utils';
import { getDmaRelatedDevices } from '@waterdesk/request/get-dma-related-devices';
import { getAssignObjectTimeValues } from '@waterdesk/request/get-group-time-values';
import { Button, Col, Modal, Row, Space, Statistic, Tabs } from 'antd';
import { Card } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import CountUp from 'react-countup';
import { useDispatch, useSelector } from 'react-redux';
import WrapperHelpIcon from 'src/app/core/containers/help-icon-container';
import NoticeValveContainer from 'src/app/core/containers/notice-valve-container';
import { curDb, hostApp } from 'src/app/host-app';
import MiniDashboardWrap from 'src/components/mini-dashboard/mini-dashboard-wrap';
import WaterSupplyChart from 'src/components/mini-dashboard/running-state/water-supply-chart';
import DmaRelatedDeviceGroup from 'src/components/property/dma-related-device-group';
import { baseActions, useBaseSlice } from 'src/store/base';
import {
  selectDataInitialComplete,
  selectNoticeValveList,
} from 'src/store/base/selectors';
import { BottomTabKey } from 'src/store/base/types';
import { selectCurrentSceneTitle } from 'src/store/scenes/selectors';
import { useTimelineSlice } from 'src/store/time-line';
import {
  selectTimelineDate,
  selectTimelineDateTime,
} from 'src/store/time-line/selectors';
import { RunningStateContainerWrapper } from './style';

interface Props {
  open: boolean;
}

const statisticValueStyle = {
  fontSize: '20px',
  lineHeight: '20px',
};

const formatter = (value: number) => (
  <CountUp
    decimals={2}
    end={value}
    separator=","
    duration={0.3}
    formattingFn={() => value as unknown as string}
  />
);

const RunningStateContainer = (props: Props) => {
  useBaseSlice();
  useTimelineSlice();

  const { open } = props;

  const { showPredict } = hostApp().appConfig;

  const dispatch = useDispatch();
  const dataInitialComplete = useSelector(selectDataInitialComplete);
  const timelineDate = useSelector(selectTimelineDate);
  const timelineDateTimeString = useSelector(selectTimelineDateTime);
  const currentSceneTitle = useSelector(selectCurrentSceneTitle);
  const noticeValveList = useSelector(selectNoticeValveList);
  const [yesterdayData, setYesterdayData] = useState<TimeData[]>([]);
  const [todayData, setTodayData] = useState<TimeData[]>([]);
  const [predictData, setPredictDate] = useState<TimeData[]>([]);
  const [deviceScore, setDeviceScore] = useState<number>(0);
  const [modelScore, setModelScore] = useState<number>(0);
  const [openDetails, setOpenDetails] = useState<boolean>(false);
  const [predictTotal, setPredictTotal] = useState<number>(0);
  const [yesterdayPredictTotal, setYesterdayPredictTotal] = useState<number>(0);

  const tmFlowUnitFormat: UnitFormat | undefined = useMemo(
    () => curDb().getUnitFormat(SCADA_SUMMARY_OBJECT.otype, 'TM_FLOW'),
    [dataInitialComplete],
  );

  const dayTotalUnitFormat: UnitFormat | undefined = useMemo(
    () => curDb().getUnitFormat(SCADA_SUMMARY_OBJECT.otype, 'CUMULATIVE_FLOW'),
    [dataInitialComplete],
  );

  const deviceScoreUnitFormat: UnitFormat | undefined = useMemo(
    () => curDb().getUnitFormat(SCADA_SUMMARY_OBJECT.otype, 'RELIABILITY'),
    [dataInitialComplete],
  );

  const modelScoreUnitFormat: UnitFormat | undefined = useMemo(
    () => curDb().getUnitFormat(MODEL_SUMMARY_OBJECT.otype, 'TOTAL_SCORE'),
    [dataInitialComplete],
  );

  const tmFlowUnit: string = useMemo(
    () =>
      tmFlowUnitFormat?.unitSymbol ? `(${tmFlowUnitFormat?.unitSymbol})` : '',
    [tmFlowUnitFormat],
  );

  const dayTotalUnit: string = useMemo(
    () =>
      dayTotalUnitFormat?.unitSymbol
        ? `(${dayTotalUnitFormat?.unitSymbol})`
        : '',
    [dayTotalUnitFormat],
  );

  const dayTotalUnitWithoutBracket: string = useMemo(
    () =>
      dayTotalUnitFormat?.unitSymbol ? dayTotalUnitFormat?.unitSymbol : '',
    [dayTotalUnitFormat],
  );

  const deviceScoreUnit: string = useMemo(
    () =>
      deviceScoreUnitFormat?.unitSymbol
        ? `${deviceScoreUnitFormat?.unitSymbol}`
        : '',
    [deviceScoreUnitFormat],
  );

  const modelScoreUnit: string = useMemo(
    () =>
      modelScoreUnitFormat?.unitSymbol
        ? `${modelScoreUnitFormat?.unitSymbol}`
        : '',
    [modelScoreUnitFormat],
  );

  const yesterdayTotal = useMemo((): number => {
    if (yesterdayData.length > 0) {
      const total = totalWaterSupply(
        yesterdayData,
        dayjs(yesterdayData[0].time).format('YYYY-MM-DD 00:00:00'),
        dayjs(yesterdayData[0].time)
          .add(1, 'day')
          .format('YYYY-MM-DD 00:00:00'),
      );
      return total;
    }
    return 0;
  }, [yesterdayData]);

  const todayTotalInHistory = useMemo((): number => {
    if (todayData.length > 0) {
      const total = totalWaterSupply(
        todayData,
        dayjs(todayData[0].time).format('YYYY-MM-DD 00:00:00'),
        dayjs(todayData[0].time).add(1, 'day').format('YYYY-MM-DD 00:00:00'),
      );
      return total;
    }
    return 0;
  }, [todayData]);

  const getObjectTimeValues = async (
    otype: string,
    oname: string,
    vprop: string,
    startDate: string,
    endDate: string,
  ): Promise<Map<string, GroupTimeData>> => {
    const res = await getAssignObjectTimeValues(
      otype,
      oname,
      vprop,
      startDate,
      endDate,
    );

    let values: Map<string, GroupTimeData> = new Map();
    if (res.status === 'Success' && res.values) {
      values = res.values;
    }
    return values;
  };

  const getDeviceList = async (
    otype: string,
    oname: string,
    time: string,
  ): Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }> => {
    const res = await getDmaRelatedDevices(otype, oname, time, curDb());
    if (res.status === 'Success') {
      return {
        inDeviceList: res.inDeviceList ?? [],
        outDeviceList: res.outDeviceList ?? [],
      };
    }
    return {
      inDeviceList: [],
      outDeviceList: [],
    };
  };

  const getYesterdayData = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'TM_FLOW',
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('TM_FLOW')?.timeData || [];
        setYesterdayData(timeData);
      }
    });
  };

  const getTodayData = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'TM_FLOW',
      dayjs(date).format('YYYY-MM-DD'),
      dayjs(date).format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('TM_FLOW')?.timeData || [];
        setTodayData(timeData);
      }
    });
  };

  const getPredictData = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'TM_FLOW_FC1',
      dayjs(date).format('YYYY-MM-DD'),
      dayjs(date).format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('TM_FLOW_FC1')?.timeData || [];
        setPredictDate(timeData);
      }
    });
  };

  const getDeviceScore = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'RELIABILITY',
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('RELIABILITY')?.timeData || [];
        if (timeData.length > 0) {
          setDeviceScore(formatNumber(timeData[0].value, 0));
        }
      }
    });
  };

  const getModelScore = (date: string) => {
    getObjectTimeValues(
      MODEL_SUMMARY_OBJECT.otype,
      MODEL_SUMMARY_OBJECT.oname,
      'TOTAL_SCORE',
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
      dayjs(date).add(-1, 'day').format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('TOTAL_SCORE')?.timeData || [];
        if (timeData.length > 0) {
          setModelScore(formatNumber(timeData[0].value, 0));
        }
      }
    });
  };

  const getPredictDayFlow = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'DAY_FLOW_FC1',
      dayjs(date).format('YYYY-MM-DD'),
      dayjs(date).format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('DAY_FLOW_FC1')?.timeData || [];
        if (timeData.length > 0 && timeData[0].value !== null) {
          setPredictTotal(timeData[0].value);
        } else {
          setPredictTotal(0);
        }
      }
    });
  };

  const getYesterdayPredictDayFlow = (date: string) => {
    getObjectTimeValues(
      SCADA_SUMMARY_OBJECT.otype,
      SCADA_SUMMARY_OBJECT.oname,
      'DAY_FLOW_FC1',
      dayjs(date).subtract(1, 'day').format('YYYY-MM-DD'),
      dayjs(date).subtract(1, 'day').format('YYYY-MM-DD'),
    ).then((data) => {
      if (data) {
        const timeData = data.get('DAY_FLOW_FC1')?.timeData || [];
        if (timeData.length > 0 && timeData[0].value !== null) {
          setYesterdayPredictTotal(timeData[0].value);
        } else {
          setYesterdayPredictTotal(0);
        }
      }
    });
  };

  const handleOpenChart = (
    object: InvisibleObject,
    vprop: string,
    forecast?: boolean,
  ) => {
    dispatch(
      baseActions.propertyChartAction({
        arg: {
          indicatorType: undefined,
          indicatorName: undefined,
          pinnedItem: object,
          vprop,
          forecast,
        },
      }),
    );
    dispatch(
      baseActions.updateBottomTab({
        type: 'ADD',
        activeKey: BottomTabKey.CHARTS,
      }),
    );
  };

  const handleLocate = (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => {
    const { otype, oname, shape } = locateObj;
    const mapView = hostApp().getMainMapView();
    if (mapView) {
      mapView.selectAndNavigate(otype, oname, shape);
    }
  };

  useEffect(() => {
    getYesterdayData(timelineDate);
    getPredictData(timelineDate);
    getDeviceScore(timelineDate);
    getModelScore(timelineDate);
    getPredictDayFlow(timelineDate);
    getYesterdayPredictDayFlow(timelineDate);
  }, [timelineDate]);

  useEffect(() => {
    const timelineDateString = dayjs(timelineDateTimeString).format(
      'YYYY-MM-DD',
    );
    getTodayData(timelineDateString);
  }, [timelineDateTimeString]);

  const isCurrentDay = useMemo(
    () => dayjs(timelineDate).isSame(dayjs(), 'day'),
    [timelineDate],
  );

  const todayTotal = useMemo(() => {
    if (todayData.length > 0) {
      const timelineDateTime = dayjs(timelineDateTimeString);
      const lastTodayDataTime = dayjs(todayData[todayData.length - 1].time);
      const endTime = lastTodayDataTime.isBefore(timelineDateTime)
        ? lastTodayDataTime
        : timelineDateTime;

      const total = totalWaterSupply(
        todayData,
        todayData[0].time,
        endTime.format('YYYY-MM-DD HH:mm:ss'),
      );
      return dayTotalUnitFormat
        ? dayTotalUnitFormat.getValue(total)
        : (formatNumber(total, 2) as number);
    }
    return 0;
  }, [todayData, timelineDate, timelineDateTimeString, dayTotalUnitFormat]);

  const yesterdayTotalFormatValue = useMemo(
    () =>
      dayTotalUnitFormat
        ? (dayTotalUnitFormat.getValue(yesterdayTotal) as number)
        : formatNumber(yesterdayTotal, 2),
    [yesterdayTotal, dayTotalUnitFormat],
  );

  const todayTotalInHistoryFormatValue = useMemo(
    () =>
      dayTotalUnitFormat
        ? (dayTotalUnitFormat.getValue(todayTotalInHistory) as number)
        : formatNumber(todayTotalInHistory, 2),
    [todayTotalInHistory, dayTotalUnitFormat],
  );

  const predictTotalFormatValue = useMemo(
    () =>
      dayTotalUnitFormat
        ? dayTotalUnitFormat.getValue(predictTotal)
        : formatNumber(predictTotal, 2),
    [predictTotal, dayTotalUnitFormat],
  );

  const yesterdayPredictTotalFormatValue = useMemo(() => {
    if (dayTotalUnitFormat) {
      const yesterdayPredict = dayTotalUnitFormat.getValue(
        yesterdayPredictTotal,
      ) as number;
      const difference = yesterdayPredict - yesterdayTotalFormatValue;
      return formatNumber((difference / yesterdayTotalFormatValue) * 100, 2);
    }
    return formatNumber(yesterdayPredictTotal, 2);
  }, [yesterdayPredictTotal, dayTotalUnitFormat]);

  const deviceScoreFormatValue = useMemo(
    () =>
      deviceScoreUnitFormat
        ? (deviceScoreUnitFormat.getValue(deviceScore) as number)
        : formatNumber(deviceScore, 0),
    [deviceScore, deviceScoreUnitFormat],
  );

  const modelScoreFormatValue = useMemo(
    () =>
      modelScoreUnitFormat
        ? (modelScoreUnitFormat.getValue(modelScore) as number)
        : formatNumber(modelScore, 0),
    [modelScore, modelScoreUnitFormat],
  );

  return (
    <MiniDashboardWrap open={open}>
      <RunningStateContainerWrapper>
        <Card title={currentSceneTitle}>
          {showPredict && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: 13,
              }}
            >
              <span>
                {isCurrentDay
                  ? `当日累计水量: ${todayTotal}`
                  : `实际供水量: ${todayTotalInHistoryFormatValue}`}
                {dayTotalUnitWithoutBracket}
              </span>
              <span>
                预测供水量: {predictTotalFormatValue}
                {dayTotalUnitWithoutBracket}
              </span>
            </div>
          )}
        </Card>
        <Card>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span>
              {curDb().getPropertyTitleUnit(
                SCADA_SUMMARY_OBJECT.otype,
                'TM_FLOW',
              )[0] ?? 'TM_FLOW'}
              {tmFlowUnit}
            </span>
            <Space>
              <WrapperHelpIcon title={getHelpDescription('totalFlow')} />
              <InfoCircleTwoTone
                title="查看明细"
                onClick={() => setOpenDetails(true)}
              />
            </Space>
          </div>
        </Card>
        <Card>
          <WaterSupplyChart
            style={{ width: '100%', height: '120px' }}
            date={dayjs(timelineDateTimeString).format('YYYY-MM-DD')}
            yesterdayData={yesterdayData}
            todayData={todayData}
            predictData={showPredict ? predictData : []}
            unitFormat={tmFlowUnitFormat}
            propertyName={
              curDb().getPropertyTitleUnit(
                SCADA_SUMMARY_OBJECT.otype,
                'TM_FLOW',
              )[0] ?? 'TM_FLOW'
            }
          />
        </Card>
        <Card>
          <Row>
            <Col span={12}>
              <Statistic
                title={
                  <div className="total-supply-card-title">
                    <span>
                      昨日总供水量
                      {dayTotalUnit}
                    </span>
                    <Button
                      size="small"
                      style={{ padding: 0 }}
                      title="查看曲线"
                      type="link"
                      onClick={() =>
                        handleOpenChart(
                          SCADA_SUMMARY_OBJECT,
                          'CUMULATIVE_FLOW',
                          true,
                        )
                      }
                      icon={<LineChartOutlined />}
                    />
                  </div>
                }
                value={yesterdayTotalFormatValue}
                suffix={
                  showPredict && yesterdayPredictTotal > 0 ? (
                    <span style={{ fontSize: 14 }}>
                      ({yesterdayPredictTotalFormatValue}%)
                    </span>
                  ) : (
                    ''
                  )
                }
                formatter={(value) =>
                  typeof value === 'number' ? formatter(value as number) : value
                }
                valueStyle={statisticValueStyle}
                precision={2}
              />
              <span className="statistic-subfix">
                {dayjs(timelineDateTimeString)
                  .subtract(1, 'day')
                  .format('YYYY-MM-DD')}
              </span>
            </Col>

            <Col span={12}>
              <div className="score-content">
                <div>
                  <span>
                    设备(昨日)
                    <WrapperHelpIcon
                      title={getHelpDescription('deviceScore')}
                    />
                    :{deviceScoreFormatValue}
                    {deviceScoreUnit}
                  </span>
                  <Button
                    title="设备评分"
                    type="link"
                    style={{
                      width: '24px',
                    }}
                    onClick={() =>
                      handleOpenChart(SCADA_SUMMARY_OBJECT, 'RELIABILITY')
                    }
                    icon={<LineChartOutlined />}
                  />
                </div>
                <div>
                  <span>
                    模型(昨日)
                    <WrapperHelpIcon title={getHelpDescription('modelScore')} />
                    :{modelScoreFormatValue}
                    {modelScoreUnit}
                  </span>
                  <Button
                    title="模型评分"
                    type="link"
                    style={{
                      width: '24px',
                    }}
                    onClick={() =>
                      handleOpenChart(MODEL_SUMMARY_OBJECT, 'TOTAL_SCORE')
                    }
                    icon={<LineChartOutlined />}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Card>
        <Card>
          <Tabs
            size="small"
            tabBarExtraContent={{
              left: (
                <h5
                  className="title"
                  style={{ marginRight: '20px' }}
                >
                  消息中心
                </h5>
              ),
            }}
            tabBarStyle={{
              marginBottom: '0',
            }}
            items={[
              {
                label: `阀门${
                  noticeValveList.length ? `(${noticeValveList.length})` : ''
                }`,
                key: 'valveOperation',
                forceRender: true,
                children: (
                  <div className="message-list">
                    <NoticeValveContainer />
                  </div>
                ),
              },
            ]}
          />
        </Card>

        <Modal
          title={SCADA_SUMMARY_OBJECT.title}
          open={openDetails}
          onCancel={() => setOpenDetails(false)}
          footer={null}
        >
          <DmaRelatedDeviceGroup
            time={dayjs(timelineDateTimeString).format('YYYY-MM-DD HH:mm:ss')}
            dmaType={undefined}
            selectedObject={SCADA_SUMMARY_OBJECT}
            getDeviceList={getDeviceList}
            handleLocate={handleLocate}
          />
        </Modal>
      </RunningStateContainerWrapper>
    </MiniDashboardWrap>
  );
};

RunningStateContainer.displayName = 'RunningStateContainer';

export default RunningStateContainer;
