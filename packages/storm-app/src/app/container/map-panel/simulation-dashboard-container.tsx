/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import { MODEL_SUMMARY_OBJECT } from '@waterdesk/data/const/system-const';
import {
  AssessmentDataScoreItem,
  getScoreData,
  SearchForm,
} from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import {
  AssessmentInfo,
  DashboardFormConfig,
  mergeIndicatorsValues,
  SimulationDashboardCollection,
  SimulationData,
} from '@waterdesk/data/mini-dashboard/simulation-dashboard-data';
import { getSimulationScore } from '@waterdesk/request/get-device-assessments';
import { getSimulationIndicatorList } from '@waterdesk/request/get-simulation-indicator-list';
import { Collapse, Form, Select, Spin } from 'antd';
import { Card } from 'antd-mobile';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import HelpIcon from 'src/components/icon/help-icon';
import ScoreList from 'src/components/mini-dashboard/devices-dashboard/score-list';
import { SearchPanelHeader } from 'src/components/mini-dashboard/devices-dashboard/style';
import MiniDashboardWrap from 'src/components/mini-dashboard/mini-dashboard-wrap';
import Details from 'src/components/mini-dashboard/simulation-dashboard/details';
import Summary from 'src/components/mini-dashboard/simulation-dashboard/summary';
import { baseActions, useBaseSlice } from 'src/store/base';
import { selectDeviceUpdateDate } from 'src/store/base/selectors';
import { highlightActions } from 'src/store/highlight';
import { selectCurrentSceneTitle } from 'src/store/scenes/selectors';
import { useTimelineSlice } from 'src/store/time-line';
import {
  selectTimelineDate,
  selectTimelineDateTime,
} from 'src/store/time-line/selectors';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import { PanelTitle, RunningStateContainerWrapper } from './style';

interface Props {
  open: boolean;
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const SimulationDashboardContainer = ({ open }: Props) => {
  useBaseSlice();
  useTimelineSlice();

  const dispatch = useDispatch();
  const timelineDate = useSelector(selectTimelineDate);
  const deviceUpdateDate = useSelector(selectDeviceUpdateDate);
  const timelineDateTimeString = useSelector(selectTimelineDateTime);
  const currentSceneTitle = useSelector(selectCurrentSceneTitle);
  const collectionRef = useRef<SimulationDashboardCollection>(
    new SimulationDashboardCollection(),
  );
  const [formConfig, setFormConfig] = useState<
    | {
        formItemList: SearchForm[];
        formValues: DashboardFormConfig | undefined;
        formConfigDate: string | undefined;
      }
    | undefined
  >(undefined);
  const [simulationData, setSimulationData] = useState<SimulationData>({
    totalScore: 0,
    pressScore: 0,
    flowScore: 0,
    list: [],
  });

  const { meanAbsoluteError, score } = hostApp().appConfig.assessmentSimulation;

  const [loading, setLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string | undefined>(undefined);
  const [selectRow, setSelectRow] = useState<AssessmentInfo | null>(null);
  const [scoreData, setScoreData] = useState<AssessmentDataScoreItem[]>([]);

  const getSimulationData = (params: DashboardFormConfig): void => {
    const data = collectionRef.current.generateSimulationData(
      curDb(),
      { ...params },
      meanAbsoluteError,
    );
    setSimulationData(data);
  };

  const fetchIndicatorList = async () => {
    try {
      setLoading(true);
      const [simScoreRes, meanAbsoluteErrorRes] = await Promise.all([
        getSimulationIndicatorList({
          otype_list: 'SDVAL_FLOW_W,SDVAL_PRESS_W,SDVAL_CR',
          vprop: 'SIM_SCORE',
          time: timelineDate,
        }),
        getSimulationIndicatorList({
          otype_list: 'SDVAL_FLOW_W,SDVAL_PRESS_W,SDVAL_CR',
          vprop: 'ABSOLUTE_MEAN_ERROR',
          time: timelineDate,
        }),
      ]);
      if (
        simScoreRes.status === 'Success' &&
        simScoreRes.indicatorsValues &&
        meanAbsoluteErrorRes.status === 'Success' &&
        meanAbsoluteErrorRes.indicatorsValues
      ) {
        const indicatorsValues = mergeIndicatorsValues(
          curDb(),
          simScoreRes.indicatorsValues,
          meanAbsoluteErrorRes.indicatorsValues,
        );
        collectionRef.current.initialize(
          curDb(),
          indicatorsValues,
          hostApp().appConfig.assessmentSimulation.formConfig,
        );
        const formList = collectionRef.current.generateSearchFormList(curDb());
        const values: { [index: string]: string[] } = {};
        formList.forEach(({ initialValues, field }) => {
          values[field] = initialValues;
        });
        setFormConfig((state) => {
          if (typeof state === 'undefined') {
            return {
              formItemList: formList,
              formValues: values,
              formConfigDate: deviceUpdateDate,
            };
          }
          return {
            ...state,
            formConfigDate: deviceUpdateDate,
          };
        });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const handleLocate = (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => {
    const { otype, oname, shape } = locateObj;
    const mapView = hostApp().getMapView(MAP_VIEW_NAME_ONLINE);
    if (mapView) {
      mapView.selectAndNavigate(otype, oname, shape);
    }
  };

  const handleHover = (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => {
    dispatch(highlightActions.updateHoverObject({ hoverObject: locateObj }));
  };

  const handleSetFormValues = (formValues: DashboardFormConfig | undefined) => {
    setFormConfig((state) => {
      if (state) {
        return {
          ...state,
          formValues,
        };
      }
      return state;
    });
  };

  const handleFormValuesChange = (
    _: string,
    allValues: DashboardFormConfig,
  ) => {
    if (formConfig) {
      handleSetFormValues(allValues);
    }
  };

  useEffect(() => {
    if (typeof deviceUpdateDate === 'undefined') return;
    fetchIndicatorList();
  }, [deviceUpdateDate]);

  useEffect(() => {
    if (open) {
      getSimulationData(formConfig?.formValues ?? {});
    }
  }, [open, formConfig]);

  const getSummaryExtra = () => {
    const activeItem = simulationData.list.find(
      (item) => item.key === activeKey,
    );
    if (activeItem?.enableStandard) {
      return (
        <span
          style={{
            color: activeItem.isReachStandard ? 'green' : 'red',
            fontWeight: '800',
          }}
        >
          {activeItem.isReachStandard ? '满足标准' : '不满足标准'}
          {activeItem.helpDescription ? (
            <HelpIcon title={activeItem.helpDescription} />
          ) : null}
        </span>
      );
    }
    return null;
  };

  const fetchAssessmentScoreList = async () => {
    const vprops = score.map((scoreItem) => scoreItem.vprop);
    const res = await getSimulationScore(vprops, timelineDate);

    const scoreData = getScoreData(
      res.data.values,
      score,
      timelineDateTimeString,
      curDb(),
    );

    setScoreData(scoreData);
  };

  const handleOpenChart = (vprop: string) => {
    dispatch(
      baseActions.propertyChartAction({
        arg: {
          indicatorType: undefined,
          indicatorName: undefined,
          pinnedItem: MODEL_SUMMARY_OBJECT,
          vprop,
        },
      }),
    );
  };

  useEffect(() => {
    if (open) {
      fetchAssessmentScoreList();
    }
  }, [timelineDate, open]);

  return (
    <MiniDashboardWrap open={open}>
      <RunningStateContainerWrapper>
        <PanelTitle level={5}>{currentSceneTitle}</PanelTitle>
        <Spin
          spinning={loading ?? false}
          delay={300}
        >
          <Card>
            <SpecificPrefixH2 style={{ margin: '4px' }}>
              <span>分数</span>
            </SpecificPrefixH2>
            <ScoreList
              handleOpenChart={handleOpenChart}
              dataSource={scoreData}
            />
          </Card>
          <Card
            title={
              <SpecificPrefixH2 style={{ margin: '4px' }}>
                <span>汇总</span>
                <span>{getSummaryExtra()}</span>
              </SpecificPrefixH2>
            }
          >
            <Collapse bordered={false}>
              {formConfig && formConfig.formItemList.length > 0 ? (
                <Collapse.Panel
                  header={<SearchPanelHeader>筛选条件</SearchPanelHeader>}
                  key="search"
                  className="collapse-custom-panel"
                >
                  <Form
                    {...formItemLayout}
                    name="simulation-form"
                    onValuesChange={handleFormValuesChange}
                    initialValues={formConfig.formValues}
                  >
                    {formConfig.formItemList.map(
                      ({ fieldName, field, multiple, options }) => (
                        <Form.Item
                          key={field}
                          label={fieldName}
                          name={field}
                          style={{ marginBottom: '10px' }}
                        >
                          <Select
                            mode={multiple ? 'multiple' : undefined}
                            options={options}
                          />
                        </Form.Item>
                      ),
                    )}
                  </Form>
                </Collapse.Panel>
              ) : null}
            </Collapse>

            <Summary
              activeKey={activeKey}
              data={simulationData.list}
              selectRow={selectRow}
              setSelectRow={(selectRow) => setSelectRow(selectRow)}
              setActiveKey={(key) => setActiveKey(key)}
            />
          </Card>
          <Card
            title={
              <SpecificPrefixH2 style={{ margin: '4px' }}>
                <span>
                  详情
                  {selectRow
                    ? `(${selectRow?.indicatorTitle}:${selectRow?.meanAbsoluteErrorTitle})`
                    : ''}
                </span>
              </SpecificPrefixH2>
            }
          >
            <Details
              data={selectRow}
              onLocate={handleLocate}
              onHover={handleHover}
            />
          </Card>
        </Spin>
      </RunningStateContainerWrapper>
    </MiniDashboardWrap>
  );
};

export default SimulationDashboardContainer;
