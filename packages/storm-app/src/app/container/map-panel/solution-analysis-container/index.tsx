/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MAP_VIEW_NAME_ONLINE,
  MAP_VIEW_NAME_SOLUTION,
} from '@waterdesk/data/const/map';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  AnalysisResultData,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  getAllSelected,
  getAllSelectedByMergeRoadName,
  getAnalysisLegendData,
  getAnalysisResultHighlightDataTheme,
  getAnalysisResultHightData,
  getAnalysisThemes,
  getAnalysisUiData,
  getHoverHighlightData,
  mergeSameRoadNameAndDiameter,
  PlantStationInfo,
  PlantStationType,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import {
  getAdvanceInitialValues,
  getCompareStep,
  getUpdatedAdvanceSetting,
} from '@waterdesk/data/quick-analysis/solution-analysis-data';
import {
  CompareSourceType,
  getHydraulicTimeStepMinutes,
} from '@waterdesk/data/solution';
import {
  SolutionBaseInfo,
  SolutionDetail,
} from '@waterdesk/data/solution-detail';
import { FeatureCode } from '@waterdesk/data/system-feature';
import {
  getDateTimeFromValue,
  getValueFromDateTime,
} from '@waterdesk/data/time-data';
import {
  convertToSpecificDate,
  getTimeRangesOverlap,
} from '@waterdesk/data/utils';
import { AsyncTaskStatus } from '@waterdesk/request/get-async-task';
import { getSolutionComparisonDetail } from '@waterdesk/request/get-solution-comparsion';
import {
  getSolutionInfo,
  getSolutionList,
} from '@waterdesk/request/get-solution-data';
import {
  formatAnalysisData,
  GetAnalysisDataAsyncResponse,
  getSolutionAnalysisResult,
} from '@waterdesk/request/get-valve-analysis-data';
import { useRequest } from 'ahooks';
import {
  Empty,
  Form,
  message,
  Progress,
  Spin,
  TableColumnsType,
  Tabs,
  TabsProps,
} from 'antd';
import dayjs from 'dayjs';
import lodash from 'lodash';
import { Key, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'react-router';
import { useAsyncLocate } from 'src/app/hooks/use-async-locate';
import useFeatureFlags from 'src/app/hooks/use-feature-flags';
import useLocalStorageAsyncTask from 'src/app/hooks/use-local-storage-tasks';
import { curDb, hostApp } from 'src/app/host-app';
import { useSolutionIds } from 'src/app/pages/solution/use-solution-ids';
import AnalysisResult from 'src/components/analyse-result/analysis-result';
import DrawerHeader from 'src/components/common/drawer-header/drawer-header';
import MiniDashboardWrap from 'src/components/mini-dashboard/mini-dashboard-wrap';
import AnalysisResultLegend from 'src/components/quick-analysis/analysis-result-legend';
import SolutionAnalysisForm, {
  CompareTimeSetting,
  SolutionAnalysisFormValues,
} from 'src/components/quick-analysis/solution-analysis-form';
import { baseActions, useBaseSlice } from 'src/store/base';
import { highlightActions, useHighlightSlice } from 'src/store/highlight';
import { useSchedulingSlice } from 'src/store/scheduling';
import { useSelectionSlice } from 'src/store/selection';
import {
  EmptyContainer,
  ProgressContainer,
  SavedComparisonDivider,
  SavedComparisonNote,
  SavedComparisonTitle,
  SavedComparisonWrapper,
  SolutionAnalysisWrapper,
} from './style';

type ActiveTabType = 'result' | 'form';
interface Props {
  open: boolean;
  solutionBaseInfo: SolutionBaseInfo | undefined;
  solutionDetail: SolutionDetail | undefined;
  onClose?: () => void;
}

const defaultParams = {
  current: 1,
  pageSize: 20,
};

const defaultMarks = {
  240: getDateTimeFromValue(240).format('HH:mm'),
  480: getDateTimeFromValue(480).format('HH:mm'),
  720: getDateTimeFromValue(720).format('HH:mm'),
  960: getDateTimeFromValue(960).format('HH:mm'),
  1200: getDateTimeFromValue(1200).format('HH:mm'),
};

interface SaveFormValues {
  title: string;
  note?: string;
}

export default function SolutionAnalysisContainer(props: Props) {
  useBaseSlice();
  useSelectionSlice();
  useHighlightSlice();
  useSchedulingSlice();

  const [messageApi, contextHolder] = message.useMessage();
  const { open, solutionBaseInfo, solutionDetail, onClose } = props;
  const dispatch = useDispatch();
  const { state: hasSendSMS } = useFeatureFlags(FeatureCode.QUICK_SOLUTION_SMS);

  const { solutionId, compareIds, historyDate } = useSolutionIds();

  const [activeTab, setActiveTab] = useState<ActiveTabType>('form');
  const [analysisResult, setAnalysisResult] = useState<
    AnalysisResultData | undefined
  >(undefined);
  const [dimensionVisible, setDimensionVisible] = useState<boolean>(false);
  const [highlightNode, setHighlightNode] = useState<boolean>(false);
  const [activeThemes, setActiveThemes] = useState<{
    [key in AnalysisUiConfigType]?: string;
  }>({});
  const [analysisThemesConfig, setAnalysisThemesConfig] = useState<{
    [key in AnalysisUiConfigType]?: LegendGroupData[];
  }>({});
  const [tableSelectedKeys, setTableSelectedKeys] = useState<{
    [key in AnalysisUiConfigType]?: React.Key[];
  }>({});
  const [hoverObject, setHoverObject] = useState<
    undefined | AnalysisUiDataItem
  >(undefined);
  const [compareSolutionInfo, setCompareSolutionInfo] = useState<
    SolutionBaseInfo | undefined
  >(undefined);
  const [compareSolutionDetail, setCompareSolutionDetail] = useState<
    SolutionDetail | undefined
  >(undefined);
  const [savedComparisonId, setSavedComparisonId] = useState<
    string | undefined
  >();
  const [savedComparisonTitle, setSavedComparisonTitle] = useState<string>('');
  const [savedComparisonNote, setSavedComparisonNote] = useState<string>('');
  const [loadingComparison, setLoadingComparison] = useState(false);

  const [form] = Form.useForm<SolutionAnalysisFormValues>();
  const [saveForm] = Form.useForm<SaveFormValues>();

  const compareIdByForm = Form.useWatch('compareId', form);
  const compareSource = Form.useWatch('compareSource', form);

  const [searchParams] = useSearchParams();
  const comparisonId = searchParams.get('comparisonId');

  const { solutionAnalysisConfig: analysisConfig } = hostApp().appConfig;

  const { locate: handleLocate } = useAsyncLocate();
  const {
    progress,
    taskId,
    add,
    message: taskProcessMag,
  } = useLocalStorageAsyncTask('solutionAnalysisTask', {
    enable: open,
    progressStep: hostApp().appConfig.progressStep,
    progressFixed: true,
    formatValues: (values) =>
      formatAnalysisData(values, analysisConfig!, curDb()),
    callback: (res: GetAnalysisDataAsyncResponse) => {
      const { values, taskStatus } = res;
      if (values) {
        const newPlantStation: AnalysisResultData['plantStation'] = {
          plants: [],
          pumpStations: [],
        };
        Object.entries(values.plantStation).forEach((item) => {
          const [key, data] = item;
          newPlantStation[key as PlantStationType] = data.map((m) => {
            const { newValue, oldValue } = m;
            return {
              ...m,
              newValue: oldValue,
              oldValue: newValue,
            };
          });
        });
        values.plantStation = newPlantStation;
      }
      if (taskStatus === AsyncTaskStatus.SUCCESS) {
        setAnalysisResult(values);
        setActiveTab('result');
      }
    },
  });
  const { data, run } = useRequest(getSolutionList, {
    debounceWait: 300,
  });

  const solutionOptions = useMemo(() => {
    const defaultOptions = [];
    if (solutionBaseInfo) {
      defaultOptions.push({
        value: solutionId,
        label: <span>{solutionBaseInfo.name}</span>,
      });
    }
    if (compareSolutionInfo && compareIdByForm) {
      defaultOptions.push({
        value: compareIdByForm,
        label: <span>{compareSolutionInfo.name}</span>,
      });
    }
    const dataOptions =
      data?.list.map(({ id, name, solutionGuid }) => ({
        label: <span>{name}</span>,
        value: id,
        solutionGuid,
      })) ?? [];
    return lodash.uniqBy([...defaultOptions, ...dataOptions], 'value');
  }, [
    data,
    solutionBaseInfo,
    solutionId,
    compareIdByForm,
    compareSolutionInfo,
  ]);

  const advanceInitialValues = useMemo(
    () => getAdvanceInitialValues(analysisConfig?.advanceSetting),
    [analysisConfig?.advanceSetting],
  );

  const initialValues: Partial<SolutionAnalysisFormValues> | undefined =
    useMemo(
      () =>
        open
          ? {
              ...advanceInitialValues,
              originId: solutionId,
              compareId: compareIdByForm,
              historyDate,
              compareTime:
                getValueFromDateTime(
                  dayjs(solutionDetail?.calculateStartTime),
                ) || 0,
              compareSource: historyDate
                ? CompareSourceType.HISTORY
                : CompareSourceType.SOLUTION,
            }
          : undefined,
      [
        solutionId,
        historyDate,
        compareIdByForm,
        solutionDetail,
        advanceInitialValues,
        open,
      ],
    );

  const handleOnSearch = (value: string) => {
    if (value) {
      run({
        ...defaultParams,
        solutionName: value,
      });
    }
  };

  const handleHover = (hoverObject: AnalysisUiDataItem | undefined) => {
    setHoverObject(hoverObject);
  };

  const clearHighlightAnalysisResult = () => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: [
          'affectedArea',
          'affectedAreaUser',
          'affectedPipeline',
          'closedPipeline',
          'valveList',
          'waterOutageArea',
          'waterOutageAreaUser',
          'reversePipeline',
          'analysisResultHover',
          'closeNode',
          'affectedNode',
        ],
      }),
    );
  };

  // 分析结果高亮
  const handleHighlightAnalysisResult = (
    analysisResult: AnalysisResultData,
    theme: {
      [key in AnalysisUiConfigType]?: string;
    },
    dimension: boolean,
    highlightNode: boolean,
  ) => {
    if (typeof analysisConfig === 'undefined') {
      messageApi.warning('主题配置加载失败');
    } else {
      const analysisResultHighlightDataTheme: {
        [key in AnalysisUiConfigType]?: LegendGroupData;
      } = getAnalysisResultHighlightDataTheme(theme, analysisThemesConfig);

      const analysisResultHighlightData = getAnalysisResultHightData(
        curDb(),
        analysisResult,
        analysisConfig,
        analysisResultHighlightDataTheme,
        dimension,
        highlightNode,
      );
      dispatch(
        highlightActions.updateHighlight({
          highlight: {
            ...analysisResultHighlightData,
          },
          highlightMapName: solutionBaseInfo?.name ?? MAP_VIEW_NAME_SOLUTION,
        }),
      );
    }
  };

  const hideFeatureTooltipImportant = () => {
    dispatch(
      baseActions.updateHideFeatureTooltipImportant({
        visible: false,
      }),
    );
    hostApp().redrawMapView();
  };

  const resetHideFeatureTooltipImportant = () => {
    dispatch(baseActions.updateHideFeatureTooltipImportant({}));
    hostApp().redrawMapView();
  };

  const loadComparisonData = async (id: string) => {
    if (!id) return;
    try {
      setLoadingComparison(true);
      const response = await getSolutionComparisonDetail(id);
      if (response.status === 'Success' && response.comparisonResult) {
        // 设置保存的对比结果
        setSavedComparisonId(id);
        setAnalysisResult(JSON.parse(response.comparisonResult));
        setActiveTab('result');
        setSavedComparisonTitle(response.comparisonTitle || '');
        setSavedComparisonNote(response.comparisonNote || '');

        // 设置表单值
        if (response.comparisonCondition) {
          const condition = JSON.parse(response.comparisonCondition);
          const formValues = {
            compareSource: response.comparisonType,
            compareTime: condition.compareTime,
            historyDate: condition.historyDate,
            ...condition.advanceSetting,
          };
          form.setFieldsValue(formValues);
        }

        // 设置保存表单的值
        saveForm.setFieldsValue({
          title: response.comparisonTitle,
          note: response.comparisonNote,
        });
      } else {
        messageApi.error(response.errorMessage || '加载对比结果失败');
      }
    } catch (error) {
      messageApi.error('加载对比结果失败');
      console.error('加载对比结果失败:', error);
    } finally {
      setLoadingComparison(false);
    }
  };

  const handleReset = () => {
    setActiveTab('form');
    setAnalysisResult(undefined);
    setSavedComparisonId(undefined);
    clearHighlightAnalysisResult();
    dispatch(highlightActions.resetHighlight());
    resetHideFeatureTooltipImportant();
  };

  const handleSetActiveThemes = (panelKey: string, value?: string) => {
    setActiveThemes((state) => ({
      ...state,
      [panelKey]: value,
    }));
  };

  const handleSetTableSelectedKeys = (
    keys: Key[],
    tableKey: AnalysisUiConfigType,
  ) => {
    setTableSelectedKeys((state) => ({
      ...state,
      [tableKey]: keys,
    }));
  };

  const handleHighlightHover = (
    hoverObject: AnalysisUiDataItem | undefined,
  ) => {
    const hoverObjectHighlightData = {
      analysisResultHover: getHoverHighlightData(
        typeof hoverObject === 'undefined' ? [] : [hoverObject],
        curDb(),
      ),
    };
    dispatch(
      highlightActions.updateHighlight({
        highlight: {
          ...hoverObjectHighlightData,
        },
        highlightMapName: solutionBaseInfo?.name ?? MAP_VIEW_NAME_SOLUTION,
      }),
    );
  };

  const getCompareDateTime = (
    compareSource: CompareSourceType,
    historyDate: string | undefined,
    compareTime: number,
    compareSolutionInfo: SolutionBaseInfo | undefined,
  ): string => {
    let compareDateTime: string = '';
    let compareDate: string = '';
    if (compareSource === CompareSourceType.HISTORY) {
      compareDate = historyDate ?? '';
    } else {
      compareDate = compareSolutionInfo?.startTime ?? '';
    }
    compareDateTime = dayjs(compareDate, 'YYYY-MM-DD 00:00:00')
      .add(compareTime, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    return compareDateTime;
  };

  const fetchAnalysisResult = async (values: SolutionAnalysisFormValues) => {
    const {
      originId,
      compareId,
      compareSource,
      compareTime,
      historyDate,
      ...advanceSetting
    } = values;
    const solutionTime = dayjs(
      solutionBaseInfo?.startTime,
      "'YYYY-MM-DD 00:00:00'",
    )
      .add(compareTime, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    const compareDateTime: string = getCompareDateTime(
      compareSource,
      historyDate,
      compareTime,
      compareSolutionInfo,
    );
    const viewId =
      hostApp()
        .getMapView(solutionBaseInfo?.name ?? MAP_VIEW_NAME_SOLUTION)
        ?.getViewId() ?? '';
    const res = await getSolutionAnalysisResult(
      viewId,
      originId,
      compareSource === CompareSourceType.HISTORY
        ? MAP_VIEW_NAME_ONLINE
        : compareId,
      solutionTime,
      compareDateTime,
      {
        ...advanceInitialValues,
        ...advanceSetting,
      },
    );
    if (res.status === 'Success' && res.taskId) {
      add(res.taskId);
    }
  };

  const handleAnalysis = () => {
    hideFeatureTooltipImportant();
    const values = form.getFieldsValue();
    const {
      originId,
      compareId,
      compareSource,
      compareTime,
      historyDate,
      ...advanceSetting
    } = values;
    const updatedAdvanceSetting = getUpdatedAdvanceSetting(
      analysisConfig!.advanceSetting,
      advanceSetting,
    );
    const analysisThemes = getAnalysisThemes(
      analysisConfig!,
      curDb(),
      updatedAdvanceSetting,
    );
    // reset analysisThemesConfig when advanceSetting updated
    setAnalysisThemesConfig(analysisThemes);
    setSavedComparisonId(undefined);
    fetchAnalysisResult({
      originId,
      compareId,
      compareSource,
      compareTime,
      historyDate,
      ...advanceSetting,
    });
  };

  const fetchSolutionInfo = async (solutionId: string) => {
    const res = await getSolutionInfo(solutionId);
    if (res.status === 'Success') {
      setCompareSolutionInfo(res.baseInfo);
      setCompareSolutionDetail(res.detail);
    }
  };

  const plantsStationColumns: TableColumnsType<PlantStationInfo> = [
    {
      title: '',
      key: 'ptitle',
      dataIndex: 'ptitle',
      width: 30,
      onCell: (record) => ({
        rowSpan: record.rowSpan,
      }),
    },
    {
      title: '名称',
      key: 'title',
      dataIndex: 'title',
      width: 80,
    },
    {
      title: '当前方案',
      key: 'newValue',
      dataIndex: 'newValue',
      width: 60,
    },
    {
      title: '基准方案',
      key: 'oldValue',
      dataIndex: 'oldValue',
      width: 60,
    },
    {
      title: '变化',
      key: 'diffValue',
      dataIndex: 'diffValue',
      width: 50,
    },
  ];

  const compareTimeSetting = useMemo(() => {
    const setting: CompareTimeSetting = {
      step: 1,
      min: 0,
      max: 1440,
      marks: defaultMarks,
      status: true,
    };

    if (solutionBaseInfo && solutionDetail) {
      const {
        calculateStartTime: solutionCalculateStartTime,
        calculateEndTime: solutionCalculateEndTime,
      } = solutionDetail;
      const solutionStep = getHydraulicTimeStepMinutes(
        solutionDetail.options.HydraulicTimestep,
      );
      // 如果有对比方案（历史方案不考虑）
      if (compareSolutionDetail) {
        const {
          calculateStartTime: compareCalculateStartTime,
          calculateEndTime: compareCalculateEndTime,
        } = compareSolutionDetail ?? {};
        // 比较对比方案和原方案步长, 获取最大步长作为对比步长
        setting.step = getCompareStep(
          getHydraulicTimeStepMinutes(
            compareSolutionDetail.options.HydraulicTimestep,
          ),
          solutionStep,
        );

        // 判断原方案计算时间和对比方案计算时间是否存在重叠 - 仅仅对比时间，不对比日期,方案时间不跨天
        const overlapTime = getTimeRangesOverlap(
          [
            convertToSpecificDate(solutionCalculateStartTime, dayjs()),
            convertToSpecificDate(solutionCalculateEndTime, dayjs()),
          ],
          [
            convertToSpecificDate(compareCalculateStartTime, dayjs()),
            convertToSpecificDate(compareCalculateEndTime, dayjs()),
          ],
        );

        if (overlapTime) {
          const [start, end] = overlapTime;
          const startDateTime = dayjs(start);
          const endDateTime = dayjs(end);
          // 如果是时间范围，则使用重叠时间作为开始、结束时间
          if (!startDateTime.isSame(endDateTime)) {
            setting.min = getValueFromDateTime(startDateTime);
            setting.max = getValueFromDateTime(endDateTime);
            setting.marks = {
              [setting.min]: getDateTimeFromValue(setting.min).format('HH:mm'),
              [setting.max]: getDateTimeFromValue(setting.max).format('HH:mm'),
            };
          } else {
            // 如果重叠时间是单时刻，则用0~24小时, 单时刻作为唯一marks
            setting.step = null;
            const mark = getValueFromDateTime(startDateTime);
            setting.marks = {
              [mark]: startDateTime.format('HH:mm'),
            };
          }
          // 如果不存在重叠时间， 则标记为不能对比
        } else {
          setting.status = false;
        }
      } else {
        // 如果不存在对比方案，以原始方案步长作为对比步长
        setting.step = solutionStep;
      }
    }
    return setting;
  }, [
    solutionBaseInfo,
    solutionBaseInfo,
    compareSolutionInfo,
    compareSolutionDetail,
  ]);

  const items: TabsProps['items'] = [
    {
      key: 'form',
      label: '分析条件',
      children: (
        <SolutionAnalysisForm
          form={form}
          initialValues={initialValues}
          onSearchSolution={handleOnSearch}
          solutionOptions={solutionOptions}
          compareTimeSetting={compareTimeSetting}
          onFinish={handleAnalysis}
          // onValuesChange={handleFormValuesChange}
        />
      ),
    },
    {
      key: 'result',
      label: '分析结果',
      children: analysisResult ? (
        <AnalysisResult
          canSendSMS={hasSendSMS}
          analysisConfig={analysisConfig}
          analysisData={analysisResult}
          activeThemes={activeThemes}
          analysisThemesConfig={analysisThemesConfig}
          tableSelectedKeys={tableSelectedKeys}
          setTableSelectedKeys={handleSetTableSelectedKeys}
          handleActiveOnChange={handleSetActiveThemes}
          handleLocate={handleLocate}
          handleHover={handleHover}
          db={curDb()}
          showPlantStation
          plantStationColumn={plantsStationColumns}
          dimension={dimensionVisible}
          highlightNodeFunc={analysisConfig?.highlightNodeFunc ?? false}
          highlightNode={highlightNode}
          setHighlightNode={(visible) => setHighlightNode(visible)}
          handleSwitchDimensionVisible={(visible) =>
            setDimensionVisible(visible)
          }
        />
      ) : (
        <Empty />
      ),
    },
  ];

  useEffect(() => {
    if (open && comparisonId) {
      loadComparisonData(comparisonId);
    }
  }, [open, comparisonId]);

  useEffect(() => {
    if (compareIdByForm || compareIds?.length) {
      fetchSolutionInfo(compareIdByForm ?? compareIds?.[0]);
    } else {
      setCompareSolutionInfo(undefined);
    }
  }, [compareIdByForm, compareIds]);

  useEffect(() => {
    handleHighlightHover(hoverObject);
  }, [hoverObject]);

  useEffect(() => {
    if (compareSource === CompareSourceType.HISTORY) {
      form.setFieldValue('historyDate', solutionBaseInfo?.startTime);
    }
  }, [compareSource, solutionBaseInfo]);

  useEffect(() => {
    if (analysisResult) {
      const selectedKeys: typeof tableSelectedKeys = {};
      const analysisUiData = getAnalysisUiData(analysisResult);
      Object.entries(analysisUiData).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            selectedKeys[key as AnalysisUiConfigType] = value.map(
              (item) => item.oname,
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            selectedKeys[key as AnalysisUiConfigType] =
              mergeSameRoadNameAndDiameter(value).map((item) => item.oname);
            break;
          default:
            break;
        }
      });

      setTableSelectedKeys(selectedKeys);
    }
  }, [analysisResult]);

  useEffect(() => {
    if (analysisResult) {
      const customResult: AnalysisResultData = { ...analysisResult };
      const analysisUiData = getAnalysisUiData(customResult);
      Object.entries(analysisUiData).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            customResult[key as AnalysisUiConfigType] = getAllSelected(
              (tableSelectedKeys[key as AnalysisUiConfigType] ??
                []) as string[],
              value,
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            customResult[key as AnalysisUiConfigType] =
              getAllSelectedByMergeRoadName(
                (tableSelectedKeys[key as AnalysisUiConfigType] ??
                  []) as string[],
                value,
              );
            break;
          default:
            break;
        }
      });
      handleHighlightAnalysisResult(
        customResult,
        activeThemes,
        dimensionVisible,
        highlightNode,
      );
    }
  }, [
    analysisResult,
    dimensionVisible,
    activeThemes,
    tableSelectedKeys,
    highlightNode,
    analysisThemesConfig,
  ]);

  useEffect(() => {
    if (analysisConfig && open) {
      const analysisThemes = getAnalysisThemes(analysisConfig, curDb());
      setAnalysisThemesConfig(analysisThemes);
      Object.entries(analysisThemes).forEach((item) => {
        const [panelKey, config] = item;
        if (config) {
          handleSetActiveThemes(panelKey, config[0].name);
        }
      });
    }
  }, [analysisConfig, open]);

  useEffect(() => {
    if (open) {
      run({ ...defaultParams });
      setHighlightNode(analysisConfig?.defaultHighlightNode ?? false);
    }
  }, [analysisConfig?.defaultHighlightNode, open]);

  useEffect(
    () => () => {
      handleReset();
    },
    [],
  );

  return (
    <MiniDashboardWrap open={open}>
      <SolutionAnalysisWrapper>
        <Spin
          spinning={!!taskId || loadingComparison}
          delay={500}
          tip={
            <>
              <span>
                {loadingComparison
                  ? '加载对比结果'
                  : (taskProcessMag ?? '开始分析')}
                :
              </span>
              <ProgressContainer>
                <Progress percent={progress} />
              </ProgressContainer>
            </>
          }
        >
          <DrawerHeader
            onClose={onClose}
            title="快速对比"
          />
          {savedComparisonId && (
            <SavedComparisonWrapper>
              <SavedComparisonTitle>
                {savedComparisonTitle}
              </SavedComparisonTitle>
              {savedComparisonNote && (
                <SavedComparisonNote>{savedComparisonNote}</SavedComparisonNote>
              )}
              <SavedComparisonDivider />
            </SavedComparisonWrapper>
          )}
          {compareIds?.length && compareIds.length > 1 ? (
            <EmptyContainer>
              <Empty description="快速分析只支持单方案对比分析" />
            </EmptyContainer>
          ) : (
            <Tabs
              style={{ padding: '10px' }}
              activeKey={activeTab}
              onTabClick={(activeKey) =>
                setActiveTab(activeKey as ActiveTabType)
              }
              items={items}
            />
          )}
        </Spin>
        {analysisConfig && activeTab === 'result' ? (
          <AnalysisResultLegend
            showSwitchNode={analysisConfig?.highlightNodeFunc ?? false}
            data={getAnalysisLegendData(
              activeThemes,
              analysisThemesConfig,
              analysisConfig,
            )}
            dimension={dimensionVisible}
            node={highlightNode}
            handleSwitchNodeVisible={(visible) => setHighlightNode(visible)}
            handleSwitchDimensionVisible={(visible) =>
              setDimensionVisible(visible)
            }
          />
        ) : null}
        {contextHolder}
      </SolutionAnalysisWrapper>
    </MiniDashboardWrap>
  );
}
