/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const SolutionAnalysisWrapper = styled.div``;

export const SavedComparisonWrapper = styled.div`
  margin: 5px 10px -20px;
`;

export const SavedComparisonTitle = styled.div`
  margin-bottom: 4px;
  font-weight: bold;
  font-size: 16px;
`;

export const SavedComparisonNote = styled.div`
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const SavedComparisonDivider = styled.div`
  height: 2px;
  background: #f0f0f0;
  margin: 8px 0;
`;

export const WarningContainer = styled.div<{
  colorWarning: string;
  colorWarningBg: string;
}>`
  color: ${(props) => props.colorWarning};
  text-align: center;
  margin: 16px 0;
  padding: 8px;
  background-color: ${(props) => props.colorWarningBg};
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const WarningIcon = styled.span`
  margin-right: 8px;
`;

export const ButtonContainer = styled.div`
  margin: 20px 0;
  text-align: center;
`;

export const ButtonWithMargin = styled.div`
  margin-right: 16px;
  display: inline-block;
`;

export const ProgressContainer = styled.div`
  width: 50%;
`;

export const EmptyContainer = styled.div`
  margin-top: 50%;
`;

export const TabsContainer = styled.div`
  padding: 10px;
`;
