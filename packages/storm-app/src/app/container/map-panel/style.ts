/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Row, Tabs } from 'antd';
import Title from 'antd/es/typography/Title';
import { BeforePrefix, SpanClose } from 'src/styles/common-style';
import styled from 'styled-components';

export const PanelTitle = styled(Title)`
  color: #0000004d;
  padding: 10px 10px 0 10px;
`;

export const RowWrapper = styled(Row)<{ open: boolean; width?: string }>`
  width: 100%;
  height: 0;
  position: absolute;
  z-index: 1;
  .left-drawer-wrapper {
    width: ${({ open, width = '349px' }) => (open ? width : '0px')};
    height: calc(100vh - 46px);
    background-color: ${(props) => props.theme.colorBgBase};
    border-right: 1px solid ${(props) => props.theme.colorBorder};
    position: relative;
    visibility: ${(props) => (props.open ? 'visible' : 'hidden')};
    left: ${(props, width = '349px') => (props.open ? 0 : `-${width}`)};
    transition: all 0.3s ease-in-out;

    .closeLeftDrawer {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 1;
    }
  }
`;

export const ToolWrapper = styled.div`
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  transition: all 0.3s ease-in-out;
`;

export const RightDrawerContainerContent = styled.div<{
  active: boolean;
}>`
  display: ${({ active }) => (active ? 'block' : 'none')};
`;

export const CustomTabBarWrapper = styled.div`
  position: absolute;
  top: -40px;
  left: 0;
  .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list .ant-tabs-tab {
    background-color: ${({ theme }) => theme.colorBgContainer};
  }
  .ant-tabs-nav > .ant-tabs-nav-operations {
    display: none !important;
  }
`;

export const LeftTabBarExtraContent = styled.div`
  display: flex;
  flex-direction: wrap;
  padding: 8px 16px;
  margin-right: 2px;
  background-color: ${({ theme }) => theme.colorBgContainer};
  border: 1px solid ${({ theme }) => theme.colorBorderSecondary};
  border-bottom-color: ${({ theme }) => theme.colorBgContainer};
  border-radius: 4px 4px 0 0;
`;

export const SpanCollapse = styled(SpanClose)`
  transform: rotate(90deg);
`;

export const UserManagerContainerWrapper = styled.div`
  margin: 20px 20px 0;
`;

export const RunningStateContainerWrapper = styled.div`
  height: 100%;
  background-color: ${({ theme }) => theme.colorBgContainer};

  ${PanelTitle} {
    color: #0000004d;
  }

  .adm-card {
    margin: 8px;
  }
  .water-supply-summary-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
    justify-content: stretch;
    .statistic-subfix {
      color: ${({ theme }) => theme.colorTextQuaternary};
    }
    .total-supply-card-title {
      display: flex;
      justify-content: space-between;
    }
  }
  .score-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .title {
    position: relative;
    margin: 0;
    font-size: 14px;
    line-height: 14px;
    padding: 3px 0 3px 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &::before {
      ${BeforePrefix}
    }
  }

  .message-list {
    overflow-y: scroll;
    height: 300px;
  }
`;

export const NoticePopupContainer = styled.div`
  font-size: 14px;
  overflow-y: auto;
  .notice-title {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    margin: 18px 14px 14px 18px;
    color: ${({ theme }) => theme.colorTextLabel};
    font-size: 1.7em;
  }
`;

export const TabsContainer = styled(Tabs)`
  margin: 0 18px;
  .ant-tabs-tab {
    padding: 0;
  }
`;

export const NetworkStateChartWrapper = styled.div`
  .network-state-content {
    .network-state-content-header {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
    }
  }
`;

export const LastSimulationInfoWrapper = styled.div`
  .err-background {
    background-color: ${({ theme }) => theme.colorErrorBg};
  }
`;

export const BatchQueryContainerWrapper = styled.div`
  height: calc(100vh - 46px);
  overflow: auto;
`;
