/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  ObjectFormItem,
  ResultData,
  TRACK_DOWN,
  TRACK_UP,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import {
  GetTrackDownDataResponse,
  getDownTrack,
} from '@waterdesk/request/track/down-data';
import { getRecommendByTrack } from '@waterdesk/request/track/get-track-data';
import {
  GetUpTrackDataResponse,
  getUpTrack,
} from '@waterdesk/request/track/up-data';
import { Col, Row } from 'antd';
import { NavBar, Popup, Switch } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import LegendItem from 'src/components/legend/legend-item';
import { LegendItemTitle } from 'src/components/legend/style';
import { highlightActions, useHighlightSlice } from 'src/store/highlight';
import { selectHighlightThemeConfig } from 'src/store/highlight/selectors';
import { leftWrapperActions } from 'src/store/left-wrapper';
import { selectLeftContainerTypeWrapper } from 'src/store/left-wrapper/selector';
import { ContainerType } from 'src/store/left-wrapper/types';
import { selectSelectionItem } from 'src/store/selection/selector';
import TrackResult from '../../../components/track/track-result';
import TrackStream from '../../../components/track/track-stream';

export default function useTrackContainer() {
  useHighlightSlice();

  const highlightThemeConfig = useSelector(selectHighlightThemeConfig);
  const selectedItems = useSelector(selectSelectionItem);
  const dispatch = useDispatch();
  const leftContainerType = useSelector(selectLeftContainerTypeWrapper);
  const { token } = useToken();

  const [resultData, setResultData] = useState<
    { type: ContainerType; result: ResultData[] } | undefined
  >();

  const [resultVisible, setResultVisible] = useState<boolean>(false);
  const [dimensionVisible, setDimensionVisible] = useState<boolean>(false);

  const [tempLegendData, setTempLegendData] = useState<
    (LegendGroupData & { key: string })[]
  >([]);
  const onClose = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: undefined,
      }),
    );
  };

  const getCurrentItem = (): IObjectItem | undefined => {
    if (selectedItems.length > 0) {
      const selectionCollection =
        hostApp().getMainMapView()?.selectionCollection;
      return selectionCollection?.firstSelectedObject;
    }
    return undefined;
  };
  const getUpTrackData = async (
    oname: string,
    otype: string,
    minDiameter: number,
    minFlow: number,
  ): Promise<void> => {
    const trackRequests: Promise<GetUpTrackDataResponse>[] = [];
    const mapViews = hostApp().getMapViews();
    mapViews?.forEach((mapView) => {
      if (mapView) {
        const time =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE
            ? dayjs(mapView.dateTime).format('YYYY-MM-DD HH:mm:ss')
            : undefined;
        trackRequests.push(
          getUpTrack(
            oname,
            otype,
            minDiameter,
            minFlow,
            curDb(),
            time,
            mapView,
          ),
        );
      }
    });

    Promise.all(trackRequests).then((res) => {
      // hide tooltip
      const trackResults: ResultData[] = [];
      res.forEach((trackResult) => {
        trackResults.push({
          upDmaDatas: trackResult.upDmaDatas,
          upLinkDatasMap: trackResult.upLinkDatasMap,
          upLinkDatas: trackResult.upLinkDatas,
          upScadaDatas: trackResult.upScadaDatas,
          defaultHighlightDatas: trackResult.defaultHighlightDatas,
        });
      });
      setResultData({
        result: trackResults,
        type: TRACK_UP,
      });
      setResultVisible(true);
      onClose();
    });
  };

  const getDownTrackData = async (
    oname: string,
    otype: string,
    minDiameter: number,
    minFlow: number,
  ): Promise<void> => {
    const trackRequests: Promise<GetTrackDownDataResponse>[] = [];
    const mapViews = hostApp().getMapViews();
    mapViews?.forEach((mapView) => {
      if (mapView) {
        const time =
          mapView.mapViewName !== MAP_VIEW_NAME_ONLINE
            ? dayjs(mapView.dateTime).format('YYYY-MM-DD HH:mm:ss')
            : undefined;
        trackRequests.push(
          getDownTrack(
            oname,
            otype,
            minDiameter,
            minFlow,
            curDb(),
            time,
            mapView,
          ),
        );
      }
    });

    Promise.all(trackRequests).then((res) => {
      // hide tooltip
      const trackResults: ResultData[] = [];
      res.forEach((trackResult) => {
        trackResults.push({
          downDmaDatas: trackResult.downDmaDatas,
          downLinkDatasMap: trackResult.downLinkDatasMap,
          downLinkDatas: trackResult.downLinkDatas,
          downScadaDatas: trackResult.downScadaDatas,
          defaultHighlightDatas: trackResult.defaultHighlightDatas,
          downWaterMeterDatas: trackResult.downWaterMeterDatas,
        });
      });
      setResultData({ result: trackResults, type: TRACK_DOWN });
      setResultVisible(true);
      onClose();
    });
  };

  const fetchRecommendValues = async (
    otype: string,
    oname: string,
    trackType: TrackType,
  ) => {
    const res = await getRecommendByTrack(otype, oname, trackType);
    return {
      flow: res.flow,
      diameter: res.diameter,
    };
  };

  const getTrackTitle = (type: ContainerType): string => {
    switch (type) {
      case TRACK_UP:
        return '来源追踪';
      default:
        return '去向追踪';
    }
  };

  const positionToMapView = (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => {
    if (highlightObject.shape)
      hostApp()
        .getMainMapView()
        ?.selectAndNavigate(
          highlightObject.otype,
          highlightObject.oname,
          highlightObject.shape,
        );
  };

  const handleHover = (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => {
    dispatch(
      highlightActions.updateHoverObject({ hoverObject: highlightObject }),
    );
  };

  const highlight = (
    highlightObject: {
      waterMeter?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
    },
    themeConfig?: {
      waterMeter?: LegendGroupData;
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => {
    if (themeConfig) {
      dispatch(
        highlightActions.updateThemeConfig({
          themeConfig,
        }),
      );
    }

    dispatch(
      highlightActions.updateHighlight({
        highlight: highlightObject,
      }),
    );
  };

  let trackConfig: TrackConfig | undefined;
  const getTrackConfig = (type: TrackType) => {
    if (!trackConfig) {
      trackConfig = new TrackConfig(
        curDb(),
        hostApp().appConfig.trackColumnsConfig,
        type,
      );
    }
    return trackConfig;
  };

  const getDmaSwitchData = (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => {
    const theme = themeMap.get(collapse);
    const themeConfig = theme?.find((item) => item.name === value);
    if (themeConfig) {
      const newHighlightData:
        | TrackDownDma[]
        | TrackDownLink[]
        | HighlightObject[] = [];
      dataSource.forEach((item: any) => {
        newHighlightData.push({
          ...item,
          highlightType: 'custom',
          highlightShowMark: dimensionVisible,
          highlightTextBgColor: '#ffffff',
        });
      });
      return {
        highlightData: newHighlightData,
        highlightTheme: themeConfig,
      };
    }
    return undefined;
  };

  const getLinkSwitchData = (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) => {
    const theme = themeMap.get(collapse);
    const themeConfig = theme?.find((item) => item.name === value);
    if (themeConfig) {
      const newHighlightData:
        | TrackDownDma[]
        | TrackDownLink[]
        | HighlightObject[] = [];
      dataSource.forEach((item) => {
        item.highlightObjects.forEach((object: any) => {
          const newObject: TrackDownLink = {
            ...object,
            highlightType: 'custom',
            highlightShowMark: dimensionVisible,
          };
          newHighlightData.push(newObject);
        });
      });
      return {
        highlightData: newHighlightData,
        highlightTheme: themeConfig,
      };
    }
    return undefined;
  };

  const clearHighlight = () => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: undefined,
      }),
    );
    dispatch(
      highlightActions.clearThemeConfig({
        highlightLayerName: undefined,
      }),
    );
    dispatch(highlightActions.resetHighlight());
  };

  useEffect(() => {
    const tempLegendDatas: (LegendGroupData & { key: string })[] = [];

    if (highlightThemeConfig) {
      Object.keys(highlightThemeConfig)?.forEach((item) => {
        const legendData = highlightThemeConfig[item];
        if (legendData) {
          tempLegendDatas.push({ ...legendData, key: item });
        }
      });
    }

    setTempLegendData(tempLegendDatas);
  }, [highlightThemeConfig]);

  return {
    trackForm: (
      <Popup
        position="right"
        visible={
          leftContainerType === TRACK_UP || leftContainerType === TRACK_DOWN
        }
      >
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
          }}
        >
          <NavBar
            onBack={onClose}
            style={{ backgroundColor: token.colorBgContainer }}
          >
            {getTrackTitle(leftContainerType ?? TRACK_UP)}
          </NavBar>
          {leftContainerType === TRACK_UP ? (
            <TrackStream
              selectObjects={getCurrentItem()}
              getStreamTrack={getUpTrackData}
              getRecommendValues={fetchRecommendValues}
            />
          ) : null}
          {leftContainerType === TRACK_DOWN ? (
            <TrackStream
              selectObjects={getCurrentItem()}
              getStreamTrack={getDownTrackData}
              getRecommendValues={fetchRecommendValues}
            />
          ) : null}
        </div>
      </Popup>
    ),
    resultPanel: resultVisible ? (
      <div
        style={{
          width: '100vw',
          paddingBottom: '8px',
        }}
      >
        <NavBar
          onBack={() => {
            setResultVisible(false);
            clearHighlight();
          }}
          style={{ backgroundColor: token.colorBgContainer }}
        >
          {getTrackTitle(resultData ? resultData.type : TRACK_UP)}
        </NavBar>
        {resultData ? (
          <>
            <TrackResult
              dimension={dimensionVisible}
              type={resultData.type as TrackType}
              resultData={resultData?.result[0]}
              positionToMapView={positionToMapView}
              handleHover={handleHover}
              highlight={highlight}
              trackResultConfig={getTrackConfig}
              getDmaSwitchData={getDmaSwitchData}
              getLinkSwitchData={getLinkSwitchData}
            />
            {tempLegendData && tempLegendData.length > 0 ? (
              <>
                <LegendItemTitle style={{ marginBottom: 0 }}>
                  <Row>
                    <Col span={12}>显示标注</Col>
                    <Col span={12}>
                      <Switch
                        checked={dimensionVisible}
                        onChange={(checked) => setDimensionVisible(checked)}
                      />
                    </Col>
                  </Row>
                </LegendItemTitle>
                {tempLegendData.map((legendDataItem) => (
                  <Row key={legendDataItem.key}>
                    <LegendItem groupData={legendDataItem} />
                  </Row>
                ))}
              </>
            ) : null}
          </>
        ) : null}
      </div>
    ) : null,
  };
}
