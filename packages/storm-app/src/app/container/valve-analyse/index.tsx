/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PlusCircleOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { IObjectItem, makeObjectId } from '@waterdesk/data/object-item';
import {
  AnalysisObject,
  AnalysisObjects,
  AnalysisResultData,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  filterArrayByKeyName,
  getAffectedAreaHighlightData,
  getAffectedAreaUserHighlightData,
  getAffectedPipelineHighlightData,
  getAllSelected,
  getAllSelectedByMergeRoadName,
  getAnalysisThemeConfig,
  getAnalysisUiConfig,
  getAnalysisUiData,
  getClosedPipelineHighlightData,
  getHoverHighlightData,
  getReversePipelineHighlightData,
  getSelectedObjectHighlightData,
  getValveListHighlightData,
  getWaterOutageAreaHighlightData,
  getWaterOutageAreaUserHighlightData,
  mergeSameRoadNameAndDiameter,
  QueryParams,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Importance, MsgFrom, SendSMSParams } from '@waterdesk/data/sms';
import { FeatureCode } from '@waterdesk/data/system-feature';
import { AsyncTaskStatus } from '@waterdesk/request/get-async-task';
import { sendSMS } from '@waterdesk/request/get-sms-data';
import {
  formatAnalysisData,
  GetAnalysisDataAsyncResponse,
  getAnalysisData,
  getWaterMeterListByDMA,
} from '@waterdesk/request/get-valve-analysis-data';
import { message, Progress, Spin } from 'antd';
import { Button, NavBar, Space } from 'antd-mobile';
import { Key, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useFeatureFlags from 'src/app/hooks/use-feature-flags';
import useLocalStorageAsyncTask from 'src/app/hooks/use-local-storage-tasks';
import { curDb, hostApp } from 'src/app/host-app';
import { baseActions, useBaseSlice } from 'src/store/base';
import { highlightActions, useHighlightSlice } from 'src/store/highlight';
import { leftWrapperActions } from 'src/store/left-wrapper';
import { selectLeftContainerTypeWrapper } from 'src/store/left-wrapper/selector';
import { MENU_TOOLS_VALVE } from 'src/store/left-wrapper/types';
import { useSchedulingSlice } from 'src/store/scheduling';
import { useSelectionSlice } from 'src/store/selection';
import { selectSelectionItem } from 'src/store/selection/selector';
import AnalysisResult from '../../../components/analyse-result/analysis-result';
import { TabsContentWrapper } from './style';

export interface ObjectFormItem {
  oname: string;
  otype: string;
  shape: string;
  otypeTitle: string;
  key: string;
  name: number;
  highlightColor?: string;
  highlightIcon?: string;
}

export default function ValveAnalysisContainer() {
  useBaseSlice();
  useSelectionSlice();
  useHighlightSlice();
  useSchedulingSlice();

  const [messageApi, contextHolder] = message.useMessage();
  const dispatch = useDispatch();
  const selectedItems = useSelector(selectSelectionItem);
  const leftContainerType = useSelector(selectLeftContainerTypeWrapper);

  const { state: hasSendSMS } = useFeatureFlags(FeatureCode.QUICK_SOLUTION_SMS);
  const [resultVisible, setResultVisible] = useState<boolean>(false);
  const [analysisObjects, setAnalysisObjects] = useState<AnalysisObjects>({
    openList: [],
    closeList: [],
  });
  const [analysisResult, setAnalysisResult] = useState<
    AnalysisResultData | undefined
  >(undefined);
  const [dimensionVisible, setDimensionVisible] = useState<boolean>(false);
  const [highlightNode, setHighlightNode] = useState<boolean>(false);
  const [addDeviceState, setAddDeviceState] = useState<boolean>(false);
  const [activeThemes, setActiveThemes] = useState<{
    [key in AnalysisUiConfigType]?: string;
  }>({});
  const [analysisThemesConfig, setAnalysisThemesConfig] = useState<{
    [key in AnalysisUiConfigType]?: LegendGroupData[];
  }>({});
  const [tableSelectedKeys, setTableSelectedKeys] = useState<{
    [key in AnalysisUiConfigType]?: React.Key[];
  }>({});
  const [hoverObject, setHoverObject] = useState<
    undefined | AnalysisUiDataItem
  >(undefined);
  const { token } = useToken();

  const mapView = hostApp().getMainMapView();
  const { valveAnalysisConfig } = hostApp().appConfig;

  const count =
    analysisObjects.openList.length + analysisObjects.closeList.length;

  const onClose = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: undefined,
      }),
    );
  };

  const {
    progress,
    taskId,
    add,
    message: taskProcessMag,
  } = useLocalStorageAsyncTask('valveAnalysisTask', {
    progressStep: hostApp().appConfig.progressStep,
    progressFixed: true,
    formatValues: (values) =>
      formatAnalysisData(values, valveAnalysisConfig!, curDb()),
    callback: (res: GetAnalysisDataAsyncResponse) => {
      const { values, taskStatus } = res;
      if (taskStatus === AsyncTaskStatus.SUCCESS) {
        setAnalysisResult(values);
        setResultVisible(true);
      }
    },
  });

  const fetchValveAnalysisResult = async () => {
    if (!valveAnalysisConfig) return;
    if (count < 1) {
      return;
    }
    const res = await getAnalysisData(analysisObjects);
    if (res.status === 'Success' && res.taskId) {
      add(res.taskId);
    }
  };

  const handleLocate = (otype: string, oname: string, shape: string) => {
    mapView?.selectAndNavigate(otype, oname, shape);
  };

  const handleHover = (hoverObject: AnalysisUiDataItem | undefined) => {
    setHoverObject(hoverObject);
  };

  const getSelectedObjectList = (
    selectedObjects: IObjectItem[],
  ): AnalysisObject[] => {
    const originList = [...analysisObjects.closeList];
    selectedObjects.forEach((selectedDevices) => {
      const { oname, otype, highlightIcon, shape } = selectedDevices;
      const key = makeObjectId(otype, oname);
      const itemProperty = curDb().propertyInfos.get(otype);
      const foundObject = analysisObjects.closeList.find(
        (item) => item.key === key,
      );
      if (!foundObject) {
        originList.push({
          oname,
          otype,
          shape: shape ?? '',
          highlightIcon,
          highlightColor: 'select',
          otypeTitle: itemProperty ? itemProperty.title : otype,
          key,
        });
      }
    });
    return originList;
  };

  const clearHighlightSelectedObject = () => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: 'valveAnalysisSelectedObject',
      }),
    );
  };

  const clearHighlightAnalysisResult = () => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: [
          'affectedArea',
          'affectedAreaUser',
          'affectedPipeline',
          'closedPipeline',
          'valveList',
          'waterOutageArea',
          'waterOutageAreaUser',
          'reversePipeline',
          'analysisResultHover',
        ],
      }),
    );
  };

  const handleHighlightSelectedObject = (
    analysisObjectList: AnalysisObject[],
  ) => {
    const highlightData = getSelectedObjectHighlightData(analysisObjectList);
    dispatch(
      highlightActions.updateHighlight({
        highlight: { valveAnalysisSelectedObject: highlightData },
      }),
    );
  };

  // 分析结果高亮
  const handleHighlightAnalysisResult = (
    analysisResult: AnalysisResultData,
    hoverObject: AnalysisUiDataItem | undefined,
    theme: {
      [key in AnalysisUiConfigType]?: string;
    },
    dimension: boolean,
  ) => {
    if (typeof valveAnalysisConfig === 'undefined') {
      messageApi.warning('主题配置加载失败');
    } else {
      const {
        waterOutageArea,
        waterOutageAreaUser,
        valveList,
        affectedArea,
        affectedAreaUser,
        closedPipeline,
        affectedPipeline,
        reversePipeline,
      } = analysisResult;

      const analysisResultHighlightDataTheme: {
        [key in AnalysisUiConfigType]?: LegendGroupData;
      } = {};
      Object.entries(theme).forEach((item) => {
        const [key, name] = item as [AnalysisUiConfigType, string];
        const data = analysisThemesConfig[key]?.find(
          (item) => item.name === name,
        );
        if (data) {
          analysisResultHighlightDataTheme[key] = data;
        }
      });

      const filterWaterOutageArea = filterArrayByKeyName(
        waterOutageArea,
        affectedArea,
        'oname',
      );

      const analysisResultHighlightData = {
        waterOutageArea: getWaterOutageAreaHighlightData(
          filterWaterOutageArea,
          analysisResultHighlightDataTheme.waterOutageArea,
          valveAnalysisConfig?.waterOutageArea?.defaultHighlightColor,
          dimension,
        ),
        waterOutageAreaUser: getWaterOutageAreaUserHighlightData(
          waterOutageAreaUser,
          curDb(),
          analysisResultHighlightDataTheme.waterOutageAreaUser,
          valveAnalysisConfig?.waterOutageAreaUser?.defaultHighlightColor,
          dimension,
        ),
        valveList: getValveListHighlightData(
          valveList,
          curDb(),
          analysisResultHighlightDataTheme.valveList,
          valveAnalysisConfig?.valveList?.defaultHighlightColor,
          dimension,
        ),
        affectedArea: getAffectedAreaHighlightData(
          affectedArea,
          analysisResultHighlightDataTheme.affectedArea,
          valveAnalysisConfig?.affectedArea?.defaultHighlightColor,
          dimension,
        ),
        affectedAreaUser: getAffectedAreaUserHighlightData(
          affectedAreaUser,
          curDb(),
          analysisResultHighlightDataTheme.affectedAreaUser,
          valveAnalysisConfig?.affectedAreaUser?.defaultHighlightColor,
          dimension,
        ),
        closedPipeline: getClosedPipelineHighlightData(
          closedPipeline,
          analysisResultHighlightDataTheme.closedPipeline,
          valveAnalysisConfig?.closedPipeline?.defaultHighlightColor,
          dimension,
        ),
        affectedPipeline: getAffectedPipelineHighlightData(
          affectedPipeline,
          analysisResultHighlightDataTheme.affectedPipeline,
          valveAnalysisConfig?.affectedPipeline?.defaultHighlightColor,
          dimension,
        ),
        reversePipeline: getReversePipelineHighlightData(
          reversePipeline,
          analysisResultHighlightDataTheme.reversePipeline,
          valveAnalysisConfig?.reversePipeline?.defaultHighlightColor,
          dimension,
        ),
      };

      const hoverObjectHighlightData = {
        analysisResultHover: getHoverHighlightData(
          typeof hoverObject === 'undefined' ? [] : [hoverObject],
          curDb(),
        ),
      };

      dispatch(
        highlightActions.updateThemeConfig({
          themeConfig: analysisResultHighlightDataTheme,
        }),
      );

      dispatch(
        highlightActions.updateHighlight({
          highlight: {
            ...analysisResultHighlightData,
            ...hoverObjectHighlightData,
          },
        }),
      );
    }
  };

  const hideFeatureTooltipImportant = () => {
    dispatch(
      baseActions.updateHideFeatureTooltipImportant({
        visible: false,
      }),
    );
    hostApp().redrawMapView();
  };

  const handleValveAnalysis = () => {
    dispatch(
      highlightActions.clearHighlight({ highlightLayerName: 'valveAnalyse' }),
    );
    hideFeatureTooltipImportant();
    fetchValveAnalysisResult();
  };

  const resetHideFeatureTooltipImportant = () => {
    dispatch(baseActions.updateHideFeatureTooltipImportant({}));
    hostApp().redrawMapView();
  };

  const handleReset = () => {
    const mapView = hostApp().getMainMapView();
    if (mapView) {
      mapView.setMultiSelectedFlag(false);
    }
    setAnalysisResult(undefined);
    clearHighlightAnalysisResult();
    setAnalysisObjects({
      closeList: [],
      openList: [],
    });
    clearHighlightSelectedObject();
    dispatch(highlightActions.resetHighlight());
    resetHideFeatureTooltipImportant();
  };

  const handleSetActiveThemes = (panelKey: string, value?: string) => {
    setActiveThemes((state) => ({
      ...state,
      [panelKey]: value,
    }));
  };

  const handleSetTableSelectedKeys = (
    keys: Key[],
    tableKey: AnalysisUiConfigType,
  ) => {
    setTableSelectedKeys((state) => ({
      ...state,
      [tableKey]: keys,
    }));
  };

  useEffect(() => {
    if (analysisResult) {
      const selectedKeys: typeof tableSelectedKeys = {};
      const valveAnalysisUiData = getAnalysisUiData(analysisResult);
      Object.entries(valveAnalysisUiData).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            selectedKeys[key as AnalysisUiConfigType] = value.map(
              (item) => item.oname,
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            selectedKeys[key as AnalysisUiConfigType] =
              mergeSameRoadNameAndDiameter(value).map((item) => item.oname);
            break;
          default:
            break;
        }
      });

      setTableSelectedKeys(selectedKeys);
    }
  }, [analysisResult]);

  useEffect(() => {
    if (analysisResult) {
      const customResult: AnalysisResultData = { ...analysisResult };
      Object.entries(analysisResult).forEach((item) => {
        const [key, value] = item;
        switch (key as AnalysisUiConfigType) {
          case 'valveList':
          case 'affectedArea':
          case 'affectedAreaUser':
          case 'waterOutageArea':
          case 'waterOutageAreaUser':
            customResult[key as AnalysisUiConfigType] = getAllSelected(
              (tableSelectedKeys[key as AnalysisUiConfigType] ??
                []) as string[],
              value as AnalysisUiDataItem[],
            );
            break;
          case 'affectedPipeline':
          case 'closedPipeline':
          case 'reversePipeline':
            customResult[key as AnalysisUiConfigType] =
              getAllSelectedByMergeRoadName(
                (tableSelectedKeys[key as AnalysisUiConfigType] ??
                  []) as string[],
                value as AnalysisUiDataItem[],
              );
            break;
          default:
            break;
        }
      });
      handleHighlightAnalysisResult(
        customResult,
        hoverObject,
        activeThemes,
        dimensionVisible,
      );
    }
  }, [
    analysisResult,
    dimensionVisible,
    activeThemes,
    tableSelectedKeys,
    hoverObject,
  ]);

  useEffect(() => {
    if (valveAnalysisConfig) {
      const valveAnalysisUIConfig = getAnalysisUiConfig(valveAnalysisConfig);
      Object.entries(valveAnalysisUIConfig).forEach((item) => {
        const [panelKey, config] = item;
        if (config) {
          const valveAnalysisThemeConfig = getAnalysisThemeConfig(
            config.otype,
            config.themes,
            curDb(),
          );
          setAnalysisThemesConfig((state) => ({
            ...state,
            [panelKey]: valveAnalysisThemeConfig,
          }));
          handleSetActiveThemes(panelKey, valveAnalysisThemeConfig?.[0].name);
        }
      });
    }
  }, [valveAnalysisConfig]);

  useEffect(() => {
    handleHighlightSelectedObject(analysisObjects.closeList);
  }, [analysisObjects]);

  useEffect(() => {
    if (!(leftContainerType === MENU_TOOLS_VALVE)) {
      const mapView = hostApp().getMainMapView();
      if (mapView) {
        mapView.setMultiSelectedFlag(false);
      }
      setAnalysisResult(undefined);
      clearHighlightAnalysisResult();
      dispatch(highlightActions.resetHighlight());
      resetHideFeatureTooltipImportant();
    }
  }, [leftContainerType]);

  useEffect(() => {
    if (mapView && leftContainerType === MENU_TOOLS_VALVE) {
      const selectionCollection = mapView?.selectionCollection;
      if (selectionCollection) {
        const objetcList = getSelectedObjectList(
          selectionCollection.selectedObjects,
        );
        setAnalysisObjects({
          closeList: objetcList,
          openList: [],
        });
      }
    }
  }, [selectedItems, mapView, leftContainerType]);

  useEffect(() => {
    if (addDeviceState && count < 1) {
      return;
    }
    handleValveAnalysis();
  }, [count, addDeviceState]);

  const addDevice = () => {
    const mapView = hostApp().getMainMapView();
    if (mapView) {
      mapView.setMultiSelectedFlag(true);
      setAddDeviceState(true);
    }
  };

  const handleSendSMS = async (
    msgFrom: MsgFrom,
    importance: Importance,
    msgText: string,
    dataSource: WaterMeterInfo[],
  ) =>
    new Promise<void>((resolve) => {
      const maxSendSMSCount = valveAnalysisConfig?.maxSendSMSCount ?? 0;
      if (dataSource.length >= maxSendSMSCount) {
        messageApi.warning(`发送数量超出最大限制：${maxSendSMSCount}条`);
        resolve();
        return;
      }
      const newValues: SendSMSParams = {
        msgFrom,
        importance,
        msgText,
        msgSendJson: dataSource.map((item) => ({
          msg_send_type: 'TELEPHONE',
          msg_send_code: item.phone,
        })),
      };

      sendSMS(newValues, MsgFrom.WATER_SHUTOFF_MSG).then((res) => {
        if (res.status === 'Success') {
          messageApi.success('发送成功');
        } else {
          messageApi.error('发送失败');
        }
        resolve();
      });
    });

  const fetchWaterMeterList = async (params: QueryParams['params']) => {
    const res = await getWaterMeterListByDMA(params);
    if (res.status === 'Success') {
      return res.data ?? {};
    }
    return {};
  };

  return {
    valveAnalyseForm: (
      <>
        <Spin
          spinning={!!taskId}
          delay={500}
          tip={
            <>
              <span>{taskProcessMag || '开始分析'}:</span>
              <Progress
                style={{ width: '50%' }}
                percent={progress}
              />
            </>
          }
          style={{
            width: '100vw',
            height: '100vh',
            paddingBottom: '8px',
            position: 'fixed',
            top: 0,
          }}
        />
        {contextHolder}
      </>
    ),
    valveAnalysisResult:
      analysisResult && resultVisible ? (
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
          }}
        >
          <NavBar
            onBack={() => {
              setResultVisible(false);
              handleReset();
              onClose();
            }}
            style={{ backgroundColor: token.colorBgContainer }}
            right={
              addDeviceState ? (
                '增加多选对象'
              ) : (
                <Button onClick={addDevice}>
                  <PlusCircleOutlined />
                </Button>
              )
            }
          >
            阀门分析
          </NavBar>
          <TabsContentWrapper>
            <Space
              block
              justify="between"
              align="center"
              style={{ padding: '10px 20px' }}
            >
              <span>选择对象数: {count}</span>
              <Button
                size="small"
                color="primary"
                onClick={() => {
                  setAddDeviceState(false);
                  setResultVisible(false);
                  clearHighlightAnalysisResult();
                  onClose();
                  handleValveAnalysis();
                }}
              >
                重分析
              </Button>
            </Space>
            <AnalysisResult
              canSendSMS={hasSendSMS}
              highlightNodeFunc={
                hostApp().appConfig.valveAnalysisConfig?.highlightNodeFunc ??
                false
              }
              highlightNode={highlightNode}
              setHighlightNode={() => setHighlightNode((state) => !state)}
              analysisConfig={valveAnalysisConfig}
              analysisData={analysisResult}
              dimension={dimensionVisible}
              activeThemes={activeThemes}
              analysisThemesConfig={analysisThemesConfig}
              tableSelectedKeys={tableSelectedKeys}
              setTableSelectedKeys={handleSetTableSelectedKeys}
              handleSwitchDimensionVisible={(visible) =>
                setDimensionVisible(visible)
              }
              getWaterMeterList={fetchWaterMeterList}
              onSendSMS={handleSendSMS}
              handleActiveOnChange={handleSetActiveThemes}
              handleLocate={handleLocate}
              handleHover={handleHover}
              db={curDb()}
            />
          </TabsContentWrapper>
        </div>
      ) : null,
  };
}
