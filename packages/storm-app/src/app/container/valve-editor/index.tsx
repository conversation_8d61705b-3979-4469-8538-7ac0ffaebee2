/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { ValveOperationValue } from '@waterdesk/data/valve-manager/valve-manager-data';
import { getLinkDiameter } from '@waterdesk/request/get-group-prop-values';
import {
  getSmartValveList,
  getValveOperationList,
} from '@waterdesk/request/valve-operation';
import { NavBar, Popup } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb } from 'src/app/host-app';
import {
  useValveEditorSlice,
  valveEditorActions,
} from 'src/store/valve-editor';
import selectValveEditor from 'src/store/valve-editor/selector';
import ValveEditor from '../../../components/valve-editor';

export default function ValveEditorContainer() {
  useValveEditorSlice();
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);
  const [isSmartValve, setIsSmartValve] = useState<boolean>(false);
  const { token } = useToken();

  const [valveOperationDetails, setValveOperationDetails] = useState<
    ValveOperationValue[]
  >([]);
  const dispatch = useDispatch();
  const { open, otype, oname } = useSelector(selectValveEditor);

  const getIsSmartValve = async () => {
    setIsSmartValve(false);
    const responseData = await getSmartValveList(curDb());

    if (responseData.status === 'Success') {
      responseData.smartValveList?.forEach((item) => {
        if (item.otype === otype && item.oname === oname) {
          setIsSmartValve(true);
        }
      });
    }
  };

  const initializeData = async () => {
    try {
      setDetailsLoading(true);
      const [operationListRespose] = await Promise.all([
        getValveOperationList({
          oname,
        }),
        getLinkDiameter(otype, oname),
      ]);
      if (
        operationListRespose.status === 'Success' &&
        operationListRespose.valveOperationList
      ) {
        setValveOperationDetails(operationListRespose.valveOperationList);
      }
    } finally {
      setDetailsLoading(false);
    }
  };

  const getObjectTitle = () => {
    const title = curDb().getPropertyInfo(otype)?.title;
    if (title !== undefined) return `${title}: `;
    return '';
  };

  useEffect(() => {
    if (open) {
      initializeData();
      getIsSmartValve();
    }
  }, [open, otype, oname]);

  const onClose = () => {
    dispatch(valveEditorActions.closeValveEditor());
  };

  return (
    <Popup
      position="right"
      visible={open}
    >
      <div
        style={{
          width: '100vw',
          paddingBottom: '8px',
        }}
      >
        <NavBar
          onBack={onClose}
          style={{ backgroundColor: token.colorBgContainer }}
        >
          {`状态变更记录 - ${getObjectTitle()}${oname}`}
        </NavBar>
        <ValveEditor
          loading={detailsLoading}
          valveOperationDetails={valveOperationDetails}
          isSmartValve={isSmartValve}
        />
      </div>
    </Popup>
  );
}
