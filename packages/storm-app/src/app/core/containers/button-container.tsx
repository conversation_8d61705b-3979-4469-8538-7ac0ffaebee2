/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { PropertyButtonEditor } from '@waterdesk/data/property/property-info';
import { Button } from 'antd';
import { useDispatch } from 'react-redux';
import IconText from 'src/components/icon-font/icon-text';
import { PropertyItemValue } from 'src/components/property/property-item';
import { baseActions } from 'src/store/base';
import {
  PropertyChartActionArgs,
  PropertyDmaCustomerAction,
} from 'src/store/base/types';

interface Props {
  propertyButtonEditor: PropertyButtonEditor;
  propertyItemValue: PropertyItemValue | undefined;
  pinnedItem?: IObjectItem;
  compareDevice?: IObjectItem;
}

export default function ButtonContainer(props: Props) {
  const { propertyButtonEditor, propertyItemValue, pinnedItem, compareDevice } =
    props;
  const dispatch = useDispatch();

  const displayChart = () => {
    if (!propertyItemValue) {
      return;
    }
    const propertyChartAction: PropertyChartActionArgs = {
      indicatorType: propertyItemValue.indicatorType,
      indicatorName: propertyItemValue.indicatorName,
      vprop: propertyItemValue.vprop,
      pinnedItem,
    };
    dispatch(
      baseActions.propertyChartAction({
        arg: propertyChartAction,
        compareDevice,
      }),
    );
  };

  const displayDmaCustomer = () => {
    if (!propertyItemValue) {
      return;
    }
    const propertyChartAction: PropertyDmaCustomerAction = {
      open: true,
      otype: propertyItemValue.selectedObject.otype,
      oname: propertyItemValue.selectedObject.oname,
    };
    dispatch(
      baseActions.propertyDmaCustomerAction({
        arg: propertyChartAction,
      }),
    );
  };

  const onClick = () => {
    if (!propertyItemValue) {
      return;
    }
    switch (propertyButtonEditor.actionName) {
      case 'propertyChartAction':
        displayChart();
        break;
      case 'propertyDmaCustomerAction':
        displayDmaCustomer();
        break;
      default:
        break;
    }
  };

  return (
    <Button
      className={propertyButtonEditor.actionName}
      style={{ padding: '0' }}
      type="link"
      onClick={() => onClick()}
      title={propertyButtonEditor.title}
      icon={<IconText text={propertyButtonEditor.iconText ?? ''} />}
    />
  );
}
