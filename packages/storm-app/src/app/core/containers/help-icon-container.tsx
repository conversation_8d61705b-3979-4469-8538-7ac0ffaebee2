/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useSelector } from 'react-redux';
import HelpIcon from 'src/components/icon/help-icon';
import { useUserConfigSlice } from 'src/store/user-config';
import { selectShowHelp } from 'src/store/user-config/selector';

const HocHelpIcon = <P extends {}>(WrappedComponent: React.ComponentType<P>) =>
  function Component(props: P) {
    useUserConfigSlice();

    const showHelp = useSelector(selectShowHelp);

    if (showHelp) return <WrappedComponent {...props} />;
    return null;
  };

const WrapperHelpIcon = HocHelpIcon(HelpIcon);

export default WrapperHelpIcon;
