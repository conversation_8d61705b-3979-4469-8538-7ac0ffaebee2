/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ValveOperationValue } from '@waterdesk/data/valve-manager/valve-manager-data';
import { getValveOperationList } from '@waterdesk/request/valve-operation';
import { useInterval } from 'ahooks';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsyncLocate } from 'src/app/hooks/use-async-locate';
import NoticeValveList from 'src/components/notice/notice-valve-list';
import { baseActions, useBaseSlice } from 'src/store/base';
import {
  selectDataInitialComplete,
  selectLastQueryNoticeValveTime,
  selectNoticeValveList,
} from 'src/store/base/selectors';
import { selectGlobalConfig } from 'src/store/user-config/selector';

const NoticeValveContainer = ({ list }: { list?: ValveOperationValue[] }) => {
  useBaseSlice();

  const dispatch = useDispatch();
  const globalConfig = useSelector(selectGlobalConfig);
  const noticeValveList = useSelector(selectNoticeValveList);
  const dataInitialComplete = useSelector(selectDataInitialComplete);
  const lastQueryNoticeValveTime = useSelector(selectLastQueryNoticeValveTime);

  const [delay, setDelay] = useState<number | undefined>(undefined);

  const { locate } = useAsyncLocate();

  const valveOperationsChanged = (
    lastItems: ValveOperationValue[],
    newItems: ValveOperationValue[],
  ): boolean => {
    if (lastItems.length !== newItems.length) return true;

    for (let i = 0; i < lastItems.length; i += 1) {
      if (
        lastItems[i].otime !== newItems[i].otime ||
        lastItems[i].otype !== newItems[i].otype ||
        lastItems[i].oname !== newItems[i].oname ||
        lastItems[i].eventId !== newItems[i].eventId
      )
        return true;
    }

    return false;
  };

  const fetchValveOperationList = async () => {
    const endTime = dayjs();
    getValveOperationList({
      startDate: dayjs(lastQueryNoticeValveTime).format('YYYY-MM-DD HH:mm:ss'),
      endDate: endTime.format('YYYY-MM-DD HH:mm:ss'),
    }).then((res) => {
      if (res.status === 'Success') {
        const sortedList = (res.valveOperationList || []).sort((a, b) =>
          dayjs(a.otime).isBefore(dayjs(b.otime)) ? 1 : -1,
        );
        if (valveOperationsChanged(noticeValveList, sortedList)) {
          dispatch(
            baseActions.updateNoticeValveList({
              list: sortedList,
            }),
          );
        }
      }
    });
  };

  const handleClickWarn = (info: ValveOperationValue) => {
    const { otype, oname, shape } = info;
    locate(otype, oname, shape);
  };

  const clearInterval = useInterval(fetchValveOperationList, delay, {
    immediate: true,
  });

  useEffect(() => {
    if (typeof globalConfig === 'undefined' || !dataInitialComplete)
      return undefined;
    if (typeof lastQueryNoticeValveTime === 'undefined') {
      dispatch(
        baseActions.updateLastQueryNoticeValveTime({
          time: dayjs()
            .add(-globalConfig.noticeQueryDuration, 'hours')
            .format('YYYY-MM-DD 00:00:00'),
        }),
      );
    }

    setDelay(120000);

    return () => clearInterval();
  }, [dataInitialComplete, globalConfig]);

  return (
    <NoticeValveList
      list={(list || noticeValveList.slice(0, 100)) ?? []}
      onClickValve={handleClickWarn}
    />
  );
};

NoticeValveContainer.displayName = 'NoticeValveContainer';

export default NoticeValveContainer;
