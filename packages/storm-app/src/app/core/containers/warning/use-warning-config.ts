/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  convertWarnTypeDataToOptions,
  WarnPrimaryType,
} from '@waterdesk/data/warn';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useBaseSlice } from 'src/store/base';
import { selectGlobalConfig } from 'src/store/user-config/selector';

const useWarningConfig = () => {
  useBaseSlice();

  const globalConfig = useSelector(selectGlobalConfig);

  /** 数据库配置的警告大类 */
  const warnTypeData = useMemo(
    () => globalConfig?.warnConfig?.warnType ?? [],
    [globalConfig],
  );

  /** 警告分组显示规则设置 */
  const warnGroupDisplayConfig = useMemo(
    () =>
      globalConfig?.warnGroupDisplayConfig ?? {
        enable: 0,
        itemTimeDiff: 2,
        groupTimeDiff: 30,
      },
    [globalConfig],
  );

  /** 评估警告 */
  const assessmentWarnType = WarnPrimaryType.ASSESSMENT;

  /** 非评估警告 */
  const nonAssessmentWarnTypes = useMemo(
    () =>
      globalConfig?.warnConfig?.nonAssessmentWarnTypes ??
      warnTypeData
        .filter((item) => item.type !== WarnPrimaryType.ASSESSMENT)
        .map((item) => item.type as WarnPrimaryType),
    [globalConfig],
  );

  /** 警告类型选项 */
  const warnTypeOptions = useMemo(
    () => convertWarnTypeDataToOptions(warnTypeData) ?? [],
    [warnTypeData],
  );

  /** 业务分类选项 */
  const classTypeOptions = useMemo(() => {
    const warnClass = globalConfig?.warnConfig?.warnClassId ?? [];
    return convertWarnTypeDataToOptions(warnClass) ?? [];
  }, [globalConfig?.warnConfig?.warnClassId]);

  /** 警告来源选项 */
  const warnSourceOptions = useMemo(() => {
    const warnSource = globalConfig?.warnConfig?.warnSource ?? [];
    return convertWarnTypeDataToOptions(warnSource) ?? [];
  }, [globalConfig?.warnConfig?.warnSource]);

  return {
    warnTypeData,
    assessmentWarnType,
    nonAssessmentWarnTypes,
    warnTypeOptions,
    classTypeOptions,
    warnGroupDisplayConfig,
    warnSourceOptions,
  };
};

export default useWarningConfig;
