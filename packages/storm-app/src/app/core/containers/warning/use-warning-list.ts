/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ObservationType } from '@waterdesk/data/observation-scada-data';
import {
  DEFAULT_PROCESSED_WARN_TIME,
  DEFAULT_PROCESSED_WARN_TIME_UNIT,
  DEFAULT_UNPROCESSED_WARN_TIME,
  DEFAULT_UNPROCESSED_WARN_TIME_UNIT,
  WarnConfig,
} from '@waterdesk/data/scenes/warning-scene';
import {
  getWarnListByConfig,
  WarnConfirmStatus,
  WarnInfoList,
} from '@waterdesk/data/warn';
import { classifyByRank } from '@waterdesk/data/warn/warn-count';
import { WarnSettingList } from '@waterdesk/data/warn-setting';
import { getWarnListByDateTime } from '@waterdesk/request/get-warn';
import { getObservationScadaList } from '@waterdesk/request/observation-scada';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { baseActions } from 'src/store/base';
import useWarningConfig from './use-warning-config';

interface Props {
  warnTypeList: { [key: string]: WarnSettingList };
  warnConfig: WarnConfig | undefined;
}

const useWarningList = ({ warnTypeList, warnConfig }: Props) => {
  const dispatch = useDispatch();

  const [unprocessedWarningList, setUnprocessedWarningList] = useState<
    Record<string, WarnInfoList>
  >({});
  const [processedWarningList, setProcessedWarningList] = useState<
    Record<string, WarnInfoList>
  >({});

  const { nonAssessmentWarnTypes } = useWarningConfig();

  const dispatchNoticeWarnList = (warnList: WarnInfoList) => {
    if (warnList.length) {
      dispatch(
        baseActions.updateNoticeWarnList({
          list: warnList,
        }),
      );
    }
  };

  const fetchWarnList = async () => {
    const unprocessedWarnTime =
      warnConfig?.unprocessedWarnTime ?? DEFAULT_UNPROCESSED_WARN_TIME;
    const processedWarnTime =
      warnConfig?.processedWarnTime ?? DEFAULT_PROCESSED_WARN_TIME;
    const unprocessedWarnTimeUnit =
      warnConfig?.unprocessedWarnTimeUnit ?? DEFAULT_UNPROCESSED_WARN_TIME_UNIT;
    const processedWarnTimeUnit =
      warnConfig?.processedWarnTimeUnit ?? DEFAULT_PROCESSED_WARN_TIME_UNIT;
    const endTime = dayjs();
    const processedStartTime = dayjs()
      .add(-processedWarnTime, processedWarnTimeUnit)
      .format('YYYY-MM-DD HH:mm:ss');
    const unprocessedStartTime = dayjs()
      .add(-unprocessedWarnTime, unprocessedWarnTimeUnit)
      .format('YYYY-MM-DD HH:mm:ss');
    const [
      processedWarnTypeListRes,
      unprocessedWarnTypeListRes,
      observationListRes,
    ] = await Promise.all([
      getWarnListByDateTime(
        {
          startTime: processedStartTime,
          endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          type: nonAssessmentWarnTypes,
          warnTypeList,
        },
        curDb(),
      ),
      getWarnListByDateTime(
        {
          startTime: unprocessedStartTime,
          endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          type: nonAssessmentWarnTypes,
          warnTypeList,
          warnConfirmStatus: WarnConfirmStatus.NOT_CONFIRM,
        },
        curDb(),
      ),
      getObservationScadaList(),
    ]);

    const observationList =
      observationListRes.list
        ?.filter((i) => i.type === ObservationType.SCADA)
        .map((item) => ({
          otype: item.otype,
          oname: item.oname,
        })) ?? undefined;

    if (processedWarnTypeListRes.status === 'Success') {
      const processedWarningList = processedWarnTypeListRes.list
        .filter((i) => i.confirmStatus !== WarnConfirmStatus.NOT_CONFIRM)
        .sort((a, b) =>
          dayjs(a.startTime).isBefore(dayjs(b.startTime)) ? 1 : -1,
        );
      const list = getWarnListByConfig(processedWarningList, warnConfig);
      setProcessedWarningList(classifyByRank(list, observationList));
      dispatchNoticeWarnList(processedWarnTypeListRes.list);
    }

    if (unprocessedWarnTypeListRes.status === 'Success') {
      const unprocessedWarningList = unprocessedWarnTypeListRes.list.sort(
        (a, b) => (dayjs(a.startTime).isBefore(dayjs(b.startTime)) ? 1 : -1),
      );
      const list = getWarnListByConfig(unprocessedWarningList, warnConfig);
      setUnprocessedWarningList(classifyByRank(list, observationList));
    }
  };

  return {
    unprocessedWarningList,
    processedWarningList,
    fetchWarnList,
  };
};

export default useWarningList;
