/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DmaCustomer,
  getDmaCustomer,
} from '@waterdesk/request/get-dma-customer';
import { useAntdTable } from 'ahooks';
import { Card, Modal, Table } from 'antd';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  dmaCustomerActions,
  useDmaCustomerSlice,
} from 'src/store/dma-customer';
import selectDmaCustomer from 'src/store/dma-customer/selector';

interface Result {
  total: number;
  list: DmaCustomer[];
}

export default function DmaCustomerContainer() {
  useDmaCustomerSlice();
  const dispatch = useDispatch();
  const dmaCustomer = useSelector(selectDmaCustomer);
  const { open, otype, oname } = dmaCustomer;

  const getTableData = async ({
    current,
    pageSize,
  }: {
    current: number;
    pageSize: number;
  }): Promise<Result> => {
    const res = await getDmaCustomer(pageSize, current, otype, oname, '');
    return Promise.resolve({
      list: res.dmaCustomers ?? [],
      total: res.total ?? 0,
    });
  };

  const defaultParams = {
    current: 1,
    pageSize: 10,
  };

  const { tableProps, run } = useAntdTable(getTableData, {
    defaultPageSize: 10,
    defaultParams: [defaultParams],
    manual: true,
  });

  const columns = [
    {
      title: '编号',
      dataIndex: 'oname',
    },
    {
      title: '所属一级分区',
      dataIndex: 'firstDmaId',
    },
    {
      title: '所属二级分区',
      dataIndex: 'secondDmaId',
    },
    {
      title: '所属三级分区',
      dataIndex: 'thirdDmaId',
    },
    {
      title: '地址',
      dataIndex: 'address',
    },
  ];

  useEffect(() => {
    if (open) {
      run(defaultParams);
    }
  }, [open, otype, oname]);

  return (
    <Modal
      title="用户表"
      width="90%"
      destroyOnHidden
      onCancel={() =>
        dispatch(
          dmaCustomerActions.dmaCustomerVisible({
            open: false,
          }),
        )
      }
      open={open}
      maskClosable={false}
      centered
      footer={false}
    >
      <Card>
        <Table
          size="small"
          columns={columns}
          scroll={{
            y: 'calc(100vh - 300px)',
          }}
          rowKey="key"
          {...tableProps}
        />
      </Card>
    </Modal>
  );
}
