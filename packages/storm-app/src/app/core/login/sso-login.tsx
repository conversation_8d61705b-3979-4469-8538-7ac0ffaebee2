/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LoadingOutlined } from '@ant-design/icons';
import { MenuInfo } from '@waterdesk/data/menu-data';
import {
  GetLoginUserInfoResponse,
  getLoginUserInfo,
} from '@waterdesk/request/get-user-info';
import {
  LoginResponse,
  onLoginSuccess,
  requestSSOLogin,
} from '@waterdesk/request/login';
import { getUrlParams, UrlParams } from '@waterdesk/request/url-params';
import { message } from 'antd';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { APP_NAME } from 'src/config';

function SsoLoginDirect() {
  const navigate = useNavigate();

  const location = useLocation();
  const [messageApi, contextHolder] = message.useMessage();

  const getPageUrlParams = (): UrlParams => getUrlParams(location.search);

  const navigateToHomePage = (menuList: MenuInfo[] | undefined) => {
    const homePageMenuInfo: MenuInfo | undefined = (menuList || []).find(
      (item) => item.homepage,
    );

    if (!homePageMenuInfo?.url) {
      messageApi.error('获取首页失败');
      return;
    }
    navigate(homePageMenuInfo?.url || '/', {
      state: {
        menuId: homePageMenuInfo?.id,
      },
    });
  };

  const appLogin = async () => {
    const urlParams = getPageUrlParams();
    const code = urlParams.code ? urlParams.code : '';
    if (code) {
      const response: LoginResponse = await requestSSOLogin(
        code,
        APP_NAME,
        undefined,
      );
      if (response.status === 'Success') {
        onLoginSuccess(response);
        const loginUserInfo: GetLoginUserInfoResponse = await getLoginUserInfo({
          appId: APP_NAME,
        });
        if (loginUserInfo.status === 'Success') {
          navigateToHomePage(loginUserInfo.permissionList);
        } else {
          messageApi.error('获取用户信息失败!');
        }
      }
    } else {
      messageApi.error('登录失败!');
    }
  };

  useEffect(() => {
    appLogin();
  }, []);

  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <LoadingOutlined />
      {contextHolder}
    </div>
  );
}

export default SsoLoginDirect;
