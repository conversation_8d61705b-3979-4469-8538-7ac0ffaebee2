/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  convertLegacyProjectionData,
  registerProjections,
} from '@waterdesk/core/map';
import { Theme } from '@waterdesk/core/theme';
import { MAP_VIEW_NAME_SOLUTION, MapViewName } from '@waterdesk/data/const/map';
import {
  DeviceCollection,
  PlantsAndPumpStationsCollection,
} from '@waterdesk/data/device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '@waterdesk/data/indicator';
import { LayerDataCollection } from '@waterdesk/data/layer-data';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import {
  SCENE_ID_TYPE_SOLUTION,
  Scene,
  SceneIdType,
} from '@waterdesk/data/scene';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import { registerSystemUnits } from '@waterdesk/data/unit-system';
import {
  createSolutionView,
  getPlantAndPumpStationList,
  getPropDefines,
  getScadaNameList,
  getScadaQuotaList,
  getThemeSectionList,
} from '@waterdesk/request/create-map-data';
import dayjs from 'dayjs';
import { hostApp } from 'src/app/host-app';
import MapView from '../map-view/map-view';

export interface MapViewConfig {
  id: string;
  projection: string;
  viewExtent: number[];
  viewId: string;
  viewName: string;
  mapZoomFactor: number;
  scenes: Scene[] | undefined;
  layerData: LayerDataCollection;
  solutionDate: string;
}

type CommonDataGetter = (
  solutionId: string | undefined,
  mapView: MapView,
  theme: Theme,
  solutionDate?: string,
  editable?: boolean,
) => Promise<MapViewConfig | undefined>;

export function createCommonDataGetter(
  viewName: MapViewName,
  sceneId: SceneIdType,
): CommonDataGetter {
  return async function getCommonData(
    solutionId: string | undefined,
    mapView: MapView,
    theme: Theme,
    solutionDate?: string,
    editable?: boolean,
  ): Promise<MapViewConfig | undefined> {
    const date = dayjs(solutionDate).format('YYYY-MM-DD');
    return createSolutionView(theme, solutionId).then((viewResponse) =>
      Promise.all([
        getScadaNameList(viewResponse.startDate ?? date),
        getPlantAndPumpStationList(viewResponse.startDate ?? date),
        getScadaQuotaList(viewResponse.startDate ?? date),
        getPropDefines(viewName, editable),
        getThemeSectionList(sceneId),
      ]).then(
        ([
          deviceResponse,
          plantsAndPumpStationsResponse,
          indicatorResponse,
          propertyResponse,
          themeResponse,
        ]) => {
          if (viewResponse.status === 'Success') {
            const devices: DeviceCollection =
              deviceResponse.devices as DeviceCollection;
            const indicatorObjects: IndicatorObjectCollection =
              deviceResponse.indicators as IndicatorObjectCollection;
            devices.initializeOverlayIndicators(
              indicatorResponse.deviceIndicators as {},
              indicatorResponse.indicatorTypes as IndicatorTypeCollection,
              indicatorObjects,
            );
            const icons = viewResponse.icons as Map<string, string>;
            devices.initializeIcons(icons);

            const plantsAndPumpStations =
              plantsAndPumpStationsResponse.plantsAndPumpStations as PlantsAndPumpStationsCollection;
            devices.initializePlantsAndPumpStations(plantsAndPumpStations);

            const layerData: LayerDataCollection =
              viewResponse.layerData as LayerDataCollection;
            layerData.initializeDevices(devices);

            mapView.setTimelineDate(dayjs(viewResponse.startDate ?? date));
            const timeIndex = getValueFromDateTime(
              dayjs(viewResponse.startDate ?? date),
            );
            mapView.setTimelineDateTime(timeIndex);

            mapView.curDb.initializeIcon(icons);
            mapView.curDb.initializeDevice(
              devices,
              propertyResponse.propertyInfos,
            );
            mapView.curDb.initializeIndicator(indicatorObjects);
            // Todo: appConfig unitJson从getPropDefines接口分离
            mapView.curDb.initializePropertyInfos(
              propertyResponse.propertyInfos as Map<string, PropertyInfo>,
            );
            mapView.curDb.initializeFieldEnumMap(propertyResponse.fieldEnumMap);

            mapView.curDb.initializeLayer(layerData, viewResponse.extent ?? []);

            hostApp().initializeAppConfig(propertyResponse.appConfig);
            registerSystemUnits(propertyResponse.unitJson);
            const projections = propertyResponse.appConfig.proj4Register;
            if (Array.isArray(projections)) {
              const configs = convertLegacyProjectionData(projections);
              registerProjections(configs);
            }

            const solutionData = {
              id: solutionId ?? mapView.mapViewName,
              viewName: mapView.mapViewName,
              projection: propertyResponse.appConfig.mapProjection,
              viewExtent: viewResponse.extent ?? [],
              viewId: viewResponse.viewId ?? '',
              mapZoomFactor: propertyResponse.appConfig.mapZoomFactor ?? '',
              scenes: themeResponse.scenes,
              layerData,
              solutionDate: viewResponse.startDate ?? date,
            };
            return solutionData;
          }
          return undefined;
        },
      ),
    );
  };
}

export const getSolutionData = createCommonDataGetter(
  MAP_VIEW_NAME_SOLUTION,
  SCENE_ID_TYPE_SOLUTION,
);
