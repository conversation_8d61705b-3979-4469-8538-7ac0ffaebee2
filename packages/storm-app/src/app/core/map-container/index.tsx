/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MAP_VIEW_NAME_ONLINE,
  MapViewName,
  MapViewType,
} from '@waterdesk/data/const/map';
import { FeatureCode } from '@waterdesk/data/system-feature';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MapView from 'src/app/core/map-view/map-view';
import useFeatureFlags from 'src/app/hooks/use-feature-flags';
import { hostApp } from 'src/app/host-app';
import { baseActions } from 'src/store/base';
import { selectViewId } from 'src/store/base/selectors';
import { selectTheme } from 'src/store/theme/selector';
import BlinkOverlayManager from '../map-view/blink-overlay-manager';
import MapViewWrapper from './style';

interface Props {
  mapName: MapViewName;
  mapType: MapViewType;
}

export default function MapContainer(props: Props) {
  const { mapName, mapType } = props;

  const dispatch = useDispatch();
  const mapRef = useRef(null);
  const theme = useSelector(selectTheme);
  const viewId = useSelector(selectViewId);
  const mapViewRef = useRef<MapView | undefined>(undefined);
  const [mapView, setMapView] = useState<MapView | undefined>(undefined);

  const { state: hasQuickMapMenu } = useFeatureFlags(
    FeatureCode.QUICK_MAP_MENU,
  );

  useEffect(() => {
    const mapView = new MapView({
      mapViewName: mapName,
      mapViewType: mapType,
      target: mapRef.current ?? '',
      dispatch,
    });
    setMapView(mapView);
    mapViewRef.current = mapView;
    hostApp().addMapView(mapName, mapView);
    return () => {
      dispatch(baseActions.resetStateSaga());
      hostApp().removeMapView(mapName);
    };
  }, []);

  useEffect(() => {
    dispatch(
      baseActions.initializeOnLineDataBase({
        theme,
        sectionId: MAP_VIEW_NAME_ONLINE,
        hasContextMenu: hasQuickMapMenu,
      }),
    );
    mapViewRef.current?.setTheme(theme);
  }, [theme, hasQuickMapMenu]);

  useEffect(() => {
    if (viewId !== undefined) mapViewRef.current?.setViewId(viewId);
  }, [viewId]);

  return (
    <>
      <MapViewWrapper ref={mapRef} />
      {mapView?.map ? <BlinkOverlayManager map={mapView?.map} /> : null}
    </>
  );
}
