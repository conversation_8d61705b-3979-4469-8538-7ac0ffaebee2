/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MAP_VIEW_TYPE_SOLUTION, MapViewName } from '@waterdesk/data/const/map';
import {
  getDefaultSceneId,
  getInvisibleLayerNames,
  getScene,
  refreshLayerData,
} from '@waterdesk/data/scene';
import { SolutionBaseInfo } from '@waterdesk/data/solution-detail';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MapView from 'src/app/core/map-view/map-view';
import { hostApp } from 'src/app/host-app';
import { baseActions, useBaseSlice } from 'src/store/base';
import { selectViewId } from 'src/store/base/selectors';
import {
  editSelectionActions,
  useEditSelectionSlice,
} from 'src/store/edit-selection';
import {
  leftWrapperActions,
  useLeftWrapperSlice,
} from 'src/store/left-wrapper';
import { scenesActions, useScenesSlice } from 'src/store/scenes';
import { selectTheme } from 'src/store/theme/selector';
import { useTimelineSlice } from 'src/store/time-line';
import { getSolutionData } from './get-solution-data';
import MapViewWrapper, { SolutionNameTag } from './style';

interface Props {
  mapName: MapViewName;
  solutionId: string | undefined;
  solutionBaseInfo: SolutionBaseInfo;
}

export default function SolutionMapContainer(props: Props) {
  useBaseSlice();
  useScenesSlice();
  useLeftWrapperSlice();
  useEditSelectionSlice();

  const { mapName, solutionId, solutionBaseInfo } = props;

  const dispatch = useDispatch();
  const mapRef = useRef<HTMLDivElement>(null);
  const theme = useSelector(selectTheme);
  const viewId = useSelector(selectViewId);
  const mapViewRef = useRef<MapView | undefined>(undefined);

  const { actions } = useTimelineSlice();

  const resetSolution = () => {
    if (mapRef.current) {
      mapRef.current.innerHTML = '';
      hostApp().resetMapView();
    }
  };
  useEffect(() => {
    if (solutionId) {
      resetSolution();

      const mapView = new MapView({
        dispatch,
        mapViewName: mapName,
        mapViewType: MAP_VIEW_TYPE_SOLUTION,
        target: mapRef.current ?? '',
      });
      mapViewRef.current = mapView;
      hostApp().addMapView(mapName, mapView);

      getSolutionData(
        solutionId,
        mapView,
        theme,
        undefined,
        solutionBaseInfo.createByMyself,
      ).then((res) => {
        if (res) {
          dispatch(
            baseActions.updateViewId({
              viewId: res.viewId ?? '',
            }),
          );

          if (res.scenes) {
            const defaultSceneId = getDefaultSceneId(res.scenes);
            dispatch(
              scenesActions.initializeScenes({
                scenes: refreshLayerData(res.scenes, res.layerData),
              }),
            );
            dispatch(
              scenesActions.updateCurrentSceneId({
                sceneId: defaultSceneId,
              }),
            );
            // dispatch(
            //   leftWrapperActions.leftWrapperContainerTypeChanged({
            //     containerType: MENU_TOOLS_SCENE,
            //   }),
            // );
            dispatch(leftWrapperActions.leftWrapperChanged({ open: true }));

            const currentScene = getScene(res.scenes, defaultSceneId);
            const invisibleLayers = getInvisibleLayerNames(currentScene);

            mapView.initialize(
              res.viewId,
              res.viewExtent,
              invisibleLayers,
              theme,
              res.projection,
              res.mapZoomFactor,
            );
          }

          dispatch(
            actions.updateTimelineDate({
              timelineDate: dayjs(res.solutionDate).format('YYYY-MM-DD'),
            }),
          );

          dispatch(
            actions.updateTimelineTime({
              timelineTime: getValueFromDateTime(dayjs(res.solutionDate)),
            }),
          );

          // solution data is initialzed
          dispatch(baseActions.updateDataInitialComplete());
        }
      });
    }
  }, [solutionId]);

  useEffect(() => {
    mapViewRef.current?.setTheme(theme);
  }, [theme]);

  useEffect(() => {
    const oldMapName = mapViewRef.current?.mapViewName;
    if (oldMapName === mapName) return;
    mapViewRef.current?.setMapViewName(mapName);
    if (mapViewRef.current && oldMapName && hostApp().getMapView(oldMapName)) {
      hostApp().removeMapView(oldMapName);
      hostApp().addMapView(mapName, mapViewRef.current);
    }
  }, [mapName]);

  useEffect(() => {
    if (viewId !== undefined) mapViewRef.current?.setViewId(viewId);
  }, [viewId]);

  useEffect(
    () => () => {
      dispatch(baseActions.resetStateSaga());
      dispatch(editSelectionActions.clearEdit());
      hostApp().removeMapView(mapName);
    },
    [],
  );

  return (
    <>
      <MapViewWrapper ref={mapRef} />
      <SolutionNameTag
        title={solutionBaseInfo.name ?? ''}
        color="#55acee"
      >
        {solutionBaseInfo.name ?? ''}
      </SolutionNameTag>
    </>
  );
}
