/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HighlightObject } from '@waterdesk/data/highlight-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  DEVICE_RELATION,
  getSelectColor,
  HIGHLIGHT_CUSTOM,
  HIGHLIGHT_POLLUTED_TRACK_SELECT,
  HIGHLIGHT_SELECT,
  HIGHLIGHT_TRACK_SELECT,
  HIGHLIGHT_UNPOLLUTED_TRACK_SELECT,
  HighlightStyle,
  HighlightStyleType,
  StyleConfig,
} from '@waterdesk/data/style-config';
import Feature from 'ol/Feature';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point';
import { Circle, Fill, Stroke, Style, Text } from 'ol/style';
import { StyleLike } from 'ol/style/Style';

export default class FeatureStyle {
  private _featureType: HighlightStyleType = HIGHLIGHT_SELECT;

  private _styleMap: HighlightStyle = new Map();

  private _defaultStyle: StyleConfig = {
    pointColor: '#ff0000',
    lineColor: '#ff0000',
    polygonColor: '#ff0000',
    polygonStrokeColor: '#ff000000',
    fontSize: 32,
  };

  constructor(type: HighlightStyleType, styles: HighlightStyle) {
    this._featureType = type;
    this._styleMap = styles;
  }

  private getStyleConfig(
    highlightObject: HighlightObject | IObjectItem,
  ): StyleConfig {
    const { highlightType, highlightColor, highlightSize, highlightLineWidth } =
      highlightObject;
    if (highlightType === HIGHLIGHT_CUSTOM) {
      return {
        pointColor: highlightColor ?? '#ff0000',
        lineColor: highlightColor ?? '#ff0000',
        polygonColor: highlightColor ?? '#ff0000',
        polygonStrokeColor: highlightColor ?? '#ff0000',
        lineWidth: highlightLineWidth,
        fontSize: highlightSize ?? 32,
      };
    }
    const style = this._styleMap.get(highlightType as HighlightStyleType);
    if (style) {
      return style;
    }
    return this._defaultStyle;
  }

  private generatePointStyle(
    highlightObject: HighlightObject | IObjectItem,
  ): Style | Style[] {
    const { highlightType, highlightText, highlightTextBgColor } =
      highlightObject;
    const styleConfig = this.getStyleConfig({
      ...highlightObject,
      highlightType: highlightType ?? this._featureType,
    });

    const styles = [
      new Style({
        image: new Circle({
          radius: 6,
          fill: new Fill({
            color: styleConfig.pointColor,
          }),
        }),
        stroke: new Stroke({
          color: '#ff0000',
          width: 3,
        }),
        zIndex: 9999,
      }),
    ];

    if (highlightText) {
      styles.push(
        new Style({
          text: new Text({
            font: '14px Calibri,sans-serif',
            fill: new Fill({
              color: '#000000',
            }),
            stroke: new Stroke({
              color: '#ffffff',
            }),
            backgroundFill: new Fill({
              color: highlightTextBgColor,
            }),
            text: highlightText,
            offsetY: 20,
          }),
          zIndex: 9999,
        }),
      );
    }
    return styles;
  }

  private generatePointIconStyle(
    highlightObject: HighlightObject | IObjectItem,
  ): Style | Style[] {
    const {
      highlightIcon,
      highlightText,
      highlightType,
      highlightColor,
      highlightTextBgColor,
    } = highlightObject;
    const styleConfig = this.getStyleConfig({
      ...highlightObject,
      highlightType: highlightType ?? this._featureType,
    });
    const { fontSize, pointColor } = styleConfig;
    const styles = [
      new Style({
        fill: new Fill({
          color: '#ffffff',
        }),
        image: new Circle({
          fill: new Fill({
            color: '#ffffff',
          }),
          radius: fontSize * 0.375,
        }),
        text: new Text({
          font: `${fontSize}px iconfont`,
          // eslint-disable-next-line no-eval
          text: eval(`'${highlightIcon}'`),
          fill: new Fill({
            color: highlightColor || pointColor,
          }),
        }),
        zIndex: 9999,
      }),
    ];
    if (highlightText) {
      styles.push(
        new Style({
          text: new Text({
            font: '14px Calibri,sans-serif',
            fill: new Fill({
              color: '#000000',
            }),
            stroke: new Stroke({
              color: '#ffffff',
            }),
            backgroundFill: new Fill({
              color: highlightTextBgColor,
            }),
            text: highlightText,
            offsetY: 20,
          }),
          zIndex: 9999,
        }),
      );
    }
    return styles;
  }

  private generateLineStyle(
    highlightObject: HighlightObject | IObjectItem,
  ): Style[] {
    const {
      highlightType,
      highlightText,
      highlightIcon,
      highlightColor,
      lineType = 'default',
    } = highlightObject;

    const styleConfig = this.getStyleConfig({
      ...highlightObject,
      highlightType: highlightType ?? this._featureType,
    });

    const { fontSize, lineColor, lineWidth = 3 } = styleConfig;
    const styles: Style[] = [
      new Style({
        stroke: new Stroke({
          color: lineColor,
          width: lineWidth,
          lineDash: lineType === 'dash' ? [10, 10] : undefined,
        }),
        fill: new Fill({
          color: lineColor,
        }),
        text: new Text({
          font: '14px Calibri,sans-serif',
          fill: new Fill({
            color: '#000000',
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 3,
          }),
          text: highlightText,
          placement: 'line',
          textBaseline: 'bottom',
        }),
        zIndex: 9999,
      }),
    ];
    if (
      highlightType === HIGHLIGHT_TRACK_SELECT ||
      highlightType === HIGHLIGHT_POLLUTED_TRACK_SELECT ||
      highlightType === HIGHLIGHT_UNPOLLUTED_TRACK_SELECT
    ) {
      styles.push(
        new Style({
          text: new Text({
            font: '30px iconfont',
            fill: new Fill({
              color: getSelectColor(highlightType),
            }),
            text: highlightText,
            offsetY: -12,
          }),
        }),
      );
    }

    if (highlightIcon) {
      styles.push(
        new Style({
          fill: new Fill({
            color: '#ffffff',
          }),
          image: new Circle({
            fill: new Fill({
              color: '#ffffff',
            }),
            radius: fontSize * 0.375,
          }),
          text: new Text({
            font: `${fontSize}px iconfont`,
            fill: new Fill({
              color: highlightColor || lineColor,
            }),
            // eslint-disable-next-line no-eval
            text: eval(`'${highlightIcon}'`),
            textBaseline: 'bottom',
          }),
          zIndex: 9999,
        }),
      );
    }
    return styles;
  }

  private generatePolygonStyle(
    highlightObject: HighlightObject | IObjectItem,
  ): Style {
    const { highlightType, highlightText } = highlightObject;
    const styleConfig = this.getStyleConfig({
      ...highlightObject,
      highlightType: highlightType ?? this._featureType,
    });
    const { polygonStrokeColor, polygonColor } = styleConfig;
    return new Style({
      stroke: polygonStrokeColor
        ? new Stroke({
            color: polygonStrokeColor,
            width: 3,
          })
        : undefined,
      fill: new Fill({
        color: polygonColor,
      }),
      text: new Text({
        font: '16px Calibri,sans-serif',
        fill: new Fill({
          color: '#000000',
        }),
        backgroundFill: new Fill({
          color: '#ffffff',
        }),
        text: highlightText,
        offsetY: 10,
      }),
      zIndex: 9999,
    });
  }

  static deviceRelationStyle(feature: Feature, resolution: number): Style[] {
    const geometry = feature.getGeometry() as LineString;
    const { highlightColor } = feature.getProperties();
    const length = geometry.getLength();
    const radio = (100 * resolution) / length;
    const styles = [
      new Style({
        stroke: new Stroke({
          color: highlightColor,
          width: 3,
        }),
      }),
    ];
    for (let i = 0; i <= 1; i += radio) {
      const arrowLocation = geometry.getCoordinateAt(i);
      geometry.forEachSegment((start, end) => {
        if (start[0] === end[0] || start[1] === end[1]) return;
        const dx1 = end[0] - arrowLocation[0];
        const dy1 = end[1] - arrowLocation[1];
        const dx2 = arrowLocation[0] - start[0];
        const dy2 = arrowLocation[1] - start[1];
        if (dx1 !== dx2 && dy1 !== dy2) {
          const dx = end[0] - start[0];
          const dy = end[1] - start[1];
          const rotation = Math.atan2(dy, dx);
          styles.push(
            new Style({
              geometry: new Point(arrowLocation),
              text: new Text({
                font: 'bolder 16px  iconfont',
                fill: new Fill({
                  color: '#000000',
                }),
                text: '\ue67f',
                rotation: -rotation + Math.PI,
              }),
            }),
          );
        }
      });
    }
    return styles;
  }

  generateObjectStyle(
    highlightObject: HighlightObject | IObjectItem,
  ): StyleLike | undefined {
    if (highlightObject.shapeType === 'POINT') {
      if (highlightObject.highlightIcon && highlightObject.highlightIcon !== '')
        return this.generatePointIconStyle(highlightObject);
      return this.generatePointStyle(highlightObject);
    }
    if (highlightObject.shapeType === 'LINE') {
      if (highlightObject.highlightType === DEVICE_RELATION) {
        return FeatureStyle.deviceRelationStyle as StyleLike;
      }
      return this.generateLineStyle(highlightObject);
    }
    if (highlightObject.shapeType === 'POLYGON') {
      return this.generatePolygonStyle(highlightObject);
    }
    return undefined;
  }
}
