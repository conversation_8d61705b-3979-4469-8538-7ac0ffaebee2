/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import BaseLayer from 'ol/layer/Base';
import Tile from 'ol/layer/Tile';
import TileImage from 'ol/source/TileImage';
import TileGrid from 'ol/tilegrid/TileGrid';
import { IMapLayer } from './map-layer';

export default class MapBaiduLayer implements IMapLayer {
  _theme: string = 'white';

  private _name: string;

  _layer: Tile<any> | null = null;

  constructor() {
    this._name = 'Baidu';
    this.initialize();
  }

  private initialize(): void {
    const resolutions: number[] = [];
    for (let i = 0; i < 19; i += 1) {
      resolutions[i] = 2 ** (18 - i);
    }
    const tilegrid = new TileGrid({
      origin: [0, 0],
      resolutions,
    });
    const curThemes = this._theme;

    const tileImage: TileImage = new TileImage({
      projection: 'EPSG:3857',
      tileGrid: tilegrid,
      tileUrlFunction(tileCoord) {
        if (!tileCoord) {
          return '';
        }
        const z = tileCoord[0];
        const x = tileCoord[1];
        let y = tileCoord[2] + 1;
        let newX: string;
        let newY: string;
        if (x < 0) {
          newX = `M${(-x).toString()}`;
        } else {
          newX = x.toString();
        }
        y = -y;
        if (y < 0) {
          newY = `M${(-y).toString()}`;
        } else {
          newY = y.toString();
        }

        let mapUrl = `https://api.map.baidu.com/customimage/tile?&x=${newX}&y=${newY}&z=${z}&udt=20181205&scale=1&ak=1XjLLEhZhQNUzd93EjU5nOGQ&styles=t%3Aland%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23091220ff%2Ct%3Awater%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23113549ff%2Ct%3Agreen%7Ce%3Ag%7Cv%3Aon%7Cc%3A%230e1b30ff%2Ct%3Abuilding%7Ce%3Ag%7Cv%3Aon%2Ct%3Abuilding%7Ce%3Ag.f%7Cc%3A%23113549ff%2Ct%3Abuilding%7Ce%3Ag.s%7Cc%3A%23dadada00%2Ct%3Asubwaystation%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23113549B2%2Ct%3Aeducation%7Ce%3Ag%7Cv%3Aoff%7Cc%3A%2312223dff%2Ct%3Aeducationlabel%7Ce%3Al%7Cv%3Aoff%2Ct%3Amedical%7Ce%3Ag%7Cv%3Aon%7Cc%3A%2312223dff%2Ct%3Ascenicspots%7Ce%3Ag%7Cv%3Aon%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Ag%7Cv%3Aon%7Cw%3A4%2Ct%3Ahighway%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Ag.s%7Cc%3A%23fed66900%2Ct%3Ahighway%7Ce%3Al%7Cv%3Aon%2Ct%3Ahighway%7Ce%3Al.t.f%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Ahighway%7Ce%3Al.i%7Cv%3Aon%2Ct%3Aarterial%7Ce%3Ag%7Cv%3Aon%7Cw%3A2%2Ct%3Aarterial%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Aarterial%7Ce%3Ag.s%7Cc%3A%23ffeebb00%2Ct%3Aarterial%7Ce%3Al%7Cv%3Aon%2Ct%3Aarterial%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Aarterial%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Alocal%7Ce%3Ag%7Cv%3Aon%7Cw%3A1%2Ct%3Alocal%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Alocal%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Alocal%7Ce%3Al%7Cv%3Aon%2Ct%3Alocal%7Ce%3Al.t.f%7Cc%3A%23979c9aff%2Ct%3Alocal%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Arailway%7Ce%3Ag%7Cv%3Aoff%7Cw%3A1%2Ct%3Arailway%7Ce%3Ag.f%7Cc%3A%23123c52ff%2Ct%3Arailway%7Ce%3Aall%7Cl%3A-40%2Ct%3Aboundary%7Ce%3Ag%7Cc%3A%23000000%7Cl%3A-29%7Cw%3A1g.s%7Cc%3A%2312223dff%2Ct%3Asubway%7Ce%3Ag%7Cv%3Aoff%7Cw%3A1%2Ct%3Asubway%7Ce%3Ag.f%7Cc%3A%23d8d8d8ff%2Ct%3Asubway%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Asubway%7Ce%3Al%7Cv%3Aon%2Ct%3Asubway%7Ce%3Al.t.f%7Cc%3A%23979c9aff%2Ct%3Asubway%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Acontinent%7Ce%3Al%7Cv%3Aon%2Ct%3Acontinent%7Ce%3Al.i%7Cv%3Aon%2Ct%3Acontinent%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Acontinent%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Acity%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Acity%7Ce%3Al%7Cv%3Aon%2Ct%3Acity%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Acity%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Atown%7Ce%3Al.i%7Cv%3Aon%2Ct%3Atown%7Ce%3Al%7Cv%3Aoff%2Ct%3Atown%7Ce%3Al.t.f%7Cc%3A%23454d50ff%2Ct%3Atown%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Aroad%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Apoi%7Ce%3Al%7Cv%3Aon%2Ct%3Alabel%7Ce%3Al%7Cv%3Aoff%2Ct%3Aroad%7Ce%3Ag%7Cv%3Aon%2Ct%3Aroad%7Ce%3Al%7Cv%3Aoff%2Ct%3Aroad%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Adistrict%7Ce%3Al%7Cv%3Aon%2Ct%3Apoi%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Apoi%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Apoi%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Amanmade%7Ce%3Ag%7Cc%3A%2312223dff%2Ct%3Alabel%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Aentertainment%7Ce%3Ag%7Cc%3A%2312223dff%2Ct%3Ashopping%7Ce%3Ag%7Cv%3Aoff%7Cc%3A%2312223dff%2Ct%3Ashoppinglabel%7Ce%3Al.t.f%7Cv%3Aoff%7Cc%3A%232dc4bbff%2Ct%3Ashoppinglabel%7Ce%3Al.t.s%7Cv%3Aoff%7Cc%3A%23ffffff0%2Ct%3Ashoppinglabel%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Ahighway%7Ce%3Ag%7Cv%3Aoff%2Ct%3Ahighway%7Ce%3Al%7Cv%3Aon`;
        if (curThemes === 'white') {
          mapUrl = `http://online0.map.bdimg.com/tile/?qt=tile&x=${newX}&y=${newY}&z=${z}&styles=pl&v=017&udt=20180712`;
        } else if (curThemes === 'dark') {
          mapUrl = `https://api.map.baidu.com/customimage/tile?&x=${newX}&y=${newY}&z=${z}&udt=20181205&scale=1&ak=1XjLLEhZhQNUzd93EjU5nOGQ&styles=t%3Aland%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23091220ff%2Ct%3Awater%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23113549ff%2Ct%3Agreen%7Ce%3Ag%7Cv%3Aon%7Cc%3A%230e1b30ff%2Ct%3Abuilding%7Ce%3Ag%7Cv%3Aon%2Ct%3Abuilding%7Ce%3Ag.f%7Cc%3A%23113549ff%2Ct%3Abuilding%7Ce%3Ag.s%7Cc%3A%23dadada00%2Ct%3Asubwaystation%7Ce%3Ag%7Cv%3Aon%7Cc%3A%23113549B2%2Ct%3Aeducation%7Ce%3Ag%7Cv%3Aoff%7Cc%3A%2312223dff%2Ct%3Aeducationlabel%7Ce%3Al%7Cv%3Aoff%2Ct%3Amedical%7Ce%3Ag%7Cv%3Aon%7Cc%3A%2312223dff%2Ct%3Ascenicspots%7Ce%3Ag%7Cv%3Aon%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Ag%7Cv%3Aon%7Cw%3A4%2Ct%3Ahighway%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Ag.s%7Cc%3A%23fed66900%2Ct%3Ahighway%7Ce%3Al%7Cv%3Aon%2Ct%3Ahighway%7Ce%3Al.t.f%7Cc%3A%2312223dff%2Ct%3Ahighway%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Ahighway%7Ce%3Al.i%7Cv%3Aon%2Ct%3Aarterial%7Ce%3Ag%7Cv%3Aon%7Cw%3A2%2Ct%3Aarterial%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Aarterial%7Ce%3Ag.s%7Cc%3A%23ffeebb00%2Ct%3Aarterial%7Ce%3Al%7Cv%3Aon%2Ct%3Aarterial%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Aarterial%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Alocal%7Ce%3Ag%7Cv%3Aon%7Cw%3A1%2Ct%3Alocal%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Alocal%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Alocal%7Ce%3Al%7Cv%3Aon%2Ct%3Alocal%7Ce%3Al.t.f%7Cc%3A%23979c9aff%2Ct%3Alocal%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Arailway%7Ce%3Ag%7Cv%3Aoff%7Cw%3A1%2Ct%3Arailway%7Ce%3Ag.f%7Cc%3A%23123c52ff%2Ct%3Arailway%7Ce%3Aall%7Cl%3A-40%2Ct%3Aboundary%7Ce%3Ag%7Cc%3A%23000000%7Cl%3A-29%7Cw%3A1g.s%7Cc%3A%2312223dff%2Ct%3Asubway%7Ce%3Ag%7Cv%3Aoff%7Cw%3A1%2Ct%3Asubway%7Ce%3Ag.f%7Cc%3A%23d8d8d8ff%2Ct%3Asubway%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Asubway%7Ce%3Al%7Cv%3Aon%2Ct%3Asubway%7Ce%3Al.t.f%7Cc%3A%23979c9aff%2Ct%3Asubway%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Acontinent%7Ce%3Al%7Cv%3Aon%2Ct%3Acontinent%7Ce%3Al.i%7Cv%3Aon%2Ct%3Acontinent%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Acontinent%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Acity%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Acity%7Ce%3Al%7Cv%3Aon%2Ct%3Acity%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Acity%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Atown%7Ce%3Al.i%7Cv%3Aon%2Ct%3Atown%7Ce%3Al%7Cv%3Aoff%2Ct%3Atown%7Ce%3Al.t.f%7Cc%3A%23454d50ff%2Ct%3Atown%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Aroad%7Ce%3Ag.f%7Cc%3A%2312223dff%2Ct%3Apoi%7Ce%3Al%7Cv%3Aon%2Ct%3Alabel%7Ce%3Al%7Cv%3Aoff%2Ct%3Aroad%7Ce%3Ag%7Cv%3Aon%2Ct%3Aroad%7Ce%3Ag.s%7Cc%3A%23ffffff00%2Ct%3Adistrict%7Ce%3Al%7Cv%3Aon%2Ct%3Apoi%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Apoi%7Ce%3Al.t.f%7Cc%3A%232dc4bbff%2Ct%3Apoi%7Ce%3Al.t.s%7Cc%3A%23ffffff00%2Ct%3Amanmade%7Ce%3Ag%7Cc%3A%2312223dff%2Ct%3Alabel%7Ce%3Al.t.s%7Cc%3A%23ffffffff%2Ct%3Aentertainment%7Ce%3Ag%7Cc%3A%2312223dff%2Ct%3Ashopping%7Ce%3Ag%7Cv%3Aoff%7Cc%3A%2312223dff%2Ct%3Ashoppinglabel%7Ce%3Al.t.f%7Cv%3Aoff%7Cc%3A%232dc4bbff%2Ct%3Ashoppinglabel%7Ce%3Al.t.s%7Cv%3Aoff%7Cc%3A%23ffffff0%2Ct%3Ashoppinglabel%7Ce%3Al.i%7Cv%3Aoff%2Ct%3Ahighway%7Ce%3Ag%7Cv%3Aoff%2Ct%3Ahighway%7Ce%3Al%7Cv%3Aon`;
        }
        return mapUrl;
      },
    });

    this._layer = new Tile({
      opacity: 0.5,
      source: tileImage,
      zIndex: -1,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  // eslint-disable-next-line class-methods-use-this
  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  setTheme(_theme: Theme) {}

  dispose(): void {
    if (this._layer) {
      // 移除图层的source
      const source = this._layer.getSource();
      if (source) {
        source.dispose();
      }
      // 移除图层本身
      this._layer.dispose();
      this._layer = null;
    }
  }
}
