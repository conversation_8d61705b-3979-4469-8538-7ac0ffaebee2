/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import TraceNetwork, { TracePath } from '@waterdesk/data/trace/trace-network';
import { LinesSeriesOption } from 'echarts';
import * as echarts from 'echarts/core';
import { default as OlMap } from 'ol/Map';
import EChartsLayer from 'src/components/ol-echarts';

export default class MapEchartsLinesLayer {
  private _map: OlMap;

  private _name: string;

  private _layer: EChartsLayer | null = null;

  private _currentArrayTracePath: TracePath[] = [];

  private highlightObjectsMap: { [key: string]: HighlightObject } = {};

  private option: echarts.ComposeOption<LinesSeriesOption> = {};

  constructor(map: OlMap) {
    this._name = 'echartsLines';
    this._map = map;
    if (map.getTarget()) {
      this._layer = new EChartsLayer(
        this.option,
        {
          hideOnMoving: true,
          hideOnZooming: true,
          forcedPrecomposeRerender: false,
          source: map.getView().getProjection().getCode(),
        },
        map,
      );
      this._layer.setZIndex(-1);
    } else {
      console.log('no Target!');
    }
  }

  get name(): string {
    return this._name;
  }

  get layer(): EChartsLayer | null {
    return this._layer;
  }

  drawTracePath(zoomFactor?: number) {
    const series: echarts.ComposeOption<LinesSeriesOption>['series'] = [];
    this._currentArrayTracePath.forEach((item) => {
      const lines = item.coordinates.map((item) => item.split(' ').map(Number));
      if (!lines || lines.length < 2) {
        console.warn('Invalid coordinates for item:', item);
        return;
      }
      const pipe = item.pipes[0];
      const currentHighlightObject = this.highlightObjectsMap[pipe.id];
      const serie: echarts.ComposeOption<LinesSeriesOption>['series'] = [
        {
          type: 'lines',
          polyline: true,
          silent: true,
          data: [{ coords: lines }],
          lineStyle: {
            color: currentHighlightObject.highlightColor,
            opacity: 0.2,
            width: 10,
          },
          progressiveThreshold: 500,
          progressive: 200,
        },
        {
          type: 'lines',
          polyline: true,
          data: [{ coords: lines }],
          lineStyle: {
            width: 0,
            color: currentHighlightObject.highlightSecondColor,
          },
          effect: {
            symbol: 'rect',
            constantSpeed: Math.max(item.velocity * 50, 30),
            show: true,
            trailLength: 0.85,
            symbolSize: [
              (currentHighlightObject.highlightSize ?? 0) +
                (item.diameter ?? 0) / 200 -
                (zoomFactor ?? 0),
              0.8,
            ],
          },
          zlevel: 1,
        },
      ];
      series.push(...serie);
    });

    this.setVisible(true);
    this._layer?.setChartOptions({
      series,
    });
  }

  addHighlightObject(highlightObjects: HighlightObject[]) {
    this.clearHighlightObject();
    highlightObjects.forEach((i) => {
      this.highlightObjectsMap[i.id] = i;
    });
    const traceValues = new TraceNetwork();
    traceValues.initialize(highlightObjects);
    traceValues.tryMergePath();

    this._currentArrayTracePath = traceValues.arrayTracePath;
    this.drawTracePath();
  }

  clearHighlightObject() {
    this._currentArrayTracePath = [];
    this.setVisible(false);
    this._layer?.setChartOptions({
      series: [],
    });
  }

  redraw(): void {
    this._layer?.onMoveEnd();
  }

  setVisible(visible: boolean) {
    if (visible) {
      this._layer?.show();
    } else {
      this._layer?.hide();
    }
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
  setTheme(_theme: Theme) {}
}
