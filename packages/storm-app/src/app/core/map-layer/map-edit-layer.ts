/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { validateShape } from '@waterdesk/data/object-item';
import { findAnyElementByPos } from '@waterdesk/request/find-any-element';
import { Feature, MapBrowserEvent } from 'ol';
import { Coordinate } from 'ol/coordinate';
import WKT from 'ol/format/WKT';
import { SimpleGeometry } from 'ol/geom';
import { Draw, Select, Snap } from 'ol/interaction';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import { Circle, Fill, RegularShape, Stroke, Style } from 'ol/style';
import { curDb } from 'src/app/host-app';
import store from 'src/store/configure-store';
import { editSelectionActions } from 'src/store/edit-selection';
import { AddJunctionType, AddPipeType } from 'src/store/edit-selection/types';
import { IMapLayer } from './map-layer';

export enum EditType {
  SINGLELINESTRING = 'SingleLineString',
  DELETEPIPE = 'DeletePipe',
  LINESTRING = 'LineString',
  POINT = 'Point',
}

export type DrawType = EditType.LINESTRING | EditType.POINT;
interface LinePointType {
  type?: 'newPoint' | undefined;
  oname: string;
  otype: string;
  shape?: string | undefined;
  pipe?:
    | {
        otype: string;
        oname: string;
      }
    | undefined;
}

class CustomSnap extends Snap {
  _wkt = new WKT();

  openSnap = false;

  positionRange: number[] | null = null;

  _source: VectorSource;

  _map: OlMap;

  _style: Style = new Style({
    stroke: new Stroke({
      color: '#00000005',
    }),
    fill: new Fill({
      color: '#2e74f3',
    }),
    image: new Circle({
      radius: 4,
      fill: new Fill({
        color: '#2e74f3',
      }),
    }),
  });

  constructor(params: { source: VectorSource }, map: OlMap) {
    super(params);
    this._source = params.source;
    this._map = map;
  }

  handleEvent(event: MapBrowserEvent<PointerEvent>): any {
    const mapPosition = event.coordinate;
    if (this.positionRange != null) {
      if (
        this.positionRange[0] < mapPosition[0] &&
        mapPosition[0] < this.positionRange[2] &&
        this.positionRange[1] < mapPosition[1] &&
        mapPosition[1] < this.positionRange[3]
      ) {
        return super.handleEvent(event);
      }
      this.positionRange = null;
    }
    return super.handleEvent(event);
  }

  handleMoveEvent(event: MapBrowserEvent<PointerEvent>): any {
    const mapPosition = event.coordinate;
    if (this.openSnap) {
      if (this.positionRange != null) return;
      const minPos = this._map.getCoordinateFromPixel([0, 0]);
      const maxPos = this._map.getCoordinateFromPixel([50, 50]);
      const dist = maxPos[0] - minPos[0];

      this.openSnap = false;

      findAnyElementByPos(
        mapPosition[0],
        mapPosition[1],
        dist,
        curDb(),
        300,
        'WDM_JUNCTIONS,WDM_JUNCTIONS_DMA,WDM_PIPES,WDM_PIPES_DMA,WDM_MODELPIPES,WDM_ENDPOINT,WDM_MODELNODE,WDM_HYDRANT',
      ).then((res) => {
        this.openSnap = true;
        this._source.clear();
        if (res.status === 'Fail') {
          return;
        }
        const modelObjects = res.foundObjects;
        modelObjects?.forEach((object) => {
          if (validateShape(object.shape)) {
            const feature = this._wkt.readFeature(object.shape);
            feature.setProperties({
              oname: object.oname,
              otype: object.otype,
            });
            feature.setStyle(this._style);
            this._source.addFeature(feature);
          }
        });

        const halfDist = dist / 2;
        this.positionRange = [
          mapPosition[0] - halfDist,
          mapPosition[1] - halfDist,
          mapPosition[0] + halfDist,
          mapPosition[1] + halfDist,
        ];
      });
    }
  }
}

export default class MapEditLayer implements IMapLayer {
  private _map: OlMap;

  private _name: string;

  private _layer: VectorLayer;

  private _source: VectorSource;

  private _draw: Draw;

  private _drawType: DrawType = EditType.POINT;

  private _snap: CustomSnap;

  private _select: Select;

  private _tempPoint: Coordinate = [];

  private _lineStartPoint: LinePointType | undefined = undefined;

  private _lineEndPoint: LinePointType | undefined = undefined;

  private _tempLineString: Feature | undefined;

  static breakLineStyle() {
    return new Style({
      image: new RegularShape({
        points: 4,
        radius: 12,
        radius2: 0,
        angle: Math.PI / 4,
        stroke: new Stroke({
          color: '#FF0000',
          width: 2,
        }),
        fill: new Fill({
          color: '#FF0000',
        }),
      }),
      stroke: new Stroke({
        color: '#03a2d6',
        width: 2,
      }),
    });
  }

  constructor(map: OlMap) {
    this._name = 'select';
    this._map = map;
    this._source = new VectorSource({});
    this._layer = new VectorLayer({
      source: this._source,
    });
    this._draw = new Draw({
      source: this._source,
      type: this._drawType,
    });
    this._snap = new CustomSnap({ source: this._source }, map);
    this._map.on('pointermove', (event) => {
      const { pixel } = event;
      const snapped = this._snap.snapTo(pixel, event.coordinate, map);
      if (snapped?.vertex) {
        const features = this._source.getFeaturesAtCoordinate(snapped.vertex);
        if (features.length > 0) {
          const properties = features[0].getProperties();
          if (properties.otype === 'WDM_PIPES') {
            this._draw.getOverlay().setStyle(MapEditLayer.breakLineStyle());
            return;
          }
        }
      }
      this._draw.getOverlay().setStyle();
    });

    this._select = new Select();
    this._select.on('select', (event) => {
      const features = event.selected;
      if (features.length > 0) {
        const pipes: { oname: string; otype: string }[] = [];
        features.forEach((feature) => {
          const property = feature.getProperties();
          if (property.otype === 'WDM_PIPES') {
            pipes.push({
              oname: property.oname,
              otype: property.otype,
            });
          }
        });
        store.dispatch(
          editSelectionActions.deletePipeChanged({
            deletePipe: pipes,
          }),
        );
      }
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  setDrawType(drawType: DrawType) {
    this._drawType = drawType;
    this._map.removeInteraction(this._draw);
    this._draw = new Draw({
      source: this._source,
      type: this._drawType,
    });
  }

  static convertToPoint(coordinates: number[] | null): string | undefined {
    if (
      coordinates &&
      typeof coordinates[0] === 'number' &&
      typeof coordinates[1] === 'number'
    ) {
      return `POINT(${coordinates[0]} ${coordinates[1]})`;
    }
    return undefined;
  }

  static convertToLine(coordinates: number[][] | null): string | undefined {
    if (coordinates) {
      let paths = '';
      coordinates.forEach((item) => {
        if (Array.isArray(item) && item.length === 2) {
          if (paths.length !== 0) paths += ',';
          paths += `${item[0]} ${item[1]}`;
        }
      });
      return `LINESTRING(${paths})`;
    }

    return undefined;
  }

  static getPoint(features: Feature[]): AddJunctionType | undefined {
    if (features.length === 1) {
      const newPoint = features[0];
      return {
        type: 'ADDJUNCTION',
        junction: {
          shape:
            MapEditLayer.convertToPoint(
              (newPoint.getGeometry() as SimpleGeometry).getCoordinates(),
            ) ?? '',
        },
      };
    }
    const newPoint = features[0];
    const selectObject = features[1].getProperties();
    if (selectObject.otype === 'WDM_PIPES') {
      return {
        type: 'BREAKPIPE',
        junction: {
          shape:
            MapEditLayer.convertToPoint(
              (newPoint.getGeometry() as SimpleGeometry).getCoordinates(),
            ) ?? '',
        },
        pipe: {
          otype: selectObject.otype,
          oname: selectObject.oname,
        },
      };
    }
    return undefined;
  }

  static drawPoint(features: Feature[]) {
    const junction = MapEditLayer.getPoint(features);
    store.dispatch(
      editSelectionActions.selectionChanged({
        addJunction: junction ? [junction] : undefined,
      }),
    );
  }

  static getStartPointAndEndPoint(pointInfo: LinePointType): AddJunctionType {
    return {
      type: 'ADDJUNCTION',
      junction: {
        shape: pointInfo.shape ?? '',
        otype: pointInfo?.otype ?? '',
        oname: pointInfo?.oname ?? '',
      },
      pipe: pointInfo.pipe,
    };
  }

  isBreakSamePipe(otype: string, oname: string) {
    return (
      this._lineStartPoint?.pipe &&
      this._lineStartPoint.pipe.otype === otype &&
      this._lineStartPoint.pipe.oname === oname
    );
  }

  drawLine() {
    const newJunctions: AddJunctionType[] = [];
    if (this._lineStartPoint?.type === 'newPoint') {
      newJunctions.push(
        MapEditLayer.getStartPointAndEndPoint(this._lineStartPoint),
      );
    }
    if (this._lineEndPoint?.type === 'newPoint') {
      newJunctions.push(
        MapEditLayer.getStartPointAndEndPoint(this._lineEndPoint),
      );
    }
    const newPipe: AddPipeType = {
      startJunction: {
        otype: this._lineStartPoint?.otype ?? '',
        oname: this._lineStartPoint?.oname ?? '',
      },
      endJunction: {
        otype: this._lineEndPoint?.otype ?? '',
        oname: this._lineEndPoint?.oname ?? '',
      },
      shape:
        MapEditLayer.convertToLine(
          (
            this._tempLineString?.getGeometry() as SimpleGeometry
          ).getCoordinates(),
        ) ?? '',
      diameter: 500,
    };

    store.dispatch(
      editSelectionActions.selectionChanged({
        addJunction: newJunctions,
        addPipe: [newPipe],
      }),
    );

    this._lineStartPoint = undefined;
    this._lineEndPoint = undefined;
  }

  generateLinePoint(features: Feature[], oname: string): LinePointType {
    let linePoint: LinePointType = {
      type: 'newPoint',
      oname,
      otype: 'WDM_JUNCTIONS',
    };

    if (features.length === 0) {
      linePoint.shape = this._tempPoint
        ? (MapEditLayer.convertToPoint(this._tempPoint) ?? '')
        : '';
    } else {
      for (let i = 0; i < features.length; i += 1) {
        const objectInfo = features[i].getProperties();
        if (
          objectInfo.otype === 'WDM_JUNCTIONS' ||
          objectInfo.otype === 'WDM_HYDRANT' ||
          objectInfo.otype === 'WDM_ENDPOINT' ||
          objectInfo.otype === 'WDM_MODELNODE'
        ) {
          return {
            oname: objectInfo.oname,
            otype: objectInfo.otype,
          };
        }
        if (objectInfo.otype === 'WDM_PIPES') {
          linePoint.shape = this._tempPoint
            ? (MapEditLayer.convertToPoint(this._tempPoint) ?? '')
            : '';
          if (!this.isBreakSamePipe(objectInfo.otype, objectInfo.oname)) {
            linePoint.pipe = {
              otype: objectInfo.otype,
              oname: objectInfo.oname,
            };
          }
          return linePoint;
        }
      }

      linePoint = {
        type: 'newPoint',
        oname,
        otype: 'WDM_JUNCTIONS',
        shape: this._tempPoint
          ? (MapEditLayer.convertToPoint(this._tempPoint) ?? '')
          : '',
      };
    }

    return linePoint;
  }

  startEditNetwork(drawType: EditType) {
    this._map.removeInteraction(this._draw);
    this._map.removeInteraction(this._snap);
    this._map.removeInteraction(this._select);

    if (drawType === EditType.DELETEPIPE) {
      this._map.addInteraction(this._select);
    } else {
      this._drawType =
        drawType === EditType.SINGLELINESTRING ? EditType.LINESTRING : drawType;
      this._draw = new Draw({
        source: this._source,
        maxPoints: drawType === EditType.SINGLELINESTRING ? 2 : undefined,
        type: this._drawType,
      });

      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Enter') {
          this._draw.finishDrawing();
        }
      };

      this._draw.on('drawstart', (event) => {
        if (this._drawType === EditType.LINESTRING) {
          const map = event.target.getMap();
          const features: Feature[] = [];
          // 遍历与该像素位置相交的所有 feature
          map.forEachFeatureAtPixel(
            event.target.downPx_,
            (feature: Feature) => {
              features.push(feature);
            },
          );

          this._tempPoint = (
            event.feature.getGeometry() as SimpleGeometry
          ).getCoordinates()?.[0];
          if (!this._lineStartPoint) {
            this._lineStartPoint = {
              type: 'newPoint',
              oname: 'junction_1',
              otype: 'WDM_JUNCTIONS',
              shape: this._tempPoint
                ? (MapEditLayer.convertToPoint(this._tempPoint) ?? '')
                : '',
            };
          }
          this._lineStartPoint = this.generateLinePoint(features, 'junction_1');

          if (drawType === EditType.LINESTRING)
            document.addEventListener('keydown', handleKeyDown);
        }
      });

      this._draw.on('drawend', (event) => {
        const map = event.target.getMap();
        const features: Feature[] = [];
        map.forEachFeatureAtPixel(event.target.downPx_, (feature: Feature) => {
          features.push(feature);
        });

        if (this._drawType === EditType.POINT) {
          MapEditLayer.drawPoint(features);
        }
        if (this._drawType === EditType.LINESTRING) {
          const linePoints = (
            event.feature.getGeometry() as SimpleGeometry
          ).getCoordinates();
          this._tempPoint = linePoints ? linePoints[linePoints.length - 1] : '';
          if (!this._lineEndPoint) {
            this._lineEndPoint = {
              type: 'newPoint',
              oname: 'junction_2',
              otype: 'WDM_JUNCTIONS',
              shape: this._tempPoint
                ? (MapEditLayer.convertToPoint(this._tempPoint) ?? '')
                : '',
            };
          }

          this._lineEndPoint = this.generateLinePoint(features, 'junction_2');
          this._tempLineString = event.feature;
          this.drawLine();
        }

        document.removeEventListener('keydown', handleKeyDown);
      });
      this._map.addInteraction(this._draw);
    }
    this._map.addInteraction(this._snap);
    this._snap.openSnap = true;
  }

  endEditNetwork() {
    this._map.removeInteraction(this._draw);
    this._map.removeInteraction(this._snap);
    this._map.removeInteraction(this._select);
    this._source.clear();
    this._snap.openSnap = false;
    this._tempPoint = [];
    this._tempLineString = undefined;
    this._lineStartPoint = undefined;
    this._lineEndPoint = undefined;
  }

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  // eslint-disable-next-line class-methods-use-this
  redraw(): void {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  setTheme(_theme: Theme) {}
}
