/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import { MainPipeLayerData } from '@waterdesk/data/layer-data';
import TraceNetwork, { TracePath } from '@waterdesk/data/trace/trace-network';
import { getMainPipes } from '@waterdesk/request/get-main-pipes';
import dayjs, { Dayjs } from 'dayjs';
import { LinesSeriesOption } from 'echarts';
import * as echarts from 'echarts/core';
import { View } from 'ol';
import { Extent } from 'ol/extent';
import { default as OlMap } from 'ol/Map';
import EChartsLayer from 'src/components/ol-echarts';

export default class MapMainPipeLayer {
  private _map: OlMap;

  private _view: View;

  private _name: string;

  private _layer: EChartsLayer | null = null;

  private _currentArrayTracePath: TracePath[] = [];

  private highlightObjectsMap: { [key: string]: HighlightObject } = {};

  private option: echarts.ComposeOption<LinesSeriesOption> = {};

  private lastExtent: Extent | undefined;

  private currentZoom: number | undefined;

  private _currentTime: string | undefined;

  private _layerData: MainPipeLayerData;

  private _visible: boolean = false;

  constructor(map: OlMap, layerData: MainPipeLayerData) {
    this._name = 'echartsLines';
    this._map = map;
    this._view = map.getView();
    this._layerData = layerData;
    if (map.getTarget()) {
      this._layer = new EChartsLayer(
        this.option,
        {
          hideOnMoving: true,
          hideOnZooming: true,
          forcedPrecomposeRerender: false,
          source: map.getView().getProjection().getCode(),
        },
        map,
      );
      this._layer.setZIndex(-1);
    } else {
      console.log('no Target!');
    }
  }

  get name(): string {
    return this._name;
  }

  get layer(): EChartsLayer | null {
    return this._layer;
  }

  drawTracePath(zoomFactor?: number) {
    const series: echarts.ComposeOption<LinesSeriesOption>['series'] = [];
    this._currentArrayTracePath.forEach((item) => {
      const lines = item.coordinates.map((item) => item.split(' ').map(Number));
      if (!lines || lines.length < 2) {
        console.warn('Invalid coordinates for item:', item);
        return;
      }
      const { pipeColor, waterFlowColor, symbolSize } =
        this._layerData.mainPipeConfig;
      const linesSeriesOption: echarts.ComposeOption<LinesSeriesOption>['series'] =
        [
          {
            type: 'lines',
            polyline: true,
            silent: true,
            data: [{ coords: lines }],
            lineStyle: {
              color: pipeColor,
              width: 5,
            },
            progressiveThreshold: 500,
            progressive: 200,
          },
          {
            type: 'lines',
            polyline: true,
            data: [{ coords: lines }],
            lineStyle: {
              width: 0,
              color: `${waterFlowColor}`,
            },
            effect: {
              symbol: 'rect',
              constantSpeed: Math.max(item.velocity * 50, 30),
              show: true,
              trailLength: 0.85,
              symbolSize: [
                (symbolSize ?? 0) +
                  (item.diameter ?? 0) / 200 -
                  (zoomFactor ?? 0),
                0.8,
              ],
            },
            zlevel: 1,
          },
        ];
      series.push(...linesSeriesOption);
    });
    this.setVisible(true);
    this._layer?.setChartOptions({
      series,
    });
  }

  refreshHighlightObject(highlightObjects: HighlightObject[]) {
    this.clearHighlightObject();
    highlightObjects.forEach((i) => {
      this.highlightObjectsMap[i.id] = i;
    });
    const traceValues = new TraceNetwork();
    traceValues.initialize(highlightObjects);
    traceValues.tryMergePath();
    this._currentArrayTracePath = traceValues.arrayTracePath;
    this.drawTracePath();
  }

  clearHighlightObject() {
    this._currentArrayTracePath = [];
    this.setVisible(false);
  }

  calculateExtent(): Extent {
    const extent = this._view.calculateExtent(this._map.getSize());
    const center = this._view.getCenter() ?? [0, 0];
    const newExtent = [
      2 * extent[0] - center[0], // 左边界
      2 * extent[1] - center[1], // 下边界
      2 * extent[2] - center[0], // 右边界
      2 * extent[3] - center[1], // 上边界
    ];
    this.currentZoom = this._view.getZoom();
    this.lastExtent = newExtent;
    return newExtent;
  }

  getDiameter(): number {
    const currentZoom = this._view.getZoom() ?? 0;
    const { zoomDiameter } = this._layerData.mainPipeConfig;
    for (let i = 0; i < zoomDiameter.length; i += 1) {
      if (currentZoom <= zoomDiameter[i].zoom) {
        return zoomDiameter[i].diameter;
      }
    }
    return 600;
  }

  inLastExtent(): boolean {
    if (this.currentZoom !== this._view.getZoom()) return false;
    if (!this.lastExtent) return false;
    const currentExtent = this._view.calculateExtent(this._map.getSize());
    if (currentExtent[0] < this.lastExtent[0]) {
      return false;
    }
    if (currentExtent[1] < this.lastExtent[1]) {
      return false;
    }
    if (currentExtent[2] > this.lastExtent[2]) {
      return false;
    }
    if (currentExtent[3] > this.lastExtent[3]) {
      return false;
    }
    return true;
  }

  refreshMainPipeData(currentTime?: Dayjs) {
    if (!this._visible) return;
    const currentZoom = this._view.getZoom() as number;
    if (
      (this._layerData.minZoom && currentZoom < this._layerData.minZoom) ||
      (this._layerData.maxZoom && currentZoom > this._layerData.maxZoom)
    )
      return;

    let currentTimeString;
    if (currentTime) {
      const minute: number = 5 * Math.floor(currentTime.minute() / 5);
      currentTimeString = currentTime.format(`YYYY-MM-DD HH:${minute}:00`);
    }
    if (
      currentTimeString &&
      currentTimeString === this._currentTime &&
      this.inLastExtent()
    ) {
      this._layer?.onMoveEnd();
      return;
    }
    this._currentTime = currentTimeString;
    const extent = this.calculateExtent();
    getMainPipes(
      extent,
      currentTimeString,
      this.getDiameter(),
      this._layerData.mainPipeConfig.otypes
        ? this._layerData.mainPipeConfig.otypes.toString()
        : undefined,
    ).then((res) => {
      if (res.status === 'Success' && res.list) {
        this.refreshHighlightObject(res.list);
      }
    });
  }

  redraw(): void {
    if (!this._map && !this._layer && !this._visible) {
      return;
    }
    this.refreshMainPipeData(dayjs(this._currentTime));
  }

  setVisible(visible: boolean) {
    if (visible) {
      this._visible = true;
      this._layer?.show();
    } else {
      this._visible = false;
      this._layer?.hide();
    }
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
  setTheme(_theme: Theme) {}

  dispose() {
    this.clearHighlightObject();
    if (this._layer) {
      this._layer.dispose();
      this._layer = null;
    }
    this._currentArrayTracePath = [];
    this.highlightObjectsMap = {};
    this.option = {};
    this.lastExtent = undefined;
    this.currentZoom = undefined;
    this._currentTime = undefined;
  }
}
