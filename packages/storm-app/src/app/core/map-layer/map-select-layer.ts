/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import Device from '@waterdesk/data/device';
import { IObjectItem, validateShape } from '@waterdesk/data/object-item';
import { Feature } from 'ol';
import WKT from 'ol/format/WKT';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import { Style } from 'ol/style';
import { StyleLike } from 'ol/style/Style';
import { hostApp } from 'src/app/host-app';
import FeatureStyle from './feature-style';

import { IMapLayer } from './map-layer';

export default class MapSelectLayer implements IMapLayer {
  private _map: OlMap;

  private _name: string;

  private _layer: VectorLayer;

  private _source: VectorSource;

  private _selectedObjects: Array<IObjectItem> = [];

  private _selectedObjectIds: Set<string> = new Set();

  private _WKT: WKT = new WKT();

  private featureStyle = new FeatureStyle(
    'select',
    hostApp().appConfig.highlightStyle,
  );

  constructor(map: OlMap) {
    this._name = 'select';
    this._map = map;
    this._source = new VectorSource();
    this._layer = new VectorLayer({ source: this._source });
    this._layer.setProperties({
      undetectable: true,
      unselectable: true,
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  clearSelectedObjects() {
    this._source.clear();
    this._selectedObjects = [];
    this._selectedObjectIds.clear();
  }

  addSelectedObject(clickedObject: IObjectItem) {
    if (this._selectedObjectIds.has(clickedObject.id)) return;

    this._selectedObjectIds.add(clickedObject.id);
    this._selectedObjects.push(clickedObject);

    if (
      clickedObject instanceof Device &&
      clickedObject.feature !== undefined
    ) {
      const feature: Feature = (clickedObject.feature as Feature).clone();
      MapSelectLayer.setHighlightStyle(feature);
      this._source.addFeature(feature);
    } else {
      this.addClickedObjectFeature(clickedObject);
    }
  }

  private addClickedObjectFeature(clickedObject: IObjectItem) {
    if (validateShape(clickedObject.shape)) {
      const feature: Feature<any> = this._WKT.readFeature(clickedObject.shape);
      feature.set('id', clickedObject.id, true);
      feature.set('oname', clickedObject.oname, true);
      feature.set('otype', clickedObject.otype, true);
      const style: StyleLike | undefined =
        this.featureStyle.generateObjectStyle(clickedObject);
      if (style) feature.setStyle(style);
      this._source.addFeature(feature);
    }
  }

  private static setHighlightStyle(feature: Feature) {
    const style: Style = feature.getStyle() as Style;
    if (style) {
      const clonedStyle = style.clone();
      const originalColor = clonedStyle.getText()?.getFill()?.getColor();
      feature.set('color', originalColor);
      clonedStyle.getText()?.getFill()?.setColor('#ff0000');
      clonedStyle.getText()?.setFont('32px iconfont');
      clonedStyle.getImage()?.setScale(2);

      feature.setStyle(clonedStyle);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  redraw(): void {}

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  setTheme(_theme: Theme) {}

  addFeature(feature: Feature) {
    this._source.addFeature(feature);
  }
}
