/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { isTimeRangesOverlap } from '@waterdesk/data/utils';
import {
  displayBlink,
  WarnDetail,
  WarnInfoItem,
  WarnInfoList,
} from '@waterdesk/data/warn';
import lodash from 'lodash';
import { Overlay } from 'ol';
import { Extent } from 'ol/extent';
import WKT from 'ol/format/WKT';
import { default as OlMap } from 'ol/Map';
import { memo, useEffect, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import { useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import { useBaseSlice } from 'src/store/base';
import {
  selectDataInitialComplete,
  selectNoticeWarnList,
} from 'src/store/base/selectors';
import { useBlinkSlice } from 'src/store/blink';
import { selectRealtimeWarn } from 'src/store/blink/selector';
import {
  selectTimelineDate,
  selectTimelineTime,
} from 'src/store/time-line/selectors';
import ScadaWarningContainer from '../scada-label/warning';
import { BlinkingCircle } from './style';

interface Props {
  map: OlMap;
}

const wkt = new WKT();

const getCenterCoordinates = (extent: Extent | undefined): [number, number] => {
  if (extent) {
    const positionCount = extent.length / 2;
    let xSum = 0;
    let ySum = 0;
    extent.forEach((item, index) => {
      if (index % 2 === 0) {
        xSum += item;
      } else {
        ySum += item;
      }
    });
    return [xSum / positionCount, ySum / positionCount];
  }
  return [0, 0];
};

const BlinkingCircleMemo = memo(BlinkingCircle);

const createOverlay = (id: string, shape: string | undefined): Overlay => {
  const deviceExtent = wkt.readFeature(shape).getGeometry()?.getExtent();
  const position = getCenterCoordinates(deviceExtent);
  const overlay = new Overlay({
    id,
    position,
    positioning: 'center-center',
    stopEvent: false,
  });
  return overlay;
};

const extractWarnDetails = (
  warnList: WarnInfoList,
  filterCondition: (item: WarnInfoItem) => boolean,
) =>
  warnList
    .filter(filterCondition)
    .flatMap((item) => item.details.filter((detail) => detail.shape));

const BlinkOverlayManager = ({ map }: Props) => {
  useBaseSlice();
  useBlinkSlice();

  const timelineDate = useSelector(selectTimelineDate);
  const timelineTime = useSelector(selectTimelineTime);
  const noticeWarnList = useSelector(selectNoticeWarnList);
  const dataInitialComplete = useSelector(selectDataInitialComplete);
  const { blinkNoticeWarnQueryDuration, notFlashWarnStatus } =
    hostApp().appConfig;
  // 选中的实时警告
  const realtimeWarnList = useSelector(selectRealtimeWarn);

  const timelineDateTime = getDateTimeFromValue(timelineTime, timelineDate);

  // 警告闪烁: 选中的实时警告+时间轴命中的警告
  const blinkWarnDetails = useMemo((): WarnDetail[] => {
    const startTime = timelineDateTime.add(
      -blinkNoticeWarnQueryDuration,
      'minute',
    );
    const endTime = timelineDateTime.add(
      blinkNoticeWarnQueryDuration,
      'minute',
    );
    // 过滤条件
    const isBlinkEnabled = (item: WarnInfoItem) =>
      displayBlink(item) && !notFlashWarnStatus.includes(item.confirmStatus);
    const isInTimeRange = (detail: WarnDetail) =>
      isTimeRangesOverlap(
        [detail.startTime, detail.endTime],
        [startTime, endTime],
      );
    // 时间轴命中的警告
    const filterTimelineWarnDetails = extractWarnDetails(
      noticeWarnList,
      (item) => isBlinkEnabled(item) && item.details.some(isInTimeRange),
    );

    // 实时警告
    const realtimeWarnDetails = extractWarnDetails(
      realtimeWarnList,
      isBlinkEnabled,
    );

    return lodash.uniqBy(
      [...filterTimelineWarnDetails, ...realtimeWarnDetails],
      'id',
    );
  }, [
    dataInitialComplete,
    blinkNoticeWarnQueryDuration,
    timelineDateTime,
    realtimeWarnList,
    noticeWarnList,
  ]);

  const overlays = useMemo(
    () =>
      blinkWarnDetails.map(({ deviceId, shape }) =>
        createOverlay(deviceId, shape),
      ),
    [blinkWarnDetails],
  );

  const onMouseOver = (deviceId: string) => {
    hostApp().getMainMapView()?.pointerMove(deviceId);
  };

  useEffect(() => {
    setTimeout(() => {
      overlays.forEach((overlay) => {
        map.addOverlay(overlay);
      });
    }, 1000);
    return () => {
      overlays.forEach((overlay) => {
        map.removeOverlay(overlay);
      });
    };
  }, [overlays, map]);

  useEffect(() => {
    overlays.forEach((overlay) => {
      const div = document.createElement('div');
      const root = createRoot(div);
      root.render(
        <div
          onMouseOver={() => onMouseOver(overlay.getId() as string)}
          onFocus={() => {}}
        >
          <BlinkingCircleMemo
            radius={30}
            duration={3}
            bgColor="red"
          />
          <ScadaWarningContainer
            style={{ transform: 'translateY(0)' }}
            id={overlay.getId() as string}
            timelineDate={timelineDate}
            noticeWarnList={noticeWarnList}
          />
        </div>,
      );
      overlay.setElement(div);
    });
  }, [overlays]);

  return <div />;
};

export default BlinkOverlayManager;
