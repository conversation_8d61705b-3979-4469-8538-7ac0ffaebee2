/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DataLayerManager,
  DataLayerManagerConfig,
  FeatureStyleAdapterCreator,
  IMapEventHandler,
  InteractionLayerManager,
  InteractionLayerManagerConfig,
  LayerCreationConfig,
  MapCoreConfig,
  MapCoreManager,
  MapCustomLayer,
  MapEventHandler,
  MapEventHandlerConfig,
  MapFeatureLayer,
  MapHighlightLayer,
  MapHighlightLayerGroup,
  MapHoverLayer,
  MapImageArcGISLayer,
  MapNetworkLayer,
} from '@waterdesk/core/map';
import { Theme } from '@waterdesk/core/theme';
import { MapViewName, MapViewType } from '@waterdesk/data/const/map';
import Database from '@waterdesk/data/database';
import Device from '@waterdesk/data/device';
import { DeviceThemeData } from '@waterdesk/data/device-theme';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { GisLayerIdentifyArgs } from '@waterdesk/data/gis-layer-data';
import GisObject from '@waterdesk/data/gis-object';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import {
  FeatureLayerData,
  LayerData,
  LayerDataCollection,
  MainPipeLayerData,
  zoomFilter,
} from '@waterdesk/data/layer-data';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import ModelObject from '@waterdesk/data/model-object';
import {
  getShapeCenter,
  getShapesCenter,
  getShapesExtent,
  IObjectItem,
  makeObjectId,
  validateShape,
} from '@waterdesk/data/object-item';
import {
  HighlightStyle,
  HighlightStyleType,
} from '@waterdesk/data/style-config';
import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { MOUSE_SELECT, MouseMode } from '@waterdesk/data/ui-types';
import { findAnyElementByPos } from '@waterdesk/request/find-any-element';
import {
  FindElementResponse,
  findElementByPos,
} from '@waterdesk/request/find-element';
import { findGisObject } from '@waterdesk/request/find-gis-object';
import { Dayjs } from 'dayjs';
import { MapBrowserEvent, View } from 'ol';
import { Coordinate } from 'ol/coordinate';
import { platformModifierKeyOnly } from 'ol/events/condition';
import { Extent } from 'ol/extent';
import Feature, { FeatureLike } from 'ol/Feature';
import WKT from 'ol/format/WKT';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point';
import Polygon from 'ol/geom/Polygon';
import { default as OlMap } from 'ol/Map';
import { Size } from 'ol/size';
import { Dispatch, UnknownAction } from 'redux';
import useTokenRefresher from 'src/app/hooks/use-token-refresher';
import { curDb, getServiceUrl, hostApp } from 'src/app/host-app';
import { blinkActions } from 'src/store/blink';
import store from 'src/store/configure-store';
import FeatureStyle from '../map-layer/feature-style';
import MapEchartsLinesLayer from '../map-layer/map-echarts-lines-layer';
import MapEditLayer, { DrawType, EditType } from '../map-layer/map-edit-layer';
import MapMainPipeLayer from '../map-layer/map-main-pipe-layer';
import MapSelectLayer from '../map-layer/map-select-layer';
import OverlayCollection from './overlay-collection';
import SelectionCollection from './selection-collection';

export default class MapView {
  private _mapViewName: MapViewName;

  private _mapViewType: MapViewType | undefined;

  private _target?: HTMLElement | string;

  private _projection?: string;

  private _selectLayer?: MapSelectLayer;

  private _editLayer?: MapEditLayer;

  private _hoverLayer?: MapHoverLayer;

  private _highlightLayer?: MapHoverLayer;

  private _trackHighlightObject:
    | {
        [key: string]: HighlightObject[];
      }
    | undefined;

  private _lastHoverDevice?: Device;

  private _lastHighlightDevice?: Device;

  private _lastSelectOverlay?: Device;

  private _highlightLayerGroup?: MapHighlightLayerGroup;

  private _echartsLinesLayer?: MapEchartsLinesLayer;

  private _flowLinesLayers: Map<string, MapMainPipeLayer> = new Map();

  private _gisIdentifyLayer?: MapImageArcGISLayer;

  private _layerManager?: DataLayerManager;

  private _selectedItems: Array<{
    object: IObjectItem;
    indicatorType?: string;
    indicatorName?: string;
    vprop?: string;
  }> = [];

  private _multiSelectedFlag: boolean = false;

  private _selectionCollection: SelectionCollection;

  private _overlayCollection: OverlayCollection = new OverlayCollection(this);

  private _identifyArgs?: GisLayerIdentifyArgs;

  private _viewId?: string;

  private _timelineDate: Dayjs | undefined;

  private _timelineDateTime: Dayjs | undefined;

  private _tempTime: Dayjs | undefined;

  private _mapCoreManager: MapCoreManager = new MapCoreManager();

  private _eventHandler: IMapEventHandler = new MapEventHandler();

  private _interactionLayerManager: InteractionLayerManager =
    new InteractionLayerManager();

  constructor(options: {
    mapViewName: MapViewName;
    mapViewType?: MapViewType;
    target?: HTMLElement | string;
    dispatch?: Dispatch<UnknownAction>;
  }) {
    const { mapViewName, mapViewType, target, dispatch } = options;
    this._mapViewName = mapViewName;
    this._target = target;
    this._mapViewType = mapViewType;
    this._selectionCollection = new SelectionCollection(dispatch);
  }

  curDb: Database = new Database();

  private initializeMap(projection?: string, mapZoomFactor?: number) {
    const coreConfig: MapCoreConfig = {
      target: this._target,
      projection: projection || 'EPSG:3857',
      mapZoomFactor,
      enableDoubleClickZoom: false,
      enableRotation: false,
      showScaleLine: true,
      showMousePosition: true,
    };

    this._mapCoreManager.initialize(coreConfig);

    // 初始化业务相关的图层
    this._selectLayer = new MapSelectLayer(this.map);
    this._editLayer = new MapEditLayer(this.map);

    // 创建样式配置适配器
    const createFeatureStyleAdapter: FeatureStyleAdapterCreator = (
      styleType: string,
      config: { [key: string]: unknown },
    ) => {
      const featureStyle = new FeatureStyle(
        styleType as HighlightStyleType,
        config as unknown as HighlightStyle,
      );
      return {
        generateObjectStyle:
          featureStyle.generateObjectStyle.bind(featureStyle),
      };
    };

    // 初始化交互图层管理器
    const interactionLayerConfig: InteractionLayerManagerConfig = {
      highlightStyle: hostApp().appConfig.highlightStyle,
      hoverStyle: hostApp().appConfig.highlightStyle,
      featureStyleCreator: createFeatureStyleAdapter,
    };

    this._interactionLayerManager.initialize(this.map, interactionLayerConfig);

    // 获取图层引用以保持向后兼容
    this._hoverLayer = this._interactionLayerManager.hoverLayer;
    this._highlightLayer = this._interactionLayerManager.highlightLayer;
    this._highlightLayerGroup =
      this._interactionLayerManager.highlightLayerGroup;
    this._echartsLinesLayer = new MapEchartsLinesLayer(this.map);

    // 初始化图层管理器
    const layerCreationConfig: LayerCreationConfig = {
      tokenRefresher: useTokenRefresher,
      serviceUrlGetter: getServiceUrl,
      skipInDevelopment: false, // storm-app 不跳过开发环境图层
      enableAnimatedOverlay: false, // storm-app 不使用动画覆盖层
      projection: this._projection,
    };

    const dataLayerManagerConfig: DataLayerManagerConfig = {
      creationConfig: layerCreationConfig,
      visibilityStrategy: 'show-all-not-invisible', // storm-app 策略
    };

    this._layerManager = new DataLayerManager(dataLayerManagerConfig);
    this._layerManager.setMap(this.map);

    // 初始化事件处理器
    const eventConfig: MapEventHandlerConfig = {
      onMapClick: (event) => this.handleClick(event),
      onPointerMove: (event) => this.handlePointerMove(event),
      onMoveEnd: () => this.handleMoveEnd(),
      debounceDelay: 80,
    };
    this._eventHandler.initialize(this.map, eventConfig);
  }

  initializeData(layerData: LayerDataCollection, extent: Array<number>) {
    this.curDb.initializeLayer(layerData, extent);
  }

  setMainPipeVisible(layerName: string, flag: boolean) {
    this._flowLinesLayers?.get(layerName)?.setVisible(flag);
    this._flowLinesLayers?.get(layerName)?.redraw();
  }

  updateMainPipe(currentTime?: Dayjs) {
    Array.from(this._flowLinesLayers?.values() ?? []).forEach((layer) => {
      layer.refreshMainPipeData(currentTime);
    });
  }

  dispose() {
    // 使用 LayerManager 清理所有普通图层
    if (this._layerManager) {
      this._layerManager.dispose();
      this._layerManager = undefined;
    }

    // 使用 InteractionLayerManager 清理交互图层
    this._interactionLayerManager.dispose();

    this._echartsLinesLayer?.layer?.remove();
    // 清理流线图层
    if (this._flowLinesLayers.size) {
      Array.from(this._flowLinesLayers?.values() ?? []).forEach((layer) => {
        layer.dispose();
        layer.layer?.dispose();
      });
      this._flowLinesLayers = new Map();
    }

    // 清理事件处理器
    this._eventHandler.dispose();

    // 清理图层引用
    this._hoverLayer = undefined;
    this._highlightLayer = undefined;
    this._highlightLayerGroup = undefined;

    this._mapCoreManager.dispose();
  }

  get mapViewName(): MapViewName {
    return this._mapViewName;
  }

  get mapViewType(): MapViewType | undefined {
    return this._mapViewType;
  }

  setView(view: View) {
    this._mapCoreManager.setView(view);
  }

  get map(): OlMap {
    return this._mapCoreManager.map as OlMap;
  }

  get view(): View {
    return this._mapCoreManager.view as View;
  }

  get extent(): Array<number> {
    return this._mapCoreManager.extent;
  }

  get initialZoom(): number {
    return this._mapCoreManager.initialZoom;
  }

  get selectionCollection(): SelectionCollection {
    return this._selectionCollection;
  }

  get multiSelectedFlag(): boolean {
    return this._multiSelectedFlag;
  }

  get date(): Dayjs | undefined {
    return this._timelineDate;
  }

  get dateTime(): Dayjs | undefined {
    return this._tempTime ?? this._timelineDateTime;
  }

  get trackHighlightObject():
    | {
        [key: string]: HighlightObject[];
      }
    | undefined {
    return this._trackHighlightObject;
  }

  set trackHighlightObject(trackHighlightObject:
    | {
        [key: string]: HighlightObject[];
      }
    | undefined,) {
    this._trackHighlightObject = trackHighlightObject;
  }

  setTempTime(time: Dayjs | undefined) {
    this._tempTime = time;
  }

  setTimelineDate(timelineDate: Dayjs) {
    this._timelineDate = timelineDate;
  }

  setTimelineDateTime(timelineTime: number) {
    this._timelineDateTime = getDateTimeFromValue(
      timelineTime,
      this._timelineDate,
    );
  }

  setMultiSelectedFlag(flag: boolean) {
    this._multiSelectedFlag = flag;
  }

  setChartObjects(objects: IObjectItem[]) {
    this._selectionCollection.setChartObjects(objects);
  }

  get overlayCollection(): OverlayCollection {
    return this._overlayCollection;
  }

  setMouseOnMap(mouseOnMap: boolean) {
    this._eventHandler.setMouseOnMap(mouseOnMap);
  }

  setMouseMode(mouseMode: MouseMode) {
    this._eventHandler.setMouseMode(mouseMode);
  }

  redraw() {
    this._layerManager?.redraw();
    this.redrawOverlay();

    this.updateMainPipe(this.dateTime);
  }

  setViewId(viewId: string) {
    this._viewId = viewId;
    // 使用 LayerManager 查找网络图层并设置 viewId
    const networkLayers = this._layerManager?.findLayersByType(MapNetworkLayer);
    networkLayers?.forEach((layer) => {
      layer.setViewId(viewId);
    });
  }

  setMapViewName(name: string) {
    this._mapViewName = name;
  }

  getViewId(): string | undefined {
    return this._viewId;
  }

  updateDeviceFeatureRatio(
    deviceTypeRatioData?: Map<string, [number | undefined, number | undefined]>,
    deviceThemeData?: DeviceThemeData,
  ) {
    if (deviceTypeRatioData?.size || deviceThemeData?.size) {
      // 使用 LayerManager 查找特征图层并更新设备特征比例
      const featureLayers =
        this._layerManager?.findLayersByType(MapFeatureLayer);
      featureLayers?.forEach((layer) => {
        layer.updateDeviceFeatureRatio(
          deviceTypeRatioData ?? new Map(),
          deviceThemeData ?? new Map(),
        );
      });
    }
  }

  updateLayersVisible(
    invisibleLayers: Set<string>,
    currentSceneAllLayers: string[],
  ) {
    // 使用 LayerManager 更新图层可见性
    this._layerManager?.updateLayersVisible(
      invisibleLayers,
      currentSceneAllLayers,
    );

    // 处理主管道图层
    const mainPipeFlowNames = this._flowLinesLayers?.keys();
    Array.from(mainPipeFlowNames ?? [])?.forEach((name) => {
      const layerMainPipeVisible = invisibleLayers.has(name);
      if (!layerMainPipeVisible && currentSceneAllLayers.includes(name)) {
        this.setMainPipeVisible(name, true);
      } else {
        this.setMainPipeVisible(name, false);
      }
    });
  }

  setExtent(extent: Array<number>) {
    this._mapCoreManager.setExtent(extent);
  }

  mapToExtent() {
    this._mapCoreManager.fitToExtent();
  }

  initialize(
    viewId: string,
    extent: Array<number>,
    invisibleLayers: string[],
    defaultTheme: Theme,
    projection?: string,
    mapZoomFactor?: number,
  ) {
    this.initializeMap(projection, mapZoomFactor);
    this.setExtent(extent);

    this._overlayCollection.clearOverlay();

    // 使用 LayerManager 创建和管理图层
    this.curDb.layerCollection.layerDatas.forEach((layerData) => {
      if (layerData.type === 'MainPipe' && this.map) {
        this._flowLinesLayers.set(
          layerData.name,
          new MapMainPipeLayer(this.map, layerData as MainPipeLayerData),
        );
        return;
      }

      this.generateMapOverlay(layerData);

      // 使用 LayerManager 创建图层
      const mapLayer = this._layerManager?.addLayer(
        layerData,
        viewId,
        defaultTheme,
      );

      if (mapLayer) {
        // 设置初始可见性
        if (invisibleLayers.find((layerName) => layerName === mapLayer.name)) {
          mapLayer.setVisible(false);
        }

        // 保存 GIS 识别图层
        if (
          mapLayer instanceof MapImageArcGISLayer &&
          mapLayer.identifyParams
        ) {
          this._identifyArgs = mapLayer.identifyParams;
          this._gisIdentifyLayer = mapLayer;
        }
      }
    });

    // 通过 InteractionLayerManager 添加交互图层
    this._interactionLayerManager.addLayersToMap();

    if (this._selectLayer?.layer) this.map?.addLayer(this._selectLayer.layer);
    if (this._editLayer?.layer) this.map?.addLayer(this._editLayer.layer);

    // 初始化完成后显示覆盖层
    this.redrawOverlay();
  }

  private generateMapOverlay(layerData: LayerData) {
    if (this.map && layerData instanceof FeatureLayerData) {
      this._overlayCollection.olMap = this.map;
      this._overlayCollection.initializeOverlays(layerData);
    }
  }

  private async handleClick(event: MapBrowserEvent<PointerEvent>) {
    if (
      !this._eventHandler.getMouseOnMap() ||
      this._eventHandler.getMouseMode() !== MOUSE_SELECT
    )
      return;
    const mapViews = hostApp().getMapViews();

    if (mapViews?.some((item) => item._mapViewName === 'MINIMAP')) {
      return;
    }

    if (!platformModifierKeyOnly(event) && !this._multiSelectedFlag) {
      mapViews?.forEach((item) => {
        const mapView = item;
        mapView._selectedItems = [];
        mapView._selectLayer?.clearSelectedObjects();
        if (mapView._lastHoverDevice !== undefined) {
          mapView._overlayCollection.removeOverlay(mapView._lastHoverDevice.id);
          mapView._lastHoverDevice = undefined;
        }
        if (mapView._lastSelectOverlay !== undefined) {
          mapView._overlayCollection.removeOverlay(
            mapView._lastSelectOverlay.id,
          );
          mapView._lastSelectOverlay = undefined;
        }
      });
    }

    mapViews?.forEach((mapView) => {
      mapView.resetHighlightLayer();
    });

    if (mapViews) {
      for await (const item of mapViews) {
        const mapView = item;
        const clickedItem = await mapView.getClickItem(event);
        if (
          clickedItem &&
          mapView._selectedItems.findIndex(
            (item) => item.object.id === clickedItem.id,
          ) < 0
        ) {
          mapView._selectedItems.push({
            object: clickedItem,
          });
          mapView._selectLayer?.addSelectedObject(clickedItem);
          if (
            mapView._selectedItems.length === 1 &&
            clickedItem instanceof Device
          ) {
            if (
              mapView._overlayCollection.forceShowOverlay(clickedItem.id, false)
            ) {
              mapView._lastHoverDevice = undefined;
              mapView._lastSelectOverlay = clickedItem;
            }
          }
        }
      }
    }

    mapViews?.reverse().forEach((mapView, index) => {
      if (index !== mapViews.length - 1) {
        mapView.updateSelectionItems(undefined, undefined, undefined, true);
      } else {
        mapView.updateSelectionItems();
      }
    });

    this.setMultiSelectedFlag(false);
  }

  private setHoverBlinkObjectId(feature: Feature | undefined) {
    let id: string | undefined;
    if (typeof feature === 'undefined') {
      id = undefined;
    } else {
      id = feature.getId() as string;
    }
    store.dispatch(
      blinkActions.updateHoverBlinkObject({
        id,
      }),
    );
  }

  pointerMove(deviceId: string) {
    if (this._lastHoverDevice) {
      this._overlayCollection.removeOverlay(this._lastHoverDevice.id);
      this._lastHoverDevice = undefined;
    }
    this.setHoverBlinkObjectId(undefined);
    if (!deviceId) return;

    const device = this.curDb.getDeviceById(deviceId);
    if (device !== undefined) {
      this.displayHoverHighlight(device);
      if (this._overlayCollection.forceShowOverlay(device.id, true)) {
        this._lastHoverDevice = device;
        this.setMouseOnMap(true);
      }
      this._overlayCollection.setDisplayOverlayWarning(device.id);
      this._overlayCollection.toggleHoverElementVisible(true, device.id);
    }
  }

  private handlePointerMove(event: MapBrowserEvent<PointerEvent>) {
    if (!this.map || this._eventHandler.getMouseMode() !== MOUSE_SELECT) return;

    const features = this._eventHandler.getFeaturesAtPixel(
      event.pixel,
      (layer) => !layer.getProperties().undetectable,
    );

    this.clearHoverHighlight();

    this._overlayCollection.toggleHoverElementVisible(false);
    if (features.length > 0) {
      this.pointerMove(features[0].get('id'));
    }
  }

  private redrawOverlay() {
    this._overlayCollection.clearOverlay();
    // 使用 LayerManager 查找特征图层来处理覆盖层
    const featureLayers = this._layerManager?.findLayersByType(MapFeatureLayer);
    featureLayers?.forEach((layer) => {
      if (layer.layer?.getVisible()) {
        this._overlayCollection.displayOverlays(layer.displayFeatures);
      }
    });
    this._overlayCollection.displayOverlay();

    if (this._lastSelectOverlay !== undefined) {
      this._overlayCollection.forceShowOverlay(
        this._lastSelectOverlay.id,
        false,
      );
    }

    if (this._lastHighlightDevice) {
      this._overlayCollection.forceShowOverlay(
        this._lastHighlightDevice.id,
        true,
      );
      this._overlayCollection.setDisplayOverlayWarning(
        this._lastHighlightDevice.id,
      );
    }

    this.setMouseOnMap(true);
  }

  private handleMoveEnd() {
    if (this._flowLinesLayers) {
      Array.from(this._flowLinesLayers.values()).forEach((layer) => {
        layer.redraw();
      });
    }
    // 使用 LayerManager 查找特征图层进行重绘
    const featureLayers = this._layerManager?.findLayersByType(MapFeatureLayer);
    featureLayers?.forEach((layer) => {
      if (layer.layer?.getVisible()) {
        layer.redraw();
      }
    });
    this.redrawOverlay();
  }

  private async getClickItem(
    event: MapBrowserEvent<PointerEvent>,
  ): Promise<IObjectItem | undefined> {
    if (!this.map || !this.view) return undefined;

    const features: Array<Feature> = [];
    this.map.forEachFeatureAtPixel(
      event.pixel,
      (f: FeatureLike) => {
        if (f instanceof Feature) features.push(f);
      },
      {
        layerFilter: (layer) => !layer.getProperties().unselectable,
      },
    );

    if (features.length > 0) {
      const device = this.getDevice(features[0]);
      if (device) {
        return device;
      }
      const { otype, oname, shape } = features[0].getProperties();
      if (otype !== undefined && oname !== undefined)
        return new ModelObject(otype, oname, shape, this.curDb);
    }

    const mapRange = this.view.calculateExtent([10, 10]);
    const tolerance = mapRange[2] - mapRange[0];
    const res: FindElementResponse = await findElementByPos(
      event.coordinate[0],
      event.coordinate[1],
      tolerance,
      curDb(),
      this._viewId,
      this._target ? undefined : this.view.getZoom(),
    );

    if (res.foundObject) return res.foundObject;

    const gisObject = await this.tryFindGisObject(
      event.coordinate[0],
      event.coordinate[1],
      tolerance,
    );

    return gisObject;
  }

  private async tryFindGisObject(
    x: number,
    y: number,
    tolerance: number,
  ): Promise<Promise<GisObject | undefined>> {
    if (!(this.map && this._identifyArgs && this._gisIdentifyLayer?.isVisible))
      return undefined;

    const extent: Extent | undefined = this.view?.calculateExtent();
    const size: Size | undefined = this.map.getSize();
    if (!(extent && size)) return undefined;

    const { minZoom, maxZoom } = this._gisIdentifyLayer;
    if (!zoomFilter(this.map, minZoom, maxZoom)) return undefined;

    const [gisObjectResponse, elementResponse] = await Promise.all([
      findGisObject(this._identifyArgs, x, y, extent, size[0], size[1]),
      findAnyElementByPos(x, y, tolerance, curDb()),
    ]);

    const gisObject = gisObjectResponse.foundObject;
    if (gisObject) {
      const modelObjects = elementResponse.foundObjects;
      if (modelObjects) {
        for (let i = 0; i < modelObjects.length; i += 1) {
          if (gisObject.shapeType === modelObjects[i].shapeType) {
            gisObject.refModelObject = modelObjects[i];
            break;
          }
        }
      }
      return gisObject;
    }

    return undefined;
  }

  private updateSelectionItems(
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
    noDispatch?: boolean,
  ) {
    const items = this.getSelectedObjects();
    if (items.length > 0) {
      this._selectionCollection.setSelectionItems(
        items,
        indicatorType,
        indicatorName,
        vprop,
        noDispatch,
      );
    } else {
      this._selectionCollection.clear();
    }
  }

  private getSelectedObjects(): typeof this._selectedItems {
    const items: typeof this._selectedItems = [];
    this._selectedItems.forEach((item) => {
      if (item.object instanceof Feature) {
        const id: string = item.object.get('id') as string;
        if (id) {
          const device = this.curDb.getDeviceById(id);
          if (device)
            items.push({
              ...item,
              object: device,
            });
        }
      } else {
        items.push(item);
      }
    });

    return items;
  }

  startEditNetwork(drawType: EditType) {
    this._editLayer?.startEditNetwork(drawType);
  }

  endEditNetwork() {
    this._editLayer?.endEditNetwork();
  }

  setHighlightLayer(layerName: string, highlightLayer: MapHighlightLayer) {
    this._highlightLayerGroup?.setLayer(layerName, highlightLayer);
  }

  displayHighlight(highlightObjects: { [key: string]: HighlightObject[] }) {
    this._highlightLayerGroup?.addHighlightObject(highlightObjects);
  }

  clearHighlight(layerName?: string) {
    this._highlightLayerGroup?.clearHighlightObjects(layerName);
  }

  displayEchartsLines(
    themes?:
      | {
          [key: string]: LegendGroupData | undefined;
        }
      | undefined,
  ) {
    const highlightObj = this.trackHighlightObject;
    if (!highlightObj) return;

    const echartsLines: HighlightObject[] = [];
    Object.keys(highlightObj).forEach((item) => {
      if (item === 'upLink' || item === 'downLink' || item === 'sourceLink') {
        let array: HighlightObject[] | undefined = highlightObj[item];
        const style = themes?.[item];
        if (style) {
          array = highlightObj[item].map((i) => ({
            ...i,
            highlightColor: style.animateConfig?.pipeColor,
            highlightSecondColor: style.animateConfig?.waterFlowColor,
            highlightSize: style.animateConfig?.symbolSize,
          }));
        }
        if (array) {
          echartsLines.push(...array);
        }
      }
    });

    this._echartsLinesLayer?.addHighlightObject(echartsLines);
  }

  clearEchartsLines() {
    this._echartsLinesLayer?.clearHighlightObject();
  }

  displayHoverHighlight(hoverObject: IObjectItem | undefined) {
    if (hoverObject) this._hoverLayer?.addHoverObject(hoverObject);
  }

  clearHoverHighlight() {
    this._hoverLayer?.clearHoverObject();
  }

  cancelSelectedObject(id: string) {
    const selectedItems = this._selectedItems.filter(
      (item) => item.object.id !== id,
    );
    this._selectedItems = selectedItems;
    this.updateSelectionItems();
  }

  clearSelected() {
    this._selectedItems = [];
    this._selectLayer?.clearSelectedObjects();
    this.updateSelectionItems();
  }

  selectAndNavigate(otype: string, oname: string, shape: string) {
    if (validateShape(shape)) {
      this._mapCoreManager.animateTo(
        getShapeCenter(shape),
        hostApp().appConfig.animateZoomLevel,
      );
      this.selectAndHighlight(otype, oname, shape);
    }
  }

  navigate(shape: string) {
    if (validateShape(shape)) {
      this._mapCoreManager.animateTo(
        getShapeCenter(shape),
        hostApp().appConfig.animateZoomLevel,
      );
    }
  }

  navigateToCenterOfWKT(wktString: string) {
    const wkt = new WKT();
    const geometry = wkt.readGeometry(wktString);
    let centerX = 0;
    let centerY = 0;

    if (geometry instanceof Point) {
      const coordinates = geometry.getCoordinates();
      [centerX, centerY] = coordinates;
    } else if (geometry instanceof LineString) {
      const coordinates = geometry.getCoordinateAt(0.5);
      [centerX, centerY] = coordinates;
    } else if (geometry instanceof Polygon) {
      const extent = geometry.getExtent();
      centerX = (extent[0] + extent[2]) / 2;
      centerY = (extent[1] + extent[3]) / 2;
    } else {
      console.warn('未知的几何类型');
    }

    this.navigateToPoint([centerX, centerY]);
  }

  navigateToPoint(point: Coordinate) {
    this._mapCoreManager.animateTo(point, hostApp().appConfig.animateZoomLevel);
  }

  highlightFeature(feature: Feature) {
    this._selectLayer?.addFeature(feature);
  }

  selectAndHighlight(
    otype: string,
    oname: string,
    shape?: string,
    append?: boolean,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
  ) {
    if (!append) {
      this._selectedItems = [];
      this._selectLayer?.clearSelectedObjects();
      if (this._lastSelectOverlay !== undefined) {
        this._overlayCollection.removeOverlay(this._lastSelectOverlay.id);
        this._lastSelectOverlay = undefined;
      }
    }

    const objectId: string = makeObjectId(otype, oname);
    const foundSelectedItem = this._selectedItems.find(
      (item) => item.object.id === objectId,
    );
    const device = this.curDb.getDevice(otype, oname);
    if (device) {
      // append时支持多次选中同一个设备的不同指标
      if (
        foundSelectedItem &&
        append &&
        foundSelectedItem.indicatorName === indicatorName &&
        foundSelectedItem.indicatorType === indicatorType &&
        foundSelectedItem.vprop === vprop
      ) {
        return;
      }
      // select a device
      this._selectedItems.push({
        object: device,
        indicatorType,
        indicatorName,
        vprop,
      });
      this._selectLayer?.addSelectedObject(device);
      if (this._selectedItems.length === 1) {
        if (this._overlayCollection.forceShowOverlay(device.id, false)) {
          this._lastHoverDevice = undefined;
          this._lastSelectOverlay = device;
        }
      }
    } else {
      // 非设备不支持多次选择
      if (foundSelectedItem !== undefined) return;
      const modelObject = new ModelObject(
        otype,
        oname,
        shape ?? '',
        this.curDb,
      );
      this._selectedItems.push({
        object: modelObject,
      });
      this._selectLayer?.addSelectedObject(modelObject);
    }
    this.updateSelectionItems(indicatorType, indicatorName, vprop);
  }

  locateAndSelectAndHighlight(
    otype: string,
    oname: string,
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    shape?: string,
    append?: boolean,
  ) {
    if (shape) {
      this._mapCoreManager.animateTo(
        getShapeCenter(shape),
        hostApp().appConfig.animateZoomLevel,
      );
    }
    this.selectAndHighlight(
      otype,
      oname,
      shape,
      append,
      indicatorType,
      indicatorName,
      vprop,
    );
  }

  navigateAndHighlight(objectItem: IObjectItem) {
    if (!this._highlightLayer) return;

    this.resetHighlightLayer();
    this._highlightLayer.addHoverObject(objectItem);
    if (objectItem instanceof Device) {
      if (this._overlayCollection.forceShowOverlay(objectItem.id, true)) {
        this._lastHighlightDevice = objectItem;
      }
    }

    if (objectItem.shape)
      this._mapCoreManager.animateTo(
        getShapeCenter(objectItem.shape),
        hostApp().appConfig.animateZoomLevel,
      );
  }

  private resetHighlightLayer() {
    if (!this._highlightLayer) return;
    this._highlightLayer.clearHoverObject();
    if (this._lastHighlightDevice) {
      this._overlayCollection.removeOverlay(this._lastHighlightDevice.id);
      this._lastHighlightDevice = undefined;
    }
  }

  selectRoad(title: string, shape: Array<string>) {
    this._mapCoreManager.animateTo(
      getShapesCenter(shape),
      hostApp().appConfig.animateZoomLevel,
    );

    this._selectedItems = [];
    this._selectLayer?.clearSelectedObjects();

    let index = 0;
    shape.forEach((item) => {
      const modelObject = new ModelObject(
        'road',
        `${title}_${index}`,
        item,
        this.curDb,
      );
      index += 1;
      this._selectLayer?.addSelectedObject(modelObject);
    });

    const roadObject: GisObject = new GisObject('road', '道路', title, '', [
      ['名称', title],
    ]);
    this._selectedItems.push({
      object: roadObject,
    });
    this.updateSelectionItems();
  }

  private getDevice(feature: Feature): Device | undefined {
    const id: string = feature.get('id') as string;
    if (id) return this.curDb.getDeviceById(id);
    return undefined;
  }

  setLayerVisible(layerName: string, visible: boolean): void {
    this._layerManager?.setLayerVisible(layerName, visible);
  }

  setTheme(theme: Theme): void {
    this._layerManager?.setTheme(theme);
  }

  zoomToShapes(shapes: string[]) {
    if (shapes.length === 1) {
      this._mapCoreManager.moveToCenter(getShapeCenter(shapes[0]));
    } else {
      const extent: Extent = getShapesExtent(shapes, 3);
      this._mapCoreManager.fitToGeometry(extent);
    }
  }

  setEditDrawType(drawType: DrawType) {
    this._editLayer?.setDrawType(drawType);
  }

  convertObjectsByGIS(
    originObjects?: { otype: string; oname: string; [index: string]: any }[],
  ): { otype: string; oname: string; shape: string | undefined }[] {
    const data: { otype: string; oname: string; shape: string | undefined }[] =
      [];
    const objects = originObjects ?? this.selectionCollection.selectedObjects;
    objects.forEach((selectedObject) => {
      const { otype, oname, shape } = selectedObject;
      const id = makeObjectId(otype, oname);
      const foundObject = this.selectionCollection.selectedObjects.find(
        ({ otype, oname }) => id === makeObjectId(otype, oname),
      );
      data.push({
        otype:
          foundObject instanceof GisObject
            ? (foundObject.refModelObject?.otype ?? otype)
            : otype,
        oname:
          foundObject instanceof GisObject
            ? (foundObject.refModelObject?.oname ?? oname)
            : oname,
        shape:
          foundObject instanceof GisObject
            ? (foundObject.refModelObject?.shape ?? shape)
            : shape,
      });
    });
    return data;
  }

  updateCustomData(data: EventSchedulingBasicInfo[]) {
    // 使用 LayerManager 查找自定义图层并更新数据
    const customLayers = this._layerManager?.findLayersByType(MapCustomLayer);
    customLayers?.forEach((layer) => {
      layer.updateCustomData(data);
    });
  }

  addCustomData(data: EventSchedulingBasicInfo[]) {
    // 使用 LayerManager 查找自定义图层并添加数据
    const customLayers = this._layerManager?.findLayersByType(MapCustomLayer);
    customLayers?.forEach((layer) => {
      layer.addCustomData(data);
    });
  }
}
