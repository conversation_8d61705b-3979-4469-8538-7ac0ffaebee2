/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MOUSE_MEASURE_BOX,
  MOUSE_MEASURE_LINESTRING,
  MOUSE_MEASURE_POLYGON,
  MouseMode,
} from '@waterdesk/data/ui-types';
import { Feature, MapBrowserEvent, Overlay } from 'ol';
import { Coordinate } from 'ol/coordinate';
import { EventsKey } from 'ol/events';
import { Geometry, LineString, Polygon } from 'ol/geom';
import Draw, { createBox, DrawEvent, Options } from 'ol/interaction/Draw';
import VectorLayer from 'ol/layer/Vector';
import { EventTypes } from 'ol/Observable';
import VectorSource from 'ol/source/Vector';
import { getArea, getLength } from 'ol/sphere';
import { Fill, Stroke, Style } from 'ol/style';
import CircleStyle from 'ol/style/Circle';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import { selectMouseMode } from 'src/store/base/selectors';
import { measurePolygonActions } from 'src/store/measure-polygon';
import MapView from './map-view';

const helpTooltipElement: HTMLElement = document.createElement('div');

const helpTooltip: Overlay = new Overlay({
  element: helpTooltipElement,
  offset: [15, 0],
  stopEvent: false,
});

export default function useMeasureTool() {
  const dispatch = useDispatch();
  const mouseMode = useSelector(selectMouseMode);
  const mapView = useRef<MapView>(undefined);
  const measureTooltipArray = useRef<Overlay[]>([]);
  const mapEvent = useRef<EventsKey | undefined>(undefined);
  const source = useRef<VectorSource>(new VectorSource());
  const measureLayer = useRef<VectorLayer>(
    new VectorLayer({
      source: source.current,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)',
        }),
        stroke: new Stroke({
          color: '#ffcc33',
          width: 2,
        }),
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: '#ffcc33',
          }),
        }),
      }),
      zIndex: 2,
    }),
  );
  const drawObject = useRef<Draw>(
    new Draw({
      type: 'LineString', // 'LineString',
      source: source.current,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.8)',
        }),
        stroke: new Stroke({
          color: 'rgba(0, 0, 0, 0.5)',
          lineDash: [10, 10],
          width: 2,
        }),
        image: new CircleStyle({
          radius: 5,
          stroke: new Stroke({
            color: 'rgba(0, 0, 0, 0.7)',
          }),
          fill: new Fill({
            color: 'rgba(255, 255, 255, 0.2)',
          }),
        }),
      }),
    }),
  );

  let sketch: Feature = new Feature();
  const continuePolygonMsg: string = '继续点击绘制多边形';
  const continueLineMsg: string = '继续点击绘制线';
  let measureTooltipElement: HTMLElement;
  let measureTooltip: Overlay;

  const createHelpTooltip = () => {
    helpTooltipElement.className = 'ol-tooltip hidden';
    mapView.current?.map?.addOverlay(helpTooltip);
  };

  const createMeasureTooltip = () => {
    measureTooltipElement = document.createElement('div');
    measureTooltipElement.className =
      'tooltip-wrap tooltip-static tooltip-measure';
    measureTooltip = new Overlay({
      element: measureTooltipElement,
      offset: [-20, -40],
    });
    measureTooltipElement.className =
      'tooltip-wrap tooltip-static tooltip-measure';
    measureTooltipArray.current.push(measureTooltip);
    mapView.current?.map?.addOverlay(measureTooltip);
  };

  const formatLength = (line: LineString) => {
    // 获取投影坐标系
    const sourceProj = mapView.current?.map.getView().getProjection();
    const length = getLength(line, { projection: sourceProj });
    let output;
    if (length > 100) {
      output = `${Math.round((length / 1000) * 100) / 100} km`;
    } else {
      output = `${Math.round(length * 100) / 100} m`;
    }
    return output;
  };

  const formatArea = (polygon: Polygon) => {
    // 获取投影坐标系
    const sourceProj = mapView.current?.map.getView().getProjection();
    const area = getArea(polygon, { projection: sourceProj });
    let output;
    if (area > 10000) {
      output = `${Math.round((area / 1000000) * 100) / 100} km<sup>2</sup>`;
    } else {
      output = `${Math.round(area * 100) / 100} m<sup>2</sup>`;
    }
    return output;
  };

  const onDrawStart = (event: DrawEvent) => {
    sketch = event.feature;

    let tooltipCoord: Coordinate = [0, 0];

    if (sketch?.getGeometry()) {
      sketch.getGeometry()?.on('change', (evt) => {
        const geometry = evt.target;
        let output;
        if (geometry instanceof Polygon) {
          output = formatArea(geometry);
          tooltipCoord = geometry.getInteriorPoint().getCoordinates();
        } else if (geometry instanceof LineString) {
          output = formatLength(geometry);

          tooltipCoord = geometry.getLastCoordinate();
        }
        measureTooltipElement.innerHTML = output ?? '';
        measureTooltip?.setPosition(tooltipCoord);
      });
    }
  };

  const onDrawEnd = () => {
    measureTooltipElement.className =
      'tooltip-wrap tooltip-static tooltip-measure';
    measureTooltip?.setOffset([-25, -35]);
    if (
      mouseMode === MOUSE_MEASURE_POLYGON ||
      mouseMode === MOUSE_MEASURE_BOX
    ) {
      const geomtry: Geometry | undefined = sketch.getGeometry();
      if (geomtry instanceof Polygon) {
        const coordinates = geomtry.getCoordinates();
        if (coordinates.length > 0) {
          dispatch(
            measurePolygonActions.updatePolygon({
              polygon: coordinates[0],
            }),
          );
        }
      }
    }
    sketch = new Feature();
    createMeasureTooltip();
    helpTooltipElement?.classList.add('hidden');
  };

  const addInteraction = () => {
    mapView.current?.map.addInteraction(drawObject.current);
    createMeasureTooltip();
    createHelpTooltip();
    drawObject.current.on('drawstart', (e) => {
      onDrawStart(e);
    });
    drawObject.current.on('drawend', onDrawEnd);
  };

  const pointerMoveHandler = (evt: MapBrowserEvent<any>) => {
    if (evt.dragging) {
      return;
    }
    let mousePointorMessage = '点击开始绘制';
    if (sketch) {
      const geometry = sketch.getGeometry();
      if (geometry instanceof Polygon) {
        mousePointorMessage = continuePolygonMsg;
      } else if (geometry instanceof LineString) {
        mousePointorMessage = continueLineMsg;
      }
    }
    if (helpTooltipElement) {
      helpTooltipElement.innerHTML = mousePointorMessage;
      helpTooltipElement.classList.remove('hidden');
    }

    helpTooltip.setPosition(evt.coordinate);
  };

  const getDrawOptions = (type: MouseMode): Options | undefined => {
    switch (type) {
      case MOUSE_MEASURE_LINESTRING:
        return {
          type: 'LineString',
        };
      case MOUSE_MEASURE_POLYGON:
        return {
          type: 'Polygon',
        };
      case MOUSE_MEASURE_BOX:
        return {
          type: 'Circle',
          geometryFunction: createBox(),
        };
      default:
        return undefined;
    }
  };

  const exitMeasure = () => {
    const map = mapView?.current?.map;
    if (!map) return;

    map.removeInteraction(drawObject.current);
    measureTooltipArray.current.forEach((overlay) => {
      if (overlay) {
        map.removeOverlay(overlay);
      }
    });
    if (helpTooltip) {
      map.removeOverlay(helpTooltip);
    }
    map.removeLayer(measureLayer.current);
    if (mapEvent.current) {
      map.un(mapEvent.current.type as EventTypes, mapEvent.current.listener);
    }
  };

  const startMeasure = (
    drawType: Options['type'],
    geometryFunction?: Options['geometryFunction'],
  ) => {
    const map = mapView?.current?.map;
    if (!map) return;

    exitMeasure();
    const mapViewWrap = map.getViewport() as HTMLElement;
    mapViewWrap.className = 'ol-viewport cursorCell';
    source.current.clear();
    drawObject.current = new Draw({
      type: drawType,
      source: source.current,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.8)',
        }),
        stroke: new Stroke({
          color: 'rgba(0, 0, 0, 0.5)',
          lineDash: [10, 10],
          width: 2,
        }),
        image: new CircleStyle({
          radius: 5,
          stroke: new Stroke({
            color: 'rgba(0, 0, 0, 0.7)',
          }),
          fill: new Fill({
            color: 'rgba(255, 255, 255, 0.2)',
          }),
        }),
      }),
      geometryFunction,
    });
    map.addLayer(measureLayer.current);
    mapEvent.current = map.on('pointermove', pointerMoveHandler);
    map.getViewport().addEventListener('mouseout', () => {
      if (helpTooltipElement) {
        helpTooltipElement.classList.add('hidden');
      }
    });

    // 量测调用
    addInteraction();
  };

  useEffect(() => {
    if (!mapView.current) {
      mapView.current = hostApp().getMainMapView();
    }
    const drawTypeOptions = getDrawOptions(mouseMode);
    if (drawTypeOptions) {
      startMeasure(drawTypeOptions.type, drawTypeOptions.geometryFunction);
    } else {
      exitMeasure();
    }
    mapView.current?.setMouseMode(mouseMode);
  }, [mouseMode]);
}
