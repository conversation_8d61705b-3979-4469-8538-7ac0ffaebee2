/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FeatureLayerData } from '@waterdesk/data/layer-data';
import { getCurrentDeviceColor } from '@waterdesk/data/legend-data';
import { validateShape } from '@waterdesk/data/object-item';
import { Feature } from 'ol';
import { Extent } from 'ol/extent';
import WKT from 'ol/format/WKT';
import { default as OlMap } from 'ol/Map';
import Overlay, { Positioning } from 'ol/Overlay';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import {
  collisionDetect,
  LabelRect,
  outsideScreen,
} from 'src/components/label/label-collision-detect';
import store from 'src/store/configure-store';
import { UserConfigState } from 'src/store/user-config/types';
import {
  AntdThemeProvider,
  StyledThemeProvider,
} from 'src/styles/theme/theme-provider-adapter';
import OverlayContainer, {
  OverlayProperties,
} from '../overlay-container/scada-overlay-container';
import MapView from './map-view';

export default class OverlayCollection {
  private _overlayMap: Map<string, Overlay> = new Map();

  private _overlayElementMap: Map<string, Overlay> = new Map();

  private _displayOverlayIds: string[] = [];

  private _WKT: WKT = new WKT();

  private _map: OlMap | undefined;

  private _warnBlinkOverlay: Overlay = new Overlay({
    positioning: 'bottom-center',
  });

  private _showLabelTitleIsChecked: boolean = false;

  private _showIndicatorIsChecked: boolean = false;

  private _hoverElementId: string | undefined = undefined;

  static getCenterCoordinates(extent: Extent | undefined): [number, number] {
    if (extent) {
      const positionCount = extent.length / 2;
      let xSum = 0;
      let ySum = 0;
      extent.forEach((item, index) => {
        if (index % 2 === 0) {
          xSum += item;
        } else {
          ySum += item;
        }
      });
      return [xSum / positionCount, ySum / positionCount];
    }
    return [0, 0];
  }

  private _mapView: MapView;

  private static updateOverlayStyle(deviceId: string, overlay: Overlay) {
    const scadaOverlayDom: HTMLElement | undefined = overlay.getElement();
    const overLayWrapTitle = scadaOverlayDom?.querySelector(
      '.overlay-wrap-title',
    ) as HTMLDivElement | undefined;
    if (overLayWrapTitle) {
      overLayWrapTitle.style.backgroundColor =
        getCurrentDeviceColor(deviceId) ?? '#4c6ad7';
    }
    const overLayScadaOverlay = scadaOverlayDom?.querySelector(
      '.overlayer-simple-mode',
    ) as HTMLDivElement | undefined;
    if (overLayScadaOverlay) {
      overLayScadaOverlay.style.backgroundColor =
        getCurrentDeviceColor(deviceId) ?? '#4c6ad7';
    }
  }

  private querySelectorAll(
    selectors: string,
  ): NodeListOf<HTMLDivElement> | undefined {
    if (this._map) {
      return this._map
        .getViewport()
        .querySelectorAll<HTMLDivElement>(selectors);
    }
    return undefined;
  }

  private querySelector(
    deviceId: string,
    selector: string,
  ): HTMLDivElement | undefined {
    if (this._map) {
      return this._map
        .getViewport()
        .getElementsByClassName(deviceId)[0]
        ?.getElementsByClassName(selector)[0] as HTMLDivElement;
    }
    return undefined;
  }

  private toggleElementsVisible(selectors: string, visible: boolean) {
    if (this._map) {
      const elements = this.querySelectorAll(selectors);
      elements?.forEach((element) => {
        const { style } = element;
        style.display = visible ? 'block' : 'none';
      });
    }
  }

  updateLabelTitleStyle(visible: boolean) {
    this.toggleElementsVisible('.overlay-wrap-title', visible);
  }

  updateIndicatorVisible(visible: boolean) {
    this.toggleElementsVisible('.overlay-wrap-targets', visible);
  }

  private toggleElementVisible(
    deviceId: string,
    selector: string,
    visible: boolean,
  ) {
    if (this._map) {
      const overlayElement = this.querySelector(deviceId, selector);
      if (overlayElement) {
        const { display } = overlayElement.style;
        if (visible && display === 'none') {
          overlayElement.style.display = 'block';
        } else if (!visible && display === 'block') {
          overlayElement.style.display = 'none';
        }
      }
    }
  }

  private toggleLabelTitleVisibleById(deviceId: string, visible: boolean) {
    this.toggleElementVisible(deviceId, 'overlay-wrap-title', visible);
  }

  private toggleIndicatorVisibleById(deviceId: string, visible: boolean) {
    this.toggleElementVisible(deviceId, 'overlay-wrap-targets', visible);
  }

  toggleHoverElementVisible(visible: boolean, deviceId?: string) {
    const id = deviceId ?? this._hoverElementId;
    this._hoverElementId = deviceId;
    if (id) {
      if (!this._showLabelTitleIsChecked)
        this.toggleLabelTitleVisibleById(id, visible);

      if (!this._showIndicatorIsChecked)
        this.toggleIndicatorVisibleById(id, visible);
    }
  }

  constructor(mapView: MapView) {
    this._mapView = mapView;
  }

  enableMouseOnMap(mouseOnMap: boolean, overlay: Overlay) {
    this._mapView.setMouseOnMap(mouseOnMap);
    if (overlay) {
      if (!mouseOnMap) {
        OverlayCollection.upgradeOverlayZIndex(overlay, '9');
      } else {
        OverlayCollection.resetOverlayZIndex(overlay);
      }
    }
  }

  static selectObject(
    otype: string,
    oname: string,
    append: boolean,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
  ) {
    const mapViews = hostApp().getMapViews();
    mapViews?.forEach((mapView) => {
      mapView.selectAndHighlight(
        otype,
        oname,
        undefined,
        append,
        indicatorType,
        indicatorName,
        vprop,
      );
    });
  }

  private static getOffset(position: Positioning): number[] | undefined {
    switch (position) {
      case 'top-center':
        return [0, 10];
      case 'bottom-center':
        return [0, -10];
      case 'center-left':
        return [10, 0];
      case 'center-right':
        return [-10, 0];
      default:
        return undefined;
    }
  }

  initializeOverlays(layerData: FeatureLayerData) {
    if (!this._map) return;

    layerData.devices.forEach((device) => {
      if (validateShape(device.shape)) {
        const deviceExtent = this._WKT
          .readFeature(device.shape)
          .getGeometry()
          ?.getExtent();
        const position = OverlayCollection.getCenterCoordinates(deviceExtent);
        const globalConfig = (
          store.getState()?.userConfig as UserConfigState | undefined
        )?.globalConfig;
        const maxVisibleIndicators =
          globalConfig?.bubbleBoxConfig?.maxVisibleIndicators ?? 5;
        const indicatorLength =
          device.overlayIndicators.length > maxVisibleIndicators
            ? maxVisibleIndicators
            : device.overlayIndicators.length;
        const overlayProperties: OverlayProperties = {
          id: device.id,
          device,
          x: 0,
          y: 0,
          width: 100,
          height: indicatorLength * 22.4 + 35,
          priority: device.displayLevel,
          visible: true,
        };
        const deviceOverlay = new Overlay({
          id: device.id,
          position,
          positioning: device.overlayPositioning,
          offset: OverlayCollection.getOffset(device.overlayPositioning),
          stopEvent: false,
          className: `ol-overlay-container ol-selectable zIndex${device.displayLevel}`,
        });

        deviceOverlay.setProperties(overlayProperties);
        this._overlayMap.set(device.id, deviceOverlay);
      }
    });
  }

  createOverlayElementById(deviceId: string) {
    const overlay = this._overlayMap.get(deviceId);
    if (overlay === undefined) return;

    const overlayProperties = overlay.getProperties() as OverlayProperties;
    const overlayContainer = (
      <OverlayContainer
        mapViewName={this._mapView.mapViewName}
        overlayProperties={overlayProperties}
        onMouseOver={() => this.enableMouseOnMap(false, overlay)}
        onMouseOut={() => this.enableMouseOnMap(true, overlay)}
        selectObject={(
          otype: string,
          oname: string,
          append: boolean,
          indicatorType?: string,
          indicatorName?: string,
          vprop?: string,
        ) =>
          OverlayCollection.selectObject(
            otype,
            oname,
            append,
            indicatorType,
            indicatorName,
            vprop,
          )
        }
      />
    );
    const popup = document.createElement('div');
    const root = createRoot(popup);
    root.render(
      <Provider store={store}>
        <AntdThemeProvider>
          <StyledThemeProvider>{overlayContainer}</StyledThemeProvider>
        </AntdThemeProvider>
      </Provider>,
    );
    overlay.setElement(popup);
    this._overlayElementMap.set(deviceId, overlay);
  }

  setOverlayVisibleById(deviceId: string, visible: boolean) {
    const overlayElement = this._overlayElementMap.get(deviceId);
    if (overlayElement) {
      overlayElement.setProperties({
        visible,
      });
    }
  }

  clearOverlay() {
    this._displayOverlayIds.forEach((overlayId) => {
      const overlayElement = this._overlayMap.get(overlayId);
      if (overlayElement === undefined) return;
      this._map?.removeOverlay(overlayElement);
    });
    this._displayOverlayIds = [];
    this._overlayElementMap.forEach((overlay) => {
      OverlayCollection.resetOverlayZIndex(overlay);
      overlay.setProperties({
        visible: false,
      });
    });
  }

  displayOverlay() {
    if (!this._map) {
      return;
    }
    const labels: LabelRect[] = [];
    const globalConfig = (
      store.getState()?.userConfig as UserConfigState | undefined
    )?.globalConfig;
    const labelMode = (
      store.getState()?.userConfig as UserConfigState | undefined
    )?.labelMode;
    this._displayOverlayIds.forEach((overlayId) => {
      const overlayElement = this._overlayMap.get(overlayId);
      if (overlayElement === undefined) return;

      const properties = overlayElement.getProperties();
      const pixelFromCoordinate = this._map?.getPixelFromCoordinate(
        properties.position as number[],
      ) as number[];
      if (properties.visible) {
        const width = globalConfig?.bubbleBoxConfig?.width ?? properties.width;
        labels.push({
          id: properties.id,
          x: pixelFromCoordinate ? pixelFromCoordinate[0] : 0,
          y: pixelFromCoordinate ? pixelFromCoordinate[1] : 0,
          width: labelMode ? 50 : width,
          height: labelMode ? 32 : properties.height,
          priority: properties.priority,
          visible: properties.visible,
          positioning: properties.device.overlayPositioning,
        });
      }
    });
    if (labels.length > 0) {
      const disableCollisionDetect =
        globalConfig?.bubbleBoxConfig?.disableCollisionDetect;
      const { clientWidth } = this._map.getViewport();
      const { clientHeight } = this._map.getViewport();

      const overlayKeys = disableCollisionDetect
        ? labels
            .filter((item) => !outsideScreen(item, clientWidth, clientHeight))
            .map((item) => item.id)
        : collisionDetect(labels, clientWidth, clientHeight);
      overlayKeys.forEach((overlayKey) => {
        const overlay = this._overlayMap.get(overlayKey);
        if (overlay) {
          const overlayElement = this._overlayElementMap.get(overlayKey);
          if (!overlayElement) {
            this.createOverlayElementById(overlayKey);
          }
          OverlayCollection.updateOverlayStyle(overlayKey, overlay);
          this._map?.addOverlay(overlay);
        }
      });
    }
  }

  displayOverlays(features: Feature[]) {
    if (!this._map) {
      return;
    }
    features.forEach((feature) => {
      const deviceInfo = feature.getProperties();
      this._displayOverlayIds.push(deviceInfo.id);
      this.setOverlayVisibleById(deviceInfo.id, true);
    });
  }

  getOverlayByDeviceId(deviceId: string): Overlay | undefined {
    return this._overlayMap.get(deviceId);
  }

  forceShowOverlay(deviceId: string, hover: boolean): boolean {
    if (!this._map) return false;
    let overlayElement = this._overlayElementMap.get(deviceId);
    if (overlayElement === undefined) {
      this.createOverlayElementById(deviceId);
      overlayElement = this._overlayElementMap.get(deviceId);
    }

    if (overlayElement === undefined) return false;
    if (!this._map.getOverlays().getArray().includes(overlayElement)) {
      this._map?.addOverlay(overlayElement);
      const warningOverlay = this._map?.getOverlayById(deviceId);
      if (!warningOverlay) {
        this.setDisplayOverlayWarning(deviceId);
      }
      OverlayCollection.upgradeOverlayZIndex(overlayElement, hover ? '9' : '1');
      return true;
    }

    const { visible } = overlayElement.getProperties();

    if (visible) return false;
    this.setOverlayVisibleById(deviceId, true);
    return true;
  }

  removeOverlay(deviceId: string) {
    if (!this._map) return;
    const overlayElement = this._overlayElementMap.get(deviceId);
    if (overlayElement === undefined) return;
    OverlayCollection.resetOverlayZIndex(overlayElement);
    this._map.removeOverlay(overlayElement);
  }

  private static resetOverlayZIndex(overlay: Overlay) {
    const element = overlay.getElement();
    if (element?.parentElement) element.parentElement.style.zIndex = '0';
  }

  private static upgradeOverlayZIndex(overlay: Overlay, zIndex: string) {
    const element = overlay.getElement();
    if (element?.parentElement)
      element.parentElement.style.setProperty('z-index', zIndex, 'important');
  }

  setDisplayOverlayWarning(deviceId: string) {
    const overlayElement = this._overlayElementMap.get(deviceId);
    if (overlayElement === undefined) return;
    const element = overlayElement.getElement();
    if (element) {
      const warningTipDom = element
        .getElementsByClassName('warningTip')
        .item(0) as HTMLDivElement;
      if (warningTipDom) {
        warningTipDom.style.display = 'block ';
      }
    }
  }

  set olMap(map: OlMap) {
    this._map = map;
  }

  get overlaysMap(): Map<string, Overlay> {
    return this._overlayMap;
  }

  get warnBlinkOverlay(): Overlay {
    return this._warnBlinkOverlay;
  }

  set showLabelTitleIsChecked(checked: boolean) {
    this._showLabelTitleIsChecked = checked;
  }

  set showIndicatorIsChecked(checked: boolean) {
    this._showIndicatorIsChecked = checked;
  }
}
