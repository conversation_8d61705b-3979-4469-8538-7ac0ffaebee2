/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { Dispatch, UnknownAction } from 'redux';
import { baseActions } from 'src/store/base';
import { selectionActions } from '../../../store/selection';

interface SelectedItem {
  object: IObjectItem;
  indicatorType?: string;
  indicatorName?: string;
  vprop?: string;
}
export default class SelectionCollection {
  private _items: Array<SelectedItem> = [];

  private _chartObjects: IObjectItem[] = [];

  private _dispatch: Dispatch<UnknownAction> | undefined = undefined;

  constructor(dispatch?: Dispatch<UnknownAction>) {
    this._dispatch = dispatch;
  }

  clear() {
    this._items = [];
    this._chartObjects = [];
    this._dispatch?.(selectionActions.clearSelection());
  }

  setSelectionItems(
    items: Array<{
      object: IObjectItem;
      indicatorType?: string;
      indicatorName?: string;
      vprop?: string;
    }>,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
    noDispatch?: boolean,
  ) {
    this._chartObjects = [];
    this._items = items;
    if (noDispatch) return;
    if (indicatorType || indicatorName || vprop)
      this._dispatch?.(
        selectionActions.selectionAndPropertyChanged({
          items: items.map((item) => ({
            objectId: item.object.id,
            indicatorName: item.indicatorName,
            indicatorType: item.indicatorType,
            vprop: item.vprop,
          })),
          indicatorType,
          indicatorName,
          vprop,
        }),
      );
    else
      this._dispatch?.(
        selectionActions.selectionChanged({
          items: items.map((item) => ({
            objectId: item.object.id,
            indicatorName: item.indicatorName,
            indicatorType: item.indicatorType,
            vprop: item.vprop,
          })),
        }),
      );
    if (items.length > 0) {
      this._dispatch?.(baseActions.trySwitchToPropertyPalette());
    }
  }

  get chartObjects(): Array<SelectedItem> {
    if (this._chartObjects && this._chartObjects.length !== 0)
      return this._chartObjects.map((item) => ({
        object: item,
      }));
    return this._items;
  }

  setChartObjects(objects: IObjectItem[]) {
    this._chartObjects = objects;
  }

  get selectedObjects(): Array<IObjectItem> {
    const items: Map<string, IObjectItem> = new Map();
    this._items.forEach((item) => {
      if (items.get(item.object.id)) return;
      items.set(item.object.id, item.object);
    });
    return Array.from(items.values());
  }

  get selectedItems(): Array<SelectedItem> {
    return this._items;
  }

  get firstSelectedObject(): IObjectItem | undefined {
    if (this._items.length === 0) return undefined;
    return this._items[0].object;
  }

  get firstSelectedItem(): SelectedItem | undefined {
    if (this._items.length === 0) return undefined;
    return this._items[0];
  }

  get lastSelectedObject(): IObjectItem | undefined {
    return this._items[this._items.length - 1].object;
  }

  get lastSelectedItem(): SelectedItem | undefined {
    return this._items[this._items.length - 1];
  }

  get firstChartObject(): IObjectItem | undefined {
    if (this._chartObjects && this._chartObjects.length !== 0)
      return this._chartObjects[0];
    if (this._items.length === 0) return undefined;
    return this._items[0].object;
  }

  get firstChartObjectItem(): SelectedItem | undefined {
    if (this._chartObjects && this._chartObjects.length !== 0)
      return {
        object: this._chartObjects[0],
      };
    if (this._items.length === 0) return undefined;
    return this._items[0];
  }

  private static selectionItemsChanged(
    items1: Array<IObjectItem>,
    items2: Array<IObjectItem>,
  ): boolean {
    if (items1.length !== items2.length) return true;

    for (let i: number = 0; i < items1.length; i += 1) {
      if (items1[i].id !== items2[i].id) return true;
    }

    return false;
  }
}
