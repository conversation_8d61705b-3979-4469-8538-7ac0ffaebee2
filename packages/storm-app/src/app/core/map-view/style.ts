/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled, { css, keyframes } from 'styled-components';

interface BlinkingCircleProps {
  radius: number;
  duration: number;
  bgColor?: string;
}

const blinkAnimate = () => keyframes`
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.8;
  }
  75% {
    transform: scale(0.75);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
`;

export const BlinkingCircle = styled.div<BlinkingCircleProps>`
  width: ${({ radius }) => radius}px;
  height: ${({ radius }) => radius}px;
  border-radius: 50%;
  &::before {
    content: '';
    display: block;
    border-radius: 50%;
    width: ${({ radius }) => radius}px;
    height: ${({ radius }) => radius}px;
    opacity: 0.8;
    ${({ bgColor }) =>
      bgColor &&
      css`
        background-color: ${bgColor};
      `}
    animation: ${({ duration }) => css`
      ${blinkAnimate} ${duration}s linear infinite
    `};
    transform: translateZ(0);
  }
`;
