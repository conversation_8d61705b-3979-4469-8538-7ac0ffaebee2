/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MOUSE_EDIT_ADDJUNCTION,
  MOUSE_EDIT_ADDPIPE,
  MOUSE_EDIT_ADDSINGLEPIPE,
  MOUSE_EDIT_DELETEPIPE,
  MouseMode,
} from '@waterdesk/data/ui-types';
import { MapBrowserEvent, Overlay } from 'ol';
import { EventsKey } from 'ol/events';
import { EventTypes } from 'ol/Observable';
import { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import { selectMouseMode } from 'src/store/base/selectors';
import { useSelectionSlice } from 'src/store/selection';
import { EditType } from '../map-layer/map-edit-layer';
import MapView from './map-view';

const helpTooltipElement: HTMLElement = document.createElement('div');

const helpTooltip: Overlay = new Overlay({
  element: helpTooltipElement,
  offset: [15, 0],
  stopEvent: false,
});

export default function useEditTool() {
  useSelectionSlice();
  const mouseMode = useSelector(selectMouseMode);
  const mapView = useRef<MapView>(undefined);
  const mapEvent = useRef<EventsKey | undefined>(undefined);

  const createHelpTooltip = () => {
    helpTooltipElement.className = 'ol-tooltip hidden';
    mapView.current?.map?.addOverlay(helpTooltip);
  };

  const getMessage = (mouseMode: MouseMode): string => {
    if (mouseMode === MOUSE_EDIT_ADDPIPE) return '按回车结束绘制';
    if (mouseMode === MOUSE_EDIT_DELETEPIPE) return '请选择要删除的物体';
    return '';
  };

  const pointerMoveHandler = (
    evt: MapBrowserEvent<any>,
    mouseMode: MouseMode,
  ) => {
    if (evt.dragging) {
      return;
    }
    if (helpTooltipElement) {
      helpTooltipElement.innerHTML = getMessage(mouseMode);
      helpTooltipElement.classList.remove('hidden');
    }

    helpTooltip.setPosition(evt.coordinate);
  };

  const removeEvent = () => {
    if (mapEvent.current) {
      mapView?.current?.map?.un(
        mapEvent.current.type as EventTypes,
        mapEvent.current.listener,
      );
    }
  };

  useEffect(() => {
    createHelpTooltip();
  }, [mapView.current]);

  useEffect(() => {
    if (!mapView.current) {
      mapView.current = hostApp().getMainMapView();
    }
    const _map = mapView.current?.map;
    removeEvent();
    mapEvent.current = _map?.on('pointermove', (event: MapBrowserEvent<any>) =>
      pointerMoveHandler(event, mouseMode),
    );

    switch (mouseMode) {
      case MOUSE_EDIT_ADDJUNCTION:
        mapView.current?.startEditNetwork(EditType.POINT);
        break;
      case MOUSE_EDIT_ADDPIPE:
        mapView.current?.startEditNetwork(EditType.LINESTRING);
        break;
      case MOUSE_EDIT_ADDSINGLEPIPE:
        mapView.current?.startEditNetwork(EditType.SINGLELINESTRING);
        break;
      case MOUSE_EDIT_DELETEPIPE:
        mapView.current?.startEditNetwork(EditType.DELETEPIPE);
        break;
      default:
        mapView.current?.endEditNetwork();
        break;
    }
    mapView.current?.setMouseMode(mouseMode);
  }, [mouseMode]);
}
