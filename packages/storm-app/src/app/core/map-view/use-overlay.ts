/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import {
  selectLabelIndicator,
  selectLabelSimulation,
  selectLabelTitle,
} from 'src/store/user-config/selector';

export default function useOverlay() {
  const labelTitle = useSelector(selectLabelTitle);
  const labelIndicator = useSelector(selectLabelIndicator);
  const labelSimulation = useSelector(selectLabelSimulation);
  useEffect(() => {
    const mapView = hostApp().getMainMapView();
    mapView?.overlayCollection.overlaysMap.forEach((overlay) => {
      if (overlay) {
        const overlayDom: HTMLElement | undefined = overlay.getElement();
        const labelTitleDom = overlayDom?.getElementsByClassName(
          'overlay-wrap-title',
        )[0] as HTMLElement;
        if (labelTitleDom) {
          labelTitleDom.style.display = labelTitle ? 'block' : 'none';
        }
      }
    });
  }, [labelTitle]);

  useEffect(() => {
    const mapView = hostApp().getMainMapView();
    mapView?.overlayCollection.overlaysMap.forEach((overlay) => {
      if (overlay) {
        const overlayDom: HTMLElement | undefined = overlay.getElement();
        const labelIndicatorDom = overlayDom?.getElementsByClassName(
          'overlay-wrap-targets',
        )[0] as HTMLElement;
        if (labelIndicatorDom) {
          labelIndicatorDom.style.display = labelIndicator ? 'block' : 'none';
        }
      }
    });
  }, [labelIndicator]);

  useEffect(() => {
    const mapView = hostApp().getMainMapView();
    mapView?.overlayCollection.overlaysMap.forEach((overlay) => {
      if (overlay) {
        const overlayDom: HTMLElement | undefined = overlay.getElement();
        if (overlayDom) {
          const allLabelSimulationDoms: NodeListOf<HTMLElement> =
            overlayDom.querySelectorAll('.calculate');
          allLabelSimulationDoms.forEach((item) => {
            const labelSimulationDom = item;
            labelSimulationDom.style.display = labelSimulation
              ? 'block'
              : 'none';
          });
        }
      }
    });
  }, [labelSimulation]);
}
