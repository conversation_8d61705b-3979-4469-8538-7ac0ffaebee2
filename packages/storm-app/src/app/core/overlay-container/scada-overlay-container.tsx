/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { AppMode } from '@waterdesk/data/app-config';
import { MapViewName } from '@waterdesk/data/const/map';
import Device, { StationDevice } from '@waterdesk/data/device';
import { ScadaModelTimeData } from '@waterdesk/data/device-time-data';
import { getCurrentDeviceColor } from '@waterdesk/data/legend-data';
import { platformModifierKeyOnly } from '@waterdesk/data/selection-data';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { Button, Col, Row, Tooltip, Typography } from 'antd';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import ScadaLabel from 'src/components/scada-label';
import { baseActions, useBaseSlice } from 'src/store/base';
import {
  selectAppMode,
  selectCurrentTimeDataChanged,
  selectHideFeatureTooltipImportant,
  selectNoticeWarnList,
} from 'src/store/base/selectors';
import { BottomTabKey } from 'src/store/base/types';
import { selectTimelineDate } from 'src/store/time-line/selectors';
import { useUserConfigSlice } from 'src/store/user-config';
import {
  selectAppLabelIndicator,
  selectAppLabelMode,
  selectAppLabelSimulation,
  selectAppLabelTitle,
  selectGlobalConfig,
  selectLabelIndicator,
  selectLabelMode,
  selectLabelSimulation,
  selectLabelTitle,
} from 'src/store/user-config/selector';
import ScadaWarningContainer from '../scada-label/warning';
import { ScadaOverlayWrapper } from './style';

const { Text } = Typography;

export interface OverlayProperties {
  id: string;
  device: Device;
  x: number;
  y: number;
  width: number;
  height: number;
  priority: number;
  visible: boolean;
}

interface Props {
  mapViewName: MapViewName;
  overlayProperties: OverlayProperties;
  onMouseOver: () => void;
  onMouseOut: () => void;
  selectObject: (
    otype: string,
    oname: string,
    append: boolean,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
  ) => void;
}

export default function OverlayContainer(props: Readonly<Props>) {
  useBaseSlice();
  useUserConfigSlice();

  const {
    mapViewName,
    overlayProperties,
    onMouseOver,
    onMouseOut,
    selectObject,
  } = props;
  const { device } = overlayProperties;

  const dispatch = useDispatch();
  const currentTimeDataChanged = useSelector(selectCurrentTimeDataChanged);
  const webLabelSimulation = useSelector(selectLabelSimulation);
  const webLabelIndicator = useSelector(selectLabelIndicator);
  const timelineDate = useSelector(selectTimelineDate);
  const noticeWarnList = useSelector(selectNoticeWarnList);
  const webLabelTitle = useSelector(selectLabelTitle);
  const webLabelMode = useSelector(selectLabelMode);
  const appMode = useSelector(selectAppMode);
  const appLabelSimulation = useSelector(selectAppLabelSimulation);
  const appLabelIndicator = useSelector(selectAppLabelIndicator);
  const appLabelTitle = useSelector(selectAppLabelTitle);
  const appLabelMode = useSelector(selectAppLabelMode);
  const labelSimulation =
    appMode === AppMode.MOBILE ? appLabelSimulation : webLabelSimulation;
  const labelIndicator =
    appMode === AppMode.MOBILE ? appLabelIndicator : webLabelIndicator;
  const labelTitle = appMode === AppMode.MOBILE ? appLabelTitle : webLabelTitle;
  const labelMode = appMode === AppMode.MOBILE ? appLabelMode : webLabelMode;
  const hideFeatureTooltipImportant = useSelector(
    selectHideFeatureTooltipImportant,
  );
  const globalConfig = useSelector(selectGlobalConfig);

  const [showAllIndicators, setShowAllIndicators] = useState(false);

  const toggleIndicators = () => {
    setShowAllIndicators(!showAllIndicators);
  };

  const bubbleBoxConfig = useMemo(
    () => globalConfig?.bubbleBoxConfig,
    [globalConfig],
  );

  const handleClickTitle = (e: MouseEvent) => {
    selectObject(device.otype, device.oname, platformModifierKeyOnly(e));
  };

  const handleClickIndicator = (
    indicatorType: string,
    indicatorName: string,
    e: MouseEvent,
  ) => {
    selectObject(
      device.otype,
      device.oname,
      platformModifierKeyOnly(e),
      indicatorType,
      indicatorName,
      'SDVAL',
    );
    dispatch(
      baseActions.updateBottomTab({
        activeKey: BottomTabKey.CHARTS,
        type: 'ADD',
      }),
    );
  };

  const getScadaDataCell = (id: string): ScadaModelTimeData | undefined => {
    const currentData =
      curDb(mapViewName).currentDeviceTimeData.getIndicatorValueById(id);
    if (currentData) {
      return currentData;
    }
    return curDb(undefined, false).currentDeviceTimeData.getIndicatorValueById(
      id,
    );
  };

  const getPumpState = (): ReactNode => {
    if (device instanceof StationDevice) {
      const pumpData = device.pumpList;
      return (
        <div
          style={{
            width: '100%',
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, 17px)',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {pumpData.map((item) => {
            const { onOffIndicator } = item;
            if (onOffIndicator) {
              return (
                <div
                  key={onOffIndicator.id}
                  className="scada-value-wrap"
                  onClick={(e) => {
                    handleClickIndicator(
                      onOffIndicator.otype,
                      onOffIndicator.oname,
                      e as unknown as MouseEvent,
                    );
                  }}
                  tabIndex={0}
                  role="button"
                  onFocus={() => {}}
                  onMouseOver={onMouseOver}
                  onMouseOut={onMouseOut}
                  onBlur={() => {}}
                  onKeyDown={() => {}}
                >
                  <ScadaLabel
                    id={onOffIndicator.id}
                    dataType="pumpData"
                    pumpInfo={item}
                    pumpStateColor={hostApp().appConfig.pumpStateColor}
                    title={onOffIndicator?.title}
                    getScadaDataCell={getScadaDataCell}
                    currentTimeDataChanged={currentTimeDataChanged}
                  />
                </div>
              );
            }
            return '';
          })}
        </div>
      );
    }
    return '';
  };

  useEffect(() => {
    const overlayCollection = hostApp().getMainMapView()?.overlayCollection;
    if (overlayCollection) {
      overlayCollection.showLabelTitleIsChecked = labelTitle;
      overlayCollection.showIndicatorIsChecked = labelIndicator;
    }
  }, [labelTitle, labelIndicator]);

  const sortedIndicators = [...device.overlayIndicators].sort((a, b) => {
    const orderA =
      bubbleBoxConfig?.indicatorConfig?.find((ic) => ic.otype === a.otype)
        ?.order ?? 0;
    const orderB =
      bubbleBoxConfig?.indicatorConfig?.find((ic) => ic.otype === b.otype)
        ?.order ?? 0;
    return orderB - orderA;
  });

  const maxVisibleIndicators = bubbleBoxConfig?.maxVisibleIndicators ?? 5;

  const displayIndicators = useMemo(
    () =>
      showAllIndicators
        ? sortedIndicators
        : sortedIndicators.slice(0, maxVisibleIndicators),
    [showAllIndicators, sortedIndicators, bubbleBoxConfig],
  );

  const getSimpleOverContent = (): ReactNode => {
    if (
      typeof hideFeatureTooltipImportant === 'boolean' &&
      !hideFeatureTooltipImportant
    )
      return null;
    return (
      <div
        className={`${device.id} overlayer-target overlayer-simple-mode`}
        style={{
          backgroundColor: getCurrentDeviceColor(device.id) ?? '#000',
        }}
        onMouseOver={onMouseOver}
        onFocus={() => {}}
        onMouseOut={onMouseOut}
        onBlur={() => {}}
      >
        <div
          className="ant-popover-title overlay-wrap-title"
          style={{ display: 'none' }}
        >
          <Tooltip title={device.title}>
            <div
              className="scada-overlay-title"
              role="button"
              tabIndex={0}
              onClick={(e) => {
                handleClickTitle(e as unknown as MouseEvent);
              }}
              onKeyDown={() => {}}
            >
              {device.shortTitle}
            </div>
          </Tooltip>
        </div>
        {device.overlayIndicators.map((item) => {
          const { id, otype, oname } = item;
          if (otype === labelMode) {
            const [, unitKey] = curDb().getPropertyTitleUnit(otype, 'SDVAL');
            const unit = unitKey ? getUnitFormat(unitKey) : undefined;
            return (
              <Row
                key={id}
                wrap={false}
                className={`scadaValueWrap${id}`}
                onClick={(e) => {
                  handleClickIndicator(
                    otype,
                    oname,
                    e as unknown as MouseEvent,
                  );
                }}
                onMouseOver={onMouseOver}
                onFocus={() => {}}
                onMouseOut={onMouseOut}
                onBlur={() => {}}
                title={unit?.valueTitle}
              >
                <Col>
                  <ScadaLabel
                    style={{ color: '#fff' }}
                    id={id}
                    dataType="scadaData"
                    title={item?.title}
                    getScadaDataCell={getScadaDataCell}
                    currentTimeDataChanged={currentTimeDataChanged}
                  />
                </Col>
                <Col>
                  <ScadaLabel
                    style={{ color: '#fff' }}
                    id={id}
                    dataType="simulationData"
                    title={item?.title}
                    getScadaDataCell={getScadaDataCell}
                    currentTimeDataChanged={currentTimeDataChanged}
                  />
                </Col>
              </Row>
            );
          }
          return null;
        })}
        <ScadaWarningContainer
          id={device.id}
          timelineDate={timelineDate}
          noticeWarnList={noticeWarnList}
        />
      </div>
    );
  };

  const getOverlayContentByMode = (): ReactNode => {
    if (
      typeof hideFeatureTooltipImportant === 'boolean' &&
      !hideFeatureTooltipImportant
    )
      return null;

    return (
      <div
        onMouseOver={onMouseOver}
        onMouseOut={onMouseOut}
        onBlur={() => {}}
        onFocus={() => {}}
        className={`${device.id} ant-popover ant-popover-placement-top default-color scada-overlay`}
        style={{
          width: bubbleBoxConfig?.width,
        }}
      >
        <div className="ant-popover-content">
          <div
            className="ant-popover-inner"
            role="tooltip"
          >
            <div
              className="ant-popover-title overlay-wrap-title"
              style={{
                backgroundColor: getCurrentDeviceColor(device.id) ?? '#4c6ad7',
                display: labelTitle ? 'block' : 'none',
              }}
            >
              <Tooltip title={device.title}>
                <div
                  className="scada-overlay-title"
                  style={{
                    maxWidth:
                      typeof bubbleBoxConfig?.width === 'number'
                        ? bubbleBoxConfig.width - 5
                        : '',
                  }}
                  role="button"
                  tabIndex={0}
                  onMouseOver={onMouseOver}
                  onFocus={() => {}}
                  onMouseOut={onMouseOut}
                  onBlur={() => {}}
                  onClick={(e) => {
                    handleClickTitle(e as unknown as MouseEvent);
                  }}
                  onKeyDown={() => {}}
                >
                  {device.title}
                </div>
              </Tooltip>
            </div>
            <div
              className={`ant-popover-inner-content ${overlayProperties.device.overlayPositioning}`}
              style={{
                display: labelIndicator ? 'flex' : 'none',
                flexDirection: 'column',
                alignItems:
                  bubbleBoxConfig?.displayStyle === 'onlyNumber'
                    ? 'center'
                    : '',
                whiteSpace: 'nowrap',
              }}
            >
              {displayIndicators.map((item) => {
                const { id, otype, oname, indicatorType, title } = item;
                const indicatorConfig = bubbleBoxConfig?.indicatorConfig?.find(
                  (ic) => ic.otype === otype,
                );
                const color = indicatorConfig?.color;
                const style = color ? { color } : undefined;
                const [, unitKey] = curDb().getPropertyTitleUnit(
                  otype,
                  'SDVAL',
                );
                const unit = unitKey ? getUnitFormat(unitKey) : undefined;
                return (
                  <div
                    key={id}
                    className={`${otype} overlayer-target`}
                  >
                    <Row
                      wrap={false}
                      className={`scadaValueWrap${id}`}
                      onClick={(e) => {
                        handleClickIndicator(
                          otype,
                          oname,
                          e as unknown as MouseEvent,
                        );
                      }}
                      onMouseOver={onMouseOver}
                      onFocus={() => {}}
                      onMouseOut={onMouseOut}
                      onBlur={() => {}}
                    >
                      {bubbleBoxConfig?.displayStyle !== 'onlyNumber' && (
                        <Col flex="auto">
                          <Text
                            className="scada-title"
                            title={unit?.valueTitle}
                          >
                            {indicatorType?.icon
                              ? // eslint-disable-next-line no-eval
                                eval(`'${indicatorType?.icon}'`)
                              : indicatorType?.title}
                            :
                          </Text>
                        </Col>
                      )}
                      <Col
                        className="scada-value-wrap"
                        style={{ padding: '0 5px' }}
                      >
                        <ScadaLabel
                          id={id}
                          dataType="scadaData"
                          title={title ?? indicatorType?.title}
                          style={style}
                          getScadaDataCell={getScadaDataCell}
                          currentTimeDataChanged={currentTimeDataChanged}
                        />
                      </Col>
                      <Col
                        className="scada-value-wrap calculate"
                        style={{
                          display: labelSimulation ? 'block' : 'none',
                        }}
                      >
                        <ScadaLabel
                          id={id}
                          dataType="simulationData"
                          title={title ?? indicatorType?.title}
                          getScadaDataCell={getScadaDataCell}
                          currentTimeDataChanged={currentTimeDataChanged}
                        />
                      </Col>
                      <div className="warnTip" />
                    </Row>
                  </div>
                );
              })}
              {bubbleBoxConfig?.showPump === false ? null : getPumpState()}
              {sortedIndicators.length > maxVisibleIndicators && (
                <Button
                  style={{ color: 'rgba(0, 0, 0, 0.88)' }}
                  onClick={toggleIndicators}
                  type="text"
                  size="small"
                >
                  {showAllIndicators ? (
                    <UpOutlined color="black" />
                  ) : (
                    <DownOutlined color="black" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
        <ScadaWarningContainer
          id={device.id}
          timelineDate={timelineDate}
          noticeWarnList={noticeWarnList}
        />
      </div>
    );
  };

  return (
    <ScadaOverlayWrapper className="scada-overlay">
      {labelMode ? getSimpleOverContent() : getOverlayContentByMode()}
    </ScadaOverlayWrapper>
  );
}
