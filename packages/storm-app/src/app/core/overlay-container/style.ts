/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

const ScadaOverlayWrapper = styled.div`
  opacity: 0.9;
  padding-bottom: 5px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;

  .overlayer-simple-mode {
    padding: 0px 5px 0px;
    border-radius: 5px;
    .overlay-wrap-title {
      color: #fff;
      border-bottom: 1px solid #fff;
    }
    &:hover {
      .overlay-wrap-title {
        display: block !important;
      }
    }
    .ant-typography {
      color: #fff;
    }
  }

  .ant-popover {
    position: relative;

    &:after {
      border: 0;
      transform: rotate(45deg);
    }
    .ant-popover-inner {
      min-width: auto;
      &:hover {
        .overlay-wrap-title,
        .overlay-wrap-targets {
          display: block !important;
        }
      }
    }
    .ant-popover-title {
      min-width: 100%;
      padding: 5px 8px 4px;
      text-align: center;
      min-height: auto;
      line-height: 1;
    }
    .ant-popover-inner-content {
      width: 100%;
      padding: 5px 8px;
      background-color: #ffffff;
      border: 1px solid ${({ theme }) => theme.colorBorderSecondary};
      box-shadow: ${({ theme }) => theme.boxShadow};
      position: relative;

      &:after {
        content: '';
        display: block;
        position: absolute;
        width: 5px;
        height: 5px;
        background: ${({ theme }) => theme.colorBorderSecondary};
        transform: rotate(45deg);
      }

      &.top-center:after {
        top: -28px;
      }
      &.bottom-center:after {
        bottom: -3px;
        transform: rotate(45deg);
      }
      &.center-left:after {
        left: -3px;
        top: calc(50% - 12px);
      }
      &.center-right:after {
        top: calc(50% - 12px);
        right: -3px;
      }

      &.overlay-wrap-targets {
        background: ${({ theme }) => theme.colorBgBase};
      }
    }
    .scada-overlay-title {
      ${EllipsisText}
      display: inline-block;
      max-width: 95px;
      text-align: center;
      font-weight: bold;
    }
    .scada-title {
      ${EllipsisText}
      font-family: 'iconfont';
      text-align: left;
      color: rgba(0, 0, 0, 0.88);
    }
    .scada-value-wrap {
      padding: 0 2px;
    }
    .col-unit {
      min-width: 32px;
      text-align: left;
    }
    .scada-unit {
      text-align: left;
    }

    &.default-color {
      .ant-popover-title {
        background: #4c6ad7;
        color: #ffffff;
      }
    }

    a {
      ${EllipsisText}
      display: block;
      color: #ffffff;
    }
    &:hover {
      z-index: 1;
    }
    h5 {
      ${EllipsisText}
      font-size: 14px;
      font-weight: normal;
      color: #fff;
      margin: 0;
      line-height: 1.6;
      padding: 0 5px;
      text-align: center;
    }
    span {
      display: block;
      line-height: 1.6;
      text-align: center;
    }
    &.default-color h5 {
      background: #4c6ad7;
    }
    &.goodColor {
      border: 1px solid #1cd1a1;
    }
    &.goodColor h5 {
      background: #1cd1a1;
    }
    &.secondColor {
      border: 1px solid #00b2f5;
    }
    &.secondColor h5 {
      background: #00b2f5;
    }
    &.thirdColor {
      border: 1px solid #6b50f1;
    }
    &.thirdColor h5 {
      background: #6b50f1;
    }
    &.badColor {
      border: 1px solid #e63343;
    }
    &.badColor h5 {
      background: #e63343;
    }
    &:after {
      content: none;
      display: block;
      position: absolute;
      width: 5px;
      height: 5px;
      background: ${({ theme }) => theme.colorBorderSecondary};
      left: 50%;
      bottom: -3px;
    }
    i.fa {
      font-size: 12px;
    }
    &.top:after {
      top: -13px;
      margin-left: -6px;
      border-bottom-color: #4c6ad7;
    }
    &.bottom:after {
      bottom: -4px;
      margin-left: -4px;
      transform: rotate(45deg);
    }
    &.left:after {
      left: auto;
      top: 50%;
      border-left-color: ${({ theme }) => theme.colorBorderSecondary};
      right: -12px;
      transform: translateY(-50%);
    }
    &.right:after {
      top: 50%;
      border-right-color: ${({ theme }) => theme.colorBorderSecondary};
      left: -12px;
      transform: translateY(-50%);
    }
  }

  .transformData {
    display: inline-block;
    background-color: transparent;
  }

  @keyframes displayText {
    from {
      background-color: #ff8383;
    }
    to {
      background-color: transparent;
    }
  }
`;

const SelectedOverlayWrapper = styled.div`
  opacity: 0.9;
  padding-bottom: 5px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;

  .pointClickOverlay {
    z-index: 9999;
    &.highlightBorder {
      .ant-popover-inner {
        box-shadow: 0 0 8px rgb(0 0 0);
      }
    }
  }

  .ant-col {
    word-break: break-all;
  }
  .ant-popover {
    position: relative;

    .ant-popover-inner {
      min-width: auto;
    }
    .ant-popover-title {
      min-width: 100%;
      padding: 5px 8px 4px;
      text-align: center;
      min-height: auto;
      line-height: 1;
    }
    .ant-popover-inner-content {
      width: 100%;
      padding: 5px 8px;
      background-color: #ffffff;
    }
    &:after {
      border: 0;
      bottom: -3px;
      transform: rotate(45deg);
    }
    .scada-overlay-title {
      ${EllipsisText}
      display: inline-block;
      max-width: 80px;
      text-align: center;
    }
    .scada-title {
      ${EllipsisText}
      font-family: 'iconfont';
    }
    .scada-value-wrap {
      padding: 0 2px;
    }
    .unit {
      min-width: 32px;
      text-align: left;
    }
  }
`;

export { ScadaOverlayWrapper, SelectedOverlayWrapper };
