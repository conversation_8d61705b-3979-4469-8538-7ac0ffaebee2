/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createHashRouter } from 'react-router';
import WaterApp from 'src/app/index';
import Devices from 'src/app/pages/devices';
import AppLoginWrap from 'src/app/pages/login';
import AppMap from 'src/app/pages/map';
import Messages from 'src/app/pages/messages';
import AppNotFoundPage from 'src/app/pages/not-found';
import Solution from 'src/app/pages/solution';
import SolutionList from 'src/app/pages/solution/solution-list';
import User from 'src/app/pages/user';
import LoginDirect from '../login/auto-login';

const routes = createHashRouter([
  {
    path: '/',
    children: [
      { index: true, element: <AppLoginWrap /> },
      { path: 'appLogin', element: <AppLoginWrap /> },
      { path: 'autoLogin', element: <LoginDirect /> },
      { path: '*', element: <AppNotFoundPage /> },
      { path: 'app404', element: <AppNotFoundPage /> },
      {
        path: 'app',
        element: <WaterApp />,
        children: [
          { path: ':dataMode', index: true, element: <AppMap /> },
          {
            path: 'devices',
            children: [{ path: ':dataMode', element: <Devices /> }],
          },
          {
            path: 'messages',
            element: <Messages />,
            children: [{ path: ':dataMode', element: <Messages /> }],
          },
          {
            path: 'solutionList',
            element: <SolutionList />,
            children: [{ path: ':dataMode', element: <SolutionList /> }],
          },
          {
            path: 'solution',
            children: [
              {
                path: ':dataMode',
                children: [{ path: ':solutionId', element: <Solution /> }],
              },
            ],
          },

          { path: 'user', element: <User /> },
        ],
      },
    ],
  },
]);

export default routes;
