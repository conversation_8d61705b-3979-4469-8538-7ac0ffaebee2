/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Carousel } from 'antd';
import styled from 'styled-components';

export const CarouselContainer = styled(Carousel)`
  && .slick-prev,
  && .slick-next {
    color: #4f4f4f;
    opacity: 1;
  }

  && .slick-dots li.slick-active button {
    background: #4f4f4f;
  }
`;

export const ContentContainer = styled.h3`
  padding: 10px 30px 0 30px;
  height: 160px;
  color: #000;
  background: #cf616c;
  margin-bottom: 0;
  opacity: 0.8;
`;
