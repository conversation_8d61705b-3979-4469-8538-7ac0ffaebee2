/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  displayBlink,
  formatDevicesWarns,
  WarnDetail,
  WarnInfoList,
} from '@waterdesk/data/warn';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { CarouselContainer, ContentContainer } from './style';

interface Props {
  id: string;
  timelineDate: string;
  noticeWarnList: WarnInfoList;
  forceOpen?: boolean;
  style?: React.CSSProperties;
}

export default function ScadaWarningContainer(props: Props) {
  const { id, timelineDate, noticeWarnList, forceOpen, style } = props;
  const [data, setData] = useState<WarnDetail[]>([]);
  const [open, setOpen] = useState(false);

  const updateScadaWarns = () => {
    const devicesWarns = formatDevicesWarns(
      {},
      noticeWarnList.reduce(
        (acc: WarnDetail[], obj) => acc.concat(obj.details),
        [],
      ),
    );
    const currentDevicesDatas = devicesWarns[id];
    if (currentDevicesDatas) {
      const endTime = dayjs(timelineDate)
        .add(1, 'day')
        .format('YYYY-MM-DD 00:00:00');
      const startTime = dayjs(endTime).subtract(48, 'hour');
      const warns = currentDevicesDatas
        .filter(
          (item) =>
            dayjs(item.startTime).isBefore(endTime) &&
            dayjs(item.startTime).isAfter(startTime) &&
            displayBlink(item),
        )
        .sort((a, b) => b.startTime.localeCompare(a.startTime));
      setData(warns);
    }
  };

  useEffect(() => {
    updateScadaWarns();
  }, [timelineDate, noticeWarnList]);

  return data.length > 0 ? (
    <div
      className="warningTip"
      style={{
        display: open || forceOpen ? 'block' : 'none',
        position: 'absolute',
        transform: 'translateY(20px)',
        width: '400px',
        pointerEvents: 'auto',
        zIndex: 999999,
        ...style,
      }}
      onMouseEnter={(event) => {
        const currentTarget = event.currentTarget as HTMLElement;
        currentTarget.style.pointerEvents = 'auto';
        setOpen(true);
      }}
      onMouseLeave={(event) => {
        const currentTarget = event.currentTarget as HTMLElement;
        currentTarget.style.pointerEvents = 'none';
        setOpen(false);
      }}
    >
      <CarouselContainer arrows>
        {data?.map((item) => (
          <div key={item.deviceId}>
            <ContentContainer>
              <div style={{ marginBottom: -2 }}>{item.abnormalType ?? 1}</div>
              <div style={{ fontSize: 14 }}>[{item.startTime}]</div>
              <div
                style={{
                  fontSize: 14,
                  display: '-webkit-box',
                  WebkitLineClamp: 4,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  lineHeight: '1.5em',
                  maxHeight: '6em',
                }}
              >
                {item.description}
              </div>
            </ContentContainer>
          </div>
        ))}
      </CarouselContainer>
    </div>
  ) : null;
}
