/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentList } from '@waterdesk/data/department';
import {
  DepartmentUserNode,
  ShareSolutionTableType,
} from '@waterdesk/data/department-user';
import { UserList } from '@waterdesk/data/system-user';
import type { TabsProps, TreeProps } from 'antd';

export type State = {
  department: DepartmentList;
  user: UserList;
  departmentTreeData: DepartmentUserNode[];
  userTreeData: DepartmentUserNode[];
  activeTabKey: TabsProps['activeKey'];
  departmentCheckedKeys: TreeProps['checkedKeys'];
  userCheckedKeys: TreeProps['checkedKeys'];
  tableData: ShareSolutionTableType[];
  loading: boolean;
  searchTreeData: DepartmentUserNode[];
};

export type Action =
  | { type: 'SET_DEPARTMENT'; payload: DepartmentList }
  | { type: 'SET_USER'; payload: UserList }
  | { type: 'SET_DEPARTMENT_TREE_DATA'; payload: DepartmentUserNode[] }
  | { type: 'SET_USER_TREE_DATA'; payload: DepartmentUserNode[] }
  | { type: 'SET_ACTIVE_TAB_KEY'; payload: TabsProps['activeKey'] }
  | { type: 'SET_DEPARTMENT_CHECKED_KEYS'; payload: TreeProps['checkedKeys'] }
  | { type: 'SET_USER_CHECKED_KEYS'; payload: TreeProps['checkedKeys'] }
  | { type: 'SET_TABLE_DATA'; payload: ShareSolutionTableType[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SEARCH_TREE_DATA'; payload: DepartmentUserNode[] };

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case 'SET_DEPARTMENT':
      return { ...state, department: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_DEPARTMENT_TREE_DATA':
      return { ...state, departmentTreeData: action.payload };
    case 'SET_USER_TREE_DATA':
      return { ...state, userTreeData: action.payload };
    case 'SET_ACTIVE_TAB_KEY':
      return { ...state, activeTabKey: action.payload };
    case 'SET_DEPARTMENT_CHECKED_KEYS':
      return { ...state, departmentCheckedKeys: action.payload };
    case 'SET_USER_CHECKED_KEYS':
      return { ...state, userCheckedKeys: action.payload };
    case 'SET_TABLE_DATA':
      return { ...state, tableData: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_SEARCH_TREE_DATA':
      return { ...state, searchTreeData: action.payload };
    default:
      return state;
  }
};

export const initialState: State = {
  department: [],
  user: [],
  departmentTreeData: [],
  userTreeData: [],
  activeTabKey: 'department',
  departmentCheckedKeys: [],
  userCheckedKeys: [],
  tableData: [],
  loading: false,
  searchTreeData: [],
};
