/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentList } from '@waterdesk/data/department';
import {
  buildDepartmentTree,
  createDepartmentUserTree,
  DepartmentUserNode,
  getAllChildKeys,
  ShareSolutionTableType,
  ShareSolutionType,
} from '@waterdesk/data/department-user';
import { UserList } from '@waterdesk/data/system-user';
import { fetchAllRequest } from '@waterdesk/request/api/fetch-all-request';
import { getDepartmentList } from '@waterdesk/request/department';
import { getUserList } from '@waterdesk/request/user';
import { useDebounceFn } from 'ahooks';
import { TabsProps, TreeProps } from 'antd';
import { useEffect, useReducer } from 'react';
import BaseTree from 'src/components/tree/base-tree';
import { ShareSolutionTabs } from '../style';
import { initialState, reducer } from './share-solution-reducer';

function searchTree(
  node: DepartmentUserNode,
  keyword: string,
): DepartmentUserNode | null {
  // 检查当前节点是否匹配
  if (node.title.includes(keyword)) {
    // 返回匹配节点的副本，但没有子节点
    return { ...node, children: [] };
  }

  // 如果有子节点，遍历它们
  if (node.children && node.children.length > 0) {
    const matchedChildren: DepartmentUserNode[] = [];
    node.children.forEach((child) => {
      const result = searchTree(child, keyword);
      if (result) matchedChildren.push(result);
    });

    // 如果在子节点中找到匹配项，则返回当前节点和匹配的子节点
    if (matchedChildren.length > 0) {
      return { ...node, children: matchedChildren };
    }
  }

  // 如果当前节点和子节点都不匹配，返回 null
  return null;
}

export function searchInTree(
  nodes: DepartmentUserNode[],
  keyword: string,
): DepartmentUserNode[] {
  return nodes
    .map((node) => searchTree(node, keyword))
    .filter((node) => node !== null) as DepartmentUserNode[];
}

interface UseShareSolutionProps {
  department: string[];
  user: string[];
}

function useShareSolution({ department, user }: UseShareSolutionProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const setLoading = (payload: boolean) => {
    dispatch({ type: 'SET_LOADING', payload });
  };

  const setSearchTreeData = (payload: DepartmentUserNode[]) => {
    dispatch({ type: 'SET_SEARCH_TREE_DATA', payload });
  };

  const setDepartmentTree = (payload: DepartmentList) => {
    dispatch({
      type: 'SET_DEPARTMENT_TREE_DATA',
      payload: buildDepartmentTree(payload, true),
    });
  };

  const setDepartment = (payload: DepartmentList) => {
    dispatch({ type: 'SET_DEPARTMENT', payload });
    setDepartmentTree(payload);
    setSearchTreeData(buildDepartmentTree(payload, true));
  };

  const setUser = (payload: UserList) => {
    dispatch({ type: 'SET_USER', payload });
  };

  const setUserTree = (departmentList: DepartmentList, userList: UserList) => {
    dispatch({
      type: 'SET_USER_TREE_DATA',
      payload: createDepartmentUserTree(departmentList, userList),
    });
  };

  const setCheckedKeys = (
    payload: TreeProps['checkedKeys'],
    type: ShareSolutionType,
  ) => {
    if (type === 'department')
      dispatch({ type: 'SET_DEPARTMENT_CHECKED_KEYS', payload });
    else dispatch({ type: 'SET_USER_CHECKED_KEYS', payload });
  };

  const setTableData = (payload: ShareSolutionTableType[]) => {
    dispatch({ type: 'SET_TABLE_DATA', payload });
  };

  const handleChangeTab = (payload: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB_KEY', payload });
    if (payload === 'department') {
      setSearchTreeData(state.departmentTreeData);
    } else {
      setSearchTreeData(state.userTreeData);
    }
  };

  const handleDepartmentCheck: TreeProps['onCheck'] = (_, info) => {
    const currentNodeKeys = [info.node.key];
    const currentChildKeys = getAllChildKeys(
      info.node as unknown as DepartmentUserNode,
    );
    const allCurrentKeys = [...currentNodeKeys, ...currentChildKeys];

    let finalCheckedKeys;

    if (info.checked) {
      finalCheckedKeys = Array.from(
        new Set([
          ...(state.departmentCheckedKeys as string[]),
          ...allCurrentKeys,
        ]),
      );
    } else {
      finalCheckedKeys = (state.departmentCheckedKeys as string[])?.filter(
        (key) => !allCurrentKeys.includes(key),
      );
    }
    setCheckedKeys(finalCheckedKeys, 'department');
  };

  const handleUserCheck: TreeProps['onCheck'] = (key) => {
    setCheckedKeys(key, 'user');
  };

  const { run: handleSearchDepartmentTree } = useDebounceFn(
    (searchTerm: string) => {
      const result = searchInTree(state.departmentTreeData, searchTerm);
      if (searchTerm) {
        setSearchTreeData(result);
      } else {
        setSearchTreeData(state.departmentTreeData);
      }
    },
    { wait: 500 },
  );

  const { run: handleSearchUserTree } = useDebounceFn(
    (searchTerm: string) => {
      const result = searchInTree(state.userTreeData, searchTerm);
      if (searchTerm) {
        setSearchTreeData(result);
      } else {
        setSearchTreeData(state.userTreeData);
      }
    },
    { wait: 500 },
  );

  useEffect(() => {
    const initialList = async () => {
      setLoading(true);

      const [userRes, departmentRes] = await Promise.all([
        fetchAllRequest(getUserList),
        getDepartmentList(),
      ]);

      if (userRes.length > 0) {
        setUser(userRes);
      }

      if (departmentRes.status === 'Success') {
        const departmentList = departmentRes.departmentList ?? [];
        setDepartment(departmentList);
        setUserTree(departmentList, userRes);
      }

      setLoading(false);
    };

    initialList();
  }, []);

  useEffect(() => {
    if (department?.length > 0) {
      setCheckedKeys(department ?? [], 'department');
    }

    if (user?.length > 0) {
      setCheckedKeys(user ?? [], 'user');
    }
  }, [department, user]);

  useEffect(() => {
    const tableData = [
      ...state.department
        .filter((item) =>
          (state.departmentCheckedKeys as string[])?.includes(item.id),
        )
        .map((item) => ({
          id: `department-${item.id}`,
          type: 'department',
          name: item.name,
        })),
      ...state.user
        .filter((item) =>
          (state.userCheckedKeys as string[])?.includes(item.id),
        )
        .map((item) => ({
          id: `user-${item.id}`,
          type: 'user',
          name: item.name,
          phone: item.phone,
        })),
    ] as ShareSolutionTableType[];

    setTableData(tableData);
  }, [
    state.department,
    state.user,
    state.departmentCheckedKeys,
    state.userCheckedKeys,
  ]);

  const items: TabsProps['items'] = [
    {
      key: 'department',
      label: '部门',
      children: (
        <BaseTree
          defaultExpandAll
          checkStrictly
          checkable
          showSearch
          treeData={state.searchTreeData}
          checkedKeys={state.departmentCheckedKeys}
          onCheck={handleDepartmentCheck}
          loading={state.loading}
          searchConfig={{
            onSearch: handleSearchDepartmentTree,
          }}
        />
      ),
    },
    {
      key: 'user',
      label: '用户',
      children: (
        <BaseTree
          defaultExpandAll
          checkable
          showSearch
          treeData={state.searchTreeData}
          checkedKeys={state.userCheckedKeys}
          onCheck={handleUserCheck}
          loading={state.loading}
          searchConfig={{
            onSearch: handleSearchUserTree,
          }}
        />
      ),
    },
  ];

  const shareSolutionTabsContext = (
    <ShareSolutionTabs
      items={items}
      activeKey={state.activeTabKey}
      onTabClick={handleChangeTab}
    />
  );

  return {
    shareSolutionTabsContext,
    departmentCheckedKeys: state.departmentCheckedKeys,
    userCheckedKeys: state.userCheckedKeys,
    tableData: state.tableData,
    reset: () => {
      handleChangeTab('department');
    },
  };
}

export default useShareSolution;
