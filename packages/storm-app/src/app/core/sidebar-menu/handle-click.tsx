/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SidebarMenuType } from '@waterdesk/data/sidebar-menu-data';
import { baseActions } from 'src/store/base';
import { BottomTabKey } from 'src/store/base/types';
import store from 'src/store/configure-store';
import { leftWrapperActions } from 'src/store/left-wrapper';
import { MENU_TOOLS_SCENE } from 'src/store/left-wrapper/types';
import { scenesActions } from 'src/store/scenes';

export default function handleMenuClick(menuType: string) {
  switch (menuType) {
    // 开启底部tab
    case SidebarMenuType.WARN:
    case SidebarMenuType.ISSUE_REPORT:
    case SidebarMenuType.WATER_OUTAGE_INFO:
    case SidebarMenuType.QUICK_SOLUTION_LIST:
    case SidebarMenuType.WATER_QUALITY:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey[menuType],
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.DEVICE_STATE_RECORDS:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.DEVICE_BAD_RECORDS,
          type: 'ADD',
        }),
      );
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.DEVICE_STATE_RECORDS,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.WORK_ORDER:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.WORK_ORDER_REPAIR_DETAIL,
          type: 'ADD',
        }),
      );
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.WORK_ORDER,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.VALVE_MANAGEMENT:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.VALVE_MANAGEMENT,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.VALVE_GROUP:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.VALVE_GROUP,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.CLOSED_VALVES:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.CLOSED_VALVES,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.VALVE_MANAGEMENT_AND_GROUP:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.VALVE_MANAGEMENT,
          type: 'ADD',
        }),
      );
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.CLOSED_VALVES,
          type: 'ADD',
        }),
      );
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.VALVE_GROUP,
          type: 'ADD',
        }),
      );
      break;
    case SidebarMenuType.ALERT:
      store.dispatch(
        baseActions.updateBottomTab({
          activeKey: BottomTabKey.ALERT,
          type: 'ADD',
        }),
      );
      break;
    // 开启左侧抽屉
    case SidebarMenuType.VALVE_ANALYSIS:
    case SidebarMenuType.BURST_PIPE_FLUSHING:
    case SidebarMenuType.SCHEDULING_ANALYSIS:
    case SidebarMenuType.DOWNSTREAM_TRACKING:
    case SidebarMenuType.UPSTREAM_TRACKING:
    case SidebarMenuType.POLLUTION_SOURCE_TRACING:
    case SidebarMenuType.CUSTOM_TRACKING:
    case SidebarMenuType.BATCH_QUERY:
    case SidebarMenuType.SOLUTION_ANALYSIS:
    case SidebarMenuType.WATER_VOLUME_STATISTICS:
      store.dispatch(
        leftWrapperActions.leftWrapperContainerTypeChanged({
          containerType: menuType,
        }),
      );
      store.dispatch(
        leftWrapperActions.leftWrapperChanged({
          open: true,
        }),
      );
      break;
    // 开启 Modal
    case SidebarMenuType.SOLUTION_CREATE:
    case SidebarMenuType.SOLUTION_LIST:
    case SidebarMenuType.SOLUTION_COMPARE:
    case SidebarMenuType.DEVICE_EVALUATION:
    case SidebarMenuType.SIMULATION_LOG:
    case SidebarMenuType.SMART_VALVE_MANAGEMENT:
    case SidebarMenuType.SOLUTION_SIMULATION:
    case SidebarMenuType.DOWNLOAD_SCADA_DATA:
      store.dispatch(leftWrapperActions.openModal({ modal: menuType }));
      break;
    // 场景
    case SidebarMenuType.SCENE_RUN:
    case SidebarMenuType.SCENE_MODEL:
    case SidebarMenuType.SCENE_SCADA:
    case SidebarMenuType.SCENE_SCHEME:
    case SidebarMenuType.SCHEDULE:
    case SidebarMenuType.WARNING:
    case SidebarMenuType.SCENE_ENERGY_CONSUMPTION:
      store.dispatch(
        leftWrapperActions.leftWrapperContainerTypeChanged({
          containerType: MENU_TOOLS_SCENE,
        }),
      );
      store.dispatch(
        leftWrapperActions.leftWrapperChanged({
          open: true,
        }),
      );
      store.dispatch(
        scenesActions.updateCurrentSceneId({
          sceneId: menuType,
        }),
      );
      break;
    default:
      break;
  }
}
