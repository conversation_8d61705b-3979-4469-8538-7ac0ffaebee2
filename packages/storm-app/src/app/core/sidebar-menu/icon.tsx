/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import * as AllIcons from '@ant-design/icons';
import {
  getSidebarMenuName,
  SidebarMenuItem,
  SidebarMenuItemWithBadge,
} from '@waterdesk/data/sidebar-menu-data';
import { Badge } from 'antd';
import React from 'react';
import WrapperHelpIcon from 'src/app/core/containers/help-icon-container';
import IconFont from 'src/components/icon-font';

const AntdIcons: { [key: string]: any } = AllIcons;

function createIcon(menuItem: SidebarMenuItem): React.JSX.Element | null {
  const icon = menuItem?.icon ?? '';
  if (menuItem.iconType === 'ANTD') {
    if (icon in AntdIcons) {
      const IconComponent = AntdIcons[icon];
      return <IconComponent />;
    }
  }
  if (menuItem.iconType === 'ICONFONT') {
    return <IconFont type={icon} />;
  }
  return null;
}

function createIconElement(
  menuItem: SidebarMenuItemWithBadge,
  icon: React.JSX.Element | null,
): React.JSX.Element {
  const key = menuItem?.key ?? '';
  const name = menuItem?.title ?? getSidebarMenuName(key);
  const help = menuItem?.help ?? false;
  if (menuItem.className === 'subMenu') {
    return (
      <div>
        <Badge
          showZero
          dot={menuItem.badge === 0}
          count={menuItem.badge === 0 ? undefined : menuItem.badge}
          styles={{ root: { width: 'calc(100% - 10px)' } }}
        >
          {icon}
          <div style={{ maxWidth: '100%', overflow: 'hidden' }}>{name}</div>
        </Badge>
      </div>
    );
  }
  return (
    <Badge
      showZero
      dot={menuItem.badge === 0}
      count={menuItem.badge}
    >
      <div>
        {name}
        {help && <WrapperHelpIcon title={help} />}
      </div>
    </Badge>
  );
}

function generateIcon(menuItem: SidebarMenuItem): React.JSX.Element {
  const icon = createIcon(menuItem);
  return createIconElement(menuItem, icon);
}

export default generateIcon;
