/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Tabs } from 'antd';
import { BeforePrefix } from 'src/styles/common-style';
import styled from 'styled-components';

export const SolutionDetailsWrapper = styled.div`
  padding: 10px 5px;
  overflow-y: scroll;
  height: 100%;
  .title {
    position: relative;
    margin: 0 0 10px 0;
    font-size: 18px;
    line-height: 18px;
    padding: 3px 0 3px 10px;
    font-weight: 600;
    &::before {
      ${BeforePrefix}
    }
  }
`;

export const SolutionChangesWrapper = styled.div``;

export const ShareSolutionWrapper = styled.div`
  display: flex;
`;

export const ShareSolutionTabs = styled(Tabs)`
  flex: 1;
  margin-right: 10px;
  padding: 10px;
  box-shadow: ${(props) => props.theme.boxShadow};
  height: 88vh;
  overflow-y: scroll;
`;
