/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Modal } from 'antd';
import React, { useEffect } from 'react';
import useVersionChecker from 'src/app/hooks/use-version-checker';

const UpdateNotification: React.FC = () => {
  const [modal, contextHolder] = Modal.useModal();
  const newVersionAvailable = useVersionChecker();

  useEffect(() => {
    if (newVersionAvailable) {
      modal.confirm({
        title: '版本更新提醒',
        content: `新版本已发布！
        点击确认刷新按钮将刷新页面获取最新版本。
        或点击关闭弹窗按钮继续使用当前版本。
        `,
        okText: '确认刷新',
        cancelText: '关闭弹窗',
        onOk: () => window.location.reload(),
        onCancel: () => Modal.destroyAll(),
        centered: true,
      });
    }
  }, [newVersionAvailable]);

  return contextHolder;
};

export default UpdateNotification;
