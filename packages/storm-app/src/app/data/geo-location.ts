/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Coordinate } from 'ol/coordinate';
import { transform } from 'ol/proj';

export function transformGeoLocationCoordinate(
  geoLocation: string,
  mapProjection?: string,
): Coordinate {
  const geoInfo = JSON.parse(geoLocation);
  const x = geoInfo.longitude;
  const y = geoInfo.latitude;
  const XYCoordinate = transform([x, y], 'EPSG:4326', 'EPSG:3857');
  if (mapProjection) {
    return transform(XYCoordinate, 'EPSG:3857', mapProjection);
  }
  return XYCoordinate;
}
