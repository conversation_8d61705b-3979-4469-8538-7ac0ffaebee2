/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getElementShape } from '@waterdesk/request/get-element-shape';
import { useRequest } from 'ahooks';
import { useBaseSlice } from '../../store/base';
import { useSelectionSlice } from '../../store/selection';
import { curDb, hostApp } from '../host-app';

export const useAsyncLocate = () => {
  useBaseSlice();
  useSelectionSlice();

  const mapView = hostApp().getMainMapView();

  const handleLocate = (
    otype: string,
    oname: string,
    shape: string,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
    locateType: 'navigate' | 'selected' = 'selected',
  ) => {
    if (mapView && shape) {
      if (locateType === 'navigate') {
        mapView.navigate(shape);
      } else if (indicatorType && indicatorName && vprop) {
        mapView.locateAndSelectAndHighlight(
          otype,
          oname,
          indicatorType,
          indicatorName,
          vprop,
          shape,
        );
      } else {
        mapView.selectAndNavigate(otype, oname, shape);
      }
    }
  };

  const fetchElementShape = async (
    otype: string,
    oname: string,
  ): Promise<string | undefined> => {
    const elementInfo = curDb().getDevice(otype, oname);
    let shape = elementInfo?.shape;

    if (!shape) {
      const res = await getElementShape(otype, oname);
      if (res.status === 'Success' && res.shape) {
        shape = res.shape;
      }
    }
    if (mapView && shape) {
      mapView.selectAndNavigate(otype, oname, shape);
    }
    return shape;
  };

  const { run } = useRequest(fetchElementShape, {
    manual: true,
  });

  const asyncLocate = (
    otype: string,
    oname: string,
    shape?: string,
    indicatorType?: string,
    indicatorName?: string,
    vprop?: string,
    locateType: 'navigate' | 'selected' = 'selected',
  ) => {
    if (shape) {
      handleLocate(
        otype,
        oname,
        shape,
        indicatorType,
        indicatorName,
        vprop,
        locateType,
      );
      return;
    }
    if (typeof otype === 'string' && typeof oname === 'string') {
      run(otype, oname);
    }
  };

  return {
    locate: asyncLocate,
  };
};
