/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { appDeviceDetect } from 'src/config';

export default function useDeviceDetect() {
  const navigate = useNavigate();
  const getDeviceTypeName = (typeName: string): string => {
    switch (typeName) {
      case 'wxwork': {
        return '企业微信';
      }
      case 'micromessenger': {
        return '微信';
      }
      default: {
        return '';
      }
    }
  };
  const ua = navigator.userAgent.toLowerCase();
  useEffect(() => {
    if (!ua.match(/mobile/i) && appDeviceDetect) {
      navigate('/app404', {
        state: {
          message: '仅支持移动端设备浏览!',
        },
      });
    }
    if (appDeviceDetect) {
      const deviceType = appDeviceDetect.split(',');
      deviceType.forEach((item) => {
        if (!ua.match(item)) {
          navigate('/app404', {
            state: {
              message: `仅支持${getDeviceTypeName(item)}设备浏览`,
            },
          });
        }
      });
    }
  }, []);
}
