/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { ScadaModelTimeData } from '@waterdesk/data/device-time-data';
import {
  getCurrentTimeState,
  getDateTimeFromValue,
} from '@waterdesk/data/time-data';
import getDeviceTimeData from '@waterdesk/request/get-device-time-data';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { baseActions, useBaseSlice } from 'src/store/base';
import { useTimelineSlice } from 'src/store/time-line';
import {
  selectTimelineDate,
  selectTimelineTime,
} from 'src/store/time-line/selectors';
import {
  selectDataInitialComplete,
  selectViewId,
} from '../../store/base/selectors';
import { curDb, hostApp } from '../host-app';

export default function useDeviceTimeData(options?: { needViewId?: boolean }) {
  useBaseSlice();
  useTimelineSlice();
  const dispatch = useDispatch();
  const timeIndex = useSelector(selectTimelineTime);
  const timelineDate = useSelector(selectTimelineDate);
  const dataInitialComplete = useSelector(selectDataInitialComplete);
  const viewId = useSelector(selectViewId);

  const needViewId = options?.needViewId ?? false;

  const updateDeviceTimeData = async (
    timeString: string,
    database: Database,
  ) => {
    const responseData = await getDeviceTimeData(
      timeString,
      getCurrentTimeState(timeIndex, dayjs(timelineDate)) === 'future',
      curDb(),
    );
    return {
      responseData,
      database,
    };
  };

  const { run } = useRequest(updateDeviceTimeData, {
    manual: true,
    onSuccess: (res, params) => {
      if (res.responseData.status === 'Success') {
        const [timeString, database] = params;
        database.currentDeviceTimeData.updateIndicatorData(
          res.responseData.scadaModelTimeData as Map<
            string,
            ScadaModelTimeData
          >,
        );
        dispatch(
          baseActions.updateCurrentTimeData({
            updateTime: timeString,
          }),
        );
      }
    },
    ready: needViewId ? !!viewId : true,
  });

  useEffect(() => {
    if (!dataInitialComplete) return;
    const time = getDateTimeFromValue(timeIndex, dayjs(timelineDate));
    const timeString = time.format('YYYY-MM-DD HH:mm:ss');
    const mapViews = hostApp().getMapViews();
    if (mapViews && mapViews.length > 0) {
      mapViews.forEach((mapView) => {
        const currentTime = mapView.dateTime
          ? mapView.dateTime.format('YYYY-MM-DD HH:mm:ss')
          : timeString;
        run(currentTime, mapView.curDb);
      });
    } else {
      run(timeString, curDb());
    }
  }, [timeIndex, timelineDate, dataInitialComplete]);
}
