/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AsyncTaskStatus,
  GetAsyncTaskStatusResponse,
  getAsyncTaskStatus,
} from '@waterdesk/request/get-async-task';
import { useInterval, useLocalStorageState } from 'ahooks';
import { Options } from 'ahooks/lib/createUseStorageState';
import { useEffect, useState } from 'react';

export type AsyncTaskMap = Map<string, string>;
export type AsyncTaskArray = Array<[string, string]>;

export default function useLocalStorageAsyncTask(
  // 创建异步任务时的key， 可以通过创建时传入自定义key覆盖
  key: string,
  options?: Options<AsyncTaskArray> & {
    /** 是否首次渲染立即执行 */
    immediate?: boolean;
    /** 定时器执行间隔 ms */
    delay?: number;
    /** 是否启用轮询 */
    enable?: boolean;
    /** 自定义异步获取状态时间 */
    asyncQueryRequest?: (taskId: string) => Promise<GetAsyncTaskStatusResponse>;
    /** 异步任务结束后的回调 , 如果有formatValues, 则返回原始的响应值和处理过的值 */
    callback?: (
      asyncRes: GetAsyncTaskStatusResponse,
      originalRes?: GetAsyncTaskStatusResponse,
    ) => void;
    /** values 格式化函数 */
    formatValues?: (values: any) => any;
    /** 自定义控制显示进度步长 default: 50 */
    progressStep?: number;
    /** 进度增长是否按步长固定 default: false */
    progressFixed?: boolean;
  },
) {
  const delay = options?.delay ?? 3000;
  const enableInterval = options?.enable ?? true;
  const immediate = options?.immediate ?? false;
  const progressStep = options?.progressStep ?? 50;
  const progressFixed = options?.progressFixed ?? false;

  const [progress, setProgress] = useState<number>(0);
  const [interval, setInterval] = useState<number | undefined>(undefined);
  const [progressMsg, setProgressMsg] = useState<string | undefined>(undefined);
  const [asyncTask, setAsyncTask] = useLocalStorageState<AsyncTaskArray>(
    'asyncTask',
    {
      ...options,
    },
  );
  const taskId = new Map(asyncTask).get(key);

  const clearAsyncTask = (): void => {
    const asyncTaskMap: AsyncTaskMap = new Map(asyncTask);
    asyncTaskMap.delete(key);
    setAsyncTask([...asyncTaskMap]);
    setProgressMsg(undefined);
    setProgress(0);
  };

  const addAsyncTask = (taskId: string, customKey?: string): void => {
    const asyncTaskMap: AsyncTaskMap = new Map(asyncTask);
    // 增加异步任务时，如果传入自定义key则使用，否则使用key
    asyncTaskMap.set(customKey ?? key, taskId);
    setAsyncTask([...asyncTaskMap]);
    setProgressMsg(undefined);
    setProgress(0);
  };

  const fetchAsyncTaskStatus = options?.asyncQueryRequest ?? getAsyncTaskStatus;

  const getMockProgress = (): number => {
    const coefficient = progressFixed ? 1 : Math.random();

    return progress + Math.round(coefficient * progressStep);
  };

  const asyncQueryStatus = async (taskId?: string) => {
    if (typeof taskId === 'undefined') return;
    const originRes = await fetchAsyncTaskStatus(taskId);
    let res = { ...originRes };
    setProgressMsg(res?.message);
    const countProgress = res?.progress ?? getMockProgress();
    setProgress(countProgress >= 99 ? 99 : countProgress);
    if (res.values && options?.formatValues)
      res = { ...res, values: options?.formatValues?.(res.values) };
    if (
      res.taskStatus === AsyncTaskStatus.SUCCESS ||
      res.taskStatus === AsyncTaskStatus.FAIL
    ) {
      clearAsyncTask();
      setInterval(undefined);
      options?.callback?.(res, options?.formatValues ? originRes : undefined);
    }
  };

  useInterval(() => asyncQueryStatus(taskId), interval, {
    immediate,
  });

  useEffect(() => {
    if (!enableInterval) {
      setInterval(undefined);
      return;
    }
    if (typeof taskId !== 'undefined' && enableInterval) {
      setInterval(delay);
    }
  }, [taskId, enableInterval]);

  return {
    taskId,
    message: progressMsg,
    progress,
    clear: clearAsyncTask,
    add: addAsyncTask,
  };
}
