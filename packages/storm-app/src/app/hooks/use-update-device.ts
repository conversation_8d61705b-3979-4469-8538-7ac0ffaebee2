/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { baseActions, useBaseSlice } from '../../store/base';
import { selectDeviceUpdateDate } from '../../store/base/selectors';
import { useTimelineSlice } from '../../store/time-line';
import { selectTimelineDate } from '../../store/time-line/selectors';

const useUpdateDevice = () => {
  useTimelineSlice();
  useBaseSlice();
  const timelineDate = useSelector(selectTimelineDate);
  const deviceUpdateDate = useSelector(selectDeviceUpdateDate);
  const dispatch = useDispatch();
  useEffect(() => {
    // 组件初始化 => 异步更新设备 => 更新设备更新时间
    // 时间轴日期变化 => 异步更新设备 => 更新设备更新时间
    if (typeof deviceUpdateDate === 'undefined') return;
    dispatch(
      baseActions.updateDeviceUpdateDate({
        date: timelineDate,
      }),
    );
  }, [timelineDate]);
};

export default useUpdateDevice;
