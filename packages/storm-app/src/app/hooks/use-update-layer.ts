/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { scenesActions, useScenesSlice } from '../../store/scenes';
import { selectUpdatedLayerStates } from '../../store/scenes/selectors';

// refresh layer, if scenes layer is update
const useUpdateLayer = () => {
  useScenesSlice();
  const dispatch = useDispatch();
  const updatedLayerStates = useSelector(selectUpdatedLayerStates);
  useEffect(() => {
    if (typeof updatedLayerStates !== 'undefined') {
      dispatch(scenesActions.updateLayerVisible());
    }
  }, [updatedLayerStates]);
};

export default useUpdateLayer;
