/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectViewId } from '../../store/base/selectors';
import { timelineActions, useTimelineSlice } from '../../store/time-line';
import {
  selectTimelineDate,
  selectTimelineTime,
} from '../../store/time-line/selectors';

const useUpdateThemeTime = () => {
  useTimelineSlice();
  const dispatch = useDispatch();
  const viewId = useSelector(selectViewId);
  const timelineTime = useSelector(selectTimelineTime);
  const timelineDate = useSelector(selectTimelineDate);
  useEffect(() => {
    if (typeof viewId === 'undefined') return;
    dispatch(timelineActions.setViewThemeTime());
  }, [timelineTime, timelineDate]);
};

export default useUpdateThemeTime;
