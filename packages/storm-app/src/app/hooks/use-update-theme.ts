/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectViewId } from '../../store/base/selectors';
import { scenesActions, useScenesSlice } from '../../store/scenes';
import {
  selectCurrentSceneId,
  selectUpdatedThemeItem,
} from '../../store/scenes/selectors';

// refresh layer, if scenes theme is update or currentSceneId is update
const useUpdateTheme = () => {
  useScenesSlice();
  const dispatch = useDispatch();
  const updatedThemeItem = useSelector(selectUpdatedThemeItem);
  const currentSceneId = useSelector(selectCurrentSceneId);
  const viewId = useSelector(selectViewId);
  useEffect(() => {
    if (typeof updatedThemeItem !== 'undefined') {
      dispatch(scenesActions.switchViewTheme({}));
    }
  }, [updatedThemeItem]);

  useEffect(() => {
    if (
      typeof currentSceneId !== 'undefined' &&
      typeof viewId !== 'undefined'
    ) {
      dispatch(scenesActions.switchViewTheme({}));
    }
  }, [currentSceneId, viewId]);
};

export default useUpdateTheme;
