/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import axios from 'axios';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

const VERSION_URL = './version.json';

const useVersionChecker = () => {
  const [currentVersion, setCurrentVersion] = useState<string | null>(null);
  const [currentBuildTime, setCurrentBuildTime] = useState<string | null>(null);
  const [newVersionAvailable, setNewVersionAvailable] = useState(false);

  useEffect(() => {
    const fetchVersion = async () => {
      try {
        const timestamp = dayjs().unix();
        const response = await axios.get(`${VERSION_URL}?t=${timestamp}`);
        const latestVersion = response.data.version;
        const latestBuildTime = response.data.buildTime;

        if (currentVersion !== null && currentBuildTime !== null) {
          if (
            currentVersion !== latestVersion ||
            currentBuildTime !== latestBuildTime
          ) {
            setNewVersionAvailable(true);
          }
        } else {
          setCurrentVersion(latestVersion);
          setCurrentBuildTime(latestBuildTime);
        }
      } catch (error) {
        console.error('Failed to fetch version:', error);
      }
    };

    fetchVersion();

    const intervalId = setInterval(fetchVersion, 60000); // 每分钟检查一次

    return () => clearInterval(intervalId);
  }, [currentVersion, currentBuildTime]);

  return newVersionAvailable;
};

export default useVersionChecker;
