/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import getWeComConfig, { WeComConfig } from '@waterdesk/request/get-wecom';
import * as WeCom from '@wecom/jssdk';
import { useEffect, useState } from 'react';
import { WeComAgentId } from 'src/config';

export interface PositionInfo {
  latitude: number;
  longitude: number;
  speed: number;
  accuracy: number;
}

export default function useWeComLocation() {
  const [positionInfo, setPositionInfo] = useState<PositionInfo | undefined>(
    undefined,
  );

  const getConfig = () => {
    getWeComConfig().then((response) => {
      if (response.status === 'Fail' && !response.weComConfig) {
        return;
      }
      const { appId, timestamp, nonceStr, ticket, signature } =
        response.weComConfig as WeComConfig;
      WeCom.register({
        corpId: appId,
        agentId: WeComAgentId,
        jsApiList: [
          'openLocation',
          'getLocation',
          'startAutoLBS',
          'stopAutoLBS',
          'onLocationChange',
        ],
        getAgentConfigSignature() {
          const sign = WeCom.getSignature({
            timestamp,
            nonceStr,
            ticket,
            signature,
          } as WeCom.GetSignatureOptions);
          return sign as WeCom.SignatureData;
        },
        onAgentConfigFail: (res) => {
          // eslint-disable-next-line no-alert
          alert(JSON.stringify(res));
        },
        onAgentConfigSuccess: () => {
          setTimeout(() => {
            WeCom.invoke(
              'startAutoLBS',
              {
                type: 'gcj02',
              },
              (res) => {
                if (res.errCode !== 0) {
                  // 错误处理
                  console.log(`startAutoLBS:错误处理${JSON.stringify(res)}`);
                }
              },
            );

            WeCom.onLocationChange((res: PositionInfo) => {
              const { latitude, longitude } = res;
              if (latitude && longitude) {
                setPositionInfo(res);
              }
            });
          }, 300);
        },
      });
    });
  };

  useEffect(() => {
    getConfig();
  }, []);

  return { positionInfo };
}
