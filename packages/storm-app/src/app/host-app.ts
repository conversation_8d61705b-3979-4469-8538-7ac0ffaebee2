/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AppConfig,
  defaultTimePeriods,
  parseTimePeriods,
} from '@waterdesk/data/app-config';
import { MapViewName } from '@waterdesk/data/const/map';
import Database from '@waterdesk/data/database';
import {
  CommandMode,
  NotificationType,
} from '@waterdesk/data/dispatch-command/create-command';
import { DEFAULT_LAST_SIMULATION_DELAY_MINUTES } from '@waterdesk/data/scenes/running-state';
import { BASE_URL } from 'src/config';

import MapView from './core/map-view/map-view';

type Callback = () => void;

export default class HostApp {
  // eslint-disable-next-line no-use-before-define
  private static _instance: HostApp;

  listeners: Callback[] = [];

  static getInstance(): HostApp {
    if (HostApp._instance == null) {
      HostApp._instance = new HostApp();
    }

    return HostApp._instance;
  }

  constructor() {
    this._viewId = '';
    this._mapViews = new Map<MapViewName, MapView>();
    this._appConfig = {
      mapZoomFactor: 1,
      mapProjection: 'EPSG:4549',
      transformEPSG: '',
      animateZoomLevel: 5,
      highlightStyle: new Map(),
      proj4Register: undefined,
      scadaDownloadConfig: [],
      docPath: undefined,
      trackColumnsConfig: undefined,
      trackCustomConfig: undefined,
      trackDownConfig: undefined,
      trackPollutionConfig: undefined,
      trackUpConfig: undefined,
      runningStateConfig: {
        lastSimulationDelayMinutes: 60,
        showModelCalculationTime: false,
        cardItems: [],
        noticeItems: [],
        warnTabConfig: {},
        valveTabConfig: {},
        helpConfig: {},
      },
      dispatchSceneConfig: {
        noticeItems: [],
        warnTabConfig: {},
        valveTabConfig: {},
        helpConfig: {},
      },
      warningSceneConfig: {
        secondFilterItems: [],
        warnConfig: {},
      },
      rawWaterMonitoringSceneConfig: {
        helpConfig: {},
      },
      timePeriods: defaultTimePeriods,
      valveAnalysisConfig: undefined,
      blinkNoticeWarnQueryDuration: 5,
      pumpStateColor: {
        closed: '#808080',
        variable: '#009ece',
        fixed: '#00c542',
      },
      contextMenu: [],
      sidebarMenu: [],
      showPredict: false,
      showWeatherForecast: false,
      batchQuery: {
        objects: [],
      },
      assessmentDevice: {
        dataSource: {
          otypeList: [],
          vpropList: [],
        },
        formConfig: new Map(),
        score: [],
      },
      assessmentIndicator: {
        dataSource: {
          otypeList: [],
          vpropList: [],
        },
        formConfig: new Map(),
        score: [],
        themesConfig: {},
      },
      assessmentSimulation: {
        meanAbsoluteError: [],
        formConfig: new Map(),
        formula: '',
        score: [],
        themesConfig: {},
      },
      progressStep: 20,
      burstPipeAnalysisConfig: undefined,
      solutionAnalysisConfig: undefined,
      notFlashWarnStatus: [],
      notNoticeWarnStatus: [],
      plantAndPumpStationTreeFilter: [],
      issueReportConfig: {},
      workOrderConfig: { options: [] },
      waterNoticeConfig: { options: [] },
      mapToolWidgetConfig: {
        online: {},
        solution: {},
        solutionCompare: {},
      },
      enableHighlightRefModel: false,
      notificationType: [NotificationType.SYSTEM],
      commandMode: CommandMode.THRESHOLD,
      reportConfig: { requestUrl: '' },
    };
    this._curDb = new Database();
  }

  private _loginPage: string = '/login';

  get loginPage(): string {
    return this._loginPage;
  }

  set loginPage(path: string) {
    this._loginPage = path;
  }

  private _viewId: string;

  private _curDb: Database;

  get viewId(): string {
    return this._viewId;
  }

  set viewId(id: string) {
    this._viewId = id;
  }

  private _mapViews: Map<MapViewName, MapView>;

  addMapView(name: MapViewName, view: MapView) {
    this._mapViews.set(name, view);
  }

  getMapView(name: MapViewName): MapView | undefined {
    return this._mapViews.get(name);
  }

  removeMapView(name: MapViewName) {
    const mapView = this._mapViews.get(name);
    if (mapView) {
      mapView.dispose();
      this._mapViews.delete(name);
    }
  }

  cleanMapViews() {
    this._mapViews.forEach((mapView) => {
      mapView.dispose();
    });
    this._mapViews = new Map();
  }

  getMapViews(names?: MapViewName[]): MapView[] | undefined {
    let mapViews: MapView[] = [];
    if (names) {
      names.forEach((item) => {
        const mapView = this._mapViews.get(item);
        if (mapView) mapViews.push(mapView);
      });
    } else {
      mapViews = [...this._mapViews.values()];
    }
    return mapViews;
  }

  getMainMapView(): MapView | undefined {
    if (this._mapViews.size === 0) return undefined;
    const [mapView] = this._mapViews.values();
    return mapView;
  }

  redrawMapView() {
    this.getMapViews()?.forEach((mapView) => {
      mapView?.redraw();
    });
  }

  getCurDb(mapView?: MapView): Database {
    if (!mapView) {
      return this._curDb;
    }
    return mapView.curDb;
  }

  resetMapView() {
    this._mapViews = new Map();
  }

  private _appConfig: AppConfig;

  get appConfig(): AppConfig {
    return this._appConfig;
  }

  subscribe(callback: Callback) {
    this.listeners.push(callback);
  }

  unsubscribe(callback: Callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  notifyAll() {
    this.listeners.forEach((callback) => callback());
  }

  initializeAppConfig(data: any) {
    this._appConfig = {
      mapZoomFactor: data.mapZoomFactor || 1,
      mapProjection: data.mapProjection || 'EPSG:4549',
      animateZoomLevel: data.animateZoomLevel || 5,
      proj4Register: data.proj4Register,
      highlightStyle: data.highlightStyle,
      gisRoadConfig: {
        type: data?.gisRoadConfig?.type,
        config: data?.gisRoadConfig?.config,
      },
      scadaDownloadConfig: data.scadaDownloadConfig,
      docPath: data.docPath,
      trackColumnsConfig: data.trackColumnsConfig,
      trackCustomConfig: data.trackCustomConfig,
      trackUpConfig: data.trackUpConfig,
      trackDownConfig: data.trackDownConfig,
      trackPollutionConfig: data.trackPollutionConfig,
      runningStateConfig: {
        ...data?.runningStateConfig,
        lastSimulationDelayMinutes:
          data?.runningStateConfig?.lastSimulationDelayMinutes ??
          DEFAULT_LAST_SIMULATION_DELAY_MINUTES,
      },
      dispatchSceneConfig: data?.dispatchSceneConfig ?? {},
      warningSceneConfig: {
        secondFilterItems: data?.warningSceneConfig?.secondFilterItems ?? [],
        warnConfig: data?.warningSceneConfig?.warnConfig ?? {},
      },
      rawWaterMonitoringSceneConfig: data.rawWaterMonitoringSceneConfig,
      timePeriods: parseTimePeriods(data.timePeriods) ?? defaultTimePeriods,
      valveAnalysisConfig: data?.valveAnalysisConfig ?? {},
      blinkNoticeWarnQueryDuration: data?.blinkNoticeWarnQueryDuration ?? 5,
      pumpStateColor: data?.pumpStateColor,
      contextMenu: data?.contextMenu || [],
      sidebarMenu: data?.sidebarMenu || [],
      showPredict: data?.showPredict,
      showWeatherForecast: data?.showWeatherForecast,
      batchQuery: data?.batchQuery || {
        objects: [],
      },
      assessmentDevice: data.assessmentDevice,
      assessmentIndicator: data.assessmentIndicator,
      assessmentSimulation: data.assessmentSimulation,
      progressStep: data.progressStep ?? 20,
      burstPipeAnalysisConfig: data?.burstPipeAnalysisConfig ?? {},
      solutionAnalysisConfig: data?.solutionAnalysisConfig ?? {},
      transformEPSG: data?.transformEPSG,
      notFlashWarnStatus: data?.notFlashWarnStatus ?? [],
      notNoticeWarnStatus: data?.notFlashWarnStatus ?? [],
      timelineConfig: data?.timelineConfig ?? {},
      plantAndPumpStationTreeFilter: data?.plantAndPumpStationTreeFilter ?? {},
      issueReportConfig: data?.issueReportConfig ?? {},
      workOrderConfig: data?.workOrderConfig ?? { options: [] },
      waterNoticeConfig: data?.waterNoticeConfig ?? { options: [] },
      mapToolWidgetConfig: {
        online: data?.mapToolWidgetConfig?.online ?? {},
        solution: data?.mapToolWidgetConfig?.solution ?? {},
        solutionCompare: data?.mapToolWidgetConfig?.solutionCompare ?? {},
      },
      enableHighlightRefModel: data?.enableHighlightRefModel ?? false,
      notificationType: data?.notificationType ?? [NotificationType.SYSTEM],
      commandMode: data?.commandMode ?? CommandMode.THRESHOLD,
      reportConfig: data.reportConfig,
    };
    this.notifyAll();
  }
}

export function hostApp(): HostApp {
  return HostApp.getInstance();
}

export function curDb(mapViewId?: MapViewName, auto: boolean = true): Database {
  let mapView = auto ? hostApp().getMainMapView() : undefined;
  if (mapViewId) {
    mapView = hostApp().getMapView(mapViewId);
  }
  return hostApp().getCurDb(mapView);
}

export function getServiceUrl(api: string): string {
  return `${BASE_URL}${api}`;
}
