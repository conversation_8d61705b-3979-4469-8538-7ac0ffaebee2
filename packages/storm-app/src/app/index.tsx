/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AppstoreOutlined,
  CreditCardOutlined,
  FileSearchOutlined,
  MessageOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { AppMode } from '@waterdesk/data/app-config';
import { ResponseBodyJson, requestApi } from '@waterdesk/request/request';
import {
  checkAuthorizationPlugin as checkAuthorizationFn,
  redirectLoginPlugin as redirectLoginFn,
} from '@waterdesk/request/request-plugins';
import { SafeArea, TabBar } from 'antd-mobile';
import { ReactNode, useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useOutlet, useParams } from 'react-router';
import { useDispatchLogSlice } from 'src/store/dispatch-log';
import { appDataActions, useAppDataSlice } from '../store/app-data';
import { baseActions, useBaseSlice } from '../store/base';
import { selectLayoutMainLoading, selectViewId } from '../store/base/selectors';
import { useUserConfigSlice } from '../store/user-config';
import { transformGeoLocationCoordinate } from './data/geo-location';
import useDeviceDetect from './hooks/use-device-detect';
import useWeComLocation from './hooks/use-wecom-location';
import { hostApp } from './host-app';
import { Wrapper } from './style';

const Bottom = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { pathname } = location;
  const { token } = useToken();

  const setRouteActive = (value: string) => {
    navigate(value);
  };

  const tabs: { key: string; title: string; icon?: ReactNode }[] = [
    { key: '/app/Supply', title: '地图', icon: <AppstoreOutlined /> },
    { key: '/app/devices/Supply', title: '列表', icon: <CreditCardOutlined /> },
    { key: '/app/messages/Supply', title: '消息', icon: <MessageOutlined /> },
    {
      key: '/app/solutionList/Supply',
      title: '方案',
      icon: <FileSearchOutlined />,
    },
    { key: '/app/user', title: '我的', icon: <UserOutlined /> },
  ];

  return (
    <TabBar
      activeKey={pathname}
      onChange={(value) => setRouteActive(value)}
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {tabs.map((item) => (
        <TabBar.Item
          key={item.key}
          icon={item.icon}
          title={item.title}
        />
      ))}
    </TabBar>
  );
};

export default function WaterApp() {
  useAppDataSlice();
  useBaseSlice();
  useUserConfigSlice();
  useDispatchLogSlice();

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const outlet = useOutlet();
  const viewId = useSelector(selectViewId);
  const loading = useSelector(selectLayoutMainLoading);
  const messageRef = useRef<string>('');

  const redirectToLogin = () => {
    if (!pathname.startsWith('/appLogin')) {
      localStorage.removeItem('token');
      navigate('/appLogin');
    }
  };

  const checkAuthorizationPlugin = (request: Request) =>
    checkAuthorizationFn(request, redirectToLogin);
  const redirectLoginPlugin = (
    response: Response,
    responseBody: ResponseBodyJson,
  ) => redirectLoginFn(response, responseBody, redirectToLogin);

  requestApi.injectRequestInterceptors([
    { name: 'checkAuthorizationPlugin', handler: checkAuthorizationPlugin },
  ]);
  requestApi.injectResponseInterceptors([
    { name: 'redirectLoginPlugin', handler: redirectLoginPlugin },
  ]);
  const { dataMode } = useParams();

  if (dataMode) requestApi.addCommonSearchParams({ data_mode: dataMode });

  // 用于监听 react Native 传入消息
  hostApp().loginPage = '/appLogin';

  const updateLocation = (message: string) => {
    const coordinate = transformGeoLocationCoordinate(
      message,
      hostApp().appConfig.transformEPSG ?? hostApp().appConfig.mapProjection,
    );
    dispatch(appDataActions.updateGeoLocation({ geoLocation: coordinate }));
  };

  // 企业微信定位接口
  const { positionInfo } = useWeComLocation();
  useDeviceDetect();

  useEffect(() => {
    document.addEventListener('message', (event: any) => {
      const message = event?.data;
      messageRef.current = message;
    });

    dispatch(baseActions.updateAppMode({ appMode: AppMode.MOBILE }));
    dispatch(baseActions.initializeLayoutMainSaga());
  }, []);

  useEffect(() => {
    if (positionInfo && viewId) {
      updateLocation(
        JSON.stringify({
          longitude: positionInfo.longitude,
          latitude: positionInfo.latitude,
        }),
      );
    }
  }, [positionInfo, viewId]);

  useEffect(() => {
    if (messageRef.current) {
      updateLocation(messageRef.current);
    }
  }, [viewId]);

  return (
    <>
      <Helmet>
        <meta
          name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"
        />
      </Helmet>
      <Wrapper className="app">
        <div className="body"> {loading ? null : outlet}</div>
        <div className="bottom">
          <Bottom />
        </div>
        <SafeArea position="bottom" />
      </Wrapper>
    </>
  );
}
