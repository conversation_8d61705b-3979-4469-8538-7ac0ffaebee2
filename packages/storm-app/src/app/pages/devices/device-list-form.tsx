/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DownOutlined } from '@ant-design/icons';
import { Form, FormInstance } from 'antd';
import { Dropdown, SearchBar, Selector, SelectorOption } from 'antd-mobile';
import { useState } from 'react';

export interface FormValues {
  otypes: string[];
  states: string[];
  organization: string[];
  keyword: string;
}
interface Props {
  form: FormInstance<FormValues>;
  deviceTypeOption: SelectorOption<string>[];
  deviceOrganizationOption: SelectorOption<string>[];
  stateOptions: SelectorOption<string | number>[];
}
export default function DeviceListForm(props: Props) {
  const { form, deviceTypeOption, deviceOrganizationOption, stateOptions } =
    props;

  const [activeKey, setActiveKey] = useState<string | null>(null);
  return (
    <Form
      layout="inline"
      form={form}
    >
      <Form.Item
        name="keyword"
        style={{ width: '100%', padding: '8px' }}
      >
        <SearchBar
          style={{
            '--height': '40px',
          }}
        />
      </Form.Item>
      <Dropdown
        arrow={<DownOutlined />}
        style={{ width: '100%' }}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      >
        <Dropdown.Item
          key="deviceReliability"
          title="设备归属"
          forceRender
        >
          <Form.Item
            name="organization"
            style={{ padding: '8px' }}
          >
            <Selector
              columns={3}
              options={deviceOrganizationOption}
              style={{ fontSize: '12px' }}
              multiple
            />
          </Form.Item>
        </Dropdown.Item>
        <Dropdown.Item
          key="deviceOrganization"
          title="设备评级"
          forceRender
        >
          <Form.Item
            name="states"
            style={{ padding: '8px' }}
          >
            <Selector
              columns={3}
              options={stateOptions}
              style={{ fontSize: '12px' }}
              multiple
            />
          </Form.Item>
        </Dropdown.Item>
        <Dropdown.Item
          key="deviceType"
          title="设备类型"
          forceRender
        >
          <Form.Item
            name="otypes"
            style={{ padding: '8px' }}
          >
            <Selector
              columns={3}
              options={deviceTypeOption}
              style={{ fontSize: '12px' }}
              multiple
            />
          </Form.Item>
        </Dropdown.Item>
      </Dropdown>
    </Form>
  );
}
