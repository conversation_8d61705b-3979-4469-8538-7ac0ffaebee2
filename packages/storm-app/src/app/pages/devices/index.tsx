/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import Device from '@waterdesk/data/device';
import { ScadaModelTimeData } from '@waterdesk/data/device-time-data';
import { IndicatorObject } from '@waterdesk/data/indicator';
import { InitialDeviceInfoValues } from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import { ScadaTreeListItem } from '@waterdesk/data/scada-tree-data';
import {
  getCurrentTimeState,
  getDateTimeFromValue,
} from '@waterdesk/data/time-data';
import { getUnitFormat, getUnitValue } from '@waterdesk/data/unit-system';
import {
  GetDeviceAssessmentsResponse,
  getDeviceAssessments,
} from '@waterdesk/request/get-device-assessments';
import getDeviceTimeData from '@waterdesk/request/get-device-time-data';
import { Form, Tag } from 'antd';
import { List, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import ScadaLabel from 'src/components/scada-label';
import { baseActions, useBaseSlice } from 'src/store/base';
import {
  selectCurrentTimeDataChanged,
  selectDataInitialComplete,
} from 'src/store/base/selectors';
import {
  selectTimelineDate,
  selectTimelineTime,
} from 'src/store/time-line/selectors';
import DeviceListForm, { FormValues } from './device-list-form';
import useDeviceChart from './use-device-chart';

interface DeviceType extends ScadaTreeListItem {
  state?: string | number;
}

function getDeviceTreeByProperty(
  db: Database,
  propertyName: string,
  parentId: string,
): DeviceType[] {
  const devices = db.getAllDevices();

  const groupMap: Map<string, ScadaTreeListItem> = new Map();
  devices.forEach((device) => {
    const propertyValue = device.getPropertyValue(propertyName) || '其它';
    const group = groupMap.get(propertyValue);
    if (group) {
      (group.deviceCount as number) += 1;
    } else {
      groupMap.set(propertyValue, {
        id: propertyValue,
        title: db.getPropertyInfo(propertyValue)?.title || propertyValue,
        parentId,
        order: groupMap.size,
        deviceCount: 1,
        type: 'other',
      });
    }
    const otherGroup = groupMap.get('其它');
    if (otherGroup) otherGroup.order = groupMap.size + 1;
  });
  return [...groupMap.values()];
}

export default function Devices() {
  useBaseSlice();
  const dispatch = useDispatch();
  const timeIndex = useSelector(selectTimelineTime);
  const timelineDate = useSelector(selectTimelineDate);
  const dataInitialComplete = useSelector(selectDataInitialComplete);
  const currentTimeDataChanged = useSelector(selectCurrentTimeDataChanged);
  const [scadaTreeListItem, setScadaTreeListItem] = useState<DeviceType[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [scadaModelTimeData, setScadaModelTimeData] = useState<
    Map<string, ScadaModelTimeData>
  >(new Map());
  const [devicesState, setDevicesState] = useState<
    InitialDeviceInfoValues | undefined
  >();

  const [currentDevices, setCurrentDevices] = useState<Device[]>([]);
  const [deviceOrganizations, setDeviceOrganizations] = useState<DeviceType[]>(
    [],
  );

  const [form] = Form.useForm<FormValues>();
  const { popupChart, setObject, displayChart } = useDeviceChart();

  const otypes = Form.useWatch('otypes', form);
  const states = Form.useWatch('states', form);
  const organization = Form.useWatch('organization', form);
  const keyword = Form.useWatch('keyword', form);

  const reliabilityUnit = getUnitFormat('RELIABILITY');
  const unitValues = reliabilityUnit
    ? reliabilityUnit.getYAxisValues().map((item) => ({
        label: reliabilityUnit.getValueWithSymbol(item),
        value: item,
      }))
    : [];
  const getDeviceState = async (): Promise<
    InitialDeviceInfoValues | undefined
  > => {
    const { otypeList = [], vpropList = [] } =
      hostApp().appConfig.assessmentDevice.dataSource;
    const res: GetDeviceAssessmentsResponse = await getDeviceAssessments({
      otype_list: otypeList.join(','),
      vprop_list: vpropList.join(','),
      time: timelineDate,
    });
    if (res.status === 'Success' && res.devices) {
      return res.devices;
    }
    return undefined;
  };

  const getScadaData = () => {
    const time = getDateTimeFromValue(timeIndex, dayjs(timelineDate));
    const timeString = time.format('YYYY-MM-DD HH:mm:ss');
    getDeviceTimeData(
      timeString,
      getCurrentTimeState(timeIndex, dayjs(timelineDate)) === 'future',
      curDb(),
    ).then((res) => {
      if (res.status === 'Success') {
        curDb().currentDeviceTimeData.updateIndicatorData(
          res.scadaModelTimeData as Map<string, ScadaModelTimeData>,
        );
        setScadaModelTimeData(
          res.scadaModelTimeData as Map<string, ScadaModelTimeData>,
        );
        dispatch(
          baseActions.updateCurrentTimeData({
            updateTime: timeString,
          }),
        );
      }
    });
  };

  useEffect(() => {
    getScadaData();
  }, []);

  useEffect(() => {
    if (dataInitialComplete) {
      getDeviceState().then((res) => {
        setDevicesState(res);
        const devices = getDeviceTreeByProperty(curDb(), 'OTYPE', 'OTYPE');
        const devicesOrganizations = getDeviceTreeByProperty(
          curDb(),
          'ORANIZATION',
          'ORANIZATION',
        );
        if (devices.length > 0) {
          setScadaTreeListItem(devices);
          setDeviceOrganizations(devicesOrganizations);
          setDevices(curDb().getAllDevices());
          form.setFieldsValue({
            otypes: [devices[0].id],
            states: [],
            keyword: '',
          });

          getScadaData();
        }
      });
    }
  }, [dataInitialComplete, timelineDate]);

  const displayObject = (item: Device) => {
    setObject(item);
    displayChart();
  };

  const getStateColor = (state: string | number | null) => {
    switch (state) {
      case '1':
        return 'error';
      case '2':
        return 'warning';
      case '3':
        return 'success';
      case '4':
        return 'processing';
      case '0':
      default:
        return 'default';
    }
  };

  const getState = (device: Device): ReactNode => {
    if (devicesState) {
      const initialDeviceInfoValues = devicesState.get(device.otype);
      if (initialDeviceInfoValues) {
        const initialDeviceInfo = initialDeviceInfoValues.get(device.oname);
        if (initialDeviceInfo) {
          const value = getUnitValue(
            'RELIABILITY',
            initialDeviceInfo.RELIABILITY_S || '',
          );
          if (typeof value === 'undefined' || value === '') {
            return '';
          }
          return (
            <Tag
              color={getStateColor(initialDeviceInfo.RELIABILITY_S)}
              style={{ width: '55px', textAlign: 'center' }}
            >
              {getUnitValue(
                'RELIABILITY',
                initialDeviceInfo.RELIABILITY_S || '',
              )}
            </Tag>
          );
        }
      }
    }
    return '';
  };

  useEffect(() => {
    const sortFunction = (a: Device, b: Device) =>
      a.title.localeCompare(b.title);

    const currentDevices = devices
      .filter(
        (item) => otypes?.includes(item.otype) && item.title.includes(keyword),
      )
      .filter((item) => {
        if (organization) {
          if (organization.length === 0) {
            return true;
          }
          return organization?.includes(item.getPropertyValue('ORANIZATION'));
        }
        return true;
      })
      .filter((item) => {
        if (devicesState) {
          if (states.length === 0) {
            return true;
          }
          const initialDeviceInfoValues = devicesState.get(item.otype);
          if (initialDeviceInfoValues) {
            const initialDeviceInfo = initialDeviceInfoValues.get(item.oname);
            const reliability = (initialDeviceInfo?.RELIABILITY_S ??
              '') as string;
            if (states && reliability) {
              return states.includes(reliability);
            }
          }
        }
        return false;
      })
      .sort(sortFunction);
    setCurrentDevices(currentDevices);
  }, [otypes, states, organization, keyword]);

  const getScadaDataCell = (id: string): ScadaModelTimeData | undefined =>
    curDb().currentDeviceTimeData.getIndicatorValueById(id);

  const getFirstIndicatorData = (device: Device): ReactNode | null => {
    const indicator: IndicatorObject | undefined = device.indicators[0];
    if (!indicator) {
      return null;
    }

    const [title, unitKey] = curDb(undefined, false).getPropertyTitleUnit(
      indicator.otype,
      'SDVAL',
    );
    const unit = unitKey ? getUnitFormat(unitKey) : undefined;
    const value = (
      <ScadaLabel
        id={indicator.id}
        dataType="scadaData"
        getScadaDataCell={getScadaDataCell}
        currentTimeDataChanged={currentTimeDataChanged}
      />
    );
    return (
      <Space
        align="center"
        style={{ marginTop: '5px' }}
      >
        <span>{title}:</span>
        <span>{value}</span>
        <span>{unit ? unit.unitSymbol : ''}</span>
      </Space>
    );
  };

  const getIndicatorDataTime = (device: Device): ReactNode | null => {
    const indicator: IndicatorObject | undefined = device.indicators[0];
    if (!indicator) {
      return null;
    }
    const scadaData = scadaModelTimeData
      ? scadaModelTimeData.get(indicator.id)
      : undefined;

    return (
      <Space
        align="center"
        style={{ marginTop: '5px' }}
      >
        <span>取数时间:</span>
        <span>{scadaData ? scadaData.scadaData?.time : '-'}</span>
      </Space>
    );
  };

  return (
    <div
      style={{
        width: '100vw',
      }}
    >
      <DeviceListForm
        form={form}
        deviceTypeOption={scadaTreeListItem.map((item) => ({
          label: item.title,
          value: item.id,
        }))}
        deviceOrganizationOption={deviceOrganizations.map((item) => ({
          label: item.title,
          value: item.id,
        }))}
        stateOptions={unitValues}
      />
      <List
        style={{
          height: 'calc(100vh - 152px)',
          overflow: 'auto',
          '--font-size': '14px',
        }}
      >
        {currentDevices.map((item) => (
          <List.Item
            key={item.id}
            onClick={() => displayObject(item)}
            description={
              <Space
                wrap
                direction="vertical"
                style={{ fontSize: '12px', overflow: 'hidden', '--gap': '0px' }}
              >
                <span>{getFirstIndicatorData(item)}</span>
                <span>地址: {item.getPropertyValue('ADDRESS') ?? '无'}</span>
                <span>{getIndicatorDataTime(item)}</span>
              </Space>
            }
          >
            {getState(item) ?? '无'}
            {item.title}
          </List.Item>
        ))}
      </List>
      {popupChart}
    </div>
  );
}
