/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { getChartPropertiesByConfig } from '@waterdesk/data/device';
import { IObjectItem, makeId, splitId } from '@waterdesk/data/object-item';
import { Form } from 'antd';
import { Card, NavBar, Popup } from 'antd-mobile';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { HocObjectChartContent } from 'src/app/pages/objects-analysis/hoc-analysis-object-chart';
import {
  ObjectChartFormValues,
  TimeRangeType,
} from 'src/components/charts/object-chart/object-chart-content';
import { baseActions, useBaseSlice } from 'src/store/base';
import { selectBottomOpen } from 'src/store/base/selectors';
import { selectGlobalConfig } from 'src/store/user-config/selector';
import ScadaChartForm from '../../../components/chart/scada-chart-form';
import ObjectChartContainer from '../../container/chart/object-chart-container';
import { ChartWrap } from './style';

export default function useDeviceChart() {
  useBaseSlice();

  const dispatch = useDispatch();

  const globalConfig = useSelector(selectGlobalConfig);
  const ChartOpen = useSelector(selectBottomOpen);
  const { token } = useToken();

  const { schemeType = [] } = globalConfig?.schemeConfig ?? {};

  const [visible, setVisible] = useState(false);
  const [timeRanges, setTimeRanges] = useState<[Dayjs, Dayjs]>([
    dayjs(),
    dayjs(),
  ]);
  const [selectedObject, setSelectedObject] = useState<
    IObjectItem | undefined
  >();
  const [form] = Form.useForm<ObjectChartFormValues>();
  const timeRange = Form.useWatch('timeRange', form);

  useEffect(() => {
    if (timeRange) {
      setTimeRanges(timeRange);
    }
  }, [timeRange]);

  const indicatorsOptions = useMemo(() => {
    if (selectedObject) {
      const chartIndicators =
        getChartPropertiesByConfig(
          curDb(undefined, false),
          selectedObject,
          'chartConfig',
        ) ?? [];
      return chartIndicators.map(({ oname, otype, vprop }) =>
        makeId(otype, oname, vprop),
      );
    }
    return [];
  }, [selectedObject]);

  const deviceObjectsMap: Map<string, IObjectItem[]> = useMemo(() => {
    const deviceObjectsMap: Map<string, IObjectItem[]> = new Map();
    const indicators = indicatorsOptions;
    if (selectedObject && indicators) {
      if (Array.isArray(indicators)) {
        indicators.forEach((id) => {
          const devices = deviceObjectsMap.get(id);
          if (typeof devices === 'undefined') {
            deviceObjectsMap.set(id, [selectedObject]);
          } else {
            devices.push(selectedObject);
          }
        });
      }
    }
    return deviceObjectsMap;
  }, [selectedObject, indicatorsOptions]);

  const displayChart = useCallback(() => {
    if (selectedObject) {
      setVisible(true);
    }
  }, [selectedObject]);

  const onClose = () => {
    setVisible(false);
  };

  const handleCloseAllTabs = () => {
    dispatch(
      baseActions.updateBottomTab({
        type: 'CLOSE',
      }),
    );
  };

  return {
    popupChart: (
      <Popup
        position="right"
        visible={visible}
      >
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
            backgroundColor: token.colorBorderSecondary,
          }}
        >
          <NavBar
            onBack={onClose}
            style={{
              backgroundColor: token.colorBgContainer,
              '--border-bottom': `1px solid ${token.colorBorder}`,
            }}
          >
            {selectedObject?.title}
          </NavBar>
          <ScadaChartForm
            visible={visible}
            form={form}
            showTimeRange
            timeRangeTypeOptions={[
              TimeRangeType.threeDays,
              TimeRangeType.sevenDays,
              TimeRangeType.oneMonth,
              TimeRangeType.threeMonths,
              TimeRangeType.oneYear,
            ]}
            showTimeStep
          />
        </div>
        {deviceObjectsMap.size > 0 ? (
          <ChartWrap>
            {[...deviceObjectsMap.entries()].map(
              ([id, selectedObjects]: [string, IObjectItem[]], index) => {
                const [indicatorType, indicatorName, vprop] = splitId(id);
                return (
                  <Card
                    style={index > 0 ? { marginTop: '8px' } : undefined}
                    key={id}
                    title={
                      <span>
                        {curDb().getPropertyTitleUnit(indicatorType, vprop)[0]}
                      </span>
                    }
                  >
                    <HocObjectChartContent
                      selectedObjects={selectedObjects}
                      indicatorType={indicatorType}
                      indicatorName={indicatorName}
                      form={form}
                      vprop={vprop}
                      formData={{ timeRange: timeRanges }}
                      database={curDb(undefined, false)}
                      isConnect
                      showToolbox={false}
                      showDataTable={false}
                      schemeType={schemeType}
                    />
                  </Card>
                );
              },
            )}
          </ChartWrap>
        ) : null}
      </Popup>
    ),
    popupObjectChart: (
      <Popup
        position="right"
        visible={ChartOpen}
      >
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
            backgroundColor: token.colorBorderSecondary,
          }}
        >
          <NavBar
            onBack={handleCloseAllTabs}
            style={{ backgroundColor: token.colorBgContainer }}
          >
            {selectedObject?.title}
          </NavBar>
        </div>
        <div style={{ padding: '0 8px' }}>
          <ObjectChartContainer
            showToolbox={false}
            visible={ChartOpen}
          />
        </div>
      </Popup>
    ),
    displayChart,
    setObject: (selectedObject: IObjectItem) => {
      setSelectedObject(selectedObject);
    },
  };
}
