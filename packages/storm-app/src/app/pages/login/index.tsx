/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AppMode } from '@waterdesk/data/app-config';
import { MenuInfo } from '@waterdesk/data/menu-data';
import {
  GetLoginUserInfoResponse,
  getLoginUserInfo,
} from '@waterdesk/request/get-user-info';
import {
  LoginResponse,
  onLoginSuccess,
  requestLogin,
} from '@waterdesk/request/login';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import useDeviceDetect from 'src/app/hooks/use-device-detect';
import Login from 'src/components/login';
import { APP_NAME, appDeviceDetect, wxworkAuthUrl } from 'src/config';
import { baseActions } from 'src/store/base';
import { useThemeSlice } from 'src/store/theme';
import { selectTheme } from 'src/store/theme/selector';

export default function AppLoginWrap() {
  useDeviceDetect();

  useThemeSlice();
  const dispatch = useDispatch();
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const theme = useSelector(selectTheme);

  const navigateToHomePage = (menuList: MenuInfo[] | undefined) => {
    const homePageMenuInfo: MenuInfo | undefined = (menuList || []).find(
      (item) => item.homepage,
    );

    if (!homePageMenuInfo?.url) {
      messageApi.error('获取首页失败');
      return;
    }
    navigate('/app/Supply');
  };

  const login = async (username: string, password: string) => {
    try {
      setLoading(true);
      const response: LoginResponse = await requestLogin(
        username,
        password,
        APP_NAME,
        'APP',
      );
      if (response.status === 'Success') {
        onLoginSuccess(response);
        const loginUserInfo: GetLoginUserInfoResponse = await getLoginUserInfo({
          appId: APP_NAME,
        });
        if (loginUserInfo.status === 'Success') {
          navigateToHomePage(loginUserInfo.permissionList);
        } else {
          messageApi.error('获取用户信息失败!');
        }
      } else {
        console.log(response.errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    dispatch(baseActions.updateAppMode({ appMode: AppMode.MOBILE }));
    if (appDeviceDetect?.match('wxwork') && wxworkAuthUrl) {
      window.location.href = wxworkAuthUrl;
    }
  }, []);

  return (
    <>
      <Helmet>
        <meta
          name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"
        />
      </Helmet>
      <Login
        onFinish={login}
        loading={loading}
        theme={theme}
        appMode={AppMode.MOBILE}
      />
      {contextHolder}
    </>
  );
}
