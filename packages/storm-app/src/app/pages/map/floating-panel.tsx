/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CloseOutlined } from '@ant-design/icons';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  SCENE_TYPE_MODEL,
  SCENE_TYPE_RUN,
  SCENE_TYPE_SCADA,
} from '@waterdesk/data/scene';
import { Button } from 'antd';
import { Card, FloatingPanelRef, InputRef, SearchBar } from 'antd-mobile';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import { highlightActions } from 'src/store/highlight';
import { useLeftWrapperSlice } from 'src/store/left-wrapper';
import { selectCurrentSceneId } from 'src/store/scenes/selectors';
import { selectSelectionItem } from 'src/store/selection/selector';
import BurstPipeFlushingContainer from '../../container/burst-pipe-flushing';
import DevicesDashboardContainer from '../../container/map-panel/devices-dashboard-container';
import RunningStateContainer from '../../container/map-panel/running-state-container';
import SimulationDashboardContainer from '../../container/map-panel/simulation-dashboard-container';
import useTrackContainer from '../../container/track/use-track-container';
import ValveAnalysisContainer from '../../container/valve-analyse';
import ValveEditorContainer from '../../container/valve-editor';
import useDeviceChart from '../devices/use-device-chart';
import PropertyPanel from '../property';
import ToolBar from '../tool/tool-bar';
import { FloatingPanelWrapper } from './style';

interface Props {
  openSearch: (flag: boolean) => void;
}
export default function FloatingPanelContainer(props: Props) {
  useLeftWrapperSlice();

  const { openSearch } = props;
  const dispatch = useDispatch();
  const anchors = [125, window.innerHeight * 0.4, window.innerHeight];
  const floatingPanelRef = useRef<FloatingPanelRef>(null);
  const searchBarButton = useRef<InputRef>(null);
  const selectedItems = useSelector(selectSelectionItem);
  const currentSceneId = useSelector(selectCurrentSceneId);

  const [displayCloseButton, setDisplayCloseButton] = useState<boolean>(false);

  const { popupChart, popupObjectChart, setObject, displayChart } =
    useDeviceChart();

  const { trackForm, resultPanel } = useTrackContainer();
  const { burstPipeForm, burstPipeResult } = BurstPipeFlushingContainer();
  const { valveAnalyseForm, valveAnalysisResult } = ValveAnalysisContainer();

  const getCurrentItem = (): IObjectItem | undefined => {
    if (selectedItems.length > 0) {
      const selectionCollection =
        hostApp().getMainMapView()?.selectionCollection;
      return selectionCollection?.firstSelectedObject;
    }
    return undefined;
  };

  const onClosePanel = () => {
    setDisplayCloseButton(false);
    floatingPanelRef.current?.setHeight(125);
  };

  const onHeightChange = (height: number, animating: boolean) => {
    if (!animating) {
      if (height === 125) {
        setDisplayCloseButton(false);
      } else {
        setDisplayCloseButton(true);
      }
    }
  };

  const onFocusSearch = () => {
    openSearch(true);
    searchBarButton.current?.blur();
  };

  const handleRelativeHighlight = (
    _deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: 'relativeHighlight',
      }),
    );
    if (highlightObject) {
      dispatch(
        highlightActions.updateHighlight({
          highlight: { relativeHighlight: highlightObject },
        }),
      );
    }
  };

  const handleDmaRelatedDeviceHighlight = (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => {
    dispatch(
      highlightActions.clearHighlight({
        highlightLayerName: 'dmaRelativeDeviceHighlight',
      }),
    );
    if (highlightObject && displayHighlight) {
      dispatch(
        highlightActions.updateHighlight({
          highlight: {
            dmaRelativeDeviceHighlight: highlightObject,
          },
        }),
      );
    }
  };

  useEffect(() => {
    const currentItem = getCurrentItem();
    if (currentItem) {
      setObject(currentItem);
      floatingPanelRef.current?.setHeight(window.innerHeight * 0.4);
    } else {
      onClosePanel();
    }
  }, [selectedItems.toString()]);

  return (
    <FloatingPanelWrapper
      ref={floatingPanelRef}
      anchors={anchors}
      style={{ zIndex: 0 }}
      onHeightChange={onHeightChange}
    >
      {!resultPanel && !burstPipeResult && !valveAnalysisResult ? (
        <Card bodyStyle={{ paddingTop: 0 }}>
          <SearchBar
            ref={searchBarButton}
            placeholder="请输入内容"
            onFocus={onFocusSearch}
            style={{ '--height': '40px' }}
          />
        </Card>
      ) : null}
      <ToolBar
        IssueReportButton
        zoomCurrentPositionButton
      />
      {displayCloseButton ? (
        <Button
          ghost
          style={{
            position: 'absolute',
            top: '0',
            right: '0',
          }}
          icon={<CloseOutlined />}
          onClick={onClosePanel}
        />
      ) : null}
      {resultPanel}
      {burstPipeResult}
      {valveAnalysisResult}
      {selectedItems.length > 0 &&
      !resultPanel &&
      !burstPipeResult &&
      !valveAnalysisResult ? (
        <>
          <PropertyPanel
            displayChart={displayChart}
            handleRelativeHighlight={handleRelativeHighlight}
            handleDmaRelatedDeviceHighlight={handleDmaRelatedDeviceHighlight}
          />
          {popupChart}
          {popupObjectChart}
          {trackForm}
          {burstPipeForm}
          {valveAnalyseForm}
          <ValveEditorContainer />
        </>
      ) : null}
      <RunningStateContainer
        open={currentSceneId === SCENE_TYPE_RUN && selectedItems.length === 0}
      />
      <DevicesDashboardContainer
        open={currentSceneId === SCENE_TYPE_SCADA && selectedItems.length === 0}
      />
      <SimulationDashboardContainer
        open={currentSceneId === SCENE_TYPE_MODEL && selectedItems.length === 0}
      />
    </FloatingPanelWrapper>
  );
}
