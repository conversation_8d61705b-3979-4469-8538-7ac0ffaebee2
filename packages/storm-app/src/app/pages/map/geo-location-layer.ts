/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme } from '@waterdesk/core/theme';
import { Feature } from 'ol';
import { Coordinate } from 'ol/coordinate';
import { Point } from 'ol/geom';
import BaseLayer from 'ol/layer/Base';
import VectorLayer from 'ol/layer/Vector';
import { default as OlMap } from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import { Circle, Fill, Style } from 'ol/style';
import { IMapLayer } from 'src/app/core/map-layer/map-layer';

export default class GeoLocationLayer implements IMapLayer {
  private static generateStyle(): Style {
    return new Style({
      fill: new Fill({
        color: '#ffcc33',
      }),
      image: new Circle({
        radius: 7,
        fill: new Fill({
          color: '#ffcc33',
        }),
      }),
    });
  }

  private _name: string;

  private _map: OlMap;

  private _source: VectorSource | null = null;

  private _layer: VectorLayer | null = null;

  constructor(map: OlMap) {
    this._name = 'geoLocationLayer';
    this._map = map;
    this.initialize();
    if (this._layer) {
      this._map.addLayer(this._layer);
    }
  }

  private initialize() {
    this._source = new VectorSource({});
    this._layer = new VectorLayer({
      source: this._source,
      style: GeoLocationLayer.generateStyle(),
    });
  }

  get name(): string {
    return this._name;
  }

  get layer(): BaseLayer | null {
    return this._layer;
  }

  get source(): VectorSource | null {
    return this._source;
  }

  updateFeature(coordinate: Coordinate) {
    this._source?.clear();
    const feature = new Feature({
      geometry: new Point(coordinate),
    });
    feature.setStyle(GeoLocationLayer.generateStyle());
    this._source?.addFeature(feature);
  }

  redraw(): void {
    this._source?.changed();
  }

  setVisible(visible: boolean) {
    this._layer?.setVisible(visible);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
  setTheme(_theme: Theme) {}
}
