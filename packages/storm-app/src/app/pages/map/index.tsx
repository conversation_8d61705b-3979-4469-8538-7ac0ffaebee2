/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MAP_VIEW_NAME_ONLINE,
  MAP_VIEW_TYPE_ONLINE,
} from '@waterdesk/data/const/map';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import DmaCustomerContainer from 'src/app/core/dma-container/dma-customer-container';
import MapContainer from 'src/app/core/map-container';
import useDeviceTimeData from 'src/app/hooks/use-device-time-data';
import useUpdateDevice from 'src/app/hooks/use-update-device';
import useUpdateThemeTime from 'src/app/hooks/use-update-theme-time';
import { useThemeSlice } from 'src/store/theme';
import { timelineActions } from 'src/store/time-line';
import SearchContainer from '../tool';
import FloatingPanelContainer from './floating-panel';
import RealTime from './real-time';

export default function AppMap() {
  useThemeSlice();
  useDeviceTimeData({ needViewId: true });
  useUpdateDevice();
  useUpdateThemeTime();

  const dispatch = useDispatch();
  const appContent = useRef<HTMLDivElement>(null);

  const [openSearch, setOpenSearch] = useState<boolean>(false);

  useEffect(() => {
    dispatch(
      timelineActions.updateTimelineTime({
        timelineTime: getValueFromDateTime(dayjs()),
      }),
    );
  }, []);

  return (
    <div
      ref={appContent}
      style={{ width: '100%', height: '100%' }}
    >
      <MapContainer
        mapName={MAP_VIEW_NAME_ONLINE}
        mapType={MAP_VIEW_TYPE_ONLINE}
      />
      <RealTime />
      <DmaCustomerContainer />
      <SearchContainer
        visible={openSearch}
        onClose={setOpenSearch}
        getContainer={appContent.current}
      />
      <FloatingPanelContainer openSearch={setOpenSearch} />
    </div>
  );
}
