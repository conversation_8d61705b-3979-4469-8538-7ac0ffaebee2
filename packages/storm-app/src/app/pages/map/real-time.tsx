/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getValueFromDateTime } from '@waterdesk/data/time-data';
import dayjs, { Dayjs } from 'dayjs';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import SecondTimer from 'src/components/time-line/second-timer';
import { useTimelineSlice } from 'src/store/time-line';

export default function RealTime() {
  const dispatch = useDispatch();

  const { actions } = useTimelineSlice();
  const [latestTime, setLatestTime] = useState<Dayjs>(dayjs());

  const updateDateValue = (value: Dayjs) => {
    dispatch(
      actions.updateTimelineDate({
        timelineDate: dayjs(value).format('YYYY-MM-DD'),
      }),
    );
  };
  const updateTimeValue = (value: number) => {
    dispatch(
      actions.updateTimelineTime({
        timelineTime: value,
      }),
    );
  };

  const handleSecondTimerFinish = () => {
    const nowDate: Dayjs = dayjs();
    setLatestTime(nowDate);
    const timeValue = getValueFromDateTime(nowDate);
    if (timeValue === 0) updateDateValue(nowDate);
    updateTimeValue(timeValue);
  };
  return (
    <div
      style={{
        position: 'absolute',
        top: 10,
        left: 10,
        borderRadius: '4px',
        backgroundColor: '#1677ffcc',
        color: '#fff',
        padding: '0 10px',
      }}
    >
      {latestTime.format('MM月DD日 HH:mm')}:
      <SecondTimer
        style={{ fontSize: '1.2em' }}
        onFinish={handleSecondTimerFinish}
      />
    </div>
  );
}
