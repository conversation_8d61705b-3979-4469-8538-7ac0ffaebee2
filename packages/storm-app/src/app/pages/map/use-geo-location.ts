/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import { selectGeoLocation } from 'src/store/app-data/selectors';
import { selectViewId } from 'src/store/base/selectors';
import GeoLocationLayer from './geo-location-layer';

export default function useGeoLocation() {
  const geoLocation = useSelector(selectGeoLocation);
  const viewId = useSelector(selectViewId);
  const geoLayer = useRef<GeoLocationLayer | null>(null);
  useEffect(() => {
    const mapView = hostApp().getMainMapView();

    if (mapView?.map) {
      geoLayer.current = new GeoLocationLayer(mapView.map);
    }
  }, [viewId]);

  useEffect(() => {
    if (geoLayer.current && geoLocation) {
      geoLayer.current.updateFeature(geoLocation);
    }
  }, [geoLocation]);
}
