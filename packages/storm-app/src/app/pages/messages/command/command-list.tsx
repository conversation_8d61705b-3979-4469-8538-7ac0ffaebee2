/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DispatchCommandList,
  DispatchCommandState,
  dispatchCommandTypeNameMap,
} from '@waterdesk/data/dispatch-command/command-list';
import { formatCommandDescription } from '@waterdesk/data/dispatch-command/create-command';
import { makeObjectId } from '@waterdesk/data/object-item';
import { PageParams } from '@waterdesk/request/api/api-request';
import {
  updateDispatchCommandReceiveInfo,
  updateDispatchCommandReplyInfo,
} from '@waterdesk/request/get-dispatch-command';
import {
  Button,
  InfiniteScroll,
  List,
  Popup,
  PullToRefresh,
  Toast,
} from 'antd-mobile';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { selectPlantsAndPumpStations } from 'src/store/dispatch-log/selector';
import {
  CommandSection,
  CommandStateTag,
  PopupTitle,
  StyledListItem,
} from '../style';
import ExecuteConfirmation from './execute-confirmation';
import ReceiveCommand from './receive-command';

interface CommandListProps {
  fetchCommandList: (params: PageParams) => Promise<DispatchCommandList[]>;
  initialCommandId?: string;
}

// 添加状态映射
const dispatchCommandStateNameMap = {
  [DispatchCommandState.SENT]: '待处理',
  [DispatchCommandState.READ_ACCEPTED]: '已接收',
  [DispatchCommandState.REPLIED]: '已同意',
  [DispatchCommandState.REJECTED]: '已拒绝',
} as const;

export default function CommandList({
  fetchCommandList,
  initialCommandId,
}: CommandListProps) {
  const { waterPlants, pumpStations } = useSelector(
    selectPlantsAndPumpStations,
  );

  const [data, setData] = useState<DispatchCommandList[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 20;
  const [selectedCommand, setSelectedCommand] = useState<DispatchCommandList>();
  const [visible, setVisible] = useState(false);

  const getCommandDeviceTitle = useCallback(
    (receiveOrganization: string) =>
      [...waterPlants, ...pumpStations].find(
        (device) =>
          makeObjectId(device.otype, device.oname) === receiveOrganization,
      )?.title ?? receiveOrganization,
    [waterPlants, pumpStations],
  );

  const sortByState = (data: DispatchCommandList[]) =>
    [...data].sort((a, b) => {
      if (
        a.state === DispatchCommandState.SENT &&
        b.state !== DispatchCommandState.SENT
      ) {
        return -1;
      }
      if (
        a.state !== DispatchCommandState.SENT &&
        b.state === DispatchCommandState.SENT
      ) {
        return 1;
      }
      // 如果状态相同，按时间倒序排列
      return dayjs(b.sendTime).valueOf() - dayjs(a.sendTime).valueOf();
    });

  const loadMore = async () => {
    const params: PageParams = {
      current: page,
      pageSize,
    };

    const res = await fetchCommandList(params);
    const newData = res || [];
    const allData = [...data, ...newData];
    setData(sortByState(allData));
    setHasMore(newData.length === pageSize);
    setPage(page + 1);
  };

  const refresh = async () => {
    const list = await fetchCommandList({ current: 1, pageSize: 20 });
    setData(sortByState(list));
  };

  const handleCommandAction = (item: DispatchCommandList) => {
    setSelectedCommand(item);
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
    setSelectedCommand(undefined);
  };

  const handleReceive = async () => {
    if (!selectedCommand?.id) return;

    try {
      const res = await updateDispatchCommandReceiveInfo({
        id: selectedCommand.id,
        state: DispatchCommandState.READ_ACCEPTED,
      });

      if (res.status === 'Success') {
        Toast.show({
          content: '接收成功',
          icon: 'success',
        });

        setSelectedCommand((prev) => ({
          ...prev,
          state: DispatchCommandState.READ_ACCEPTED,
        }));

        const list = await fetchCommandList({ current: 1, pageSize: 20 });
        const sortedList = sortByState(list);
        setData(sortedList);
      } else {
        Toast.show({
          content: '接收失败',
          icon: 'fail',
        });
      }
    } catch (error) {
      console.error(error);
      Toast.show({
        content: '操作失败',
        icon: 'fail',
      });
    }
  };

  const handleAgreeOrRefuse = async (isAgree: boolean, content?: string) => {
    if (!selectedCommand?.id) return;

    try {
      const res = await updateDispatchCommandReplyInfo({
        id: selectedCommand.id,
        state: isAgree
          ? DispatchCommandState.REPLIED
          : DispatchCommandState.REJECTED,
        replyContent: content,
      });

      if (res.status === 'Success') {
        Toast.show({
          content: isAgree ? '已同意执行' : '已拒绝执行',
          icon: 'success',
        });
        await refresh();
        handleClose();
      } else {
        Toast.show({
          content: '操作失败',
          icon: 'fail',
        });
      }
    } catch (error) {
      console.error(error);
      Toast.show({
        content: '操作失败',
        icon: 'fail',
      });
    }
  };

  useEffect(() => {
    const init = async () => {
      const list = await fetchCommandList({ current: 1, pageSize: 20 });
      const sortedList = sortByState(list);
      setData(sortedList);

      // 如果有初始命令 ID，找到对应的命令并打开弹窗
      if (initialCommandId) {
        const command = sortedList.find((item) => item.id === initialCommandId);
        if (command) {
          setSelectedCommand(command);
          setVisible(true);
        }
      }
    };

    init();
  }, [initialCommandId]);

  return (
    <>
      <PullToRefresh
        onRefresh={async () => {
          await refresh();
        }}
      >
        <List>
          {data.map((item) => (
            <StyledListItem
              isPending={item.state === DispatchCommandState.SENT}
              key={item.id}
              extra={
                <Button
                  color="primary"
                  size="small"
                  onClick={() => handleCommandAction(item)}
                >
                  {item.state === DispatchCommandState.SENT ? '处理' : '查看'}
                </Button>
              }
              description={
                <>
                  {dayjs(item.sendTime).format('YYYY-MM-DD HH:mm')}
                  <CommandStateTag
                    state={item.state ?? DispatchCommandState.SENT}
                  >
                    {dispatchCommandStateNameMap[
                      item.state as keyof typeof dispatchCommandStateNameMap
                    ] ?? item.state}
                  </CommandStateTag>
                </>
              }
            >
              [
              {dispatchCommandTypeNameMap[
                item.type as keyof typeof dispatchCommandTypeNameMap
              ] ?? item.type}
              ]
              {formatCommandDescription(
                item,
                curDb(),
                getCommandDeviceTitle(item.receiveOrganization ?? '') ?? '',
              )}
            </StyledListItem>
          ))}
          <InfiniteScroll
            loadMore={loadMore}
            hasMore={hasMore}
          />
        </List>
      </PullToRefresh>

      <Popup
        visible={visible}
        onMaskClick={handleClose}
        bodyStyle={{ height: '90vh', overflowY: 'auto', background: '#f5f5f5' }}
      >
        {selectedCommand && (
          <div>
            <PopupTitle>指令操作</PopupTitle>
            <CommandSection>
              <ReceiveCommand
                data={selectedCommand}
                curDb={curDb()}
                deviceTitle={getCommandDeviceTitle(
                  selectedCommand.receiveOrganization ?? '',
                )}
                onReceive={handleReceive}
              />
            </CommandSection>

            {selectedCommand.state !== DispatchCommandState.SENT && (
              <CommandSection>
                <ExecuteConfirmation
                  data={selectedCommand}
                  handleAgreeOrRefuse={handleAgreeOrRefuse}
                />
              </CommandSection>
            )}
          </div>
        )}
      </Popup>
    </>
  );
}
