/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DispatchCommandList,
  DispatchCommandState,
} from '@waterdesk/data/dispatch-command/command-list';
import { Button, TextArea } from 'antd-mobile';
import { useState } from 'react';
import Descriptions from 'src/components/description';
import styled from 'styled-components';
import { DescriptionsTitle } from '../style';

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

interface Props {
  data?: DispatchCommandList;
  handleAgreeOrRefuse: (isAgree: boolean, content?: string) => void;
}

export default function ExecuteConfirmation({
  data,
  handleAgreeOrRefuse,
}: Readonly<Props>) {
  const [replyContent, setReplyContent] = useState('');

  if (!data) return null;

  const descriptionItems = [
    ...(data?.state &&
    [DispatchCommandState.REPLIED, DispatchCommandState.REJECTED].includes(
      data?.state,
    )
      ? [
          {
            label: '回复时间',
            content: data?.replyTime ?? '-',
          },
          {
            label: '回复内容',
            content:
              data?.state === DispatchCommandState.REPLIED
                ? '同意执行'
                : '拒绝执行',
          },
        ]
      : []),
    {
      label: '备注',
      content:
        data?.state &&
        [DispatchCommandState.READ_ACCEPTED].includes(data?.state) ? (
          <TextArea
            rows={3}
            value={replyContent}
            onChange={setReplyContent}
            style={{
              backgroundColor: '#f5f5f5',
              borderRadius: '8px',
              width: '70vw',
              padding: '8px',
            }}
          />
        ) : (
          (data?.replyContent ?? '-')
        ),
    },
  ];

  return (
    <div>
      <DescriptionsTitle>回复指令</DescriptionsTitle>
      <Descriptions items={descriptionItems} />

      {data?.state &&
        [DispatchCommandState.READ_ACCEPTED].includes(data?.state) && (
          <ButtonGroup>
            <Button
              color="primary"
              block
              onClick={() => handleAgreeOrRefuse(true, replyContent)}
            >
              同意
            </Button>
            <Button
              color="danger"
              block
              onClick={() => handleAgreeOrRefuse(false, replyContent)}
            >
              拒绝
            </Button>
          </ButtonGroup>
        )}
    </div>
  );
}
