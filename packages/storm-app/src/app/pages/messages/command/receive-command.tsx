/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import {
  DispatchCommandList,
  DispatchCommandState,
  dispatchCommandSendOrganizationNameMap,
  dispatchCommandTypeNameMap,
} from '@waterdesk/data/dispatch-command/command-list';
import { formatCommandDescription } from '@waterdesk/data/dispatch-command/create-command';
import { formatTime } from '@waterdesk/data/mini-dashboard/energy-data';
import { Button } from 'antd-mobile';
import Descriptions from 'src/components/description';
import { DescriptionsTitle } from '../style';

interface Props {
  data?: DispatchCommandList;
  curDb: Database;
  deviceTitle?: string;
  onReceive?: () => void;
}

export default function ReceiveCommand({
  data,
  curDb,
  deviceTitle,
  onReceive,
}: Props) {
  if (!data) return null;

  const descriptionItems = [
    {
      label: '指令来源',
      content: data.sendOrganization
        ? dispatchCommandSendOrganizationNameMap[data.sendOrganization]
        : '-',
    },
    {
      label: '发送时间',
      content: formatTime(data.sendTime) ?? '-',
    },
    {
      label: '指令类型',
      content: data.type ? dispatchCommandTypeNameMap[data.type] : '-',
    },
    {
      label: '计划执行时间',
      content: formatTime(data.planTime) ?? '-',
    },
    {
      label: '接收组织',
      content: deviceTitle ?? '-',
    },
    {
      label: '指令内容',
      content: data.content
        ? formatCommandDescription(data, curDb, deviceTitle ?? '')
        : '-',
    },
    {
      label: '备注',
      content: data.note ?? '-',
    },
  ];

  return (
    <div>
      <DescriptionsTitle>接收指令</DescriptionsTitle>
      <Descriptions items={descriptionItems} />

      {data.state && [DispatchCommandState.SENT].includes(data.state) && (
        <div className="mt-8">
          <Button
            block
            color="primary"
            onClick={onReceive}
          >
            接收确认
          </Button>
        </div>
      )}
    </div>
  );
}
