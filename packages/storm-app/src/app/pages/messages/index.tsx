/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DispatchCommandState } from '@waterdesk/data/dispatch-command/command-list';
import { PageParams } from '@waterdesk/request/api/api-request';
import { getDispatchCommandListByCurrentUser } from '@waterdesk/request/get-dispatch-command';
import { getWarnListByDate } from '@waterdesk/request/get-warn';
import { Badge, Tabs } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { curDb } from 'src/app/host-app';
import { useBaseSlice } from 'src/store/base';
import { useTimelineSlice } from 'src/store/time-line';
import CommandList from './command/command-list';
import { MessageCenter, MessageTabsWrapper, StyledContainer } from './style';
import WarningList from './warning/warning-list';

const defaultPageParams: PageParams = {
  current: 1,
  pageSize: 20,
};

export default function Message() {
  useBaseSlice();
  useTimelineSlice();

  const [activeKey, setActiveKey] = useState('commands');
  const [commandCount, setCommandCount] = useState(0);
  const [warningCount, setWarningCount] = useState(0);
  const [messageId, setMessageId] = useState<string>();

  const fetchCommandList = async (params: PageParams) => {
    const res = await getDispatchCommandListByCurrentUser(params);
    setCommandCount(res.count[DispatchCommandState.SENT] ?? 0);
    return res.list || [];
  };

  const fetchWarningList = async () => {
    const res = await getWarnListByDate(
      {
        startTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD 00:00:00'),
        endTime: dayjs().format('YYYY-MM-DD 23:59:59'),
      },
      curDb(),
    );
    setWarningCount(res.warnStatusCounts.NOT_CONFIRM ?? 0);
    return res.list || [];
  };

  useEffect(() => {
    fetchCommandList(defaultPageParams);
    fetchWarningList();

    const { hash } = window.location;
    const searchParams = hash.split('?')[1];
    const urlParams = new URLSearchParams(searchParams);

    const tab = urlParams.get('tab');
    const id = urlParams.get('id');

    if (tab) {
      setActiveKey(tab);
    }

    if (id) {
      setMessageId(id);
    }
  }, []);

  return (
    <MessageCenter>
      <MessageTabsWrapper>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
        >
          <Tabs.Tab
            title={
              commandCount > 0 ? (
                <Badge content={commandCount}>指令</Badge>
              ) : (
                '指令'
              )
            }
            key="commands"
          />
          <Tabs.Tab
            title={
              warningCount > 0 ? (
                <Badge content={warningCount}>警告</Badge>
              ) : (
                '警告'
              )
            }
            key="warnings"
          />
        </Tabs>
      </MessageTabsWrapper>
      <StyledContainer>
        {activeKey === 'commands' && (
          <CommandList
            fetchCommandList={fetchCommandList}
            initialCommandId={messageId}
          />
        )}
        {activeKey === 'warnings' && (
          <WarningList
            fetchWarningList={fetchWarningList}
            initialWarningId={messageId}
          />
        )}
      </StyledContainer>
    </MessageCenter>
  );
}
