/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DispatchCommandState } from '@waterdesk/data/dispatch-command/command-list';
import { WarnConfirmStatus } from '@waterdesk/data/warn';
import { List } from 'antd-mobile';
import styled from 'styled-components';

export const MessageCenter = styled.div`
  width: 100vw;
  height: 100vh;
  height: -webkit-fill-available;
  height: stretch;
  max-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  padding: 0;
  padding-top: env(safe-area-inset-top);
`;

export const MessageTabsWrapper = styled.div`
  background: var(--adm-color-background);
  position: sticky;
  top: 0;
  z-index: 100;

  .adm-tabs-header {
    background: var(--adm-color-background);
  }
`;

export const StyledListItem = styled(List.Item)<{ isPending: boolean }>`
  opacity: ${(props) => (props.isPending ? 1 : 0.6)};
  color: ${(props) => (props.isPending ? 'inherit' : '#999')};
`;

export const StyledContainer = styled.div`
  flex: 1;
  min-height: 0;
  position: relative;
  height: 100%;

  .adm-pull-to-refresh {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 8px;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }
`;

export const PopupTitle = styled.div`
  font-size: 18px;
  font-weight: 600;
  margin-top: 5px;
  text-align: center;
  color: var(--adm-color-text);
`;

export const DescriptionsTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  margin: 0px 8px;
  color: var(--adm-color-text);
`;

export const CommandSection = styled.div`
  background: var(--adm-color-background);
  border-radius: 8px;
  padding: 16px;
  margin: 0px 10px;
  & + & {
    margin-top: 12px;
  }
`;

interface StateTagProps {
  state: DispatchCommandState;
}

const getCommandStateColor = (state: DispatchCommandState) => {
  switch (state) {
    case DispatchCommandState.SENT:
      return '#1890ff';
    case DispatchCommandState.READ_ACCEPTED:
      return '#faad14';
    case DispatchCommandState.REPLIED:
      return '#52c41a';
    case DispatchCommandState.REJECTED:
      return '#ff4d4f';
    default:
      return '#d9d9d9';
  }
};

export const CommandStateTag = styled.span<StateTagProps>`
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
  background-color: #fff;
  border: 1px solid ${({ state }) => getCommandStateColor(state)};
  color: ${({ state }) => getCommandStateColor(state)};
`;

interface WarningStateTagProps {
  state: WarnConfirmStatus;
}

const getWarningStateColor = (state: WarnConfirmStatus) => {
  switch (state) {
    case WarnConfirmStatus.NOT_CONFIRM:
      return '#1890ff';
    case WarnConfirmStatus.CONFIRM:
      return '#52c41a';
    case WarnConfirmStatus.SHELVE:
      return '#faad14';
    case WarnConfirmStatus.NOT_WARN:
      return '#8c8c8c';
    default:
      return '#8c8c8c';
  }
};
export const WarningStateTag = styled.span<WarningStateTagProps>`
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
  background-color: #fff;
  border: 1px solid ${({ state }) => getWarningStateColor(state)};
  color: ${({ state }) => getWarningStateColor(state)};
`;

export const WarningDescription = styled.div`
  font-size: 13px;
  color: #666;
  margin-top: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const WarningLevelTag = styled.span<{ color: string }>`
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
  background-color: #fff;
  color: ${({ color }) => color};
`;

export const WarningTypeTag = styled.span`
  font-size: 14px;
  margin-right: 8px;
  color: #595959;
  font-weight: 600;
`;

export const DetailList = styled.div`
  margin: 12px 0;
  background: white;
  border-radius: 8px;
`;

export const DetailItem = styled.div`
  margin-bottom: 12px;

  &:last-child {
    border-bottom: none;
  }
`;

export const DetailHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
`;

export const DetailDeviceName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #333;
`;

export const DetailContent = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px 12px;
`;

export const DetailLabel = styled.div`
  color: #666;
  font-size: 13px;
`;

export const DetailValue = styled.div`
  color: #333;
  font-size: 14px;
  word-break: break-all;
`;

export const CommentSection = styled.div`
  margin-top: 16px;
`;

export const PopupContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 90vh;
`;

export const PopupHeader = styled.div`
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 1;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
`;

export const PopupContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f5f5f5;
`;

export const PopupFooter = styled.div`
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
`;
