/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BarChartOutlined } from '@ant-design/icons';
import {
  getWarnConfirmStatusName,
  shelveTimeOptions,
  WarnConfirmStatus,
  WarnInfoItem,
  WarnInfoList,
} from '@waterdesk/data/warn';
import {
  getWarningLevelColor,
  getWarningLevelName,
} from '@waterdesk/data/warn/warn-level';
import { confirmWarnInfo, getWarnInfo } from '@waterdesk/request/get-warn';
import {
  Button,
  InfiniteScroll,
  List,
  Popup,
  PullToRefresh,
  Toast,
} from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { baseActions, useBaseSlice } from 'src/store/base';
import { PropertyChartActionArgs } from 'src/store/base/types';
import { useSelectionSlice } from 'src/store/selection';
import useDeviceChart from '../../devices/use-device-chart';
import {
  CommandSection,
  CommentSection,
  DetailContent,
  DetailDeviceName,
  DetailHeader,
  DetailItem,
  DetailLabel,
  DetailList,
  DetailValue,
  PopupContainer,
  PopupContent,
  PopupFooter,
  PopupHeader,
  PopupTitle,
  StyledListItem,
  WarningDescription,
  WarningLevelTag,
  WarningStateTag,
  WarningTypeTag,
} from '../style';

interface WarningListProps {
  fetchWarningList: () => Promise<WarnInfoList>;
  initialWarningId?: string;
}

// 添加筛选状态类型
const filterOptions = [
  { label: '全部', value: 'ALL' },
  { label: '待确认', value: WarnConfirmStatus.NOT_CONFIRM },
  { label: '已确认', value: WarnConfirmStatus.CONFIRM },
  { label: '搁置', value: WarnConfirmStatus.SHELVE },
  { label: '已忽略', value: WarnConfirmStatus.NOT_WARN },
];

export default function WarningList({
  fetchWarningList,
  initialWarningId,
}: WarningListProps) {
  useSelectionSlice();
  useBaseSlice();

  const { popupObjectChart, setObject, displayChart } = useDeviceChart();

  const dispatch = useDispatch();

  const [warningList, setWarningList] = useState<WarnInfoList>([]);
  const [displayedWarnings, setDisplayedWarnings] = useState<WarnInfoList>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 20;
  const [selectedWarning, setSelectedWarning] = useState<WarnInfoItem>();
  const [visible, setVisible] = useState(false);
  const [processingComment, setProcessingComment] = useState('');
  const [shelveVisible, setShelveVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState('ALL');

  const loadInitialData = async () => {
    try {
      const res = await fetchWarningList();
      const sortedList = (res || []).sort((a, b) => {
        // 首先比较确认状态
        if (
          a.confirmStatus === WarnConfirmStatus.NOT_CONFIRM &&
          b.confirmStatus !== WarnConfirmStatus.NOT_CONFIRM
        ) {
          return -1; // a排在前面
        }
        if (
          a.confirmStatus !== WarnConfirmStatus.NOT_CONFIRM &&
          b.confirmStatus === WarnConfirmStatus.NOT_CONFIRM
        ) {
          return 1; // b排在前面
        }
        // 如果确认状态相同，则按时间倒序排序
        return dayjs(b.startTime).valueOf() - dayjs(a.startTime).valueOf();
      });

      setWarningList(sortedList);
      // 应用当前筛选条件
      const filteredList =
        filterStatus === 'ALL'
          ? sortedList
          : sortedList.filter((item) => item.confirmStatus === filterStatus);
      setDisplayedWarnings(filteredList.slice(0, pageSize));
      setHasMore(filteredList.length > pageSize);

      if (initialWarningId) {
        const warning = sortedList.find((item) => item.id === initialWarningId);
        if (warning) {
          setSelectedWarning(warning);
          setVisible(true);
        }
      }
    } catch (error) {
      console.error('加载警告列表失败:', error);
      Toast.show({
        content: '加载失败',
        icon: 'fail',
      });
    }
  };

  const loadMore = async () => {
    // 使用当前的筛选状态
    const filteredList =
      filterStatus === 'ALL'
        ? warningList
        : warningList.filter((item) => item.confirmStatus === filterStatus);
    const nextPage = page + 1;
    const start = (nextPage - 1) * pageSize;
    const end = nextPage * pageSize;

    if (start < filteredList.length) {
      const nextItems = filteredList.slice(start, end);
      setDisplayedWarnings((prev) => [...prev, ...nextItems]);
      setPage(nextPage);
      setHasMore(end < filteredList.length);
    } else {
      setHasMore(false);
    }
  };

  const handleWarningAction = async (item: WarnInfoList[0]) => {
    const res = await getWarnInfo({ warnId: item.id }, curDb());

    // Sort the details to put isKey:true devices at the top
    if (res.data?.details && res.data.details.length > 0) {
      res.data.details.sort((a, b) => {
        // isKey:true devices come first
        if (a.isKey && !b.isKey) return -1;
        if (!a.isKey && b.isKey) return 1;
        return 0;
      });
    }

    setSelectedWarning(res.data);
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
    setSelectedWarning(undefined);
    setProcessingComment('');
    loadInitialData();
  };

  const displayObject = (warning: WarnInfoItem) => {
    if (!warning.details || warning.details.length === 0) {
      Toast.show({
        content: '警告没有设备详情，无法显示曲线',
        icon: 'fail',
      });
      return;
    }

    const detail = warning.details[0];

    const deviceObject = curDb().getDeviceById(detail.deviceId);

    if (!deviceObject) {
      Toast.show({
        content: '未找到设备对象，无法显示曲线',
        icon: 'fail',
      });
      return;
    }

    const warningTime = dayjs(warning.startTime);
    const startDate = warningTime.startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const endDate = warningTime.endOf('day').format('YYYY-MM-DD HH:mm:ss');

    setObject(deviceObject);
    displayChart();

    const propertyChartAction: PropertyChartActionArgs = {
      indicatorType: detail.otype,
      indicatorName: detail.oname,
      vprop: detail.vprop,
      pinnedItem: deviceObject,
      chartStartDate: startDate,
      chartEndDate: endDate,
    };
    dispatch(
      baseActions.propertyChartAction({
        arg: propertyChartAction,
      }),
    );
  };

  const handleShelveClick = () => {
    setShelveVisible(true);
  };

  const handleProcessWarning = async (
    confirmStatus: WarnConfirmStatus,
    shelveHours?: number,
  ) => {
    try {
      const res = await confirmWarnInfo({
        warnIdList: [selectedWarning?.id || ''],
        confirmStatus,
        note: processingComment,
        shelveTime: shelveHours,
      });

      if (res.status === 'Success') {
        let content = '忽略成功';
        if (confirmStatus === WarnConfirmStatus.CONFIRM) {
          content = '处理成功';
        } else if (confirmStatus === WarnConfirmStatus.SHELVE) {
          content = '搁置成功';
        }

        Toast.show({
          content,
          icon: 'success',
        });

        handleClose();
      }
    } catch (error) {
      console.error('处理警告失败:', error);
      Toast.show({
        content: '处理失败',
        icon: 'fail',
      });
    }
  };

  const handleShelveConfirm = async (hours: number) => {
    setShelveVisible(false);
    await handleProcessWarning(WarnConfirmStatus.SHELVE, hours);
  };

  const handleFilterChange = (value: string) => {
    setFilterStatus(value);
    setPage(1);
    const filteredList =
      value === 'ALL'
        ? warningList
        : warningList.filter((item) => item.confirmStatus === value);
    setDisplayedWarnings(filteredList.slice(0, pageSize));
    setHasMore(filteredList.length > pageSize);
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (initialWarningId && warningList.length > 0 && !visible) {
      const warning = warningList.find((item) => item.id === initialWarningId);
      if (warning) {
        setSelectedWarning(warning);
        setVisible(true);
      }
    }
  }, [warningList, initialWarningId]);

  return (
    <>
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div
          style={{
            padding: '10px 16px',
            background: '#fff',
            display: 'flex',
            gap: '8px',
            overflowX: 'auto',
            position: 'sticky',
            top: 0,
            height: '50px',
            zIndex: 1,
          }}
        >
          {filterOptions.map((option) => (
            <Button
              key={option.value}
              size="small"
              color={filterStatus === option.value ? 'primary' : 'default'}
              onClick={() => handleFilterChange(option.value)}
              style={{ whiteSpace: 'nowrap' }}
            >
              {option.label}
            </Button>
          ))}
        </div>
        <div style={{ flex: 1 }}>
          <PullToRefresh onRefresh={loadInitialData}>
            <List style={{ flex: 1, marginTop: '35px' }}>
              {displayedWarnings.map((item, index) => (
                <StyledListItem
                  isPending={
                    item.confirmStatus === WarnConfirmStatus.NOT_CONFIRM
                  }
                  key={item.id || index}
                  extra={
                    <Button
                      color="primary"
                      size="small"
                      onClick={() => handleWarningAction(item)}
                    >
                      {item.confirmStatus === WarnConfirmStatus.NOT_CONFIRM
                        ? '处理'
                        : '查看'}
                    </Button>
                  }
                >
                  <div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: '4px',
                      }}
                    >
                      <WarningLevelTag color={getWarningLevelColor(item.rank)}>
                        {getWarningLevelName(item.rank, true)}
                      </WarningLevelTag>
                      <WarningTypeTag>{item.secondTypeName}</WarningTypeTag>
                    </div>
                    <WarningDescription>
                      {item.description || '-'}
                    </WarningDescription>
                    <div
                      style={{
                        fontSize: '12px',
                        color: '#999',
                        marginTop: '4px',
                      }}
                    >
                      {item.startTime &&
                        dayjs(item.startTime).format('YYYY-MM-DD HH:mm')}
                      <WarningStateTag state={item.confirmStatus}>
                        {getWarnConfirmStatusName(item.confirmStatus)}
                      </WarningStateTag>
                    </div>
                  </div>
                </StyledListItem>
              ))}
              <InfiniteScroll
                loadMore={loadMore}
                hasMore={hasMore}
              />
            </List>
          </PullToRefresh>
        </div>
      </div>
      <Popup
        visible={visible}
        onMaskClick={handleClose}
        bodyStyle={{ height: '90vh', overflow: 'hidden' }}
      >
        {selectedWarning && (
          <PopupContainer>
            <PopupHeader>
              <PopupTitle>警告详情</PopupTitle>
            </PopupHeader>

            <PopupContent>
              <CommandSection>
                <div>
                  <div style={{ marginBottom: '12px' }}>
                    <WarningLevelTag
                      color={getWarningLevelColor(selectedWarning.rank)}
                    >
                      {getWarningLevelName(selectedWarning.rank)}
                    </WarningLevelTag>
                    <WarningTypeTag>
                      {selectedWarning.secondTypeName}
                    </WarningTypeTag>
                  </div>
                  <h4>{selectedWarning.description || '-'}</h4>
                  {selectedWarning.startTime && (
                    <p style={{ marginBottom: '4px' }}>
                      开始时间:{' '}
                      {dayjs(selectedWarning.startTime).format(
                        'YYYY-MM-DD HH:mm:ss',
                      )}
                    </p>
                  )}
                  {selectedWarning.endTime && (
                    <p>
                      结束时间:{' '}
                      {selectedWarning.endStatus === 0
                        ? '持续中'
                        : dayjs(selectedWarning.endTime).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )}
                    </p>
                  )}

                  <DetailList>
                    {selectedWarning.details?.map((detail, index) => (
                      <DetailItem key={detail.id || index}>
                        <DetailHeader>
                          <DetailDeviceName>
                            {detail.isKey ? '主要设备' : '其他设备'}{' '}
                            {detail.name}
                          </DetailDeviceName>
                        </DetailHeader>

                        <DetailContent>
                          {detail.typeName && (
                            <>
                              <DetailLabel>设备类型</DetailLabel>
                              <DetailValue>{detail.typeName}</DetailValue>
                            </>
                          )}

                          {detail.abnormalType && (
                            <>
                              <DetailLabel>异常类型</DetailLabel>
                              <DetailValue>{detail.abnormalType}</DetailValue>
                            </>
                          )}

                          {detail.description && (
                            <>
                              <DetailLabel>异常描述</DetailLabel>
                              <DetailValue>{detail.description}</DetailValue>
                            </>
                          )}

                          <DetailLabel>开始时间</DetailLabel>
                          <DetailValue>
                            {dayjs(detail.startTime).format(
                              'YYYY-MM-DD HH:mm:ss',
                            )}
                          </DetailValue>

                          {detail.endTime && (
                            <>
                              <DetailLabel>结束时间</DetailLabel>
                              <DetailValue>
                                {detail.endStatus === 0
                                  ? '持续中'
                                  : dayjs(detail.endTime).format(
                                      'YYYY-MM-DD HH:mm:ss',
                                    )}
                              </DetailValue>
                            </>
                          )}

                          <DetailLabel>查看曲线</DetailLabel>
                          <DetailValue>
                            <BarChartOutlined
                              type="primary"
                              onClick={() => displayObject(selectedWarning)}
                            />
                          </DetailValue>
                        </DetailContent>
                      </DetailItem>
                    ))}
                  </DetailList>

                  <CommentSection>
                    <DetailLabel>处理意见</DetailLabel>
                    <textarea
                      rows={4}
                      style={{
                        width: '100%',
                        padding: '8px',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                        marginTop: '8px',
                      }}
                      placeholder="请输入处理意见..."
                      value={processingComment}
                      onChange={(e) => setProcessingComment(e.target.value)}
                    />
                  </CommentSection>

                  {popupObjectChart}
                </div>
              </CommandSection>
            </PopupContent>

            <PopupFooter>
              <Button
                color="primary"
                style={{ flex: 1 }}
                onClick={() => handleProcessWarning(WarnConfirmStatus.CONFIRM)}
              >
                处理
              </Button>
              <Button
                color="warning"
                style={{ flex: 1 }}
                onClick={handleShelveClick}
              >
                搁置
              </Button>
              <Button
                color="danger"
                style={{ flex: 1 }}
                onClick={() => handleProcessWarning(WarnConfirmStatus.NOT_WARN)}
              >
                忽略
              </Button>
            </PopupFooter>
          </PopupContainer>
        )}
      </Popup>
      <Popup
        visible={shelveVisible}
        onMaskClick={() => setShelveVisible(false)}
        bodyStyle={{ height: 'auto' }}
      >
        <div style={{ padding: '16px' }}>
          <h4>选择搁置时间</h4>
          <List>
            {shelveTimeOptions.map((option) => (
              <List.Item
                key={option.value}
                onClick={() => handleShelveConfirm(option.value)}
              >
                {option.label}
              </List.Item>
            ))}
          </List>
        </div>
      </Popup>
    </>
  );
}
