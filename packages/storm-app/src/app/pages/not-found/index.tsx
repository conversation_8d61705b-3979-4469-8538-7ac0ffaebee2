/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router';
import { useUserConfigSlice } from 'src/store/user-config';
import { P, Title, Wrapper } from './style';

const AppNotFoundPage = () => {
  const location = useLocation();
  const { state } = location;
  const message: string | undefined = state?.message;
  useUserConfigSlice();

  return (
    <>
      <Helmet>
        <title>404 Page Not Found</title>
        <meta
          name="description"
          content="Page not found"
        />
      </Helmet>
      <Wrapper>
        <Title>
          4
          <span
            role="img"
            aria-label="Crying Face"
          >
            😢
          </span>{' '}
          4
        </Title>
        <P>{message || 'Page not found.'}</P>
      </Wrapper>
    </>
  );
};

export default AppNotFoundPage;
