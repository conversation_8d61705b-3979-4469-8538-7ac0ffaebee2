/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const Wrapper = styled.div`
  height: calc(100vh - 45px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 320px;
`;

export const Title = styled.div`
  margin-top: -8vh;
  font-weight: bold;
  color: ${({ theme }) => theme.colorTextBase};
  font-size: 3.375rem;
  span {
    font-size: 3.125rem;
  }
`;

export const P = styled.p`
  font-size: 1rem;
  line-height: 1.5;
  color: ${({ theme }) => theme.colorTextSecondary};
  margin: 0.625rem 0 1.5rem 0;
`;
