/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import { getChartPropertiesByConfig } from '@waterdesk/data/device';
import { IObjectItem, makeObjectId } from '@waterdesk/data/object-item';
import { ChartProperties } from '@waterdesk/data/property/property-info';
import { GroupTimeData } from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { getMarkListByDate } from '@waterdesk/request/get-chart-mark';
import { getGroupTimeValues } from '@waterdesk/request/get-group-time-values';
import { getWarnListByDate } from '@waterdesk/request/get-warn';
import { Form, FormInstance } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import useWarningConfig from 'src/app/core/containers/warning/use-warning-config';
import { curDb } from 'src/app/host-app';
import ObjectChartContent, {
  ObjectChartFormValues,
} from 'src/components/charts/object-chart/object-chart-content';
import { useBaseSlice } from 'src/store/base';
import { makeChartCode } from 'src/store/selection';
import { useThemeSlice } from 'src/store/theme';
import { selectTheme } from 'src/store/theme/selector';
import { selectGlobalConfig } from 'src/store/user-config/selector';

type ObjectChartContentPropsType = typeof ObjectChartContent extends React.FC<
  infer P
>
  ? P
  : never;

type HocAnalysisObjectChartProps = Omit<
  ObjectChartContentPropsType,
  | 'darkMode'
  | 'getObjectTimeValues'
  | 'getWarnList'
  | 'chartCode'
  | 'isDailyChart'
  | 'formData'
  | 'selectedModelLegend'
> & {
  form?: FormInstance<Partial<ObjectChartFormValues>>;
  formData?: Partial<ObjectChartFormValues>;
  selectedModelLegend?: boolean;
};

const HocAnalysisObjectChart = <P extends HocAnalysisObjectChartProps>(
  WrapperComponent: React.ComponentType<P>,
) =>
  function Component(props: P) {
    useBaseSlice();
    useThemeSlice();

    const theme = useSelector(selectTheme);
    const globalConfig = useSelector(selectGlobalConfig);
    const {
      selectedObjects,
      indicatorType,
      indicatorName: indicatorNames,
      vprop,
      forecast,
      form,
      formData = {},
      markList,
      selectedModelLegend = false,
    } = props;

    const { schemeType = [] } = globalConfig?.schemeConfig ?? {};

    const { nonAssessmentWarnTypes } = useWarningConfig();

    const indicatorName = Array.isArray(indicatorNames)
      ? indicatorNames[0]
      : indicatorNames;

    const timeRange = Form.useWatch('timeRange', form);
    const yMin = Form.useWatch('yMin', form);
    const yMax = Form.useWatch('yMax', form);
    const chainBase = Form.useWatch('chainBase', form);
    const envelopLine = Form.useWatch('envelopLine', form);
    const warn = Form.useWatch('warn', form);
    const timeStep = Form.useWatch('timeStep', form);
    const multiDates = Form.useWatch('dateMultiSelect', form);
    const mark = Form.useWatch('mark', form);
    const showModel = Form.useWatch('showModel', form);

    const mergeFormData = {
      timeRange,
      yMin,
      yMax,
      chainBase,
      envelopLine,
      warn,
      timeStep,
      dateMultiSelect: multiDates,
      mark,
      ...formData,
    };

    const [markListData, setMarkListData] = useState<MarkInfoList | undefined>(
      [],
    );
    const [darkMode, setDarkMode] = useState<boolean>(false);
    const [chartProperty, setChartProperty] = useState<
      ChartProperties | undefined
    >(undefined);

    const isDailyChart = useMemo(() => {
      const chartEditor = chartProperty?.editors?.find(
        (item) => item.type === 'chart',
      );
      return chartEditor?.dateType === 'day';
    }, [chartProperty]);

    const chartCode = useMemo(
      () =>
        makeChartCode(
          selectedObjects.map((obj) => ({
            objectId: makeObjectId(obj.otype, obj.oname),
          })),
          indicatorType,
          indicatorName,
          vprop,
          forecast,
        ),
      [selectedObjects, indicatorType, indicatorName, vprop, forecast],
    );

    const getObjectTimeValues = async (
      object: IObjectItem,
      indicatorOType: string | undefined,
      indicatorOName: string | undefined,
      vprop: string,
      startDate: string,
      endDate: string,
      includeMinMax?: boolean,
      includeCorrelatedProps?: string[],
      timeStep?: number,
      multiDate?: string[],
    ): Promise<Map<string, GroupTimeData>> => {
      const res = await getGroupTimeValues(
        globalConfig?.envelopMinMaxField,
        object,
        indicatorOType,
        indicatorOName,
        vprop,
        startDate,
        endDate,
        includeMinMax,
        includeCorrelatedProps,
        timeStep,
        multiDate,
      );

      if (res.status === 'Success' && res.values) {
        return res.values;
      }
      return new Map();
    };

    const fetchWarnList = async (
      startDate: Dayjs,
      endDate: Dayjs,
      otype: string,
      oname: string,
      vprop: 'SDVAL',
    ): Promise<WarnInfoList> => {
      const responseData = await getWarnListByDate(
        {
          startTime: startDate.format('YYYY-MM-DD'),
          endTime: endDate.format('YYYY-MM-DD'),
          indicatorType: otype,
          indicatorName: oname,
          vprop,
          type: nonAssessmentWarnTypes,
        },
        curDb(),
      );

      return responseData.list;
    };

    const getMatchedChartProperty = (
      selectedObject: IObjectItem,
      indicatorType: string | undefined,
      indicatorName: string | undefined,
      vprop: string | undefined,
      ignoreDefault?: boolean,
    ): ChartProperties | undefined => {
      const chartProperties: ChartProperties[] = getChartPropertiesByConfig(
        curDb(),
        selectedObject,
      );

      for (let i = 0; i < chartProperties.length; i += 1) {
        const property = chartProperties[i];
        if (
          property.otype === indicatorType &&
          property.oname === indicatorName &&
          property.vprop === vprop
        ) {
          return property;
        }
      }

      for (let i = 0; i < chartProperties.length; i += 1) {
        const property = chartProperties[i];
        if (property.otype === indicatorType && property.vprop === vprop) {
          return property;
        }
      }

      if (ignoreDefault) return undefined;

      if (chartProperties.length > 0) return chartProperties[0];
      return undefined;
    };

    const fetchMarkList = async (
      startDate: Dayjs,
      endDate: Dayjs,
      indicatorType: string,
      indicatorNames: string | string[],
      vprop: string,
    ) => {
      const responseData = await getMarkListByDate({
        startDate: startDate.format('YYYY-MM-DD 00:00:00'),
        endDate: endDate.format('YYYY-MM-DD 00:00:00'),
        otypeList: [indicatorType],
        onameList: Array.isArray(indicatorNames)
          ? indicatorNames
          : [indicatorNames],
        vpropList: [vprop],
      });

      setMarkListData(responseData.list ?? []);
    };

    const mergeMarkList = markList ?? markListData;

    useEffect(() => {
      if (
        mergeFormData.timeRange &&
        indicatorNames &&
        indicatorType &&
        mergeFormData.mark &&
        typeof markList === 'undefined' &&
        vprop
      ) {
        const startDate = mergeFormData.timeRange[0];
        const endDate = mergeFormData.timeRange[1];
        fetchMarkList(startDate, endDate, indicatorType, indicatorNames, vprop);
      }
    }, [
      mergeFormData.mark,
      markList,
      mergeFormData.timeRange,
      indicatorNames,
      indicatorType,
      vprop,
    ]);

    useEffect(() => {
      if (chartCode === undefined) return;

      if (selectedObjects.length > 0) {
        const currentChartProperty = getMatchedChartProperty(
          selectedObjects[0],
          indicatorType,
          indicatorName,
          vprop,
        );
        setChartProperty(currentChartProperty);
      } else {
        setChartProperty(undefined);
      }
    }, [chartCode, selectedObjects]);

    useEffect(() => {
      setDarkMode(theme === 'dark');
    }, [theme]);

    return (
      <WrapperComponent
        {...props}
        formData={mergeFormData}
        isDailyChart={isDailyChart}
        chartCode={chartCode}
        darkMode={darkMode}
        getObjectTimeValues={getObjectTimeValues}
        getWarnList={fetchWarnList}
        markList={mergeMarkList}
        selectedModelLegend={showModel ?? selectedModelLegend}
        defaultShowAsLineType={
          globalConfig?.chartConfig.analysis.showAsLineType
        }
        schemeType={schemeType}
      />
    );
  };

/**
 * @deprecated
 */
export const HocObjectChartContent = HocAnalysisObjectChart(
  ObjectChartContent as React.ComponentType<HocAnalysisObjectChartProps>,
);
