/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const TabsContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    flex: 0 1 auto;
  }

  .content {
    margin-top: 20px;
    flex: 1 0 auto;
    overflow-y: auto;
  }

  .chart-content-show-table,
  .chart-content-show-chart,
  .chart-content-show-both {
    width: 100%;
    max-width: 100%;
    flex: 1;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px;
    .left-chart {
      flex: 2;
      overflow: auto;
    }
    .right-table {
      flex: 1;
      max-width: 440px;
      overflow: auto;
      height: 100%;
      &-card {
        height: 100%;
        overflow: auto;
      }
    }
  }
  .chart-content-show-chart {
    .left-chart {
      flex: 1 1 100%;
    }
    .right-table {
      display: none;
    }
  }
  .chart-content-show-table {
    .left-chart {
      display: none;
    }
    .right-table {
      flex: 1 1 100%;
      max-width: 100%;
    }
  }

  .chart-content-empty {
    justify-content: center;
    align-items: center;
  }
`;

export const TabsContentWrapperNew = styled.div`
  display: grid;
  grid-template-rows: auto 1fr;
  height: calc(100vh - 110px);
  .bottom {
    height: 100%;
    display: flex;
    align-items: center;
  }
`;
