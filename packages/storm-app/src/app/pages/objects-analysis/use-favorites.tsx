/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ChartCompareType } from '@waterdesk/data/object-chart';
import {
  ApplyType,
  convertSchemeTypeDataToOptions,
  ScadaFavorite,
} from '@waterdesk/data/scheme-config';
import { convertReactKeysToKeys } from '@waterdesk/data/utils';
import { addSchemeConfig } from '@waterdesk/request/get-scheme-config';
import { Form, Modal, message } from 'antd';
import dayjs from 'dayjs';
import { Key, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { ObjectChartFormValues } from 'src/components/charts/object-chart/object-chart-content';
import { CustomCompareTypeValue } from 'src/components/charts/object-chart/object-chart-content-new';
import ScadaFavoritesForm, {
  ScadaFavoritesFormValues,
} from 'src/components/scada-chart/scada-solution-form';
import { useBaseSlice } from 'src/store/base';
import { selectGlobalConfig } from 'src/store/user-config/selector';

const scadaFormDataStringify = (
  formData: ObjectChartFormValues | undefined,
): ScadaFavorite['formData'] => {
  if (!formData) return undefined;
  const newFormData: ScadaFavorite['formData'] = {
    ...formData,
    timeRange: undefined,
    compareType: undefined,
  };
  if ('timeRange' in formData && formData.timeRange) {
    newFormData.timeRange = [
      formData.timeRange[0].format('YYYY-MM-DD HH:mm:ss'),
      formData.timeRange[1].format('YYYY-MM-DD HH:mm:ss'),
    ];
  }
  if ('compareType' in formData && formData.compareType) {
    const dateRanges = formData.compareType.dateRanges
      .filter((f) => f?.[0] && f?.[1])
      .map((dateRange) => [
        dateRange![0]!.format('YYYY-MM-DD HH:mm:ss'),
        dateRange![1]!.format('YYYY-MM-DD HH:mm:ss'),
      ]);
    newFormData.compareType = {
      type: formData.compareType.type,
      dateRanges,
    };
  }
  return newFormData;
};

export const parseScadaFormData = (
  formData: ScadaFavorite['formData'],
): ObjectChartFormValues | undefined => {
  if (!formData) return undefined;
  const newFormData: ObjectChartFormValues = {
    ...formData,
    timeRange: undefined,
    compareType: undefined,
  };
  if ('timeRange' in formData && formData.timeRange) {
    newFormData.timeRange = [
      dayjs(formData.timeRange[0]),
      dayjs(formData.timeRange[1]),
    ];
  }

  if ('compareType' in formData && formData.compareType) {
    const dateRanges = formData.compareType.dateRanges
      .filter((f) => f?.[0] && f?.[1])
      .map((dateRange) => [
        dayjs(dateRange[0]),
        dayjs(dateRange[1]),
      ]) as CustomCompareTypeValue['dateRanges'];
    newFormData.compareType = {
      type: formData.compareType.type as ChartCompareType,
      dateRanges,
    };
  }
  return newFormData;
};

export type onSuccessCallback = (
  scadaFormData: ObjectChartFormValues,
  checkedKeys: Key[],
  applyType: ApplyType,
  scadaFavoritesFormData: ScadaFavoritesFormValues,
) => void;

export const useFavorites = () => {
  useBaseSlice();

  const globalConfig = useSelector(selectGlobalConfig);

  const { schemeType = [] } = globalConfig?.schemeConfig ?? {};

  const [messageApi, messageContextHolder] = message.useMessage();
  const [form] = Form.useForm<ScadaFavoritesFormValues>();
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const paramsRef = useRef<
    | {
        scadaFormData: ObjectChartFormValues;
        checkedKeys: Key[];
        applyType: ApplyType;
        tabType: string;
        callback?: onSuccessCallback;
      }
    | undefined
  >(undefined);

  const schemeTypeOptions = useMemo(
    () => convertSchemeTypeDataToOptions(schemeType) ?? [],
    [schemeType],
  );

  const save = (
    scadaFormData: ObjectChartFormValues,
    checkedKeys: Key[],
    tabType: string,
    applyType: ApplyType,
    callback?: (
      scadaFormData: ObjectChartFormValues,
      checkedKeys: Key[],
      applyType: ApplyType,
      scadaFavoritesFormData: ScadaFavoritesFormValues,
    ) => void,
  ) => {
    paramsRef.current = {
      scadaFormData,
      checkedKeys,
      tabType,
      applyType,
      callback,
    };
    setOpen(true);
    form.resetFields();
  };

  const handleOk = async () => {
    const values = await form.validateFields();
    const { title, note, schemeType, share, isFixedTime } = values;
    if (paramsRef.current) {
      const { scadaFormData, checkedKeys, tabType, applyType, callback } =
        paramsRef.current;
      setLoading(true);
      const scadaSolutionData: ScadaFavorite = {
        title,
        checkedKeys: convertReactKeysToKeys(checkedKeys),
        tabType,
        formData: scadaFormDataStringify(scadaFormData),
      };
      const addScheme = await addSchemeConfig({
        schemeName: title,
        applyType,
        schemeType,
        configValue: JSON.stringify(scadaSolutionData),
        schemeShare: share,
        remark: note,
        isFixedTime,
      });
      setLoading(false);
      if (addScheme.status === 'Success') {
        messageApi.success('保存成功!');
        callback?.(scadaFormData, checkedKeys, applyType, values);
        setOpen(false);
      } else {
        messageApi.error('保存失败!');
      }
    }
  };

  const contextHolder = (
    <Modal
      title="添加到收藏夹"
      confirmLoading={loading}
      open={open}
      width={600}
      onOk={handleOk}
      onCancel={() => setOpen(false)}
      destroyOnHidden
    >
      <ScadaFavoritesForm
        form={form}
        schemeTypeOptions={schemeTypeOptions}
      />
      {messageContextHolder}
    </Modal>
  );

  return { contextHolder, save };
};
