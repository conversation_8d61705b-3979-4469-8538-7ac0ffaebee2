/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IndicatorObject } from '@waterdesk/data/indicator';
import { makeObjectId } from '@waterdesk/data/object-item';
import { updateScadaData } from '@waterdesk/request/update-scada-data';
import { Form, InputNumber, Modal, message } from 'antd';
import { useRef, useState } from 'react';
import { curDb } from 'src/app/host-app';
import { UpdateMinMaxType } from 'src/components/charts/object-chart/use-axis-data';
import { useBaseSlice } from 'src/store/base';

interface FormData {
  LIMIT_MIN: number | undefined;
  LIMIT_MAX: number | undefined;
}

const useScadaLimit = () => {
  useBaseSlice();

  const [messageApi, messageContextHolder] = message.useMessage();
  const [form] = Form.useForm<FormData>();
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const paramsRef = useRef<
    | {
        indicator: IndicatorObject;
        vprop: string;
        callback?: (params: UpdateMinMaxType) => void;
      }
    | undefined
  >(undefined);

  const clickEditLimit = (
    indicator: IndicatorObject,
    vprop: string,
    callback?: (params: UpdateMinMaxType) => void,
  ) => {
    paramsRef.current = {
      indicator,
      vprop,
      callback,
    };

    form.setFieldsValue({
      LIMIT_MIN: indicator.minLimitation,
      LIMIT_MAX: indicator.maxLimitation,
    });
    setOpen(true);
  };

  const handleOk = async () => {
    const formData = form.getFieldsValue();
    const indicator = paramsRef.current?.indicator;
    const vprop = paramsRef.current?.vprop;
    const callback = paramsRef.current?.callback;

    if (indicator) {
      const params: {
        vprop: string;
        value: number | undefined;
      }[] = Object.keys(formData).map((item) => ({
        vprop: item,
        value: formData[item as keyof FormData],
      }));

      const res = await updateScadaData(
        indicator.otype,
        indicator.oname,
        params,
      );
      if (res.status === 'Success') {
        setLoading(false);
        if (vprop) {
          callback?.({
            otype: indicator.otype,
            oname: indicator.oname,
            vprop,
            min: formData.LIMIT_MIN ?? undefined,
            max: formData.LIMIT_MAX ?? undefined,
          });
        }
        curDb().updateIndicatorInfoById(
          makeObjectId(indicator.otype, indicator.oname),
          formData,
        );
        messageApi.success('修改成功');
      }
      setOpen(false);
      form.resetFields();
    }
  };

  const contextScadaLimitHolder = (
    <Modal
      title="修改上下限"
      confirmLoading={loading}
      open={open}
      width={600}
      onOk={handleOk}
      onCancel={() => setOpen(false)}
      destroyOnHidden
    >
      <Form
        name="scada-limit-form"
        form={form}
        autoComplete="off"
      >
        <Form.Item
          name="LIMIT_MAX"
          label="超过数据上限"
        >
          <InputNumber />
        </Form.Item>
        <Form.Item
          name="LIMIT_MIN"
          label="小于数据下限"
        >
          <InputNumber />
        </Form.Item>
      </Form>
      {messageContextHolder}
    </Modal>
  );

  return { contextScadaLimitHolder, clickEditLimit };
};

export default useScadaLimit;
