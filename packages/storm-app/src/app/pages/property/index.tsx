/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BarChartOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import Device, { PropertyValue } from '@waterdesk/data/device';
import GisObject from '@waterdesk/data/gis-object';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import {
  FlowRelativeIndicatorValue,
  PressureRelativeIndicatorValue,
} from '@waterdesk/data/indicator';
import ModelObject from '@waterdesk/data/model-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  DMA_THIRD,
  DmaRelatedDevice,
  DmaType,
} from '@waterdesk/data/property/dma-related-devices-category';
import { PropertyButtonEditor } from '@waterdesk/data/property/property-info';
import { GroupTimeData, getDateTimeFromValue } from '@waterdesk/data/time-data';
import { TRACK_DOWN, TRACK_UP } from '@waterdesk/data/track-data';
import { WarnInfoList, WarnPrimaryType } from '@waterdesk/data/warn';
import { getDevicePictures } from '@waterdesk/request/api/get-device-picture';
import {
  getFlowCorrelationDevices,
  getPressureCorrelationDevices,
} from '@waterdesk/request/get-correlation';
import {
  getDmaRelatedDevices,
  getThirdDmaRelatedDevices,
} from '@waterdesk/request/get-dma-related-devices';
import { getAllMapViewsGroupPropValues } from '@waterdesk/request/get-group-prop-values';
import { getAllGroupObjectsTimeValues } from '@waterdesk/request/get-group-time-values';
import { getWarnListByDate } from '@waterdesk/request/get-warn';
import { NavBar } from 'antd-mobile';
import dayjs from 'dayjs';
import { ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ButtonContainer from 'src/app/core/containers/button-container';
import useWarningConfig from 'src/app/core/containers/warning/use-warning-config';
import { curDb, hostApp } from 'src/app/host-app';
import { PropertyItemValue } from 'src/components/property/property-item';
import { baseActions } from 'src/store/base';
import { selectDeviceUpdateDate } from 'src/store/base/selectors';
import { PropertyChartActionArgs } from 'src/store/base/types';
import { highlightActions } from 'src/store/highlight';
import { leftWrapperActions } from 'src/store/left-wrapper';
import {
  MENU_TOOLS_BURST_PIPE,
  MENU_TOOLS_VALVE,
} from 'src/store/left-wrapper/types';
import { selectSelectionItem } from 'src/store/selection/selector';
import {
  selectTimelineDate,
  selectTimelineTime,
} from 'src/store/time-line/selectors';
import { valveEditorActions } from 'src/store/valve-editor';
import PropertyPalette from '../../../components/property/property-palette';
import { PropertyWrapper } from './style';

interface Props {
  handleRelativeHighlight: (
    deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => void;
  handleDmaRelatedDeviceHighlight: (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => void;
  displayChart: () => void;
}
export default function PropertyPanel(props: Props) {
  const {
    handleRelativeHighlight,
    handleDmaRelatedDeviceHighlight,
    displayChart,
  } = props;
  const dispatch = useDispatch();
  const selectedItems = useSelector(selectSelectionItem);
  const timelineTime = useSelector(selectTimelineTime);
  const timelineDate = useSelector(selectTimelineDate);
  const deviceUpdateDate = useSelector(selectDeviceUpdateDate);
  const { token } = useToken();

  const { warnTypeData, assessmentWarnType, nonAssessmentWarnTypes } =
    useWarningConfig();

  const [selectedObject, setSelectedObject] = useState<IObjectItem | undefined>(
    undefined,
  );
  const [propValues, setPropValues] = useState<Map<string, PropertyValue[]>>(
    new Map(),
  );
  const [title, setTitle] = useState<string>('无选中对象');

  const updateObjectProperties = (currentItem: IObjectItem | undefined) => {
    if (currentItem !== undefined) {
      const time = getDateTimeFromValue(timelineTime, dayjs(timelineDate));
      if (currentItem instanceof Device || currentItem instanceof ModelObject) {
        getAllMapViewsGroupPropValues(
          currentItem,
          curDb(),
          hostApp().getMapViews(),
          time.format('YYYY-MM-DD HH:mm:ss'),
        ).then((result) => {
          setSelectedObject(currentItem);
          if (result) setPropValues(result);
        });
      } else if (currentItem instanceof GisObject) {
        if (currentItem.refModelObject) {
          getAllMapViewsGroupPropValues(
            currentItem.refModelObject,
            curDb(),
            hostApp().getMapViews(),
            time.format('YYYY-MM-DD HH:mm:ss'),
          ).then((result) => {
            setSelectedObject(currentItem);
            if (result) setPropValues(result);
          });
        } else {
          setSelectedObject(currentItem);
          setPropValues(new Map());
        }
      } else {
        setSelectedObject(undefined);
        setPropValues(new Map());
      }

      if (currentItem) {
        if (currentItem instanceof GisObject) {
          setTitle(`${currentItem.layerName}: ${currentItem.title}`);
        } else {
          const propertyInfo = curDb().getPropertyInfo(currentItem.otype);
          if (propertyInfo)
            setTitle(`${propertyInfo.title}: ${currentItem.title}`);
          else setTitle(currentItem.title);
        }
      }
    } else {
      setSelectedObject(undefined);
      setTitle('无选中对象');
    }
  };

  const getCurrentItem = (): IObjectItem | undefined => {
    if (selectedItems.length > 0) {
      const selectionCollection =
        hostApp().getMainMapView()?.selectionCollection;
      return selectionCollection?.firstSelectedObject;
    }
    return undefined;
  };

  useEffect(() => {
    handleRelativeHighlight();
    handleDmaRelatedDeviceHighlight();
    const currentItem = getCurrentItem();
    updateObjectProperties(currentItem);
  }, [selectedItems]);

  const handleSelect = (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => {
    const { otype, oname, shape } = locateObj;
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      mapViews.forEach((mapView) => {
        mapView.selectAndNavigate(otype, oname, shape);
      });
    }
  };

  const handleHover = (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => {
    dispatch(highlightActions.updateHoverObject({ hoverObject: locateObj }));
  };

  const handleNavigate = (selectedObject: IObjectItem) => {
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      mapViews.forEach((mapView) => {
        mapView.navigateAndHighlight(selectedObject);
      });
    }
  };

  const handleShowValveEditor = (otype: string, oname: string) => {
    dispatch(
      valveEditorActions.showValveEditor({
        otype,
        oname,
      }),
    );
  };

  const getButtonByType = (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ): ReactNode => (
    <ButtonContainer
      propertyButtonEditor={propertyButtonEditor}
      propertyItemValue={propertyItemValue}
      pinnedItem={undefined}
      compareDevice={compareDevice}
    />
  );

  const handleGetPressureCorrelationDevice = async (
    otype: string,
    oname: string,
    date: string,
  ): Promise<{
    relativeIndicators: PressureRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }> => {
    const values: {
      relativeIndicators: PressureRelativeIndicatorValue[];
      relativeLineString: HighlightObject[];
    } = {
      relativeIndicators: [],
      relativeLineString: [],
    };
    const database = curDb();
    if (database) {
      const res = await getPressureCorrelationDevices(
        otype,
        oname,
        database,
        date,
      );

      if (res.status === 'Success' && res.relativeIndicators) {
        values.relativeIndicators = res.relativeIndicators;
        values.relativeLineString = res.relativeLineString ?? [];
      }
    }
    return values;
  };

  const displayCorrelationDeviceChart = (
    correlationDevice:
      | PressureRelativeIndicatorValue
      | FlowRelativeIndicatorValue,
  ): ReactNode => {
    const compareDevice = curDb().getDeviceById(correlationDevice.id);
    if (!compareDevice) {
      return null;
    }
    return getButtonByType(
      {
        actionName: 'propertyChartAction',
        type: 'propertyChartAction',
        iconText: '\ue629',
        title: '查看对比曲线',
      },
      {
        selectedObject: compareDevice,
        indicatorType: correlationDevice.indicatorType,
        indicatorName: correlationDevice.indicatorName,
        vprop: 'SDVAL',
        key: compareDevice?.id ?? '',
        label: compareDevice?.title ?? '',
        value: undefined,
        buttons: undefined,
      },
      compareDevice,
    );
  };

  const handleGetFlowCorrelationDevice = async (
    otype: string,
    oname: string,
    date: string,
  ): Promise<{
    relativeIndicators: FlowRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }> => {
    const database = curDb();
    const values: {
      relativeIndicators: FlowRelativeIndicatorValue[];
      relativeLineString: HighlightObject[];
    } = {
      relativeIndicators: [],
      relativeLineString: [],
    };
    if (database) {
      const res = await getFlowCorrelationDevices(otype, oname, database, date);
      if (res.status === 'Success' && res.relativeIndicators) {
        values.relativeIndicators = res.relativeIndicators;
        values.relativeLineString = res.relativeLineString ?? [];
      }
    }
    return values;
  };

  const displayWarnDeviceChart = (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    startDate?: string,
    endDate?: string,
  ) => {
    const propertyChartAction: PropertyChartActionArgs = {
      indicatorType,
      indicatorName,
      vprop,
      chartStartDate: startDate,
      chartEndDate: endDate,
    };
    dispatch(
      baseActions.propertyChartAction({
        arg: propertyChartAction,
      }),
    );
  };

  const getWarnData = async (
    deviceType: string,
    deviceName: string,
  ): Promise<{
    realtimeWarnList: WarnInfoList;
    assessmentWarnList: WarnInfoList;
  }> => {
    const responseData = await getWarnListByDate(
      {
        startTime: dayjs().add(-1, 'year').format('YYYY-MM-DD'),
        endTime: dayjs().format('YYYY-MM-DD'),
        deviceName,
        deviceType,
        type: warnTypeData.map((item) => item.type as WarnPrimaryType),
      },
      curDb(),
    );

    const realtimeWarnList: WarnInfoList = responseData.list.filter((item) =>
      nonAssessmentWarnTypes.includes(item.primaryType),
    );
    const assessmentWarnList: WarnInfoList = responseData.list.filter(
      (item) => item.primaryType === assessmentWarnType,
    );
    const values: {
      realtimeWarnList: WarnInfoList;
      assessmentWarnList: WarnInfoList;
    } = {
      realtimeWarnList,
      assessmentWarnList,
    };
    return values;
  };

  const getPictureList = async (deviceId: string): Promise<string[]> => {
    const res = await getDevicePictures(deviceId);
    let values: string[] = [];
    if (res.status === 'Success' && res.pictureList) {
      values = res.pictureList;
    }
    return values;
  };

  const getDeviceList = async (
    otype: string,
    oname: string,
    time: string,
    dmaType: DmaType | undefined,
  ): Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }> => {
    const res =
      dmaType === DMA_THIRD
        ? await getThirdDmaRelatedDevices(otype, oname, time, curDb())
        : await getDmaRelatedDevices(otype, oname, time, curDb());
    if (res.status === 'Success') {
      return {
        inDeviceList: res.inDeviceList ?? [],
        outDeviceList: res.outDeviceList ?? [],
      };
    }
    return {
      inDeviceList: [],
      outDeviceList: [],
    };
  };

  const getGroupObjectsTimeValues = async (valueGroupParams: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  }) => {
    const time = getDateTimeFromValue(timelineTime, dayjs(timelineDate));
    const values: Map<string, GroupTimeData>[] = [];
    if (Object.keys(valueGroupParams).length > 0) {
      const res = await getAllGroupObjectsTimeValues(
        dayjs(time).format('YYYY-MM-DD 00:00:00'),
        dayjs(time).format('YYYY-MM-DD 23:59:59'),
        hostApp().getMapViews(),
        valueGroupParams,
      );
      res.forEach((result) => {
        if (result.status === 'Success' && result.values) {
          values.push(result.values);
        }
      });
    }
    return values[0];
  };

  const handleUpstream = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: TRACK_UP,
      }),
    );
    dispatch(
      leftWrapperActions.leftWrapperChanged({
        open: true,
      }),
    );
  };

  const handleDownstream = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: TRACK_DOWN,
      }),
    );
    dispatch(
      leftWrapperActions.leftWrapperChanged({
        open: true,
      }),
    );
  };

  const handleBurstPipe = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: MENU_TOOLS_BURST_PIPE,
      }),
    );
    dispatch(
      leftWrapperActions.leftWrapperChanged({
        open: true,
      }),
    );
  };

  const handleValveAnalyse = () => {
    dispatch(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: MENU_TOOLS_VALVE,
      }),
    );
    dispatch(
      leftWrapperActions.leftWrapperChanged({
        open: true,
      }),
    );
  };

  return typeof selectedObject === 'undefined' ? (
    <NavBar
      style={{
        backgroundColor: token.colorBgContainer,
        '--height': '36px',
        '--border-bottom': `1px solid ${token.colorBorder}`,
      }}
      onBack={() => {
        hostApp().getMainMapView()?.clearSelected();
      }}
    >
      {title}
    </NavBar>
  ) : (
    <PropertyWrapper>
      <NavBar
        style={{
          backgroundColor: token.colorBgContainer,
          '--height': '36px',
          '--border-bottom': `1px solid ${token.colorBorder}`,
        }}
        onBack={() => {
          hostApp().getMainMapView()?.clearSelected();
        }}
        right={<BarChartOutlined onClick={displayChart} />}
      >
        {title}
      </NavBar>
      <PropertyPalette
        selectedObject={selectedObject}
        activePanels={undefined}
        propValues={propValues}
        database={curDb()}
        appConfig={hostApp().appConfig}
        getButtonByType={getButtonByType}
        getPressureCorrelationDevices={handleGetPressureCorrelationDevice}
        getFlowCorrelationDevices={handleGetFlowCorrelationDevice}
        getWarnData={getWarnData}
        displayWarnDeviceChart={displayWarnDeviceChart}
        displayCorrelationDeviceChart={displayCorrelationDeviceChart}
        handleRelativeHighlight={handleRelativeHighlight}
        handleDmaRelatedDeviceHighlight={handleDmaRelatedDeviceHighlight}
        handleSelect={handleSelect}
        handleHover={handleHover}
        handleNavigate={handleNavigate}
        handleChangeActivePanels={() => {}}
        handleShowValveEditor={handleShowValveEditor}
        getPictureList={getPictureList}
        getDeviceList={getDeviceList}
        getGroupObjectsTimeValues={getGroupObjectsTimeValues}
        date={deviceUpdateDate}
        time={getDateTimeFromValue(timelineTime, dayjs(timelineDate)).format(
          'YYYY-MM-DD HH:mm:ss',
        )}
        handleUpstream={handleUpstream}
        handleDownstream={handleDownstream}
        handleBurstPipe={handleBurstPipe}
        handleValveAnalyse={handleValveAnalyse}
      />
    </PropertyWrapper>
  );
}
