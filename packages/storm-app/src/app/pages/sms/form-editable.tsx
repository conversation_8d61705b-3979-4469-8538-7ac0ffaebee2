/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { phoneNumberRegex } from '@waterdesk/data/regex';
import { UserSource } from '@waterdesk/data/sms';
import { UserInfo } from '@waterdesk/data/system-user';
import { getUserList } from '@waterdesk/request/user';
import {
  Form,
  FormInstance,
  Input,
  InputRef,
  message,
  Select,
  SelectProps,
} from 'antd';
import {
  createContext,
  FC,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { v4 as uuidv4 } from 'uuid';

const EditableContext = createContext<FormInstance | null>(null);

export { UserSource };
export interface Item {
  key: string | number;
  id?: string;
  type: UserSource;
  name: string;
  department: string;
  mobile: string;
}

interface EditableRowProps {
  index: number;
}

const EditableRow: FC<EditableRowProps> = ({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  index,
  ...props
}: EditableRowProps) => {
  const [form] = Form.useForm();
  return (
    <Form
      form={form}
      component={false}
    >
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  handleSave: (record: Item) => void;
}

const EditableCell: FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  ...restProps
}: EditableCellProps) => {
  const [editing, setEditing] = useState(false);
  const [open, setOpen] = useState(false);

  const inputRef = useRef<InputRef>(null);
  const selectRef = useRef(null);

  const form = useContext(EditableContext)!;

  const [options, setOptions] = useState<
    SelectProps['options'] & { department?: string; phone?: string }
  >([]);

  const [messageApi, contextHolder] = message.useMessage();

  const isSystemEditable =
    record?.type === UserSource.SYSTEM && dataIndex === 'name';
  const isNonSystemEditable =
    record?.type === UserSource.NON_SYSTEM && dataIndex === 'mobile';

  const fetchUserList = async () => {
    const res = await getUserList();
    if (res.status === 'Success') {
      const userList = res?.list || [];
      const option = userList
        .filter((item: UserInfo) => item.phone)
        .map((item: UserInfo) => ({
          label: item.name,
          value: item.id,
          phone: item.phone,
          department: item.departmentName,
        }));
      setOptions(option || []);
    }
  };

  useEffect(() => {
    if (editing) {
      fetchUserList();
      setOpen(true);
      if (isNonSystemEditable) inputRef.current!.focus();
    } else {
      setOpen(false);
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };

  const save = async () => {
    try {
      const values = await form.validateFields();

      if (isSystemEditable) {
        const findValues = options?.find(
          (option) => option.value === values.name,
        );

        if (!findValues?.phone) {
          messageApi.error('该用户没有手机号码, 请重新选择!');
          return;
        }

        handleSave({
          ...record,
          ...values,
          name: findValues?.label?.toString() ?? '',
          department: findValues?.department,
          mobile: findValues?.phone,
          id: findValues?.value?.toString() ?? '',
        });
      }

      if (isNonSystemEditable) {
        handleSave({
          ...record,
          ...values,
          name: '-',
          department: '-',
          mobile: values.mobile,
          id: uuidv4(),
        });
      }

      toggleEdit();
    } catch (errInfo) {
      console.error('Save failed:', errInfo);
    }
  };

  let childNode = children;

  const canEdit = (isSystemEditable || isNonSystemEditable) && editable;

  const filterOption: SelectProps['filterOption'] = (input, option) => {
    if (typeof option?.label === 'string') {
      return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
    }
    return false;
  };

  if (canEdit) {
    childNode = editing ? (
      <>
        <Form.Item
          style={{ margin: 0 }}
          name={dataIndex}
          rules={[
            {
              required: true,
              message: `${title} 不可以为空`,
            },
            {
              validateTrigger: 'onBlur',
              validator(_, value, callback) {
                if (isNonSystemEditable && !phoneNumberRegex.test(value)) {
                  callback('手机号不符合规则');
                }
                callback();
              },
            },
          ]}
        >
          {dataIndex === 'name' ? (
            <Select
              showSearch
              optionFilterProp="children"
              placeholder="请选择"
              ref={selectRef}
              options={options}
              filterOption={filterOption}
              onChange={save}
              onBlur={save}
              open={open}
            />
          ) : (
            <Input
              placeholder="请输入"
              ref={inputRef}
              onBlur={save}
            />
          )}
        </Form.Item>
        {contextHolder}
      </>
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{ paddingRight: 24 }}
        onClick={toggleEdit}
        onKeyDown={toggleEdit}
        role="button"
        tabIndex={0}
      >
        {Array.isArray(children) && children[1] === '' ? '请输入' : children}
      </div>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

const formEditableComponents = {
  body: {
    row: EditableRow,
    cell: EditableCell,
  },
};

export default formEditableComponents;
