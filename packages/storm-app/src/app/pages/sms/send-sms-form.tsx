/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeleteOutlined } from '@ant-design/icons';
import { SmsTemplateUser } from '@waterdesk/data/sms-template';
import { Button, Form, FormInstance, Popconfirm, Table } from 'antd';
import { useEffect, useState } from 'react';
import { VerticalFormTitleWrapper } from 'src/styles/common-style';
import formEditableComponents, { Item, UserSource } from './form-editable';
import { EditableWrapper } from './style';
import useSelectUserDrawer from './use-select-user-drawer';

type EditableTableProps = Parameters<typeof Table<Item>>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

interface Props {
  form: FormInstance;
  required?: boolean;
  style?: React.CSSProperties;
}

function SendSMSForm({ form, required = true, style }: Readonly<Props>) {
  const [dataSource, setDataSource] = useState<Item[]>([]);
  const [count, setCount] = useState<number>(0);

  const receivers = Form.useWatch('receivers', form);

  const { selectUserDrawerContextHolder, onOpenSelectUserDrawer } =
    useSelectUserDrawer({
      userKeys: dataSource,
      onSubmit: (selected) => {
        const selectedSystemItems: Item[] = selected.map((i) => ({
          ...i,
          key: i.id.toString(),
          type: UserSource.SYSTEM,
        }));
        const nonSystemItems = dataSource.filter(
          (i) => i.type === UserSource.NON_SYSTEM,
        );
        setDataSource([...selectedSystemItems, ...nonSystemItems]);
        form.setFieldValue('receivers', [
          ...selectedSystemItems,
          ...nonSystemItems,
        ]);
      },
    });

  const handleAddSystemUser = () => {
    onOpenSelectUserDrawer();
  };

  const handleAddNonSystemUser = () => {
    setDataSource([
      ...dataSource,
      {
        key: count + 1,
        type: UserSource.NON_SYSTEM,
        name: '-',
        department: '-',
        mobile: '',
      },
    ]);
    setCount(count + 1);
  };

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter((item) => item.key !== key);
    setDataSource(newData);
    form.setFieldValue('receivers', newData);
  };

  const handleSave = (row: Item) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
    form.setFieldValue('receivers', newData);
  };

  useEffect(() => {
    if (receivers) {
      const value = (receivers ?? [])?.map((i: SmsTemplateUser) => ({
        ...i,
        key: i.id?.toString(),
        name: i.name ?? '-',
        department: i.department ?? '-',
      }));

      setDataSource(value);
    } else {
      setDataSource([]);
    }
  }, [receivers]);

  const defaultColumns: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
  })[] = [
    {
      title: '编号',
      dataIndex: 'index',
      width: '8%',
      render: (_text: string, _record, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: '25%',
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: '25%',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      width: '25%',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '8%',
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Popconfirm
            key={record.key}
            title="确认删除吗?"
            onConfirm={() => handleDelete(record.key)}
          >
            <Button
              type="link"
              danger
              title="删除"
              style={{ padding: '5px', height: '22px' }}
            >
              <DeleteOutlined />
            </Button>
          </Popconfirm>
        ) : null,
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (
        record: ColumnTypes[number] & {
          editable?: boolean;
          dataIndex: string;
        },
      ) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave,
      }),
    };
  });

  return (
    <>
      <Form.Item
        label={
          <VerticalFormTitleWrapper>
            <span>发送对象：</span>
            <div>
              <Button
                onClick={handleAddSystemUser}
                style={{ marginRight: 10 }}
              >
                部门人员选择
              </Button>
              <Button onClick={handleAddNonSystemUser}>自定义用户</Button>
            </div>
          </VerticalFormTitleWrapper>
        }
        name="receivers"
        rules={[{ required, message: '请选择发送对象' }]}
        labelCol={{ span: 24 }}
        tooltip="发送系统用户短信，只展示设置了手机号的用户"
        style={style}
      >
        <EditableWrapper
          components={formEditableComponents}
          rowClassName="editable-row"
          dataSource={dataSource}
          columns={columns as ColumnTypes}
          bordered
          size="small"
          pagination={false}
        />
      </Form.Item>
      {selectUserDrawerContextHolder}
    </>
  );
}

export default SendSMSForm;
