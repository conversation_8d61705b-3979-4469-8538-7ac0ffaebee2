/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { importanceOptions } from '@waterdesk/data/sms';
import {
  Button,
  Col,
  Divider,
  Form,
  FormInstance,
  Input,
  Row,
  Select,
} from 'antd';
import { JSX } from 'react';
import { VerticalFormTitleWrapper } from 'src/styles/common-style';
import SendSMSForm from './send-sms-form';

interface SMSEditProps {
  form: FormInstance;
  content?: JSX.Element;
}

export default function SMSEdit(props: SMSEditProps) {
  const { form, content } = props;

  return (
    <Form
      name="editForm"
      form={form}
    >
      {content}
      {content && <Divider orientation="left">短信信息</Divider>}
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="重要类型"
            name="importance"
            rules={[{ required: true, message: '请选择重要类型' }]}
          >
            <Select
              placeholder="请选择"
              options={importanceOptions}
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item
        label={
          <VerticalFormTitleWrapper>
            <span>信息内容：</span>
            <Button disabled>短信模板选择</Button>
          </VerticalFormTitleWrapper>
        }
        name="msgText"
        rules={[{ required: true, message: '请输入短信模板' }]}
        labelCol={{ span: 24 }}
      >
        <Input.TextArea
          placeholder="请输入"
          rows={6}
        />
      </Form.Item>
      <SendSMSForm form={form} />
    </Form>
  );
}
