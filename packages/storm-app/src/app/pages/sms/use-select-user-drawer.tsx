/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DepartmentList } from '@waterdesk/data/department';
import {
  createDepartmentUserTree,
  DepartmentUserNode,
} from '@waterdesk/data/department-user';
import { UserList } from '@waterdesk/data/system-user';
import { getDepartmentList } from '@waterdesk/request/department';
import { getUserList } from '@waterdesk/request/user';
import { useDebounceFn } from 'ahooks';
import { Button, Drawer, Space, Tabs, TabsProps, TreeProps } from 'antd';
import { useEffect, useState } from 'react';
import { searchInTree } from 'src/app/core/share-solution/use-share-solution';
import BaseTree from 'src/components/tree/base-tree';

import { Item, UserSource } from './form-editable';

interface UserItem {
  id: string;
  name: string;
  department: string;
  mobile: string;
}

interface UseSelectUserDrawerProps {
  userKeys?: Item[];
  onSubmit?: (selectedUsers: UserItem[]) => void;
}

function useSelectUserDrawer({ userKeys, onSubmit }: UseSelectUserDrawerProps) {
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<DepartmentUserNode[]>([]);
  const [departmentUserTree, setDepartmentUserTree] = useState<
    DepartmentUserNode[]
  >([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedUserItems, setSelectedUserItems] = useState<UserItem[]>([]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSubmit = () => {
    handleClose();
    onSubmit?.(selectedUserItems);
  };

  const handleSelectUser: TreeProps['onCheck'] = (_, info) => {
    const userItems: UserItem[] = info.checkedNodes
      .filter((node) => (node as any).type === 'user')
      .map((node: any) => ({
        id: node.key,
        name: node.title,
        department: node.department,
        mobile: node.mobile,
      }));

    setSelectedUserItems(userItems);
    setSelectedKeys(userItems.map((i) => i.id));
  };

  const convertFetchDataToTreeData = (
    departmentList: DepartmentList,
    userList: UserList,
  ) => {
    const departmentUserTree = createDepartmentUserTree(
      departmentList,
      userList,
      true,
      true,
    );
    setTreeData(departmentUserTree);
    setDepartmentUserTree(departmentUserTree);
  };

  const { run: handleSearchUserTree } = useDebounceFn(
    (searchTerm: string) => {
      const result = searchInTree(treeData, searchTerm);
      if (searchTerm) {
        setDepartmentUserTree(result);
      } else {
        setDepartmentUserTree(treeData);
      }
    },
    { wait: 500 },
  );

  const items: TabsProps['items'] = [
    {
      key: 'users',
      label: '系统用户',
      children: (
        <BaseTree
          defaultExpandAll
          checkable
          showSearch
          loading={loading}
          treeData={departmentUserTree}
          onCheck={handleSelectUser}
          checkedKeys={selectedKeys}
          searchConfig={{
            onSearch: handleSearchUserTree,
          }}
        />
      ),
    },
  ];

  const initialData = async () => {
    try {
      setLoading(true);

      const [userRes, departmentRes] = await Promise.all([
        getUserList(),
        getDepartmentList(),
      ]);

      if (departmentRes.status === 'Success' && userRes.status === 'Success') {
        const departmentList = departmentRes.departmentList ?? [];
        const userList = userRes.list ?? [];
        convertFetchDataToTreeData(departmentList, userList);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      initialData();
    } else {
      setLoading(false);
      setDepartmentUserTree([]);
      setSelectedUserItems([]);
    }
    setSelectedKeys(
      userKeys
        ?.filter((i) => i.type === UserSource.SYSTEM)
        .map((i) => i.key.toString()) ?? [],
    );
  }, [open]);

  const contextHolder = (
    <Drawer
      title="选择系统用户"
      open={open}
      onClose={handleClose}
      extra={
        <Space>
          <Button onClick={handleClose}>取消</Button>
          <Button
            type="primary"
            onClick={handleSubmit}
          >
            确定
          </Button>
        </Space>
      }
      destroyOnHidden
    >
      <Tabs
        defaultActiveKey="users"
        items={items}
      />
    </Drawer>
  );

  return {
    selectUserDrawerContextHolder: contextHolder,
    onOpenSelectUserDrawer: handleOpen,
  };
}

export default useSelectUserDrawer;
