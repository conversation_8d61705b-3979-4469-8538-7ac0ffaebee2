/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getCompareSourceName,
  SolutionComparisonItem,
} from '@waterdesk/data/solution';
import { PageParams } from '@waterdesk/request/api/api-request';
import { InfiniteScroll, List, PullToRefresh, Tag } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { StyledListItem } from './style';

interface ComparisionListProps {
  fetchSolutionComparisonList: (
    params: PageParams,
  ) => Promise<SolutionComparisonItem[]>;
  // initialCommandId?: string;
}

export default function ComparisonList({
  fetchSolutionComparisonList,
}: ComparisionListProps) {
  const navigator = useNavigate();
  const { dataMode } = useParams();
  const [data, setData] = useState<SolutionComparisonItem[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 20;

  const handleComparisonWatch = (
    solutionGuid: string,
    comparisonId: string,
    solutionTitle: string,
  ) => {
    navigator(
      `/app/solution/${dataMode}/${solutionGuid}?comparisonId=${comparisonId}`,
      {
        state: { solutionTitle },
      },
    );
  };

  const sortByState = (data: SolutionComparisonItem[]) =>
    [...data].sort(
      (a, b) =>
        // 如果状态相同，按时间倒序排列
        dayjs(b.comparisonTime).valueOf() - dayjs(a.comparisonTime).valueOf(),
    );

  const loadMore = async () => {
    const params: PageParams = { current: page, pageSize };

    const res = await fetchSolutionComparisonList(params);
    const newData = res || [];
    const allData = [...data, ...newData];
    setData(sortByState(allData));
    setHasMore(newData.length === pageSize);
    setPage(page + 1);
  };

  const refresh = async () => {
    const list = await fetchSolutionComparisonList({
      current: 1,
      pageSize: 20,
    });
    setData(sortByState(list));
  };

  useEffect(() => {
    if (data.length === 0) {
      refresh();
    }
  }, []);

  return (
    <PullToRefresh
      onRefresh={async () => {
        await refresh();
      }}
    >
      <List>
        {data.map((item) => (
          <StyledListItem
            isPending
            key={item.comparisonId}
            onClick={() =>
              handleComparisonWatch(
                item.baseSolutionGuid,
                item.comparisonId,
                item.comparisonTitle,
              )
            }
            extra={getCompareSourceName(item.comparisonType)}
            description={
              <>
                {item.testSolutionName ? (
                  <>
                    <Tag color="default">{item.testSolutionName}</Tag>-
                  </>
                ) : null}
                {item.baseSolutionName ? (
                  <Tag color="default">{item.baseSolutionName}</Tag>
                ) : null}
              </>
            }
          >
            {item.comparisonTitle}
          </StyledListItem>
        ))}
        <InfiniteScroll
          loadMore={loadMore}
          hasMore={hasMore}
        />
      </List>
    </PullToRefresh>
  );
}
