/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LeftOutlined } from '@ant-design/icons';
import { SidebarMenuType } from '@waterdesk/data/sidebar-menu-data';
import { SolutionStatus } from '@waterdesk/data/solution';
import {
  SolutionBaseInfo,
  SolutionDetail,
} from '@waterdesk/data/solution-detail';
import { getValueFromDateTime } from '@waterdesk/data/time-data';
import { getSchedulingData } from '@waterdesk/request/get-scheduling-data';
import {
  getSolutionInfo,
  updateSolutionStatusMsg,
} from '@waterdesk/request/get-solution-data';
import { useLocalStorageState } from 'ahooks';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useSearchParams } from 'react-router';
import SolutionMapContainer from 'src/app/core/map-container/solutionContainer';
import { useBaseSlice } from 'src/store/base';
import { selectViewId } from 'src/store/base/selectors';
import {
  editSelectionActions,
  useEditSelectionSlice,
} from 'src/store/edit-selection';
import { selectModifiedTime } from 'src/store/edit-selection/selector';
import {
  leftWrapperActions,
  useLeftWrapperSlice,
} from 'src/store/left-wrapper';
import { themeActions, useThemeSlice } from 'src/store/theme';
import { timelineActions, useTimelineSlice } from 'src/store/time-line';
import { useUserConfigSlice } from 'src/store/user-config';
import { GlobalConfig } from 'src/store/user-config/types';
import SearchContainer from '../tool';
import FloatingPanelContainer from './floating-panel';
import { SolutionTitleWrap } from './style';
import { useSolutionIds } from './use-solution-ids';

export default function Solution() {
  useBaseSlice();
  useThemeSlice();
  useTimelineSlice();
  useUserConfigSlice();
  useLeftWrapperSlice();
  useEditSelectionSlice();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { solutionId } = useSolutionIds();
  const location = useLocation();

  const solutionTitle = location.state?.solutionTitle;
  const viewId = useSelector(selectViewId);
  const modifiedTime = useSelector(selectModifiedTime);
  const [solutionDetail, setSolutionDetail] = useState<
    SolutionDetail | undefined
  >(undefined);
  const [solutionBaseInfo, setSolutionBaseInfo] = useState<
    SolutionBaseInfo | undefined
  >(undefined);
  const [hasAutoOpenedAnalysis, setHasAutoOpenedAnalysis] = useState(false);
  const [openSearch, setOpenSearch] = useState<boolean>(false);
  const [solutionName, setSolutionName] = useState<string>('-');

  const appContent = useRef<HTMLDivElement>(null);

  const [searchParams] = useSearchParams();
  const comparisonId = searchParams.get('comparisonId');
  const showPollutionSpreadAnalysis = searchParams.get(
    'showPollutionSpreadAnalysis',
  );

  const [themeToken] =
    useLocalStorageState<GlobalConfig['systemThemeConfig']>('themeToken');

  const fetchSchedulingData = async (solutionTime: string) => {
    const res = await getSchedulingData(
      dayjs(solutionTime).format('YYYY-MM-DD HH:mm:00'),
    );

    if (res.status === 'Success') {
      console.log(res.schedulingData);
    }
  };

  const fetchSolutionInfo = async (solutionId: string) => {
    const res = await getSolutionInfo(solutionId);
    if (res.status === 'Success') {
      setSolutionDetail(res.detail);
      setSolutionBaseInfo(res.baseInfo);
      dispatch(
        timelineActions.updateTimelineTime({
          timelineTime: getValueFromDateTime(dayjs(res.baseInfo?.startTime)),
        }),
      );
      if (res.baseInfo?.startTime) fetchSchedulingData(res.baseInfo?.startTime);
    }
  };

  const updateSolutionStatus = () => {
    if (solutionId) {
      updateSolutionStatusMsg(
        solutionId,
        '当前计算结果失效，请重新计算',
        SolutionStatus.INVALID,
      ).then((res) => {
        dispatch(editSelectionActions.cleanModifiedTime());
        if (res.status === 'Success') fetchSolutionInfo(solutionId);
      });
    }
  };

  useEffect(() => {
    if (solutionTitle) {
      setSolutionName(solutionTitle);
    }
  }, [solutionTitle]);

  useEffect(() => {
    if (solutionId) {
      fetchSolutionInfo(solutionId);
    }
  }, [solutionId]);

  useEffect(() => {
    if (typeof viewId !== 'undefined' && solutionId && modifiedTime) {
      updateSolutionStatus();
    }
  }, [modifiedTime]);

  useEffect(() => {
    if (
      solutionBaseInfo &&
      solutionDetail &&
      (comparisonId || showPollutionSpreadAnalysis) &&
      !hasAutoOpenedAnalysis
    ) {
      dispatch(
        leftWrapperActions.leftWrapperContainerTypeChanged({
          containerType: SidebarMenuType.SOLUTION_ANALYSIS,
        }),
      );
      setHasAutoOpenedAnalysis(true);
    }
  }, [
    comparisonId,
    showPollutionSpreadAnalysis,
    solutionBaseInfo,
    solutionDetail,
    hasAutoOpenedAnalysis,
  ]);

  useEffect(() => {
    if (
      hasAutoOpenedAnalysis &&
      solutionBaseInfo &&
      solutionDetail &&
      (comparisonId || showPollutionSpreadAnalysis)
    ) {
      dispatch(
        leftWrapperActions.leftWrapperContainerTypeChanged({
          containerType: SidebarMenuType.SOLUTION_ANALYSIS,
        }),
      );
    }
  }, [comparisonId]);

  useEffect(() => {
    if (themeToken) {
      dispatch(
        themeActions.updateThemeToken({
          seedToken: themeToken.solution?.token,
          componentsConfig: themeToken.solution?.components,
          darkModeSeedToken: themeToken.solution?.darkToken,
          darkModeComponentsConfig: themeToken.solution?.darkComponents,
        }),
      );
    }
    return () => {
      dispatch(
        themeActions.updateThemeToken({
          seedToken: themeToken?.main?.token,
          componentsConfig: themeToken?.main?.components,
          darkModeSeedToken: themeToken?.main?.darkToken,
          darkModeComponentsConfig: themeToken?.main?.darkComponents,
        }),
      );
    };
  }, [themeToken]);

  const back = () => {
    navigate('/app/solutionList');
  };

  return (
    <div
      ref={appContent}
      style={{ width: '100%', height: '100%' }}
    >
      <SolutionTitleWrap>
        <LeftOutlined
          onClick={back}
          style={{ fontSize: '20px' }}
        />
        <span className="title">
          {solutionName ?? solutionBaseInfo?.name ?? '-'}
        </span>
      </SolutionTitleWrap>
      {solutionBaseInfo ? (
        <SolutionMapContainer
          mapName={solutionBaseInfo.name}
          solutionId={solutionId}
          solutionBaseInfo={solutionBaseInfo}
        />
      ) : null}
      <SearchContainer
        visible={openSearch}
        onClose={setOpenSearch}
        getContainer={appContent.current}
      />
      <FloatingPanelContainer
        openSearch={setOpenSearch}
        solutionBaseInfo={solutionBaseInfo}
        solutionDetail={solutionDetail}
        fetchSolutionInfo={fetchSolutionInfo}
        comparisonId={comparisonId}
        showPollutionSpreadAnalysis={showPollutionSpreadAnalysis}
      />
    </div>
  );
}
