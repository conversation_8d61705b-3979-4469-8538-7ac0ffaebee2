/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PollutionSpreadAnalysisItem } from '@waterdesk/data/solution';
import { PageParams } from '@waterdesk/request/api/api-request';
import { InfiniteScroll, List, PullToRefresh, Tag } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { StyledListItem } from './style';

interface PollutionSpreadAnalysisListProps {
  fetchPollutionSpreadAnalysisList: (
    params: PageParams,
  ) => Promise<PollutionSpreadAnalysisItem[]>;
}

export default function PollutionSpreadAnalysisList({
  fetchPollutionSpreadAnalysisList,
}: PollutionSpreadAnalysisListProps) {
  const navigator = useNavigate();
  const { dataMode } = useParams();
  const [data, setData] = useState<PollutionSpreadAnalysisItem[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 20;

  const handlePollutionWatch = (
    solutionGuid: string,
    solutionTitle: string,
  ) => {
    navigator(
      `/app/solution/${dataMode}/${solutionGuid}?showPollutionSpreadAnalysis=true`,
      {
        state: { solutionTitle },
      },
    );
  };

  const sortByState = (data: PollutionSpreadAnalysisItem[]) =>
    [...data].sort(
      (a, b) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf(),
    );

  const loadMore = async () => {
    const params: PageParams = { current: page, pageSize };

    const res = await fetchPollutionSpreadAnalysisList(params);
    const newData = res || [];
    const allData = [...data, ...newData];
    setData(sortByState(allData));
    setHasMore(newData.length === pageSize);
    setPage(page + 1);
  };

  const refresh = async () => {
    const list = await fetchPollutionSpreadAnalysisList({
      current: 1,
      pageSize: 20,
    });
    setData(sortByState(list));
  };

  useEffect(() => {
    if (data.length === 0) {
      refresh();
    }
  }, []);

  return (
    <PullToRefresh
      onRefresh={async () => {
        await refresh();
      }}
    >
      <List>
        {data.map((item) => (
          <StyledListItem
            isPending
            key={item.solutionId}
            description={<Tag color="default">{item.solutionName}</Tag>}
            onClick={() =>
              handlePollutionWatch(item.solutionGuid, item.analysisTitle)
            }
          >
            {item.analysisTitle}
          </StyledListItem>
        ))}
        <InfiniteScroll
          loadMore={loadMore}
          hasMore={hasMore}
        />
      </List>
    </PullToRefresh>
  );
}
