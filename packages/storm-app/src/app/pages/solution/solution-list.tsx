/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PageParams } from '@waterdesk/request/api/api-request';
import { getPollutionSpreadAnalysisList } from '@waterdesk/request/get-pollution-spread-analysis';
import { getSolutionComparisonList } from '@waterdesk/request/get-solution-comparsion';
import { Tabs } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useBaseSlice } from 'src/store/base';
import { useTimelineSlice } from 'src/store/time-line';
import ComparisonList from './comparison-list';
import PollutionSpreadAnalysisList from './pollution-analyse-list';
import { MessageCenter, MessageTabsWrapper, StyledContainer } from './style';

export default function SolutionList() {
  useBaseSlice();
  useTimelineSlice();

  const [activeKey, setActiveKey] = useState('comparison');
  const fetchComparisonList = async (params: PageParams) => {
    const res = await getSolutionComparisonList({
      current: params.current ?? 1,
      pageSize: params.pageSize ?? 10,
      sortField: params.sorter?.field ?? 'createTime',
      sortOrder: params.sorter?.order ?? 'descend',
    });
    return res.list ?? [];
  };

  const fetchPollutionSpreadAnalysisList = async (params: PageParams) => {
    const res = await getPollutionSpreadAnalysisList({
      current: params.current ?? 1,
      pageSize: params.pageSize ?? 10,
      sortField: params.sorter?.field ?? 'createTime',
      sortOrder: params.sorter?.order ?? 'descend',
    });
    return res.list ?? [];
  };

  useEffect(() => {
    const { hash } = window.location;
    const searchParams = hash.split('?')[1];
    const urlParams = new URLSearchParams(searchParams);

    const tab = urlParams.get('tab');

    if (tab) {
      setActiveKey(tab);
    }
  }, []);

  return (
    <MessageCenter>
      <MessageTabsWrapper>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
        >
          <Tabs.Tab
            title="方案对比"
            key="comparison"
          />
          <Tabs.Tab
            title="污染扩散"
            key="pollution"
          />
        </Tabs>
      </MessageTabsWrapper>
      <StyledContainer>
        {activeKey === 'comparison' && (
          <ComparisonList fetchSolutionComparisonList={fetchComparisonList} />
        )}
        {activeKey === 'pollution' && (
          <PollutionSpreadAnalysisList
            fetchPollutionSpreadAnalysisList={fetchPollutionSpreadAnalysisList}
          />
        )}
      </StyledContainer>
    </MessageCenter>
  );
}
