/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getSolutionIdByGuid } from '@waterdesk/request/get-solution-data';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router';

export function useSolutionIds() {
  const {
    solutionId: solutionGuid,
    compareIds: compareOriginalGuids,
    historyDate,
  } = useParams();
  const [solutionId, setSolutionId] = useState<string>();
  const [compareIds, setCompareIds] = useState<string[]>();
  const [compareGuids, setCompareGuids] = useState<string[]>();

  // 处理基准方案 ID
  useEffect(() => {
    if (solutionGuid) {
      getSolutionIdByGuid(solutionGuid).then((res) => {
        if (res.status === 'Success' && res.solutionId) {
          setSolutionId(res.solutionId);
        }
      });
    }
  }, [solutionGuid]);

  // 处理对比方案 IDs
  useEffect(() => {
    if (compareOriginalGuids) {
      const guids = compareOriginalGuids.split(',');
      setCompareGuids(guids);

      const promises = guids.map((guid) => getSolutionIdByGuid(guid));
      Promise.all(promises).then((results) => {
        const newIds = results
          .filter((res) => res.status === 'Success' && res.solutionId)
          .map((res) => res.solutionId!);
        setCompareIds(newIds);
      });
    }
  }, [compareOriginalGuids]);

  return {
    solutionId,
    compareIds,
    historyDate,
    solutionGuid,
    compareGuids,
  };
}
