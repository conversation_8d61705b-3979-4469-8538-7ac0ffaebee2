/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  SearchElementInfo,
  SearchRoadInfo,
} from '@waterdesk/data/search-element-info';
import {
  QueryElemLikeNameResponse,
  queryElemLikeName,
} from '@waterdesk/request/query-elem-like-name';
import { saveSearchHistory } from '@waterdesk/request/user-config/set-user-config-value';
import { Popup } from 'antd-mobile';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import useUpdateLayer from 'src/app/hooks/use-update-layer';
import useUpdateTheme from 'src/app/hooks/use-update-theme';
import { curDb, hostApp } from 'src/app/host-app';
import { APP_NAME } from 'src/config';
import { useTimelineSlice } from 'src/store/time-line';
import { selectTimelineDateTime } from 'src/store/time-line/selectors';
import SearchBox from '../../../components/search-bar/search';

interface Props {
  visible: boolean;
  onClose: (flag: boolean) => void;
  getContainer?: HTMLElement | null;
}

export default function SearchContainer(props: Props) {
  useUpdateTheme();
  useUpdateLayer();
  useTimelineSlice();

  const { visible, onClose, getContainer } = props;
  const timeLineDateTimeString = useSelector(selectTimelineDateTime);

  const [searchedElements, setSearchedElements] = useState<
    SearchElementInfo[] | SearchRoadInfo[]
  >([]);

  const [historyItems, setHistoryItems] = useState<string[]>([]);

  const selectSearchItem = (item: SearchElementInfo | SearchRoadInfo) => {
    const mapViews = hostApp().getMapViews();
    mapViews?.forEach((mapView) => {
      if (item.infoType === 'Object')
        mapView?.selectAndNavigate(item.otype, item.oname, item.shape);
      else if (item.shapes) {
        mapView?.selectRoad(item.title, item.shapes);
      }
    });
  };

  const saveHistoryItems = (searchKey: string) => {
    const index: number = historyItems.indexOf(searchKey);
    const newHistoryItems = Object.assign([], historyItems);
    if (index > -1) {
      newHistoryItems.splice(index, 1);
    }
    newHistoryItems.unshift(searchKey);

    if (newHistoryItems.length > 10) {
      newHistoryItems.splice(11, newHistoryItems.length - 10);
    }

    setHistoryItems(newHistoryItems);
    saveSearchHistory(newHistoryItems, APP_NAME);
  };

  const handleSearchObject = async (
    value: string,
    saveHistory: boolean,
  ): Promise<void> => {
    const searchKey = value.trim();
    if (searchKey === '') {
      setSearchedElements([]);
      return;
    }
    const res: QueryElemLikeNameResponse = await queryElemLikeName(
      timeLineDateTimeString,
      searchKey,
    );

    if (res.status === 'Success' && res.elements)
      setSearchedElements(res.elements);
    else setSearchedElements([]);

    if (saveHistory) saveHistoryItems(searchKey);
  };

  const handleClickSearchObject = (
    item: SearchElementInfo,
    searchKey?: string,
  ) => {
    if (searchKey !== undefined && searchKey !== '') {
      saveHistoryItems(searchKey);
    }

    selectSearchItem(item);
  };

  return (
    <Popup
      position="bottom"
      mask={false}
      visible={visible}
      style={{ '--z-index': '1' }}
      getContainer={getContainer}
      bodyStyle={{
        height: '100%',
      }}
    >
      <div
        style={{
          width: '100vw',
          paddingBottom: '8px',
          height: '100%',
        }}
      >
        <SearchBox
          visible={visible}
          searchedElements={searchedElements}
          database={curDb()}
          onBack={() => onClose(false)}
          handleSearchObject={handleSearchObject}
          handleClickSearchObject={handleClickSearchObject}
        />
      </div>
    </Popup>
  );
}
