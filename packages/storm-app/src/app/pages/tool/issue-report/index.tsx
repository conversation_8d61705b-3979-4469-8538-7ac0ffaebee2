/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import {
  IssueReportList,
  IssueReportProcessState,
} from '@waterdesk/data/issue-report';
import { Importance, MsgFrom, SendSMSParams } from '@waterdesk/data/sms';
import { updateIssueReport } from '@waterdesk/request/get-issue-report-data';
import { sendSMS } from '@waterdesk/request/get-sms-data';
import { Button, Form, Input, message, Select, Space } from 'antd';
import { NavBar, Popup } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import SendSMSForm from 'src/app/pages/sms/send-sms-form';
import { selectSelectionItem } from 'src/store/selection/selector';

interface Props {
  callback?: () => void;
  canEdit?: boolean;
}

const useIssueReportModal = ({ callback, canEdit }: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [data, setData] = useState<IssueReportList | undefined>(undefined);
  const selectedItems = useSelector(selectSelectionItem);
  const { token } = useToken();

  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  const { issueType } = hostApp().appConfig.issueReportConfig;

  const handleOpenModal = (data?: IssueReportList) => {
    setOpen(true);
    setData(data);
    form.setFieldsValue(data);
  };

  const handleCloseModal = () => {
    setOpen(false);
    form.resetFields();
  };

  const handleSendSMS = async () => {
    const values = await form.validateFields();

    const smsValues: SendSMSParams = {
      msgSendJson: values?.receivers?.map(
        (item: { mobile: string; id: string }) => ({
          msg_send_type: 'TELEPHONE',
          msg_send_code: item.mobile,
          msg_send_user: item.id,
        }),
      ),
      msgFrom: MsgFrom.ISSUE_REPORT,
      importance: Importance.LOW,
      msgText: values.content ?? values.processContent,
    };

    const res = await sendSMS(smsValues);

    if (res.status === 'Fail') {
      messageApi.error('发送失败');
    }
  };

  const handleOk = async () => {
    const values = await form.validateFields();

    let questionState = IssueReportProcessState.UNPROCESSED;

    if (data) {
      if (canEdit) {
        questionState = data.questionState;
      } else {
        questionState = IssueReportProcessState.PROCESSED;
      }
    }

    const res = await updateIssueReport({
      ...values,
      questionState,
      oname: data?.oname ?? undefined,
      questionType: values.questionType ?? data?.questionType,
    });

    if (res.status === 'Success') {
      if (values.receivers) await handleSendSMS();
      messageApi.success('上报成功');
      handleCloseModal();
      callback?.();
      form.resetFields();
    } else {
      messageApi.error('上报失败');
    }
  };

  useEffect(() => {
    if (open) {
      if (selectedItems.length > 0) {
        const selectionCollection =
          hostApp().getMainMapView()?.selectionCollection;
        if (selectionCollection?.firstSelectedObject) {
          const { shape } = selectionCollection.firstSelectedObject;
          if (shape) {
            form.setFieldValue('shape', shape ?? '');
            form.setFieldValue('address', shape ?? '');
          }
        }
      } else {
        messageApi.error('请先选择上报对象!');
        handleCloseModal();
      }
    }
  }, [open, selectedItems]);

  useEffect(() => {
    if (data) form.setFieldsValue(data);
  }, [data]);

  const issueReportModalContext = (
    <>
      <Popup
        position="right"
        visible={open}
      >
        <div
          style={{
            width: '100vw',
            paddingBottom: '8px',
          }}
        >
          <NavBar
            onBack={handleCloseModal}
            style={{
              backgroundColor: token.colorBgContainer,
              '--border-bottom': `1px solid ${token.colorBorder}`,
            }}
            right={
              <Space>
                <Button
                  type="primary"
                  onClick={handleOk}
                >
                  确定
                </Button>
              </Space>
            }
          >
            问题上报
          </NavBar>

          <Form
            form={form}
            labelCol={{ span: 3 }}
            style={{
              padding: '16px',
              height: 'calc(100vh - 45px)',
              overflow: 'auto',
            }}
          >
            <Form.Item
              label="问题名称"
              name={!data && 'title'}
              rules={[{ required: !data }]}
            >
              {data ? data?.title : <Input />}
            </Form.Item>
            <Form.Item
              label="问题类别"
              name={(!data || canEdit) && 'questionType'}
              rules={[{ required: !data }]}
            >
              {data && !canEdit ? (
                issueType?.find((i) => i.value === data?.questionType)?.label
              ) : (
                <Select options={issueType} />
              )}
            </Form.Item>
            {!data && (
              <Form.Item
                label="位置"
                name={!data && 'address'}
                rules={[{ required: !data }]}
              >
                <Input
                  placeholder="请输入"
                  disabled
                />
              </Form.Item>
            )}
            <Form.Item
              label="坐标"
              name={!data && 'shape'}
              hidden
              rules={[{ required: !data }]}
            >
              <Input placeholder="请选择" />
            </Form.Item>
            <Form.Item
              label="问题描述"
              name={(!data || canEdit) && 'content'}
              rules={[{ required: !data }]}
            >
              {data && !canEdit ? data.content : <Input.TextArea rows={6} />}
            </Form.Item>
            {(data?.questionState === IssueReportProcessState.PROCESSED ||
              (data?.questionState === IssueReportProcessState.UNPROCESSED &&
                !canEdit)) && (
              <Form.Item
                label="处理描述"
                name="processContent"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input.TextArea rows={8} />
              </Form.Item>
            )}
            {open && !data && (
              <SendSMSForm
                form={form}
                required={false}
              />
            )}
          </Form>
        </div>
      </Popup>
      {contextHolder}
    </>
  );

  return {
    issueReportModalContext,
    handleOpenModal,
  };
};

export default useIssueReportModal;
