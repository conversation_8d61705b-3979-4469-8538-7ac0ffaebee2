/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getCurrentLegendData,
  LegendGroupData,
} from '@waterdesk/data/legend-data';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Legend from 'src/components/legend/legend';
import { useBaseSlice } from 'src/store/base';
import { legendActions, useLegendSlice } from 'src/store/legend';
import { selectLegend } from 'src/store/legend/selector';

export default function LegendContainer() {
  useBaseSlice();
  useLegendSlice();

  const dispatch = useDispatch();
  const legendCode = useSelector(selectLegend);

  const [legendData, setLegendData] = useState<LegendGroupData[]>([]);

  const changeLegendVisible = (
    legendName: string,
    legendId: string | number,
    visible: boolean,
  ) => {
    dispatch(
      legendActions.triggerLegendChanged({
        legendData: {
          legendName,
          legendId,
          visible,
        },
      }),
    );
    setLegendData(getCurrentLegendData());
  };

  useEffect(() => {
    setLegendData(getCurrentLegendData());
  }, [legendCode]);

  return (
    <div style={{ padding: '0 20px 20px' }}>
      <Legend
        groupDataCollection={legendData}
        changeLegendVisible={changeLegendVisible}
      />
    </div>
  );
}
