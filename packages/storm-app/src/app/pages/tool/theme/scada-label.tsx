/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { Card, Selector, SelectorOption } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import RadioButton from 'src/components/radio-button';
import { useBaseSlice } from 'src/store/base';
import { userConfigActions } from 'src/store/user-config';
import {
  selectAppLabelIndicator,
  selectAppLabelMode,
  selectAppLabelSimulation,
  selectAppLabelTitle,
  selectGlobalConfig,
} from 'src/store/user-config/selector';
import { LabelMode } from 'src/store/user-config/types';
import { ScadaLabelWrapper } from './style';

export default function ScadaLabel() {
  useBaseSlice();

  const globalConfig = useSelector(selectGlobalConfig);
  const labelMode = useSelector(selectAppLabelMode);
  const labelTitle = useSelector(selectAppLabelTitle);
  const labelIndicator = useSelector(selectAppLabelIndicator);
  const labelSimulation = useSelector(selectAppLabelSimulation);
  const { token } = useToken();

  const dispatch = useDispatch();

  const [currentValue, setCurrentValue] = useState<string[]>([]);

  const displayLabelMode = (value: LabelMode) => {
    dispatch(
      userConfigActions.updateAppLabelMode({
        appLabelMode: value,
      }),
    );
  };

  const displayLabelTitle = (checked: boolean) => {
    hostApp()
      .getMainMapView()
      ?.overlayCollection?.updateLabelTitleStyle(checked);
    dispatch(
      userConfigActions.updateAppLabelTitle({
        appLabelTitle: checked,
      }),
    );
  };

  const displayLabelIndicator = (checked: boolean) => {
    hostApp()
      .getMainMapView()
      ?.overlayCollection?.updateIndicatorVisible(checked);
    dispatch(
      userConfigActions.updateAppLabelIndicator({
        appLabelIndicator: checked,
      }),
    );
  };

  const displayLabelSimulation = (checked: boolean) => {
    dispatch(
      userConfigActions.updateAppLabelSimulation({
        appLabelSimulation: checked,
      }),
    );
  };

  const switchLabelMode = (value: string) => {
    if (value === 'multiple') {
      displayLabelMode(undefined);
    } else {
      const indicator = globalConfig?.indicatorSelector?.[0].value;
      displayLabelMode(indicator);
    }
  };

  const items: SelectorOption<string>[] = [
    {
      label: `名称`,
      value: 'labelTitle',
    },
    {
      label: `指标`,
      value: 'labelIndicator',
    },
    {
      label: `计算`,
      value: 'labelSimulation',
    },
  ];

  const targetItems = (): SelectorOption<string>[] => {
    const targetItem = globalConfig?.indicatorSelector?.map((item) => ({
      label: item.label,
      value: item.value,
    }));
    return targetItem ?? [];
  };

  const setDisplayLabel = (value: string[]) => {
    if (!labelMode) {
      if (value.includes('labelTitle')) {
        displayLabelTitle(true);
      } else {
        displayLabelTitle(false);
      }
      if (value.includes('labelIndicator')) {
        displayLabelIndicator(true);
      } else {
        displayLabelIndicator(false);
      }
      if (value.includes('labelSimulation')) {
        displayLabelSimulation(true);
      } else {
        displayLabelSimulation(false);
      }
    } else {
      displayLabelMode(value[0]);
    }
  };

  useEffect(() => {
    if (labelMode) {
      setCurrentValue([labelMode]);
    } else {
      const values = [];
      if (labelTitle) {
        values.push('labelTitle');
      }
      if (labelIndicator) {
        values.push('labelIndicator');
      }
      if (labelSimulation) {
        values.push('labelSimulation');
      }
      setCurrentValue(values);
    }
  }, [labelMode, labelTitle, labelIndicator, labelSimulation]);

  return (
    <ScadaLabelWrapper>
      <Card
        title={
          <div>
            <div style={{ color: token.colorTextSecondary }}>监测标注</div>
            <RadioButton
              dataSource={[
                {
                  title: '单一展示',
                  id: 'single',
                  onClick: (currentId: string) => switchLabelMode(currentId),
                },
                {
                  title: '监测展示',
                  id: 'multiple',
                  onClick: (currentId: string) => switchLabelMode(currentId),
                },
              ]}
              value={labelMode ? 'single' : 'multiple'}
            />
          </div>
        }
        headerStyle={{
          fontWeight: 'normal',
          paddingBottom: 0,
          marginBottom: '10px',
          color: '#0000004d',
          fontSize: '12px',
          borderBottom: `1px solid ${token.colorBorder}`,
        }}
        bodyStyle={{ paddingTop: 0 }}
      >
        {labelMode ? (
          <Selector
            options={targetItems()}
            defaultValue={currentValue}
            value={currentValue}
            columns={3}
            onChange={(value) => setDisplayLabel(value)}
          />
        ) : null}
        {!labelMode ? (
          <Selector
            options={items}
            defaultValue={currentValue}
            multiple
            columns={3}
            value={currentValue}
            onChange={(value) => setDisplayLabel(value)}
          />
        ) : null}
      </Card>
    </ScadaLabelWrapper>
  );
}
