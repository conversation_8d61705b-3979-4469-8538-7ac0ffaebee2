/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SceneType } from '@waterdesk/data/scene';
import { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router';
import RadioButton from 'src/components/radio-button';
import { scenesActions, useScenesSlice } from 'src/store/scenes';
import { selectCurrentSceneId, selectScenes } from 'src/store/scenes/selectors';
import { SceneWrapper } from './style';

export default function SceneContainer() {
  useScenesSlice();

  const [searchParams] = useSearchParams();
  const comparisonId = searchParams.get('comparisonId');
  const dispatch = useDispatch();
  const scenes = useSelector(selectScenes);
  const currentScenes = useSelector(selectCurrentSceneId);

  const switchScene = (sceneId: SceneType) => {
    dispatch(
      scenesActions.updateCurrentSceneId({
        sceneId,
      }),
    );
  };

  const dataSource = useMemo(
    () => [
      ...scenes
        .filter((item) => !item.hidden)
        .map((scene) => ({
          id: scene.id,
          title: scene.title,
          onClick: () => switchScene(scene.id),
        })),
    ],
    [scenes, comparisonId],
  );
  return (
    <SceneWrapper wrap>
      <RadioButton
        dataSource={dataSource}
        value={currentScenes}
      />
    </SceneWrapper>
  );
}
