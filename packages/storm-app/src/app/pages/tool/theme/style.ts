/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Space } from 'antd-mobile';
import styled from 'styled-components';

export const SceneWrapper = styled(Space)`
  padding: 10px;
`;

export const ItemWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  span {
    flex: 1;
  }
`;

export const ScadaLabelWrapper = styled.div`
  width: 100%;
  padding: 10px 10px 0 10px;
  background-color: ${({ theme }) => theme.colorBgContainer};
  font-size: 12px;
  .adm-selector-item {
    font-size: 12px;
  }
`;
