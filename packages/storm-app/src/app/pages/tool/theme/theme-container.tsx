/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getScene,
  getSimpleThemeAndLayerList,
  ThemeSection,
} from '@waterdesk/data/scene';
import { Key, memo, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ThemeSetting from 'src/components/theme-setting';
import { scenesActions } from 'src/store/scenes';
import { selectCurrentSceneId, selectScenes } from 'src/store/scenes/selectors';
import ScadaLabel from './scada-label';
import SceneContainer from './scene-container';

const ThemeContainer = memo(() => {
  const dispatch = useDispatch();
  const currentSceneId = useSelector(selectCurrentSceneId);
  const scenes = useSelector(selectScenes);
  const settingsList = useMemo((): ThemeSection[] => {
    const scene = getScene(scenes, currentSceneId);
    const themeSections = scene?.themeSections || [];
    const simpleThemeSections = scene?.simpleThemeSections || [];
    return getSimpleThemeAndLayerList(simpleThemeSections, themeSections);
  }, [currentSceneId, scenes]);
  const onLayerChange = useCallback(
    (layerNames: string[], checked: boolean, themeSectionType: string) => {
      dispatch(
        scenesActions.updateSceneLayer({
          themeSectionType,
          layerVisible: checked,
          layerNames,
        }),
      );
    },
    [],
  );
  const onThemeChange = useCallback(
    (
      _value: Key,
      option: { label: string; value: string },
      themeSectionType: string,
    ) => {
      dispatch(
        scenesActions.updateSceneCurrentTheme({
          currentTheme: option,
          themeSectionType,
        }),
      );
    },
    [],
  );
  return (
    <>
      <SceneContainer />
      <ScadaLabel />
      <ThemeSetting
        onLayerChange={onLayerChange}
        onThemeChange={onThemeChange}
        settingsList={settingsList}
      />
    </>
  );
});

ThemeContainer.displayName = 'ThemeContainer';

export default ThemeContainer;
