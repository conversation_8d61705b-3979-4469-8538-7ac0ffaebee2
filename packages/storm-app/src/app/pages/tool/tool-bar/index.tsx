/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AimOutlined,
  FileAddOutlined,
  FullscreenOutlined,
  SwitcherOutlined,
} from '@ant-design/icons';
import { Button } from 'antd-mobile';
import { ReactNode, useState } from 'react';
import { useDispatch } from 'react-redux';
import { hostApp } from 'src/app/host-app';
import IconText from 'src/components/icon-font/icon-text';
import { appDataActions } from 'src/store/app-data';
import useGeoLocation from '../../map/use-geo-location';
import useIssueReportModal from '../issue-report';
import LegendContainer from '../theme/legend-container';
import ThemeContainer from '../theme/theme-container';
import { PopupWrapper, ToolBarWrapper } from './style';

interface Props {
  IssueReportButton?: boolean;
  zoomCurrentPositionButton?: boolean;
}
export default function ToolBar({
  IssueReportButton,
  zoomCurrentPositionButton,
}: Props) {
  useGeoLocation();

  const dispatch = useDispatch();

  const [popupContentVisible, setPopupContentVisible] =
    useState<boolean>(false);
  const [popupContent, setPopupContent] = useState<ReactNode>('');
  const { issueReportModalContext, handleOpenModal } = useIssueReportModal({});

  const toSwitchLayer = () => {
    setPopupContentVisible(true);
    setPopupContent(<ThemeContainer />);
  };

  const displayLegend = () => {
    setPopupContentVisible(true);
    setPopupContent(<LegendContainer />);
  };

  const zoomCurrentPosition = () => {
    dispatch(appDataActions.geoLocationToCenter());
  };

  return (
    <>
      <ToolBarWrapper
        wrap
        direction="vertical"
      >
        {IssueReportButton ? (
          <Button
            shape="rounded"
            onClick={() => handleOpenModal()}
          >
            <FileAddOutlined />
          </Button>
        ) : null}
        <Button
          shape="rounded"
          onClick={toSwitchLayer}
        >
          <SwitcherOutlined />
        </Button>
        {zoomCurrentPositionButton ? (
          <Button
            shape="rounded"
            onClick={zoomCurrentPosition}
          >
            <AimOutlined />
          </Button>
        ) : null}
        <Button
          shape="rounded"
          onClick={() => hostApp().getMainMapView()?.mapToExtent()}
        >
          <FullscreenOutlined />
        </Button>
        <Button
          shape="rounded"
          onClick={displayLegend}
        >
          <IconText text="&#xe762;" />
        </Button>
      </ToolBarWrapper>
      <PopupWrapper
        visible={popupContentVisible}
        onMaskClick={() => {
          setPopupContentVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          height: '55vh',
          overflow: 'auto',
        }}
      >
        {popupContent}
      </PopupWrapper>
      {issueReportModalContext}
    </>
  );
}
