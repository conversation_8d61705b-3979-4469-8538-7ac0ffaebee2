/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { passwordRegex } from '@waterdesk/data/regex';
import { Button, Form, Input } from 'antd-mobile';
import { useEffect } from 'react';

export interface FormValues {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface Props {
  open: boolean;
  onClose: () => void;
  handleSave: (params: FormValues) => void;
}

const EditPassword = (props: Props) => {
  const { open, onClose, handleSave } = props;
  const [form] = Form.useForm();

  const handleOk = async () => {
    const formValues: FormValues = await form.validateFields();
    handleSave({
      ...formValues,
    });
    onClose();
  };

  useEffect(() => {
    if (open) form.resetFields();
  }, [open]);

  return (
    <Form
      style={{ width: '100vw', paddingTop: '40px' }}
      form={form}
      onFinish={handleOk}
      name="editPasswordForm"
      footer={
        <Button
          block
          type="submit"
          color="primary"
          size="middle"
        >
          提交
        </Button>
      }
    >
      <Form.Item
        label="登录密码"
        name="oldPassword"
        rules={[
          {
            required: true,
            message: '请输入登录密码',
            validateTrigger: 'change',
          },
        ]}
      >
        <Input
          type="password"
          autoComplete="new-password"
        />
      </Form.Item>
      <Form.Item
        label="新密码"
        name="newPassword"
        rules={[
          {
            required: true,
            message: '请输入新密码',
            validateTrigger: 'change',
          },
          {
            min: 8,
            message: '密码至少为8位',
          },
          {
            pattern: passwordRegex,
            message: '密码必须包含字母、数字和符号',
          },
        ]}
      >
        <Input
          type="password"
          autoComplete="new-password"
        />
      </Form.Item>
      <Form.Item
        label="确认密码"
        name="confirmPassword"
        rules={[
          {
            required: true,
            message: '请输入确认密码',
            validateTrigger: 'change',
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致!'));
            },
          }),
        ]}
      >
        <Input
          type="password"
          autoComplete="new-password"
        />
      </Form.Item>
    </Form>
  );
};

EditPassword.displayName = 'EditPassword';

export default EditPassword;
