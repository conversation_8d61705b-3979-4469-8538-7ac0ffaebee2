/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  BulbOutlined,
  CustomerServiceOutlined,
  LogoutOutlined,
  ManOutlined,
  SettingOutlined,
  WomanOutlined,
} from '@ant-design/icons';
import { logout } from '@waterdesk/request/login';
import { updateCurrentUserPassword } from '@waterdesk/request/user';
import { Avatar, Card, Dialog, List, Popup, Space, Toast } from 'antd-mobile';
import { ReactNode, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router';
import { SSO_LOGIN_URL } from 'src/config';
import { baseActions } from 'src/store/base';
import { BaseUserInfo, getUserInfoFromLocal } from 'src/utils/tool';
import EditPassword, { FormValues } from './edit-password';
import SettingUrl from './setting-url';
import { Wrapper } from './style';
import Support from './support';
import Version from './version';

export default function User() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useState<ReactNode>('');
  const [userInfo, setUserInfo] = useState<BaseUserInfo | undefined>();

  const onClose = () => {
    setVisible(false);
  };

  const setIp = () => {
    setVisible(true);
    setContent(<SettingUrl close={onClose} />);
  };

  const displayVersion = () => {
    setVisible(true);
    setContent(<Version />);
  };

  const displaySupport = () => {
    setVisible(true);
    setContent(<Support />);
  };

  const handleSaveEditPassword = async (formValues: FormValues) => {
    const { oldPassword, newPassword } = formValues;
    const res = await updateCurrentUserPassword(oldPassword, newPassword);
    if (res.status === 'Success') {
      Toast.show({
        icon: 'success',
        content: '修改成功!',
      });
      setVisible(false);
    } else {
      Toast.show({
        icon: 'fail',
        content: res.errorMessage,
      });
    }
  };

  const editPassword = () => {
    setVisible(true);
    setContent(
      <EditPassword
        open={visible}
        onClose={onClose}
        handleSave={handleSaveEditPassword}
      />,
    );
  };

  const handleSignOut = async () => {
    const result = await Dialog.confirm({
      content: '是否退出登录?',
    });
    if (result) {
      const res = await logout({
        ssoLoginUrl: SSO_LOGIN_URL,
      });
      if (res.status === 'Success') {
        dispatch(baseActions.signOutResetSaga());
        navigate('/appLogin', { replace: true });
        Toast.show({
          icon: 'success',
          content: '退出成功',
        });
      }
    }
  };

  useEffect(() => {
    setUserInfo(getUserInfoFromLocal());
  }, []);

  return (
    <Wrapper>
      <div style={{ textAlign: 'center', margin: '20px 0' }}>
        <Avatar
          src=""
          style={{ '--size': '128px', margin: '0 auto', borderRadius: '50%' }}
        />
        <Space style={{ margin: '10px' }}>
          <div>{userInfo?.userName}</div>
          <div>
            {userInfo?.userSex === '男' ? (
              <ManOutlined style={{ color: '#1677ff' }} />
            ) : (
              <WomanOutlined style={{ color: '#eb2f96' }} />
            )}
          </div>
        </Space>
      </div>
      <Card>
        <List>
          <List.Item
            prefix={<SettingOutlined />}
            onClick={setIp}
          >
            设置IP
          </List.Item>
          <List.Item
            prefix={<SettingOutlined />}
            onClick={editPassword}
          >
            修改密码
          </List.Item>
          <List.Item
            prefix={<CustomerServiceOutlined />}
            onClick={displaySupport}
          >
            技术支持
          </List.Item>
          <List.Item
            prefix={<BulbOutlined />}
            onClick={displayVersion}
          >
            当前版本
          </List.Item>
        </List>
      </Card>
      <Card>
        <List>
          <List.Item
            prefix={<LogoutOutlined />}
            onClick={handleSignOut}
          >
            退出登录
          </List.Item>
        </List>
      </Card>

      <Popup
        position="right"
        visible={visible}
        showCloseButton
        onClose={onClose}
      >
        {content}
      </Popup>
    </Wrapper>
  );
}
