/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Button, Form, Input } from 'antd-mobile';

interface Props {
  close: () => void;
}
export default function SettingUrl(props: Props) {
  const { close } = props;
  const onFinish = (values: { url: string | undefined }) => {
    const data = {
      url: values.url ?? '',
    };
    window.postMessage(data);
    close();
  };
  return (
    <Form
      style={{ width: '100vw', paddingTop: '40px' }}
      requiredMarkStyle="text-optional"
      onFinish={onFinish}
      footer={
        <Button
          block
          type="submit"
          color="primary"
          size="middle"
        >
          提交
        </Button>
      }
    >
      <Form.Header>修改服务地址</Form.Header>
      <Form.Item
        name="url"
        label="IP"
      >
        <Input placeholder="请输入新服务地址" />
      </Form.Item>
    </Form>
  );
}
