/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MailOutlined } from '@ant-design/icons';
import { Card, List } from 'antd-mobile';

export default function Support() {
  return (
    <List
      header="技术支持"
      style={{ width: '100vw', paddingTop: '40px' }}
    >
      <List.Item>
        <Card title="问题反馈">
          <MailOutlined />{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Card>
      </List.Item>
    </List>
  );
}
