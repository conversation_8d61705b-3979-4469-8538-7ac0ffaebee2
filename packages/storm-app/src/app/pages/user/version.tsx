/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getVersion } from '@waterdesk/request/api/get-version';
import { Card, List } from 'antd-mobile';
import { useEffect, useState } from 'react';

export default function Version() {
  const [serverVersion, setServerVersion] = useState<string>('');

  useEffect(() => {
    getVersion().then((res) => {
      if (res.status === 'Success') {
        setServerVersion(res.version || '');
      }
    });
  }, []);

  const webVersion: string = process.env.VERSION || '';

  return (
    <List
      header="版本信息 - 3.0.0"
      style={{ width: '100vw', paddingTop: '40px' }}
    >
      <List.Item>
        <Card title="后台版本">{serverVersion}</Card>
      </List.Item>
      <List.Item>
        <Card title="前端版本">{webVersion}</Card>
      </List.Item>
    </List>
  );
}
