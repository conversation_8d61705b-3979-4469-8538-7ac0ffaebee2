/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ResponseBodyJson } from '@waterdesk/request/request';

export async function injectAuthorizationPlugin(request: Request) {
  const token = localStorage.getItem('token');

  if (token) request.headers.set('hswatersession', token);
  return request;
}

export async function checkAuthorizationPlugin(
  request: Request,
  redirect: () => void,
): Promise<Request | boolean> {
  const { url } = request;
  if (url.match('/login') || url.match('/public/watergis/ssoLogin')) {
    return request;
  }

  const authorization =
    request.headers.get('hswatersession') ?? localStorage.getItem('token');
  if (!authorization) {
    redirect();
    return false;
  }
  return request;
}

export async function redirectLoginPlugin(
  response: Response,
  responseBody: ResponseBodyJson,
  redirect: () => void,
): Promise<Response | boolean> {
  if (responseBody.err_no_login) {
    redirect();
    return false;
  }

  return response;
}
