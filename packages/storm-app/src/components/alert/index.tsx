/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AlertDefinition,
  AlertInstance,
  AlertInstanceList,
  AlertLevel,
  getAlertLevelParam,
} from '@waterdesk/data/alert';
import { Badge, Button, notification } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import IconText from '../icon-font/icon-text';

interface AlertIconProps {
  currentAlertList: AlertInstanceList;
  alertDefinitions: AlertDefinition[];
}

const AlertIcon: React.FC<AlertIconProps> = ({
  currentAlertList,
  alertDefinitions,
}) => {
  const [api, contextHolder] = notification.useNotification();
  const [displayedAlertIds, setDisplayedAlertIds] = useState<Set<string>>(
    new Set(),
  );

  const sortedAlertsByLevelAndTime = useMemo(
    () =>
      [...currentAlertList]
        .filter((i) => (i.endTime ? dayjs().isBefore(i.endTime) : true)) // 过滤已过期预警
        .sort((a, b) => {
          if (a.level !== b.level) {
            return b.level - a.level; // 等级从小到大排序（等级越高数字越小）
          }
          return dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? 1 : -1; // 在相同等级下，按时间从旧到新排序
        }),
    [currentAlertList],
  );

  const firstAlert = useMemo(() => {
    if (sortedAlertsByLevelAndTime.length > 0) {
      const alert =
        sortedAlertsByLevelAndTime[sortedAlertsByLevelAndTime.length - 1];
      const alertLevelParam = getAlertLevelParam(alert, alertDefinitions);
      return { icon: alertLevelParam?.icon, color: alertLevelParam?.color };
    }
    return { icon: '', color: 'black' };
  }, [sortedAlertsByLevelAndTime, alertDefinitions]);

  const handleNotificationClose = (id: string) => {
    setDisplayedAlertIds((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const displayNotification = (
    alert: AlertInstance,
    alertLevelParam?: AlertLevel | null,
  ) => {
    api.open({
      message: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <IconText
            text={alertLevelParam?.icon ?? ''}
            style={{
              color: alertLevelParam?.color,
              fontSize: '1.5em',
              marginRight: 10,
            }}
          />
          <span style={{ color: alertLevelParam?.color, fontSize: '1.125em' }}>
            {alert.definitionTitle}
          </span>
        </div>
      ),
      description: (
        <div>
          <div style={{ marginBottom: 10 }}>
            【{alertLevelParam?.signal}】
            {dayjs(alert.startTime).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          <div>{alert.content}</div>
        </div>
      ),
      duration: 0,
      onClose: () => handleNotificationClose(alert.id),
    });

    setDisplayedAlertIds((prev) => new Set(prev).add(alert.id));
  };

  const openNotification = () => {
    sortedAlertsByLevelAndTime.forEach((alert, index) => {
      if (!displayedAlertIds.has(alert.id)) {
        const alertLevelParam = getAlertLevelParam(alert, alertDefinitions);

        setTimeout(() => {
          displayNotification(alert, alertLevelParam);
        }, index * 500); // 每次调用间隔 0.5 秒
      }
    });
  };

  return (
    <>
      {contextHolder}
      {currentAlertList.length > 0 && (
        <Button
          style={{ marginTop: 8 }}
          type="link"
          icon={
            <Badge
              size="small"
              count={
                sortedAlertsByLevelAndTime.length > 1
                  ? sortedAlertsByLevelAndTime.length
                  : 0
              }
            >
              <IconText
                text={firstAlert.icon ?? ''}
                style={{
                  color: firstAlert.color,
                  fontSize: '2.2em',
                }}
              />
            </Badge>
          }
          onClick={openNotification}
        />
      )}
    </>
  );
};

export default AlertIcon;
