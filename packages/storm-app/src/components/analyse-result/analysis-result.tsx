/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SendOutlined } from '@ant-design/icons';
import Database from '@waterdesk/data/database';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  AnalysisConfig,
  AnalysisResultData,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  getAnalysisUiConfig,
  getCollapseItemKeysByOrder,
  getEnergyHeaderTitle,
  getPipelineLength,
  getPlantStationHeaderTitle,
  getPlantsStationsDataByType,
  getWaterMeterCount,
  mergeSameRoadNameAndDiameter,
  PlantStationInfo,
  PlantStationType,
  QueryParams,
  SendWaterMeter,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Importance, MsgFrom } from '@waterdesk/data/sms';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import {
  Button,
  Collapse,
  CollapseProps,
  Empty,
  Select,
  Space,
  Table,
  TableColumnsType,
} from 'antd';
import { ColumnsType, TableProps } from 'antd/es/table';
import { Key, useState } from 'react';
import IconText from 'src/components/icon-font/icon-text';
import AnalysisResultDetail from 'src/components/quick-analysis/analysis-result-detail';
import SendSMSDrawer from 'src/components/quick-analysis/send-sms-drawer';
import { v4 as uuidv4 } from 'uuid';
import PlantStationTable from '../quick-analysis/plant-station-table';
import AnalysisResultLegend from '.';

type CollapseActiveKey = string | number | (string | number)[];
type PanelKey = AnalysisUiConfigType;

export interface AnalysisResultProps {
  db: Database;
  analysisConfig: AnalysisConfig | undefined;
  analysisData: AnalysisResultData;
  dimension: boolean;
  activeThemes: {
    [key in AnalysisUiConfigType]?: string;
  };
  analysisThemesConfig: {
    [key in AnalysisUiConfigType]?: LegendGroupData[];
  };
  tableSelectedKeys: {
    [key in AnalysisUiConfigType]?: React.Key[];
  };
  canSendSMS?: boolean;
  highlightNode: boolean;
  highlightNodeFunc: boolean;
  showPlantStation?: boolean;
  setHighlightNode: (checked: boolean) => void;
  setTableSelectedKeys: (keys: Key[], tableKey: AnalysisUiConfigType) => void;
  handleActiveOnChange: (panelKey: string, value?: string) => void;
  handleSwitchDimensionVisible: (checked: boolean) => void;
  handleLocate: (otype: string, oname: string, shape: string) => void;
  handleHover: (hoverObject: AnalysisUiDataItem | undefined) => void;
  getWaterMeterList?: (
    params: QueryParams['params'],
  ) => Promise<{ [index: string]: WaterMeterInfo[] }>;
  onSendSMS?: (
    msgFrom: MsgFrom,
    importance: Importance,
    msgText: string,
    dataSource: WaterMeterInfo[],
  ) => Promise<void>;
  plantStationColumn?: TableProps<PlantStationInfo>['columns'];
}

export function getColumnsItem(
  otype: string,
  db: Database,
  vprops?: string[],
  handleLocate?: (otype: string, oname: string, shape: string) => void,
  handleHover?: (hoverObject: AnalysisUiDataItem | undefined) => void,
): ColumnsType<any> {
  const otypeProperty = db.getPropertyInfo(otype);
  if (!otypeProperty || !vprops) {
    return [];
  }
  return vprops.map((vprop) => {
    const title = otypeProperty.getPropertyTitle(vprop) ?? vprop;
    const unitKey = otypeProperty.getPropertyUnit(vprop);
    const unit = unitKey ? getUnitFormat(unitKey) : undefined;
    const unitSymbol = unit?.unitSymbol ? `(${unit.unitSymbol})` : '';
    return {
      key: vprop,
      dataIndex: vprop,
      title: `${title}${unitSymbol}`,
      width: 100,
      ellipsis: true,
      render: (value, record) => {
        if (
          (vprop === 'ROAD_NAME' || vprop === 'TITLE' || vprop === 'NAME') &&
          record.shape
        ) {
          return (
            <Button
              type="link"
              style={{ margin: 0, height: '22px', paddingLeft: '0' }}
              onClick={() =>
                handleLocate?.(record.otype, record.oname, record.shape)
              }
              onMouseOver={() => handleHover?.(record)}
              onMouseOut={() => handleHover?.(undefined)}
            >
              {value || '-'}
            </Button>
          );
        }
        return value || '-';
      },
    };
  });
}

const AnalysisResult = (props: AnalysisResultProps) => {
  const {
    highlightNode,
    highlightNodeFunc,
    analysisConfig,
    analysisData,
    dimension,
    activeThemes,
    analysisThemesConfig,
    tableSelectedKeys,
    db,
    canSendSMS,
    showPlantStation,
    plantStationColumn,
    onSendSMS,
    getWaterMeterList,
    setTableSelectedKeys,
    handleActiveOnChange,
    handleSwitchDimensionVisible,
    handleLocate,
    handleHover,
    setHighlightNode,
  } = props;

  const [activeKeys, setActiveKeys] = useState<CollapseActiveKey>([]);
  const [openKey, setOpenKey] = useState<PanelKey | undefined>(undefined);
  const [openSendSMS, setOpenSendSMS] = useState<PanelKey | false>(false);
  const [sendDataSource, setSendDataSource] = useState<SendWaterMeter[]>([]);

  if (typeof analysisConfig === 'undefined')
    return <Empty description="获取分析结果配置失败" />;

  const analysisUiConfig = getAnalysisUiConfig(analysisConfig);
  const {
    waterOutageArea: waterOutageAreaData,
    affectedArea: affectedAreaData,
  } = analysisData;

  const getSmallTableColumns = (
    panelKey: PanelKey,
  ): TableProps<any>['columns'] => {
    const config = analysisUiConfig[panelKey];
    if (config) {
      const columns = getColumnsItem(
        config.otype,
        db,
        config.category,
        handleLocate,
        handleHover,
      );
      switch (panelKey) {
        case 'valveList':
          return [
            {
              title: '定位',
              width: 50,
              dataIndex: 'fixedLocate',
              align: 'center',
              fixed: true,
              render: (_, record) => (
                <Button
                  title="查看"
                  type="link"
                  style={{ margin: 0 }}
                  onClick={() =>
                    handleLocate(record.otype, record.oname, record.shape)
                  }
                >
                  <IconText text="&#xe69d;" />
                </Button>
              ),
            },
            ...columns,
          ];
        case 'affectedArea':
        case 'affectedAreaUser':
        case 'affectedPipeline':
        case 'closedPipeline':
        case 'reversePipeline':
        case 'waterOutageArea':
        case 'waterOutageAreaUser':
        default:
          return columns;
      }
    }
    return [];
  };

  const getBigTableColumns: <T>(
    panelKey: PanelKey | undefined,
  ) => TableProps<T>['columns'] = (panelKey) => {
    if (typeof panelKey === 'undefined') return [];
    const config = analysisUiConfig[panelKey];
    if (!config) return [];
    return getColumnsItem(config.otype, db, config.table, handleLocate);
  };

  const getTableDataSource = (
    panelKey: AnalysisUiConfigType | undefined,
    analysisData: AnalysisResultData,
  ): AnalysisUiDataItem[] => {
    const {
      waterOutageArea: waterOutageAreaData,
      waterOutageAreaUser: waterOutageAreaUserData,
      closedPipeline: closedPipelineData,
      valveList: valveListData,
      affectedArea: affectedAreaData,
      affectedAreaUser: affectedAreaUserData,
      affectedPipeline: affectedPipelineData,
      reversePipeline: reversePipelineData,
      waterReverseAffectedArea: waterReverseAffectedAreaData,
      waterReverseAffectedAreaUser: waterReverseAffectedAreaUserData,
    } = analysisData;
    switch (panelKey) {
      case 'waterOutageArea':
        return waterOutageAreaData;
      case 'waterOutageAreaUser':
        return waterOutageAreaUserData;
      case 'affectedArea':
        return affectedAreaData;
      case 'affectedAreaUser':
        return affectedAreaUserData;
      case 'affectedPipeline':
        return mergeSameRoadNameAndDiameter(affectedPipelineData);
      case 'closedPipeline':
        return mergeSameRoadNameAndDiameter(closedPipelineData);
      case 'valveList':
        return valveListData;
      case 'reversePipeline':
        return mergeSameRoadNameAndDiameter(reversePipelineData);
      case 'waterReverseAffectedArea':
        return waterReverseAffectedAreaData;
      case 'waterReverseAffectedAreaUser':
        return waterReverseAffectedAreaUserData;
      default:
        return [];
    }
  };

  const getPanelHeaderTitle = (
    panelKey: PanelKey,
    analysisUiConfig: AnalysisConfig,
  ): string => {
    const title = analysisUiConfig[panelKey]?.title;
    if (typeof title !== 'undefined') return title;
    switch (panelKey) {
      case 'waterOutageArea':
        return '停水小区';
      case 'waterOutageAreaUser':
        return '停水小区外用户';
      case 'affectedArea':
        return '压力受影响小区';
      case 'affectedAreaUser':
        return '压力受影响小区外用户';
      case 'affectedPipeline':
        return '流速受影响管道';
      case 'closedPipeline':
        return '关闭的管道';
      case 'valveList':
        return '需要关闭的阀门';
      case 'reversePipeline':
        return '反向管道';
      default:
        return '';
    }
  };

  const getPanelHeaderCountText = (panelKey: AnalysisUiConfigType): string => {
    switch (panelKey) {
      case 'affectedArea':
      case 'waterOutageArea':
        return `(总数:${
          analysisData[panelKey].length
        }, 用户表数量:${getWaterMeterCount(analysisData[panelKey])})`;
      case 'affectedAreaUser':
        return `(${analysisData.affectedAreaUserCount})`;
      case 'waterOutageAreaUser':
        return `(${analysisData.waterOutageAreaUserCount})`;
      case 'valveList':
        return `(${analysisData[panelKey].length})`;
      case 'affectedPipeline':
      case 'closedPipeline':
      case 'reversePipeline':
        return `(${getPipelineLength(analysisData[panelKey], 2)}km)`;
      default:
        return '';
    }
  };

  const getTableRowSelection = (
    tableKey: AnalysisUiConfigType,
  ): TableProps<any>['rowSelection'] => ({
    columnWidth: 40,
    renderCell: (_, __, ___, originNode) => (
      <div style={{ textAlign: 'center' }}>{originNode}</div>
    ),
    onChange: (e: React.Key[]) => setTableSelectedKeys(e, tableKey),
    selectedRowKeys: tableSelectedKeys[tableKey],
  });

  const getTable = (panelKey: PanelKey) => (
    <Table
      rowSelection={getTableRowSelection(panelKey)}
      scroll={{
        y: 'calc(35vh - 100px)',
      }}
      virtual
      size="small"
      pagination={false}
      rowKey="oname"
      columns={getSmallTableColumns(panelKey)}
      dataSource={getTableDataSource(panelKey, analysisData)}
    />
  );

  const getLegendData = (): LegendGroupData[] => {
    const legendGroupData: LegendGroupData[] = [];
    Object.entries(activeThemes).forEach((item, index) => {
      const [panelKey, activeKey] = item as [PanelKey, string];
      const themeConfig = analysisThemesConfig[panelKey];
      const themeItems = themeConfig?.find((item) => item.name === activeKey);

      if (themeItems) {
        legendGroupData.push({
          ...themeItems,
          key: `${themeItems.name}@${index}`,
          title: `${getPanelHeaderTitle(panelKey, analysisConfig)}:${
            themeItems?.title
          }`,
        });
      }
    });
    return legendGroupData;
  };

  const getDataByPressChange = (
    pressChange: 'UP' | 'DOWN',
    data: AnalysisUiDataItem[],
  ): AnalysisUiDataItem[] => {
    if (pressChange === 'UP') {
      return data.filter((f) => {
        if ('press_change' in f && typeof f.press_change === 'number')
          return f.press_change > 0;
        return true;
      });
    }
    return data.filter((f) => {
      if ('press_change' in f && typeof f.press_change === 'number')
        return f.press_change < 0;
      return true;
    });
  };

  const getSendDataSource = async (
    panelKey: PanelKey,
    pressChange?: 'UP' | 'DOWN',
  ) => {
    const params: QueryParams['params'] = {};
    let key1: PanelKey = 'affectedAreaUser';
    let key2: PanelKey = 'affectedArea';
    if (panelKey === 'affectedArea' || panelKey === 'affectedAreaUser') {
      let filterData = [...affectedAreaData];
      if (pressChange) {
        filterData = getDataByPressChange(pressChange, filterData);
      }
      params.affectedArea = filterData.map((item) => item.oname).join(',');
    }
    if (panelKey === 'waterOutageArea' || panelKey === 'waterOutageAreaUser') {
      let filterData = [...waterOutageAreaData];
      if (pressChange) {
        filterData = getDataByPressChange(pressChange, filterData);
      }
      params.waterOutageArea = filterData.map((item) => item.oname).join(',');
      key1 = 'waterOutageAreaUser';
      key2 = 'waterOutageArea';
    }

    const data = await getWaterMeterList?.(params);
    const key1Data = pressChange
      ? getDataByPressChange(
          pressChange,
          getTableDataSource(key1, analysisData),
        )
      : getTableDataSource(key1, analysisData);

    setSendDataSource([
      {
        key: key1,
        label: getPanelHeaderTitle(key1, analysisConfig),
        data: key1Data.map((item) => ({
          key: uuidv4(),
          userName: (item.NAME ?? '') as string,
          address: (item.ADDRESS ?? '') as string,
          waterMeterCode: item.oname,
          phone: (item.PAY_PHONE ??
            item.OWNER_MOBILE_PHONE ??
            item.OWNER_CONTACT_PHONE ??
            item.SMS_PHONE ??
            '') as string,
          pressChange: item.press_change as number,
        })),
      },
      {
        key: key2,
        label: getPanelHeaderTitle(key2, analysisConfig),
        data: data?.[key2] ?? [],
      },
      {
        key: 'custom',
        label: '自定义用户',
        data: [],
      },
    ]);
  };

  const handleOnSendSMS = (panelKey: PanelKey) => {
    setOpenSendSMS(panelKey);
    getSendDataSource(panelKey);
  };

  const getSendSmsButton = (panelKey: PanelKey) => {
    if (!canSendSMS) return null;
    const list = [
      'affectedArea',
      'affectedAreaUser',
      'waterOutageArea',
      'waterOutageAreaUser',
    ];
    if (!list.includes(panelKey)) return null;
    return (
      <SendOutlined
        title="发送通知"
        onClick={(e) => {
          e?.stopPropagation();
          handleOnSendSMS(panelKey);
        }}
      />
    );
  };

  const mergePlantsStationColumns: TableColumnsType<PlantStationInfo> =
    plantStationColumn ?? [
      {
        title: '',
        key: 'ptitle',
        dataIndex: 'ptitle',
        width: 30,
        onCell: (record) => ({
          rowSpan: record.rowSpan,
        }),
      },
      {
        title: '名称',
        key: 'title',
        dataIndex: 'title',
        width: 80,
      },
      {
        title: '方案前',
        key: 'oldValue',
        dataIndex: 'oldValue',
        width: 60,
      },
      {
        title: '方案后',
        key: 'newValue',
        dataIndex: 'newValue',
        width: 60,
      },
      {
        title: '变化',
        key: 'diffValue',
        dataIndex: 'diffValue',
        width: 50,
      },
    ];

  const getCollapseItems = (): CollapseProps['items'] => {
    if (typeof analysisUiConfig === 'undefined') return [];
    const sortCollapsePanelKeys = getCollapseItemKeysByOrder(analysisUiConfig);
    return sortCollapsePanelKeys.map((panelKey) => {
      const analysisThemeConfig = analysisThemesConfig[panelKey];

      const headerText = `${getPanelHeaderTitle(
        panelKey,
        analysisConfig,
      )}${getPanelHeaderCountText(panelKey)}`;

      return {
        label: headerText,
        key: panelKey,
        extra: (
          <Space>
            {analysisThemeConfig?.length ? (
              <Select
                size="small"
                value={activeThemes[panelKey]}
                options={analysisThemeConfig}
                fieldNames={{ label: 'title', value: 'name' }}
                style={{ width: '120px' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onChange={(value) => handleActiveOnChange(panelKey, value)}
              />
            ) : null}
            {getSendSmsButton(panelKey)}
          </Space>
        ),
        children: getTable(panelKey),
      };
    });
  };

  const getPlantsStationsData = (
    type: PlantStationType | undefined,
  ): PlantStationInfo[] =>
    getPlantsStationsDataByType(type, analysisData.plantStation);

  const getEnergyConsumptionData = (): PlantStationInfo[] =>
    analysisData.energyConsumption;

  const getPlantAndPumpStationItems = (): CollapseProps['items'] =>
    showPlantStation
      ? [
          {
            label: getPlantStationHeaderTitle('plants'),
            key: 'plants',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getPlantsStationsData('plants')}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
          },
          {
            label: getPlantStationHeaderTitle('pumpStations'),
            key: 'pumpStation',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getPlantsStationsData('pumpStations')}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
          },
        ]
      : undefined;

  const getEnergyConsumptionItems = (): CollapseProps['items'] =>
    analysisConfig.showEnergy
      ? [
          {
            label: getEnergyHeaderTitle(),
            key: 'energyConsumption',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getEnergyConsumptionData()}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
          },
        ]
      : undefined;
  return (
    <>
      <Collapse
        activeKey={activeKeys}
        bordered={false}
        onChange={(activeKeys: CollapseActiveKey) => setActiveKeys(activeKeys)}
        items={[
          ...(getCollapseItems() ?? []),
          ...(getPlantAndPumpStationItems() ?? []),
          ...(getEnergyConsumptionItems() ?? []),
        ]}
      />
      <AnalysisResultLegend
        showSwitchNode={highlightNodeFunc}
        data={getLegendData()}
        dimension={dimension}
        node={highlightNode}
        handleSwitchNodeVisible={setHighlightNode}
        handleSwitchDimensionVisible={handleSwitchDimensionVisible}
      />
      <AnalysisResultDetail
        open={typeof openKey === 'string'}
        title={
          typeof openKey === 'undefined'
            ? ''
            : getPanelHeaderTitle(openKey, analysisConfig)
        }
        onClose={() => setOpenKey(undefined)}
        columns={getBigTableColumns<any>(openKey)}
        dataSource={getTableDataSource(openKey, analysisData)}
      />
      <SendSMSDrawer
        open={openSendSMS !== false}
        onClose={() => setOpenSendSMS(false)}
        dataSource={sendDataSource}
        updateDataSource={setSendDataSource}
        onSendSMS={onSendSMS}
        onFilter={(pressChange) =>
          getSendDataSource(openSendSMS as PanelKey, pressChange)
        }
      />
    </>
  );
};

AnalysisResult.displayName = 'AnalysisResult';

export default AnalysisResult;
