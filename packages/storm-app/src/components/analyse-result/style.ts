/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const ValveAnalysisFormWrapper = styled.ul`
  list-style: none;
  padding: 0;
  li {
    display: flex;
    justify-content: space-between;
    .object-name {
      width: calc(100% - 20px);
    }
  }
`;

export const AnalysisResultLegendWrapper = styled.div`
  padding: 0 10px 10px;
  border-radius: 5px;
  background: ${({ theme }) => theme.colorBgElevated};
`;
