/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DownOutlined } from '@ant-design/icons';
import { MarkInfoList } from '@waterdesk/data/chart-mark';
import Database from '@waterdesk/data/database';
import {
  getChartProperties,
  getChartPropertiesByConfig,
} from '@waterdesk/data/device';
import { replaceIndicatorTitle } from '@waterdesk/data/object-chart';
import { IObjectItem, makeId, splitId } from '@waterdesk/data/object-item';
import { ObjectChartProperty } from '@waterdesk/data/property/property-info';
import { SchemeTypeData } from '@waterdesk/data/scheme-config';
import {
  convertTimeData,
  GroupTimeData,
  ObjectTimeDataSeries,
  TimeData,
  TimeDataSeries,
} from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { WarnInfoList } from '@waterdesk/data/warn';
import { useDeepCompareEffect } from 'ahooks';
import {
  Checkbox,
  Col,
  Input,
  Radio,
  RadioChangeEvent,
  Row,
  Select,
  Space,
  Typography,
} from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { Dropdown } from 'antd-mobile';
import dayjs, { Dayjs } from 'dayjs';
import { debounce } from 'lodash';
import { useEffect, useState } from 'react';
import { curDb } from 'src/app/host-app';
import DailyDataChart from 'src/components/charts/daily-data-chart';
import TimeDataChart from 'src/components/charts/time-data-chart';
import CustomRangePicker from 'src/components/common/date/custom-range-picker';
import TimePickerRange from '../time-picker';

const { Text } = Typography;

type RangeValue = [Dayjs | null, Dayjs | null] | null;
type VpropType = string | string[] | undefined;

const MaxTimeDays: number = 30;
const MaxDailyDays: number = 366;

type TimeRangeType = '1day' | '3days' | '7days' | 'customize';
const TimeRange1Day: TimeRangeType = '1day';
const TimeRange3Days: TimeRangeType = '3days';
const TimeRange7Days: TimeRangeType = '7days';
const TimeRangeCustomize: TimeRangeType = 'customize';

type DailyRangeType = '1month' | '3months' | 'customize';
const DailyRange1Month: DailyRangeType = '1month';
const DailyRange3Months: DailyRangeType = '3months';
const DailyRangeCustomize: DailyRangeType = 'customize';

interface Props {
  title: string;
  chartCode: string | undefined;
  selectedObjects: Array<IObjectItem>;
  indicatorType: string | undefined;
  indicatorName: string | undefined;
  vprop: VpropType;
  currentDate: string;
  darkMode?: boolean;
  defaultStartDate?: string;
  defaultEndDate?: string;
  dateUpdateTime?: number;
  showWarnMark: boolean;
  showRange: boolean;
  showExtraTools: boolean;
  defaultChainBase: boolean;
  selectedModelLegend: boolean;
  defaultShowAsLineType?: boolean;
  database: Database;
  showToolbox?: boolean;
  startDateChange?: (value: string) => void;
  endDateChange?: (value: string) => void;
  handleShowWarnMark: (checked: boolean) => void;
  handleShowRangeArea: (checked: boolean) => void;
  handleSelectIndicator: (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
  ) => void;
  getObjectTimeValues: (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string | string[],
    startDate: string,
    endDate: string,
    includeMinMax?: boolean,
    includeCorrelatedProps?: string[],
  ) => Promise<Map<string, GroupTimeData>[]>;
  getWarnList: (
    startDate: Dayjs,
    endDate: Dayjs,
    otype: string,
    oname: string,
    vprop: 'SDVAL',
  ) => Promise<WarnInfoList>;
  schemeType: SchemeTypeData;
  getMarkList: (
    startTime: string,
    endTime: string,
    otype: string,
    oname: string,
    vprop: string,
  ) => Promise<MarkInfoList>;
  markList?: MarkInfoList;
  visible?: boolean;
}

function formatChartName(name: string, index: number): string {
  if (!index) return name;
  return `对比方案${index}`;
}

export default function ObjectChart({
  title,
  chartCode,
  selectedObjects,
  indicatorType,
  indicatorName,
  vprop,
  currentDate,
  darkMode,
  defaultStartDate,
  defaultEndDate,
  dateUpdateTime,
  defaultShowAsLineType,
  showWarnMark,
  showRange,
  showExtraTools,
  defaultChainBase,
  database,
  selectedModelLegend,
  showToolbox,
  handleShowRangeArea,
  handleShowWarnMark,
  getObjectTimeValues,
  getWarnList,
  getMarkList,
  handleSelectIndicator,
  schemeType,
  visible,
}: Props) {
  const [objectDataSeries, setObjectDataSeries] = useState<
    Array<ObjectTimeDataSeries>
  >([]);
  const [propertyTitle, setPropertyTitle] = useState<string>('');
  const [unitFormat, setUnitFormat] = useState<UnitFormat | undefined>(
    undefined,
  );
  const [chartProperty, setChartProperty] = useState<
    ObjectChartProperty | undefined
  >(undefined);

  const [minSeries, setMinSeries] = useState<Array<TimeData>>([]);
  const [maxSeries, setMaxSeries] = useState<Array<TimeData>>([]);
  const [rangeValues, setRangeValues] = useState<RangeValue>(null);
  const [timeRangeType, setTimeRangeType] =
    useState<TimeRangeType>(TimeRange1Day);

  const [timeStartDate, setTimeStartDate] = useState<string>(
    dayjs().format('YYYY-MM-DD'),
  );
  const [timeEndDate, setTimeEndDate] = useState<string>(
    dayjs().format('YYYY-MM-DD'),
  );
  const [chainBase, setChainBase] = useState<boolean>(
    defaultChainBase || false,
  );
  const [dailyRangeType, setDailyRangeType] =
    useState<DailyRangeType>(DailyRange1Month);
  const [dailyStartDate, setDailyStartDate] = useState<string>(
    dayjs().add(-1, 'month').format('YYYY-MM-DD'),
  );
  const [dailyEndDate, setDailyEndDate] = useState<string>(
    dayjs().format('YYYY-MM-DD'),
  );
  const [yMinValue, setYMinValue] = useState<number | undefined>();
  const [yMaxValue, setYMaxValue] = useState<number | undefined>();
  const [yDailyMinValue, setYDailyMinValue] = useState<number | undefined>();
  const [yDailyMaxValue, setYDailyMaxValue] = useState<number | undefined>();
  const [warnList, setWarnList] = useState<WarnInfoList>([]);
  const [markList, setMarkList] = useState<MarkInfoList>([]);
  const [showMark, setShowMark] = useState<boolean>(true);
  const [activeKey, setActiveKey] = useState<string | null>(null);
  const [indicatorsOptions, setIndicatorsOptions] = useState<
    { label: string; value: string }[]
  >([]);

  const [indicatorSelect, setIndicatorSelect] = useState('');

  const isDailyChart = (
    chartProperty: ObjectChartProperty | undefined,
  ): boolean => {
    const chartEditor = chartProperty?.editors.find(
      (item) => item.type === 'chart',
    );
    return chartEditor?.dateType === 'day';
  };

  const getStartDate = (
    chartProperty: ObjectChartProperty | undefined,
  ): string => {
    if (isDailyChart(chartProperty)) return dailyStartDate;
    return timeStartDate;
  };

  const getEndDate = (
    chartProperty: ObjectChartProperty | undefined,
  ): string => {
    if (isDailyChart(chartProperty)) return dailyEndDate;
    return timeEndDate;
  };

  useEffect(() => {
    setIndicatorSelect('请选择属性');
  }, [visible]);

  useDeepCompareEffect(() => {
    if (selectedObjects.length > 0) {
      const chartIndicators =
        getChartPropertiesByConfig(
          curDb(),
          selectedObjects[0],
          'chartConfig',
        ) ?? [];

      const chartIndicatorsArr = chartIndicators.map(
        ({ title, oname, otype, vprop }) => ({
          label: replaceIndicatorTitle(
            title ?? oname,
            selectedObjects[0].title,
          ),
          value: makeId(otype, oname, vprop),
        }),
      );
      setIndicatorSelect('请选择属性');
      setIndicatorsOptions(chartIndicatorsArr);
    } else {
      setIndicatorsOptions([]);
    }
  }, [selectedObjects]);

  const getMatchedChartProperty = (
    selectedObject: IObjectItem,
    indicatorType: string | undefined,
    indicatorName: string | undefined,
    vprop: VpropType,
    ignoreDefault?: boolean,
    matchOnlyByVProp: boolean = false,
  ): ObjectChartProperty | undefined => {
    const chartProperties: ObjectChartProperty[] = getChartProperties(
      database,
      selectedObject,
    );

    // Look for a match by indicatorType, indicatorName, and vprop.
    const matchByAllCriteria = chartProperties.find(
      (property) =>
        property.indicatorOType === indicatorType &&
        property.indicatorOName === indicatorName &&
        property.vprop === vprop,
    );

    if (matchByAllCriteria) return matchByAllCriteria;

    // Look for a match by indicatorType and vprop.
    const matchByPartialCriteria = chartProperties.find(
      (property) =>
        property.indicatorOType === indicatorType && property.vprop === vprop,
    );

    if (matchByPartialCriteria) return matchByPartialCriteria;

    // Return the first chart property if ignoreDefault is not set.
    if (!ignoreDefault && chartProperties.length > 0) {
      // If matchOnlyByVProp is true, return the first chart property that matches vprop.
      const matchByVProp = chartProperties.find(
        (property) => property.vprop === vprop,
      );
      if (matchOnlyByVProp && matchByVProp) return matchByVProp;
      return chartProperties[0];
    }

    return undefined;
  };

  const includeMinMax = (
    chartProperty: ObjectChartProperty | undefined,
  ): boolean => {
    const chartEditor = chartProperty?.editors.find(
      (item) => item.type === 'chart',
    );
    return (
      chartEditor?.proCharts === 'TIME_MAX_MIN' &&
      chartEditor?.charts === 'scadaChart'
    );
  };

  const includeCorrelatedProps = (
    chartProperty: ObjectChartProperty | undefined,
  ): string[] => {
    const chartEditor = chartProperty?.editors.find(
      (item) => item.type === 'chart',
    );
    return chartEditor?.correlatedVprop ?? [];
  };

  const singleSelectObjectChart = (
    selectedObject: IObjectItem,
    vprop: string | string[] | undefined,
  ) => {
    const currentChartProperty = getMatchedChartProperty(
      selectedObject,
      indicatorType,
      indicatorName,
      Array.isArray(vprop) ? vprop?.[0] : vprop,
      false,
      true,
    );

    if (currentChartProperty !== undefined) {
      const correlatedProps = includeCorrelatedProps(currentChartProperty);
      getObjectTimeValues(
        selectedObject,
        currentChartProperty.indicatorOType,
        currentChartProperty.indicatorOName,
        Array.isArray(vprop) ? vprop : currentChartProperty.vprop,
        getStartDate(currentChartProperty),
        getEndDate(currentChartProperty),
        includeMinMax(currentChartProperty),
        correlatedProps,
      ).then((result) => {
        const timeDataSeries: Array<TimeDataSeries> = [];
        let minTimeData: Array<TimeData> = [];
        let maxTimeData: Array<TimeData> = [];
        result.forEach((values, index) => {
          values.forEach((item, key) => {
            if (key === 'model') {
              let { timeData } = item;
              const sdvalItem = values.get('SDVAL');
              if (sdvalItem) {
                timeData = convertTimeData(
                  database,
                  item.timeData,
                  item.otype,
                  item.vprop,
                  sdvalItem.otype,
                  sdvalItem.vprop,
                );
              }

              timeDataSeries.push({
                name: '模拟',
                type: 'line',
                timeData,
                legendSelected: selectedModelLegend,
                dataType: 'model',
              });
            } else if (key === 'ENVELOP_MAX') maxTimeData = item.timeData;
            else if (key === 'ENVELOP_MIN') minTimeData = item.timeData;
            else if (correlatedProps.includes(key)) {
              const propertyTitleUnit = database.getPropertyTitleUnit(
                selectedObject.otype,
                key,
              );
              timeDataSeries.push({
                name: formatChartName(propertyTitleUnit[0] ?? key, index),
                type: key === 'SDVAL' ? 'scatter' : 'line',
                timeData: item.timeData,
                dataType: key === 'SDVAL' ? 'scada' : undefined,
              });
            } else {
              let name = formatChartName(selectedObject.title, index);
              if (values.has('model')) name = '监测';
              if (key.endsWith('_FC1')) name = '预测';
              timeDataSeries.push({
                name,
                type: key === 'SDVAL' ? 'scatter' : 'line',
                timeData: item.timeData,
                dataType: key === 'SDVAL' ? 'scada' : undefined,
              });
            }
          });
        });

        const objectSeries: ObjectTimeDataSeries = {
          typeTitle: selectedObject.otype,
          objectTitle: selectedObject.title,
          objectName: selectedObject.oname,
          series: timeDataSeries,
        };
        setMinSeries(minTimeData);
        setMaxSeries(maxTimeData);
        setPropertyTitle(currentChartProperty.propertyTitle || '');
        setUnitFormat(currentChartProperty.unitFormat);
        setChartProperty(currentChartProperty);
        setObjectDataSeries([objectSeries]);
      });
    } else {
      setPropertyTitle('');
      setUnitFormat(undefined);
      setMinSeries([]);
      setMaxSeries([]);
      setObjectDataSeries([]);
    }
  };

  const multipleSelectObjectChart = async (
    objects: Array<IObjectItem>,
    vprop: VpropType,
  ) => {
    const firstObject = objects[0];
    const firstObjectChartProperty = getMatchedChartProperty(
      firstObject,
      indicatorType,
      indicatorName,
      vprop,
      false,
      true,
    );

    if (firstObjectChartProperty === undefined) return;

    const objectChartProperties: Array<[IObjectItem, ObjectChartProperty]> = [];
    objectChartProperties.push([firstObject, firstObjectChartProperty]);
    objects.forEach((item) => {
      if (item === firstObject) return;
      const chartProperty = getMatchedChartProperty(
        item,
        firstObjectChartProperty.indicatorOType,
        firstObjectChartProperty.indicatorOName,
        firstObjectChartProperty.vprop,
        true,
      );
      if (chartProperty !== undefined)
        objectChartProperties.push([item, chartProperty]);
    });

    Promise.all(
      objectChartProperties.map((item) =>
        getObjectTimeValues(
          item[0],
          item[1].indicatorOType,
          item[1].indicatorOName,
          firstObjectChartProperty.vprop,
          getStartDate(firstObjectChartProperty),
          getEndDate(firstObjectChartProperty),
          false,
        ),
      ),
    ).then((datas) => {
      const allObjectTimeDataSeries: Array<ObjectTimeDataSeries> = [];
      if (datas.length === objectChartProperties.length) {
        for (let i = 0; i < datas.length; i += 1) {
          const currentObject: IObjectItem = objectChartProperties[i][0];
          const timeDataSeries: Array<TimeDataSeries> = [];
          datas[i].forEach((results, index) => {
            results.forEach((item, key) => {
              if (key === 'model')
                timeDataSeries.push({
                  name: formatChartName(`${currentObject.title} - 模拟`, index),
                  type: 'line',
                  timeData: item.timeData,
                  legendSelected: selectedModelLegend,
                  dataType: 'model',
                });
              else {
                let name = formatChartName(currentObject.title, index);
                if (results.has('model'))
                  name = formatChartName(
                    `${currentObject.title} - 监测`,
                    index,
                  );
                timeDataSeries.push({
                  name,
                  type: key === 'SDVAL' ? 'scatter' : 'line',
                  timeData: item.timeData,
                  dataType: key === 'SDVAL' ? 'scada' : undefined,
                });
              }
            });
          });

          const objectSeries: ObjectTimeDataSeries = {
            typeTitle: currentObject.otype,
            objectTitle: currentObject.title,
            objectName: currentObject.oname,
            series: timeDataSeries,
          };

          allObjectTimeDataSeries.push(objectSeries);
        }

        setPropertyTitle(firstObjectChartProperty.propertyTitle || '');
        setUnitFormat(firstObjectChartProperty.unitFormat);
        setMinSeries([]);
        setMaxSeries([]);
        setChartProperty(firstObjectChartProperty);
        setObjectDataSeries(allObjectTimeDataSeries);
      }
    });
  };

  const fetchMarkList = async (params: {
    startTime: string;
    endTime: string;
    otype: string;
    oname: string;
    vprop: string;
  }) => {
    const { startTime, endTime, otype, oname, vprop } = params;
    const list = await getMarkList(
      dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
      dayjs(endTime).add(1, 'd').format('YYYY-MM-DD 00:00:00'),
      otype,
      oname,
      vprop,
    );
    setMarkList(list);
  };

  useEffect(() => {
    if (chartCode === undefined) return;

    if (selectedObjects.length === 0) {
      setPropertyTitle('');
      setUnitFormat(undefined);
      setObjectDataSeries([]);
    } else if (selectedObjects.length === 1) {
      singleSelectObjectChart(selectedObjects[0], vprop);
    } else {
      // charts for multiple selected objects
      multipleSelectObjectChart(selectedObjects, vprop);
    }
  }, [
    chartCode,
    timeStartDate,
    timeEndDate,
    dailyStartDate,
    dailyEndDate,
    indicatorSelect,
  ]);

  useEffect(() => {
    if (timeRangeType === TimeRange1Day) {
      setTimeStartDate(currentDate);
      setTimeEndDate(currentDate);
    } else if (timeRangeType === TimeRange3Days) {
      setTimeStartDate(dayjs(currentDate).add(-2, 'day').format('YYYY-MM-DD'));
      setTimeEndDate(currentDate);
    } else if (timeRangeType === TimeRange7Days) {
      setTimeStartDate(dayjs(currentDate).add(-6, 'day').format('YYYY-MM-DD'));
      setTimeEndDate(currentDate);
    }

    if (dailyRangeType === DailyRange1Month) {
      setDailyStartDate(
        dayjs(currentDate).add(-1, 'month').format('YYYY-MM-DD'),
      );
      setDailyEndDate(currentDate);
    } else if (dailyRangeType === DailyRange3Months) {
      setDailyStartDate(
        dayjs(currentDate).add(-3, 'month').format('YYYY-MM-DD'),
      );
      setDailyEndDate(currentDate);
    }
  }, [currentDate]);

  useEffect(() => {
    setWarnList([]);
    if (chartCode === undefined) return;
    if (selectedObjects.length > 0) {
      const currentChartProperty = getMatchedChartProperty(
        selectedObjects[0],
        indicatorType,
        indicatorName,
        vprop,
      );
      if (currentChartProperty) {
        const { indicatorOType, indicatorOName, vprop } = currentChartProperty;
        fetchMarkList({
          startTime: timeStartDate,
          endTime: timeEndDate,
          oname: indicatorOName as string,
          otype: indicatorOType as string,
          vprop,
        });
        if (indicatorOType && indicatorOName && vprop === 'SDVAL') {
          getWarnList(
            dayjs(timeStartDate),
            dayjs(timeEndDate),
            indicatorOType,
            indicatorOName,
            vprop,
          ).then((data) => {
            setWarnList(data);
          });
        }
      }
    }
  }, [chartCode, timeStartDate, timeEndDate]);

  useEffect(() => {
    if (isDailyChart(chartProperty)) {
      if (defaultStartDate && defaultEndDate) {
        setDailyStartDate(defaultStartDate);
        setDailyEndDate(defaultEndDate);
      }
      setDailyRangeType(DailyRangeCustomize);
    } else {
      if (defaultStartDate && defaultEndDate) {
        setTimeStartDate(defaultStartDate);
        setTimeEndDate(defaultEndDate);
      }
      setTimeRangeType(TimeRangeCustomize);
    }
  }, [dateUpdateTime]);

  const handleTimeRangeDateChange = (value: RangePickerProps['value']) => {
    if (value?.[0] && value[1]) {
      if (value[1].diff(value[0], 'days') <= MaxTimeDays) {
        setTimeStartDate(value[0].format('YYYY-MM-DD'));
        setTimeEndDate(value[1].format('YYYY-MM-DD'));
        setTimeRangeType(TimeRangeCustomize);
      }
    }
  };

  const handleDailyRangeDateChange = (value: RangePickerProps['value']) => {
    if (value?.[0] && value[1]) {
      if (value[1].diff(value[0], 'days') <= MaxDailyDays) {
        setDailyStartDate(value[0].format('YYYY-MM-DD'));
        setDailyEndDate(value[1].format('YYYY-MM-DD'));
        setDailyRangeType(DailyRangeCustomize);
      }
    }
  };

  const disabledDate = (current: Dayjs, maxDays: number) => {
    if (!rangeValues) {
      return false;
    }
    const tooLate =
      rangeValues[0] && current.diff(rangeValues[0], 'days') > maxDays;
    const tooEarly =
      rangeValues[1] && rangeValues[1].diff(current, 'days') > maxDays;
    return !!tooEarly || !!tooLate;
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setRangeValues([null, null]);
    } else {
      setRangeValues(null);
    }
  };

  const handleDailyChainBase = (e: CheckboxChangeEvent) => {
    setChainBase(e.target.checked);
    if (e.target.checked && timeRangeType === TimeRange1Day) {
      setTimeStartDate(dayjs(currentDate).add(-2, 'day').format('YYYY-MM-DD'));
      setTimeRangeType(TimeRange3Days);
    }
  };

  const onChangeYMinValue = async (value: string) => {
    if (value.length === 0) {
      setYMinValue(undefined);
      return;
    }
    const newValue = Number(value);
    if (!Number.isNaN(newValue)) setYMinValue(newValue);
  };

  const onChangeYMaxValue = async (value: string) => {
    if (value.length === 0) {
      setYMaxValue(undefined);
      return;
    }
    const newValue = Number(value);
    if (!Number.isNaN(newValue)) setYMaxValue(newValue);
  };

  const onChangeYDailyMinValue = async (value: string) => {
    if (value.length === 0) {
      setYDailyMinValue(undefined);
      return;
    }
    const newValue = Number(value);
    if (!Number.isNaN(newValue)) setYDailyMinValue(newValue);
  };

  const onChangeYDailyMaxValue = async (value: string) => {
    if (value.length === 0) {
      setYDailyMaxValue(undefined);
      return;
    }
    const newValue = Number(value);
    if (!Number.isNaN(newValue)) setYDailyMaxValue(newValue);
  };

  const showWarnCheckbox = showExtraTools && !chainBase && warnList.length > 0;
  const showMarkCheckbox = showExtraTools && !chainBase && markList.length > 0;
  const showRangeCheckbox =
    showExtraTools &&
    !chainBase &&
    minSeries.length > 0 &&
    maxSeries.length > 0;

  const getTimeDataChartInstance = (): React.JSX.Element => (
    <>
      <Row justify="space-between">
        <Col>
          <Space size="small">
            <Text strong>{title}</Text>
            {indicatorsOptions.length > 0 ? (
              <Space size="small">
                <Select
                  value={indicatorSelect}
                  size="small"
                  style={{ width: 150 }}
                  onChange={(value) => {
                    const [indicatorType, indicatorName, vprop] =
                      splitId(value);
                    handleSelectIndicator(indicatorType, indicatorName, vprop);
                    setIndicatorSelect(value);
                  }}
                  options={indicatorsOptions}
                />
              </Space>
            ) : null}
          </Space>
        </Col>
        <Col>
          <Space
            size="small"
            wrap
          >
            {showWarnCheckbox ? (
              <Checkbox
                onChange={(e) => handleShowWarnMark(e.target.checked)}
                defaultChecked={showWarnMark}
                checked={showWarnMark}
              >
                警告
              </Checkbox>
            ) : null}
            {showMarkCheckbox ? (
              <Checkbox
                onChange={(e) => setShowMark(e.target.checked)}
                defaultChecked={showMark}
                checked={showMark}
              >
                标注
              </Checkbox>
            ) : null}
            {showRangeCheckbox ? (
              <Checkbox
                onChange={(e) => handleShowRangeArea(e.target.checked)}
                defaultChecked={showRange}
                checked={showRange}
              >
                包络线
              </Checkbox>
            ) : null}
            {!showExtraTools ? null : (
              <Checkbox onChange={handleDailyChainBase}>多日对比</Checkbox>
            )}
            {!showExtraTools ? null : (
              <>
                <Dropdown
                  arrow={<DownOutlined />}
                  style={{ width: '100%' }}
                  activeKey={activeKey}
                  onChange={(key) => setActiveKey(key)}
                >
                  <Dropdown.Item
                    key="sorter"
                    title="时间选择"
                  >
                    <div style={{ padding: 12 }}>
                      <TimePickerRange onChange={handleTimeRangeDateChange} />
                      {/* <Grid columns={4} gap={16}>
                        {timeRangeTypeRadios}
                      </Grid> */}
                    </div>
                  </Dropdown.Item>
                </Dropdown>
                {/* <CustomRangePicker
                  size="small"
                  style={{ maxWidth: '220px' }}
                  allowClear={false}
                  allowEmpty={[false, false]}
                  value={[dayjs(timeStartDate), dayjs(timeEndDate)]}
                  disabledDate={(val: dayjs.Dayjs) =>
                    disabledDate(val, MaxTimeDays)
                  }
                  onCalendarChange={(val: RangeValue) => setRangeValues(val)}
                  onChange={handleTimeRangeDateChange}
                  onOpenChange={onOpenChange}
                /> */}
              </>
            )}
            <Space.Compact size="small">
              <span>Y轴范围:</span>
              <Input
                style={{ width: 80, textAlign: 'center' }}
                size="small"
                allowClear
                defaultValue={yMinValue}
                placeholder="最小值"
                onChange={debounce(
                  (e) => onChangeYMinValue(e.target.value),
                  200,
                )}
              />
              <Input
                className="site-input-split"
                style={{
                  width: 20,
                  borderLeft: 0,
                  borderRight: 0,
                  pointerEvents: 'none',
                }}
                size="small"
                placeholder="~"
                disabled
              />
              <Input
                className="site-input-right"
                style={{
                  width: 80,
                  textAlign: 'center',
                }}
                size="small"
                allowClear
                defaultValue={yMaxValue}
                placeholder="最大值"
                onChange={debounce(
                  (e) => onChangeYMaxValue(e.target.value),
                  200,
                )}
              />
            </Space.Compact>
          </Space>
        </Col>
      </Row>
      <TimeDataChart
        height="250px"
        startDate={timeStartDate}
        endDate={timeEndDate}
        propertyName={propertyTitle}
        unitFormat={unitFormat}
        minSeries={minSeries}
        maxSeries={maxSeries}
        objectTimeDataSeries={objectDataSeries}
        solutionChart={!showExtraTools}
        chainBase={chainBase}
        showRange={showRange && showRangeCheckbox}
        yMinValue={yMinValue}
        yMaxValue={yMaxValue}
        darkMode={darkMode}
        warnList={warnList}
        showWarnMark={showWarnMark && showWarnCheckbox}
        defaultShowAsLineType={defaultShowAsLineType}
        showLegendWhenSingle
        title={title}
        schemeType={schemeType}
        showToolbox={showToolbox}
        markList={markList}
        showMark={showMark}
      />
    </>
  );

  const handleDateRangeTypeChange = (e: RadioChangeEvent) => {
    setDailyRangeType(e.target.value);
    switch (e.target.value) {
      case DailyRange1Month:
        setDailyStartDate(
          dayjs(currentDate).add(-1, 'month').format('YYYY-MM-DD'),
        );
        setDailyEndDate(currentDate);
        break;
      case DailyRange3Months:
        setDailyStartDate(
          dayjs(currentDate).add(-3, 'month').format('YYYY-MM-DD'),
        );
        setDailyEndDate(currentDate);
        break;
      case DailyRangeCustomize:
        setDailyStartDate(
          dayjs(dailyEndDate).add(-6, 'month').format('YYYY-MM-DD'),
        );
        break;
      default:
    }
  };

  const getDailyDataChartInstance = (): any => (
    <>
      <Row justify="space-between">
        <Col>
          <Space size="small">
            <Text strong>{title}</Text>
          </Space>
        </Col>
        <Col>
          <Space
            size="small"
            wrap
          >
            <Radio.Group
              value={dailyRangeType}
              onChange={handleDateRangeTypeChange}
            >
              <Radio value={DailyRange1Month}>近一个月</Radio>
              <Radio value={DailyRange3Months}>近三个月</Radio>
              <Radio value={DailyRangeCustomize}>自定义</Radio>
            </Radio.Group>
            <CustomRangePicker
              size="small"
              allowClear={false}
              allowEmpty={[false, false]}
              value={[dayjs(dailyStartDate), dayjs(dailyEndDate)]}
              disabledDate={(val: dayjs.Dayjs) =>
                disabledDate(val, MaxDailyDays)
              }
              onCalendarChange={(val: RangeValue) => setRangeValues(val)}
              onChange={handleDailyRangeDateChange}
              onOpenChange={onOpenChange}
            />
            <Space.Compact size="small">
              <span>Y轴范围:</span>
              <Input
                style={{ width: 80, textAlign: 'center' }}
                size="small"
                allowClear
                defaultValue={yDailyMinValue}
                placeholder="最小值"
                onChange={debounce(
                  (e) => onChangeYDailyMinValue(e.target.value),
                  200,
                )}
              />
              <Input
                className="site-input-split"
                style={{
                  width: 20,
                  borderLeft: 0,
                  borderRight: 0,
                  pointerEvents: 'none',
                }}
                size="small"
                placeholder="~"
                disabled
              />
              <Input
                className="site-input-right"
                style={{
                  width: 80,
                  textAlign: 'center',
                }}
                size="small"
                allowClear
                defaultValue={yDailyMaxValue}
                placeholder="最大值"
                onChange={debounce(
                  (e) => onChangeYDailyMaxValue(e.target.value),
                  200,
                )}
              />
            </Space.Compact>
          </Space>
        </Col>
      </Row>
      <DailyDataChart
        height="250px"
        startDate={dailyStartDate}
        endDate={dailyEndDate}
        propertyName={propertyTitle}
        unitFormat={unitFormat}
        objectTimeDataSeries={objectDataSeries}
        yMinValue={yDailyMinValue}
        yMaxValue={yDailyMaxValue}
        darkMode={darkMode}
      />
    </>
  );

  return isDailyChart(chartProperty)
    ? getDailyDataChartInstance()
    : getTimeDataChartInstance();
}
