/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DownOutlined } from '@ant-design/icons';
import { getTimeStepName, TimeStepType } from '@waterdesk/data/object-chart';
import { Button, Checkbox, Form, FormInstance, Radio } from 'antd';
import { Dropdown, Grid, Selector } from 'antd-mobile';
import { useEffect, useState } from 'react';
import {
  getTimeRangeByType,
  ObjectChartFormValues,
  TimeRangeType,
} from 'src/components/charts/object-chart/object-chart-content';
import TimeStepSelector from '../radio-button/time-step-selector';
import TimePickerRange from '../time-picker';

interface Props {
  visible: boolean;
  form: FormInstance<ObjectChartFormValues>;
  /** 时间范围的显示/隐藏; */
  showTimeRange?: boolean;
  /** 时间间隔的显示/隐藏 */
  showTimeStep?: boolean;

  timeRangeTypeOptions?: TimeRangeType[];
  timeStepTypeOptions?: TimeStepType[];
}

const defaultOptions = [
  TimeRangeType.threeDays,
  TimeRangeType.sevenDays,
  TimeRangeType.oneMonth,
  TimeRangeType.oneYear,
];

const defaultTimeStepOptions = [
  TimeStepType.oneMinute,
  TimeStepType.fiveMinutes,
  TimeStepType.fifteenMinutes,
  TimeStepType.thirtyMinutes,
  TimeStepType.oneHour,
];

export default function ScadaChartForm(props: Props) {
  const {
    visible,
    form,
    showTimeRange,
    showTimeStep,
    timeRangeTypeOptions,
    timeStepTypeOptions,
  } = props;

  const [activeKey, setActiveKey] = useState<string | null>(null);
  const handleTimeRangeTypeChange = (changeValue: string) => {
    form.setFieldValue('timeRange', getTimeRangeByType(changeValue));
  };

  useEffect(() => {
    if (!visible) {
      setActiveKey(null);
    }
  }, [visible]);

  const timeRangeTypeRadios = (timeRangeTypeOptions || defaultOptions).map(
    (timeRangeType) => {
      switch (timeRangeType) {
        case TimeRangeType.threeDays:
          return (
            <Button
              key={TimeRangeType.threeDays}
              value={TimeRangeType.threeDays}
              onClick={() => handleTimeRangeTypeChange(TimeRangeType.threeDays)}
            >
              近三日
            </Button>
          );
        case TimeRangeType.sevenDays:
          return (
            <Button
              key={TimeRangeType.sevenDays}
              value={TimeRangeType.sevenDays}
              onClick={() => handleTimeRangeTypeChange(TimeRangeType.sevenDays)}
            >
              近七日
            </Button>
          );
        case TimeRangeType.oneMonth:
          return (
            <Button
              key={TimeRangeType.oneMonth}
              value={TimeRangeType.oneMonth}
              onClick={() => handleTimeRangeTypeChange(TimeRangeType.oneMonth)}
            >
              近一月
            </Button>
          );
        case TimeRangeType.threeMonths:
          return (
            <Button
              key={TimeRangeType.threeMonths}
              value={TimeRangeType.threeMonths}
              onClick={() =>
                handleTimeRangeTypeChange(TimeRangeType.threeMonths)
              }
            >
              近三月
            </Button>
          );
        case TimeRangeType.customize:
          return (
            <Radio
              key={TimeRangeType.customize}
              value={TimeRangeType.customize}
            >
              自定义
            </Radio>
          );
        default:
          return null;
      }
    },
  );

  const handleChartConfig = (value: string[]) => {
    form.setFieldsValue({
      warnLine: value.includes('warnLine'),
      envelopLine: value.includes('envelopLine'),
      chainBase: value.includes('chainBase'),
    });
  };

  const timeStepTypeRadios = (
    timeStepTypeOptions || defaultTimeStepOptions
  ).map((timeStepType) => ({
    label: getTimeStepName(timeStepType),
    value: timeStepType,
  }));

  return (
    <Form
      layout="inline"
      form={form}
      name="objectChartForm"
    >
      <Dropdown
        arrow={<DownOutlined />}
        style={{ width: '100%' }}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      >
        <Dropdown.Item
          key="sorter"
          title="时间"
        >
          <div style={{ padding: 12 }}>
            <Form.Item
              name="timeRange"
              hidden={!showTimeRange}
            >
              <TimePickerRange />
            </Form.Item>
            <Form.Item
              noStyle
              name="timeRangeType"
              hidden={!showTimeRange}
            >
              <Grid
                columns={4}
                gap={16}
              >
                {timeRangeTypeRadios}
              </Grid>
            </Form.Item>
          </div>
        </Dropdown.Item>
        <Dropdown.Item
          key="bizop"
          title="步长"
        >
          <div style={{ padding: 12 }}>
            <Form.Item
              name="timeStep"
              hidden={!showTimeStep}
            >
              <TimeStepSelector options={timeStepTypeRadios} />
            </Form.Item>
          </div>
        </Dropdown.Item>
        <Dropdown.Item
          key="more"
          title="更多"
        >
          <div style={{ padding: 12 }}>
            <Selector
              columns={3}
              options={[
                { label: '显示警告线', value: 'warnLine' },
                { label: '显示包络线', value: 'envelopLine' },
                { label: '多日对比', value: 'chainBase' },
              ]}
              multiple
              onChange={handleChartConfig}
            />
            <Form.Item
              noStyle
              name="warnLine"
              valuePropName="checked"
              hidden
            >
              <Checkbox>显示警告线</Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              name="envelopLine"
              valuePropName="checked"
              hidden
            >
              <Checkbox>显示包络线</Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              name="chainBase"
              valuePropName="checked"
              hidden
            >
              <Checkbox>多日对比</Checkbox>
            </Form.Item>
          </div>
        </Dropdown.Item>
      </Dropdown>
    </Form>
  );
}
