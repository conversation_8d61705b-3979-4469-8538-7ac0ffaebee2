/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ObjectTimeDataSeries } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import dayjs from 'dayjs';
import {
  BarChart,
  // 系列类型的定义后缀都为 SeriesOption
  BarSeriesOption,
  LineChart,
  LineSeriesOption,
} from 'echarts/charts';
import {
  // 数据集组件
  DatasetComponent,
  DatasetComponentOption,
  GridComponent,
  GridComponentOption,
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { useMemo } from 'react';
import { WrappedReactECharts } from '../react-echarts';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
  | ToolboxComponentOption
>;

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

interface Props {
  startDate: string;
  endDate: string;
  propertyName: string;
  unitFormat: UnitFormat | undefined;
  objectTimeDataSeries: Array<ObjectTimeDataSeries>;
  yMinValue: number | undefined;
  yMaxValue: number | undefined;
  width?: string;
  height?: string;
  darkMode?: boolean;
  groupName?: string;
  showDataTable?: boolean;
}

export default function DailyDataChart(props: Props) {
  const {
    startDate,
    endDate,
    propertyName,
    unitFormat,
    objectTimeDataSeries,
    yMinValue,
    yMaxValue,
    width,
    height,
    darkMode,
    groupName,
    showDataTable = true,
  } = props;

  const getYAxisName = (): string => {
    let name = propertyName;
    if (unitFormat?.unitSymbol)
      name = `${propertyName} (${unitFormat?.unitSymbol})`;
    // pad space to make sure the Y axis name display fully
    if (name.length > 4) name = name.padStart(name.length * 2, ' ');
    return name;
  };

  const getLegendData = (): string[] => {
    const legendData: string[] = [];
    objectTimeDataSeries.forEach((objectItem) => {
      legendData.push(...objectItem.series.map((item) => item.name));
    });

    if (legendData.length <= 1) return [];
    return legendData;
  };

  const timeLabelFormatter = (value: string): string => {
    const time = dayjs(value);
    if (time.isSame(dayjs(time).startOf('year'))) {
      // 一月一日显示年份
      return dayjs(value).format('YYYY');
    }
    return dayjs(value).format('M/DD');
  };

  const timePointerFormatter = (value: string): string =>
    dayjs(value).format('YYYY-MM-DD');

  const tooltipValueFormatter = (value: any): string => {
    if (unitFormat !== undefined) return unitFormat.getValueWithSymbol(value);
    return value;
  };

  const getMarkLine = (unitFormat: UnitFormat | undefined): any => {
    if (unitFormat === undefined || unitFormat.unitType !== 'R')
      return undefined;
    const values: number[] = unitFormat.getYAxisValues() as number[];
    if (values === undefined) return undefined;
    return {
      symbol: 'none',
      emphasis: {
        disabled: true,
      },
      label: {
        position: 'start',
        formatter: '{b}',
      },
      lineStyle: {
        color: darkMode ? '#484753' : '#E0E6F1',
        type: 'solid',
      },
      data: values.map((item) => ({
        yAxis: item,
        name: unitFormat.getValue(item),
      })),
    };
  };

  const getDataSeries = (
    objectTimeDataSeries: Array<ObjectTimeDataSeries>,
  ): any => {
    const seriesData: any = [];
    objectTimeDataSeries.forEach((objectItem) => {
      seriesData.push(
        ...objectItem.series.map((seriesItem) => ({
          name: seriesItem.name,
          type: 'bar',
          zlevel: 2,
          emphasis: {
            focus: 'series',
          },
          symbolSize: 2,
          showSymbol: false,
          data: seriesItem.timeData.map((dataItem) => [
            dataItem.time,
            dataItem.value,
          ]),
          markLine: getMarkLine(unitFormat),
        })),
      );
    });

    return seriesData;
  };

  const isYAxisValues = (unitFormat: UnitFormat, value: number): boolean => {
    const yAxisValues = unitFormat.getYAxisValues();
    for (let i = 0; i < yAxisValues.length; i += 1) {
      const yValue = yAxisValues[i];
      if (typeof yValue === 'number' && yValue === value) return true;
      if (typeof yValue === 'string' && yValue === value.toString())
        return true;
    }

    return false;
  };

  const yAxisLabelFormatter = (
    unitFormat: UnitFormat | undefined,
    val: number,
  ): string => {
    if (unitFormat?.unitType === 'R') return '      ';
    if (unitFormat) {
      if (unitFormat.unitType === 'E' && !isYAxisValues(unitFormat, val))
        return '';

      const v = unitFormat.getValue(val);
      if (v !== undefined) return v.toString();
    }

    return `${val}`;
  };

  const getAllDates = (series: any): Array<string> => {
    const allDates: Set<string> = new Set();
    series.forEach((seriesItem: any) => {
      seriesItem.data.forEach((dataItem: (string | number)[]) => {
        if (typeof dataItem[0] === 'string') allDates.add(dataItem[0]);
      });
    });
    return Array.from(allDates).sort();
  };

  const optionToContent = (opt: any): any => {
    const { series } = opt;
    if (!Array.isArray(series)) return '';
    const allDates = getAllDates(series);
    let table =
      `<table border="1" style="color:#333;margin-left:20px;border-collapse:collapse;font-size:14px;text-align:center;user-select:all"><tbody><tr>` +
      `<td>日期</td>`;
    series.forEach((seriesItem) => {
      table += `<td>${seriesItem.name}</td>`;
    });
    table += `</tr>`;

    const seriesData: Array<Map<string, number>> = [];
    series.forEach((seriesItem) => {
      const data: Map<string, number> = new Map();
      seriesItem.data.forEach((dataItem: (string | number)[]) => {
        if (typeof dataItem[0] === 'string' && typeof dataItem[1] === 'number')
          data.set(dataItem[0], dataItem[1]);
      });
      seriesData.push(data);
    });

    for (let i = 0; i < allDates.length; i += 1) {
      const currentDate = allDates[i];
      table += `<tr> <td>${dayjs(currentDate).format('YYYY-MM-DD')}</td>`;
      for (let j = 0; j < seriesData.length; j += 1) {
        const value = seriesData[j].get(currentDate);
        if (value === undefined) table += `<td></td>`;
        else table += `<td>${value}</td>`;
      }
      table += `</tr>`;
    }
    table += '</tbody></table>';
    return table;
  };

  const option: ECOption = useMemo(() => {
    const yAxisValues = unitFormat?.getYAxisValues();
    let yMax;
    if (yMaxValue !== undefined) yMax = yMaxValue;
    else if (yAxisValues && yAxisValues.length > 0)
      yMax = yAxisValues[yAxisValues.length - 1];

    return {
      color: [
        '#1492cc',
        '#008000',
        '#f5b533',
        '#ed3434',
        '#0000ff',
        '#dc69aa',
        '#7eb00a',
        '#b6a2de',
        '#07a2a4',
        '#27727b',
      ],
      grid: {
        left: '1%',
        right: '20px',
        bottom: '1%',
        top: '20%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        valueFormatter: (value) => tooltipValueFormatter(value),
      },
      legend: {
        data: getLegendData(),
      },
      dataZoom: [
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      xAxis: {
        type: 'time',
        min: dayjs(startDate).startOf('day').format('YYYY-MM-DD'),
        max: dayjs(endDate).startOf('day').format('YYYY-MM-DD'),
        axisLabel: {
          formatter(value: string) {
            return timeLabelFormatter(value);
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        axisPointer: {
          label: {
            formatter(params: any) {
              return timePointerFormatter(params.value);
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        name: getYAxisName(),
        axisLabel: {
          formatter(val: number) {
            return yAxisLabelFormatter(unitFormat, val);
          },
        },
        splitLine: {
          show: unitFormat?.unitType !== 'R',
        },
        min: yMinValue,
        max: yMax,
        data: unitFormat?.getYAxisValues(),
      },
      toolbox: {
        show: true,
        feature: {
          dataZoom: {
            yAxisIndex: 'none',
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原',
            },
          },
          dataView: {
            show: showDataTable,
            readOnly: false,
            title: '数据表格',
            lang: [propertyName, '关闭', '刷新'],
            optionToContent(opt: any) {
              return optionToContent(opt);
            },
          },
          restore: {
            title: '还原',
          },
        },
      },
      series: getDataSeries(objectTimeDataSeries),
    };
  }, [objectTimeDataSeries, yMinValue, yMaxValue, darkMode]);

  return (
    <WrappedReactECharts
      groupName={groupName}
      option={option}
      style={{
        width: `${width ?? '100%'}`,
        height: `${height ?? 'calc(100% - 24px)'}`,
      }}
    />
  );
}
