/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import dayjs from 'dayjs';
import {
  // 系列类型的定义后缀都为 SeriesOption
  LineChart,
  LineSeriesOption,
} from 'echarts/charts';
import {
  GridComponent,
  GridComponentOption,
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { useMemo } from 'react';
import { WrappedReactECharts } from '../react-echarts';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = echarts.ComposeOption<
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
>;

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  TransformComponent,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

export interface SeriesItem {
  name: string;
  // color: string;
  timeData: TimeData[];
}

interface Props {
  date: string;
  propertyName: string;
  unitFormat: UnitFormat | undefined;
  seriesItems: Array<SeriesItem>;
  width?: string;
  height?: string;
  yScale?: boolean;
}

export default function GeneralLineChart(props: Props) {
  const { date, propertyName, unitFormat, seriesItems, width, height, yScale } =
    props;

  const getYAxisName = (): string => {
    let name = propertyName;
    if (unitFormat?.unitSymbol)
      name = `${propertyName} (${unitFormat?.unitSymbol})`;
    // pad space to make sure the Y axis name display fully
    if (name.length > 4) name = name.padStart(name.length * 2, ' ');
    return name;
  };

  const getLegendData = (): string[] => {
    const legendData: string[] = [];
    seriesItems.forEach((objectItem) => {
      legendData.push(objectItem.name);
    });

    return legendData;
  };

  const timeLabelFormatter = (value: number, date: string): string => {
    const time = dayjs(value);
    const startTime = dayjs(date).format('YYYY-MM-DD 00:00:00');
    const diff = time.diff(startTime, 'minute');
    const hours = Math.floor(diff / 60);
    const minutes = diff - 60 * hours;
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;
  };

  const timePointerFormatter = (value: number | string | Date): string => {
    const time = dayjs(value);

    if (time.second() === 0) return time.format('H:mm');
    return time.format('H:mm:ss');
  };

  const tooltipValueFormatter = (value: any): string => {
    if (typeof value === 'number') {
      if (unitFormat !== undefined) return unitFormat.getValueWithSymbol(value);
      return value.toString();
    }

    return value;
  };

  const getDataSeries = (
    objectTimeDataSeries: Array<SeriesItem>,
  ): ECOption['series'] => {
    const seriesData: ECOption['series'] = objectTimeDataSeries.map(
      (seriesItem) => ({
        name: seriesItem.name,
        type: 'line',
        emphasis: {
          focus: 'series',
        },
        symbolSize: 2,
        showSymbol: false,
        sampling: 'lttb',
        large: true,
        data: seriesItem.timeData.map((dataItem) => [
          dataItem.time,
          dataItem.value,
        ]),
      }),
    );

    return seriesData;
  };

  const isYAxisValues = (unitFormat: UnitFormat, value: number): boolean => {
    const yAxisValues = unitFormat.getYAxisValues();
    for (let i = 0; i < yAxisValues.length; i += 1) {
      const yValue = yAxisValues[i];
      if (typeof yValue === 'number' && yValue === value) return true;
      if (typeof yValue === 'string' && yValue === value.toString())
        return true;
    }

    return false;
  };

  const yAxisLabelFormatter = (
    unitFormat: UnitFormat | undefined,
    val: number,
  ): string => {
    if (unitFormat) {
      if (unitFormat.unitType === 'E' && !isYAxisValues(unitFormat, val))
        return '';

      const v = unitFormat.getValue(val);
      if (v !== undefined) return v.toString();
    }

    return `${val}`;
  };

  const getAllTimes = (series: any): Array<string> => {
    const allDates: Set<string> = new Set();
    series.forEach((seriesItem: any) => {
      seriesItem.data.forEach((dataItem: (string | number)[]) => {
        if (typeof dataItem[0] === 'string') allDates.add(dataItem[0]);
      });
    });
    return Array.from(allDates).sort();
  };

  const optionToContent = (opt: any): any => {
    const { series } = opt;
    if (!Array.isArray(series)) return '';
    const allDates = getAllTimes(series);
    let table =
      `<table border="1" style="color:#333;margin-left:20px;border-collapse:collapse;font-size:14px;text-align:center;user-select:all"><tbody><tr>` +
      `<td>时间</td>`;
    series.forEach((seriesItem) => {
      table += `<td>${seriesItem.name}</td>`;
    });
    table += `</tr>`;

    const seriesData: Array<Map<string, number>> = [];
    series.forEach((seriesItem) => {
      const data: Map<string, number> = new Map();
      seriesItem.data.forEach((dataItem: (string | number)[]) => {
        if (typeof dataItem[0] === 'string' && typeof dataItem[1] === 'number')
          data.set(dataItem[0], dataItem[1]);
      });
      seriesData.push(data);
    });

    for (let i = 0; i < allDates.length; i += 1) {
      const currentDate = allDates[i];
      table += `<tr> <td>${currentDate}</td>`;
      for (let j = 0; j < seriesData.length; j += 1) {
        const value = seriesData[j].get(currentDate);
        if (value === undefined) table += `<td></td>`;
        else table += `<td>${value}</td>`;
      }
      table += `</tr>`;
    }
    table += '</tbody></table>';
    return table;
  };

  const series: ECOption['series'] = useMemo(() => {
    const seriesData = getDataSeries(seriesItems);
    return seriesData;
  }, [seriesItems]);

  const option: ECOption = useMemo(() => {
    const option: ECOption = {
      animation: false,
      color: [
        '#1492cc',
        '#cb3f1c',
        '#0000ff',
        '#f5b533',
        '#008000',
        '#dc69aa',
        '#7eb00a',
        '#b6a2de',
        '#07a2a4',
        '#27727b',
      ],
      grid: {
        left: '1%',
        right: '20px',
        bottom: '1%',
        top: '20%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        valueFormatter: (value) => tooltipValueFormatter(value),
        position: (pos, _params, _el, _elRect, size) => {
          let x = pos[0] + 20;
          let y = pos[1];
          const pointX = pos[0];
          const pointY = pos[1];
          const boxWidth = size.contentSize[0];
          const boxHeight = size.contentSize[1];
          if (pointX + boxWidth + 20 > size.viewSize[0]) x = pointX - boxWidth;
          if (pointY + boxHeight + 40 > size.viewSize[1])
            y = pointY - boxHeight;
          if (y < 0) {
            if (boxHeight > size.viewSize[1]) y = 0;
            else y = 20;
          }

          return [x, y];
        },
      },
      legend: {
        type: 'scroll',
        data: getLegendData(),
      },
      dataZoom: [
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      xAxis: {
        type: 'time',
        min: dayjs(date).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        max: dayjs(date)
          .add(1, 'day')
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
        axisLabel: {
          formatter(value) {
            return timeLabelFormatter(value, date);
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        axisPointer: {
          label: {
            formatter(params) {
              return timePointerFormatter(params.value);
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        name: getYAxisName(),
        scale: yScale,
        axisLabel: {
          formatter(val: number) {
            return yAxisLabelFormatter(unitFormat, val);
          },
        },
        axisPointer: {
          label: {
            formatter(params: any) {
              return yAxisLabelFormatter(unitFormat, params.value);
            },
          },
        },
      },
      series,
      toolbox: {
        show: true,
        feature: {
          dataView: {
            readOnly: false,
            title: '数据表格',
            lang: [propertyName, '关闭', '刷新'],
            optionToContent(opt: any) {
              return optionToContent(opt);
            },
          },
          restore: {
            title: '还原',
          },
        },
      },
    };

    return option;
  }, [seriesItems]);

  return (
    <WrappedReactECharts
      option={option}
      style={{
        height: `${height ?? '100%'}`,
        width: `${width ?? '100%'}`,
      }}
    />
  );
}
