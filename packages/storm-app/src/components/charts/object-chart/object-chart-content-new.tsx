/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MarkInfoItem,
  SaveChartMarkCallbackParams,
  ScadaMarkConfig,
} from '@waterdesk/data/chart-mark';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from '@waterdesk/data/event-scheduling/related-info';
import {
  AxisDataItem,
  ChartCompareType,
  ChartGroup,
  TableData,
  TimeStepType,
  ToolboxName,
} from '@waterdesk/data/object-chart';
import { SchemeTypeData } from '@waterdesk/data/scheme-config';
import { ObjectTimeDataSeries, TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { WarnInfoItem } from '@waterdesk/data/warn';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import * as echarts from 'echarts';
import { EChartsOption } from 'echarts';
import { JSX, useMemo, useRef } from 'react';
import { MultiDateValue } from 'src/components/multi-date-picker';
import { EChartsEvents } from '../react-echarts';
import TimeDataChartNew from '../time-data-chart/index-new';
import ObjectChartTableNew from './object-chart-table-new';
import { ObjectChartContentWrapperNew } from './style';
import { UpdateMinMaxType } from './use-axis-data';

export type CustomCompareTypeValue = {
  type: ChartCompareType;
  dateRanges: Array<RangePickerProps['value']>;
};

export interface ObjectChartFormValues {
  warn?: boolean;
  warnLine?: boolean;
  envelopLine?: boolean;
  chainBase?: boolean;
  timeRange?: [Dayjs, Dayjs];
  compareType?: CustomCompareTypeValue;
  yMin?: number;
  yMax?: number;
  timeRangeType?: string;
  timeStep?: number;
  indicators?: string | string[];
  mark?: boolean;
  leftAxis?: string | string[];
  rightAxis?: string | string[];
}

export interface TableDataConfig {
  startDate: string;
  endDate: string;
  unitFormat?: UnitFormat;
  objectTimeDataSeries: Array<ObjectTimeDataSeries>;
  minSeries: Array<TimeData>;
  maxSeries: Array<TimeData>;
  chainBase?: boolean;
  multiChainBase?: MultiDateValue;
  height?: number | string;
}

interface Props {
  darkMode?: boolean;
  formData: Partial<
    ObjectChartFormValues & {
      [index: string]: any;
    }
  >;
  height?: string;
  showToolbox?: ToolboxName[];
  showSettingMark?: boolean;
  defaultShowAsLineType?: boolean;
  schemeType: SchemeTypeData;
  handleMarkEvent?: (
    markInfo: Partial<MarkInfoItem>,
    scadaMarkConfig: ScadaMarkConfig[],
    callback?: (params: SaveChartMarkCallbackParams) => void,
  ) => void;
  handleWarnEvent?: (warnInfo: WarnInfoItem) => void;
  handleEditLimit?: (
    otype: string,
    oname: string,
    vprops: string,
    callback?: (params: UpdateMinMaxType) => void,
  ) => void;
  axisData: AxisDataItem[];
  updateAxisData: () => void;
  updatePositionLine: (
    positionLine:
      | {
          x: string | number;
          y: string | number | null;
        }
      | undefined,
    id?: string | undefined,
  ) => void;
  updateLimit: (params: UpdateMinMaxType) => void;
  loading: boolean;
  showEventTimeline?: boolean;
  eventList?: {
    basicData: EventSchedulingBasicInfo[];
    relatedData: EventSchedulingRelatedInfo[];
  };
  onOpenRelatedEventDrawer?: (event: EventSchedulingBasicInfo) => void; // 打开时间轴事件抽屉 #12280
}

const defaultStartTime = dayjs().format('YYYY-MM-DD 00:00:00');
const defaultEndTime = dayjs().format('YYYY-MM-DD 23:59:59');

const ObjectChartContentNew = (props: Props) => {
  const {
    loading,
    axisData,
    darkMode,
    formData,
    height = '250px',
    showToolbox,
    showSettingMark,
    defaultShowAsLineType,
    schemeType,
    handleMarkEvent,
    handleWarnEvent,
    handleEditLimit,
    onOpenRelatedEventDrawer,
    updatePositionLine,
    updateAxisData,
    updateLimit,
    showEventTimeline,
    eventList,
  } = props;

  const {
    timeStep,
    timeRange,
    warn,
    mark,
    warnLine,
    showSummary,
    compareType,
  } = formData;

  const objectChartTableRef = useRef<{
    onScrollToRow: (rowKey: string | number | Date) => void;
  }>(undefined);
  const chartRef = useRef<{
    getChartInstance: () => echarts.ECharts | undefined;
  }>(undefined);

  const timeRangeRef = useRef<string[]>(undefined);

  const handleMarkClick = (markInfo: MarkInfoItem) => {
    const scadaConfig = [
      {
        otype: markInfo.otype,
        oname: markInfo.oname,
        vprop: markInfo.vprop,
        ptype: markInfo.ptype,
        pname: markInfo.pname,
      },
    ];
    handleMarkEvent?.(
      markInfo,
      scadaConfig as ScadaMarkConfig[],
      updateAxisData,
    );
  };

  const updateMarkTimeRange = (timestampRange: number[] | undefined) => {
    let startTime = dayjs().valueOf();
    let endTime = dayjs().valueOf();
    if (timestampRange) {
      const [startTimestamp, endTimestamp] = timestampRange;
      startTime = startTimestamp;
      endTime = endTimestamp;
    }
    const range = [
      dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
      dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
    ];
    timeRangeRef.current = range;
  };

  const events: Partial<EChartsEvents> = {
    onZRClick: (_params, _chart, option) => {
      if (option?.xAxis) {
        const xAxis = Array.isArray(option.xAxis)
          ? option.xAxis
          : [option.xAxis];
        if (xAxis[0].axisPointer?.value) {
          objectChartTableRef.current?.onScrollToRow(
            xAxis[0].axisPointer.value,
          );
          updatePositionLine({
            x: dayjs(xAxis[0].axisPointer.value).format('YYYY-MM-DD HH:mm:ss'),
            y: '',
          });
        }
      }
    },
    onClick: (params) => {
      const { componentType, seriesName } = params;
      if (componentType === 'markArea') {
        const { params: markInfo } = params.data as unknown as {
          params: (MarkInfoItem | WarnInfoItem) & { markType: 'mark' | 'warn' };
        };

        if (markInfo.markType === 'mark')
          handleMarkClick(markInfo as MarkInfoItem);
        if (markInfo.markType === 'warn')
          handleWarnEvent?.(markInfo as WarnInfoItem);
      }

      if (
        seriesName === 'timelineBasicEvent' ||
        seriesName === 'timelineRelatedEvent'
      ) {
        onOpenRelatedEventDrawer?.((params.data as any)?.events?.[0]);
      }
    },
    onBrushEnd: (params) => {
      const { areas } = params;
      updateMarkTimeRange(areas[0]?.coordRange);
    },
  };

  const memoTimeRange = useMemo(() => {
    // 如果是多日环比， 轴应该和成一天而不是根据timeRange
    if (compareType?.type === ChartCompareType.chainBase) {
      const firstDateRange = compareType.dateRanges[0] ?? timeRange;
      return [
        firstDateRange?.[0]?.format('YYYY-MM-DD HH:mm:ss') ?? defaultStartTime,
        firstDateRange?.[1]?.format('YYYY-MM-DD HH:mm:ss') ?? defaultEndTime,
      ];
    }
    // 支持范围选择在时间，但是轴还是按天
    return [
      timeRange?.[0].format('YYYY-MM-DD HH:mm:ss') ?? defaultStartTime,
      timeRange?.[1].format('YYYY-MM-DD HH:mm:ss') ?? defaultEndTime,
    ];
  }, [timeRange, compareType]);

  const [startTime, endTime] = memoTimeRange;

  const getTimeDataChartInstance = (): JSX.Element => (
    <TimeDataChartNew
      ref={chartRef}
      loading={loading}
      startTime={startTime}
      endTime={endTime}
      xAxisType="timely"
      height={height}
      darkMode={darkMode}
      showToolbox={showToolbox}
      events={events}
      groupName={ChartGroup.timeChart}
      axisData={axisData}
      showWarnMark={warn}
      showMark={mark}
      showPreWarnLine={warnLine}
      defaultShowAsLineType={defaultShowAsLineType}
      schemeType={schemeType}
      showEventTimeline={showEventTimeline}
      eventList={eventList}
    />
  );

  const handleClickTable = (rowInfo: TableData, index: number | undefined) => {
    const chartInstance = chartRef.current?.getChartInstance();
    const option = chartInstance?.getOption();
    const value = dayjs(rowInfo.time).valueOf();
    const start = dayjs(startTime).valueOf();
    const end = dayjs(endTime).valueOf();

    if (option && typeof index === 'number') {
      const dataZoom = option.dataZoom as EChartsOption['dataZoom'];

      let zoomViewStart = Array.isArray(dataZoom)
        ? (dataZoom[0].startValue as number)
        : (dataZoom?.startValue as number);
      let zoomViewEnd = Array.isArray(dataZoom)
        ? (dataZoom[0].endValue as number)
        : (dataZoom?.endValue as number);
      const diff = zoomViewEnd - zoomViewStart;

      if (value < zoomViewStart) {
        const offset = value - start > diff / 2 ? diff / 2 : value - start;
        zoomViewStart = value - offset;
        zoomViewEnd = value - offset + diff;
      }
      if (value > zoomViewEnd) {
        const offset = end - value > diff / 2 ? diff / 2 : end - value;
        zoomViewEnd = value + offset;
        zoomViewStart = value + offset - diff;
      }
      chartInstance?.dispatchAction({
        type: 'dataZoom',
        startValue: zoomViewStart,
        endValue: zoomViewEnd,
      });
    }

    // 随机获取一个id: otype@oname@vprop
    const id = Object.keys(rowInfo).filter((key) => key !== 'time')[0];
    if (id) {
      updatePositionLine(
        {
          x: rowInfo.time,
          y: rowInfo[id],
        },
        id,
      );
    }
  };

  const handleClickMarkEvent = (
    time: string,
    scadaMarkConfig: ScadaMarkConfig[],
  ) => {
    const dateTime = dayjs().format('YYYY-MM-DD HH:mm');
    let markTime: string[] = time === '' ? [dateTime, dateTime] : [time, time];
    if (timeRangeRef.current) {
      markTime = timeRangeRef.current;
    }
    handleMarkEvent?.(
      {
        startTime: dayjs(markTime[0]).format('YYYY-MM-DD HH:mm'),
        endTime: dayjs(markTime[1]).format('YYYY-MM-DD HH:mm'),
      },
      scadaMarkConfig,
      updateAxisData,
    );
    timeRangeRef.current = undefined;
  };

  const onEditLimit = (otype: string, oname: string, vprop: string) => {
    handleEditLimit?.(otype, oname, vprop, updateLimit);
  };

  return (
    <ObjectChartContentWrapperNew>
      <div className="chart">{getTimeDataChartInstance()}</div>
      <div className="table">
        <ObjectChartTableNew
          showSummary={!!showSummary}
          axisData={axisData ?? []}
          ref={objectChartTableRef}
          onClickRow={handleClickTable}
          handleClickMarkEvent={handleClickMarkEvent}
          handleEditLimit={onEditLimit}
          timeStep={timeStep ?? TimeStepType.oneMinute}
          startTime={startTime}
          endTime={endTime}
          showSettingMark={showSettingMark}
        />
      </div>
    </ObjectChartContentWrapperNew>
  );
};

export default ObjectChartContentNew;
