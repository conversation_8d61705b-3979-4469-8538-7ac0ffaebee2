/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import Database from '@waterdesk/data/database';
import { getMatchedChartProperty } from '@waterdesk/data/object-chart';
import { IObjectItem, makeObjectId } from '@waterdesk/data/object-item';
import { ObjectChartProperty } from '@waterdesk/data/property/property-info';
import { SchemeTypeData } from '@waterdesk/data/scheme-config';
import {
  convertTimeData,
  GroupTimeData,
  ObjectTimeDataSeries,
  TimeData,
  TimeDataSeries,
} from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { WarnInfoList } from '@waterdesk/data/warn';
import dayjs, { Dayjs } from 'dayjs';
import * as echarts from 'echarts';
import { JSX, useEffect, useState } from 'react';
import { MultiDateValue } from 'src/components/multi-date-picker';
import DailyDataChart from '../daily-data-chart';
import TimeDataChart from '../time-data-chart';
import { CustomCompareTypeValue } from './object-chart-content-new';

export enum ChartGroup {
  timeChart = 'timeChart',
  dailyChart = 'dailyChart',
}
export enum TimeRangeType {
  oneDay = '1day',
  threeDays = '3days',
  sevenDays = '7days',
  oneMonth = '1months',
  threeMonths = '3months',
  oneYear = '1year',
  customize = 'timeRangeCustomize',
}

export enum DailyRangeType {
  oneMonth = '1month',
  threeMonths = '3months',
  customize = 'dailyRangeCustomize',
}

export interface ObjectChartFormValues {
  warn?: boolean;
  warnLine?: boolean;
  envelopLine?: boolean;
  chainBase?: boolean;
  timeRange?: [Dayjs, Dayjs];
  yMin?: number;
  yMax?: number;
  timeRangeType?: string;
  compareType?: CustomCompareTypeValue;
  timeStep?: number;
  indicators?: string | string[];
  dateMultiSelect?: MultiDateValue;
  mark?: boolean;
  rightAxis?: string | string[];
  leftAxis?: string | string[];
  showEventTimeline?: boolean;
}

export interface TableDataConfig {
  startDate: string;
  endDate: string;
  unitFormat?: UnitFormat;
  objectTimeDataSeries: Array<ObjectTimeDataSeries>;
  minSeries: Array<TimeData>;
  maxSeries: Array<TimeData>;
  chainBase?: boolean;
  multiChainBase?: MultiDateValue;
  height?: number | string;
}

export function getTimeRangeByType(
  timeRangeType: string,
  currentDate: string = dayjs().format('YYYY-MM-DD'),
): [Dayjs, Dayjs] {
  switch (timeRangeType) {
    case TimeRangeType.threeDays:
      return [dayjs(currentDate).add(-2, 'day'), dayjs(currentDate)];
    case TimeRangeType.sevenDays:
      return [dayjs(currentDate).add(-6, 'day'), dayjs(currentDate)];
    case TimeRangeType.oneYear:
      return [dayjs(currentDate).add(-1, 'year'), dayjs(currentDate)];
    case TimeRangeType.customize:
      return [dayjs(currentDate).add(-1, 'month'), dayjs(currentDate)];
    case TimeRangeType.oneMonth:
    case DailyRangeType.oneMonth:
      return [dayjs(currentDate).add(-1, 'month'), dayjs(currentDate)];
    case TimeRangeType.threeMonths:
      return [dayjs(currentDate).add(-3, 'month'), dayjs(currentDate)];
    case DailyRangeType.customize:
      return [dayjs(currentDate).add(-6, 'month'), dayjs(currentDate)];
    case TimeRangeType.oneDay:
    default:
      return [dayjs(currentDate), dayjs(currentDate)];
  }
}

interface Props {
  chartCode: string | undefined;
  selectedObjects: Array<IObjectItem>;
  indicatorType: string | undefined;
  indicatorName: string | string[] | undefined;
  vprop: string | undefined;
  forecast?: boolean;
  isDailyChart: boolean;
  darkMode?: boolean;
  formData: Partial<ObjectChartFormValues>;
  database: Database;
  height?: string;
  setDisplayTable?: (
    tableConfig: TableDataConfig,
    indicatorType: string,
  ) => void;
  isConnect?: boolean;
  setChartGroup?: () => void;
  getObjectTimeValues: (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string,
    startDate: string,
    endDate: string,
    includeMinMax?: boolean,
    includeCorrelatedProps?: string[],
    timeStep?: number,
    multiDates?: string[],
  ) => Promise<Map<string, GroupTimeData>>;
  getWarnList: (
    startDate: Dayjs,
    endDate: Dayjs,
    otype: string,
    oname: string,
    vprop: 'SDVAL',
  ) => Promise<WarnInfoList>;
  markList?: MarkInfoList;
  selectedModelLegend: boolean;
  showToolbox?: boolean;
  showDataTable?: boolean;
  actions?:
    | {
        [actionName: string]: (...args: any) => void;
      }
    | undefined;
  defaultShowAsLineType?: boolean;
  schemeType: SchemeTypeData;
}

function getScadaRealDataByTimeStep(
  vprop: string,
  timeStep: number | undefined,
) {
  if (typeof timeStep === 'number' && timeStep !== 60 && vprop === 'SDVAL') {
    return 'SDVAL_MATCH';
  }
  return vprop;
}

const defaultStartDate = dayjs().format('YYYY-MM-DD');
const defaultEndDate = dayjs().format('YYYY-MM-DD');

const ObjectChartContent = (props: Props) => {
  const {
    chartCode,
    selectedObjects,
    indicatorType,
    indicatorName: indicatorNames,
    vprop,
    isDailyChart,
    darkMode,
    formData,
    database,
    height = '250px',
    getWarnList,
    markList,
    getObjectTimeValues,
    setDisplayTable,
    isConnect,
    actions,
    selectedModelLegend,
    showToolbox,
    showDataTable,
    defaultShowAsLineType,
    schemeType,
  } = props;

  const {
    timeRange,
    yMin,
    yMax,
    chainBase,
    envelopLine,
    warn,
    timeStep,
    dateMultiSelect: multiDates,
    mark,
  } = formData;

  const indicatorName = Array.isArray(indicatorNames)
    ? indicatorNames[0]
    : indicatorNames;

  const startDate = timeRange?.[0].format('YYYY-MM-DD');
  const endDate = timeRange?.[1].format('YYYY-MM-DD');

  const [propertyTitle, setPropertyTitle] = useState<string>('');
  const [warnList, setWarnList] = useState<WarnInfoList>([]);
  const [objectDataSeries, setObjectDataSeries] = useState<
    Array<ObjectTimeDataSeries>
  >([]);

  const [minSeries, setMinSeries] = useState<Array<TimeData>>([]);
  const [maxSeries, setMaxSeries] = useState<Array<TimeData>>([]);
  const [unitFormat, setUnitFormat] = useState<UnitFormat | undefined>(
    undefined,
  );

  const includeMinMax = (
    chartProperty: ObjectChartProperty | undefined,
  ): boolean => {
    const chartEditor = chartProperty?.editors.find(
      (item) => item.type === 'chart',
    );
    return (
      chartEditor?.proCharts === 'TIME_MAX_MIN' &&
      chartEditor?.charts === 'scadaChart'
    );
  };

  const includeCorrelatedProps = (
    chartProperty: ObjectChartProperty | undefined,
  ): string[] => {
    const chartEditor = chartProperty?.editors.find(
      (item) => item.type === 'chart',
    );
    return chartEditor?.correlatedVprop ?? [];
  };

  const getDailyDataChartInstance = (): JSX.Element => (
    <DailyDataChart
      height={height}
      startDate={startDate ?? defaultStartDate}
      endDate={endDate ?? defaultEndDate}
      propertyName={propertyTitle}
      unitFormat={unitFormat}
      objectTimeDataSeries={objectDataSeries}
      yMinValue={yMin}
      yMaxValue={yMax}
      darkMode={darkMode}
      groupName={ChartGroup.dailyChart}
      showDataTable={showDataTable}
    />
  );

  const getTimeDataChartInstance = (): JSX.Element => (
    <TimeDataChart
      height={height}
      startDate={startDate ?? defaultStartDate}
      endDate={endDate ?? defaultEndDate}
      propertyName={propertyTitle}
      unitFormat={unitFormat}
      minSeries={minSeries}
      maxSeries={maxSeries}
      objectTimeDataSeries={objectDataSeries}
      solutionChart={false}
      chainBase={chainBase}
      chainMultiDates={multiDates}
      showRange={envelopLine}
      yMinValue={yMin}
      yMaxValue={yMax}
      darkMode={darkMode}
      warnList={warnList}
      markList={markList}
      actions={actions}
      showWarnMark={warn}
      showMark={mark}
      groupName={ChartGroup.timeChart}
      showToolbox={showToolbox}
      showDataTable={showDataTable}
      defaultShowAsLineType={defaultShowAsLineType}
      schemeType={schemeType}
    />
  );

  const singleSelectObjectChart = (
    selectedObject: IObjectItem,
    vprop: string | undefined,
  ) => {
    const currentChartProperty = getMatchedChartProperty(
      database,
      selectedObject,
      indicatorType,
      indicatorName,
      vprop,
    );

    if (currentChartProperty !== undefined) {
      const correlatedProps = includeCorrelatedProps(currentChartProperty);
      getObjectTimeValues(
        selectedObject,
        currentChartProperty.indicatorOType,
        currentChartProperty.indicatorOName,
        getScadaRealDataByTimeStep(currentChartProperty.vprop, timeStep),
        startDate ?? defaultStartDate,
        endDate ?? defaultEndDate,
        includeMinMax(currentChartProperty),
        correlatedProps,
        timeStep,
        multiDates?.dates,
      ).then((result) => {
        const timeDataSeries: Array<TimeDataSeries> = [];
        let minTimeData: Array<TimeData> = [];
        let maxTimeData: Array<TimeData> = [];
        result.forEach((item, key) => {
          if (key.startsWith('model')) {
            if (!selectedModelLegend) return;
            let { timeData } = item;
            const sdvalItem = result.get('SDVAL');
            if (sdvalItem) {
              timeData = convertTimeData(
                database,
                item.timeData,
                item.otype,
                item.vprop,
                sdvalItem.otype,
                sdvalItem.vprop,
              );
            }

            timeDataSeries.push({
              name: '模拟',
              type: 'line',
              timeData,
              legendSelected: selectedModelLegend,
              dataType: 'model',
            });
          } else if (key === 'ENVELOP_MAX') maxTimeData = item.timeData;
          else if (key === 'ENVELOP_MIN') minTimeData = item.timeData;
          else if (correlatedProps.includes(key)) {
            const propertyTitleUnit = database.getPropertyTitleUnit(
              selectedObject.otype,
              key,
            );
            timeDataSeries.push({
              name: propertyTitleUnit[0] ?? key,
              type: key.startsWith('SDVAL') ? 'scatter' : 'line',
              timeData: item.timeData,
              dataType: key.startsWith('SDVAL') ? 'scada' : undefined,
            });
          } else {
            let name = selectedObject.title;
            if (result.has('model')) name = '监测';
            timeDataSeries.push({
              name,
              type: key.startsWith('SDVAL') ? 'scatter' : 'line',
              timeData: item.timeData,
              dataType: key.startsWith('SDVAL') ? 'scada' : undefined,
            });
          }
        });
        const objectSeries: ObjectTimeDataSeries = {
          typeTitle: selectedObject.otype,
          objectTitle: selectedObject.title,
          objectName: selectedObject.oname,
          series: timeDataSeries,
        };
        setMinSeries(minTimeData);
        setMaxSeries(maxTimeData);
        setPropertyTitle(currentChartProperty.propertyTitle ?? '');
        setUnitFormat(currentChartProperty.unitFormat);
        setObjectDataSeries([objectSeries]);
        if (indicatorType) {
          const vpropType = makeObjectId(
            indicatorType,
            currentChartProperty.vprop,
          );
          setDisplayTable?.(
            {
              startDate: startDate ?? defaultStartDate,
              endDate: endDate ?? defaultEndDate,
              unitFormat: currentChartProperty.unitFormat,
              objectTimeDataSeries: [objectSeries],
              minSeries,
              maxSeries,
              chainBase,
              multiChainBase: multiDates,
              height,
            },
            vpropType,
          );
        }
      });
    } else {
      setPropertyTitle('');
      setUnitFormat(undefined);
      setMinSeries([]);
      setMaxSeries([]);
      setObjectDataSeries([]);
    }
  };

  const multipleSelectObjectChart = async (
    objects: Array<IObjectItem>,
    vprop: string | undefined,
  ) => {
    const firstObject = objects[0];
    const firstObjectChartProperty = getMatchedChartProperty(
      database,
      firstObject,
      indicatorType,
      indicatorName,
      vprop,
    );

    if (firstObjectChartProperty === undefined) return;

    const objectChartProperties: Array<[IObjectItem, ObjectChartProperty]> = [];
    objectChartProperties.push([firstObject, firstObjectChartProperty]);
    objects.forEach((item) => {
      if (item === firstObject) return;
      const chartProperty = getMatchedChartProperty(
        database,
        item,
        firstObjectChartProperty.indicatorOType,
        firstObjectChartProperty.indicatorOName,
        firstObjectChartProperty.vprop,
      );
      if (chartProperty !== undefined)
        objectChartProperties.push([item, chartProperty]);
    });

    Promise.all(
      objectChartProperties.map((item) =>
        getObjectTimeValues(
          item[0],
          item[1].indicatorOType,
          item[1].indicatorOName,
          getScadaRealDataByTimeStep(firstObjectChartProperty.vprop, timeStep),
          startDate ?? defaultStartDate,
          endDate ?? defaultEndDate,
          false,
          undefined,
          timeStep,
          multiDates?.dates,
        ),
      ),
    ).then((datas) => {
      const allObjectTimeDataSeries: Array<ObjectTimeDataSeries> = [];
      if (datas.length === objectChartProperties.length) {
        for (let i = 0; i < datas.length; i += 1) {
          const currentObject: IObjectItem = objectChartProperties[i][0];
          const timeDataSeries: Array<TimeDataSeries> = [];
          datas[i].forEach((item, key) => {
            if (key.startsWith('model')) {
              if (!selectedModelLegend) return;
              timeDataSeries.push({
                name: `${currentObject.title} - 模拟`,
                type: 'line',
                timeData: item.timeData,
                legendSelected: selectedModelLegend,
                dataType: 'model',
              });
            } else {
              let name = currentObject.title;
              if (datas[i].has('model')) name = `${currentObject.title} - 监测`;
              timeDataSeries.push({
                name,
                type: key.startsWith('SDVAL') ? 'scatter' : 'line',
                timeData: item.timeData,
                dataType: key.startsWith('SDVAL') ? 'scada' : undefined,
              });
            }
          });

          const objectSeries: ObjectTimeDataSeries = {
            typeTitle: currentObject.otype,
            objectTitle: currentObject.title,
            objectName: currentObject.oname,
            series: timeDataSeries,
          };

          allObjectTimeDataSeries.push(objectSeries);
        }

        if (indicatorType) {
          const vpropType = makeObjectId(
            indicatorType,
            firstObjectChartProperty.vprop,
          );
          setDisplayTable?.(
            {
              startDate: startDate ?? defaultStartDate,
              endDate: endDate ?? defaultEndDate,
              unitFormat: firstObjectChartProperty.unitFormat,
              objectTimeDataSeries: allObjectTimeDataSeries,
              minSeries,
              maxSeries,
              chainBase,
              multiChainBase: multiDates,
              height,
            },
            vpropType,
          );
        }

        setPropertyTitle(firstObjectChartProperty.propertyTitle ?? '');
        setUnitFormat(firstObjectChartProperty.unitFormat);
        setMinSeries([]);
        setMaxSeries([]);
        setObjectDataSeries(allObjectTimeDataSeries);
      }
    });
  };

  const connectChart = () => {
    echarts.connect(ChartGroup.timeChart);
    echarts.connect(ChartGroup.dailyChart);
  };

  const unConnectChart = () => {
    echarts.disconnect(ChartGroup.timeChart);
    echarts.disconnect(ChartGroup.dailyChart);
  };

  // 获取警告信息
  useEffect(() => {
    setWarnList([]);
    if (chartCode === undefined) return;
    if (selectedObjects.length > 0) {
      const currentChartProperty = getMatchedChartProperty(
        database,
        selectedObjects[0],
        indicatorType,
        indicatorName,
        vprop,
      );
      if (currentChartProperty) {
        const { indicatorOType, indicatorOName, vprop } = currentChartProperty;
        if (indicatorOType && indicatorOName && vprop === 'SDVAL') {
          getWarnList(
            dayjs(startDate),
            dayjs(endDate),
            indicatorOType,
            indicatorOName,
            vprop,
          ).then((data) => {
            setWarnList(data);
          });
        }
      }
    }
  }, [chartCode, startDate, endDate]);

  useEffect(() => {
    if (chartCode === undefined) return;

    if (
      (typeof startDate === 'undefined' || typeof endDate === 'undefined') &&
      typeof multiDates === 'undefined'
    )
      return;

    if (selectedObjects.length === 0) {
      setPropertyTitle('');
      setUnitFormat(undefined);
      setObjectDataSeries([]);
    } else if (selectedObjects.length === 1) {
      singleSelectObjectChart(selectedObjects[0], vprop);
    } else {
      // charts for multiple selected objects
      multipleSelectObjectChart(selectedObjects, vprop);
    }
  }, [
    chartCode,
    startDate,
    endDate,
    timeStep,
    multiDates,
    selectedModelLegend,
  ]);

  useEffect(() => {
    if (isConnect) connectChart();
    return () => {
      if (isConnect) unConnectChart();
    };
  }, [isConnect]);

  return isDailyChart
    ? getDailyDataChartInstance()
    : getTimeDataChartInstance();
};

export default ObjectChartContent;
