/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FormOutlined, TagsOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import {
  SaveChartMarkCallbackParams,
  ScadaMarkConfig,
} from '@waterdesk/data/chart-mark';
import {
  AxisDataItem,
  getDataSource,
  getSummary,
  TableData,
} from '@waterdesk/data/object-chart';
import { makeId, splitId } from '@waterdesk/data/object-item';
import { isBetweenRange } from '@waterdesk/data/utils';
import { Button, ConfigProvider, Space, Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import classNames from 'classnames';
import dayjs from 'dayjs';
import lodash from 'lodash';
import {
  CSSProperties,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { ObjectChartTableWrapper, TableHeaderCellWrapper } from './style';

export type Columns = ColumnType<TableData>[];

interface Props {
  axisData: AxisDataItem[];
  showSummary: boolean;
  showSettingMark?: boolean;
  timeStep: number;
  startTime: string;
  endTime: string;
  onClickRow: (rowInfo: TableData, index: number | undefined) => void;
  handleClickMarkEvent?: (
    time: string,
    scadaMarkConfig: ScadaMarkConfig[],
    callback?: (params: SaveChartMarkCallbackParams) => void,
  ) => void;
  handleEditLimit?: (otype: string, oname: string, vprop: string) => void;
}

const getColumns = (
  axisData: AxisDataItem[],
  showSettingMark: boolean,
  clickMarkEvent: () => void,
  clickEditLimit?: (otype: string, oname: string, vprop: string) => void,
): Columns => {
  const columns: Columns = [
    {
      title: '日期',
      dataIndex: 'time',
      width: 160,
      render: (value) => (
        <span className="default-color">
          {dayjs(value).format('YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
  ];

  //  string as dataIndex
  const limitationMap: Map<string, { min?: number; max?: number }> = new Map();

  axisData.forEach((axisDataItem, i) => {
    const { type, prop, unitFormat, data } = axisDataItem;
    data.forEach((dataItem, j) => {
      const { oname, maxLimitation, minLimitation } = dataItem;
      const unitSymbol = unitFormat?.unitSymbol
        ? ` (${unitFormat?.unitSymbol})`
        : '';
      // 通过 i、j解决oname重复问题
      const dataIndex = makeId(type, oname, prop, `${i}`, `${j}`);
      const foundSeries = dataItem?.series.find((i) => i.dataType === 'scada');
      if (foundSeries) {
        const { name } = foundSeries;
        limitationMap.set(dataIndex, {
          min: minLimitation,
          max: maxLimitation,
        });
        columns.push({
          title: `${name}${unitSymbol}`,
          dataIndex,
          width: 150,
          render: (value) => {
            if (typeof value !== 'number' && typeof value !== 'string')
              return <span className="empty-color">{value}</span>;
            return (
              <span
                className={classNames([
                  `default-color`,
                  typeof minLimitation === 'number' &&
                  typeof value === 'number' &&
                  value < minLimitation
                    ? 'min-color'
                    : '',
                  typeof maxLimitation === 'number' &&
                  typeof value === 'number' &&
                  value > maxLimitation
                    ? 'max-color'
                    : '',
                ])}
              >
                {value}
              </span>
            );
          },
        });
      }
    });
  });

  if (columns.length === 2) {
    const dataIndex = columns[1].dataIndex as string;
    const limitation = limitationMap.get(dataIndex);
    columns[1] = {
      ...columns[1],
      filters: [
        {
          text: '超限值',
          value: 'LIMIT',
        },
        {
          text: '正常值',
          value: 'OTHER',
        },
      ],
      onFilter: (value, record) => {
        if (value === 'OTHER') {
          if (typeof record[dataIndex] !== 'number') return false;
          return isBetweenRange(
            record[dataIndex] as number,
            limitation?.min,
            limitation?.max,
          );
        }
        if (value === 'LIMIT') {
          if (typeof record[dataIndex] !== 'number') return false;
          return !isBetweenRange(
            record[dataIndex] as number,
            limitation?.min,
            limitation?.max,
          );
        }
        return true;
      },
    };
  }

  return columns.map((column, index) => {
    if (index === 0) return column;
    const { title, dataIndex } = column;
    const [type, oname, prop] = splitId(dataIndex as string);
    return {
      ...column,
      title:
        index === columns.length - 1 && showSettingMark ? (
          <TableHeaderCellWrapper>
            <div
              className="title"
              title={title as string}
            >
              {title as string}
            </div>
            <Space>
              <Button
                onClick={() => clickEditLimit?.(type, oname, prop)}
                title="编辑上下限"
                style={{ width: '20px', padding: 0, margin: 0 }}
                type="link"
              >
                <FormOutlined />
              </Button>
              <Button
                onClick={() => clickMarkEvent()}
                style={{ width: '20px', padding: 0, margin: 0 }}
                type="link"
                title="增加标注"
              >
                <TagsOutlined />
              </Button>
            </Space>
          </TableHeaderCellWrapper>
        ) : (
          <TableHeaderCellWrapper>
            <div
              className="title"
              title={title as string}
            >
              {title as string}
            </div>
            <Space>
              <Button
                onClick={() => clickEditLimit?.(type, oname, prop)}
                title="编辑上下限"
                style={{ width: '20px', padding: 0, margin: 0 }}
                type="link"
              >
                <FormOutlined />
              </Button>
            </Space>
          </TableHeaderCellWrapper>
        ),
    };
  });
};

const getSummaryInfo = (
  data: TableData[],
  columns: Columns,
  style?: CSSProperties,
): React.ReactNode | undefined => {
  const { summaryMaxValue, summaryMinValue, summaryAverageValue } = getSummary(
    data,
    columns as Array<{
      title: string;
      dataIndex: string;
    }>,
  );

  if ([...data].length <= 0 || [...columns].length <= 0) return undefined;

  return (
    <Table.Summary fixed="top">
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>
          <span className="default-color">最大值</span>
        </Table.Summary.Cell>
        {summaryMaxValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            <span className="default-color">{value ?? ''}</span>
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>
          <span className="default-color">最小值</span>
        </Table.Summary.Cell>
        {summaryMinValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            <span className="default-color">{value ?? ''}</span>
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>
          {' '}
          <span className="default-color">平均值</span>
        </Table.Summary.Cell>
        {summaryAverageValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            <span className="default-color">{value ?? ''}</span>
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
    </Table.Summary>
  );
};

const ObjectChartTableNew = forwardRef((props: Props, ref) => {
  const {
    axisData,
    showSummary,
    timeStep,
    startTime,
    endTime,
    showSettingMark,
    onClickRow,
    handleClickMarkEvent,
    handleEditLimit,
  } = props;
  const { token } = useToken();
  const tableRef: Parameters<typeof Table>[0]['ref'] = useRef(null);

  const [activeRowKey, setActiveRowKey] = useState<React.Key>('');

  const clickMarkEvent = (data: TableData[], activeRowKey: React.Key) => {
    const record = data.find((item) => item.time === activeRowKey) ?? data[0];
    if (record) {
      const devices = Object.keys(record).filter((key) => key !== 'time');
      const scadaMarkConfig = devices.map((item) => {
        const [otype, oname, vprop] = splitId(item);
        return {
          otype,
          oname,
          vprop,
        };
      });
      handleClickMarkEvent?.(
        record?.time,
        scadaMarkConfig as ScadaMarkConfig[],
      );
    }
  };

  // fix #10626, 减少依赖项,优化性能。移除依赖项timeStep, startTime, endTime; 这三个更新时axisData会重新取数
  const dataSource = useMemo(
    () => getDataSource(axisData, timeStep, startTime, endTime),
    [axisData],
  );

  const columns = useMemo(
    () =>
      getColumns(
        axisData,
        !!showSettingMark,
        () => clickMarkEvent(dataSource, activeRowKey),
        handleEditLimit,
      ),
    [axisData, dataSource, activeRowKey],
  );
  const scrollY = useMemo(() => (showSummary ? 130 : 220), [showSummary]);

  const handleScroll = (rowKey: string | number | Date) => {
    const key = dayjs(rowKey).format('YYYY-MM-DD HH:mm:ss');
    if (tableRef.current) {
      tableRef.current.scrollTo({
        key,
      });
    }
    setActiveRowKey(key);
  };

  useImperativeHandle(ref, () => ({
    onScrollToRow: lodash.debounce(handleScroll, 200),
  }));

  const summaryInfo = useMemo(
    () =>
      getSummaryInfo(dataSource, columns, {
        fontWeight: 'bold',
        color: token.colorInfoText,
      }),
    [dataSource, columns, token],
  );

  const handleClickRow = (rowInfo: TableData, index: number | undefined) => {
    onClickRow(rowInfo, index);
    setActiveRowKey(rowInfo.time);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            rowHoverBg: 'none',
            cellPaddingBlockSM: 0,
            cellPaddingInlineSM: 0,
          },
        },
      }}
    >
      <ObjectChartTableWrapper
        ref={tableRef}
        rowClassName={(record, index) =>
          classNames([
            `${index % 2 ? 'even-row' : undefined}`,
            `${record.time === activeRowKey ? 'scroll-row' : undefined}`,
          ])
        }
        rowKey="time"
        size="small"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{
          x: 800,
          y: scrollY,
        }}
        summary={showSummary ? () => summaryInfo : undefined}
        onRow={(record, index) => ({
          onClick: () => handleClickRow(record, index),
        })}
        virtual
      />
    </ConfigProvider>
  );
});

ObjectChartTableNew.displayName = 'ObjectChartTableNew';

export default ObjectChartTableNew;
