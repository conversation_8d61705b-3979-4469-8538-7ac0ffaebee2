/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { splitObjectId } from '@waterdesk/data/object-item';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { Button, Form, FormInstance } from 'antd';
import { JSX, useMemo, useState } from 'react';
import { utils, writeFileXLSX } from 'xlsx';
import { ObjectChartFormValues, TableDataConfig } from './object-chart-content';
import ObjectChartTable, {
  Columns,
  optionToContent,
  ScadaTableData,
} from './object-chart-table';
import TabsWrapper from './style';

interface Props {
  selectedCharts: string[];
  tableDatas: {
    [key: string]: TableDataConfig;
  };
  form: FormInstance<ObjectChartFormValues>;
  database: Database;
}

export interface DownloadData {
  title: string;
  dataSource: ScadaTableData[];
  columns: Columns;
}

function generateDownloadData(columns: Columns, data: ScadaTableData[]) {
  const columnsInfo = columns;
  const columnMap: Map<string, string> = new Map();
  columnsInfo.forEach((item) => {
    const { dataIndex, title } = item;
    if (dataIndex) columnMap.set(dataIndex as string, title as string);
  });
  const transData = data.map((item) => {
    const newObject: ScadaTableData = {};
    Object.keys(item).forEach((key) => {
      const kName = columnMap.get(key);
      if (kName) newObject[kName] = item[key];
    });
    return newObject;
  });
  return transData;
}

function downloadFile(datas: DownloadData[], fileName: string): void {
  /* get state data and export to XLSX */
  const wb = utils.book_new();
  datas.forEach((item) => {
    const ws = utils.json_to_sheet(
      generateDownloadData(item.columns, item.dataSource),
    );
    utils.book_append_sheet(wb, ws, item.title);
  });

  writeFileXLSX(wb, `${fileName}.xlsx`);
}

export default function ObjectChartTableTabs(props: Props) {
  const { selectedCharts, tableDatas, form, database } = props;

  const [downloadDatas, setDownloadDatas] = useState<DownloadData[]>([]);
  const displayTable = Form.useWatch('dataTable', form);

  const getItems: {
    key: string;
    label: string;
    children: JSX.Element;
  }[] = useMemo(() => {
    const downLoadData: DownloadData[] = [];
    const items: {
      key: string;
      label: string;
      children: JSX.Element;
    }[] = [];
    selectedCharts.forEach((selectedKey) => {
      const data = tableDatas[selectedKey];
      const [otype, vprop] = splitObjectId(selectedKey);
      const [indicatorTypeTitle = '', indicatorTypeUnit] =
        database.getPropertyTitleUnit(otype, vprop) ?? selectedKey;
      const unit = indicatorTypeUnit
        ? getUnitFormat(indicatorTypeUnit)
        : undefined;
      const unitSymbol = unit ? unit.unitSymbol : '-';
      if (data) {
        const dataSource = optionToContent(
          data.startDate,
          data.endDate,
          data.objectTimeDataSeries,
          data.minSeries,
          data.maxSeries,
          data.unitFormat,
          data.chainBase,
        );
        downLoadData.push({
          title: indicatorTypeTitle,
          ...dataSource,
        });
        items.push({
          key: selectedKey,
          label: `${indicatorTypeTitle} ${
            unitSymbol ? `(${unitSymbol})` : '-'
          }`,
          children: (
            <ObjectChartTable
              key={selectedKey}
              startDate={data.startDate}
              endDate={data.endDate}
              unitFormat={data.unitFormat}
              objectTimeDataSeries={data.objectTimeDataSeries}
              minSeries={data.minSeries}
              maxSeries={data.maxSeries}
              chainBase={data.chainBase}
              multiChainBase={data.multiChainBase}
            />
          ),
        });
      }
    });
    setDownloadDatas(downLoadData);
    return items;
  }, [selectedCharts, displayTable]);

  return (
    <TabsWrapper
      items={getItems}
      size="small"
      tabBarExtraContent={
        <Button
          type="primary"
          onClick={() => downloadFile(downloadDatas, '设备曲线数据')}
        >
          导出
        </Button>
      }
    />
  );
}
