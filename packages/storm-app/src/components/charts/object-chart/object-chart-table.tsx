/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { ObjectTimeDataSeries, TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { formatNumber } from '@waterdesk/data/utils';
import { Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  GridComponentOption,
  LineSeriesOption,
  ScatterSeriesOption,
  TitleComponentOption,
  TooltipComponentOption,
} from 'echarts';
import { max, mean, min } from 'lodash';
import { CSSProperties, useMemo } from 'react';
import {
  DatePickerType,
  MultiDateValue,
} from 'src/components/multi-date-picker';
import { getColumnFilterRangeProps } from 'src/components/table/column';

export type ScadaTableData = {
  [key: string]: number | string | undefined;
};

export type Columns = ColumnType<ScadaTableData>[];

type ECOption = echarts.ComposeOption<
  | ScatterSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
>;

export function getDataSeries(
  objectTimeDataSeries: Array<ObjectTimeDataSeries>,
  minSeries: TimeData[],
  maxSeries: TimeData[],
): ECOption['series'] {
  const seriesData: ECOption['series'] = objectTimeDataSeries
    .flatMap((objectItem) => objectItem.series)
    .map((seriesItem) => ({
      name: seriesItem.name,
      data: seriesItem.timeData.map((dataItem) => [
        dataItem.time,
        dataItem.value,
      ]),
    }));

  if (minSeries.length > 0 && maxSeries.length === minSeries.length) {
    seriesData.push({
      name: '上限',
      data: maxSeries.map((item) => [item.time, item.value]),
    });

    seriesData.push({
      name: '下限',
      data: minSeries.map((item) => [item.time, item.value]),
    });
  }

  return seriesData;
}

const splitTimeDataByDay = (
  startDate: string,
  endDate: string,
  timeData: Array<TimeData>,
  multiChainBase?: MultiDateValue,
): Array<[string, Array<TimeData>]> => {
  const fakeDate = '2023-01-01';
  const isMonthChainBase = (): boolean =>
    multiChainBase?.dateType === DatePickerType.MONTH;

  const daySeries: Array<[string, Array<TimeData>]> = [];
  if (timeData.length === 0) return daySeries;

  const timeStepFormat = isMonthChainBase() ? 'YYYY-MM' : 'MM-DD';
  const today = dayjs(fakeDate).format(
    isMonthChainBase() ? 'YYYY-MM' : 'YYYY-MM-DD',
  );
  const mapDaySeries: Map<string, Array<TimeData>> = new Map();
  timeData.forEach((item) => {
    const { time, value } = item;
    const currentDate = dayjs(time).format(timeStepFormat);
    if (!mapDaySeries.has(currentDate)) mapDaySeries.set(currentDate, []);
    const fakeTime: string = `${today}${dayjs(time).format(
      isMonthChainBase() ? '-DD HH:mm:ss' : ' HH:mm:ss',
    )}`;
    mapDaySeries.get(currentDate)?.push({ time: fakeTime, value });
  });

  if (multiChainBase && multiChainBase.dates.length > 0) {
    for (let i = 0; i < multiChainBase.dates.sort().length; i += 1) {
      const currentDate = dayjs(multiChainBase.dates[i]).format(timeStepFormat);
      const dayTimeData = mapDaySeries.get(currentDate);
      if (dayTimeData) daySeries.push([currentDate, dayTimeData]);
    }
    return daySeries;
  }

  const firstDate = dayjs(startDate).startOf('day');
  const dayNum = dayjs(endDate).diff(firstDate, 'day') + 1;
  for (let i = 0; i < dayNum; i += 1) {
    const currentDate = firstDate.add(i, 'day').format(timeStepFormat);
    const dayTimeData = mapDaySeries.get(currentDate);
    if (dayTimeData) daySeries.push([currentDate, dayTimeData]);
  }

  return daySeries;
};

export function getDataSeriesByChainBase(
  startDate: string,
  endDate: string,
  objectTimeDataSeries: Array<ObjectTimeDataSeries>,
  multiChainBase?: MultiDateValue,
): ECOption['series'] {
  const seriesData: ECOption['series'] = [];
  objectTimeDataSeries.forEach((objectItem) => {
    objectItem.series.forEach((seriesItem) => {
      const daySeries = splitTimeDataByDay(
        startDate,
        endDate,
        seriesItem.timeData,
        multiChainBase,
      );
      daySeries.forEach((daySeriesItem) => {
        seriesData.push({
          name: `${daySeriesItem[0]} ${seriesItem.name}`,
          data: daySeriesItem[1].map((dataItem) => [
            dataItem.time,
            dataItem.value,
          ]),
        });
      });
    });
  });

  return seriesData;
}

export function getAllTimes(series: any): Array<string> {
  const allDates: Set<string> = new Set();
  series.forEach((seriesItem: any) => {
    seriesItem.data.forEach((dataItem: (string | number)[]) => {
      if (typeof dataItem[0] === 'string') allDates.add(dataItem[0]);
    });
  });
  return Array.from(allDates).sort();
}

export function optionToContent(
  startDate: string,
  endDate: string,
  objectTimeDataSeries: Array<ObjectTimeDataSeries>,
  minSeries: TimeData[],
  maxSeries: TimeData[],
  unitFormat: UnitFormat | undefined,
  chainBase?: boolean,
  multiChainBase?: MultiDateValue,
): {
  columns: Columns;
  dataSource: ScadaTableData[];
} {
  const series = chainBase
    ? getDataSeriesByChainBase(
        startDate,
        endDate,
        objectTimeDataSeries,
        multiChainBase,
      )
    : getDataSeries(objectTimeDataSeries, minSeries, maxSeries);
  if (!Array.isArray(series)) return { columns: [], dataSource: [] };
  const columns: Columns = [
    {
      title: '时间',
      key: 'time',
      dataIndex: 'time',
      ellipsis: true,
      width: 130,
      fixed: 'left',
    },
  ];
  const allDates = getAllTimes(series);
  series.forEach((seriesItem) => {
    columns.push({
      title: seriesItem.name,
      key: seriesItem.name,
      ellipsis: true,
      width: 130,
      dataIndex: seriesItem.name,
      ...getColumnFilterRangeProps(seriesItem.name as string),
    });
  });

  const seriesDatas: Array<Map<string, ScadaTableData>> = [];
  series.forEach((seriesItem) => {
    const { name, data: seriesData } = seriesItem;
    const data: Map<string, ScadaTableData> = new Map();
    (seriesData as [])?.forEach((dataItem: (string | number)[]) => {
      if (typeof dataItem[0] === 'string' && typeof dataItem[1] === 'number') {
        const dataValue: ScadaTableData = {
          [name as string]: unitFormat?.getValue(dataItem[1]),
        };
        data.set(dataItem[0], dataValue);
      }
    });
    seriesDatas.push(data);
  });

  const currentDatas: ScadaTableData[] = [];
  for (let i = 0; i < allDates.length; i += 1) {
    const currentDate = allDates[i];
    let time: string = '';
    if (chainBase) {
      time = dayjs(currentDate).format('HH:mm');
      if (multiChainBase?.dateType === DatePickerType.MONTH) {
        time = dayjs(currentDate).format('DD HH:mm');
      }
    } else {
      time = dayjs(currentDate).format('YYYY-MM-DD HH:mm');
    }
    let currentData: ScadaTableData = {
      time,
    };
    for (let j = 0; j < seriesDatas.length; j += 1) {
      const value = seriesDatas[j].get(currentDate);
      if (value !== undefined) {
        currentData = {
          ...currentData,
          ...value,
        };
      }
    }
    currentDatas.push(currentData);
  }
  return { columns, dataSource: currentDatas };
}

interface Props {
  tableData?: {
    columns: Columns;
    dataSource: ScadaTableData[];
  };
  startDate: string;
  endDate: string;
  unitFormat: UnitFormat | undefined;
  objectTimeDataSeries: ObjectTimeDataSeries[];
  minSeries: TimeData[];
  maxSeries: TimeData[];
  chainBase?: boolean;
  multiChainBase?: MultiDateValue;
}

const getSummaryInfo = (
  data: ScadaTableData[],
  columns: Columns,
  style?: CSSProperties,
): React.ReactNode | undefined => {
  const summaryMaxValue: Array<{
    value: number | string | undefined;
    dataIndex: number | string | undefined;
  }> = [];
  const summaryMinValue: typeof summaryMaxValue = [];
  const summaryAverageValue: typeof summaryMaxValue = [];

  [...columns].forEach((item) => {
    const { dataIndex } = item;
    if (dataIndex === 'time') return;
    const valueList = [...data].map((dataItem) =>
      item.dataIndex ? dataItem[dataIndex as string | number] : undefined,
    );
    const maxValue = max(valueList);
    const minValue = min(valueList);
    const averageValue = formatNumber(mean(valueList), 4);
    summaryMaxValue.push({
      value: maxValue,
      dataIndex: dataIndex as string | number,
    });
    summaryMinValue.push({
      value: minValue,
      dataIndex: dataIndex as string | number,
    });
    summaryAverageValue.push({
      value: averageValue,
      dataIndex: dataIndex as string | number,
    });
  });

  if ([...data].length <= 0 || [...columns].length <= 0) return undefined;

  return (
    <Table.Summary fixed>
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>最大值</Table.Summary.Cell>
        {summaryMaxValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            {value ?? ''}
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>最小值</Table.Summary.Cell>
        {summaryMinValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            {value ?? ''}
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
      <Table.Summary.Row style={style}>
        <Table.Summary.Cell index={0}>平均值</Table.Summary.Cell>
        {summaryAverageValue.map(({ value, dataIndex }, index) => (
          <Table.Summary.Cell
            index={index + 1}
            key={dataIndex}
          >
            {value ?? ''}
          </Table.Summary.Cell>
        ))}
      </Table.Summary.Row>
    </Table.Summary>
  );
};

export default function ObjectChartTable(props: Props) {
  const {
    startDate,
    endDate,
    unitFormat,
    objectTimeDataSeries,
    minSeries,
    maxSeries,
    chainBase,
    multiChainBase,
    tableData,
  } = props;

  const { token } = useToken();

  const { columns, dataSource } =
    tableData ||
    optionToContent(
      startDate,
      endDate,
      objectTimeDataSeries,
      minSeries,
      maxSeries,
      unitFormat,
      chainBase,
      multiChainBase,
    );

  const summaryInfo = useMemo(
    () =>
      getSummaryInfo(dataSource, columns, {
        fontWeight: 'bold',
        color: token.colorInfoText,
      }),
    [dataSource, columns, token],
  );

  return (
    <Table<ScadaTableData>
      rowKey="id"
      size="small"
      columns={columns}
      dataSource={dataSource ?? []}
      summary={() => summaryInfo}
    />
  );
}
