/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TableData } from '@waterdesk/data/object-chart';
import { Table, Tabs } from 'antd';
import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

const TabsWrapper = styled(Tabs)``;

export const ObjectChartContentWrapperNew = styled.div`
  height: 100%;
  display: grid;
  grid-template-rows: auto 270px;
  gap: 8px;
  .chart,
  .table {
    position: relative;
    overflow: hidden;
  }
`;

export const ObjectChartTableWrapper = styled(Table<TableData>)`
  .even-row {
    background-color: ${({ theme }) => theme.colorFillTertiary};
  }
  .scroll-row {
    background-color: ${({ theme }) => theme.colorPrimaryBgHover};
  }

  .default-color,
  .empty-color,
  .min-color,
  .max-color {
    height: 28px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .min-color,
  .max-color {
    width: 100%;
    background-color: ${({ theme }) => theme.colorError};
  }
  .empty-color {
    width: 100%;
    background-color: ${({ theme }) => theme.colorFill};
  }
`;

export const TableHeaderCellWrapper = styled.div`
  display: flex;
  align-items: center;
  .title {
    align-items: center;
    display: flex;
    flex: 1 1 auto;
    ${EllipsisText}
  }
`;

export default TabsWrapper;
