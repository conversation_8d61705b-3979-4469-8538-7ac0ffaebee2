/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import Database from '@waterdesk/data/database';
import {
  AxisDataItem,
  getMatchedChartProperty,
} from '@waterdesk/data/object-chart';
import {
  IObjectItem,
  makeId,
  makeObjectId,
  splitId,
} from '@waterdesk/data/object-item';
import {
  GroupTimeData,
  ObjectTimeDataSeriesNew,
} from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { useRequest } from 'ahooks';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';

type FormData = {
  timeRange: [Dayjs, Dayjs];
  timeStep?: number;
};
export interface AxisObject {
  selectedObject: IObjectItem;
  indicators?: {
    otype: string;
    oname: string;
    vprop: string;
    direction?: 'left' | 'right';
    formData: FormData;
  }[];
}

type RequestQueue = [
  Promise<Map<string, GroupTimeData>>,
  Promise<WarnInfoList>,
  Promise<MarkInfoList>,
];

export interface UpdateMinMaxType {
  otype: string;
  oname: string;
  vprop: string;
  min: number | undefined;
  max: number | undefined;
}

interface Options {
  // 是否获取包络线
  getEnvelop?: boolean;
  axisObjects: AxisObject[];
  // 模拟值图例是否选中
  selectedModelLegend?: boolean;
  // 显示模拟值
  showModel?: boolean;
  showEnvelop?: boolean;
  db: Database;
  getObjectTimeValues: (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string,
    startDate: string,
    endDate: string,
    includeMinMax?: boolean,
    includeCorrelatedProps?: string[],
    timeStep?: number,
  ) => Promise<Map<string, GroupTimeData>>;
  getWarnList: (
    startDate: Dayjs,
    endDate: Dayjs,
    otype: string,
    oname: string,
    vprop: string,
  ) => Promise<WarnInfoList>;
  getMarkList: (
    startDate: string,
    endDate: string,
    otype: string,
    oname: string,
    vprop: string,
  ) => Promise<MarkInfoList>;
}

function getScadaRealDataByTimeStep(
  vprop: string,
  timeStep: number | undefined,
) {
  if (typeof timeStep === 'number' && timeStep !== 60 && vprop === 'SDVAL') {
    return 'SDVAL_MATCH';
  }
  return vprop;
}

/**
 * 模拟值、包络线、警告、标注数据
 */
export default function useAxisData(options: Options) {
  const {
    axisObjects,
    db,
    selectedModelLegend,
    getObjectTimeValues,
    getWarnList,
    getMarkList,
    getEnvelop,
    showModel,
  } = options;

  const [axisData, setAxisData] = useState<AxisDataItem[]>([]);

  const getAxisData = async (): Promise<void> => {
    const axisData: AxisDataItem[] = [];
    const requestQueueGroup: Array<RequestQueue> = [];
    axisObjects.forEach((item) => {
      const { selectedObject, indicators = [] } = item;

      indicators.forEach((indicator) => {
        const {
          otype: indicatorOType,
          oname: indicatorOName,
          vprop: indicatorVProp,
          direction,
          formData,
        } = indicator;
        const { timeStep, timeRange } = formData;
        const startTime = timeRange[0].format('YYYY-MM-DD HH:mm:00');
        const endTime = timeRange[1].format('YYYY-MM-DD  HH:mm:00');
        const objectChartProperty = getMatchedChartProperty(
          db,
          selectedObject,
          indicatorOType,
          indicatorOName,
          indicatorVProp,
        );

        if (typeof objectChartProperty === 'undefined') return;

        const requestQueue: RequestQueue = [
          getObjectTimeValues(
            selectedObject,
            indicatorOType,
            indicatorOName,
            getScadaRealDataByTimeStep(objectChartProperty.vprop, timeStep),
            startTime,
            endTime,
            getEnvelop,
            undefined,
            timeStep,
          ),
          getWarnList(
            dayjs(startTime),
            dayjs(endTime),
            indicatorOType,
            indicatorOName,
            objectChartProperty.vprop,
          ),
          getMarkList(
            startTime,
            endTime,
            indicatorOType,
            indicatorOName,
            objectChartProperty.vprop,
          ),
        ];
        requestQueueGroup.push(requestQueue);
        const { vprop, propertyTitle, unitFormat } = objectChartProperty;
        const chartEditor = objectChartProperty.editors.find(
          (item) => item.type === 'chart',
        );
        const isDailyChart = chartEditor?.dateType === 'day';
        const title =
          db.getIndicator(indicatorOType, indicatorOName)?.title ??
          db.getPropertyTitleUnit(
            indicatorOType,
            objectChartProperty.vprop,
          )?.[0] ??
          indicatorOName;
        axisData.push({
          title,
          type: indicatorOType,
          prop: vprop,
          data: [],
          unitFormat,
          isDailyChart,
          propertyName: propertyTitle ?? '',
          direction: direction ?? 'left',
          yMinValue: undefined,
          yMaxValue: undefined,
        });
      });
    });
    const resGroup = await Promise.all(
      requestQueueGroup.map((subQueue) => Promise.all(subQueue)),
    );

    resGroup.forEach((res, index) => {
      let oname: string = '';
      const [timeDataMap, warnList, markList] = res;
      const objectTimeDataSeries: ObjectTimeDataSeriesNew[] = [];
      const series: ObjectTimeDataSeriesNew['series'] = [];
      timeDataMap.forEach((timeDataMapValue, key) => {
        const {
          oname: _oname,
          timeData,
          otype: _otype,
          vprop: _vprop,
        } = timeDataMapValue;
        const unitFormat = db.getUnitFormat(_otype, _vprop);

        if (key.startsWith('model')) {
          series.push({
            name: `${axisData[index].title} - 模拟`,
            type: 'line',
            timeData,
            legendSelected: selectedModelLegend,
            hidden: !showModel,
            dataType: 'model',
            unitFormat,
          });
        } else if (key.startsWith('SDVAL')) {
          oname = _oname;
          series.push({
            name: axisData[index].title,
            type: axisData[index].isDailyChart ? 'bar' : 'scatter',
            timeData,
            dataType: 'scada',
            legendSelected: true,
            unitFormat,
          });
        } else if (key.startsWith('ENVELOP_MAX')) {
          series.push({
            name: `${axisData[index].title} - 包络线(上)`,
            type: 'line',
            timeData,
            dataType: 'envelopMax',
            unitFormat,
            legendSelected: true,
          });
        } else if (key.startsWith('ENVELOP_MIN')) {
          series.push({
            name: `${axisData[index].title} - 包络线(下)`,
            type: 'line',
            timeData,
            dataType: 'envelopMin',
            unitFormat,
            legendSelected: true,
          });
        }
      });
      const indicator = db.getIndicator(axisData[index].type, oname);
      objectTimeDataSeries.push({
        series,
        warnList,
        markList,
        oname,
        maxLimitation: indicator?.maxLimitation,
        minLimitation: indicator?.minLimitation,
      });
      axisData[index].data = objectTimeDataSeries;
    });
    // string as axisId: makeObjectId(type, prop)
    const mergeAxisData: Map<string, AxisDataItem> = new Map();
    axisData.forEach((item) => {
      const axisId = makeObjectId(item.type, item.prop);
      const curAxisData = mergeAxisData.get(axisId);
      if (curAxisData) {
        const mergeData = [...curAxisData.data, ...item.data];
        mergeAxisData.set(axisId, { ...curAxisData, data: mergeData });
      } else {
        mergeAxisData.set(axisId, item);
      }
    });
    const data = Array.from(mergeAxisData.values());
    setAxisData(data);
  };

  const { loading } = useRequest(getAxisData, {
    debounceWait: 200,
    refreshDeps: [axisObjects],
  });

  const updateLegendSelected = () => {
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        series: dataItem.series.map((i) => {
          const newI = { ...i };
          if (i.dataType === 'model') {
            newI.legendSelected = selectedModelLegend;
          }
          return newI;
        }),
      })),
    }));
    setAxisData(newAxisData);
  };

  const updateShowModel = () => {
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        series: dataItem.series.map((i) => {
          const newI = { ...i };
          if (i.dataType === 'model') {
            newI.hidden = !showModel;
          }
          return newI;
        }),
      })),
    }));
    setAxisData(newAxisData);
  };

  const updatePositionLine = (
    positionLine: ObjectTimeDataSeriesNew['positionLine'],
    // id as otype@oname@vprop
    id?: string,
  ) => {
    const updateId =
      id ??
      makeId(
        axisData[0]?.type,
        axisData[0]?.data?.[0].oname,
        axisData[0]?.prop,
      );
    const [otype, oname, vprop] = splitId(updateId);
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        positionLine:
          axisDataItem.type === otype &&
          axisDataItem.prop === vprop &&
          dataItem.oname === oname
            ? positionLine
            : dataItem.positionLine,
      })),
    }));

    setAxisData(newAxisData);
  };

  useEffect(() => {
    updateLegendSelected();
  }, [selectedModelLegend]);

  useEffect(() => {
    updateShowModel();
  }, [showModel]);

  const updateAxisData = () => {
    getAxisData();
  };

  const updateLimit = (params: UpdateMinMaxType) => {
    const { otype, oname, vprop, min, max } = params;
    const newAxisData = axisData.map((axisDataItem) => {
      const newAxis = {
        ...axisDataItem,
      };
      if (newAxis.type === otype && newAxis.prop === vprop) {
        const axis: ObjectTimeDataSeriesNew | undefined = newAxis.data.find(
          (item) => item.oname === oname,
        );
        if (axis) {
          axis.minLimitation = min;
          axis.maxLimitation = max;
        }
      }
      return newAxis;
    });
    setAxisData(newAxisData);
  };

  return { axisData, loading, updateAxisData, updatePositionLine, updateLimit };
}
