/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import Database from '@waterdesk/data/database';
import {
  AxisDataItem,
  ChartCompareType,
  convertChainBaseData,
  getMatchedChartProperty,
} from '@waterdesk/data/object-chart';
import {
  IObjectItem,
  makeId,
  makeObjectId,
  splitId,
} from '@waterdesk/data/object-item';
import {
  GroupTimeData,
  ObjectTimeDataSeriesNew,
} from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { useRequest } from 'ahooks';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { ObjectChartFormValues } from './object-chart-content-new';

export interface AxisObject {
  selectedObject: IObjectItem;
  indicators?: {
    otype: string;
    oname: string;
    vprop: string;
    direction?: 'left' | 'right';
  }[];
}

type RequestQueue = [
  Promise<Map<string, GroupTimeData>>,
  Promise<WarnInfoList>,
  Promise<MarkInfoList>,
];

export interface UpdateMinMaxType {
  otype: string;
  oname: string;
  vprop: string;
  min: number | undefined;
  max: number | undefined;
}

interface Options {
  getEnvelop?: boolean;
  axisObjects: AxisObject[];
  // 模拟值图例是否选中
  selectedModelLegend?: boolean;
  // 显示模拟值
  showModel?: boolean;
  showEnvelop?: boolean;
  db: Database;
  formData: Partial<ObjectChartFormValues>;
  getObjectTimeValues: (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string,
    startDate: string,
    endDate: string,
    includeMinMax?: boolean,
    includeCorrelatedProps?: string[],
    timeStep?: number,
    multiDates?: string[],
  ) => Promise<Map<string, GroupTimeData>>;
  getWarnList: (
    startDate: Dayjs,
    endDate: Dayjs,
    otype: string,
    oname: string,
    vprop: string,
  ) => Promise<WarnInfoList>;
  getMarkList: (
    startDate: string,
    endDate: string,
    otype: string,
    oname: string,
    vprop: string,
  ) => Promise<MarkInfoList>;
}

function getScadaRealDataByTimeStep(
  vprop: string,
  timeStep: number | undefined,
) {
  if (typeof timeStep === 'number' && timeStep !== 60 && vprop === 'SDVAL') {
    return 'SDVAL_MATCH';
  }
  return vprop;
}

const defaultStartDate = dayjs().format('YYYY-MM-DD');
const defaultEndDate = dayjs().format('YYYY-MM-DD');

export default function useChainAxisData(options: Options) {
  const {
    axisObjects,
    db,
    formData,
    selectedModelLegend,
    getObjectTimeValues,
    getWarnList,
    getMarkList,
    getEnvelop,
    showModel,
  } = options;

  const [axisData, setAxisData] = useState<AxisDataItem[]>([]);
  const { timeStep, timeRange, compareType } = formData;

  const getCompareLineTitle = (
    title: string,
    time: [Dayjs, Dayjs] | undefined,
    index: number,
    type: ChartCompareType | undefined,
  ): string => {
    let formatTime = '';
    // title如果有日期范围，则同一天合并显示，跨天显示范围
    if (time)
      formatTime = time[0].isSame(time[1], 'day')
        ? `${time[0].format('YYYY/MM/DD')}`
        : `${time[0].format('YYYY/MM/DD')}-${time[1].format('YYYY/MM/DD')}`;

    // 多日对比title第一个title+日期， 剩下的为日期
    if (type === ChartCompareType.chainBase) {
      if (index === 0)
        return `${title}${title && formatTime ? '-' : ''}${formatTime}`;
      return formatTime;
    }
    // 同比、环比、自定义则第一个为title,后面为日期
    if (index !== 0) return formatTime;

    return title;
  };

  const getAxisData = async (): Promise<void> => {
    const axisData: AxisDataItem[] = [];
    const requestQueueGroup: Array<RequestQueue> = [];

    const dateRanges = compareType?.dateRanges ?? [];
    const type = compareType?.type;

    const validRanges: [Dayjs, Dayjs][] = dateRanges.filter(
      (dateRange) => dateRange?.[0] && dateRange?.[1],
    ) as [Dayjs, Dayjs][];

    // timeRange为时间范围, validRanges为对比分析的时间范围
    // 多日环比是将时间范围拆分为每一天一个range,形成对比分析的时间范围，因此存在包含关系
    const timeRanges =
      type === ChartCompareType.chainBase
        ? validRanges
        : [timeRange, ...validRanges];
    timeRanges.forEach((time, index) => {
      const startTime = time?.[0].format('YYYY-MM-DD HH:mm');
      const endTime = time?.[1].format('YYYY-MM-DD HH:mm');
      axisObjects.forEach((item) => {
        const { selectedObject, indicators = [] } = item;
        indicators.forEach((indicator) => {
          const {
            otype: indicatorOType,
            oname: indicatorOName,
            vprop: indicatorVProp,
            direction,
          } = indicator;
          const objectChartProperty = getMatchedChartProperty(
            db,
            selectedObject,
            indicatorOType,
            indicatorOName,
            indicatorVProp,
          );
          if (typeof objectChartProperty === 'undefined') return;

          const requestQueue: RequestQueue = [
            getObjectTimeValues(
              selectedObject,
              indicatorOType,
              indicatorOName,
              getScadaRealDataByTimeStep(objectChartProperty.vprop, timeStep),
              startTime ?? defaultStartDate,
              endTime ?? defaultEndDate,
              getEnvelop,
              undefined,
              timeStep,
            ),
            getWarnList(
              dayjs(startTime),
              dayjs(endTime),
              indicatorOType,
              indicatorOName,
              objectChartProperty.vprop,
            ),
            getMarkList(
              startTime ?? defaultStartDate,
              endTime ?? defaultEndDate,
              indicatorOType,
              indicatorOName,
              objectChartProperty.vprop,
            ),
          ];
          requestQueueGroup.push(requestQueue);
          const { vprop, propertyTitle, unitFormat } = objectChartProperty;
          const chartEditor = objectChartProperty.editors.find(
            (item) => item.type === 'chart',
          );
          const isDailyChart = chartEditor?.dateType === 'day';
          const title =
            db.getIndicator(indicatorOType, indicatorOName)?.title ??
            db.getPropertyTitleUnit(
              indicatorOType,
              objectChartProperty.vprop,
            )?.[0] ??
            indicatorOName;
          axisData.push({
            title: getCompareLineTitle(title, time, index, type),
            type: indicatorOType,
            prop: vprop,
            data: [],
            unitFormat,
            isDailyChart,
            propertyName: propertyTitle ?? '',
            direction: direction ?? 'left',
            yMinValue: undefined,
            yMaxValue: undefined,
          });
        });
      });
    });
    const resGroup = await Promise.all(
      requestQueueGroup.map((subQueue) => Promise.all(subQueue)),
    );

    resGroup.forEach((res, index) => {
      let oname: string = '';
      const [timeDataMap, warnList, markList] = res;
      const objectTimeDataSeries: ObjectTimeDataSeriesNew[] = [];
      const series: ObjectTimeDataSeriesNew['series'] = [];
      timeDataMap.forEach((timeDataMapValue, key) => {
        const {
          oname: _oname,
          timeData,
          otype: _otype,
          vprop: _vprop,
        } = timeDataMapValue;
        const unitFormat = db.getUnitFormat(_otype, _vprop);
        const convertTimeData = convertChainBaseData(
          timeRange?.[0] ?? defaultStartDate,
          timeData,
        );

        if (key.startsWith('model')) {
          series.push({
            name: `${axisData[index].title} - 模拟`,
            type: 'line',
            timeData: convertTimeData,
            legendSelected: selectedModelLegend,
            dataType: 'model',
            hidden: !showModel,
            unitFormat,
          });
        } else if (key.startsWith('SDVAL')) {
          oname = _oname;
          series.push({
            name: axisData[index].title,
            type: axisData[index].isDailyChart ? 'bar' : 'scatter',
            timeData: convertTimeData,
            dataType: 'scada',
            legendSelected: true,
            unitFormat,
          });
        } else if (key.startsWith('ENVELOP_MAX')) {
          series.push({
            name: `${axisData[index].title} - 包络线(上)`,
            type: 'line',
            timeData: convertTimeData,
            dataType: 'envelopMax',
            unitFormat,
            legendSelected: true,
          });
        } else if (key.startsWith('ENVELOP_MIN')) {
          series.push({
            name: `${axisData[index].title} - 包络线(下)`,
            type: 'line',
            timeData: convertTimeData,
            dataType: 'envelopMin',
            unitFormat,
            legendSelected: true,
          });
        }
      });
      const indicator = db.getIndicator(axisData[index].type, oname);
      objectTimeDataSeries.push({
        series,
        warnList,
        markList,
        oname,
        maxLimitation: indicator?.maxLimitation,
        minLimitation: indicator?.minLimitation,
      });
      axisData[index].data = objectTimeDataSeries;
    });
    // string as axisId: makeObjectId(type, prop)
    const mergeAxisData: Map<string, AxisDataItem> = new Map();
    axisData.forEach((item) => {
      const axisId = makeObjectId(item.type, item.prop);
      const curAxisData = mergeAxisData.get(axisId);
      if (curAxisData) {
        const mergeData = [...curAxisData.data, ...item.data];
        mergeAxisData.set(axisId, { ...curAxisData, data: mergeData });
      } else {
        mergeAxisData.set(axisId, item);
      }
    });
    const data = Array.from(mergeAxisData.values());
    setAxisData(data);
  };

  const { loading } = useRequest(getAxisData, {
    debounceWait: 200,
    refreshDeps: [axisObjects, timeStep, timeRange, compareType],
  });

  const updateLegendSelected = () => {
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        series: dataItem.series.map((i) => {
          const newI = { ...i };
          if (i.dataType === 'model') {
            newI.legendSelected = selectedModelLegend;
          }
          return newI;
        }),
      })),
    }));
    setAxisData(newAxisData);
  };

  const updateShowModel = () => {
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        series: dataItem.series.map((i) => {
          const newI = { ...i };
          if (i.dataType === 'model') {
            newI.hidden = !showModel;
          }
          return newI;
        }),
      })),
    }));
    setAxisData(newAxisData);
  };

  const updatePositionLine = (
    positionLine: ObjectTimeDataSeriesNew['positionLine'],
    // id as otype@oname@vprop
    id?: string,
  ) => {
    const updateId =
      id ??
      makeId(
        axisData[0]?.type,
        axisData[0]?.data?.[0].oname,
        axisData[0]?.prop,
      );
    const [otype, oname, vprop] = splitId(updateId);
    const newAxisData = axisData.map((axisDataItem) => ({
      ...axisDataItem,
      data: axisDataItem.data.map((dataItem) => ({
        ...dataItem,
        positionLine:
          axisDataItem.type === otype &&
          axisDataItem.prop === vprop &&
          dataItem.oname === oname
            ? positionLine
            : dataItem.positionLine,
      })),
    }));

    setAxisData(newAxisData);
  };

  useEffect(() => {
    updateLegendSelected();
  }, [selectedModelLegend]);

  useEffect(() => {
    updateShowModel();
  }, [showModel]);

  const updateAxisData = () => {
    getAxisData();
  };

  const updateLimit = (params: UpdateMinMaxType) => {
    const { otype, oname, vprop, min, max } = params;
    const newAxisData = axisData.map((axisDataItem) => {
      const newAxis = {
        ...axisDataItem,
      };
      if (newAxis.type === otype && newAxis.prop === vprop) {
        const axis: ObjectTimeDataSeriesNew | undefined = newAxis.data.find(
          (item) => item.oname === oname,
        );
        if (axis) {
          axis.minLimitation = min;
          axis.maxLimitation = max;
        }
      }
      return newAxis;
    });
    setAxisData(newAxisData);
  };

  return { axisData, loading, updateAxisData, updatePositionLine, updateLimit };
}
