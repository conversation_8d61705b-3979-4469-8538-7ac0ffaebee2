/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PumpStateColor } from '@waterdesk/data/app-config';
import {
  PumpStatusData,
  PumpTimeData,
  timeDataToPumpStatus,
} from '@waterdesk/data/pump-status';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import PumpStatusChartWrapper from './style';

interface Props {
  pumpTimeData: PumpTimeData[];
  pumpStateColor: PumpStateColor;
  days?: number;
  startDate?: string;
  minHeight?: string;
}

export default function PumpStatusChart(props: Props) {
  const { pumpTimeData, pumpStateColor, days, startDate, minHeight } = props;
  const [sortedPumpTimeData, setSortedPumpTimeData] = useState<PumpTimeData[]>(
    [],
  );

  useEffect(() => {
    const sortedData = [...pumpTimeData].sort((a, b) => {
      const regex = /\d+/;
      const numberA = a.name.match(regex);
      const numberB = b.name.match(regex);

      if (numberA && numberB) {
        return parseInt(numberA[0], 10) - parseInt(numberB[0], 10);
      }
      return a.name.localeCompare(b.name);
    });

    setSortedPumpTimeData(sortedData);
  }, [pumpTimeData, days, startDate]);

  const convertToTime = (timeIndex: number): string => {
    const hours: number = Math.floor(timeIndex / 60);
    const minutes: number = timeIndex - 60 * hours;
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;
  };

  const getTooltipContent = (status: PumpStatusData): string => {
    const startTime = convertToTime(status.startTimeIndex);
    const endTime = convertToTime(status.endTimeIndex);
    if (dayjs === undefined || startDate === undefined)
      return `${startTime}~${endTime}  ${status.value > 0 ? '开' : '关'}`;

    const startTimeText = dayjs(startDate)
      .add(status.startTimeIndex, 'minute')
      .format('MM-DD HH:mm');
    const endTimeText = dayjs(startDate)
      .add(status.endTimeIndex, 'minute')
      .format('MM-DD HH:mm');
    return `${startTimeText}~${endTimeText}  ${status.value > 0 ? '开' : '关'}`;
  };

  const getStatusColor = (
    variable: boolean,
    statusData: PumpStatusData,
  ): string => {
    if (statusData.value === 0) return pumpStateColor.closed;
    return variable ? pumpStateColor.variable : pumpStateColor.fixed;
  };

  const getTimeLabels = () => {
    if (days === undefined || days === 1) {
      const hourLabels = Array.from({ length: 7 }, (_, i) => i * 4);
      return hourLabels.map((hour, index) => (
        <div
          key={hour}
          className="tick"
          style={{ left: `calc(100% / 6 * ${index})` }}
        >
          <span className="hour">{`${hour}:00`}</span>
        </div>
      ));
    }
    if (days === 2) {
      const hourLabels = Array.from({ length: 9 }, (_, i) => i * 6);
      return hourLabels.map((hour, index) => {
        let label = `${hour}:00`;
        if (hour % 24 === 0) {
          label = `${dayjs(startDate)
            .add(hour / 24, 'day')
            .format('MM/DD')}`;
        }
        return (
          <div
            key={hour}
            className="tick"
            style={{ left: `calc(100% / 8 * ${index})` }}
          >
            <span className="hour">{label}</span>
          </div>
        );
      });
    }
    if (days <= 6) {
      const halfDayLabels = Array.from({ length: days * 2 + 1 }, (_, i) => i);
      return halfDayLabels.map((halfDay, index) => (
        <div
          key={halfDay}
          className="tick"
          style={{ left: `calc(100% / ${days * 2} * ${index})` }}
        >
          <span className="hour">
            {halfDay % 2 === 0
              ? `${dayjs(startDate)
                  .add(Math.floor(halfDay / 2), 'day')
                  .format('MM/DD')}`
              : '12:00'}
          </span>
        </div>
      ));
    }

    const dayLabels = Array.from({ length: days + 1 }, (_, i) => i);
    return dayLabels.map((day, index) => (
      <div
        key={day}
        className="tick"
        style={{ left: `calc(100% / ${days} * ${index})` }}
      >
        <span className="hour">{`${dayjs(startDate)
          .add(day, 'day')
          .format('MM/DD')}`}</span>
      </div>
    ));
  };

  const getPumpStatusLine = (pump: PumpTimeData) => {
    const pumpStatusData: PumpStatusData[] = timeDataToPumpStatus(
      pump.timeData,
      pump.variable,
    );

    return pumpStatusData.map((status, index) => {
      const width = `${(
        ((status.endTimeIndex - status.startTimeIndex) / (1440 * (days || 1))) *
          100
      ).toPrecision(3)}%`;

      return (
        // eslint-disable-next-line react/no-array-index-key
        <Tooltip
          key={index.toString()}
          title={getTooltipContent(status)}
        >
          <div
            // eslint-disable-next-line react/no-array-index-key
            key={index}
            style={{
              width,
              height: '20px',
              display: 'inline-block',
              backgroundColor: getStatusColor(pump.variable, status),
            }}
          />
        </Tooltip>
      );
    });
  };

  return (
    <PumpStatusChartWrapper style={{ minHeight }}>
      <div className="pump-name">
        <div style={{ marginRight: '12px' }}>水泵</div>
        {sortedPumpTimeData.map((pump) => (
          <div
            key={pump.name}
            title={pump.name}
          >
            {pump.name}
          </div>
        ))}
      </div>
      <div className="valueWrapper">
        <div className="timeline">{getTimeLabels()}</div>
        {sortedPumpTimeData.map((pump) => (
          <div
            className="value"
            key={pump.name}
          >
            {getPumpStatusLine(pump)}
          </div>
        ))}
      </div>
    </PumpStatusChartWrapper>
  );
}
