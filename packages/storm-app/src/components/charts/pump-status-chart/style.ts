/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

const PumpStatusChartWrapper = styled.div`
  width: 100%;
  padding: 0 10px;
  display: flex;
  gap: 4px;

  .pump-name {
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 1;
    max-width: 100px;
    ${EllipsisText}
    font-size: 14px;

    div {
      height: 24px;
      display: flex;
      align-items: center;
    }
  }

  .valueWrapper {
    display: flex;
    flex: 1 0 auto;
    flex-direction: column;
    .value {
      height: 24px;
      display: flex;
      align-items: center;
    }
  }

  .timeline {
    display: flex;
    align-items: center;
    padding: 0;
    height: 24px;
    bottom: 3px;
    border-bottom: 1px solid gray;
    position: relative;
  }

  .tick {
    width: 1px;
    height: 5px;
    background: gray;
    position: absolute;
    padding-bottom: 5px;
    bottom: 0px;
    text-align: left;
    font-size: 12px;
  }

  .hour {
    position: absolute;
    top: -17px;
    left: 0;
    transform: translateX(-50%);
  }

  .tick:first-child {
    border-left: none;
  }

  .tick:last-child {
    border-right: none;
  }
`;

export default PumpStatusChartWrapper;
