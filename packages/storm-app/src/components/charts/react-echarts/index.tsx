/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { ECElementEvent, ECharts, ElementEvent } from 'echarts';
import { getInstanceByDom, init } from 'echarts';
import type { CSSProperties, JSX } from 'react';
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import HocReactEcharts from 'src/utils/hoc-react-echarts';
import { DefaultTheme } from 'styled-components';

export type ZRenderOn<
  BasicOption extends {} = ReturnType<ECharts['getOption']>,
> = (e: ElementEvent, chart: ECharts, option?: BasicOption) => void;

export type RenderOn<
  BasicOption extends {} = ReturnType<ECharts['getOption']>,
> = (e: ECElementEvent, chart: ECharts, option?: BasicOption) => void;
export interface EChartsEvents {
  onZRClick?: ZRenderOn;
  onZRMouseMove?: ZRenderOn;
  onClick?: RenderOn;
  onMouseMove?: RenderOn;
  onBrushEnd?: RenderOn;
}

export interface ReactEChartsProps {
  option: any;
  style?: CSSProperties;
  loading?: boolean;
  theme?: 'light' | 'dark';
  groupName?: string;
  /** @deprecated actions will deprecated, please use events instead of it */
  actions?: { [actionName: string]: (...args: any) => void };
  events?: EChartsEvents;
  themeToken?: DefaultTheme;
}

function bindOption(
  action: ZRenderOn | RenderOn,
  e: ElementEvent | ECElementEvent,
  chart: ECharts,
): void {
  const pointInPixel = [e.offsetX, e.offsetY];
  let option: ReturnType<ECharts['getOption']> | undefined;
  if (chart.containPixel({ seriesIndex: 0 }, pointInPixel)) {
    option = chart.getOption();
  }
  (action as ZRenderOn)(e as ElementEvent, chart, option);
}

const ReactECharts = React.forwardRef(
  (
    {
      option,
      style,
      loading,
      theme,
      groupName,
      actions,
      events,
      themeToken,
    }: ReactEChartsProps,
    ref,
  ): JSX.Element => {
    const chartRef = useRef<HTMLDivElement>(null);

    const registerActions = (chart: ECharts, actions: any) => {
      const actionKeys = Object.keys(actions);
      actionKeys.forEach((key) => {
        if (key === 'zrmousemove') {
          chart?.getZr().on('mousemove', (e: any) => actions[key](e, chart));
        }
        if (key === 'zrClick') {
          chart?.getZr().on('click', (e) => actions[key](e, chart));
          chart?.on('click', actions[key]);
        } else {
          chart?.off(key);
          chart?.on(key, actions[key]);
        }
      });
    };

    const registerEvent = (chart: ECharts, events: EChartsEvents) => {
      const { onZRClick, onZRMouseMove, onClick, onMouseMove, onBrushEnd } =
        events ?? {};

      if (typeof onZRClick !== 'undefined') {
        chart
          .getZr()
          .on('click', (e: ElementEvent) => bindOption(onZRClick, e, chart));
      }

      if (typeof onZRMouseMove !== 'undefined') {
        chart
          .getZr()
          .on('mousemove', (e: ElementEvent) =>
            bindOption(onZRMouseMove, e, chart),
          );
      }

      if (typeof onClick !== 'undefined') {
        chart.on('click', (e: ECElementEvent) => bindOption(onClick, e, chart));
      }

      if (typeof onMouseMove !== 'undefined') {
        chart.on('mousemove', (e: ECElementEvent) =>
          bindOption(onMouseMove, e, chart),
        );
      }

      if (typeof onBrushEnd !== 'undefined') {
        chart.on('brushEnd', (e) =>
          (onBrushEnd as unknown as ZRenderOn)(
            e as ElementEvent,
            chart,
            option,
          ),
        );
      }
    };

    const getChartInstance = (): ECharts | undefined => {
      if (chartRef.current !== null) {
        const chart = getInstanceByDom(chartRef.current);
        return chart;
      }
      return undefined;
    };

    useImperativeHandle(
      ref,
      () => ({
        getChartInstance,
      }),
      [chartRef.current],
    );

    useEffect(() => {
      // Initialize chart
      let chart: ECharts | undefined;
      if (chartRef.current !== null) {
        chart = init(chartRef.current, theme);
        // Add chart resize listener
        new ResizeObserver(() => chart?.resize()).observe(chartRef.current);

        if (chart && actions) registerActions(chart, actions);

        if (chart && events) registerEvent(chart, events);
      }

      // Return cleanup function
      return () => {
        if (chartRef.current !== null) {
          chart?.dispose();
        }
      };
    }, [theme, actions, events]);

    useEffect(() => {
      // Update chart
      if (chartRef.current !== null) {
        const chart = getInstanceByDom(chartRef.current);
        chart?.setOption(
          { backgroundColor: themeToken?.colorBgBase ?? '#fff', ...option },
          { notMerge: true },
        );
        if (chart && groupName) chart.group = groupName;
      }
    }, [option, theme, themeToken]); // Whenever theme changes we need to add option and setting due to it being deleted in cleanup function

    useEffect(() => {
      // Update chart
      if (chartRef.current !== null) {
        const chart = getInstanceByDom(chartRef.current);
        if (loading) {
          chart?.showLoading();
        } else {
          chart?.hideLoading();
        }
      }
    }, [loading, theme]);

    return (
      <div
        ref={chartRef}
        style={{ width: '100%', height: '100px', ...style }}
      />
    );
  },
);

ReactECharts.displayName = 'ReactECharts';

export const WrappedReactECharts = HocReactEcharts(ReactECharts);
