/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  generateEventTimeSeries,
  generateRelatedEventTimeSeries,
} from '@waterdesk/data/chart';
import { MarkInfoList } from '@waterdesk/data/chart-mark';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from '@waterdesk/data/event-scheduling/related-info';
import {
  AxisDataItem,
  defaultToolboxNames,
  doubleValueFormatter,
  enumValueFormatter,
  getSeriesType,
  getYAxisMaxValue,
  getYAxisName,
  isYScale,
  ToolboxName,
  timeFormatter,
  XAxisType,
  xAxisLabelFormatter,
} from '@waterdesk/data/object-chart';
import {
  getSchemeTypeDataTitle,
  SchemeTypeData,
} from '@waterdesk/data/scheme-config';
import { isNotNullOrEmpty } from '@waterdesk/data/string';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { exportToExcel, isTimeBetween } from '@waterdesk/data/utils';
import { ChartWarnInfo } from '@waterdesk/data/warn';
import dayjs from 'dayjs';
import { BarSeriesOption, ECharts, EChartsOption } from 'echarts';
import {
  // 系列类型的定义后缀都为 SeriesOption
  LineChart,
  LineSeriesOption,
  ScatterChart,
  ScatterSeriesOption,
} from 'echarts/charts';
import {
  GridComponent,
  GridComponentOption,
  MarkLineComponent,
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import {
  CallbackDataParams,
  MarkLineOption,
  XAXisOption,
  YAXisOption,
} from 'echarts/types/dist/shared';
import {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { EChartsEvents, WrappedReactECharts } from '../react-echarts';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | ScatterSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
>;

type ECSeries = LineSeriesOption | ScatterSeriesOption | BarSeriesOption;
type ECMarkArea = ECSeries['markArea'];
type ECMarkAreaData = NonNullable<ECMarkArea>['data'];

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  TransformComponent,
  MarkLineComponent,
  ScatterChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

interface Props {
  loading?: boolean;
  startTime: string;
  endTime: string;
  xAxisType: XAxisType;
  axisData: AxisDataItem[];
  width?: string;
  height?: string;
  groupName?: string;
  showToolbox?: ToolboxName[];
  darkMode?: boolean;
  showMark?: boolean;
  showWarnMark?: boolean;
  showPreWarnLine?: boolean;
  events?: EChartsEvents;
  defaultShowAsLineType?: boolean;
  schemeType: SchemeTypeData;
  splitNumber?: number;
  showEventTimeline?: boolean;
  eventList?: {
    basicData: EventSchedulingBasicInfo[];
    relatedData: EventSchedulingRelatedInfo[];
  };
  colorOptions?: string[];
}

const defaultColorOptions = [
  '#1492cc',
  '#cb3f1c',
  '#0000ff',
  '#f5b533',
  '#008000',
  '#dc69aa',
  '#7eb00a',
  '#b6a2de',
  '#07a2a4',
  '#27727b',
];

const getTagMarkAreaData = (
  markList: MarkInfoList | undefined,
  schemeType: SchemeTypeData,
  showMark?: boolean,
): ECMarkAreaData => {
  if (!showMark || !markList?.length) return undefined;

  return markList.map((item) => {
    const { startTime, endTime, type } = item;
    return [
      {
        name: getSchemeTypeDataTitle(schemeType, type),
        silent: false,
        params: { ...item, markType: 'mark' },
        label: {
          fontFamily: startTime === endTime ? 'iconfont' : '',
          color: '#7728F5',
        },
        itemStyle: {
          color: 'rgba(119, 40, 245, 0.4)',
          borderColor: '#7728F5',
          borderWidth: 1,
        },
        xAxis: startTime,
      },
      {
        silent: false,
        params: item,
        xAxis: endTime,
      },
    ];
  });
};

const getWarnMarkAreaData = (
  darkMode: boolean,
  warnList?: Array<ChartWarnInfo & { highlightColor?: string }>,
  showMark?: boolean,
): ECMarkAreaData => {
  if (!showMark || !warnList?.length) return undefined;

  return warnList.map((warnInfo) => {
    const { startTime, endTime, secondTypeName, highlightColor } = warnInfo;
    const itemStyleColor = highlightColor ?? '#ffc3ba';
    return [
      {
        name: secondTypeName,
        itemStyle: {
          color: itemStyleColor,
          borderColor: itemStyleColor,
          borderWidth: 1,
        },
        label: {
          color: '#ff4640',
          padding: [5, 10],
          backgroundColor: darkMode ? '#000000' : '#ffffff',
        },
        params: { ...warnInfo, markType: 'warn' },
        emphasis: {
          disabled: false,
          itemStyle: {
            color: '#ff4640',
          },
        },
        xAxis: startTime,
      },
      {
        itemStyle: {
          color: itemStyleColor,
        },
        params: warnInfo,
        emphasis: {
          disabled: false,
          itemStyle: {
            color: '#ff4640',
          },
        },
        xAxis: endTime,
      },
    ];
  });
};

const createWarnLine = (
  value: number,
  labelPrefix: string,
): NonNullable<MarkLineOption['data']>[0] => ({
  yAxis: value,
  lineStyle: {
    color: '#ff0000',
  },
  symbol: 'none',
  emphasis: {
    disabled: true,
  },
  label: {
    position: 'insideEndTop',
    color: '#ff0000',
    textBorderWidth: 2,
    textBorderColor: '#fff',
    formatter: `${labelPrefix}: ${value}`,
  },
});

const getPreWarnLine = (
  maxValue: number | undefined,
  minValue: number | undefined,
  showPreWarnLine?: boolean,
): NonNullable<MarkLineOption['data']> => {
  if (!showPreWarnLine) return [];
  const data: NonNullable<MarkLineOption['data']> = [];

  if (typeof maxValue !== 'undefined')
    data.push(createWarnLine(maxValue, '上限'));

  if (typeof minValue !== 'undefined')
    data.push(createWarnLine(minValue, '下限'));

  return data;
};

const getPositionLine = (
  x: string | number,
): NonNullable<MarkLineOption['data']> => [
  {
    xAxis: x,
    lineStyle: {
      color: '#ff0000',
      type: 'solid',
      width: 2,
    },
    symbol: 'none',
    symbolSize: 0,
    emphasis: {
      disabled: true,
    },
    label: {
      color: '#ff0000',
    },
  },
];

const TimeDataChartNew = forwardRef(
  (
    {
      loading,
      startTime,
      endTime,
      xAxisType,
      axisData,
      width,
      height,
      darkMode,
      showWarnMark,
      groupName,
      showMark,
      showPreWarnLine,
      showToolbox = defaultToolboxNames,
      defaultShowAsLineType = false,
      schemeType,
      events,
      splitNumber = 8,
      showEventTimeline = false,
      eventList,
      colorOptions,
    }: Props,
    ref,
  ) => {
    const [showAsLineType, setShowAsLineType] = useState<boolean>(
      defaultShowAsLineType,
    );

    const chartInstanceRef = useRef<{
      getChartInstance: () => ECharts | undefined;
    }>(undefined);

    const getLegendSelected = (): { [index: string]: boolean } => {
      const legendSelected: { [index: string]: boolean } = {};
      axisData.forEach((axisItem) => {
        const { data } = axisItem;
        data.forEach((dataItem) => {
          dataItem.series.forEach((item) => {
            legendSelected[item.name] = !!item.legendSelected;
          });
        });
      });
      return legendSelected;
    };

    const getLegendData = (): string[] => {
      const legendData: string[] = [];

      axisData.forEach((axisItem) => {
        axisItem.data.forEach((dataItem) => {
          dataItem.series.forEach((item) => {
            if (
              item.dataType === 'scada' ||
              item.dataType === 'model' ||
              item.dataType === 'forecast'
            )
              legendData.push(item.name);
          });
        });
      });
      return legendData;
    };

    const timePointerFormatter = (
      value: string | number | Date,
      xAxisType: XAxisType,
    ): string => {
      const time = dayjs(value);

      switch (xAxisType) {
        case 'daily':
          return time.format('MM-DD');
        case 'singleDay':
          return time.format('H:mm');
        case 'timely':
        default:
          return time.format('MM-DD H:mm');
      }
    };

    const getSeriesEnvelop = (axisData: AxisDataItem[]): ECSeries[] => {
      const seriesData: ECSeries[] = [];
      axisData.forEach((axisItem) => {
        const { data, unitFormat } = axisItem;
        data.forEach((dataItem) => {
          const { series } = dataItem;
          series.forEach((item) => {
            const {
              name,
              dataType,
              timeData,
              hidden,
              unitFormat: dataUnitFormat,
            } = item;

            if (hidden) return;

            const convertTimeData = timeData.map((item) => [
              item.time,
              doubleValueFormatter(item.value, dataUnitFormat ?? unitFormat),
              dataUnitFormat?.unitName ?? (unitFormat?.unitName as string),
            ]);
            if (dataType === 'envelopMax') {
              seriesData.push({
                z: -1,
                name,
                type: 'line',
                data: convertTimeData,
                symbol: 'none',
                lineStyle: {
                  opacity: 0,
                },
                areaStyle: {
                  origin: 'start',
                  color: '#aaaaaa',
                  opacity: 0.4,
                },
                silent: true,
                animation: false,
                smooth: true,
                emphasis: { disabled: true },
                dimensions: ['x', 'y', 'unitFormat'],
              });
            }
            if (dataType === 'envelopMin') {
              seriesData.push({
                z: -1,
                name,
                type: 'line',
                data: convertTimeData,
                symbol: 'none',
                lineStyle: {
                  opacity: 0,
                },
                areaStyle: {
                  origin: 'start',
                  color: darkMode ? '#1F1F1F' : 'white',
                  opacity: 1,
                },
                silent: true,
                animation: false,
                smooth: true,
                emphasis: { disabled: true },
                dimensions: ['x', 'y', 'unitFormat'],
              });
            }
          });
        });
      });

      return seriesData;
    };

    const getDataSeries = (
      axisData: AxisDataItem[],
      yAxis?: ECOption['yAxis'],
    ): ECSeries[] => {
      const seriesData: ECSeries[] = [];
      const modelSeriesData: ECSeries[] = [];
      axisData.forEach((axisItem, axisIndex) => {
        const { data, unitFormat } = axisItem;
        data.forEach((dataItem) => {
          const {
            series,
            maxLimitation,
            minLimitation,
            warnList,
            markList,
            positionLine,
          } = dataItem;
          const preWarnLine = getPreWarnLine(
            maxLimitation,
            minLimitation,
            showPreWarnLine,
          );
          const warnMarkArea =
            getWarnMarkAreaData(!!darkMode, warnList, showWarnMark) ?? [];
          // 使用tag  和mark做区分
          const tagMarkArea =
            getTagMarkAreaData(markList, schemeType, showMark) ?? [];
          const positionXLine = positionLine
            ? getPositionLine(positionLine.x)
            : [];
          series.forEach((item) => {
            const {
              name,
              type,
              dataType,
              timeData,
              unitFormat: dataUnitFormat,
              hidden,
            } = item;
            if (
              dataType === 'model' ||
              dataType === 'scada' ||
              dataType === 'forecast'
            ) {
              let yAxisIndex = axisIndex;
              if (yAxis) {
                const y: number = (yAxis as []).findIndex(
                  (unit: { name: string | undefined }) =>
                    unit.name === unitFormat?.unitSymbol &&
                    isNotNullOrEmpty(unit.name) &&
                    isNotNullOrEmpty(unitFormat?.unitSymbol),
                );

                if (y > -1) yAxisIndex = y;
              }

              const convertTimeData = timeData.map((item) => [
                item.time,
                doubleValueFormatter(item.value, dataUnitFormat ?? unitFormat),
                dataUnitFormat?.unitName ?? (unitFormat?.unitName as string),
              ]);

              if (dataType === 'scada') {
                seriesData.push({
                  name,
                  type: getSeriesType(type, showAsLineType),
                  yAxisIndex,
                  emphasis: {
                    focus: 'series',
                  },
                  symbolSize: 3,
                  showSymbol: false,
                  sampling: 'lttb',
                  large: true,
                  data: convertTimeData,
                  lineStyle: {
                    width: 3,
                  },
                  markLine: {
                    symbol: 'none',
                    precision: 5,
                    data: [...preWarnLine, ...positionXLine],
                  },
                  markArea: {
                    data: [...warnMarkArea, ...tagMarkArea],
                  },
                  barMaxWidth: 60,
                  dimensions: ['x', 'y', 'unitFormat'],
                });
              } else if (
                (dataType === 'model' || dataType === 'forecast') &&
                !hidden
              ) {
                modelSeriesData.push({
                  name,
                  type: getSeriesType(type, showAsLineType),
                  yAxisIndex,
                  emphasis: {
                    focus: 'series',
                  },
                  symbolSize: 3,
                  showSymbol: true,
                  sampling: 'lttb',
                  large: true,
                  data: convertTimeData,
                  lineStyle: {
                    width: 3,
                  },
                  barMaxWidth: 60,
                  dimensions: ['x', 'y', 'unitFormat'],
                });
              }

              if (
                showEventTimeline &&
                Array.isArray(eventList?.basicData) &&
                (eventList?.basicData?.length ?? 0) > 0 &&
                seriesData &&
                !seriesData.some((i) => i.name === 'timelineBasicEvent')
              ) {
                seriesData.push({
                  name: 'timelineBasicEvent',
                  type: 'bar',
                  xAxisIndex: 1,
                  yAxisIndex: (yAxis as []).length - 1,
                  data: generateEventTimeSeries(
                    startTime,
                    endTime,
                    eventList?.basicData ?? [],
                  ),
                  barWidth: 3,
                  itemStyle: {
                    color: 'purple',
                  },
                });
              }

              if (
                showEventTimeline &&
                Array.isArray(eventList?.relatedData) &&
                (eventList?.relatedData?.length ?? 0) > 0 &&
                seriesData &&
                !seriesData.some((i) => i.name === 'timelineRelatedEvent')
              ) {
                seriesData.push({
                  name: 'timelineRelatedEvent',
                  type: 'bar',
                  xAxisIndex: 1,
                  yAxisIndex: (yAxis as []).length - 1,
                  data: generateRelatedEventTimeSeries(
                    startTime,
                    endTime,
                    eventList?.relatedData ?? [],
                  ),
                  barWidth: 3,
                  itemStyle: {
                    color: '#A97CDC',
                  },
                });
              }
            }
          });
        });
      });

      // 模拟值和包络线的series追加到scada后面，避免模拟值和包络线在切换显示/隐藏后 打乱了原有的线的颜色
      const envelopSeries = getSeriesEnvelop(axisData);
      [...modelSeriesData, ...envelopSeries].forEach((envelopSeries) => {
        seriesData.push(envelopSeries);
      });

      return seriesData;
    };

    const getSeries = (yAxis: ECOption['yAxis']): ECSeries[] => {
      const seriesData = getDataSeries(axisData, yAxis);
      return seriesData;
    };

    const getXAxisMinMax = (): { min: string; max: string } | undefined => {
      if (xAxisType === 'timely' || xAxisType === 'singleDay')
        return {
          min: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
          max: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
        };
      if (xAxisType === 'daily') {
        return {
          min: dayjs(startTime).startOf('day').format('YYYY-MM-DD'),
          max: dayjs(endTime).startOf('day').format('YYYY-MM-DD'),
        };
      }
      return undefined;
    };

    const getAllTimes = (series: any): Array<string> => {
      const allDates: Set<string> = new Set();
      series.forEach((seriesItem: any) => {
        seriesItem.data.forEach((dataItem: (string | number)[]) => {
          if (typeof dataItem[0] === 'string') allDates.add(dataItem[0]);
        });
      });
      return [...Array.from(allDates)].sort((a, b) => a.localeCompare(b));
    };

    const getTableSourceBySeries = (
      series: ECSeries[],
    ): {
      dataSource: Array<{
        [index: string]: number | string | undefined | null;
      }>;
      columns: Array<{ title: string; dataIndex: string }>;
    } => {
      const columns = [
        {
          title: '日期',
          dataIndex: 'time',
        },
      ];
      const dataSource: Array<{
        [index: string]: number | string | undefined | null;
      }> = [];
      const allDates = getAllTimes(series);
      const seriesData: Map<string, Map<string, number>> = new Map();
      series.forEach((seriesItem: any) => {
        const dataMap: Map<string, number> = new Map();
        columns.push({
          title: seriesItem.name,
          dataIndex: seriesItem.name,
        });
        seriesItem.data?.forEach((dataItem: (string | number)[]) => {
          if (
            typeof dataItem[0] === 'string' &&
            typeof dataItem[1] === 'number'
          ) {
            dataMap.set(
              dataItem[0],
              enumValueFormatter(
                dataItem[1],
                getUnitFormat(dataItem[2] as string),
              ) as unknown as number,
            );
          }
        });
        seriesData.set(seriesItem.name, dataMap);
      });
      allDates.forEach((time) => {
        const dataItem: { [index: string]: string | number | undefined } = {
          time: timeFormatter(time, xAxisType),
        };
        seriesData.forEach((valueMap, dataIndex) => {
          dataItem[dataIndex] = valueMap.get(time);
        });
        dataSource.push(dataItem);
      });
      return { columns, dataSource };
    };

    const optionToContent = (opt: any): any => {
      const { series } = opt;
      const { columns, dataSource } = getTableSourceBySeries(series);

      const seriesHeader = columns
        .map(
          (columnsItem) =>
            `<th style="position: sticky; top: 0;background-color: #fff;">${columnsItem.title}</th>`,
        )
        .join('');
      let table = `<table border="1" style="color:#333;margin-left:20px;border-collapse:collapse;font-size:14px;text-align:center;user-select:all"><tbody>${seriesHeader}</tr>`;

      dataSource.forEach((dataItem) => {
        let trContent = '<tr>';
        columns.forEach((columnsItem) => {
          trContent += `<td>${dataItem[columnsItem.dataIndex] ?? ''}</td>`;
        });
        trContent += '</tr>';
        table += trContent;
      });

      table += '</tbody></table>';

      return table;
    };

    const xAxis: ECOption['xAxis'] = useMemo(() => {
      const xAxisMinMax = getXAxisMinMax();

      const baseXAxisConfig: XAXisOption = {
        type: 'time',
        min: xAxisMinMax?.min,
        max: xAxisMinMax?.max,
        axisLabel: {
          formatter(value: any) {
            return xAxisLabelFormatter(value, xAxisType);
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        splitNumber,
        axisPointer: {
          label: {
            formatter(params: any) {
              return xAxisLabelFormatter(params.value as number, xAxisType);
            },
          },
        },
        minInterval: xAxisType === 'daily' ? 24 * 60 * 60 * 1000 : 0,
      };

      if (axisData.length > 0 && showEventTimeline) {
        return [
          baseXAxisConfig,
          {
            ...baseXAxisConfig,
            gridIndex: 1,
            axisLabel: { show: false },
          },
        ];
      }
      return { ...baseXAxisConfig };
    }, [axisData, showEventTimeline, splitNumber, xAxisType]);

    const yAxisAndGrid: { yAxis: ECOption['yAxis']; grid: ECOption['grid'] } =
      useMemo(() => {
        let countXLeft = 0;
        let countYRight = 0;
        const offsetStep = 90;
        const defaultYAxis: ECOption['yAxis'] = { type: 'value' };
        let yAxisValue: YAXisOption[] = [];
        axisData.forEach((item) => {
          const {
            direction,
            propertyName,
            unitFormat,
            yMaxValue,
            yMinValue,
            data,
          } = item;

          const name = yAxisValue.find(
            (y) => y.name === unitFormat?.unitSymbol,
          );
          if (name) return;

          const yScale = isYScale(
            data.map((item) => ({
              ...item,
              // 当前不显示的series的data不参与最大最小值的统计
              series: item.series.filter((f) => !(f.hidden === true)),
            })),
            unitFormat,
          );
          // 先计算offset 再累加count
          const offset =
            (direction === 'right' ? countYRight : countXLeft) * offsetStep;
          const showAxisLine = offset !== 0;
          if (direction === 'right') {
            countYRight += 1;
          } else {
            countXLeft += 1;
          }

          yAxisValue = [
            ...(yAxisValue || []),
            {
              position: direction ?? 'left',
              type: 'value',
              name: getYAxisName(propertyName, direction, unitFormat),
              axisTick: {
                show: true,
              },
              axisLine: {
                show: showAxisLine,
              },
              min: yMinValue,
              max: getYAxisMaxValue(yMaxValue, unitFormat),
              scale: yScale,
              offset,
              axisLabel: {
                formatter(value) {
                  // 数值型不做单位转换， 只有枚举型单位才转换
                  return enumValueFormatter(value, unitFormat) as string;
                },
              },
            },
          ];
        });
        const offsetLeft = offsetStep * (countXLeft - 1);
        const offsetRight = offsetStep * (countYRight - 1);

        const defaultGrid = showEventTimeline
          ? {
              left: offsetLeft > 0 ? `${20 + offsetLeft * 1.2}px` : '4%',
              right: offsetRight > 0 ? `${20 + offsetRight * 1.2}px` : '3%',
              bottom: showEventTimeline ? 50 : '10%',
              top: 60,
            }
          : {
              left: offsetLeft > 0 ? `${20 + offsetLeft}px` : '1%',
              right: offsetRight > 0 ? `${20 + offsetRight}px` : '1%',
              bottom: '1%',
              top: 60,
              containLabel: true,
            };

        const eventTimelineGrid = showEventTimeline
          ? {
              show: true,
              left: offsetLeft > 0 ? `${20 + offsetLeft * 1.2}px` : '4%',
              right: offsetRight > 0 ? `${20 + offsetRight * 1.2}px` : '3%',
              bottom: 0,
              height: 25,
            }
          : {};

        const eventTimelineYAxis: YAXisOption = showEventTimeline
          ? {
              nameLocation: 'middle',
              nameRotate: 0,
              gridIndex: 1,
              show: true,
              type: 'value',
              inverse: true,
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisPointer: {
                label: {
                  formatter(params: any) {
                    return params.value ? params.values : '无';
                  },
                },
              },
            }
          : {};

        return {
          yAxis: yAxisValue.length
            ? [...yAxisValue, eventTimelineYAxis]
            : defaultYAxis,
          grid: [defaultGrid, eventTimelineGrid],
        };
      }, [axisData, showEventTimeline]);

    const getYValue = (
      params: CallbackDataParams,
    ): CallbackDataParams['value'] | undefined => {
      let valueY: CallbackDataParams['value'] | undefined;
      const { value } = params;
      if (Array.isArray(value)) {
        valueY = value[1] as string;
      } else {
        valueY = value;
      }
      return valueY;
    };

    const getWarnTooltip = (valueX: string): string => {
      let warnHtmlStr = '';
      const warnList = axisData
        .map((a) => a.data.map((d) => d.warnList ?? []))
        .flat(2);
      const foundWarnList = warnList?.filter(({ startTime, endTime }) =>
        isTimeBetween([startTime, endTime], valueX),
      );

      let warnListString = '';
      foundWarnList?.forEach((item) => {
        const { secondTypeName, description, endStatus } = item;
        let desc = description;
        if (endStatus === 0) desc += '[持续中]';
        warnListString += `<div>类型:${secondTypeName}</div><div style="max-width: 240px; white-space: break-spaces;word-break: break-all;">${desc}</div>`;
      });
      if (warnListString !== '') {
        warnHtmlStr += '<div style="color: #ff4640">警告</dvi>';
        warnHtmlStr += warnListString;
      }
      return warnHtmlStr;
    };

    const getMarkTooltip = (valueX: string): string => {
      let markHtmlStr = '';
      const markList = axisData
        .map((a) => a.data.map((d) => d.markList ?? []))
        .flat(2);
      const foundMarkList = markList?.filter(({ startTime, endTime }) =>
        isTimeBetween([startTime, endTime], valueX),
      );
      let markListString = '';
      foundMarkList?.forEach((item) => {
        const { type, description } = item;
        markListString += `<div>类型:${getSchemeTypeDataTitle(
          schemeType,
          type,
        )}</div><div style="max-width: 240px; white-space: break-spaces;word-break: break-all;">${description}</div>`;
      });
      if (markListString !== '') {
        markHtmlStr += '<div style="color: #7728f5">标注</dvi>';
        markHtmlStr += markListString;
      }
      return markHtmlStr;
    };

    const getMergedEventTooltip = (valueX: string): string => {
      let eventHtmlStr = '';

      const foundBasicEventList = eventList?.basicData.filter(
        ({ eventStartTime }) => dayjs(eventStartTime).isSame(valueX, 'minute'),
      );

      const foundRelatedEventList = eventList?.relatedData.filter(
        ({ relatedTime }) => dayjs(relatedTime).isSame(valueX, 'minute'),
      );

      let eventListString = '';
      foundBasicEventList?.forEach((item) => {
        const { eventTitle, eventDescription = '' } = item;
        eventListString += `<div>${eventTitle}</div><div style="max-width: 240px; white-space: break-spaces; word-break: break-all;">${eventDescription}</div>`;
      });

      foundRelatedEventList?.forEach((item) => {
        const { eventName, relatedDescription = '' } = item;
        eventListString += `<div>${eventName}</div><div style="max-width: 240px; white-space: break-spaces; word-break: break-all;">${relatedDescription}</div>`;
      });

      if (eventListString !== '') {
        eventHtmlStr += eventListString;
      } else {
        eventHtmlStr += '暂无事件';
      }

      return eventHtmlStr;
    };

    const tooltipFormatter: TooltipComponentOption['formatter'] = (
      params,
    ): string => {
      let titleStr = '';
      let htmlStr = '';
      let warnStr = '';
      let markStr = '';
      let valueX: string | undefined;

      const paramsArr = Array.isArray(params) ? [...params] : [params];
      const chart = chartInstanceRef.current?.getChartInstance();

      // 根据value大小进行tooltip排序
      if (chart) {
        paramsArr.sort((a, b) => {
          const aValueY = chart.convertToPixel(
            { seriesId: a.seriesId },
            a.value as number[],
          )[1];
          const bValueY = chart.convertToPixel(
            { seriesId: b.seriesId },
            b.value as number[],
          )[1];
          return aValueY - bValueY;
        });
      }

      paramsArr.forEach((item) => {
        const { marker, seriesName, value } = item;
        if (Array.isArray(value)) {
          valueX = value[0] as string;
        } else {
          valueX = value as string;
        }

        const valueY = getYValue(item);
        let unitFormatName: string = '';

        if (Array.isArray(value) && value[2]) {
          unitFormatName = value[2] as string;
        }
        htmlStr += `<div>${
          (marker?.toString() ?? '') + (seriesName ?? '')
        } &nbsp;&nbsp;&nbsp;<span style="font-weight: 600">${enumValueFormatter(
          valueY as number,
          getUnitFormat(unitFormatName),
        )}<span></div>`;
      });

      titleStr = `<div>${
        typeof valueX === 'string'
          ? timePointerFormatter(valueX, xAxisType)
          : ''
      }</div>`;

      if (typeof valueX === 'string') {
        warnStr = getWarnTooltip(valueX);
        markStr = getMarkTooltip(valueX);

        if (showEventTimeline) {
          let eventTooltip = '';

          if (
            paramsArr.some(
              (param) => param.seriesName === 'timelineBasicEvent',
            ) ||
            paramsArr.some(
              (param) => param.seriesName === 'timelineRelatedEvent',
            )
          ) {
            eventTooltip = getMergedEventTooltip(valueX);
          }

          return titleStr + (eventTooltip || '暂无事件');
        }
      }

      return titleStr + htmlStr + warnStr + markStr;
    };

    const getColorOptions = (): string[] => {
      if (colorOptions) {
        if (colorOptions.length >= defaultColorOptions.length)
          return colorOptions;
        return [
          ...colorOptions,
          ...defaultColorOptions.slice(
            colorOptions.length,
            defaultColorOptions.length,
          ),
        ];
      }
      return defaultColorOptions;
    };

    const getOption = (): ECOption => {
      const option: ECOption = {
        animation: false,
        color: getColorOptions(),
        grid: yAxisAndGrid.grid,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
          },
          confine: true,
          formatter: tooltipFormatter,
          position: (pos, _params, _el, _elRect, size) => {
            let x = pos[0] + 20;
            let y = pos[1];
            const pointX = pos[0];
            const pointY = pos[1];
            const boxWidth = size.contentSize[0];
            const boxHeight = size.contentSize[1];
            if (pointX + boxWidth + 20 > size.viewSize[0])
              x = pointX - boxWidth;
            if (pointY + boxHeight + 40 > size.viewSize[1])
              y = pointY - boxHeight;
            if (y < 0) {
              if (boxHeight > size.viewSize[1]) y = 0;
              else y = 20;
            }
            return [x, y];
          },
        },
        legend: {
          type: 'scroll',
          data: getLegendData(),
          selected: getLegendSelected(),
          padding: [5, 370, 5, 5],
          xAxisIndex: [0, 1],
        },
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1],
            realtime: true,
            start: 0,
            end: 100,
          },
        ],
        xAxis,
        yAxis: yAxisAndGrid.yAxis,
        series: getSeries(yAxisAndGrid.yAxis),
        ...(showToolbox.includes('brush')
          ? {
              brush: {
                xAxisIndex: 'all',
                throttleType: 'debounce',
                throttleDelay: 300,
                outOfBrush: {
                  colorAlpha: 0.1,
                },
              },
            }
          : {}),
        toolbox: {
          show: showToolbox,
          feature: {
            ...(showToolbox.includes('brush')
              ? {
                  brush: {
                    type: ['lineX'],
                    title: { lineX: '框选' },
                  },
                }
              : {}),
            dataZoom: {
              show: showToolbox.includes('dataZoom'),
              yAxisIndex: 'none',
              title: {
                zoom: '区域缩放',
                back: '区域缩放还原',
              },
            },
            myChartMode: {
              show: showToolbox.includes('myChartMode'),
              title: showAsLineType ? '切换为散点图' : '切换为折线图',
              icon: showAsLineType
                ? 'M981.333333 960a21.333333 21.333333 0 0 1-21.333333 21.333333H64a21.333333 21.333333 0 0 1-21.333333-21.333333V64a21.333333 21.333333 0 0 1 42.666666 0v874.666667h874.666667a21.333333 21.333333 0 0 1 21.333333 21.333333zM234.666667 725.333333a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m298.666666-298.666666a64 64 0 1 0-64-64 64 64 0 0 0 64 64zM448 640a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m256-42.666667a64 64 0 1 0-64-64 64 64 0 0 0 64 64zM277.333333 341.333333a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m490.666667 341.333334a64 64 0 1 0 64 64 64 64 0 0 0-64-64z m106.666667-298.666667a64 64 0 1 0-64-64 64 64 0 0 0 64 64z'
                : 'M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4',
              onclick() {
                setShowAsLineType(!showAsLineType);
              },
            },
            dataView: {
              show: showToolbox.includes('dataView'),
              readOnly: false,
              title: '数据表格',
              lang: ['', '关闭', '刷新'],
              optionToContent(opt: any) {
                return optionToContent(opt);
              },
            },
            restore: {
              show: showToolbox.includes('restore'),
              title: '还原',
            },
            myDownload: {
              show: showToolbox.includes('myDownload'),
              title: '下载',
              icon: 'path://M544.256 605.184l244.224-244.224a31.744 31.744 0 0 1 45.056 45.056l-295.424 295.424a36.864 36.864 0 0 1-51.2 0L190.464 406.528a31.744 31.744 0 1 1 45.056-45.056l244.224 244.224V111.104a32.256 32.256 0 1 1 64 0zM153.6 902.656a32.256 32.256 0 0 1 0-64h716.8a32.256 32.256 0 0 1 0 64z',
              onclick(global: any) {
                if (!global?.option?.series?.length) return;
                const { columns, dataSource } = getTableSourceBySeries(
                  global?.option?.series,
                );
                const fileName = dayjs().format('YYYY_MM_DD_HHmmss');
                exportToExcel(dataSource, columns, fileName);
              },
            },
          },
        },
      };

      // memo data zoom
      const chartInstance = chartInstanceRef.current?.getChartInstance();

      const dataZoom = chartInstance?.getOption()
        ?.dataZoom as EChartsOption['dataZoom'];
      if (dataZoom) option.dataZoom = dataZoom;

      return option;
    };

    useImperativeHandle(ref, () => ({
      getChartInstance: () => chartInstanceRef.current?.getChartInstance(),
    }));

    return (
      <WrappedReactECharts
        loading={loading}
        ref={chartInstanceRef}
        groupName={groupName}
        option={getOption()}
        events={events}
        style={{
          height: `${height ?? 'calc(100% - 24px)'}`,
          width: `${width ?? '100%'}`,
        }}
      />
    );
  },
);

TimeDataChartNew.displayName = 'TimeDataChartNew';

export default TimeDataChartNew;
