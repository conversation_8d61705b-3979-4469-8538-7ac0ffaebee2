/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MarkInfoList } from '@waterdesk/data/chart-mark';
import {
  getSchemeTypeDataTitle,
  SchemeTypeData,
} from '@waterdesk/data/scheme-config';
import {
  getRangeValues,
  ObjectTimeDataSeries,
  TimeData,
} from '@waterdesk/data/time-data';
import { DoubleUnitFormat, UnitFormat } from '@waterdesk/data/unit-system';
import { getMinMax } from '@waterdesk/data/utils';
import { WarnInfoItem, WarnInfoList } from '@waterdesk/data/warn';
import dayjs from 'dayjs';
import { ECharts } from 'echarts';
import {
  // 系列类型的定义后缀都为 SeriesOption
  LineChart,
  LineSeriesOption,
  ScatterChart,
  ScatterSeriesOption,
} from 'echarts/charts';
import {
  GridComponent,
  GridComponentOption,
  MarkLineComponent,
  MarkPointComponentOption,
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import lodash from 'lodash';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  DatePickerType,
  MultiDateValue,
} from 'src/components/multi-date-picker';
import { downloadExcel } from 'src/utils/download';
import { WrappedReactECharts } from '../react-echarts';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
type ECOption = echarts.ComposeOption<
  | ScatterSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
>;

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  TransformComponent,
  MarkLineComponent,
  ScatterChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

interface Props {
  startDate: string;
  endDate: string;
  propertyName: string;
  unitFormat: UnitFormat | undefined;
  minSeries: Array<TimeData>;
  maxSeries: Array<TimeData>;
  objectTimeDataSeries: Array<ObjectTimeDataSeries>;
  yMinValue: number | undefined;
  yMaxValue: number | undefined;
  solutionChart?: boolean;
  chainBase?: boolean;
  chainMultiDates?: MultiDateValue;
  showRange?: boolean;
  width?: string;
  height?: string;
  darkMode?: boolean;
  warnList?: WarnInfoList;
  markList?: MarkInfoList;
  actions?:
    | {
        [actionName: string]: (...args: any) => void;
      }
    | undefined;
  showWarnMark?: boolean;
  showMark?: boolean;
  groupName?: string;
  showToolbox?: boolean;
  showDataTable?: boolean;
  defaultShowAsLineType?: boolean;
  showLegendWhenSingle?: boolean;
  title?: string;
  schemeType: SchemeTypeData;
}

/** @deprecated
 *  TimeDataChart will deprecated, please use TimeDataChartNew instead of it。
 */
const TimeDataChart = forwardRef(
  (
    {
      startDate,
      endDate,
      propertyName,
      unitFormat,
      minSeries,
      maxSeries,
      objectTimeDataSeries,
      yMinValue,
      yMaxValue,
      solutionChart,
      chainBase,
      chainMultiDates,
      showRange,
      width,
      height,
      darkMode,
      warnList,
      markList,
      actions,
      showWarnMark,
      groupName,
      showMark,
      showToolbox = true,
      showDataTable = true,
      defaultShowAsLineType = false,
      showLegendWhenSingle = false,
      title,
      schemeType,
    }: Props,
    ref,
  ) => {
    const [showAsLineType, setShowAsLineType] = useState<boolean>(
      defaultShowAsLineType,
    );

    const fakeDate = '2023-01-01';

    const chartInstanceRef = useRef<{
      getChartInstance: () => ECharts | undefined;
    }>(undefined);

    const isMonthChainBase = (): boolean =>
      chainMultiDates?.dateType === DatePickerType.MONTH;

    const hasScatterType = (
      objectTimeDataSeries: Array<ObjectTimeDataSeries>,
    ): boolean => {
      for (let i = 0; i < objectTimeDataSeries.length; i += 1) {
        const objectItem = objectTimeDataSeries[i];
        for (let j = 0; j < objectItem.series.length; j += 1) {
          if (objectItem.series[j].type === 'scatter') return true;
        }
      }

      return false;
    };

    const getSeriesType = (type: string | undefined): 'line' | 'scatter' => {
      if (showAsLineType) return 'line';
      if (type === 'scatter') return 'scatter';
      return 'line';
    };

    const getYAxisName = (): string => {
      let name = propertyName;
      if (unitFormat?.unitSymbol)
        name = `${propertyName} (${unitFormat?.unitSymbol})`;
      // pad space to make sure the Y axis name display fully
      if (name.length > 4) name = name.padStart(name.length * 2, ' ');
      return name;
    };

    const getDateArray = (timeData: Array<TimeData>): Array<string> => {
      const dates: string[] = [];
      timeData.forEach((item) => {
        const date = dayjs(item.time).format('MM-DD');
        if (!dates.includes(date)) dates.push(date);
      });

      return dates;
    };

    const getLegendSelected = (): { [index: string]: boolean } => {
      const legendSelected: { [index: string]: boolean } = {};
      objectTimeDataSeries.forEach((objectItem) => {
        objectItem.series.forEach((seriesItem) => {
          if ('legendSelected' in seriesItem) {
            if (chainBase) {
              const dates = getDateArray(seriesItem.timeData);
              dates.forEach((date) => {
                legendSelected[`${date} ${seriesItem.name}`] =
                  seriesItem.legendSelected ?? true;
              });
            } else {
              legendSelected[seriesItem.name] =
                seriesItem.legendSelected ?? true;
            }
          }
        });
      });
      return legendSelected;
    };

    const getLegendData = (): string[] => {
      const legendData: string[] = [];
      if (chainBase) {
        objectTimeDataSeries.forEach((objectItem) => {
          objectItem.series.forEach((seriesItem) => {
            const dates = getDateArray(seriesItem.timeData);
            dates.forEach((date) => {
              legendData.push(`${date} ${seriesItem.name}`);
            });
          });
        });
      } else {
        objectTimeDataSeries.forEach((objectItem) => {
          legendData.push(
            ...objectItem.series.map((seriesItem) => seriesItem.name),
          );
        });
      }
      if (legendData.length <= 1 && !showLegendWhenSingle) return [];
      return legendData.filter(
        (item) => item !== 'ENVELOP_MAX' && item !== 'ENVELOP_MIN',
      );
    };

    const timeLabelFormatter = (value: number): string => {
      const time = dayjs(value);
      if (chainBase) return time.format(isMonthChainBase() ? 'DD' : 'H:mm');

      if (time.isSame(dayjs(time).startOf('d'))) {
        // 零点显示成日期
        return dayjs(value).format('M/DD');
      }
      return dayjs(value).format('H:mm');
    };

    const timePointerFormatter = (value: string | number | Date): string => {
      const time = dayjs(value);
      if (chainBase) {
        if (time.second() === 0)
          return time.format(isMonthChainBase() ? 'DD日 HH:mm' : 'H:mm');
        return time.format('H:mm:ss');
      }

      if (time.second() === 0) return time.format('MM-DD H:mm');
      return time.format('MM-DD H:mm:ss');
    };

    const tooltipValueFormatter = (value: any): string => {
      if (typeof value === 'number') {
        if (unitFormat !== undefined)
          return unitFormat.getValueWithSymbol(value);
        return value.toString();
      }

      return value;
    };

    const getOriginalValue = (
      value: number | undefined,
    ): number | undefined => {
      if (value === undefined) return undefined;
      if (unitFormat instanceof DoubleUnitFormat) {
        return unitFormat.getOriginalValue(value) as number;
      }
      return value;
    };

    const getYAxisMaxValue = (): string | number | undefined => {
      const yAxisValues = unitFormat?.getYAxisValues();
      let yMax;
      if (yMaxValue !== undefined) yMax = getOriginalValue(yMaxValue);
      else if (yAxisValues && yAxisValues.length > 0)
        yMax = yAxisValues[yAxisValues.length - 1];
      return yMax;
    };

    const getWarnTooltipFormatter = (warnInfoItem: WarnInfoItem): string =>
      `<div style="color:red">类型:${
        warnInfoItem.secondTypeName
      }</div><div style="color:red;max-width: 240px; white-space: break-spaces;word-break: break-all;">${
        warnInfoItem.description
      }${warnInfoItem.endStatus === 0 ? '[持续中]' : ''}</div>`;

    const getWarnMarkArea = (warnList: WarnInfoList) =>
      warnList
        .filter((item) => item.startTime !== item.endTime)
        .map((item) => {
          const { startTime, endTime } = item;
          return [
            {
              itemStyle: {
                color: '#ffc3ba',
              },
              emphasis: {
                disabled: false,
                itemStyle: {
                  color: '#ff4640',
                },
              },
              xAxis: startTime,
            },
            {
              itemStyle: {
                color: '#ffc3ba',
              },
              emphasis: {
                disabled: false,
                itemStyle: {
                  color: '#ff4640',
                },
              },
              xAxis: endTime,
            },
          ];
        });

    const getMarkArea = (): any[] => [
      ...(showWarnMark && warnList && !chainBase
        ? getWarnMarkArea(warnList)
        : []),
    ];

    const yMinAndMaxValue: [min: number | undefined, max: number | undefined] =
      useMemo(() => {
        const minAndMaxValue: [
          min: number | undefined,
          max: number | undefined,
        ] = [undefined, undefined];
        if (!showWarnMark || chainBase) return minAndMaxValue;
        const minRange = lodash.min(minSeries.map((item) => item.value));
        const maxRange = lodash.max(maxSeries.map((item) => item.value));
        const timeDataValues = objectTimeDataSeries
          .flatMap((objectItem) => objectItem.series)
          .flatMap((item) => item.timeData)
          .map((item) => item.value);
        const minTimeDataValues = lodash.min(timeDataValues);
        const maxTimeDataValues = lodash.max(timeDataValues);
        if (showRange) {
          minAndMaxValue[0] = lodash.min([minTimeDataValues, minRange]);
          minAndMaxValue[1] = lodash.max([maxTimeDataValues, maxRange]);
        } else {
          minAndMaxValue[0] = minTimeDataValues;
          minAndMaxValue[1] = maxTimeDataValues;
        }
        return minAndMaxValue;
      }, [objectTimeDataSeries, showWarnMark, chainBase, showRange]);

    const warnPointData: MarkPointComponentOption['data'] = warnList
      // startTime等于endTime的为markPoint, 否则为markArea
      ?.filter((item) => item.startTime === item.endTime)
      .map((item) => {
        const { startTime, secondTypeName } = item;
        let yAxis;
        let symbolRotate = 0;

        const yMin = getOriginalValue(yMinValue) ?? yMinAndMaxValue[0];
        const yMax =
          typeof getYAxisMaxValue() === 'number'
            ? (getYAxisMaxValue() as number)
            : yMinAndMaxValue[1];
        if (typeof yMin !== 'undefined' && typeof yMax !== 'undefined') {
          if (Math.abs(yMin) > Math.abs(yMax)) {
            symbolRotate = 180;
            if (yMax < 0) {
              yAxis = yMax;
            }
          } else if (yMin >= 0) {
            yAxis = yMin;
          }

          if (yMin < 0 && yMax > 0) {
            yAxis = 0;
          }
        }
        return {
          name: `警告:${secondTypeName}`,
          value: secondTypeName,
          xAxis: startTime,
          yAxis: yAxis ?? '10%',
          symbolRotate,
          label: {
            show: true,
          },
          tooltip: {
            trigger: 'item',
            formatter: () => getWarnTooltipFormatter(item),
          },
        };
      });

    const getMarkPoint = (): MarkPointComponentOption => ({
      symbol: 'pin',
      itemStyle: {
        color: '#ff000086',
      },
      label: {
        position: 'inside',
      },
      data: showWarnMark && !chainBase ? warnPointData : undefined,
    });

    const getChartMarkMarkArea = (markList: MarkInfoList) =>
      markList.map((item) => {
        const { startTime, endTime, type } = item;

        return [
          {
            name:
              startTime === endTime
                ? '\ue664'
                : getSchemeTypeDataTitle(schemeType, type),
            silent: false,
            params: item,
            label: {
              fontFamily: startTime === endTime ? 'iconfont' : '',
            },
            itemStyle: {
              color: '#44b1f0',
              borderColor: '#44b1f0',
              borderWidth: 1,
            },
            xAxis: startTime,
          },
          {
            silent: false,
            params: item,
            xAxis: endTime,
          },
        ];
      });

    const getChartMarkArea = (): any[] => [
      ...(showMark && markList && !chainBase
        ? getChartMarkMarkArea(markList)
        : []),
    ];

    const getDataSeries = useCallback(
      (
        objectTimeDataSeries: Array<ObjectTimeDataSeries>,
        showRange?: boolean,
      ): ECOption['series'] => {
        const seriesData: ECOption['series'] = objectTimeDataSeries
          .flatMap((objectItem) => objectItem.series)
          .map((seriesItem) => ({
            name: seriesItem.name,
            type: getSeriesType(seriesItem.type),
            emphasis: {
              focus: 'series',
            },
            symbolSize: 3,
            showSymbol: seriesItem.dataType !== 'scada',
            sampling: 'lttb',
            large: true,
            data: seriesItem.timeData.map((dataItem) => [
              dataItem.time,
              dataItem.value,
            ]),
            lineStyle: {
              width: 3,
            },
          }));

        if (
          showRange &&
          minSeries.length > 0 &&
          maxSeries.length === minSeries.length
        ) {
          seriesData.push({
            z: -1,
            name: '上限',
            type: 'line',
            data: maxSeries.map((item) => [item.time, item.value]),
            symbol: 'none',
            lineStyle: {
              opacity: 0,
            },
            areaStyle: {
              origin: 'start',
              color: '#aaaaaa',
              opacity: 0.4,
            },
            silent: true,
            animation: false,
            smooth: true,
            emphasis: { disabled: true },
          });

          seriesData.push({
            z: -1,
            name: '下限',
            type: 'line',
            data: minSeries.map((item) => [item.time, item.value]),
            symbol: 'none',
            lineStyle: {
              opacity: 0,
            },
            areaStyle: {
              origin: 'start',
              color: darkMode ? '#1F1F1F' : 'white',
              opacity: 1,
            },
            silent: true,
            animation: false,
            smooth: true,
            emphasis: { disabled: true },
          });
        }

        return seriesData;
      },
      [minSeries, maxSeries, darkMode, getSeriesType],
    );

    const splitTimeDataByDay = (
      timeData: Array<TimeData>,
    ): Array<[string, Array<TimeData>]> => {
      const daySeries: Array<[string, Array<TimeData>]> = [];
      if (timeData.length === 0) return daySeries;

      const timeStepFormat = isMonthChainBase() ? 'YYYY-MM' : 'MM-DD';
      const today = dayjs(fakeDate).format(
        isMonthChainBase() ? 'YYYY-MM' : 'YYYY-MM-DD',
      );
      const mapDaySeries: Map<string, Array<TimeData>> = new Map();
      timeData.forEach((item) => {
        const { time, value } = item;
        const currentDate = dayjs(time).format(timeStepFormat);
        if (!mapDaySeries.has(currentDate)) mapDaySeries.set(currentDate, []);
        const fakeTime: string = `${today}${dayjs(time).format(
          isMonthChainBase() ? '-DD HH:mm:ss' : ' HH:mm:ss',
        )}`;
        mapDaySeries.get(currentDate)?.push({ time: fakeTime, value });
      });

      if (solutionChart) {
        mapDaySeries.forEach((dayTimeData, currentDate) => {
          daySeries.push([currentDate, dayTimeData]);
        });
      } else {
        if (chainMultiDates && chainMultiDates.dates.length > 0) {
          for (let i = 0; i < chainMultiDates.dates.sort().length; i += 1) {
            const currentDate = dayjs(chainMultiDates.dates[i]).format(
              timeStepFormat,
            );
            const dayTimeData = mapDaySeries.get(currentDate);
            if (dayTimeData) daySeries.push([currentDate, dayTimeData]);
          }
          return daySeries;
        }

        const firstDate = dayjs(startDate).startOf('day');
        const dayNum = dayjs(endDate).diff(firstDate, 'day') + 1;
        for (let i = 0; i < dayNum; i += 1) {
          const currentDate = firstDate.add(i, 'day').format(timeStepFormat);
          const dayTimeData = mapDaySeries.get(currentDate);
          if (dayTimeData) daySeries.push([currentDate, dayTimeData]);
        }
      }

      return daySeries;
    };

    const getDataSeriesByChainBase = (
      objectTimeDataSeries: Array<ObjectTimeDataSeries>,
    ): ECOption['series'] => {
      const seriesData: ECOption['series'] = [];
      objectTimeDataSeries.forEach((objectItem) => {
        objectItem.series.forEach((seriesItem) => {
          const daySeries = splitTimeDataByDay(seriesItem.timeData);
          daySeries.forEach((daySeriesItem) => {
            seriesData.push({
              name: `${daySeriesItem[0]} ${seriesItem.name}`,
              type: getSeriesType(seriesItem.type),
              symbolSize: 3,
              showSymbol: seriesItem.dataType !== 'scada',
              lineStyle: {
                width: 3,
              },
              sampling: 'lttb',
              large: true,
              largeThreshold: 500,
              data: daySeriesItem[1].map((dataItem) => [
                dataItem.time,
                dataItem.value,
              ]),
            });
          });
        });
      });

      return seriesData;
    };

    const isYAxisValues = (unitFormat: UnitFormat, value: number): boolean => {
      const yAxisValues = unitFormat.getYAxisValues();
      for (let i = 0; i < yAxisValues.length; i += 1) {
        const yValue = yAxisValues[i];
        if (typeof yValue === 'number' && yValue === value) return true;
        if (typeof yValue === 'string' && yValue === value.toString())
          return true;
      }

      return false;
    };

    const getSeriesRangeValues = (
      objectTimeDataSeries: Array<ObjectTimeDataSeries>,
    ): [number, number] => {
      const datas: Array<number> = [];
      objectTimeDataSeries.forEach((objectItem) => {
        objectItem.series.forEach((timeDataArray) => {
          if (timeDataArray.timeData.length > 0) {
            const values = timeDataArray.timeData.map((item) => item.value);
            const { min, max } = getMinMax(values);
            datas.push(min);
            datas.push(max);
          }
        });
      });

      return getRangeValues(datas);
    };

    const isYScale = (
      objectTimeDataSeries: Array<ObjectTimeDataSeries>,
      unitFormat: UnitFormat | undefined,
    ): boolean => {
      if (unitFormat && unitFormat.unitType === 'D') {
        const yRange = getSeriesRangeValues(objectTimeDataSeries);
        return (
          yRange[0] > 1 ||
          yRange[1] < -1 ||
          (yRange[0] > 0 && yRange[1] < 1) ||
          (yRange[0] < -1 && yRange[1] < 0)
        );
      }

      return false;
    };

    const yAxisLabelFormatter = (
      unitFormat: UnitFormat | undefined,
      val: number,
    ): string => {
      if (unitFormat) {
        if (unitFormat.unitType === 'E' && !isYAxisValues(unitFormat, val))
          return '';

        const v = unitFormat.getValue(val);
        if (v !== undefined) return v.toString();
      }

      return `${val}`;
    };

    const getYValue = (item: any): number | undefined => {
      const { value } = item;
      const { encode } = item;
      let valueY;
      if (Array.isArray(value)) {
        valueY = encode && value[encode.y[0]];
      } else {
        valueY = value;
      }
      return valueY;
    };

    const tooltipFormatter: TooltipComponentOption['formatter'] = (
      params,
    ): string => {
      let titleStr = '';
      let htmlStr = '';
      let warnStr = '';
      let markStr = '';
      let valueX: string | undefined;

      if (Array.isArray(params)) {
        const chart = chartInstanceRef.current?.getChartInstance();
        const { value, encode } = params[0];
        if (Array.isArray(value)) {
          valueX = encode && (value[encode.x[0]] as string);
        } else {
          valueX = value as string;
        }

        if (!chart) {
          return '';
        }

        const newParams = [...params].sort((a, b) => {
          const aValueY = getYValue(a);
          const bValueY = getYValue(b);

          return (
            chart.convertToPixel({ yAxisIndex: 0 }, aValueY as number) -
            chart.convertToPixel({ yAxisIndex: 0 }, bValueY as number)
          );
        });
        newParams.forEach((item) => {
          const { marker, seriesName } = item;
          const valueY = getYValue(item);

          htmlStr += `<div>${
            (marker ?? '') + (seriesName ?? '')
          } &nbsp;&nbsp;&nbsp;<span style="font-weight: 600">${tooltipValueFormatter(
            valueY,
          )}<span></div>`;
        });
      } else {
        const { marker, seriesName, value, encode } = params;
        if (Array.isArray(value)) {
          valueX = encode && (value[encode.x[0]] as string);
        } else {
          valueX = value as string;
        }
        htmlStr += `<div>${
          (marker ?? '') + (seriesName ?? '')
        }${tooltipValueFormatter(value)}</div>`;
      }
      titleStr = `<div>${
        typeof valueX === 'string' ? timePointerFormatter(valueX) : ''
      }</div>`;

      if (typeof valueX === 'string') {
        const valueXTime = dayjs(valueX);
        const foundWarnList = warnList?.filter((item) => {
          const valueXToDate = dayjs(valueX);
          return (
            (dayjs(item.startTime).isBefore(valueXToDate, 'minutes') ||
              dayjs(item.startTime).isSame(valueXToDate, 'minutes')) &&
            (dayjs(item.endTime).isAfter(valueXToDate, 'minutes') ||
              dayjs(item.endTime).isSame(valueXToDate, 'minutes'))
          );
        });

        let warnListString = '';
        foundWarnList?.forEach((item) => {
          const { secondTypeName, description, startTime, endTime, endStatus } =
            item;
          if (
            valueXTime.diff(dayjs(startTime)) >= 0 &&
            valueXTime.diff(dayjs(endTime)) <= 0
          ) {
            warnListString += `<div>类型:${secondTypeName}</div><div style="max-width: 240px; white-space: break-spaces;word-break: break-all;">${description}${
              endStatus === 0 ? '[持续中]' : ''
            }</div>`;
          }
        });
        if (warnListString !== '') {
          warnStr += '<div style="color: red">警告</dvi>';
          warnStr += warnListString;
        }

        let markListString = '';
        markList?.forEach((item) => {
          const { type, description, startTime, endTime } = item;
          if (
            valueXTime.diff(dayjs(startTime)) >= 0 &&
            valueXTime.diff(dayjs(endTime)) <= 0
          ) {
            markListString += `<div>类型:${getSchemeTypeDataTitle(
              schemeType,
              type,
            )}</div><div style="max-width: 240px; white-space: break-spaces;word-break: break-all;">${description}</div>`;
          }
        });
        if (markListString !== '') {
          markStr += '<div style="color: #0099ff">标注</dvi>';
          markStr += markListString;
        }
      }
      return titleStr + htmlStr + warnStr + markStr;
    };

    const getSeries = (): ECOption['series'] => {
      const seriesData = chainBase
        ? getDataSeriesByChainBase(objectTimeDataSeries)
        : getDataSeries(objectTimeDataSeries, showRange);

      const firstSeries = Array.isArray(seriesData)
        ? seriesData[0]
        : seriesData;

      if (firstSeries) {
        firstSeries.markArea = {
          data: [...getMarkArea(), ...getChartMarkArea()],
        };
        firstSeries.markPoint = getMarkPoint();
      }
      return seriesData;
    };

    const getXAxisMinMax = (): { min: string; max: string } => {
      if (chainBase) {
        if (
          chainMultiDates &&
          chainMultiDates.dateType === DatePickerType.MONTH
        ) {
          return {
            min: dayjs(fakeDate).startOf('day').format('YYYY-MM-01 HH:mm:ss'),
            max: dayjs(fakeDate)
              .add(1, 'month')
              .startOf('day')
              .format('YYYY-MM-01 HH:mm:ss'),
          };
        }
        return {
          min: dayjs(fakeDate).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          max: dayjs(fakeDate)
            .add(1, 'day')
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss'),
        };
      }
      return {
        min: dayjs(startDate).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        max: dayjs(endDate)
          .add(1, 'day')
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      };
    };

    const getAllTimes = (series: any): Array<string> => {
      const allDates: Set<string> = new Set();
      series.forEach((seriesItem: any) => {
        seriesItem.data.forEach((dataItem: (string | number)[]) => {
          if (typeof dataItem[0] === 'string') allDates.add(dataItem[0]);
        });
      });
      return [...Array.from(allDates)].sort((a, b) => a.localeCompare(b));
    };

    const optionToContent = (opt: any): any => {
      const { series } = opt;
      if (!Array.isArray(series)) return '';
      const allDates = getAllTimes(series);

      const seriesHeader = series
        .map(
          (seriesItem) =>
            `<th style="position: sticky; top: 0;background-color: #fff;">${seriesItem.name}</th>`,
        )
        .join('');
      let table = `<table border="1" style="color:#333;margin-left:20px;border-collapse:collapse;font-size:14px;text-align:center;user-select:all"><tbody><tr><th style="position: sticky; top: 0;background-color: #fff;">时间</th>${seriesHeader}</tr>`;

      const seriesData = series.map((seriesItem) => {
        const dataMap: Map<string, number> = new Map();
        seriesItem.data.forEach((dataItem: (string | number)[]) => {
          if (
            typeof dataItem[0] === 'string' &&
            typeof dataItem[1] === 'number'
          ) {
            dataMap.set(
              dataItem[0],
              tooltipValueFormatter(dataItem[1]) as unknown as number,
            );
          }
        });
        return dataMap;
      });

      const rows = allDates
        .map((currentDate) => {
          const dateCell = chainBase
            ? `<td>${dayjs(currentDate).format('HH:mm:ss')}</td>`
            : `<td>${currentDate}</td>`;
          const dataCells = seriesData
            .map((data) => `<td>${data.get(currentDate) ?? ''}</td>`)
            .join('');
          return `<tr>${dateCell}${dataCells}</tr>`;
        })
        .join('');

      table += rows;
      table += '</tbody></table>';

      return table;
    };

    const getOption = (): ECOption => {
      const yScale = isYScale(objectTimeDataSeries, unitFormat);
      const xAxisMinMax = getXAxisMinMax();
      const option: ECOption = {
        animation: false,
        color: [
          '#1492cc',
          '#cb3f1c',
          '#0000ff',
          '#f5b533',
          '#008000',
          '#dc69aa',
          '#7eb00a',
          '#b6a2de',
          '#07a2a4',
          '#27727b',
        ],
        grid: {
          left: '1%',
          right: '20px',
          bottom: '1%',
          top: '60px',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter: tooltipFormatter,
          valueFormatter: (value) => tooltipValueFormatter(value),
          position: (pos, _params, _el, _elRect, size) => {
            let x = pos[0] + 20;
            let y = pos[1];
            const pointX = pos[0];
            const pointY = pos[1];
            const boxWidth = size.contentSize[0];
            const boxHeight = size.contentSize[1];
            if (pointX + boxWidth + 20 > size.viewSize[0])
              x = pointX - boxWidth;
            if (pointY + boxHeight + 40 > size.viewSize[1])
              y = pointY - boxHeight;
            if (y < 0) {
              if (boxHeight > size.viewSize[1]) y = 0;
              else y = 20;
            }

            return [x, y];
          },
        },
        legend: {
          type: 'scroll',
          data: getLegendData(),
          selected: getLegendSelected(),
        },
        dataZoom: [
          {
            type: 'inside',
            realtime: true,
            start: 0,
            end: 100,
          },
        ],
        xAxis: {
          type: 'time',
          min: xAxisMinMax.min,
          max: xAxisMinMax.max,
          axisLabel: {
            formatter(value) {
              return timeLabelFormatter(value);
            },
            showMinLabel: true,
            showMaxLabel: true,
          },
          axisPointer: {
            label: {
              formatter(params) {
                return timePointerFormatter(params.value);
              },
            },
          },
        },
        yAxis: {
          type: 'value',
          name: getYAxisName(),
          axisLabel: {
            formatter(val) {
              return yAxisLabelFormatter(unitFormat, Number(val));
            },
          },
          axisPointer: {
            label: {
              formatter(params: any) {
                return yAxisLabelFormatter(unitFormat, params.value);
              },
            },
          },
          min: getOriginalValue(yMinValue) ?? yMinAndMaxValue[0],
          max: getYAxisMaxValue(),
          scale: yScale,
          // @ts-ignore
          data: unitFormat?.getYAxisValues(),
        },
        series: getSeries(),
        toolbox: {
          show: showToolbox,
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
              title: {
                zoom: '区域缩放',
                back: '区域缩放还原',
              },
            },
            myChartMode: {
              show: hasScatterType(objectTimeDataSeries),
              title: showAsLineType ? '切换为散点图' : '切换为折线图',
              icon: showAsLineType
                ? 'M981.333333 960a21.333333 21.333333 0 0 1-21.333333 21.333333H64a21.333333 21.333333 0 0 1-21.333333-21.333333V64a21.333333 21.333333 0 0 1 42.666666 0v874.666667h874.666667a21.333333 21.333333 0 0 1 21.333333 21.333333zM234.666667 725.333333a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m298.666666-298.666666a64 64 0 1 0-64-64 64 64 0 0 0 64 64zM448 640a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m256-42.666667a64 64 0 1 0-64-64 64 64 0 0 0 64 64zM277.333333 341.333333a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m490.666667 341.333334a64 64 0 1 0 64 64 64 64 0 0 0-64-64z m106.666667-298.666667a64 64 0 1 0-64-64 64 64 0 0 0 64 64z'
                : 'M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4',
              onclick() {
                setShowAsLineType(!showAsLineType);
              },
            },
            dataView: {
              show: showDataTable,
              readOnly: false,
              title: '数据表格',
              lang: [propertyName, '关闭', '刷新'],
              optionToContent(opt: any) {
                return optionToContent(opt);
              },
            },
            restore: {
              title: '还原',
            },
            myTool: {
              show: true,
              title: '下载',
              icon: 'path://M544.256 605.184l244.224-244.224a31.744 31.744 0 0 1 45.056 45.056l-295.424 295.424a36.864 36.864 0 0 1-51.2 0L190.464 406.528a31.744 31.744 0 1 1 45.056-45.056l244.224 244.224V111.104a32.256 32.256 0 1 1 64 0zM153.6 902.656a32.256 32.256 0 0 1 0-64h716.8a32.256 32.256 0 0 1 0 64z',
              onclick(global: any) {
                if (!global?.option?.series?.length) return;
                const { series: [firstSeries] = [] } = global.option;
                if (!firstSeries?.data?.length) return;
                const data = firstSeries.data.map(
                  ([date = '', value = '']: [string, string]) => ({
                    date,
                    name: firstSeries.name,
                    property: propertyName,
                    value,
                  }),
                );
                const filename = `${title}_${
                  startDate === endDate ? startDate : `${startDate}-${endDate}`
                }`;
                downloadExcel(data, filename, 'csv');
              },
            },
          },
        },
      };
      if (!chainBase) {
        option.tooltip = {
          ...option.tooltip,
          formatter: tooltipFormatter,
        };
      }
      return option;
    };

    useImperativeHandle(ref, () => ({
      getChartInstance: () => chartInstanceRef.current?.getChartInstance(),
    }));

    return (
      <WrappedReactECharts
        actions={actions}
        ref={chartInstanceRef}
        groupName={groupName}
        option={getOption()}
        style={{
          height: `${height ?? 'calc(100% - 24px)'}`,
          width: `${width ?? '100%'}`,
        }}
      />
    );
  },
);

TimeDataChart.displayName = 'TimeDataChart';

export default TimeDataChart;
