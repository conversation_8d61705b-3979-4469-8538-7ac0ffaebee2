/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import {
  getTimeRangeByType,
  getTimeRangeTypeName,
  TimeRangeType,
} from '@waterdesk/data/object-chart';
import { DatePicker, Radio, Space } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import { Dayjs } from 'dayjs';
import { useMergedState } from 'rc-util';
import { useEffect, useState } from 'react';

type Props = {
  timeRangeTypeOptions?: TimeRangeType[];
  defaultTimeRangeType?: TimeRangeType;
} & RangePickerProps;

const { RangePicker } = DatePicker;

const CustomRangePicker = (props: Props) => {
  const {
    value,
    timeRangeTypeOptions = [],
    defaultTimeRangeType,
    format = 'HH:mm:00',
    onChange,
    size,
  } = props;
  const [timeRangeType, setTimeRangeType] = useState<TimeRangeType | undefined>(
    undefined,
  );

  const timeRangeTypeRadios = timeRangeTypeOptions.map((m) => (
    <Radio.Button
      key={m}
      value={m}
    >
      {getTimeRangeTypeName(m)}
    </Radio.Button>
  ));

  const [rangeValue, setRangeValue] = useMergedState<RangePickerProps['value']>(
    undefined,
    {
      value,
    },
  );
  const handleOnChange: RangePickerProps['onChange'] = (date) => {
    setTimeRangeType(undefined);
    setRangeValue(date);
  };

  const onOpenChange: RangePickerProps['onOpenChange'] = (open) => {
    if (!open) {
      setRangeValue((rangeValue) => {
        if (rangeValue) {
          onChange?.(rangeValue as [Dayjs, Dayjs], [
            format as string,
            format as string,
          ]);
        }
        return rangeValue;
      });
    }
  };

  const handleAddLeft = () => {
    if (rangeValue) {
      const [start, end] = rangeValue;
      if (start && end) {
        // default step 1 when interval is 0
        const interval = end.diff(start, 'day') || 1;
        const range: [Dayjs, Dayjs] = [
          start.add(-interval, 'day'),
          end.add(-interval, 'day'),
        ];
        setTimeRangeType(undefined);
        setRangeValue(range);
        onChange?.(range, [format as string, format as string]);
      }
    }
  };
  const handleAddRight = () => {
    if (rangeValue) {
      const [start, end] = rangeValue;
      if (start && end) {
        const interval = end.diff(start, 'day') || 1;
        const range: [Dayjs, Dayjs] = [
          start.add(interval, 'day'),
          end.add(interval, 'day'),
        ];
        setTimeRangeType(undefined);
        setRangeValue(range);
        onChange?.(range, [format as string, format as string]);
      }
    }
  };

  useEffect(() => {
    if (timeRangeType) {
      const date = getTimeRangeByType(timeRangeType);
      onChange?.(date, [format as string, format as string]);
      setRangeValue(date);
    }
  }, [timeRangeType, format]);

  useEffect(() => {
    if (defaultTimeRangeType) {
      setTimeRangeType(defaultTimeRangeType);
    }
  }, [defaultTimeRangeType]);

  return (
    <span>
      <LeftOutlined
        disabled={!rangeValue?.[0]}
        onClick={handleAddLeft}
        style={{ marginRight: '5px' }}
      />
      <RangePicker
        {...props}
        size={size}
        value={rangeValue}
        onChange={handleOnChange}
        onOpenChange={onOpenChange}
      />
      <RightOutlined
        disabled={!rangeValue?.[1]}
        onClick={handleAddRight}
        style={{ marginLeft: '5px' }}
      />
      <Radio.Group
        value={timeRangeType}
        buttonStyle="solid"
        size={size}
        onChange={(e) => setTimeRangeType(e.target.value)}
      >
        <Space wrap>{timeRangeTypeRadios}</Space>
      </Radio.Group>
    </span>
  );
};

export default CustomRangePicker;
