/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import {
  getTimeRangeByType,
  getTimeRangeTypeName,
  TimeRangeType,
} from '@waterdesk/data/object-chart';
import { useSafeState } from 'ahooks';
import {
  DatePicker,
  Radio,
  RadioProps,
  Space,
  TimeRangePickerProps,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import LinkSmallButton from '../link-button';

interface NewRangePickerProps extends TimeRangePickerProps {
  timeRangeTypeOptions?: TimeRangeType[];
}

export const NewRangePicker = ({
  value,
  timeRangeTypeOptions,
  onChange,
  format = 'HH:mm:00',
  ...rest
}: NewRangePickerProps) => {
  const [date, setDate] = useSafeState<TimeRangePickerProps['value']>(value);
  const [timeRangeType, setTimeRangeType] = useState<TimeRangeType | undefined>(
    undefined,
  );

  const handleChangeTimeRange: TimeRangePickerProps['onChange'] = (date) => {
    setDate(date);
    onChange?.(date, [format as string, format as string]);
  };

  const handleRadioChange: RadioProps['onChange'] = (e) => {
    const type = e.target.value as TimeRangeType;
    setTimeRangeType(type);
    const newDate = getTimeRangeByType(type);
    setDate(newDate);
  };

  const handleAddLeft = () => {
    if (date && date.length === 2) {
      const [startDate, endDate] = date;
      const diff = endDate?.diff(startDate, 'day') ?? 0;

      const newStart = startDate?.subtract(diff + 1, 'day') as dayjs.Dayjs;
      const newEnd = endDate?.subtract(diff + 1, 'day') as dayjs.Dayjs;
      setDate([newStart, newEnd]);
      onChange?.([newStart, newEnd], [format as string, format as string]);
    }
  };

  const handleAddRight = () => {
    if (date && date.length === 2) {
      const [startDate, endDate] = date;
      const diff = endDate?.diff(startDate, 'day') ?? 0;

      let newStart = startDate?.add(diff + 1, 'day') as dayjs.Dayjs;
      let newEnd = endDate?.add(diff + 1, 'day') as dayjs.Dayjs;
      const today = dayjs();

      if (newEnd.isAfter(today)) {
        newEnd = today;
        newStart = today.subtract(diff, 'day');
      }

      setDate([newStart, newEnd]);
      onChange?.([newStart, newEnd], [format as string, format as string]);
    }
  };

  const isRightDisabled = () => {
    if (!date || date.length !== 2) return true;
    const endDate = date[1];
    return endDate?.endOf('d')?.isSameOrAfter(dayjs().endOf('d'));
  };

  const isLeftDisabled = () => !date || date.length !== 2;

  const timeRangeTypeRadios = useCallback(
    () =>
      timeRangeTypeOptions?.map((m) => (
        <Radio.Button
          key={m}
          value={m}
        >
          {getTimeRangeTypeName(m)}
        </Radio.Button>
      )),
    [timeRangeTypeOptions],
  );

  useEffect(() => {
    setDate(value);
  }, [value]);

  return (
    <>
      <LinkSmallButton
        icon={<LeftOutlined />}
        onClick={handleAddLeft}
        tooltipTitle="时间周期往前推移"
        disabled={isLeftDisabled()}
        style={{ marginRight: '5px' }}
      />
      <DatePicker.RangePicker
        {...rest}
        value={date}
        allowEmpty={[false, true]}
        allowClear={false}
        onChange={handleChangeTimeRange}
        onBlur={(e, info) => {
          const value = (e.target as any)?.value;
          if (info.range === 'start' && value) {
            const newDate: any = [dayjs(value), date?.[1]];
            setDate(newDate);
            onChange?.(newDate as [Dayjs, Dayjs], [
              format as string,
              format as string,
            ]);
          }
        }}
        size="small"
        disabledDate={(date) => date.isAfter(dayjs())}
      />
      <LinkSmallButton
        icon={<RightOutlined />}
        onClick={handleAddRight}
        tooltipTitle="时间周期往后推移"
        disabled={isRightDisabled()}
        style={{ marginRight: '5px' }}
      />
      <Radio.Group
        value={timeRangeType}
        buttonStyle="solid"
        size="small"
        onChange={handleRadioChange}
      >
        <Space wrap>{timeRangeTypeRadios()}</Space>
      </Radio.Group>
    </>
  );
};
