/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { isNotNullOrEmpty } from '@waterdesk/data/string';
import { Button, ButtonProps, Tooltip } from 'antd';

interface LinkSmallButtonProps extends ButtonProps {
  tooltipTitle?: string;
}

const LinkSmallButton = (props: LinkSmallButtonProps) => {
  const { onClick, children, tooltipTitle, ...rest } = props;

  if (isNotNullOrEmpty(tooltipTitle)) {
    return (
      <Tooltip title={tooltipTitle}>
        <Button
          {...rest}
          size="small"
          type="link"
          onClick={onClick}
          style={{ padding: 0, margin: 0 }}
        >
          {children}
        </Button>
      </Tooltip>
    );
  }
  return (
    <Button
      {...rest}
      size="small"
      type="link"
      onClick={onClick}
      style={{ padding: 0, margin: 0 }}
    >
      {children}
    </Button>
  );
};

export default LinkSmallButton;
