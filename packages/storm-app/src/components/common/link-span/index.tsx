/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useTheme } from 'styled-components';

const LinkSpan = (
  props: {
    onClick: () => void;
    tooltipTitle?: string;
    onMouseOver?: () => void;
    onMouseOut?: () => void;
    children: React.ReactNode;
  } & React.InsHTMLAttributes<HTMLSpanElement>,
) => {
  const {
    onClick,
    tooltipTitle,
    children,
    onMouseOver,
    onMouseOut,
    ...intrinsicAttributes
  } = props;

  const theme = useTheme();
  return (
    <span
      title={tooltipTitle}
      aria-hidden="true"
      role="link"
      onClick={onClick}
      onMouseOver={onMouseOver}
      onFocus={() => {}}
      onBlur={() => {}}
      onMouseOut={onMouseOut}
      style={{ cursor: 'pointer', color: theme.colorLink }}
      {...intrinsicAttributes}
    >
      {children}
    </span>
  );
};
export default LinkSpan;
