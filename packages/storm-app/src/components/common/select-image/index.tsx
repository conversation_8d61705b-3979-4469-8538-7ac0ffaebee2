/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SelectProps as AntdSelectProps, Image, Select } from 'antd';

type ValueType = string;
type OptionType = { src: string };

type SelectProps = AntdSelectProps<ValueType, OptionType>;

type Props = {
  options: SelectProps['options'];
} & SelectProps;

const OptionRender: SelectProps['optionRender'] = (option) => {
  const { label, data } = option;
  return (
    <div>
      <div>{label}</div>
      <Image
        width="100%"
        src={data.src}
        preview={{
          src: data.src,
        }}
      />
    </div>
  );
};

const SelectImage = (props: Props) => {
  const { ...reset } = props;

  return (
    <Select
      {...reset}
      optionRender={OptionRender}
    />
  );
};

export default SelectImage;
