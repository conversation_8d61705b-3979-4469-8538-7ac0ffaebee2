/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';

export const getIndicatorTypeOptions = (db: Database) => {
  const uniqueIndicators = new Map<string, { title: string; value: string }>();

  db.getAllDevices().forEach((device) => {
    device.indicators.forEach((indicator) => {
      const { otype, title } = indicator.indicatorType ?? {};
      if (otype && title) {
        uniqueIndicators.set(otype, { value: otype, title });
      }
    });
  });

  return Array.from(uniqueIndicators.values()).map((item) => ({
    label: item.title,
    value: item.value,
  }));
};
