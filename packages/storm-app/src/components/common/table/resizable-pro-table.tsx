/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ColumnsState,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { TableColumnsType } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';
import { AnyObject } from '../tables/hs-table';
import { ResizableHandle } from './style';

export const ResizableTitle = (
  props: React.HTMLAttributes<any> & {
    onResize: (
      e: React.SyntheticEvent<Element>,
      data: ResizeCallbackData,
    ) => void;
    width: number;
    onResizeStop?: () => void;
  },
) => {
  const { onResize, width, onResizeStop, ...restProps } = props;

  const handleResizeStart = useCallback(() => {
    document.body.style.userSelect = 'none';
  }, []);

  const handleResizeStop = useCallback(() => {
    document.body.style.userSelect = '';
    onResizeStop?.();
  }, [onResizeStop]);

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <ResizableHandle
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
      onResizeStart={handleResizeStart}
      onResizeStop={handleResizeStop}
    >
      <th {...restProps} />
    </Resizable>
  );
};

export type ColumnConfig = ColumnsState & { width?: number };

export type HSResizableTableProps<T extends AnyObject> = {
  columns: ProColumns<T>[];
  defaultColumnsState?: Record<string, ColumnConfig>;
  onChangeColumnsState?: (state: Record<string, ColumnConfig>) => void;
} & Omit<ProTableProps<T, any, any>, 'columns'>;

export const HSResizableProTable = <T extends AnyObject>({
  columns,
  defaultColumnsState,
  onChangeColumnsState,
  ...props
}: HSResizableTableProps<T>) => {
  const [cols, setCols] = useState<ProColumns<T>[]>(columns);
  const [columnsState, setColumnsState] = useState<
    Record<string, ColumnConfig>
  >(defaultColumnsState ?? {});

  const handleColumnsStateChange = (state: Record<string, ColumnConfig>) => {
    const mergedState = { ...columnsState };

    Object.keys(state).forEach((key) => {
      mergedState[key] = {
        ...mergedState[key],
        ...state[key],
        width: mergedState[key]?.width || state[key].width,
      };
    });

    setColumnsState(mergedState);
    onChangeColumnsState?.(mergedState);
  };

  const handleResize =
    (index: number) =>
    (_: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const newColumns = [...cols];
      const defaultWidth = 100;
      if (size.width < defaultWidth) return;

      newColumns[index] = {
        ...newColumns[index],
        width: size.width > defaultWidth ? size.width : defaultWidth,
      };
      setCols(newColumns);

      const newState = {
        ...columnsState,
        [newColumns[index].dataIndex as string]: {
          ...(columnsState[newColumns[index].dataIndex as string] || {}),
          width: size.width,
        },
      };

      setColumnsState(newState);
    };

  const handleResizeStop = () => {
    handleColumnsStateChange(columnsState);
  };

  const mergeColumns: ProColumns<T>[] = cols.map((col, index) => {
    const columnState = columnsState[col.dataIndex as string];
    return {
      ...col,
      width: columnState?.width || col.width,
      onHeaderCell: (column: TableColumnsType<T>[number]) => ({
        width: columnState?.width || column.width,
        onResize: handleResize(index) as React.ReactEventHandler<any>,
        onResizeStop: handleResizeStop,
      }),
    };
  });

  useEffect(() => {
    if (defaultColumnsState) setColumnsState(defaultColumnsState);
  }, [defaultColumnsState]);

  return (
    <ProTable<T>
      bordered
      components={{
        header: {
          cell: ResizableTitle,
        },
      }}
      size="small"
      columns={mergeColumns}
      options={{
        reload: false,
        density: false,
        setting: {
          draggable: true,
          checkable: true,
          showListItemOption: true,
        },
      }}
      columnsState={{
        value: columnsState,
        onChange: handleColumnsStateChange,
      }}
      scroll={{ x: 1200 }}
      search={false}
      {...props}
    />
  );
};
