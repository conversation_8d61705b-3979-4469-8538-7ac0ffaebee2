/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { removeWhitespaceFromParams } from '@waterdesk/data/string';
import {
  Button,
  Col,
  ConfigProvider,
  Form,
  FormInstance,
  Input,
  Row,
  Select,
  SelectProps,
  Space,
  TimeRangePickerProps,
  TreeSelect,
  TreeSelectProps,
} from 'antd';
import React, { FC, JSX } from 'react';
import { SearchDeviceInput } from 'src/components/observation-scada/add-observation-device';
import { rangePresets } from 'src/utils/tool';
import DatePicker from '../date/date-picker';
import { FormSearchWrapper, RightButtonWrapper } from './style';

export interface SearchItemConfig {
  type:
    | 'input'
    | 'select'
    | 'multiSelect'
    | 'date'
    | 'dateTimePicker'
    | 'dateRange'
    | 'dateTimeRange'
    | 'treeSelect'
    | 'deviceSelect'
    | 'custom';
  name: string | string[];
  label: string;
  hidden?: boolean;
  initialValue?: any;
  selectProps?: SelectProps;
  timeRangeProps?: TimeRangePickerProps;
  treeSelectProps?: TreeSelectProps;
  disabled?: boolean;
  customRender?: JSX.Element;
}

interface HSTableSearchFormProps {
  form: FormInstance;
  name: string;
  loading: boolean;
  onSubmit: () => void;
  onReset: () => void;
  items: SearchItemConfig[];
  initialValues?: Record<string, any>;
  disabled?: boolean;
  span?: number;
  isProTable?: boolean;
  onExport?: () => void;
  style?: React.CSSProperties;
  onSearch?: (searchValue: string, callback: Function) => Promise<void>;
}

const HSTableSearchForm: FC<HSTableSearchFormProps> = ({
  form,
  name,
  loading,
  onSubmit,
  onReset,
  items,
  initialValues,
  disabled,
  span,
  isProTable,
  onExport,
  style,
  onSearch,
}) => {
  const renderItem = (item: SearchItemConfig) => {
    let fieldComponent = null;

    switch (item?.type) {
      case 'input':
        fieldComponent = (
          <Input
            size="small"
            placeholder={`请输入${item.label}`}
            disabled={item.disabled}
          />
        );
        break;
      case 'select':
        fieldComponent = (
          <Select
            size="small"
            allowClear
            placeholder={`请选择${item.label}`}
            disabled={item.disabled}
            {...item.selectProps}
          />
        );
        break;
      case 'multiSelect':
        fieldComponent = (
          <Select
            size="small"
            mode="multiple"
            allowClear
            placeholder={`请选择${item.label}`}
            disabled={item.disabled}
            maxTagCount={4}
            {...item.selectProps}
          />
        );
        break;
      case 'deviceSelect':
        fieldComponent = onSearch ? (
          <SearchDeviceInput
            size="small"
            placeholder="请输入关键字查询"
            onSearch={onSearch}
          />
        ) : null;
        break;
      case 'dateTimePicker':
        fieldComponent = (
          <DatePicker
            showTime
            valueFormat="YYYY-MM-DD HH:mm:ss"
            size="small"
            style={{ width: '48%', minWidth: '120px' }}
          />
        );
        break;
      case 'dateRange':
        fieldComponent = (
          <>
            <Form.Item
              name={item.name[0]}
              noStyle
            >
              <DatePicker
                size="small"
                style={{ width: '48%', minWidth: '120px' }}
                disabled={item.disabled}
                disabledDate={(currentDate) =>
                  form.getFieldValue(item.name[1])
                    ? currentDate.isAfter(
                        form.getFieldValue(item.name[1]),
                        'day',
                      )
                    : false
                }
                presets={rangePresets.map((i) => ({
                  label: i.label,
                  value: i.value[0],
                }))}
                allowClear={false}
              />
            </Form.Item>
            -
            <Form.Item
              name={item.name[1]}
              noStyle
            >
              <DatePicker
                size="small"
                style={{ width: '48%', minWidth: '120px' }}
                disabled={item.disabled}
                disabledDate={(currentDate) =>
                  form.getFieldValue(item.name[0])
                    ? currentDate.isBefore(
                        form.getFieldValue(item.name[0]),
                        'day',
                      )
                    : false
                }
                allowClear={false}
              />
            </Form.Item>
          </>
        );
        break;
      case 'dateTimeRange':
        fieldComponent = (
          <>
            <Form.Item
              name={item.name[0]}
              noStyle
            >
              <DatePicker
                showTime
                size="small"
                format="YYYY-MM-DD HH:mm"
                style={{ width: '48%', minWidth: '120px' }}
                disabled={item.disabled}
                disabledDate={(currentDate) =>
                  form.getFieldValue(item.name[1])
                    ? currentDate.isAfter(
                        form.getFieldValue(item.name[1]),
                        'day',
                      )
                    : false
                }
                presets={rangePresets.map((i) => ({
                  label: i.label,
                  value: i.value[0],
                }))}
              />
            </Form.Item>
            -
            <Form.Item
              name={item.name[1]}
              noStyle
            >
              <DatePicker
                showTime
                size="small"
                format="YYYY-MM-DD HH:mm"
                style={{ width: '48%', minWidth: '120px' }}
                disabled={item.disabled}
                disabledDate={(currentDate) =>
                  form.getFieldValue(item.name[0])
                    ? currentDate.isBefore(
                        form.getFieldValue(item.name[0]),
                        'day',
                      )
                    : false
                }
              />
            </Form.Item>
          </>
        );
        break;
      case 'treeSelect':
        fieldComponent = (
          <TreeSelect
            allowClear
            disabled={item.disabled}
            placeholder={`请选择${item.label}`}
            {...item.treeSelectProps}
          />
        );
        break;
      case 'custom':
        fieldComponent = item.customRender;
        break;
      default:
        return null;
    }

    return (
      <Form.Item
        label={item.label}
        name={Array.isArray(item.name) ? undefined : item.name}
        hidden={item.hidden}
        initialValue={item.initialValue}
      >
        {fieldComponent}
      </Form.Item>
    );
  };

  const submitButtons = (
    <Space size="small">
      <Button
        type="primary"
        htmlType="submit"
        loading={loading}
        disabled={disabled}
      >
        查询
      </Button>
      <Button
        type="default"
        htmlType="reset"
        disabled={disabled || loading}
      >
        重置
      </Button>
      {onExport && (
        <Button
          disabled={loading}
          onClick={onExport}
        >
          导出
        </Button>
      )}
    </Space>
  );

  const getFields = () => {
    const children = [];
    const rowCapacity = 24 / (span ?? 6);
    const itemCount = items.length;
    const fillLastRow = itemCount % rowCapacity;

    for (let i = 0; i < itemCount; i += 1) {
      children.push(
        <Col
          span={span ?? 6}
          key={i}
        >
          {renderItem(items[i])}
        </Col>,
      );
    }

    const needNewRowForButtons = fillLastRow + 1 > rowCapacity;
    const buttonSpan = needNewRowForButtons
      ? 24
      : 24 - fillLastRow * (span ?? 6);

    if (needNewRowForButtons) {
      children.push(
        <Col
          span={24}
          key="buttons"
        >
          <RightButtonWrapper>{submitButtons}</RightButtonWrapper>
        </Col>,
      );
    } else {
      children.push(
        <Col
          span={buttonSpan}
          key="buttons"
          style={{ textAlign: 'right' }}
        >
          {submitButtons}
        </Col>,
      );
    }

    return children;
  };

  const handleFormSubmit = () => {
    const values = form.getFieldsValue();
    const trimmedValues = removeWhitespaceFromParams(values);

    form.setFieldsValue(trimmedValues);
    onSubmit();
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            itemMarginBottom: 10,
          },
        },
      }}
    >
      <FormSearchWrapper
        form={form}
        name={name}
        onFinish={handleFormSubmit}
        onReset={onReset}
        autoComplete="off"
        initialValues={initialValues}
        isProTable={isProTable ?? false}
        style={style}
      >
        <Row
          gutter={24}
          align="middle"
        >
          {getFields()}
        </Row>
      </FormSearchWrapper>
    </ConfigProvider>
  );
};

export default HSTableSearchForm;
