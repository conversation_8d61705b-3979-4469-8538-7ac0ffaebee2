/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getTimeRangeByType,
  TimeRangeType,
} from '@waterdesk/data/object-chart';
import { Col, Flex, Form, FormInstance, Radio, Space } from 'antd';
import dayjs from 'dayjs';
import { FC } from 'react';
import HighlightIcon from 'src/components/icon/highlight-icon';
import { rangePresets } from 'src/utils/tool';
import { NewRangePicker } from '../date/new-range-picker';

export const getRadiosByRangeOptions = (rangeOptions: TimeRangeType[]) =>
  rangeOptions.map((timeRangeType) => {
    switch (timeRangeType) {
      case TimeRangeType.oneDay:
        return (
          <Radio.Button
            key={TimeRangeType.oneDay}
            value={TimeRangeType.oneDay}
          >
            当天
          </Radio.Button>
        );
      case TimeRangeType.threeDays:
        return (
          <Radio.Button
            key={TimeRangeType.threeDays}
            value={TimeRangeType.threeDays}
          >
            近三天
          </Radio.Button>
        );
      case TimeRangeType.sevenDays:
        return (
          <Radio.Button
            key={TimeRangeType.sevenDays}
            value={TimeRangeType.sevenDays}
          >
            近七天
          </Radio.Button>
        );
      case TimeRangeType.oneMonth:
        return (
          <Radio.Button
            key={TimeRangeType.oneMonth}
            value={TimeRangeType.oneMonth}
          >
            近一月
          </Radio.Button>
        );
      case TimeRangeType.threeMonths:
        return (
          <Radio.Button
            key={TimeRangeType.threeMonths}
            value={TimeRangeType.threeMonths}
          >
            近三月
          </Radio.Button>
        );
      case TimeRangeType.oneYear:
        return (
          <Radio.Button
            key={TimeRangeType.oneYear}
            value={TimeRangeType.oneYear}
          >
            近一年
          </Radio.Button>
        );
      default:
        return null;
    }
  });

interface TableSearchMiniFormProps {
  name: string;
  form: FormInstance;
  timeRangeInitialValue: [dayjs.Dayjs, dayjs.Dayjs];
  timeRangeOptions: TimeRangeType[];
  rightNode?: React.ReactNode;
  highlightStatus?: boolean;
  onSubmit: () => void;
  onHighlight?: () => void;
}

const TableSearchMiniForm: FC<TableSearchMiniFormProps> = ({
  name,
  form,
  timeRangeInitialValue,
  timeRangeOptions,
  rightNode,
  highlightStatus,
  onSubmit,
  onHighlight,
}) => {
  const handleTimeRangeTypeChange = (changeValue: TimeRangeType) => {
    form.setFieldValue('timeRange', getTimeRangeByType(changeValue));
  };

  return (
    <Flex
      justify="space-between"
      style={{ marginBottom: 5 }}
    >
      <Form
        name={name}
        form={form}
        layout="inline"
        size="small"
        onValuesChange={onSubmit}
      >
        <Form.Item
          name="timeRange"
          label="时间"
          initialValue={timeRangeInitialValue}
        >
          <NewRangePicker
            style={{ width: '250px', maxWidth: '260px' }}
            format="YYYY-MM-DD"
            presets={rangePresets}
            disabledDate={(date) => date.isAfter(dayjs())}
            onChange={() => form.setFieldValue('timeRangeType', undefined)}
          />
        </Form.Item>
        <Form.Item name="timeRangeType">
          <Radio.Group
            buttonStyle="outline"
            onChange={(e) => handleTimeRangeTypeChange(e.target.value)}
          >
            <Space wrap>{getRadiosByRangeOptions(timeRangeOptions)}</Space>
          </Radio.Group>
        </Form.Item>
        {onHighlight && (
          <Col>
            <HighlightIcon
              showIconText
              active={highlightStatus}
              onClick={onHighlight}
            />
          </Col>
        )}
      </Form>
      <Col>{rightNode}</Col>
    </Flex>
  );
};

export default TableSearchMiniForm;
