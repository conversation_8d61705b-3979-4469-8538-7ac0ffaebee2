/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import { useState } from 'react';
import { ResizeCallbackData } from 'react-resizable';
import { ResizableTitle } from '../table/resizable-pro-table';
import { AnyObject, BottomInfiniteTableProps } from './hs-table';

const HSBottomInfiniteTable = <T extends AnyObject>({
  tableHeight = 300,
  isColumnTitleDraggable,
  ...rest
}: Omit<BottomInfiniteTableProps<T>, 'tableType'>) => {
  const [columns, setColumns] = useState<ColumnType<T>[]>(rest.columns ?? []);

  const handleResize =
    (index: number) =>
    (_e: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const nextColumns = [...columns];
      const defaultWidth = 100;
      if (size.width < defaultWidth) return;
      nextColumns[index] = {
        ...nextColumns[index],
        width: size.width,
      };
      setColumns(nextColumns);
    };

  const mergedColumns: ColumnType<T>[] = columns.map((col, index) => ({
    ...col,
    onHeaderCell: (column: ColumnType<T>) => ({
      width: column.width,
      onResize: handleResize(index) as React.ReactEventHandler<any>,
    }),
  }));

  return (
    <Table<T>
      {...rest}
      columns={isColumnTitleDraggable ? mergedColumns : rest.columns}
      components={{
        header: {
          cell: ResizableTitle,
        },
      }}
      size="small"
      virtual
      pagination={false}
      scroll={{ x: 1000, y: tableHeight - 92 }}
    />
  );
};
export default HSBottomInfiniteTable;
