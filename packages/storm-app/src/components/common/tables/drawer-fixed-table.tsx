/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Table, TableProps } from 'antd';
import { AnyObject } from './hs-table';

const HSDrawerFixedTable = <T extends AnyObject>({
  ...rest
}: TableProps<T>) => (
  <Table<T>
    {...rest}
    size="small"
    pagination={false}
    scroll={{ x: 1000 }}
  />
);

export default HSDrawerFixedTable;
