/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TableProps } from 'antd';
import { TableType } from '.';
import HSBottomInfiniteTable from './bottom-infinite-table';
import HSDrawerFixedTable from './drawer-fixed-table';
import HSFullPageTable from './full-page-table';

export type AnyObject = Record<string, any>;

interface BaseHSTableProps<T extends AnyObject> extends TableProps<T> {
  tableType: TableType;
  tableHeight?: number;
}

export interface BottomInfiniteTableProps<T extends AnyObject>
  extends BaseHSTableProps<T> {
  tableType: TableType.BOTTOM_INFINITE_TABLE;
  isColumnTitleDraggable?: boolean;
}

export interface OtherTableProps<T extends AnyObject>
  extends BaseHSTableProps<T> {
  tableType: Exclude<TableType, TableType.BOTTOM_INFINITE_TABLE>;
}

export type HSTableProps<T extends AnyObject> =
  | BottomInfiniteTableProps<T>
  | OtherTableProps<T>;

const HSTable = <T extends AnyObject>({
  tableType,
  ...rest
}: HSTableProps<T>) => {
  const renderTable = () => {
    switch (tableType) {
      case TableType.BOTTOM_INFINITE_TABLE:
        return <HSBottomInfiniteTable<T> {...rest} />;
      case TableType.DRAWER_FIXED_TABLE:
        return <HSDrawerFixedTable<T> {...rest} />;
      case TableType.FULL_PAGE_TABLE:
      default:
        return <HSFullPageTable<T> {...rest} />;
    }
  };

  return renderTable();
};

export default HSTable;
