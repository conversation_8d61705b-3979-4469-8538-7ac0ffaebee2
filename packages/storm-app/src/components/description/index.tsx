/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ReactNode } from 'react';
import {
  Content,
  DescriptionItemWrapper,
  DescriptionsWrapper,
  Label,
} from './style';

interface DescriptionItemProps {
  label: string;
  children: ReactNode;
}

export function DescriptionItem({ label, children }: DescriptionItemProps) {
  return (
    <DescriptionItemWrapper>
      <Label>{label}:</Label>
      <Content>{children}</Content>
    </DescriptionItemWrapper>
  );
}

interface DescriptionsProps {
  items: {
    label: string;
    content: ReactNode;
  }[];
}

export default function Descriptions({ items }: DescriptionsProps) {
  return (
    <DescriptionsWrapper>
      {items.map((item) => (
        <DescriptionItem
          key={item.label}
          label={item.label}
        >
          {item.content}
        </DescriptionItem>
      ))}
    </DescriptionsWrapper>
  );
}
