/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const DescriptionsWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px 32px;
  margin: 15px;
`;

export const DescriptionItemWrapper = styled.div`
  display: flex;
  align-items: center;
`;

export const Label = styled.div`
  font-size: 14px;
  color: var(--adm-color-text-secondary);
  margin-right: 8px;
`;

export const Content = styled.div`
  font-size: 14px;
  color: var(--adm-color-text);
`;
