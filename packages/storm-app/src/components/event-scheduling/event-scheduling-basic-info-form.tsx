/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  EventSchedulingBasicInfo,
  EventStatusType,
  EventType,
  getEventLevelOptions,
  getEventStatusOptions,
  getEventSubTypeOptions,
  getEventTypeOptions,
  LevelType,
} from '@waterdesk/data/event-scheduling/basic-info';
import { fieldNameMap } from '@waterdesk/data/event-scheduling/event-log';
import {
  Button,
  Col,
  ColProps,
  Form,
  FormInstance,
  Input,
  InputNumber,
  Row,
  RowProps,
  Select,
  SelectProps,
  Space,
} from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect } from 'react';
import DatePicker from 'src/components/common/date/date-picker';
import IconFont from 'src/components/icon-font';

export enum FormMode {
  CREATE = 'create',
  EDIT = 'edit',
  RELATED = 'related',
}

export interface EventSchedulingBasicInfoFormProps {
  form: FormInstance<EventSchedulingBasicInfo>;
  eventTypeData: EventType[];
  levelTypeData: LevelType[]; // 默认事件级别
  mode: FormMode;
  rowProps?: RowProps;
  colProps?: ColProps;
  openDefault?: boolean;
  shape?: string;
  relatedStartTime?: string; // 事件关联时，关联事件的开始时间
  selectPosition?: (shape?: string) => void;
  onClose?: () => void;
  onOk?: () => void;
  onAutoSave?: () => void;
}

export const EventSchedulingBasicInfoForm: FC<
  EventSchedulingBasicInfoFormProps
> = ({
  form,
  eventTypeData,
  levelTypeData,
  mode,
  rowProps,
  colProps,
  openDefault,
  shape,
  relatedStartTime,
  selectPosition,
  onClose,
  onOk,
  onAutoSave,
}) => {
  const eventStatus = Form.useWatch('eventStatus', form);
  const eventType = Form.useWatch('eventType', form);
  const eventStartTime = Form.useWatch('eventStartTime', form);

  const handleChangeEventStatus: SelectProps['onChange'] = (event) => {
    if (event === EventStatusType.DONE)
      form.setFieldValue('eventEndTime', dayjs().format('YYYY-MM-DD HH:mm'));

    onAutoSave?.();
  };

  const disabledStartDate = (currentDate: dayjs.Dayjs) =>
    eventStartTime ? currentDate?.isBefore(eventStartTime, 'day') : false;

  const handleResetEventSubType: SelectProps['onChange'] = (value) => {
    form.setFieldValue(
      'eventSubType',
      eventTypeData
        ?.find((i) => i.type === value)
        ?.children?.find((c) => c.isDefault)?.type ?? undefined,
    );
    onAutoSave?.();
  };

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue({
      eventTitle: openDefault
        ? `事件${dayjs().format('YYYYMMDDHHmm')}`
        : undefined,
      eventType: openDefault
        ? eventTypeData.find((i) => i.isDefault)?.type
        : undefined,
      eventSubType: openDefault
        ? eventTypeData
            .find((i) => i.isDefault)
            ?.children.find((c) => c.isDefault)?.type
        : undefined,
      eventStartTime: relatedStartTime ?? dayjs().format('YYYY-MM-DD HH:mm'),
      eventStatus: EventStatusType.DOING,
      eventLevel: openDefault
        ? levelTypeData.find((i) => i.isDefault)?.type
        : undefined,
    });
  }, []);

  useEffect(() => {
    form.setFieldValue('shape', shape);
    form.setFieldValue('eventAddress', shape ?? '');
  }, [shape]);

  return (
    <Form
      name="event-scheduling-form"
      form={form}
      layout="vertical"
      autoComplete="off"
    >
      <Row {...rowProps}>
        <Form.Item
          label="事件ID"
          name="eventId"
          hidden
        >
          <Input />
        </Form.Item>
        <Col span={16}>
          <Form.Item
            label={fieldNameMap.eventTitle}
            name="eventTitle"
            rules={[
              {
                required: true,
                message: `请输入${fieldNameMap.eventTitle}`,
              },
            ]}
          >
            <Input
              placeholder="请输入"
              showCount
              onBlur={onAutoSave}
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventStatus}
            name="eventStatus"
          >
            <Select
              options={getEventStatusOptions}
              placeholder="请选择"
              onChange={handleChangeEventStatus}
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventType}
            name="eventType"
            rules={[
              {
                required: true,
                message: `请选择${fieldNameMap.eventType}`,
              },
            ]}
          >
            <Select
              allowClear
              options={getEventTypeOptions(eventTypeData)}
              onChange={handleResetEventSubType}
              placeholder="请选择"
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventSubType}
            name="eventSubType"
            rules={[
              {
                required: true,
                message: `请选择${fieldNameMap.eventSubType}`,
              },
            ]}
          >
            <Select
              allowClear
              options={getEventSubTypeOptions(eventTypeData, eventType)}
              onChange={onAutoSave}
              placeholder="请选择"
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventAddress}
            name="eventAddress"
          >
            <Input
              placeholder="请选择"
              disabled
              onBlur={onAutoSave}
              addonAfter={
                <Button
                  size="small"
                  type="primary"
                  ghost
                  onClick={() => selectPosition?.(form.getFieldValue('shape'))}
                >
                  <IconFont type="icon-map" />
                </Button>
              }
            />
          </Form.Item>
        </Col>
        <Form.Item
          label={fieldNameMap.shape}
          name="shape"
          hidden
        >
          <Input
            placeholder="请选择"
            disabled
          />
        </Form.Item>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventLevel}
            name="eventLevel"
            rules={[
              {
                required: true,
                message: `请选择${fieldNameMap.eventLevel}`,
              },
            ]}
          >
            <Select
              allowClear
              options={getEventLevelOptions(levelTypeData)}
              onChange={onAutoSave}
              placeholder="请选择"
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.affectUserCount}
            name="affectUserCount"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder={`请输入${fieldNameMap.affectUserCount}`}
              onBlur={onAutoSave}
              precision={0}
              min={0}
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.label}
            name="label"
          >
            <Input
              placeholder={`请输入${fieldNameMap.label}`}
              onBlur={onAutoSave}
              max={20}
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          <Form.Item
            label={fieldNameMap.eventStartTime}
            name="eventStartTime"
            rules={[
              {
                required: true,
                message: `请选择${fieldNameMap.eventStartTime}`,
              },
            ]}
          >
            <DatePicker
              placeholder="请选择"
              changeOnBlur
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              valueFormat="YYYY-MM-DD HH:mm"
              onChange={onAutoSave}
            />
          </Form.Item>
        </Col>
        <Col {...colProps}>
          {eventStatus === EventStatusType.DONE && (
            <Form.Item
              label={fieldNameMap.eventEndTime}
              name="eventEndTime"
              rules={[
                {
                  required: true,
                  message: `请选择${fieldNameMap.eventEndTime}`,
                },
              ]}
            >
              <DatePicker
                changeOnBlur
                placeholder="请选择"
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                disabledDate={disabledStartDate}
                onChange={onAutoSave}
              />
            </Form.Item>
          )}
        </Col>
        {mode === FormMode.CREATE && (
          <Col
            {...colProps}
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              marginTop: 5,
            }}
          >
            <Space>
              <Button onClick={onClose}>取消</Button>
              <Button
                type="primary"
                onClick={() => onOk?.()}
              >
                创建
              </Button>
            </Space>
          </Col>
        )}
      </Row>
    </Form>
  );
};
