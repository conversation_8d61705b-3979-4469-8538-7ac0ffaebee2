/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import {
  EventRelatedSubType,
  EventRelatedType,
  EventSchedulingRelatedInfo,
  getEventRelatedTypeName,
  getEventRelatedTypeTagColor,
} from '@waterdesk/data/event-scheduling/related-info';
import {
  Divider,
  Form,
  FormInstance,
  Input,
  Popconfirm,
  Radio,
  RadioProps,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import DatePicker from 'src/components/common/date/date-picker';
import { VerticalFormTitleWrapper } from 'src/styles/common-style';
import { EventSchedulingRelatedContentTimeline } from './event-scheduling-related-content-timeline';

const category = [
  { label: '列表', value: 'table' },
  { label: '时间线', value: 'timeline' },
];

export interface TableItem {
  id: string;
  type: EventRelatedType;
  subType: string;
  description?: string;
  time: string | dayjs.Dayjs;
  operator: string;
  canEdit: boolean;
}

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: string;
  inputType: 'input' | 'time';
  record: TableItem;
  index: number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  children,
  ...restProps
}) => {
  const inputNode =
    inputType === 'time' ? (
      <DatePicker
        disabled={record.type !== EventRelatedType.CUSTOM}
        showTime={{ format: 'HH:mm' }}
        format="YYYY-MM-DD HH:mm"
      />
    ) : (
      <Input.TextArea
        rows={4}
        placeholder="请输入"
      />
    );

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: true,
              message: `${title} 不能为空!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

export interface EventSchedulingRelatedContentTableProps {
  eventId: string;
  relatedData: EventSchedulingRelatedInfo[];
  basicInfoForm: FormInstance<EventSchedulingBasicInfo>;
  onAddRelatedData: (
    eventId: string,
    relatedData: Partial<EventSchedulingRelatedInfo>,
  ) => void;
  onEditRelatedData: (
    eventId: string,
    relatedData: Partial<EventSchedulingRelatedInfo>,
  ) => void;
  onDeleteRelatedData: (eventId: string, relatedId: string) => void;
}

export const EventSchedulingRelatedContentTable: FC<
  EventSchedulingRelatedContentTableProps
> = ({
  eventId,
  relatedData,
  basicInfoForm,
  onAddRelatedData,
  onEditRelatedData,
  onDeleteRelatedData,
}) => {
  const [editingKey, setEditingKey] = useState<string>('');
  const [dataSource, setDataSource] = useState<TableItem[]>([]);
  const [eventsCategories, setEventsCategories] = useState(category[0].value);

  const [relatedInfoForm] = Form.useForm<TableItem>();

  const isEditing = (record: TableItem) => record.id === editingKey;

  const handleCategoryChange: RadioProps['onChange'] = (e) => {
    setEventsCategories(e.target.value);
  };

  const handleSave = async (editingKey: string) => {
    try {
      const tableItem = await relatedInfoForm.validateFields();
      const formattedTime = dayjs(tableItem.time).format('YYYY-MM-DD HH:mm:ss');
      const newValues: TableItem = { ...tableItem, time: formattedTime };

      const index = dataSource.findIndex((item) => editingKey === item.id);

      if (index > -1) {
        const item = dataSource[index];
        const updatedItem = { ...item, ...newValues };

        const newDataSource = [...dataSource];
        newDataSource.splice(index, 1, updatedItem);
        setDataSource(newDataSource);
        setEditingKey('');

        const relatedData = {
          relatedType: updatedItem.type,
          relatedSubType: EventRelatedSubType.OTHER,
          relatedDescription:
            item.description === updatedItem.description
              ? undefined
              : updatedItem.description,
          relatedTime: dayjs(updatedItem.time).isSame(item.time)
            ? undefined
            : dayjs(updatedItem.time).format('YYYY-MM-DD HH:mm:ss'),
        };

        if (item?.operator === '') {
          onAddRelatedData(eventId, relatedData);
        } else {
          onEditRelatedData(eventId, {
            ...relatedData,
            relatedId: item.id,
          });
        }
      }
    } catch (error) {
      console.error('error in handleSave: ', error);
    }
  };

  const handleDelete = (relatedId: string) => {
    const newData = dataSource.filter((item) => item.id !== relatedId);
    setDataSource(newData);
    onDeleteRelatedData(eventId, relatedId);
  };

  const handleAdd = async () => {
    await basicInfoForm.validateFields();
    const newItem: TableItem = {
      id: (dataSource.length + 1).toString(),
      type: EventRelatedType.CUSTOM,
      subType: '',
      description: '',
      time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      operator: '',
      canEdit: true,
    };
    relatedInfoForm.setFieldsValue(newItem);
    setEditingKey(newItem.id);
    setDataSource([...dataSource, newItem]);
  };

  const handleCancel = () => {
    if (
      dataSource[dataSource.length - 1].description === '' &&
      dataSource[dataSource.length - 1].type === EventRelatedType.CUSTOM
    ) {
      dataSource.pop();
      setDataSource([...dataSource]);
    }
    setEditingKey('');
  };

  const handleEdit = (record: TableItem) => {
    relatedInfoForm.setFieldsValue({ ...record, time: dayjs(record.time) });
    setEditingKey(record.id);
  };

  useEffect(() => {
    const formattedData = relatedData?.map((item) => ({
      id: item.relatedId,
      type: item.relatedType,
      subType: item.relatedSubType,
      description: item.relatedDescription,
      time: dayjs(item.relatedTime).format('YYYY-MM-DD HH:mm'),
      operator: item.relatedOperator ?? '',
      canEdit: [
        EventRelatedType.CUSTOM,
        EventRelatedType.EVENT_START,
        EventRelatedType.EVENT_END,
      ].includes(item.relatedType),
    }));
    setDataSource(formattedData);
    setEditingKey('');
  }, [relatedData]);

  const getContent = (item: TableItem) => {
    if (item.type === EventRelatedType.CUSTOM) {
      return <span>{item.description}</span>;
    }
    return (
      <div>
        <Tag color={getEventRelatedTypeTagColor(item.type)}>
          {getEventRelatedTypeName(item.type)}
        </Tag>
        {item.subType !== EventRelatedSubType.OTHER && item.subType}
        {item.description && <div>详情: {item.description}</div>}
      </div>
    );
  };

  const handleCheckedTimeLine = (
    checked: boolean,
    item: EventSchedulingRelatedInfo,
  ) => {
    onEditRelatedData(eventId, {
      relatedId: item.relatedId,
      relatedOnTimeLine: checked,
      relatedOnChart: checked,
    });
  };

  const defaultColumns = [
    {
      title: '',
      dataIndex: 'index',
      width: '5%',
      render: (_: string, __: TableItem, index: number) => index + 1,
    },
    {
      title: '事件描述',
      dataIndex: 'description',
      width: '40%',
      editable: true,
      render: (_text: string, record: TableItem) => getContent(record),
    },
    {
      title: '发生时间',
      dataIndex: 'time',
      width: '25%',
      editable: true,
    },
    {
      title: '记录人',
      dataIndex: 'operator',
      width: '15%',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '15%',
      render: (_: string, record: TableItem) => {
        const isDisabled = editingKey !== '';
        const editable = isEditing(record);

        if (!record.canEdit) {
          return null;
        }

        return editable ? (
          <span>
            <Typography.Link
              onClick={() => handleSave(record.id)}
              style={{ marginRight: 8 }}
            >
              保存
            </Typography.Link>
            <Popconfirm
              title="确认取消?"
              onConfirm={handleCancel}
            >
              <Typography.Link>取消</Typography.Link>
            </Popconfirm>
          </span>
        ) : (
          <span>
            <Typography.Link
              disabled={isDisabled}
              onClick={() => handleEdit(record)}
              style={{ marginRight: 8 }}
            >
              编辑
            </Typography.Link>
            <Popconfirm
              title="确认删除?"
              onConfirm={() => handleDelete(record.id)}
              disabled={isDisabled || record.type !== EventRelatedType.CUSTOM}
            >
              <Typography.Text
                type="danger"
                disabled={isDisabled || record.type !== EventRelatedType.CUSTOM}
              >
                删除
              </Typography.Text>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  const mergedColumns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: TableItem) => ({
        record,
        inputType: col.dataIndex === 'time' ? 'time' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <>
      <Divider orientation="left">事件内容</Divider>
      <Form
        name="related-info-form"
        form={relatedInfoForm}
        colon={false}
        component={false}
      >
        <Form.Item
          label={
            <VerticalFormTitleWrapper>
              <div>
                {eventsCategories === 'table' && (
                  <Typography.Link
                    disabled={editingKey !== ''}
                    onClick={handleAdd}
                    style={{ marginLeft: 10 }}
                  >
                    新建
                  </Typography.Link>
                )}
              </div>
              <Radio.Group
                value={eventsCategories}
                onChange={handleCategoryChange}
                size="small"
              >
                <Space wrap>
                  {category.map((category) => (
                    <Radio.Button
                      key={category.value}
                      value={category.value}
                    >
                      {category.label}
                    </Radio.Button>
                  ))}
                </Space>
              </Radio.Group>
            </VerticalFormTitleWrapper>
          }
        >
          {eventsCategories !== 'timeline' ? (
            <Table
              components={{
                body: {
                  cell: EditableCell,
                },
              }}
              bordered
              dataSource={dataSource}
              columns={mergedColumns}
              rowClassName="editable-row"
              rowKey="id"
              pagination={false}
              size="small"
            />
          ) : (
            <EventSchedulingRelatedContentTimeline
              relatedInfo={relatedData}
              onCheckedTimeline={handleCheckedTimeLine}
            />
          )}
        </Form.Item>
      </Form>
    </>
  );
};
