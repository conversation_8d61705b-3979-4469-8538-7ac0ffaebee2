/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  EventRelatedSubType,
  EventRelatedType,
  EventSchedulingRelatedInfo,
  getEventRelatedTypeName,
} from '@waterdesk/data/event-scheduling/related-info';
import { Checkbox, Timeline } from 'antd';
import dayjs from 'dayjs';
import { FC, ReactNode, useCallback, useMemo } from 'react';
import {
  TimeLineDot,
  TimeLineLeftWrapper,
  TimeLineRightWrapper,
} from './style';

export interface EventSchedulingRelatedContentTimelineProps {
  relatedInfo: EventSchedulingRelatedInfo[];
  onCheckedTimeline: (
    checked: boolean,
    item: EventSchedulingRelatedInfo,
  ) => void;
}

export const EventSchedulingRelatedContentTimeline: FC<
  EventSchedulingRelatedContentTimelineProps
> = ({ relatedInfo, onCheckedTimeline }) => {
  const getDot = useCallback(
    (index: number) => <TimeLineDot>{index}</TimeLineDot>,
    [],
  );

  const getLabel = useCallback(
    (title: string, time: string) => (
      <TimeLineLeftWrapper>
        <div>{getEventRelatedTypeName(title)}</div>
        <div>{time}</div>
      </TimeLineLeftWrapper>
    ),
    [],
  );

  const getContent = useCallback(
    (content: ReactNode) => (
      <TimeLineRightWrapper>{content}</TimeLineRightWrapper>
    ),
    [],
  );

  const getDefaultItem = useCallback(
    (item: EventSchedulingRelatedInfo, index: number) => ({
      key: index,
      dot: getDot(index + 1),
      label: getLabel(
        item.relatedType,
        dayjs(item.relatedTime).format('YYYY-MM-DD HH:mm') ?? '-',
      ),
      children: getContent(
        <>
          <Checkbox
            defaultChecked={item.relatedOnChart}
            onChange={(e) => onCheckedTimeline(e.target.checked, item)}
            disabled={item.relatedType === EventRelatedType.EVENT_START}
          />{' '}
          显示在事件时间轴
          {item.relatedSubType !== EventRelatedSubType.OTHER && (
            <div>{`类型: ${item.relatedSubType}`}</div>
          )}
          {item.relatedDescription && (
            <div>{`详情: ${item.relatedDescription}`}</div>
          )}
          {item.relatedSubType === EventRelatedSubType.OTHER &&
            !item.relatedDescription && <div style={{ height: '50px' }} />}
        </>,
      ),
    }),
    [relatedInfo],
  );

  const timelineData = useMemo(
    () => relatedInfo.map((item, index) => getDefaultItem(item, index)),
    [relatedInfo],
  );

  return (
    <Timeline
      mode="left"
      items={timelineData}
    />
  );
};
