/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Button } from 'antd';
import styled from 'styled-components';

export const TimeLineLeftWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 20px;
`;

export const TimeLineRightWrapper = styled.div`
  margin: 10px;
`;

export const TimeLineDot = styled.div`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colorPrimary};
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: 800;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const TabButton = styled(Button)`
  margin-top: -5px;
  margin-bottom: 5px;
`;
