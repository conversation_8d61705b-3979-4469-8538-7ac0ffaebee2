/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const FormTitleWrapper = styled.div<{ hasButtons: boolean }>`
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  margin-top: ${({ hasButtons }) => (hasButtons ? '13px' : '5px')};
`;

export const FormTitle = styled.span`
  margin-right: 10px;
  min-width: 50px;
`;

export const ButtonsWrapper = styled.div`
  display: flex;
`;
