/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Positioning } from 'ol/Overlay';

export interface LabelRect {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  priority: number;
  visible?: boolean;
  positioning?: Positioning;
}

function getOffset(label: LabelRect): number[] {
  switch (label.positioning) {
    case 'bottom-center':
      return [0, -label.height / 2];
    case 'top-center':
      return [0, label.height / 2];
    case 'center-left':
      return [label.width / 2, 0];
    case 'center-right':
      return [-label.width / 2, 0];
    default:
      return [0, 0];
  }
}

function intersects(test1: LabelRect, test2: LabelRect): boolean {
  const [offset1Width, offset1Height] = getOffset(test1);
  const [offset2Width, offset2Height] = getOffset(test2);
  if (
    test1.x + test1.width + offset1Width > test2.x + offset2Width && // maxX > MinX
    test1.x + offset1Width < test2.x + test2.width + offset2Width && // minX < MaxX
    test1.y + test1.height + offset1Height > test2.y + offset2Height && // maxY > MinY
    test1.y + offset1Height < test2.y + test2.height + offset2Height // minY < MaxY
  )
    return true;
  return false;
}

export function outsideScreen(
  testRect: LabelRect,
  screenWidth: number,
  screenHeight: number,
): boolean {
  return (
    testRect.x < 0 ||
    testRect.y < 0 ||
    testRect.x - testRect.width > screenWidth ||
    testRect.y - testRect.height > screenHeight
  );
}

/**
 * get visible label IDs as Set after collision detection
 */
export function collisionDetect(
  labels: Array<LabelRect>,
  screenWidth: number,
  screenHeight: number,
  reset?: boolean,
): Set<string> {
  const visibleIds: Set<string> = new Set<string>();
  const testingLabels = labels.map((item) => ({
    ...item,
    visible: reset ? false : item.visible,
  }));

  testingLabels.sort((a, b) => {
    if (a.priority > b.priority) return 1;
    if (a.priority < b.priority) return -1;
    if (a.visible) return -1;
    if (b.visible) return 1;
    return 0;
  });

  for (let i: number = 0; i < testingLabels.length; i += 1) {
    const currentLabel = testingLabels[i];
    if (outsideScreen(currentLabel, screenWidth, screenHeight)) continue;

    let visible: boolean = true;
    for (let j: number = 0; j < i; j += 1) {
      if (testingLabels[j].visible) {
        if (intersects(currentLabel, testingLabels[j])) {
          visible = false;
          break;
        }
      }
    }

    testingLabels[i].visible = visible;
    if (visible) visibleIds.add(testingLabels[i].id);
  }

  return visibleIds;
}
