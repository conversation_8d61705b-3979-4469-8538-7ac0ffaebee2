/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupData, LegendItemData } from '@waterdesk/data/legend-data';
import { Checkbox, Col, Empty, Row } from 'antd';
import { ReactNode } from 'react';

import {
  LegendDataValueTitleWrapper,
  LegendItemIconWrapper,
  LegendItemTitle,
  LegendItemWrapper,
} from './style';

interface Props {
  groupData: LegendGroupData;
  changeLegendVisible?: (
    legendName: string,
    legendId: string | number,
    visible: boolean,
  ) => void;
}

const translateIcon = (color?: string, icon?: string): ReactNode | null => {
  if (typeof icon === 'string' && icon !== '') {
    return (
      <LegendItemIconWrapper style={color ? { color } : undefined}>
        {
          // eslint-disable-next-line no-eval
          <span>{eval(`'${icon}'`)}</span>
        }
      </LegendItemIconWrapper>
    );
  }
  return null;
};

const formatCount = (count: number | undefined): string =>
  typeof count === 'number' ? `(${count})` : '';

const LegendDataValueTitle = (props: {
  legendDataValue: LegendItemData;
  icon?: string;
}) => {
  const { legendDataValue, icon } = props;
  return (
    <LegendDataValueTitleWrapper>
      {translateIcon(legendDataValue.color, icon)}
      {legendDataValue.title}
      {formatCount(legendDataValue.count)}
    </LegendDataValueTitleWrapper>
  );
};

export default function LegendItem(props: Props) {
  const { groupData, changeLegendVisible } = props;
  const defaultValue = groupData.items
    .filter((item) => item.checked)
    .map((item) => item.id);

  const onChange = (
    legendName: string,
    legendId: string | number,
    checked: boolean,
  ) => {
    changeLegendVisible?.(legendName, legendId, checked);
  };

  return (
    <Checkbox.Group
      key={groupData.name}
      value={defaultValue}
      style={{ width: '100%' }}
    >
      <LegendItemWrapper>
        <LegendItemTitle>{groupData.title}</LegendItemTitle>
        {groupData.items.length > 0 ? (
          <Row>
            {groupData.items.map((legendDataValue) =>
              !legendDataValue.id ? null : (
                <Col
                  key={legendDataValue.id}
                  span={12}
                >
                  {groupData.type === 'checkbox' ? (
                    <Checkbox
                      value={legendDataValue.id}
                      onChange={(e) =>
                        onChange(
                          groupData.name,
                          legendDataValue.id,
                          e.target.checked,
                        )
                      }
                    >
                      <LegendDataValueTitle
                        legendDataValue={legendDataValue}
                        icon={groupData.icon}
                      />
                    </Checkbox>
                  ) : (
                    <LegendDataValueTitle
                      legendDataValue={legendDataValue}
                      icon={groupData.icon}
                    />
                  )}
                </Col>
              ),
            )}
          </Row>
        ) : (
          <Empty />
        )}
      </LegendItemWrapper>
    </Checkbox.Group>
  );
}
