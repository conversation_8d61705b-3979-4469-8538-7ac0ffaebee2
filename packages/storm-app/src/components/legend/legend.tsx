/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupData } from '@waterdesk/data/legend-data';
import { Empty, Row } from 'antd';
import LegendItem from './legend-item';

interface Props {
  groupDataCollection: LegendGroupData[];
  changeLegendVisible: (
    legendName: string,
    legendId: string | number,
    visible: boolean,
    setActiveKeys?: () => void,
  ) => void;
}

const LegendEmpty = () => <Empty />;

export default function Legend(props: Props) {
  const { groupDataCollection, changeLegendVisible } = props;

  if (!groupDataCollection?.length) return <LegendEmpty />;
  return (
    <div>
      {groupDataCollection.map((legendDataItem) =>
        !legendDataItem.name ? null : (
          <Row key={legendDataItem.name}>
            <LegendItem
              groupData={legendDataItem}
              changeLegendVisible={changeLegendVisible}
            />
          </Row>
        ),
      )}
    </div>
  );
}
