/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BeforePrefix } from 'src/styles/common-style';
import styled from 'styled-components';

export const LegendItemWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

export const LegendItemIconWrapper = styled.div`
  > span {
    font-family: 'iconfont';
  }
`;

export const LegendItemTitle = styled.h4`
  text-align: left;
  padding-left: 10px;
  margin: 10px 0;
  font-weight: 600;
  position: relative;
  font-size: 14px;
  &::before {
    ${BeforePrefix}
  }
`;

export const LegendDataValueTitleWrapper = styled.div`
  display: flex;
`;
