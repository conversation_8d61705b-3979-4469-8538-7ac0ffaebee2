/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { AppMode } from '@waterdesk/data/app-config';
import { Button, Col, Form, Image, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import {
  BASE_URL,
  LOGIN_VALIDATION,
  SYSTEM_CONFIG,
  SYSTEM_ICON_BASE_PATH,
} from 'src/config';
import { LoginWrapper } from './style';

export interface Props {
  onFinish: (
    username: string,
    password: string,
    randomString: string,
    captcha: string,
  ) => void;
  loading?: boolean;
  theme: string;
  appMode?: AppMode;
}

const Login = (props: Props) => {
  const { onFinish, loading, theme, appMode } = props;

  const { token } = useToken();
  const [randomString, setRandomString] = useState<string>('');
  const { loginLogoImage, systemName, systemSubTitleColor, systemSubTitle } =
    SYSTEM_CONFIG;

  const updateRandomCode = () => {
    const random = `info-water${Math.random() * 10000}`;
    setRandomString(random);
  };

  const handleFinish = async (values: {
    username: string;
    password: string;
    captcha: string;
  }) => {
    const { username, password, captcha } = values;
    onFinish(username, password, randomString, captcha);
    updateRandomCode();
  };

  const getAuthCodeImage = () =>
    randomString
      ? `${BASE_URL}loginValidPic?valid_pic_key=${randomString}`
      : '';

  useEffect(() => {
    updateRandomCode();
  }, []);

  return (
    <LoginWrapper appMode={appMode}>
      <div className="login-content">
        <div className="logo">
          <img
            alt={systemName}
            src={`${SYSTEM_ICON_BASE_PATH}${loginLogoImage}-${theme}`}
            style={{ maxWidth: '80vw' }}
          />
        </div>
        <div
          className="login-subtitle"
          style={{
            color: systemSubTitleColor || token.colorText,
          }}
        >
          {systemSubTitle}
        </div>
        <Form
          size="large"
          name="loginForm"
          onFinish={handleFinish}
          autoComplete="off"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          {LOGIN_VALIDATION ? (
            <Form.Item>
              <Row gutter={8}>
                <Col span={12}>
                  <Form.Item
                    name="captcha"
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请输入验证码',
                      },
                    ]}
                  >
                    <Input placeholder="验证码" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Button onClick={updateRandomCode}>
                    <Image
                      height="100%"
                      src={getAuthCodeImage()}
                      preview={false}
                    />
                  </Button>
                </Col>
              </Row>
            </Form.Item>
          ) : null}

          <Form.Item>
            <Button
              loading={loading}
              style={{ width: '100%' }}
              type="primary"
              htmlType="submit"
            >
              登录
            </Button>
          </Form.Item>
        </Form>
      </div>
    </LoginWrapper>
  );
};

Login.displayName = 'Login';

export default Login;
