/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeviceAssessmentInfo } from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import { Button, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { getColumnSearchProps } from 'src/components/table/column';

interface Props {
  data: DeviceAssessmentInfo[];
  onLocate?: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  onHover?: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
}
const Details = (props: Props) => {
  const { data, onLocate, onHover } = props;

  const handleLocateDevice = (record: DeviceAssessmentInfo) => {
    const { oname, otype, shape } = record;
    if (shape) {
      onLocate?.({
        otype,
        oname,
        shape,
      });
    }
  };

  const handleHover = (record: DeviceAssessmentInfo) => {
    const { oname, otype, shape } = record;
    if (shape) {
      onHover?.({
        otype,
        oname,
        shape,
      });
    }
  };

  const columns: ColumnsType<DeviceAssessmentInfo> = [
    {
      title: '',
      key: 'index',
      width: 40,
      render: (_, __, index) => `${index + 1}`,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      dataIndex: 'deviceName',
      width: '35%',
      ellipsis: true,
      render: (value, record) =>
        record.shape ? (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleLocateDevice(record)}
            onMouseOver={() => handleHover(record)}
            onMouseOut={() => onHover?.(undefined)}
          >
            {value}
          </Button>
        ) : (
          value
        ),
      ...getColumnSearchProps('deviceName'),
    },
    {
      title: '父级类型',
      key: 'parentTypeName',
      dataIndex: 'parentTypeName',
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('parentTypeName'),
    },
    {
      title: '主要原因',
      key: 'mainCase',
      dataIndex: 'mainCase',
      width: '25%',
      ellipsis: true,
      ...getColumnSearchProps('mainCase'),
    },
  ];
  return (
    <Table
      size="small"
      rowKey="id"
      dataSource={data}
      columns={columns}
      pagination={false}
      scroll={{
        scrollToFirstRowOnChange: true,
      }}
    />
  );
};

Details.displayName = 'Details';

export default Details;
