/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AssessmentDataScoreItem,
  AssessmentInfo,
  DashboardFormConfig,
  SearchForm,
} from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import { Button, Collapse, Form, Select, Spin } from 'antd';
import { useMemo, useState } from 'react';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import Details from './details';
import ScoreList from './score-list';
import { DevicesDashboardWrapper, SearchPanelHeader } from './style';
import Summary from './summary';

const { Panel } = Collapse;

interface Props {
  loading?: boolean;
  formConfig:
    | {
        formItemList: SearchForm[];
        formValues: DashboardFormConfig | undefined;
      }
    | undefined;
  setFormConfig: (formValues: DashboardFormConfig | undefined) => void;
  onLocate?: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  onHover?: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
  assessmentData: AssessmentInfo[];
  scoreData: AssessmentDataScoreItem[];
  handleOpenChart: (vprop: string) => void;
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const DevicesDashboard = (props: Props) => {
  const {
    loading,
    formConfig,
    assessmentData,
    scoreData,
    setFormConfig,
    onLocate,
    onHover,
    handleOpenChart,
  } = props;

  const [selectRow, setSelectRow] = useState<AssessmentInfo | null>(null);
  const [showMoreScore, setShowMoreScore] = useState<boolean>(false);

  const handleFormValuesChange = (
    _: string,
    allValues: DashboardFormConfig,
  ) => {
    if (formConfig) {
      setFormConfig(allValues);
    }
  };

  const detailsData = useMemo(() => selectRow?.devices || [], [selectRow]);

  const showScoreData = useMemo(
    () => (scoreData.length > 0 && !showMoreScore ? [scoreData[0]] : scoreData),
    [showMoreScore, scoreData],
  );

  return (
    <DevicesDashboardWrapper>
      <Spin
        spinning={loading ?? false}
        delay={300}
      >
        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>分数</span>
          {scoreData.length > 1 ? (
            <Button
              style={{ padding: 0, height: '22px' }}
              type="link"
              onClick={() => setShowMoreScore((state) => !state)}
            >
              {showMoreScore ? '收起' : '显示更多'}
            </Button>
          ) : null}
        </SpecificPrefixH2>
        <ScoreList
          dataSource={showScoreData}
          handleOpenChart={handleOpenChart}
        />
        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>汇总</span>
        </SpecificPrefixH2>
        <Collapse bordered={false}>
          {formConfig && formConfig.formItemList.length > 0 ? (
            <Panel
              header={<SearchPanelHeader>筛选条件</SearchPanelHeader>}
              key="search"
              className="collapse-custom-panel"
            >
              <Form
                {...formItemLayout}
                name="device-form"
                onValuesChange={handleFormValuesChange}
                initialValues={formConfig.formValues}
              >
                {formConfig.formItemList.map(
                  ({ fieldName, field, multiple, options }) => (
                    <Form.Item
                      key={field}
                      label={fieldName}
                      name={field}
                      style={{ marginBottom: '10px' }}
                    >
                      <Select
                        mode={multiple ? 'multiple' : undefined}
                        options={options}
                      />
                    </Form.Item>
                  ),
                )}
              </Form>
            </Panel>
          ) : null}
        </Collapse>
        <Summary
          data={assessmentData}
          selectRow={selectRow}
          setSelectRow={(selectRow) => setSelectRow(selectRow)}
        />
        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>详情 {selectRow ? `(${selectRow?.statusName})` : ''}</span>
        </SpecificPrefixH2>
        <Details
          data={detailsData}
          onLocate={onLocate}
          onHover={onHover}
        />
      </Spin>
    </DevicesDashboardWrapper>
  );
};

export default DevicesDashboard;
