/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LineChartOutlined } from '@ant-design/icons';
import { AssessmentDataScoreItem } from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import { Button, List } from 'antd';
import HelpIcon from 'src/components/icon/help-icon';

interface Props {
  dataSource: AssessmentDataScoreItem[];
  handleOpenChart: (vprop: string) => void;
}

const ScoreList = (props: Props) => {
  const { dataSource, handleOpenChart } = props;

  return (
    <List
      itemLayout="horizontal"
      dataSource={dataSource}
      renderItem={({ title, score, helpDescription, vprop }) => (
        <List.Item
          style={{ padding: '4px' }}
          actions={[
            <Button
              style={{ padding: 0 }}
              type="link"
              key="chart"
              onClick={() => handleOpenChart(vprop)}
            >
              <LineChartOutlined />
            </Button>,
          ]}
        >
          <List.Item.Meta
            title={
              <span>
                {title}
                {helpDescription ? (
                  <HelpIcon
                    key="help"
                    title={helpDescription}
                  />
                ) : null}
                :{score}
              </span>
            }
          />
        </List.Item>
      )}
    />
  );
};

export default ScoreList;
