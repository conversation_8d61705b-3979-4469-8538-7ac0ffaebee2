/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

export const DevicesDashboardWrapper = styled.div`
  height: calc(100vh - 97px);
  overflow: auto;
  background-color: ${({ theme }) => theme.colorBgBase};
  .collapse-custom-panel {
    > .ant-collapse-header {
      padding: 8px 5px;
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 4px 10px;
      overflow: hidden;
    }
  }
`;

export const SearchPanelHeader = styled.div`
  width: 300px;
  ${EllipsisText}
`;
