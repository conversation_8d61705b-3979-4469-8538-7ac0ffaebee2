/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AssessmentInfo } from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import { Button, Table, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect } from 'react';

const { Text } = Typography;

interface Props {
  data: AssessmentInfo[];
  selectRow: AssessmentInfo | null;
  setSelectRow: (selectRow: AssessmentInfo | null) => void;
}

const Summary = (props: Props) => {
  const { data, selectRow, setSelectRow } = props;
  const handleClickDetails = (record: AssessmentInfo) => {
    setSelectRow(record);
  };
  const columns: ColumnsType<AssessmentInfo> = [
    {
      title: '评级',
      key: 'statusName',
      dataIndex: 'statusName',
    },
    {
      title: '数量',
      key: 'devices',
      dataIndex: 'devices',
      align: 'right',
      render: (devices, record) =>
        record.key === 'summary' ? (
          <Text type="danger">{devices.length}</Text>
        ) : (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleClickDetails(record)}
          >
            {devices.length}
          </Button>
        ),
      sorter: (a, b) => a.devices.length - b.devices.length,
    },
    {
      title: '占比',
      key: 'rate',
      dataIndex: 'rate',
      align: 'right',
      render: (value, record) =>
        record.key === 'summary' ? (
          <Text type="danger">{`${value}%`}</Text>
        ) : (
          `${value}%`
        ),
      sorter: (a, b) => a.rate - b.rate,
    },
  ];

  useEffect(() => {
    const newSelectedRow =
      data.find((item) => item.key === selectRow?.key) ?? data[0];
    setSelectRow(newSelectedRow ?? null);
  }, [data]);

  return (
    <Table
      size="small"
      rowKey="key"
      dataSource={data}
      columns={columns}
      pagination={false}
      rowSelection={{
        columnWidth: 0,
        hideSelectAll: true,
        selectedRowKeys: selectRow ? [selectRow.key] : [],
        renderCell: () => null,
      }}
    />
  );
};

Summary.displayName = 'Summary';

export default Summary;
