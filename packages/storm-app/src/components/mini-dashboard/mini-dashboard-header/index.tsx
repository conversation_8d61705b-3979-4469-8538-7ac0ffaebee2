/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CaretLeftOutlined, CaretRightOutlined } from '@ant-design/icons';
import { DefaultOptionType } from 'antd/lib/select';
import { Key, memo, useEffect, useRef, useState } from 'react';
import { MiniDashboardHeaderWrapper } from './style';

interface Props {
  /**
   * header 选项列表 options
   */
  options?: DefaultOptionType[];
  /**
   * options.value
   */
  // value and onChange can use in antd from
  value?: Key;
  /**
   * 左右切换的change事件 ()
   */
  onChange: (value: DefaultOptionType) => void;
}

const MiniDashboardHeader = memo((props: Props) => {
  const { options = [], value, onChange } = props;
  const [titleKey, setTitleKey] = useState<Key | undefined>(value);
  const indexRef = useRef<number>(0);

  const handleCliclkPrev = () => {
    if (indexRef.current === 0) return;
    indexRef.current -= 1;
    setTitleKey(options[indexRef.current].value as Key);
    onChange(options[indexRef.current]);
  };

  const handleClickNext = () => {
    if (indexRef.current === options.length - 1) return;
    indexRef.current += 1;
    setTitleKey(options[indexRef.current].value as Key);
    onChange(options[indexRef.current]);
  };

  const showPrevIcon = options.length && indexRef.current !== 0;
  const showNextIcon =
    options.length && indexRef.current !== options.length - 1;

  useEffect(() => {
    indexRef.current = 0;
  }, [options]);
  return (
    <MiniDashboardHeaderWrapper>
      {showPrevIcon ? (
        <CaretLeftOutlined onClick={() => handleCliclkPrev()} />
      ) : null}
      <span>
        {options.find((item) => item.value === titleKey)?.label || ''}
      </span>
      {showNextIcon ? (
        <CaretRightOutlined onClick={() => handleClickNext()} />
      ) : null}
    </MiniDashboardHeaderWrapper>
  );
});

MiniDashboardHeader.displayName = 'MiniDashboardHeader';

export default MiniDashboardHeader;
