/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MessageInfo } from '@waterdesk/data/mini-dashboard/running-state-data';
import { List } from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useEffect, useState } from 'react';
import { ListContent, ListTitle, ListWrapper } from './style';

dayjs.extend(relativeTime);

interface Props {
  data: MessageInfo[];
}

const MessageList = (props: Props) => {
  const { data } = props;

  const [showAnimateClassName, setShowAnimateClassName] =
    useState<boolean>(false);

  useEffect(() => {
    setShowAnimateClassName(true);
    setTimeout(() => setShowAnimateClassName(false), 5000);
  }, [data]);

  return (
    <ListWrapper
      size="small"
      pagination={false}
      dataSource={data}
      renderItem={(item) => (
        <List.Item
          className={
            item.isNew && showAnimateClassName
              ? `animate-background-${item.id}`
              : undefined
          }
          style={{ display: 'block' }}
        >
          <List.Item.Meta
            title={
              <ListTitle>
                <h4
                  title={item.title}
                  className="message-title"
                >
                  {item.title}
                </h4>
                <span className="time">{dayjs(item.createTime).fromNow()}</span>
              </ListTitle>
            }
          />
          <ListContent title={item.content}>{item.content}</ListContent>
        </List.Item>
      )}
    />
  );
};

MessageList.displayName = 'MessageList';

export default MessageList;
