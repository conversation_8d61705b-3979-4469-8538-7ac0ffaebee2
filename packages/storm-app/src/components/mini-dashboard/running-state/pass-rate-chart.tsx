/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MODELSCORE,
  SeriesData,
} from '@waterdesk/data/mini-dashboard/running-state-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import dayjs from 'dayjs';
import { LineChart, LineSeriesOption } from 'echarts/charts';
import {
  DatasetComponent,
  DatasetComponentOption,
  GridComponent,
  GridComponentOption,
  TitleComponent,
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  TransformComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { CSSProperties, useMemo } from 'react';
import { WrappedReactECharts } from 'src/components/charts/react-echarts';

echarts.use([
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  TransformComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

type EChartsOption = echarts.ComposeOption<
  | DatasetComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LineSeriesOption
>;
interface Props {
  /** string as YYYY-MM-DD  */
  date: string;
  style?: CSSProperties;
  seriesData: SeriesData[];
  unitFormat?: UnitFormat;
  yMaxValue?: number;
}

const PassRateChart = (props: Props) => {
  const { date, style, seriesData, unitFormat, yMaxValue } = props;

  const tooltipValueFormatter = (value: any): string => {
    if (unitFormat !== undefined) return unitFormat.getValueWithSymbol(value);
    return value;
  };

  const getSeriesData = (seriesData: SeriesData[]): EChartsOption['series'] =>
    seriesData.map((item): LineSeriesOption => {
      const { name, data, key } = item;
      return {
        name,
        type: 'line',
        symbol: 'none',
        data: data.map(({ time, value }) => [time, value]),
        markLine:
          key === MODELSCORE
            ? undefined
            : {
                symbol: 'none',
                animation: false,
                label: {
                  position: 'insideEnd',
                  formatter: (params) => tooltipValueFormatter(params.value),
                },
                data: [
                  {
                    lineStyle: {
                      color: '#ff0000',
                    },
                    yAxis: 0.9,
                  },
                ],
              },
      };
    });

  const isYAxisValues = (unitFormat: UnitFormat, value: number): boolean => {
    const yAxisValues = unitFormat.getYAxisValues();
    for (let i = 0; i < yAxisValues.length; i += 1) {
      const yValue = yAxisValues[i];
      if (typeof yValue === 'number' && yValue === value) return true;
      if (typeof yValue === 'string' && yValue === value.toString())
        return true;
    }

    return false;
  };

  const yAxisLabelFormatter = (
    unitFormat: UnitFormat | undefined,
    val: number,
  ): string => {
    if (unitFormat) {
      if (unitFormat.unitType === 'E' && !isYAxisValues(unitFormat, val))
        return '';

      const v = unitFormat.getValue(val);
      if (v !== undefined) return v.toString();
    }

    return `${val}`;
  };

  const options: EChartsOption = useMemo(() => {
    const yAxisValues = unitFormat?.getYAxisValues();
    let yMax;
    if (yMaxValue) {
      yMax = yMaxValue;
    } else if (yAxisValues && yAxisValues.length > 0) {
      yMax = yAxisValues[yAxisValues.length - 1];
    }
    const defaultOption: EChartsOption = {
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        valueFormatter: (value) => tooltipValueFormatter(value),
        position: (point) => [point[0], point[1]],
      },
      grid: {
        top: '5%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        min: dayjs(date).startOf('day').format('YYYY-MM-DD 00:00:00'),
        max: dayjs(date)
          .add(1, 'day')
          .startOf('day')
          .format('YYYY-MM-DD 00:00:00'),
        axisLabel: {
          formatter(value) {
            return dayjs(value).format('H:mm');
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        axisPointer: {
          label: {
            formatter(params: any) {
              return dayjs(params.value).format('MM-DD H:mm');
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter(val) {
            return yAxisLabelFormatter(unitFormat, Number(val));
          },
        },
        max: yMax,
        min: 0,
        // @ts-ignore
        data: unitFormat?.getYAxisValues(),
      },
    };

    defaultOption.series = getSeriesData(seriesData);

    return defaultOption;
  }, [seriesData, date, unitFormat, yMaxValue]);

  return (
    <WrappedReactECharts
      style={{ height: '130px', width: '150px', ...style }}
      option={options}
    />
  );
};

PassRateChart.displayName = 'PassRateChart';

export default PassRateChart;
