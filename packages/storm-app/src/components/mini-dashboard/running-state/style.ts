/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MessageInfo } from '@waterdesk/data/mini-dashboard/running-state-data';
import { List } from 'antd';
import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

export const ListWrapper = styled(List<MessageInfo>)`
  @keyframes animateBackground {
    0% {
      background-color: ${({ theme }) => `${theme.colorPrimary}00`};
    }

    50% {
      background-color: ${({ theme }) => `${theme.colorPrimary}80`};
    }

    100% {
      background-color: ${({ theme }) => `${theme.colorPrimary}00`};
    }
  }

  li[class*='animate-background'] {
    animation: 1.5s ease-in-out animateBackground 2;
  }
`;

export const ListTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  .message-title {
    flex: 0 1 auto;
    margin: 0;
    ${EllipsisText}
  }
  .time {
    flex: 1 0 auto;
    margin-left: 15px;
    color: ${({ theme }) => theme.colorTextDescription};
  }
`;

export const ListContent = styled.div`
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;
