/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import dayjs from 'dayjs';
import { LineChart, LineSeriesOption } from 'echarts/charts';
import {
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TitleComponentOption,
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { CSSProperties, useMemo } from 'react';
import { WrappedReactECharts } from 'src/components/charts/react-echarts';

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

type EChartsOption = echarts.ComposeOption<
  | TitleComponentOption
  | ToolboxComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | LineSeriesOption
>;

interface Props {
  style?: CSSProperties;
  propertyName?: string;
  /** string as YYYY-MM-DD  */
  date: string;
  yesterdayData: TimeData[];
  todayData: TimeData[];
  predictData?: TimeData[];
  unitFormat?: UnitFormat;
  showYTitle?: boolean;
}

const WaterSupplyChart = (props: Props) => {
  const { token } = useToken();
  const {
    date,
    yesterdayData,
    todayData,
    predictData,
    unitFormat,
    style,
    propertyName,
    showYTitle,
  } = props;
  const tooltipValueFormatter = (value: any): string => {
    if (unitFormat !== undefined) return unitFormat.getValueWithSymbol(value);
    return value;
  };

  const formatYesterdayTime = (time: string, date: string): string =>
    `${date} ${dayjs(time).format('HH:mm:ss')}`;

  const yAxisName = useMemo(
    () =>
      `${propertyName} ${
        unitFormat?.unitSymbol ? `(${unitFormat?.unitSymbol})` : ''
      }`,
    [propertyName, unitFormat],
  );

  const options: EChartsOption = useMemo(() => {
    const isCurrentDay = dayjs(date).isSame(dayjs(), 'date');
    const chartOptions: EChartsOption = {
      dataZoom: {
        type: 'inside',
      },
      title: {
        show: false,
        text: '供水量',
      },
      tooltip: {
        trigger: 'axis',
        position: (point) => [point[0], point[1]],
        valueFormatter: (value) => tooltipValueFormatter(value),
        confine: true,
      },
      grid: {
        top: showYTitle ? '20%' : '5%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        min: dayjs(date).startOf('day').format('YYYY-MM-DD 00:00:00'),
        max: dayjs(date)
          .add(1, 'day')
          .startOf('day')
          .format('YYYY-MM-DD 00:00:00'),
        axisLabel: {
          formatter(value: number) {
            return dayjs(value).format('H:mm');
          },
          showMinLabel: true,
          showMaxLabel: true,
        },
        axisPointer: {
          label: {
            formatter(params) {
              return dayjs(params.value).format('H:mm');
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        nameGap: 5,
        show: true,
        name: yAxisName,
        min(value) {
          return Number(value.min * 0.9).toFixed(0);
        },
      },
      series: [
        {
          name: dayjs(date).add(-1, 'day').format('MM-DD'),
          type: 'line',
          itemStyle: {
            color: '#00000080',
          },
          lineStyle: {
            type: 'dashed',
            color: token.colorTextSecondary,
          },
          symbol: 'none',
          data: yesterdayData.map(({ time, value }) => [
            formatYesterdayTime(time, date),
            value,
          ]),
          zlevel: 1,
        },
        {
          name: dayjs(date).format('MM-DD'),
          type: 'line',
          itemStyle: {
            color: '#3aa4db',
          },
          symbol: 'none',
          data: todayData.map(({ time, value }) => [time, value]),
          zlevel: 3,
        },
        {
          name: '预测值',
          type: 'line',
          itemStyle: {
            color: '#e3ba4d',
          },
          symbol: 'none',
          data: predictData?.map(({ time, value }) => [time, value]),
          zlevel: 2,
        },
      ],
    };

    if (isCurrentDay) {
      const startDateTime = dayjs(
        dayjs(date).format('YYYY-MM-DD 00:00:00'),
      ).valueOf();
      const currentDateTime = dayjs().valueOf();

      chartOptions.visualMap = {
        show: false,
        dimension: 0,
        seriesIndex: 1,
        pieces: [
          {
            gte: startDateTime,
            lte: currentDateTime,
            color: '#3aa4db',
          },
          {
            gt: currentDateTime,
            color: '#91cc75',
          },
        ],
      };
    }

    return chartOptions;
  }, [
    date,
    todayData,
    yesterdayData,
    predictData,
    propertyName,
    unitFormat,
    token,
  ]);

  return (
    <WrappedReactECharts
      style={{ height: '130px', width: '150px', ...style }}
      option={options}
    />
  );
};

WaterSupplyChart.displayName = 'WaterSupplyChart';

export default WaterSupplyChart;
