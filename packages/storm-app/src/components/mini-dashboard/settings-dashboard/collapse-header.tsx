/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Checkbox } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import type { ReactNode } from 'react';

interface Props {
  title: string | ReactNode;
  checked?: boolean | null;
  onChange: (checked: boolean) => void;
}

const CollapseHeader = (props: Props) => {
  const { title, checked, onChange } = props;

  const handleChange = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    onChange(checked);
  };

  if (typeof checked === 'undefined') return <span>{title}</span>;

  return (
    <Checkbox
      checked={!!checked}
      indeterminate={checked === null}
      onChange={handleChange}
      onClick={(e) => e.stopPropagation()}
    >
      <span
        onKeyDown={() => {}}
        onClick={(e) => e.stopPropagation()}
        role="button"
        tabIndex={0}
      >
        {title}
      </span>
    </Checkbox>
  );
};

CollapseHeader.displayName = 'CollapseHeader';

export default CollapseHeader;
