/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable no-eval */
import {
  getLayerIsAllVisible,
  getLayerNames,
  LayerState,
  ThemeItem,
  ThemeSection,
} from '@waterdesk/data/scene';
import { DefaultOptionType } from '@waterdesk/data/types';
import { Checkbox, Col, Collapse, Empty, Row, Select } from 'antd';
import { Key } from 'react';
import CollapseHeader from './collapse-header';

const { Panel } = Collapse;

interface Props {
  settingsList: ThemeSection[];
  defaultActiveKey?: string[];
  onLayerChange: (layerNames: string[], checked: boolean, type: string) => void;
  onThemeChange: (
    value: Key,
    option: DefaultOptionType<string>,
    type: string,
  ) => void;
}

const SettingsDashboard = (props: Props) => {
  const {
    settingsList,
    defaultActiveKey = [],
    onLayerChange,
    onThemeChange,
  } = props;
  const generateExtraSelect = (
    type: string,
    options: ThemeItem[],
    activeTheme: ThemeItem | undefined,
  ) => {
    if (!options.length) return null;
    return (
      <div>
        <Select
          style={{ width: '140px' }}
          size="small"
          options={options.map(({ name, title }) => ({
            label: title,
            value: name,
          }))}
          value={activeTheme?.name}
          onClick={(e) => e.stopPropagation()}
          onChange={(value, option) =>
            onThemeChange(value, option as DefaultOptionType<string>, type)
          }
        />
      </div>
    );
  };

  const getCollapseHeaderChecked = (
    layerStates: LayerState[],
  ): boolean | null | undefined => {
    if (layerStates.length === 0) return undefined;
    return getLayerIsAllVisible(layerStates);
  };

  return (
    <div>
      {settingsList.length ? (
        <Collapse defaultActiveKey={defaultActiveKey}>
          {settingsList.map((settingsItem: ThemeSection) => (
            <Panel
              header={
                <CollapseHeader
                  title={settingsItem.title}
                  checked={getCollapseHeaderChecked(settingsItem.layerStates)}
                  onChange={(checked: boolean) =>
                    onLayerChange(
                      getLayerNames(settingsItem.layerStates),
                      checked,
                      settingsItem.type,
                    )
                  }
                />
              }
              key={settingsItem.type}
              extra={generateExtraSelect(
                settingsItem.type,
                settingsItem.themeItems,
                settingsItem.currentThemeItem,
              )}
            >
              <Row>
                {settingsItem.layerStates.map((layerState) => (
                  <Col
                    span={12}
                    key={layerState.name}
                  >
                    <Checkbox
                      value={layerState.name}
                      checked={layerState.visible}
                      onChange={(e) =>
                        onLayerChange(
                          [e.target.value],
                          e.target.checked,
                          settingsItem.type,
                        )
                      }
                    >
                      {layerState.icon ? (
                        <span
                          style={{
                            paddingRight: '4px',
                            fontFamily: 'iconfont',
                          }}
                        >
                          {eval(`'${layerState.icon}'`)}
                        </span>
                      ) : null}

                      {layerState.title}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Panel>
          ))}
        </Collapse>
      ) : (
        <Empty style={{ margin: '50% 0' }} />
      )}
    </div>
  );
};

SettingsDashboard.displayName = 'SettingsDashboard';

export default SettingsDashboard;
