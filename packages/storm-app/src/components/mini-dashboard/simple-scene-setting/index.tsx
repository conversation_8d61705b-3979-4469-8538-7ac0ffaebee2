/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable no-eval */
import { ThemeItem, ThemeSection } from '@waterdesk/data/scene';
import { DefaultOptionType } from '@waterdesk/data/types';
import { Button, Col, Divider, Empty, Radio, Row } from 'antd';
import { Key } from 'react';
import { SimpleSceneSettingWrapper } from './style';

interface Props {
  settingsList: ThemeSection[];
  onLayerChange: (layerNames: string[], checked: boolean, type: string) => void;
  onThemeChange: (
    value: Key,
    option: DefaultOptionType<string>,
    type: string,
  ) => void;
}

const SimpleLayerThemeSetting = (props: Props) => {
  const { settingsList, onLayerChange, onThemeChange } = props;
  const generateExtraSelect = (
    type: string,
    options: ThemeItem[],
    activeTheme: ThemeItem | undefined,
  ) => {
    if (!options.length) return null;
    return (
      <Radio.Group
        value={activeTheme?.name}
        buttonStyle="solid"
        size="small"
        className="themeButtonGroup"
        onChange={(e) => {
          const { value } = e.target;
          const option = {
            value,
            label: options.find((item) => item.name === value)?.title,
          };
          onThemeChange(value, option as DefaultOptionType<string>, type);
        }}
      >
        {options.map(({ name, title }) => (
          <Radio.Button
            className="themeButton"
            value={name}
            key={name}
          >
            {title}
          </Radio.Button>
        ))}
      </Radio.Group>
    );
  };

  return (
    <SimpleSceneSettingWrapper>
      <div>
        {settingsList.length ? (
          <div>
            <Divider style={{ margin: 0, fontWeight: 'bold' }}>
              图层管理
            </Divider>
            {settingsList.map((settingsItem: ThemeSection) => (
              <Row
                gutter={[0, 0]}
                key={settingsItem.type}
                className="section"
              >
                {settingsItem.layerStates.length > 0 ? (
                  <Col>
                    <h2 className="title">{settingsItem.title}</h2>
                  </Col>
                ) : null}
                {settingsItem.layerStates.map((layerState) => (
                  <Col
                    span={24}
                    key={layerState.name}
                  >
                    <Button
                      className="layerButton"
                      size="small"
                      type={layerState.visible ? 'primary' : undefined}
                      onClick={() =>
                        onLayerChange(
                          [layerState.name],
                          !layerState.visible,
                          settingsItem.type,
                        )
                      }
                    >
                      {layerState.icon ? (
                        <span className="icon">
                          {eval(`'${layerState.icon}'`)}
                        </span>
                      ) : null}

                      {layerState.title}
                    </Button>
                  </Col>
                ))}
              </Row>
            ))}
            <Divider style={{ margin: 0, fontWeight: 'bold' }}>
              主题管理
            </Divider>
            {settingsList.map((settingsItem: ThemeSection) => (
              <Row
                key={`${settingsItem.type}theme`}
                className="section"
              >
                {settingsItem.themeItems.length > 0 ? (
                  <Col>
                    <h2 className="title">{settingsItem.title}</h2>
                  </Col>
                ) : null}
                {generateExtraSelect(
                  settingsItem.type,
                  settingsItem.themeItems,
                  settingsItem.currentThemeItem,
                )}
              </Row>
            ))}
          </div>
        ) : (
          <Empty style={{ margin: '50% 0' }} />
        )}
      </div>
    </SimpleSceneSettingWrapper>
  );
};

SimpleLayerThemeSetting.displayName = 'SimpleLayerThemeSetting';
export default SimpleLayerThemeSetting;
