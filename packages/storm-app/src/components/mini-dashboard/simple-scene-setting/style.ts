/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BeforePrefix } from 'src/styles/common-style';
import styled from 'styled-components';

export const SimpleSceneSettingWrapper = styled.div`
  .section {
    padding-bottom: 6px;
    .title {
      position: relative;
      margin: 0;
      font-size: 14px;
      line-height: 18px;
      padding: 3px 0 3px 10px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: left;
      &::before {
        ${BeforePrefix}
      }
    }

    .layerButton {
      width: 100%;
      margin: 1px;
      text-align: left;
      .icon {
        padding-right: 4px;
        font-family: iconfont;
      }
    }
    .themeButtonGroup {
      .themeButton {
        width: 100%;
        margin: 1px;
        text-align: left;
        border-radius: 4px;
        border-left-width: 1px;

        &::before {
          content: none;
        }
      }
    }
  }
`;
