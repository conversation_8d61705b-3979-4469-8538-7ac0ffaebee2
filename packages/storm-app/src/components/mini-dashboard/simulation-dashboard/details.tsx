/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AssessmentInfo,
  getColumnTitleByOtype,
  IndicatorsValue,
} from '@waterdesk/data/mini-dashboard/simulation-dashboard-data';
import { compareNumbers } from '@waterdesk/data/utils';
import { Button, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { getColumnSearchProps } from 'src/components/table/column';

interface Props {
  data: AssessmentInfo | null;
  onLocate?: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  onHover?: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
}
const Details = (props: Props) => {
  const { data, onLocate, onHover } = props;

  const handleLocateDevice = (record: IndicatorsValue) => {
    const { deviceOname, deviceOtype, shape } = record;
    if (shape && deviceOname && deviceOtype) {
      onLocate?.({
        otype: deviceOtype,
        oname: deviceOname,
        shape,
      });
    }
  };

  const handleHover = (record: IndicatorsValue) => {
    const { deviceOname, deviceOtype, shape } = record;
    if (shape && deviceOname && deviceOtype) {
      onHover?.({
        otype: deviceOtype,
        oname: deviceOname,
        shape,
      });
    }
  };

  const columns: ColumnsType<IndicatorsValue> = [
    {
      title: '',
      key: 'index',
      width: 40,
      render: (_, __, index) => `${index + 1}`,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      dataIndex: 'deviceName',
      width: '60%',
      ellipsis: true,
      render: (value, record) =>
        record.shape ? (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleLocateDevice(record)}
            onMouseOver={() => handleHover(record)}
            onMouseOut={() => onHover?.(undefined)}
          >
            {value}
          </Button>
        ) : (
          value
        ),
      ...getColumnSearchProps('deviceName'),
    },
    {
      title: getColumnTitleByOtype(data?.type),
      key: 'meanAbsoluteErrorText',
      dataIndex: 'meanAbsoluteErrorText',
      align: 'right',
      ellipsis: true,
      sorter: (a, b) =>
        compareNumbers(a.meanAbsoluteError, b.meanAbsoluteError),
      defaultSortOrder: 'descend',
      render: (text) => text ?? '-',
    },
  ];
  return (
    <Table
      size="small"
      dataSource={data?.values ?? []}
      columns={columns}
      pagination={false}
      scroll={{
        scrollToFirstRowOnChange: true,
      }}
    />
  );
};

Details.displayName = 'Details';

export default Details;
