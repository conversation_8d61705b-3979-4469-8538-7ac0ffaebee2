/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AssessmentDataScoreItem,
  SearchForm,
} from '@waterdesk/data/mini-dashboard/device-dashboard-data';
import {
  AssessmentInfo,
  DashboardFormConfig,
  SimulationData,
} from '@waterdesk/data/mini-dashboard/simulation-dashboard-data';
import { Button, Collapse, Form, Select, Spin } from 'antd';
import { useMemo, useState } from 'react';
import HelpIcon from 'src/components/icon/help-icon';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import ScoreList from '../devices-dashboard/score-list';
import { SearchPanelHeader } from '../devices-dashboard/style';
import Details from './details';
import { SimulationDashboardWrapper } from './style';
import Summary from './summary';

const { Panel } = Collapse;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

interface Props {
  loading?: boolean;
  formConfig:
    | {
        formItemList: SearchForm[];
        formValues: DashboardFormConfig | undefined;
      }
    | undefined;
  setFormConfig: (formValues: DashboardFormConfig | undefined) => void;
  onLocate?: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  onHover?: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
  handleOpenChart: (vprop: string) => void;
  simulationData: SimulationData;
  scoreData: AssessmentDataScoreItem[];
}

const SimulationDashboard = (props: Props) => {
  const {
    loading,
    simulationData,
    scoreData,
    formConfig,
    setFormConfig,
    onLocate,
    onHover,
    handleOpenChart,
  } = props;

  const [activeKey, setActiveKey] = useState<string | undefined>(undefined);
  const [selectRow, setSelectRow] = useState<AssessmentInfo | null>(null);
  const [showMoreScore, setShowMoreScore] = useState<boolean>(false);

  const handleFormValuesChange = (
    _: string,
    allValues: DashboardFormConfig,
  ) => {
    if (formConfig) {
      setFormConfig(allValues);
    }
  };

  const getSummaryExtra = () => {
    const activeItem = simulationData.list.find(
      (item) => item.key === activeKey,
    );
    if (activeItem?.enableStandard) {
      return (
        <span
          style={{
            color: activeItem.isReachStandard ? 'green' : 'red',
            fontWeight: '800',
          }}
        >
          {activeItem.isReachStandard ? '满足标准' : '不满足标准'}
          {activeItem.helpDescription ? (
            <HelpIcon title={activeItem.helpDescription} />
          ) : null}
        </span>
      );
    }
    return null;
  };

  const showScoreData = useMemo(
    () => (scoreData.length > 0 && !showMoreScore ? [scoreData[0]] : scoreData),
    [showMoreScore, scoreData],
  );

  return (
    <Spin
      spinning={loading ?? false}
      delay={300}
    >
      <SimulationDashboardWrapper>
        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>分数</span>
          {scoreData.length > 1 ? (
            <Button
              style={{ padding: 0, height: '22px' }}
              type="link"
              onClick={() => setShowMoreScore((state) => !state)}
            >
              {showMoreScore ? '收起' : '显示更多'}
            </Button>
          ) : null}
        </SpecificPrefixH2>
        <ScoreList
          handleOpenChart={handleOpenChart}
          dataSource={showScoreData}
        />

        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>汇总</span>
          <span>{getSummaryExtra()}</span>
        </SpecificPrefixH2>
        <Collapse bordered={false}>
          {formConfig && formConfig.formItemList.length > 0 ? (
            <Panel
              header={<SearchPanelHeader>筛选条件</SearchPanelHeader>}
              key="search"
              className="collapse-custom-panel"
            >
              <Form
                {...formItemLayout}
                name="simulation-form"
                onValuesChange={handleFormValuesChange}
                initialValues={formConfig.formValues}
              >
                {formConfig.formItemList.map(
                  ({ fieldName, field, multiple, options }) => (
                    <Form.Item
                      key={field}
                      label={fieldName}
                      name={field}
                      style={{ marginBottom: '10px' }}
                    >
                      <Select
                        mode={multiple ? 'multiple' : undefined}
                        options={options}
                      />
                    </Form.Item>
                  ),
                )}
              </Form>
            </Panel>
          ) : null}
        </Collapse>
        <Summary
          activeKey={activeKey}
          data={simulationData.list}
          selectRow={selectRow}
          setSelectRow={(selectRow) => setSelectRow(selectRow)}
          setActiveKey={(key) => setActiveKey(key)}
        />
        <SpecificPrefixH2 style={{ margin: '4px' }}>
          <span>
            详情
            {selectRow
              ? `(${selectRow?.indicatorTitle}:${selectRow?.meanAbsoluteErrorTitle})`
              : ''}
          </span>
        </SpecificPrefixH2>
        <Details
          data={selectRow}
          onLocate={onLocate}
          onHover={onHover}
        />
      </SimulationDashboardWrapper>
    </Spin>
  );
};

export default SimulationDashboard;
