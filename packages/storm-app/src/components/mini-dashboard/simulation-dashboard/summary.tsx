/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AssessmentInfo,
  getColumnTitleByOtype,
} from '@waterdesk/data/mini-dashboard/simulation-dashboard-data';
import { compareNumbers } from '@waterdesk/data/utils';
import { Button, Radio, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useMemo } from 'react';

interface Props {
  data: AssessmentInfo[];
  activeKey: string | undefined;
  selectRow: AssessmentInfo | null;
  setSelectRow: (selectRow: AssessmentInfo | null) => void;
  setActiveKey: (activeKey: string) => void;
}

const Summary = (props: Props) => {
  const { data, selectRow, activeKey, setActiveKey, setSelectRow } = props;

  const handleClickDetails = (record: AssessmentInfo) => {
    setSelectRow(record);
  };
  const columns: ColumnsType<AssessmentInfo> = useMemo(
    () => [
      {
        title: getColumnTitleByOtype(
          data.find((item) => item.key === activeKey)?.type,
        ),
        key: 'meanAbsoluteErrorTitle',
        dataIndex: 'meanAbsoluteErrorTitle',
        render: (text, record) => record.meanAbsoluteErrorDescription ?? text,
      },
      {
        title: '数量',
        key: 'values',
        dataIndex: 'values',
        align: 'right',
        width: 80,
        render: (value, record) =>
          value?.length ? (
            <Button
              style={{ padding: '0', height: '22px' }}
              type="link"
              onClick={() => handleClickDetails(record)}
            >
              {value.length}
            </Button>
          ) : (
            0
          ),
        sorter: (a, b) => compareNumbers(a.values?.length, b.values?.length),
      },
      {
        title: '占比',
        key: 'rate',
        dataIndex: 'rate',
        align: 'right',
        width: 80,
        render: (value) => (typeof value === 'number' ? `${value}%` : ''),
        sorter: (a, b) => compareNumbers(a.rate, b.rate),
      },
    ],
    [activeKey],
  );

  const options = useMemo(() => {
    const options = data.map((item) => ({
      label: `${item.prefixTitle}${item.indicatorTitle}`,
      value: item.key,
    }));
    if (options.length > 0 && typeof activeKey === 'undefined') {
      setActiveKey(options[0].value);
    }
    return options;
  }, [data]);

  const tableData = useMemo(() => {
    const newData = data.find((item) => item.key === activeKey)?.children || [];
    return newData;
  }, [activeKey, data]);

  useEffect(() => {
    const newSelectRow =
      tableData.find(
        (item) =>
          item.meanAbsoluteErrorTitle === selectRow?.meanAbsoluteErrorTitle,
      ) ?? tableData[0];
    setSelectRow(newSelectRow ?? null);
  }, [tableData]);

  return (
    <>
      <Radio.Group
        options={options}
        onChange={(e) => {
          setSelectRow(null);
          setActiveKey(e.target.value);
        }}
        value={activeKey}
        optionType="button"
        style={{ margin: '4px' }}
      />
      <Table
        size="small"
        rowKey="key"
        dataSource={tableData}
        columns={columns}
        pagination={false}
        rowSelection={{
          columnWidth: 0,
          hideSelectAll: true,
          selectedRowKeys: selectRow ? [selectRow.key] : [],
          renderCell: () => null,
        }}
        scroll={{
          y: 160,
        }}
      />
    </>
  );
};

Summary.displayName = 'Summary';

export default Summary;
