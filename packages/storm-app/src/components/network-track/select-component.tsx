/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MinusCircleOutlined } from '@ant-design/icons';
import Device from '@waterdesk/data/device';
import ModelObject from '@waterdesk/data/model-object';
import { ObjectFormItem } from '@waterdesk/data/track-data';
import { Space } from 'antd';
import { useEffect, useState } from 'react';

interface Props {
  single?: boolean;
  minFlowRatio: number;
  minDiameterRatio: number;
  selectObjects: ObjectFormItem[];
  selectable: boolean;
  setMinFlow: (flow: number) => void;
  setMinDiameter: (diameter: number) => void;
  setObjects: (objects: ObjectFormItem[]) => void;
  onRemoveObject?: (index: number) => void;
}

export default function SelectContainer(props: Props) {
  const {
    single,
    minFlowRatio,
    minDiameterRatio,
    selectObjects,
    selectable,
    setMinFlow,
    setMinDiameter,
    setObjects,
    onRemoveObject,
  } = props;

  const [objectFormData, setObjectFormData] = useState<ObjectFormItem[]>([]);

  const getObjectInfo = (currentItem: ObjectFormItem) => {
    if (currentItem instanceof Device || currentItem instanceof ModelObject) {
      setMinFlow(0 * minFlowRatio);
      setMinDiameter(0 * minDiameterRatio);
    }
  };

  useEffect(() => {
    if (!selectable) return;
    let currentObject = selectObjects;
    const selectObjectLength = currentObject.length;
    if (single) {
      currentObject = [currentObject[selectObjectLength - 1]];
      getObjectInfo(currentObject[0]);
    }
    if (currentObject[0]) {
      setObjects(currentObject);
      setObjectFormData(currentObject);
    } else {
      setObjectFormData([]);
    }
  }, [selectObjects]);

  const removeObjectForm = (index: number) => {
    const updatedData = objectFormData.filter((_, i) => i !== index);
    setObjectFormData(updatedData);
    onRemoveObject?.(index);
  };

  return (
    <div
      className="objectItems"
      style={{
        maxHeight: '500px',
        overflow: 'auto',
      }}
    >
      {objectFormData.map((item, index) => (
        <Space
          key={item.key}
          align="baseline"
        >
          <div style={{ marginBottom: 20, width: 300 }}>
            {index === 0 ? '追踪对象:' : `追踪对象${index + 1}:`}
            <span style={{ marginLeft: 20 }}>
              {item.otypeTitle} {item.title}
            </span>
          </div>
          {!single && (
            <MinusCircleOutlined
              onClick={() => {
                removeObjectForm(index);
              }}
            />
          )}
        </Space>
      ))}
    </div>
  );
}
