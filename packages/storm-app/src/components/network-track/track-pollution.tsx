/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  LockOutlined,
  MinusCircleOutlined,
  UnlockOutlined,
} from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  HIGHLIGHT_POLLUTED_TRACK_SELECT,
  HIGHLIGHT_UNPOLLUTED_TRACK_SELECT,
} from '@waterdesk/data/style-config';
import {
  generateHighlightObject,
  ObjectFormItem,
  PollutionResultData,
  ResultData,
  SELECT_ICON,
  TRACK_POLLUTION,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import {
  Button,
  Collapse,
  Form,
  InputNumber,
  message,
  Space,
  Tabs,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { curDb } from 'src/app/host-app';
import IconText from '../icon-font/icon-text';
import TrackResult from './track-result';

interface FormItem {
  otypeOnames: ObjectFormItem[];
  direction: string;
  minFlow: number;
  minDiameter: number;
}

function existObject(
  currentObject: ObjectFormItem,
  objects: ObjectFormItem[],
): boolean {
  const objectIndex = objects.findIndex(
    (item) =>
      item.otype + item.oname === currentObject.otype + currentObject.oname,
  );
  return objectIndex > -1;
}

interface Props {
  dimension: boolean;
  onResetHighlight: () => void;
  positionToMapView: (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => void;
  handleHover: (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => void;
  highlight: (
    highlightObject: {
      selectedPollutedObject?: HighlightObject[];
      selectedUnPollutedObject?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
    },
    themeConfig?: {
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => void;
  getPollutionTrack: (
    oname: string,
    otype: string,
    minDiameter: number,
    minFLow: number,
  ) => Promise<PollutionResultData[]>;
  getObject: () => ObjectFormItem | undefined;
  trackObjects: ObjectFormItem[];
  trackResultConfig: (type: TrackType) => TrackConfig | undefined;
  getDmaSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme?: LegendGroupData;
      }
    | undefined;
  getLinkSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme: LegendGroupData;
      }
    | undefined;
  convertObjectsByGIS: (
    originObjects?: { otype: string; oname: string; [index: string]: any }[],
  ) =>
    | { otype: string; oname: string; shape: string | undefined }[]
    | undefined;
}

export default function TrackPollution(props: Readonly<Props>) {
  const {
    dimension,
    onResetHighlight,
    positionToMapView,
    handleHover,
    highlight,
    getPollutionTrack,
    getObject,
    trackObjects: selectObjects,
    trackResultConfig,
    getDmaSwitchData,
    getLinkSwitchData,
    convertObjectsByGIS,
  } = props;

  const [selectable, setSelectable] = useState<boolean>(true);
  const [messageApi, contextHolder] = message.useMessage();
  const [modalForm] = Form.useForm<FormItem>();

  const { token } = useToken();
  const [pollutedObjectFormData, setPollutedObjectFormData] = useState<
    ObjectFormItem[]
  >([]);
  const [resultData, setResultData] = useState<ResultData[]>([]);

  const [insertObjectType, setInsertObjectType] = useState<
    'POLLUTED' | 'UNPOLLUTED'
  >('POLLUTED');

  const [unPollutedObjectsForm] = Form.useForm<{
    otypeOnames: ObjectFormItem[];
  }>();
  const [unPollutedObjectFormData, setUnPollutedObjectFormData] = useState<
    ObjectFormItem[]
  >([]);
  const [activeTabItem, setActiveTabItem] = useState<'form' | 'result'>('form');

  const getTrackConfig = () => trackResultConfig(TRACK_POLLUTION);

  const highlightObjects = (
    highlightObject: {
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
    },
    themeConfig?: {
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => {
    highlight(
      {
        ...highlightObject,
      },
      themeConfig,
    );
  };

  const getSelectedObject = (type: string) => {
    const selectedObject = getObject();
    if (selectedObject) {
      const convertObject = convertObjectsByGIS?.([
        { oname: selectedObject.oname, otype: selectedObject.otype },
      ])?.[0];
      if (type === 'POLLUTED') {
        if (existObject(selectedObject, pollutedObjectFormData)) {
          return;
        }
        if (convertObject) {
          setPollutedObjectFormData((prevState) => {
            const pollutedObject = [
              ...prevState,
              {
                oname: selectedObject.oname,
                otype: selectedObject.otype,
                shape: selectedObject.shape ?? '',
                otypeTitle: selectedObject.otypeTitle,
                title: selectedObject.title,
                key: selectedObject.oname + (prevState.length + 1),
                name: pollutedObjectFormData.length + 1,
                gisOtype: convertObject.otype,
                gisOname: convertObject.oname,
              },
            ];

            const newPollutedObject = (Array.from(
              new Set(
                pollutedObject.map((item) => `${item.otype}-${item.oname}`),
              ),
            ).map((uniqueKey) =>
              pollutedObject.find(
                (item) => `${item.otype}-${item.oname}` === uniqueKey,
              ),
            ) ?? []) as ObjectFormItem[];

            return newPollutedObject;
          });
        }
      } else {
        if (existObject(selectedObject, unPollutedObjectFormData)) {
          return;
        }
        if (convertObject) {
          setUnPollutedObjectFormData((prevState) => {
            const unPollutedObject = [
              ...prevState,
              {
                oname: selectedObject.oname,
                otype: selectedObject.otype,
                shape: selectedObject.shape ?? '',
                otypeTitle: selectedObject.otypeTitle,
                title: selectedObject.title,
                key: selectedObject.oname + (prevState.length + 1),
                name: unPollutedObjectFormData.length + 1,
                gisOtype: convertObject.otype,
                gisOname: convertObject.oname,
              },
            ];
            return unPollutedObject;
          });
        }
        unPollutedObjectsForm.setFieldsValue({
          otypeOnames: unPollutedObjectFormData,
        });
      }
    }
  };

  const onNetworkTrack = async (params: FormItem) => {
    const { minFlow, minDiameter } = params;
    let minFlowNumber = 0;
    let minDiameterNumber = 0;
    if (minFlow !== undefined) {
      minFlowNumber = minFlow;
    }
    if (minDiameter !== undefined) {
      minDiameterNumber = minDiameter;
    }

    if (pollutedObjectFormData.length < 1) {
      messageApi.info('请选择一个污染源再分析!');
      return;
    }
    const pollutedOtypeOnames: string[][] = [];
    pollutedObjectFormData.forEach((item) => {
      if (item.oname) {
        pollutedOtypeOnames.push([
          item.gisOtype ?? item.otype,
          item.gisOname ?? item.oname,
        ]);
      }
    });
    const unPollutedOtypeOnames: string[][] = [];
    unPollutedObjectFormData.forEach((item) => {
      if (item.oname) {
        unPollutedOtypeOnames.push([
          item.gisOtype ?? item.otype,
          item.gisOname ?? item.oname,
        ]);
      }
    });
    const trackResult = await getPollutionTrack(
      pollutedOtypeOnames.toString(),
      unPollutedOtypeOnames.toString(),
      minDiameterNumber,
      minFlowNumber,
    );

    setResultData(
      trackResult.map((item) => ({
        upDmaDatas: item.pollutedDmaDatas,
        upLinkDatasMap: item.pollutedLinkDatasMap,
        upLinkDatas: item.pollutedLinkDatas,
        upScadaDatas: item.pollutedScadaDatas,
        downDmaDatas: item.unpollutedDmaDatas,
        downLinkDatasMap: item.unpollutedLinkDatasMap,
        downLinkDatas: item.unpollutedLinkDatas,
        downScadaDatas: item.unpollutedScadaDatas,

        linkSourceDatas: item.pollutedSourceLinkDatas,
        linkSourceDatasMap: item.pollutedSourceLinkDatasMap,
      })),
    );
    setSelectable(false);
    setActiveTabItem('result');
  };

  const handleClearHighlightResult = () => {
    highlight({
      downDma: [],
      upDma: [],
      downLink: [],
      upLink: [],
      scada: [],
    });
  };

  const onSubmit = () => {
    handleClearHighlightResult();
    const params = modalForm.getFieldsValue();
    if (params) {
      params.direction = 'UP';
      onNetworkTrack(params);
    }
  };

  const onReset = () => {
    modalForm.resetFields();
    setPollutedObjectFormData([]);
    setUnPollutedObjectFormData([]);
    onResetHighlight();
    setResultData([]);
  };

  const removePollutedObject = (key: string) => {
    const deleteObject = pollutedObjectFormData.find(
      (item) => item.key === key,
    );
    if (deleteObject) {
      const newObjectFormData = pollutedObjectFormData.filter(
        (item) => item.key !== key,
      );
      setPollutedObjectFormData(newObjectFormData);
    }
  };

  const removeUnPollutedObject = (key: string) => {
    const deleteObject = unPollutedObjectFormData.find(
      (item) => item.key === key,
    );
    if (deleteObject) {
      const newObjectFormData = unPollutedObjectFormData.filter(
        (item) => item.key !== key,
      );
      unPollutedObjectsForm.setFieldsValue({
        otypeOnames: newObjectFormData,
      });
      setUnPollutedObjectFormData(newObjectFormData);
    }
  };

  const getResultTabs = () =>
    resultData.map((item, index) => ({
      key: index > 0 ? 'resultCompare' : 'result',
      label: index > 0 ? '对比结果' : '追踪结果',
      children: (
        <TrackResult
          db={curDb()}
          dimension={dimension}
          resultData={item}
          type={TRACK_POLLUTION}
          positionToMapView={positionToMapView}
          handleHover={handleHover}
          highlight={index > 0 ? () => {} : highlightObjects}
          trackResultConfig={getTrackConfig}
          getDmaSwitchData={getDmaSwitchData}
          getLinkSwitchData={getLinkSwitchData}
        />
      ),
    }));

  useEffect(() => {
    const pollutedObjects: HighlightObject[] = generateHighlightObject(
      pollutedObjectFormData,
      {
        highlightIcon: SELECT_ICON,
        highlightType: HIGHLIGHT_POLLUTED_TRACK_SELECT,
        highlightShowMark: dimension,
      },
    );
    highlight({
      selectedPollutedObject: pollutedObjects,
    });
  }, [pollutedObjectFormData, dimension]);
  useEffect(() => {
    const unPollutedObjects: HighlightObject[] = generateHighlightObject(
      unPollutedObjectFormData,
      {
        highlightIcon: SELECT_ICON,
        highlightType: HIGHLIGHT_UNPOLLUTED_TRACK_SELECT,
        highlightShowMark: dimension,
      },
    );
    highlight({
      selectedUnPollutedObject: unPollutedObjects,
    });
  }, [unPollutedObjectFormData, dimension]);

  useEffect(() => {
    if (selectable) {
      getSelectedObject(insertObjectType);
    }
  }, [selectObjects]);

  return (
    <div>
      <Tabs
        activeKey={activeTabItem}
        onTabClick={(activeKey) =>
          setActiveTabItem(activeKey as 'form' | 'result')
        }
        items={[
          {
            key: 'form',
            label: `追踪条件`,
            children: (
              <>
                <Collapse
                  collapsible="icon"
                  defaultActiveKey={['pollution', 'unPollution']}
                  style={{ flex: 1 }}
                  ghost
                >
                  <Collapse.Panel
                    header="污染点"
                    key="pollution"
                    extra={
                      <Tooltip title="增加污染点">
                        <Button
                          type="text"
                          onClick={() => setInsertObjectType('POLLUTED')}
                        >
                          <IconText
                            style={{
                              fontSize: '1.5em',
                              marginTop: '-0.5em',
                              color:
                                insertObjectType === 'POLLUTED'
                                  ? '#3d76fb'
                                  : '#c0c0c0',
                            }}
                            text="&#xe6dd;"
                          />
                        </Button>
                      </Tooltip>
                    }
                  >
                    <div
                      style={{
                        position: 'initial',
                        height: '130px',
                        overflow: 'auto',
                      }}
                    >
                      {pollutedObjectFormData.map((item, index) => (
                        <Space
                          key={item.key}
                          align="baseline"
                          style={{
                            margin: '0 20px 5px 20px',
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <div
                            role="link"
                            aria-hidden="true"
                            style={{ display: 'flex' }}
                            onClick={() => positionToMapView(item)}
                          >
                            <span
                              style={{
                                display: 'block',
                                width: 65,
                              }}
                            >
                              对象{index + 1}:
                            </span>
                            <span
                              style={{
                                display: 'block',
                                color: token.colorPrimaryText,
                                cursor: 'pointer',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {item.otypeTitle} {item.title}
                            </span>
                          </div>
                          <MinusCircleOutlined
                            onClick={() => removePollutedObject(item.key)}
                          />
                        </Space>
                      ))}
                    </div>
                  </Collapse.Panel>
                  <Collapse.Panel
                    header="未污染点"
                    key="unPollution"
                    extra={
                      <Tooltip title="增加未污染点">
                        <Button
                          type="text"
                          onClick={() => setInsertObjectType('UNPOLLUTED')}
                        >
                          <IconText
                            style={{
                              fontSize: '1.5em',
                              marginTop: '-0.5em',
                              color:
                                insertObjectType === 'UNPOLLUTED'
                                  ? '#3d76fb'
                                  : '#c0c0c0',
                            }}
                            text="&#xe6dd;"
                          />
                        </Button>
                      </Tooltip>
                    }
                  >
                    <div
                      style={{
                        position: 'initial',
                        height: '130px',
                        overflow: 'auto',
                      }}
                    >
                      {unPollutedObjectFormData.map((item, index) => (
                        <Space
                          key={item.key}
                          align="baseline"
                          style={{
                            margin: '0 20px 5px 20px',
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                          onClick={() => positionToMapView(item)}
                        >
                          <div style={{ display: 'flex' }}>
                            <span
                              style={{
                                display: 'block',
                                width: 65,
                              }}
                            >
                              对象{index + 1}:
                            </span>
                            <span
                              style={{
                                display: 'block',
                                color: token.colorPrimaryText,
                                cursor: 'pointer',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {item.otypeTitle} {item.title}
                            </span>
                          </div>
                          <MinusCircleOutlined
                            onClick={() => removeUnPollutedObject(item.key)}
                          />
                        </Space>
                      ))}
                    </div>
                  </Collapse.Panel>
                </Collapse>
                <Form
                  preserve={false}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                  form={modalForm}
                  onFinish={onSubmit}
                  initialValues={{
                    otypeOnames: [
                      {
                        oname: '',
                        otype: '',
                        otypeTitle: '',
                        key: 0,
                        name: 0,
                      },
                    ],
                  }}
                >
                  <Form.Item
                    name="minFlow"
                    label="流量最小值"
                    initialValue={0}
                  >
                    <InputNumber
                      className="formInput"
                      min={0}
                      step={100}
                      addonAfter="m³/h"
                    />
                  </Form.Item>

                  <Form.Item
                    name="minDiameter"
                    label="管径最小值"
                    initialValue={0}
                  >
                    <InputNumber
                      className="formInput"
                      min={0}
                      step={100}
                      addonAfter="mm"
                    />
                  </Form.Item>

                  <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        disabled={pollutedObjectFormData.length < 1}
                      >
                        追踪
                      </Button>
                      <Button
                        className="resetBtn"
                        htmlType="button"
                        onClick={() => onReset()}
                      >
                        重置
                      </Button>
                      {selectable ? (
                        <Button onClick={() => setSelectable(!selectable)}>
                          <UnlockOutlined title="当前可增加对象" />
                        </Button>
                      ) : (
                        <Button
                          type="primary"
                          onClick={() => setSelectable(!selectable)}
                        >
                          <LockOutlined title="当前不可增加对象" />
                        </Button>
                      )}
                    </Space>
                  </Form.Item>
                </Form>
              </>
            ),
          },
          ...getResultTabs(),
        ]}
      />
      {contextHolder}
    </div>
  );
}
