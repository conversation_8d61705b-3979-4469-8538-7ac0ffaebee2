/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CloseOutlined } from '@ant-design/icons';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { Button, Card, Flex, Modal, Popconfirm, Space, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { SpanClose } from 'src/styles/common-style';

interface Props {
  open: boolean;
  columns: ColumnsType<any>;
  title: string;
  dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[];
  onDownload?: () => void;
  close: () => void;
}

export default function TrackResultDetail(props: Props) {
  const { open, columns, title, dataSource, onDownload, close } = props;

  return (
    <Modal
      closable={false}
      title={
        <div>
          <Flex
            justify="space-between"
            align="center"
          >
            <span>{title}</span>
            <Space>
              {onDownload ? (
                <Popconfirm
                  title="数据导出"
                  description="导出时可能需要等候，请确认是否导出?"
                  onConfirm={onDownload}
                  onCancel={(e) => e?.stopPropagation()}
                >
                  <Button
                    type="primary"
                    size="small"
                  >
                    下载
                  </Button>
                </Popconfirm>
              ) : null}
              <SpanClose onClick={close}>
                <CloseOutlined />
              </SpanClose>
            </Space>
          </Flex>
        </div>
      }
      width="90%"
      destroyOnHidden
      onCancel={() => close()}
      open={open}
      maskClosable={false}
      centered
      footer={false}
    >
      <Card>
        <Table
          size="small"
          columns={columns}
          scroll={{
            y: 'calc(100vh - 300px)',
          }}
          rowKey="id"
          dataSource={dataSource}
        />
      </Card>
    </Modal>
  );
}
