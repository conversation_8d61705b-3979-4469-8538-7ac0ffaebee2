/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LockOutlined, UnlockOutlined } from '@ant-design/icons';
import {
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  generateHighlightObject,
  ObjectFormItem,
  ResultData,
  SELECT_ICON,
  TRACK_UP,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import { formatNumber } from '@waterdesk/data/utils';
import { Button, Form, InputNumber, message, Space, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { curDb } from 'src/app/host-app';
import SelectContainer from './select-component';
import TrackResult from './track-result';

interface FormItem {
  otypeOnames: ObjectFormItem[];
  direction: string;
  minFlow: number;
  minDiameter: number;
}

interface Props {
  dimension: boolean;
  trackObjects: ObjectFormItem[];
  selectObjects: ObjectFormItem[];
  onResetHighlight: () => void;
  positionToMapView: (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => void;
  handleHover: (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => void;
  highlight: (
    highlightObject: {
      selectedObject?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      downScada?: HighlightObject[];
      upScada?: HighlightObject[];
    },
    themeConfig?: {
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => void;
  clearHighlight: () => void;
  getUpStreamTrack: (
    oname: string,
    otype: string,
    minDiameter: number,
    minFLow: number,
  ) => Promise<ResultData[]>;
  trackResultConfig: (type: TrackType) => TrackConfig | undefined;
  getDmaSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme?: LegendGroupData;
      }
    | undefined;
  getLinkSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme: LegendGroupData;
      }
    | undefined;
  getRecommendValues: (
    otype: string,
    oname: string,
    trackType: TrackType,
  ) => Promise<{ flow: number; diameter: number }>;
}

export default function TrackUp(props: Readonly<Props>) {
  const {
    dimension,
    trackObjects,
    selectObjects,
    onResetHighlight,
    positionToMapView,
    handleHover,
    highlight,
    clearHighlight,
    getUpStreamTrack,
    trackResultConfig,
    getDmaSwitchData,
    getLinkSwitchData,
    getRecommendValues,
  } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [modalForm] = Form.useForm<FormItem>();
  const [selectable, setSelectable] = useState<boolean>(true);
  const [objectFormData, setObjectFormData] = useState<ObjectFormItem[]>([]);
  const [resultData, setResultData] = useState<ResultData[]>([]);
  const [activeTabItem, setActiveTabItem] = useState<'form' | 'result'>('form');

  const getTrackConfig = () => trackResultConfig(TRACK_UP);

  const onNetworkTrack = async (params: FormItem) => {
    const { minFlow, minDiameter, otypeOnames } = params;
    if (otypeOnames.length < 1) {
      messageApi.error('请选择对象!');
      return;
    }
    let minFlowNumber = 0;
    let minDiameterNumber = 0;
    if (minFlow !== undefined) {
      minFlowNumber = minFlow;
    }
    if (minDiameter !== undefined) {
      minDiameterNumber = minDiameter;
    }
    if (otypeOnames.length < 1) {
      messageApi.error('请选择追踪对象!');
      return;
    }
    const trackResult = await getUpStreamTrack(
      otypeOnames[0].oname,
      otypeOnames[0].otype,
      minDiameterNumber,
      minFlowNumber,
    );

    if (trackResult) {
      setResultData(trackResult);
      setSelectable(false);
      setActiveTabItem('result');
    }
  };

  const fetchRecommendValues = async (otype: string, oname: string) => {
    const res = await getRecommendValues(otype, oname, TRACK_UP);
    if (res) {
      modalForm.setFieldsValue({
        minFlow: formatNumber(res.flow, 1),
        minDiameter: res.diameter,
      });
    }
  };

  useEffect(() => {
    if (trackObjects.length > 0) {
      const trackObject = trackObjects[trackObjects.length - 1];
      const { oname, otype } = trackObject;
      fetchRecommendValues(otype, oname);
    }
  }, [trackObjects]);

  useEffect(() => {
    clearHighlight();

    if (objectFormData.length > 0) {
      highlight(
        {
          selectedObject: generateHighlightObject(selectObjects, {
            highlightIcon: SELECT_ICON,
          }),
        },
        {},
      );
    }
  }, [objectFormData]);

  const onSubmit = () => {
    clearHighlight();
    const params = modalForm.getFieldsValue();
    if (params) {
      params.otypeOnames = objectFormData;
      onNetworkTrack(params);
    }
  };

  const onReset = () => {
    modalForm.resetFields();
    onResetHighlight();
    setSelectable(true);
    setResultData([]);
  };

  const setMinFlow = (flow: number) => {
    modalForm.setFieldValue('minFlow', flow);
  };

  const highlightObjects = (
    highlightObject: {
      selectedObject?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
    },
    themeConfig?: {
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
    },
  ) => {
    highlight(
      {
        ...highlightObject,
        selectedObject: generateHighlightObject(selectObjects, {}),
      },
      themeConfig,
    );
  };

  const getResultTabs = () =>
    resultData.map((item, index) => ({
      key: index > 0 ? 'resultCompare' : 'result',
      label: index > 0 ? '对比结果' : '追踪结果',
      children: (
        <TrackResult
          db={curDb()}
          dimension={dimension}
          resultData={item}
          type={TRACK_UP}
          positionToMapView={positionToMapView}
          handleHover={handleHover}
          highlight={index > 0 ? () => {} : highlightObjects}
          trackResultConfig={getTrackConfig}
          getDmaSwitchData={getDmaSwitchData}
          getLinkSwitchData={getLinkSwitchData}
        />
      ),
    }));

  return (
    <div>
      <Tabs
        activeKey={activeTabItem}
        onTabClick={(activeKey) =>
          setActiveTabItem(activeKey as 'form' | 'result')
        }
        items={[
          {
            key: 'form',
            label: `追踪条件`,
            children: (
              <>
                <SelectContainer
                  single
                  setObjects={setObjectFormData}
                  minFlowRatio={0.2}
                  minDiameterRatio={0.3}
                  setMinFlow={(flow) => setMinFlow(flow)}
                  setMinDiameter={(diameter) =>
                    modalForm.setFieldValue('minDiameter', diameter)
                  }
                  selectable={selectable}
                  selectObjects={trackObjects}
                />

                <Form
                  preserve={false}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                  form={modalForm}
                  onFinish={onSubmit}
                  initialValues={{
                    otypeOnames: [
                      {
                        oname: '',
                        otype: '',
                        otypeTitle: '',
                        key: 0,
                        name: 0,
                      },
                    ],
                  }}
                >
                  <Form.Item
                    name="minFlow"
                    label="流量最小值"
                    initialValue={0}
                  >
                    <InputNumber
                      className="formInput"
                      min={0}
                      step={100}
                      addonAfter="m³/h"
                    />
                  </Form.Item>
                  <Form.Item
                    name="minDiameter"
                    label="管径最小值"
                    initialValue={0}
                  >
                    <InputNumber
                      className="formInput"
                      min={0}
                      step={100}
                      addonAfter="mm"
                    />
                  </Form.Item>
                  <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        disabled={objectFormData.length < 1}
                      >
                        追踪
                      </Button>
                      <Button
                        className="resetBtn"
                        htmlType="button"
                        onClick={() => onReset()}
                      >
                        重置
                      </Button>
                      {selectable ? (
                        <Button onClick={() => setSelectable(!selectable)}>
                          <UnlockOutlined title="当前可增加对象" />
                        </Button>
                      ) : (
                        <Button
                          type="primary"
                          onClick={() => setSelectable(!selectable)}
                        >
                          <LockOutlined title="当前不可增加对象" />
                        </Button>
                      )}
                    </Space>
                  </Form.Item>
                </Form>
              </>
            ),
          },
          ...getResultTabs(),
        ]}
      />
      {contextHolder}
    </div>
  );
}
