/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  formatSdval,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { Button, List } from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { ListContent, ListTitle } from '../mini-dashboard/running-state/style';

dayjs.extend(relativeTime);

interface Props {
  list: ValveOperationValue[];
  onClickValve?: (info: ValveOperationValue) => void;
}

const NoticeValveList = (props: Props) => {
  const { list, onClickValve } = props;

  const getTitleContent = (info: ValveOperationValue) => {
    const { oname, shape } = info;
    return shape ? (
      <Button
        className="message-title"
        style={{ padding: 0, height: '22px' }}
        type="link"
        onClick={() => onClickValve?.(info)}
      >
        {oname}
      </Button>
    ) : (
      <span
        title={oname}
        className="message-title"
      >
        {oname}
      </span>
    );
  };

  const getContentText = (info: ValveOperationValue) => {
    const { otime, oname, sdval, note } = info;
    return `阀门${oname}在${dayjs(otime).format(
      'YYYY年MM月DD日 HH时mm分ss秒',
    )}设置为${formatSdval(sdval)}。${note}`;
  };

  return (
    <List<ValveOperationValue>
      size="small"
      pagination={false}
      dataSource={list}
      style={{ paddingBottom: 5 }}
      renderItem={(item) => (
        <List.Item style={{ display: 'block', padding: '4px 0' }}>
          <List.Item.Meta
            title={
              <ListTitle>
                <div>
                  {getTitleContent(item)}
                  <span className="time">
                    {dayjs(item.createTime).fromNow()}
                  </span>
                </div>
              </ListTitle>
            }
          />
          <ListContent title={getContentText(item)}>
            {getContentText(item)}
          </ListContent>
        </List.Item>
      )}
    />
  );
};

NoticeValveList.displayName = 'NoticeValveList';

export default NoticeValveList;
