/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { makeObjectId, splitObjectId } from '@waterdesk/data/object-item';
import { SearchElementInfo } from '@waterdesk/data/search-element-info';
import { Button, Form, Input, Modal, Select, SelectProps } from 'antd';
import { useEffect, useMemo } from 'react';
import { SearchDeviceInput } from './add-observation-device';

export interface AddModelObjectFormValues {
  deviceId: string;
  indicatorId: string;
  description: string;
}

interface Props {
  db: Database;
  open: boolean;
  onClose: () => void;
  onOk: (formValues: AddModelObjectFormValues) => void;
  getSearchedElements: (
    searchValue: string,
  ) => Promise<Array<SearchElementInfo>>;
}

const AddModelObjectModal = (props: Props) => {
  const { db, open, onClose, getSearchedElements, onOk } = props;
  const [form] = Form.useForm();

  const deviceId = Form.useWatch('deviceId', form) ?? '';
  const [otype] = splitObjectId(deviceId);

  const propertyOptions: SelectProps['options'] = useMemo(() => {
    const properties = db.getPropertyInfo(otype)?.getProperties() ?? [];

    return properties.map((item) => ({
      value: item.vprop,
      label: item.title,
    }));
  }, [otype]);

  const onFinish = (values: AddModelObjectFormValues) => {
    onOk(values);
  };

  const handleSearchObjects = async (
    searchValue: string,
    callback: Function,
  ) => {
    const list = await getSearchedElements(searchValue);
    const newList = list.map((item) => ({
      ...item,
      id: makeObjectId(item.otype, item.oname),
    }));
    callback(newList);
  };

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open]);

  return (
    <Modal
      width={500}
      title="增加模型对象"
      open={open}
      onCancel={onClose}
      maskClosable={false}
      destroyOnHidden
      footer={null}
    >
      <Form
        name="addModelObjectForm"
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label="选择模型对象"
          name="deviceId"
          rules={[{ required: true, message: '模型对象不能为空!' }]}
        >
          <SearchDeviceInput
            placeholder="请输入关键字查询"
            onSearch={handleSearchObjects}
          />
        </Form.Item>
        <Form.Item
          label="属性字段"
          name="indicatorId"
          rules={[{ required: true, message: '属性字段不能为空!' }]}
        >
          <Select
            placeholder="请选择"
            options={propertyOptions}
          />
        </Form.Item>

        <Form.Item
          label="别名"
          name="description"
          rules={[{ required: true, message: '别名不能为空!' }]}
        >
          <Input placeholder="请输入别名" />
        </Form.Item>

        <Form.Item
          wrapperCol={{ span: 24 }}
          style={{ margin: '0', textAlign: 'center' }}
        >
          <Button
            type="primary"
            htmlType="submit"
          >
            保存
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddModelObjectModal;
