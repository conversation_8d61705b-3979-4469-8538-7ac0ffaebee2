/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import Device from '@waterdesk/data/device';
import { AddWorkOrderDeviceIndicator } from '@waterdesk/data/work-order';
import { Button, Form, Input, Modal, Select, SelectProps, Spin } from 'antd';
import { SizeType } from 'antd/es/config-provider/SizeContext';
import { useEffect, useMemo, useState } from 'react';

export interface AddDeviceIndicatorFormValues {
  deviceId: string;
  indicatorId: string;
  description: string;
}

interface Props {
  db: Database;
  selectedDeviceIndicator?: AddWorkOrderDeviceIndicator;
  open: boolean;
  hideAlias?: boolean;
  onClose: () => void;
  onOk: (formValues: AddDeviceIndicatorFormValues) => void;
}

export const SearchDeviceInput = (props: {
  placeholder?: string;
  style?: React.CSSProperties;
  value?: string;
  deviceTitle?: string;
  loading?: boolean;
  disabled?: boolean;
  size?: SizeType;
  onChange?: (value: string) => void;
  onSearch: (searchValue: string, callback: Function) => void;
}) => {
  const {
    value,
    deviceTitle,
    placeholder,
    style,
    loading,
    disabled,
    size,
    onChange,
    onSearch,
  } = props;

  const [data, setData] = useState<Device[]>([]);

  const handleSearch = (newValue: string) => {
    if (newValue) {
      onSearch(newValue, setData);
    } else {
      setData([]);
    }
  };

  useEffect(() => {
    if (deviceTitle) handleSearch(deviceTitle);
  }, [deviceTitle]);

  return (
    <Select
      size={size ?? 'middle'}
      showSearch
      value={value}
      placeholder={placeholder}
      style={{ ...style }}
      defaultActiveFirstOption={false}
      filterOption={false}
      onSearch={handleSearch}
      onChange={onChange}
      notFoundContent={loading ? <Spin size="small" /> : null}
      options={(data || []).map((d) => ({
        value: d.id,
        label: d.title,
      }))}
      allowClear
      disabled={disabled}
    />
  );
};

const AddObservationDeviceModal = (props: Props) => {
  const { db, selectedDeviceIndicator, open, hideAlias, onClose, onOk } = props;
  const [form] = Form.useForm();

  const [deviceTitle, setDeviceTitle] = useState<string>('');

  const deviceId = Form.useWatch('deviceId', form);

  const indicatorOptions: SelectProps['options'] = useMemo(() => {
    const device = db.getDeviceById(deviceId);

    const deviceIndicators = (device?.indicators ?? []).map((item) => ({
      value: item.id,
      label: item.title ?? item?.indicatorType?.title ?? '',
      disabled: selectedDeviceIndicator?.indicatorIds?.some(
        (i) => i === item.id,
      ),
    }));
    return deviceIndicators;
  }, [deviceId]);

  useEffect(() => {
    if (deviceId) {
      const device = db.getDeviceById(deviceId);
      form.setFieldValue('description', device?.title);
    }
  }, [deviceId]);

  useEffect(() => {
    if (open) {
      form.resetFields();
      setDeviceTitle('');
    }
  }, [open]);

  useEffect(() => {
    if (selectedDeviceIndicator?.deviceId) {
      form.setFieldValue('deviceId', selectedDeviceIndicator?.deviceId);
      setDeviceTitle(selectedDeviceIndicator?.deviceTitle ?? '');
    }
  }, [open, selectedDeviceIndicator]);

  const onFinish = (values: AddDeviceIndicatorFormValues) => {
    onOk(values);
  };

  const handleSearchDeviceList = async (
    searchValue: string,
    callback: Function,
  ) => {
    const list = db.getDeviceListByName(searchValue);
    callback(list);
  };

  return (
    <Modal
      width={500}
      title="增加监测设备"
      open={open}
      onCancel={onClose}
      footer={null}
      destroyOnHidden
      maskClosable={false}
    >
      <Form
        name="addObservationDeviceForm"
        form={form}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label="选择设备"
          name="deviceId"
          rules={[{ required: true, message: '设备不能为空!' }]}
        >
          <SearchDeviceInput
            value={selectedDeviceIndicator?.deviceId}
            deviceTitle={deviceTitle}
            placeholder="请输入关键字查询"
            onSearch={handleSearchDeviceList}
            disabled={!!selectedDeviceIndicator?.deviceId}
          />
        </Form.Item>
        <Form.Item
          label="监测指标"
          name="indicatorId"
          rules={[{ required: true, message: '监测指标不能为空!' }]}
        >
          <Select
            placeholder="请选择"
            options={indicatorOptions}
          />
        </Form.Item>

        {!hideAlias && (
          <Form.Item
            label="别名"
            name="description"
            rules={[{ required: true, message: '别名不能为空!' }]}
          >
            <Input placeholder="请输入别名" />
          </Form.Item>
        )}

        <Form.Item
          wrapperCol={{ span: 24 }}
          style={{ margin: '0', textAlign: 'center' }}
        >
          <Button
            type="primary"
            htmlType="submit"
          >
            保存
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddObservationDeviceModal;
