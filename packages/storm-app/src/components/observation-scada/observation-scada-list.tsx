/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  isBetweenRange,
  ObservationScadaItem,
  ObservationScadaItemList,
} from '@waterdesk/data/observation-scada-data';
import { TableProps } from 'antd';
import dayjs from 'dayjs';
import LinkSpan from '../common/link-span';
import { ObservationScadaTable } from './style';

interface Props {
  list: ObservationScadaItem[];
  currentTime: string;
  tableProps?: TableProps<ObservationScadaItemList>;
  onLocate: (
    otype: string,
    oname: string,
    shape?: string,
    vprop?: string,
  ) => void;
  showRange?: boolean;
}

const ObservationScadaList = (props: Props) => {
  const { list, currentTime, tableProps, onLocate, showRange = true } = props;
  const isCurrentTimeInRange = (
    startTime: string | undefined,
    endTime: string | undefined,
  ): boolean => {
    if (!startTime || !endTime) {
      return false;
    }
    const startString = startTime;
    const endTimeString = endTime;
    if (dayjs(currentTime).isBefore(dayjs(endTimeString))) {
      return dayjs(currentTime).isAfter(dayjs(startString));
    }
    return false;
  };

  const filterTimeRangeData = (observationList: ObservationScadaItem[]) => {
    const deviceMap = new Map<string, ObservationScadaItemList>();

    observationList.forEach((item) => {
      const deviceKey = `${item.otype}_${item.oname}_${item.vprop}`;
      let hasTimeRangeRecord = false;

      // 首先检查是否有当前时间范围内的记录
      const timeRangeRecord = item.observationValues.find((observationValue) =>
        isCurrentTimeInRange(
          observationValue.startTime,
          observationValue.endTime,
        ),
      );

      if (timeRangeRecord) {
        deviceMap.set(deviceKey, {
          ...item,
          ...timeRangeRecord,
        });
        hasTimeRangeRecord = true;
      }

      // 如果没有时间范围内的记录，且该设备还没有记录，则添加第一条记录
      if (!hasTimeRangeRecord && !deviceMap.has(deviceKey)) {
        deviceMap.set(deviceKey, {
          ...item,
          ...item.observationValues[0],
          min: undefined,
          max: undefined,
        });
      }
    });

    return Array.from(deviceMap.values());
  };

  const publicList = list.filter((item) => item.isPublic);
  const personalList = list.filter((item) => !item.isPublic);
  // Put personal at the top
  const sortList = filterTimeRangeData([...personalList, ...publicList]);

  const getRowClassName = (record: ObservationScadaItemList): string => {
    const { value, startTime, endTime, min, max } = record;
    if (!isCurrentTimeInRange(startTime, endTime)) return '';
    return isBetweenRange(value as number, min, max) ? '' : 'highlight-row';
  };

  const columns: TableProps<ObservationScadaItemList>['columns'] = [
    {
      title: '名称',
      dataIndex: 'description',
      ellipsis: true,
      width: showRange ? 100 : 120,
      sorter: (a, b) =>
        (a.description ?? '-').localeCompare(b.description ?? '-'),
      render: (text, { otype, oname, shape, vprop }) => (
        <LinkSpan onClick={() => onLocate(otype, oname, shape, vprop)}>
          {text ?? '-'}
        </LinkSpan>
      ),
    },
    {
      title: '类型',
      dataIndex: 'typeTitle',
      width: showRange ? 60 : 80,
      ellipsis: true,
      sorter: (a, b) => (a.typeTitle ?? '-').localeCompare(b.typeTitle ?? '-'),
    },
    {
      title: '数值',
      width: showRange ? undefined : 120,
      render: (_, record) =>
        `${record.value ?? ''}${
          record.simulatedValue ? `(${record.simulatedValue})` : ''
        } ${record.unit ?? ''}`,
    },
    ...(showRange
      ? [
          {
            title: '范围',
            width: 90,
            ellipsis: true,
            render: (_: unknown, record: ObservationScadaItemList) =>
              `${record.min ?? '-'} ~ ${record.max ?? '-'}`,
          },
        ]
      : []),
  ];

  return (
    <ObservationScadaTable
      rowClassName={getRowClassName}
      size="small"
      dataSource={sortList}
      columns={columns}
      rowKey="id"
      {...tableProps}
    />
  );
};

export default ObservationScadaList;
