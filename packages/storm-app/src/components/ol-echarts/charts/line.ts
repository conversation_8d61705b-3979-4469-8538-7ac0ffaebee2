/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { isObject } from '../utils';

const line = (
  options: {
    grid: { map: (arg0: (gri: any, index: any) => any) => void };
    series: { [x: string]: { coordinates: number[] } };
  },
  series: { [x: string]: { coordinates: number[] } },
  coordinateSystem: { dataToPoint: (arg0: any) => any },
) => {
  if (isObject(options.grid) && !Array.isArray(options.grid)) {
    console.log(options);
  } else if (Array.isArray(options.grid)) {
    // eslint-disable-next-line no-param-reassign
    options.grid = options.grid.map((gri: any, index: number) => {
      const coorPixel: number[] = coordinateSystem.dataToPoint(
        options.series[index].coordinates,
      );
      // eslint-disable-next-line no-param-reassign
      gri.left = coorPixel[0] - parseFloat(gri.width) / 2;
      // eslint-disable-next-line no-param-reassign
      gri.top = coorPixel[1] - parseFloat(gri.height) / 2;
      return gri;
    });
  }
  return series;
};

export default line;
