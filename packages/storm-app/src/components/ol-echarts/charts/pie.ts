/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

const pie = (
  _options: {
    grid: { map: (arg0: (gri: any, index: any) => any) => void };
    series: { [x: string]: { coordinates: number[] } };
  },
  series: { center: any; coordinates: number[] },
  coordinateSystem: { dataToPoint: (arg0: any) => any },
) => {
  // eslint-disable-next-line no-param-reassign
  series.center = coordinateSystem.dataToPoint(series.coordinates);
  return series;
};

export default pie;
