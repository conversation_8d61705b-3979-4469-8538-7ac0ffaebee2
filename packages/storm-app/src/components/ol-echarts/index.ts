/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import * as echarts from 'echarts';
import { Map as OlMap, Object as obj } from 'ol';
import { Coordinate } from 'ol/coordinate';
import Event from 'ol/events/Event';
import { ProjectionLike, transform } from 'ol/proj';
import { Size } from 'ol/size';
import { VERSION } from 'ol/util';
import { BoundingRect } from 'zrender';
import Transformable from 'zrender/lib/core/Transformable';
import {
  arrayAdd,
  bind,
  bindAll,
  isObject,
  merge,
  mockEvent,
  removeNode,
  semver,
  uuid,
} from './utils';

import formatGeoJSON from './utils/format-geo-json';

type CoordinateSystemCreator = any;

const _options = {
  forcedRerender: false, // Force re-rendering
  forcedPrecomposeRerender: false, // force pre re-render
  hideOnZooming: false, // when zooming hide chart
  hideOnMoving: false, // when moving hide chart
  hideOnRotating: false, // // when Rotating hide chart
  convertTypes: ['pie', 'line', 'bar'],
  insertFirst: false,
  stopEvent: false,
  polyfillEvents: semver(VERSION, '6.1.1') <= 0, // fix echarts mouse events
};

type NoDef<T> = T | undefined;

interface OptionsTypes {
  source?: ProjectionLike;
  destination?: ProjectionLike;
  forcedRerender?: boolean;
  forcedPrecomposeRerender?: boolean;
  hideOnZooming?: boolean;
  hideOnMoving?: boolean;
  hideOnRotating?: boolean;
  convertTypes?: string[] | number[];
  insertFirst?: boolean;
  stopEvent?: boolean;
  polyfillEvents?: boolean;
  [key: string]: any;
}

class RegisterCoordinateSystem {
  map: OlMap | undefined;

  _mapOffset = [0, 0];

  dimensions = ['lng', 'lat'];

  projCode: string;

  private _coordinateSystemId: string;

  static dimensions = RegisterCoordinateSystem.prototype.dimensions || [
    'lng',
    'lat',
  ];

  create(echartsModel: any) {
    const { _coordinateSystemId } = this;
    const _map = this.map;
    const { _options } = this;

    echartsModel.eachSeries((seriesModel: any) => {
      if (seriesModel.get('coordinateSystem') === _coordinateSystemId && _map) {
        // eslint-disable-next-line no-param-reassign
        seriesModel.coordinateSystem = new RegisterCoordinateSystem(
          _coordinateSystemId,
          _options,
          _map,
        );
      }
    });
  }

  static getProjectionCode(map: OlMap): string {
    let code = '';
    if (map) {
      code = map.getView()?.getProjection().getCode();
    } else {
      code = 'EPSG:3857';
    }
    return code;
  }

  /**
   * Represents the transform brought by roam/zoom.
   * If `View['_viewRect']` applies roam transform,
   * we can get the final displayed rect.
   */
  private _roamTransformable = new Transformable();

  /**
   * Represents the transform from `View['_rect']` to `View['_viewRect']`.
   */
  protected _rawTransformable = new Transformable();

  private _viewRect: BoundingRect = new BoundingRect(0, 0, 0, 0);

  private _options: echarts.EChartsOption & OptionsTypes;

  constructor(
    coordinateSystemId: string,
    options: echarts.EChartsOption & OptionsTypes,
    map: OlMap,
  ) {
    this.map = map;
    this.dimensions = ['lng', 'lat'];
    this.projCode = RegisterCoordinateSystem.getProjectionCode(map);
    this._options = options;
    this._coordinateSystemId = coordinateSystemId;
  }

  /**
   * get zoom
   * @returns {number}
   */
  getZoom(): number | undefined {
    return this.map?.getView().getZoom();
  }

  /**
   * set zoom
   * @param zoom
   */
  setZoom(zoom: number): void {
    this.map?.getView().setZoom(zoom);
  }

  getViewRectAfterRoam() {
    return this.getViewRect().clone();
  }

  /**
   * 设置地图窗口的偏移
   * @param mapOffset
   */
  setMapOffset(mapOffset: number[]): void {
    this._mapOffset = mapOffset;
  }

  /**
   * 跟据坐标转换成屏幕像素
   * @param data
   * @returns {}
   */
  dataToPoint(data: number[]): number[] {
    let coords: Coordinate;
    if (this.map && data && Array.isArray(data) && data.length > 0) {
      coords = data.map((item: string | number): number => {
        let res = 0;
        if (typeof item === 'string') {
          res = Number(item);
        } else {
          res = item;
        }
        return res;
      });

      const source: ProjectionLike = this._options?.source || 'EPSG:4326';
      const destination: ProjectionLike =
        this._options?.destination || this.projCode;
      const pixel = this.map.getPixelFromCoordinate(
        transform(coords, source, destination),
      );
      const mapOffset = this._mapOffset;
      return [pixel[0] - mapOffset[0], pixel[1] - mapOffset[1]];
    }
    return [0, 0];
  }

  /**
   * 跟据屏幕像素转换成坐标
   * @param pixel
   * @returns {}
   */
  pointToData(pixel: number[]): number[] {
    const mapOffset: number[] = this._mapOffset;
    if (!this.map) {
      return [0, 0];
    }
    return this.map.getCoordinateFromPixel([
      pixel[0] + mapOffset[0],
      pixel[1] + mapOffset[1],
    ]);
  }

  setViewRect(): void {
    const size = this.map?.getSize();
    // this._transformTo(0, 0, size[0], size[1]);
    this._viewRect = new BoundingRect(
      0,
      0,
      size ? size[0] : 0,
      size ? size[1] : 0,
    );
  }

  /**
   * 获取视图矩形范围
   * @returns {*}
   */
  getViewRect() {
    return this._viewRect;
  }

  /**
   * create matrix
   */
  getRoamTransform() {
    return this._roamTransformable.getLocalTransform();
  }

  /**
   * 处理自定义图表类型
   * @returns {{coordSys: {type: string, x, y, width, height}, api: {coord, size}}}
   */
  prepareCustoms() {
    const rect = this.getViewRect();
    return {
      coordSys: {
        type: this._coordinateSystemId,
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
      },
      api: {
        coord: bind(this.dataToPoint, this),
        size: bind(this.dataToCoordsSize, this),
      },
    };
  }

  dataToCoordsSize(dataSize: number[], dataItem: number[] = [0, 0]) {
    return [0, 1].map((dimIdx: number) => {
      const val = dataItem[dimIdx];
      const p1: number[] = [];
      const p2: number[] = [];
      const halfSize = dataSize[dimIdx] / 2;
      p1[dimIdx] = val - halfSize;
      p2[dimIdx] = val + halfSize;
      p1[1 - dimIdx] = dataItem[1 - dimIdx];
      p2[1 - dimIdx] = dataItem[1 - dimIdx];
      const offset: number =
        this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx];
      return Math.abs(offset);
    });
  }

  getTransformInfo() {
    const rawTransformable = this._rawTransformable;

    const roamTransformable = this._roamTransformable;
    // Becuase roamTransformabel has `originX/originY` modified,
    // but the caller of `getTransformInfo` can not handle `originX/originY`,
    // so need to recalcualte them.
    const dummyTransformable = new Transformable();
    dummyTransformable.transform = roamTransformable.transform;
    dummyTransformable.decomposeTransform();

    return {
      roam: {
        x: dummyTransformable.x,
        y: dummyTransformable.y,
        scaleX: dummyTransformable.scaleX,
        scaleY: dummyTransformable.scaleY,
      },
      raw: {
        x: rawTransformable.x,
        y: rawTransformable.y,
        scaleX: rawTransformable.scaleX,
        scaleY: rawTransformable.scaleY,
      },
    };
  }
}

class EChartsLayer extends obj {
  public static formatGeoJSON = formatGeoJSON;

  public static bind = bind;

  public static merge = merge;

  public static uuid = uuid;

  public static bindAll = bindAll;

  public static arrayAdd = arrayAdd;

  public static removeNode = removeNode;

  public static isObject = isObject;

  private _chartOptions: echarts.EChartsOption | undefined;

  private _isRegistered: boolean;

  private _incremental: any[];

  private _coordinateSystem: RegisterCoordinateSystem | undefined;

  private coordinateSystemId: string;

  private readonly _options: echarts.EChartsOption & OptionsTypes;

  private _initEvent: boolean;

  private prevVisibleState: string | null;

  public $chart: echarts.ECharts | null;

  public $container: NoDef<HTMLElement>;

  public _map: OlMap | undefined;

  constructor(
    chartOptions?: echarts.EChartsOption,
    options?: OptionsTypes,
    map?: OlMap,
  ) {
    const opts = Object.assign(_options, options);
    super(opts);

    /**
     * layer options
     */
    this._options = opts;

    /**
     * chart options
     */
    this._chartOptions = chartOptions;
    this.set('chartOptions', chartOptions); // cache chart Options

    /**
     * chart instance
     * @type {null}
     */
    this.$chart = null;

    /**
     * chart element
     * @type {undefined}
     */
    this.$container = undefined;

    /**
     * Whether the relevant configuration has been registered
     * @type {boolean}
     * @private
     */
    this._isRegistered = false;

    /**
     * check if init
     */
    this._initEvent = false;

    /**
     * 增量数据存放
     * @type {Array}
     * @private
     */
    this._incremental = [];

    /**
     * coordinateSystemId
     */
    this.coordinateSystemId = `openlayers_${uuid()}`;

    /**
     * register coordinateSystem
     */
    if (map) {
      this._coordinateSystem = new RegisterCoordinateSystem(
        this.coordinateSystemId,
        opts,
        map,
      );
    }

    this.prevVisibleState = '';

    bindAll(
      [
        'redraw',
        'onResize',
        'onZoomEnd',
        'onCenterChange',
        'onDragRotateEnd',
        'onMoveStart',
        'onMoveEnd',
        'mouseDown',
        'mouseUp',
        'onClick',
        'mouseMove',
      ],
      this,
    );

    if (map) this.setMap(map);
  }

  /**
   * append layer to map
   * @param map
   * @param forceIgnore
   */
  public appendTo(map: OlMap, forceIgnore = false) {
    this.setMap(map, forceIgnore);
  }

  public getMap() {
    return this._map;
  }

  /**
   * set map
   * @param map
   * @param forceIgnore 是否忽略instanceof检查
   */
  public setMap(map: OlMap, forceIgnore = false) {
    if (map && (forceIgnore || map instanceof OlMap)) {
      this._map = map;
      this._map.once('postrender', () => {
        this.handleMapChanged();
      });
      this._map.renderSync();
    } else {
      throw new Error('not ol map object');
    }
  }

  /**
   * get echarts options
   */
  public getChartOptions(): echarts.EChartsOption | undefined {
    return this.get('chartOptions');
  }

  /**
   * set echarts options and redraw
   * @param options
   * @returns {EChartsLayer}
   */
  public setChartOptions(options: echarts.EChartsOption) {
    this._chartOptions = options;
    this.set('chartOptions', options);
    this.clearAndRedraw();
    return this;
  }

  /**
   * append data
   * @param data
   * @param save
   * @returns {EChartsLayer}
   */
  public appendData(data: any, save: boolean | undefined | null = true) {
    if (data) {
      if (save) {
        this._incremental = arrayAdd(this._incremental, {
          index: this._incremental.length,
          data: data.data,
          seriesIndex: data.seriesIndex,
        });
      }
      // https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/TypedArray/copyWithin
      this.$chart?.appendData({
        data: data.data.copyWithin(),
        seriesIndex: data.seriesIndex,
      });
    }
    return this;
  }

  /**
   * clear layer
   */
  public clear(keep?: boolean) {
    if (!keep) {
      this._incremental = [];
    }
    if (this.$chart) {
      this.$chart.clear();
    }
  }

  /**
   * remove layer
   */
  public remove() {
    this.clear();
    if (this.$chart) {
      this.$chart.dispose();
    }

    if (this._initEvent && this.$container) {
      // eslint-disable-next-line no-unused-expressions
      this.$container && removeNode(this.$container);
      this.unBindEvent();
    }
    this.$chart = null;
    delete this._map;
  }

  /**
   * show layer
   */
  public show() {
    this.setVisible(true);
  }

  private innerShow() {
    if (this.$container) {
      this.$container.style.display = this.prevVisibleState ?? '';
      this.prevVisibleState = '';
    }
  }

  /**
   * hide layer
   */
  public hide() {
    this.setVisible(false);
  }

  private innerHide() {
    if (this.$container) {
      this.prevVisibleState = this.$container.style.display;
      this.$container.style.display = 'none';
    }
  }

  /**
   * check layer is visible
   */
  public isVisible() {
    return this.$container && this.$container.style.display !== 'none';
  }

  /**
   * show loading bar
   */
  public showLoading() {
    if (this.$chart) {
      this.$chart.showLoading();
    }
  }

  /**
   * hide loading bar
   */
  public hideLoading() {
    if (this.$chart) {
      this.$chart.hideLoading();
    }
  }

  /**
   * set zindex
   * @param zIndex
   */
  public setZIndex(zIndex: string | number | null) {
    if (this.$container) {
      if (typeof zIndex === 'number') {
        // eslint-disable-next-line no-param-reassign
        zIndex = String(zIndex);
      }
      this.$container.style.zIndex = zIndex ?? '';
    }
  }

  /**
   * get zindex
   */
  public getZIndex() {
    return this.$container?.style.zIndex;
  }

  /**
   * set visible
   * from: https://github.com/sakitam-fdd/ol3Echarts/blob/3929ad72f562661ba3511d4d9e360dee5ac793c2/
   * packages/ol-echarts/src/index.js
   * author: https://github.com/ChenGuanglin0924
   * @param visible
   */
  public setVisible(visible: boolean) {
    if (visible) {
      if (this.$container) {
        this.$container.style.display = '';
      }
      this._chartOptions = this.getChartOptions();
      this.clearAndRedraw();
    } else {
      if (this.$container) {
        this.$container.style.display = 'none';
      }
      this.clear(true);
      this._chartOptions = {};
      this.clearAndRedraw();
    }
  }

  /**
   * render
   */
  public render() {
    if (!this.$chart && this.$container) {
      // @ts-ignore
      this.$chart = echarts.init(this.$container);
      if (this._chartOptions) {
        this.registerMap();
        this.$chart.setOption(this._chartOptions, false);
      }
      this.dispatchEvent({
        type: 'load',
        source: this,
        value: this.$chart,
      });
    } else if (this.isVisible()) {
      this.redraw();
    }
  }

  /**
   * redraw echarts layer
   */
  public redraw() {
    this.clearAndRedraw();
  }

  /**
   * update container size
   * @param size
   */
  public updateViewSize(sizeParams: Size | undefined): void {
    const size = sizeParams || [0, 0];
    if (!this.$container) return;
    this.$container.style.width = `${size[0]}px`;
    this.$container.style.height = `${size[1]}px`;
    this.$container.setAttribute('width', String(size[0]));
    this.$container.setAttribute('height', String(size[1]));
  }

  /**
   * handle map view resize
   */
  private onResize(event?: any) {
    const map = this.getMap();
    if (map) {
      const size: Size | undefined = map.getSize();
      this.updateViewSize(size);
      this.clearAndRedraw();
      if (event) {
        // ignore events
        this.dispatchEvent({
          type: 'change:size',
          source: this,
          value: size,
        });
      }
    }
  }

  /**
   * handle rotate end events
   */
  private onDragRotateEnd() {
    // eslint-disable-next-line no-unused-expressions
    this._options.hideOnRotating && this.innerShow();
    const map = this.getMap();
    if (map?.getView()) {
      this.clearAndRedraw();
      this.dispatchEvent({
        type: 'change:rotation',
        source: this,
        value: map.getView().getRotation(),
      });
    }
  }

  /**
   * handle move start events
   */
  private onMoveStart() {
    // eslint-disable-next-line no-unused-expressions
    this._options.hideOnMoving && this.innerHide();
    const map = this.getMap();
    if (map?.getView()) {
      this.dispatchEvent({
        type: 'movestart',
        source: this,
        value: map.getView().getCenter(),
      });
    }
  }

  /**
   * handle move end events
   */
  onMoveEnd() {
    // eslint-disable-next-line no-unused-expressions
    this._options.hideOnMoving && this.innerShow();
    const map = this.getMap();
    if (map?.getView()) {
      this.clearAndRedraw();
      this.dispatchEvent({
        type: 'moveend',
        source: this,
        value: map.getView().getCenter(),
      });
    }
  }

  /**
   * on mouse click
   * @param event
   */
  private onClick(event: any) {
    if (this.$chart) {
      this.$chart
        .getZr()
        .painter.getViewportRoot()
        .dispatchEvent(mockEvent('click', event));
    }
  }

  /**
   * on mouse down
   * @param event
   */
  private mouseDown(event: any) {
    if (this.$chart) {
      this.$chart
        .getZr()
        .painter.getViewportRoot()
        .dispatchEvent(mockEvent('mousedown', event));
    }
  }

  /**
   * mouse up
   * @param event
   */
  private mouseUp(event: any) {
    if (this.$chart) {
      this.$chart
        .getZr()
        .painter.getViewportRoot()
        .dispatchEvent(mockEvent('mouseup', event));
    }
  }

  /**
   * mousemove 事件需要分两种情况处理:
   * 1. ol-overlaycontainer-stopevent 有高度, 则 propagation path 是 ol-viewport -> ol-overlaycontainer-stopevent.
   * 此时 ol-overlaycontainer 无法获得事件, 只能 mock 处理
   * 2. ol-overlaycontainer-stopevent 没有高度, 则 propagation path 是 ol-viewport -> ol-overlaycontainer. 无需 mock
   * @param event
   */
  private mouseMove(event: any) {
    if (this.$chart) {
      let { target } = event.originalEvent;
      while (target) {
        if (target.className === 'ol-overlaycontainer-stopevent') {
          this.$chart
            .getZr()
            .painter.getViewportRoot()
            .dispatchEvent(mockEvent('mousemove', event));
          return;
        }
        target = target.parentElement;
      }
    }
  }

  /**
   * handle map change
   */
  private handleMapChanged() {
    const map = this.getMap();
    if (this._initEvent && this.$container) {
      // eslint-disable-next-line no-unused-expressions
      this.$container && removeNode(this.$container);
      this.unBindEvent();
    }

    if (!this.$container) {
      this.createLayerContainer();
      this.onResize(false);
    }

    if (map && this.$container) {
      const container = this._options.stopEvent
        ? map.getOverlayContainerStopEvent()
        : map.getOverlayContainer();
      if (this._options.insertFirst) {
        container.insertBefore(
          this.$container,
          container.childNodes[0] || null,
        );
      } else {
        container.appendChild(this.$container);
      }

      this.render();
      this.bindEvent(map);
    }
  }

  /**
   * create container
   */
  private createLayerContainer() {
    this.$container = document.createElement('div');
    this.$container.style.position = 'absolute';
    this.$container.style.top = '0px';
    this.$container.style.left = '0px';
    this.$container.style.right = '0px';
    this.$container.style.bottom = '0px';
    this.$container.style.pointerEvents = 'auto';
  }

  /**
   * register events
   * @private
   */
  private bindEvent(map: any) {
    // https://github.com/openlayers/openlayers/issues/7284
    const view = map.getView();
    if (this._options.forcedPrecomposeRerender) {
      map.on('precompose', this.redraw);
    }
    map.on('change:size', this.onResize);
    view.on('change:rotation', this.onDragRotateEnd);
    map.on('movestart', this.onMoveStart);
    if (this._options.polyfillEvents) {
      map.on('pointerdown', this.mouseDown);
      map.on('pointerup', this.mouseUp);
      map.on('pointermove', this.mouseMove);
      map.on('click', this.onClick);
    }
    this._initEvent = true;
  }

  /**
   * un register events
   * @private
   */
  private unBindEvent() {
    const map = this.getMap();
    if (!map) return;
    const view = map.getView();
    if (!view) return;
    map.un('precompose', this.redraw);
    map.un('change:size', this.onResize);
    view.un('change:rotation', this.onDragRotateEnd);
    map.un('movestart', this.onMoveStart);
    if (this._options.polyfillEvents) {
      map.un('pointermove', this.mouseMove);
      map.un('click', this.onClick);
    }
    this._initEvent = false;
  }

  /**
   * clear chart and redraw
   * @private
   */
  private clearAndRedraw() {
    if (!this.$chart || !this.isVisible()) return;
    if (this._options.forcedRerender) {
      this.$chart.clear();
    }
    // this.$chart.resize();
    if (this._chartOptions) {
      this.registerMap();
      this.$chart.setOption(this._chartOptions, false);
      if (this._incremental && this._incremental.length > 0) {
        for (let i = 0; i < this._incremental.length; i += 1) {
          this.appendData(this._incremental[i], false);
        }
      }
    }

    this.dispatchEvent({
      type: 'redraw',
      source: this,
    });
  }

  /**
   * register map coordinate system
   * @private
   */
  private registerMap() {
    if (!this._isRegistered && this._coordinateSystem) {
      // @ts-ignore
      echarts.registerCoordinateSystem(
        this.coordinateSystemId,
        this._coordinateSystem as CoordinateSystemCreator,
      );
      this._isRegistered = true;
    }

    if (this._chartOptions) {
      // @ts-ignore
      const series = this._chartOptions.series as echarts.SeriesOption[];
      if (series && isObject(series)) {
        const { convertTypes } = this._options;
        if (convertTypes) {
          for (let i = series.length - 1; i >= 0; i -= 1) {
            const type = (series[i] ? series[i].type : '') as string;
            if (!((convertTypes as string[]).indexOf(type) > -1)) {
              series[i].coordinateSystem = this.coordinateSystemId;
            }
            series[i].animation = false;
          }
        }
      }
    }
  }

  /**
   * dispatch event
   * @param event
   */
  public dispatchEvent(event: object | Event | string) {
    return super.dispatchEvent(event as Event);
  }

  public set(key: string, value: any, optSilent?: boolean) {
    return super.set(key, value, optSilent);
  }

  public get(key: string) {
    return super.get(key);
  }

  public unset(key: string, optSilent?: boolean) {
    return super.unset(key, optSilent);
  }
}

export default EChartsLayer;
