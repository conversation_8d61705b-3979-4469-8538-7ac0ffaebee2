/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/**
 * 判断是否为对象
 * @param value
 * @returns {boolean}
 */
const isObject = (value: any): boolean => {
  const type = typeof value;
  return value !== null && (type === 'object' || type === 'function');
};

/**
 * merge
 * @param a
 * @param b
 * @returns {*}
 */
const merge = (a: any, b: any): any => {
  Object.keys(b).forEach((key) => {
    if (isObject(b[key]) && isObject(a[key])) {
      merge(a[key], b[key]);
    } else {
      // eslint-disable-next-line no-param-reassign
      a[key] = b[key];
    }
  });
  return a;
};

/**
 * bind context
 * @param func
 * @param context
 * @param args
 */
const bind =
  (func: Function, context: any, ...args: any[]): Function =>
  (...innerArgs: any[]) =>
    func.apply(context, args.concat(Array.prototype.slice.call(innerArgs)));

/**
 * add own item
 * @param array
 * @param item
 */
const arrayAdd = (array: any[], item: any): any[] => {
  let i = 0;
  let index;
  const { length } = array;
  for (; i < length; i += 1) {
    if (array[i].index === item.index) {
      index = i;
      break;
    }
  }
  if (index === undefined) {
    array.push(item);
  } else {
    // eslint-disable-next-line no-param-reassign
    array[index] = item;
  }
  return array;
};

const uuid = (): string => {
  function rd(a?: number | undefined) {
    // eslint-disable-next-line no-mixed-operators,no-bitwise
    return a
      ? // eslint-disable-next-line no-bitwise
        (a ^ ((Math.random() * 16) >> (a / 4))).toString(16)
      : // @ts-ignore
        ([1e7] + -[1e3] + -4e3 + -8e3 + -1e11).replace(/[018]/g, rd);
  }
  return rd();
};

/**
 * bind function array
 * @param fns
 * @param context
 */
function bindAll(fns: string[] | number[], context: any) {
  fns.forEach((fn: string | number) => {
    if (!context[fn]) {
      return;
    }
    // eslint-disable-next-line no-param-reassign
    context[fn] = context[fn].bind(context);
  });
}

/**
 * remove node
 * @param node
 */
function removeNode(node: HTMLElement) {
  return node?.parentNode ? node.parentNode.removeChild(node) : null;
}

/**
 * mock zrender mouse event
 * @param type
 * @param event
 */
function mockEvent(type: string, event: any) {
  const e = new MouseEvent(type, {
    // set bubbles, so zrender can receive the mock event. ref: https://dom.spec.whatwg.org/#interface-event
    // "event.bubbles": Returns true or false depending on how event was initialized.
    // True if event goes through its target’s ancestors in reverse tree order, and false otherwise
    bubbles: true,
    cancelable: true,
    button: event.pointerEvent.button,
    buttons: event.pointerEvent.buttons,
    clientX: event.pointerEvent.clientX,
    clientY: event.pointerEvent.clientY,
    // @ts-ignore
    zrX: event.pointerEvent.offsetX,
    zrY: event.pointerEvent.offsetY,
    movementX: event.pointerEvent.movementX,
    movementY: event.pointerEvent.movementY,
    relatedTarget: event.pointerEvent.relatedTarget,
    screenX: event.pointerEvent.screenX,
    screenY: event.pointerEvent.screenY,
    view: window,
  });
  // e.zrX = event.pointerEvent.offsetX;
  // e.zrY = event.pointerEvent.offsetY;
  // e.event = e;
  return e;
}

export function semver(a: string, b: string) {
  const pa = a.split('.');
  const pb = b.split('.');
  for (let i = 0; i < 3; i += 1) {
    const na = Number(pa[i]);
    const nb = Number(pb[i]);
    if (na > nb) return 1;
    if (nb > na) return -1;
    // eslint-disable-next-line no-restricted-globals
    if (!Number.isNaN(na) && Number.isNaN(nb)) return 1;
    // eslint-disable-next-line no-restricted-globals
    if (Number.isNaN(na) && !Number.isNaN(nb)) return -1;
  }
  return 0;
}

export {
  arrayAdd,
  bind,
  bindAll,
  isObject,
  merge,
  mockEvent,
  removeNode,
  uuid,
};
