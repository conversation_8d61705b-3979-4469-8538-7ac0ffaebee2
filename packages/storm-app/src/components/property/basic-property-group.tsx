/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PropertyValue } from '@waterdesk/data/device';
import BasicPropertyCategory from '@waterdesk/data/property/basic-property-category';
import {
  getPropertyInfo,
  getPropertyValue,
} from '@waterdesk/data/property/property-info';
import { ReactNode, useEffect, useState } from 'react';
import PropertyItem from './property-item';

interface Props {
  propertyCategory: BasicPropertyCategory;
  propertyValues: Map<string, PropertyValue | PropertyValue[]>;
  saveProperty?: (params: {
    value: string | number | undefined | null;
    oname: string | undefined;
    otype: string | undefined;
    vprop: string;
  }) => Promise<boolean>;
}

export default function BasicPropertyGroup(props: Props) {
  const { propertyCategory, propertyValues, saveProperty } = props;
  const [propertyDom, setPropertyDom] = useState<ReactNode[]>([]);
  useEffect(() => {
    setPropertyDom(
      propertyCategory.propertyItems.map((item) => {
        const data = getPropertyValue(propertyValues, item.name, item.unit);
        const propertyInfo = getPropertyInfo(propertyValues, item.name);
        return (
          <PropertyItem
            key={item.name}
            label={item.title}
            value={data.value}
            editable={item.editable}
            comparable={item.comparable}
            compareValue={data.compareValue}
            unitFormat={data.unitFormat}
            editProperty={(value) =>
              saveProperty?.({
                value,
                oname: propertyInfo.oname,
                otype: propertyInfo.otype,
                vprop: propertyInfo.vprop,
              })
            }
            buttons={item.editors}
          />
        );
      }),
    );
  }, [propertyCategory, propertyValues]);
  return <div>{propertyDom.map((item) => item)}</div>;
}
