/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { ScadaModelTimeData } from '@waterdesk/data/device-time-data';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import {
  FlowRelativeIndicatorValue,
  IndicatorObject,
  PressureRelativeIndicatorValue,
} from '@waterdesk/data/indicator';
import { makeObjectId } from '@waterdesk/data/object-item';
import CorrelationPropertyCategory from '@waterdesk/data/property/correlation-property-category';
import { Button, Space, Table, Tabs, TabsProps } from 'antd';
import { ColumnsType } from 'antd/lib/table/interface';
import { ReactNode, useEffect, useState } from 'react';
import HighlightIcon from 'src/components/icon/highlight-icon';
import ScadaLabel from 'src/components/scada-label';
import { getColumnSearchProps } from 'src/components/table/column';

interface Props {
  propertyCategory: CorrelationPropertyCategory;
  indicators: Array<IndicatorObject>;
  date?: string;
  currentTime: string;
  database: Database;
  getPressureCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: PressureRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>;
  getFlowCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: FlowRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>;
  displayCorrelationDeviceChart: (
    correlationDevice:
      | PressureRelativeIndicatorValue
      | FlowRelativeIndicatorValue,
  ) => ReactNode;
  handleRelativeHighlight: (
    deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => void;
  handleLocate: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  handleHover: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
}

export default function CorrelationPropertyGroup(props: Props) {
  const {
    propertyCategory,
    indicators,
    database,
    getPressureCorrelationDevices,
    getFlowCorrelationDevices,
    displayCorrelationDeviceChart,
    handleRelativeHighlight,
    handleLocate,
    handleHover,
    date,
    currentTime,
  } = props;
  const [activeKey, setActiveKey] = useState<string>('');
  const [tabPanels, setTabPanels] = useState<TabsProps['items']>();
  const [highlightState, setHighlightState] = useState<boolean>(false);
  const [resultData, setResultData] = useState<
    Map<
      string,
      {
        deviceId: string;
        data: PressureRelativeIndicatorValue[] | FlowRelativeIndicatorValue[];
      }
    >
  >(new Map());

  const [otherHighlightObject, setOtherHighlightObject] = useState<
    Map<
      string,
      {
        deviceId: string;
        data: HighlightObject[];
      }
    >
  >(new Map());

  const getScadaDataCell = (id: string): ScadaModelTimeData | undefined =>
    database.currentDeviceTimeData.getIndicatorValueById(id);

  const pressureColumns: ColumnsType<PressureRelativeIndicatorValue> = [
    {
      title: '设备名称',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      width: 90,
      render: (value, record) =>
        record.shape ? (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleLocate(record)}
            onMouseOver={() => handleHover(record)}
            onMouseOut={() => handleHover(undefined)}
          >
            {value}
          </Button>
        ) : (
          value
        ),
      ...getColumnSearchProps('title'),
    },
    {
      title: '相关系数',
      dataIndex: 'coeff',
      key: 'coeff',
      align: 'center',
      ellipsis: true,
      width: 90,
      defaultSortOrder: 'descend',
      sorter: (a, b) => Number(a.coeff) - Number(b.coeff),
      render: (_value, record) => record.coeffWithSymbol,
    },
    {
      title: '监测',
      dataIndex: 'scadaValue',
      key: 'scadaValue',
      ellipsis: true,
      width: 70,
      render: (_value, record) => (
        <Space>
          <ScadaLabel
            id={record.key}
            dataType="scadaData"
            getScadaDataCell={getScadaDataCell}
            currentTimeDataChanged={currentTime}
          />
          {record.scadaValueUnit}
        </Space>
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 70,
      render: (_value, record) => displayCorrelationDeviceChart(record),
    },
  ];

  const relationTranslation = {
    ADJACENT: '相邻',
    UP_STREAM: '上游',
    DOWN_STREAM: '下游',
  };

  const flowColumns: ColumnsType<FlowRelativeIndicatorValue> = [
    {
      title: '设备名称',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      width: 90,
      render: (value, record) =>
        record.shape ? (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleLocate(record)}
            onMouseOver={() => handleHover(record)}
            onMouseOut={() => handleHover(undefined)}
          >
            {value}
          </Button>
        ) : (
          value
        ),
      ...getColumnSearchProps('title'),
    },
    {
      title: '关系',
      dataIndex: 'relation',
      key: 'relation',
      align: 'center',
      ellipsis: true,
      width: 70,
      sorter: (a, b) => a.relation.localeCompare(b.relation),
      render: (value: 'ADJACENT' | 'UP_STREAM' | 'DOWN_STREAM') =>
        relationTranslation[value],
    },
    {
      title: '监测',
      dataIndex: 'scadaValue',
      key: 'scadaValue',
      ellipsis: true,
      width: 110,
      render: (_value, record) => (
        <Space>
          <ScadaLabel
            id={record.key}
            dataType="scadaData"
            getScadaDataCell={getScadaDataCell}
            currentTimeDataChanged={currentTime}
          />
          <ScadaLabel
            id={record.key}
            dataType="simulationData"
            getScadaDataCell={getScadaDataCell}
            currentTimeDataChanged={currentTime}
          />
          {record.scadaValueUnit}
        </Space>
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 70,
      render: (_value, record) => displayCorrelationDeviceChart(record),
    },
  ];

  const getCorrelationTableByOtype = (
    otype: string,
    dataSource: PressureRelativeIndicatorValue[] | FlowRelativeIndicatorValue[],
  ): ReactNode => {
    switch (otype) {
      case 'SDVAL_PRESS_W':
        return (
          <Table
            size="small"
            rowKey="key"
            dataSource={dataSource as PressureRelativeIndicatorValue[]}
            columns={pressureColumns}
          />
        );
      case 'SDVAL_FLOW_W':
      case 'SDVAL_TURBIDITY':
      case 'SDVAL_CR':
        return (
          <Table
            size="small"
            rowKey="key"
            dataSource={dataSource as FlowRelativeIndicatorValue[]}
            columns={flowColumns}
          />
        );
      default:
        return null;
    }
  };

  const tabsOnChange = (activeKey: string) => {
    setActiveKey(activeKey);
    setHighlightState(false);
    handleRelativeHighlight();
  };

  const clearHighlight = () => {
    handleRelativeHighlight();
  };

  const handleHighlight = () => {
    if (highlightState) {
      clearHighlight();
    } else {
      handleRelativeHighlight(resultData.get(activeKey)?.deviceId, [
        ...(resultData.get(activeKey)?.data ?? []),
        ...(otherHighlightObject.get(activeKey)?.data ?? []),
      ]);
    }
    setHighlightState((state) => !state);
  };

  useEffect(() => {
    if (typeof date === 'undefined') return;
    const candidateIndicators: IndicatorObject[] = [];
    propertyCategory.indicatorPropertyItems.forEach((indicatorProperty) => {
      const [otype] = indicatorProperty;
      indicators.forEach((indicator) => {
        if (indicator.otype === otype) candidateIndicators.push(indicator);
      });
    });

    Promise.all(
      candidateIndicators.map((item) => {
        switch (item.otype) {
          case 'SDVAL_FLOW_W':
          case 'SDVAL_TURBIDITY':
          case 'SDVAL_CR':
            return getFlowCorrelationDevices(item.otype, item.oname, date);
          default:
            return getPressureCorrelationDevices(item.otype, item.oname, date);
        }
      }),
    ).then((results) => {
      const items: TabsProps['items'] = [];
      const correlationDevicesMap: Map<
        string,
        PressureRelativeIndicatorValue[] | FlowRelativeIndicatorValue[]
      > = new Map();
      const resultMap: typeof resultData = new Map();
      const otherHighlightObjectMap: typeof otherHighlightObject = new Map();
      if (candidateIndicators.length === results.length) {
        for (let i = 0; i < results.length; i += 1) {
          const { otype, oname, ptype, pname } = candidateIndicators[i];
          const tabKey = `${otype}${oname}`;
          let tabTitle = database.getPropertyInfo(
            candidateIndicators[i].otype,
          )?.title;
          if (tabTitle === undefined) tabTitle = otype;
          const deviceId = makeObjectId(ptype ?? '', pname ?? '');
          resultMap.set(tabKey, {
            deviceId,
            data: results[i].relativeIndicators,
          });
          otherHighlightObjectMap.set(tabKey, {
            deviceId,
            data: results[i].relativeLineString,
          });
          items.push({
            key: tabKey,
            label: tabTitle,
            children: getCorrelationTableByOtype(
              otype,
              results[i].relativeIndicators,
            ),
          });
          correlationDevicesMap.set(tabKey, results[i].relativeIndicators);
        }
      }
      setResultData(resultMap);
      setOtherHighlightObject(otherHighlightObjectMap);
      setTabPanels(items);
      setActiveKey(items[0].key ?? '');
      setHighlightState(false);
    });
  }, [propertyCategory, indicators, date]);

  return (
    <Tabs
      items={tabPanels}
      activeKey={activeKey}
      onChange={tabsOnChange}
      tabBarExtraContent={
        <HighlightIcon
          active={highlightState}
          showIconText
          onClick={handleHighlight}
        />
      }
    />
  );
}
