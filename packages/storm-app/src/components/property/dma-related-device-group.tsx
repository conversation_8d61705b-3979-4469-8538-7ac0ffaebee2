/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HighlightObject } from '@waterdesk/data/highlight-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  DmaRelatedDevice,
  DmaType,
} from '@waterdesk/data/property/dma-related-devices-category';
import { formatNumber } from '@waterdesk/data/utils';
import { Button, Col, Row, Table, Tabs } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import HighlightIcon from 'src/components/icon/highlight-icon';

interface Props {
  time: string;
  dmaType: DmaType | undefined;
  selectedObject: IObjectItem;
  getDeviceList: (
    otype: string,
    oname: string,
    time: string,
    dmaType: DmaType | undefined,
  ) => Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }>;
  handleLocate: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  handleHighlight?: (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => void;
}

export default function DmaRelatedDeviceGroup(props: Props) {
  const {
    time,
    dmaType,
    selectedObject,
    getDeviceList,
    handleLocate,
    handleHighlight,
  } = props;

  const [outDeviceList, setOutDeviceList] = useState<DmaRelatedDevice[]>([]);
  const [inDeviceList, setInDeviceList] = useState<DmaRelatedDevice[]>([]);
  const [historyTotalFlow, setHistoryTotalFlow] = useState<number>(0);
  const [currentTotalFlow, setCurrentTotalFlow] = useState<number>(0);
  const [highlightState, setHighlightState] = useState<boolean>(false);
  const columns: ColumnsType<DmaRelatedDevice> = [
    {
      title: '设备名称',
      dataIndex: 'deviceTitle',
      ellipsis: true,
      render: (value, record) =>
        record.shape ? (
          <Button
            style={{ padding: '0', height: '22px' }}
            type="link"
            onClick={() => handleLocate(record)}
          >
            {value}
          </Button>
        ) : (
          value
        ),
    },
    {
      title: '昨日供水量',
      dataIndex: 'historySupplyFlow',
      align: 'right',
    },
    {
      title: '当前供水量',
      dataIndex: 'supplyFlow',
      align: 'right',
    },
  ];

  useEffect(() => {
    if (!selectedObject) return;
    getDeviceList(
      selectedObject.otype,
      selectedObject.oname,
      time,
      dmaType,
    ).then((res) => {
      setInDeviceList(res.inDeviceList);
      setOutDeviceList(res.outDeviceList);
      const inTotalFlow = res.inDeviceList.reduce(
        (prev, item) => item.historySupplyFlowNumber + prev,
        0,
      );
      const outTotalFlow = res.outDeviceList.reduce(
        (prev, item) => item.historySupplyFlowNumber + prev,
        0,
      );
      setHistoryTotalFlow(inTotalFlow - outTotalFlow);

      const currentInTotalFlow = res.inDeviceList.reduce(
        (prev, item) => item.supplyFlowNumber + prev,
        0,
      );
      const currentOutTotalFlow = res.outDeviceList.reduce(
        (prev, item) => item.supplyFlowNumber + prev,
        0,
      );
      setCurrentTotalFlow(
        formatNumber(currentInTotalFlow - currentOutTotalFlow, 1),
      );
    });
    setHighlightState(false);
  }, [selectedObject, time]);

  const handleHighlightDeviceList = () => {
    handleHighlight?.([...inDeviceList, ...outDeviceList], !highlightState);
    setHighlightState(!highlightState);
  };

  const getFooter = () => (
    <Row>
      <Col span={24}>昨日总供水量: {historyTotalFlow} m³</Col>
      <Col span={24}>瞬时总供水量: {currentTotalFlow} m³/h</Col>
    </Row>
  );
  return (
    <>
      <Tabs
        tabBarExtraContent={
          typeof handleHighlight === 'undefined' ? null : (
            <HighlightIcon
              showIconText
              active={highlightState}
              onClick={() => handleHighlightDeviceList()}
            />
          )
        }
        items={[
          {
            key: 'inDeviceList',
            label: `流入设备${
              inDeviceList.length ? `(${inDeviceList.length})` : ''
            }`,
            closable: false,
            children: (
              <Table
                size="small"
                pagination={false}
                columns={columns}
                dataSource={inDeviceList}
                bordered={false}
                rowKey="id"
              />
            ),
          },
          {
            key: 'outDeviceList',
            label: `流出设备${
              outDeviceList.length ? `(${outDeviceList.length})` : ''
            }`,
            closable: false,
            children: (
              <Table
                size="small"
                pagination={false}
                columns={columns}
                dataSource={outDeviceList}
                bordered={false}
                rowKey="id"
              />
            ),
          },
        ]}
      />
      {getFooter()}
    </>
  );
}
