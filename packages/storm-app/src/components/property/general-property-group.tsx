/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PropertyValue } from '@waterdesk/data/device';
import { IndicatorObject } from '@waterdesk/data/indicator';
import { replaceIndicatorTitle } from '@waterdesk/data/object-chart';
import { IObjectItem } from '@waterdesk/data/object-item';
import GeneralPropertyCategory from '@waterdesk/data/property/general-property-category';
import { PropertyButtonEditor } from '@waterdesk/data/property/property-info';
import { getUnitValueWithSymbol } from '@waterdesk/data/unit-system';
import { ReactNode, useEffect, useState } from 'react';
import PropertyItem, { PropertyItemValue } from './property-item';

interface Props {
  selectedObject: IObjectItem;
  propertyCategory: GeneralPropertyCategory;
  indicators: Array<IndicatorObject>;
  propertyValues: Map<string, PropertyValue | PropertyValue[]>;
  getButtonByType: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode;
  saveProperty?: (params: {
    value: string | number | undefined | null;
    oname: string | undefined;
    otype: string | undefined;
    vprop: string;
  }) => Promise<boolean>;
}

function getPropertyValue(
  propertyValues: Map<string, PropertyValue | PropertyValue[]>,
  key: string,
  unit: string,
) {
  const propertyValue = propertyValues.get(key);
  let value;
  let compareValue: string[] | number[] | undefined[] = [];
  if (Array.isArray(propertyValue)) {
    value = getUnitValueWithSymbol(
      unit,
      propertyValue[0].value as string | number,
    );
    const [, ...args] = propertyValue;
    compareValue = args.map((property) =>
      getUnitValueWithSymbol(unit, property.value as string | number),
    );
  } else {
    value = getUnitValueWithSymbol(
      unit,
      propertyValue?.value as string | number,
    );
  }
  return {
    value,
    compareValue,
  };
}

export default function GeneralPropertyGroup(props: Props) {
  const {
    selectedObject,
    propertyCategory,
    indicators,
    propertyValues,
    saveProperty,
    getButtonByType,
  } = props;
  const [propertyDom, setPropertyDom] = useState<ReactNode[]>([]);
  useEffect(() => {
    const propertyItems: Array<PropertyItemValue> = [];
    propertyCategory.propertyItems.forEach((item) => {
      if (item.type === 'Indicator') {
        indicators.forEach((indicator) => {
          if (indicator.otype === item.otype) {
            const key = `${indicator.oname}@${indicator.otype}@${item.name}`;
            const data = getPropertyValue(propertyValues, key, item.unit);
            propertyItems.push({
              selectedObject,
              indicatorType: indicator.otype,
              indicatorName: indicator.oname,
              vprop: item.name,
              key,
              label: replaceIndicatorTitle(
                indicator.title ?? item.title,
                selectedObject.title,
              ),
              value: data.value,
              comparable: item.comparable,
              compareValue: data.compareValue,
              buttons: item.editors,
            });
          }
        });
      } else {
        const key = `${selectedObject.oname}@${item.name}`;
        const data = getPropertyValue(propertyValues, item.name, item.unit);
        propertyItems.push({
          selectedObject,
          indicatorType: undefined,
          indicatorName: undefined,
          vprop: item.name,
          key,
          label: item.title,
          value: data.value,
          comparable: item.comparable,
          compareValue: data.compareValue,
          buttons: item.editors,
        });
      }
    });

    setPropertyDom(
      propertyItems.map((item) => (
        <PropertyItem
          itemValue={item}
          key={item.key}
          label={item.label}
          value={item.value}
          comparable={item.comparable}
          compareValue={item.compareValue}
          editProperty={(value) =>
            saveProperty?.({
              value,
              oname: item.indicatorName,
              otype: item.indicatorType,
              vprop: item.vprop,
            })
          }
          buttons={item.buttons}
          getButtonByType={getButtonByType}
        />
      )),
    );
  }, [propertyCategory, indicators, propertyValues]);
  return <div>{propertyDom.map((item) => item)}</div>;
}
