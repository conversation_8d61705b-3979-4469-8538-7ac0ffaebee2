/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ReactNode, useEffect, useState } from 'react';
import PropertyItem from './property-item';

interface Props {
  attributes: Array<[string, any]>;
}

export default function GisPropertyGroup(props: Props) {
  const { attributes } = props;
  const [propertyDom, setPropertyDom] = useState<ReactNode[]>([]);
  useEffect(() => {
    setPropertyDom(
      attributes.map((item) => (
        <PropertyItem
          key={item[0]}
          label={item[0]}
          value={item[1]}
        />
      )),
    );
  }, [attributes]);
  return <div>{propertyDom.map((item) => item)}</div>;
}
