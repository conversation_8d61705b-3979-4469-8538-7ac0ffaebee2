/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { PropertyValue } from '@waterdesk/data/device';
import { IndicatorObject } from '@waterdesk/data/indicator';
import { IObjectItem } from '@waterdesk/data/object-item';
import IndicatorPropertyCategory from '@waterdesk/data/property/indicator-property-category';
import {
  getPropertyValue,
  PropertyButtonEditor,
} from '@waterdesk/data/property/property-info';
import { Tabs } from 'antd';
import { ReactNode, useEffect, useState } from 'react';
import PropertyItem, { PropertyItemValue } from './property-item';

interface Props {
  selectedObject: IObjectItem;
  propertyCategory: IndicatorPropertyCategory;
  indicators: Array<IndicatorObject>;
  propertyValues: Map<string, PropertyValue | PropertyValue[]>;
  database: Database;
  getButtonByType: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode;
}

export default function IndicatorPropertyGroup(props: Props) {
  const {
    selectedObject,
    propertyCategory,
    indicators,
    propertyValues,
    database,
    getButtonByType,
  } = props;
  const [propertyDom, setPropertyDom] = useState<ReactNode>();
  useEffect(() => {
    const tabPropertyItems: Array<[string, Array<PropertyItemValue>]> = [];
    propertyCategory.indicatorPropertyItems.forEach((indicatorProperty) => {
      const otype = indicatorProperty[0];
      const objectProperties = indicatorProperty[1];
      indicators.forEach((indicator) => {
        if (indicator.otype === otype) {
          const propertyItems: Array<PropertyItemValue> = [];
          objectProperties.forEach((propertyItem) => {
            const key = `${indicator.oname}@${otype}@${propertyItem.name}`;
            const data = getPropertyValue(
              propertyValues,
              key,
              propertyItem.unit,
            );
            propertyItems.push({
              selectedObject,
              indicatorType: otype,
              indicatorName: indicator.oname,
              vprop: propertyItem.name,
              key,
              label: propertyItem.title,
              value: data.value,
              comparable: propertyItem.comparable,
              compareValue: data.compareValue,
              unitFormat: data.unitFormat,
              buttons: propertyItem.editors,
            });
          });
          tabPropertyItems.push([otype, propertyItems]);
        }
      });
    });

    const tabPanels = tabPropertyItems.map((tabItem, index) => {
      const [otype, propertyItems] = tabItem;
      const tabKey = `${otype}${index}`;
      let tabTitle = database.getPropertyInfo(otype)?.title;
      if (tabTitle === undefined) tabTitle = otype;
      return {
        key: tabKey,
        label: tabTitle,
        children: (
          <>
            {propertyItems.map((item) => (
              <PropertyItem
                itemValue={item}
                key={item.key}
                label={item.label}
                value={item.value}
                comparable={item.comparable}
                compareValue={item.compareValue}
                buttons={item.buttons}
                getButtonByType={getButtonByType}
              />
            ))}
          </>
        ),
      };
    });

    setPropertyDom(<Tabs items={tabPanels} />);
  }, [propertyCategory, indicators, propertyValues]);
  return <div>{propertyDom}</div>;
}
