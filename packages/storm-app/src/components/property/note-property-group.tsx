/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import NotePropertyCategory from '@waterdesk/data/property/note-property-category';
import { saveNote } from '@waterdesk/request/api/note-property';
import { Button, Form, FormInstance, Input, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

interface Props {
  propertyCategory: NotePropertyCategory;
  otype: string;
  oname: string;
  note: string;
}

export default function NotePropertyGroup(props: Props) {
  const { propertyCategory, otype, oname, note } = props;
  const [editable, setEditable] = useState<boolean>(false);
  const [textareaValue, setTextareaValue] = useState<string>('');
  const editorRef = useRef<FormInstance<{ note: string }>>(null);
  useEffect(() => {
    setTextareaValue(note);
  }, [propertyCategory]);

  const onChangeTextArea = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditable(true);
    setTextareaValue(e.target.value);
  };

  const cancel = () => {
    editorRef.current!.setFieldsValue({
      note,
    });
    setEditable(false);
  };

  const updateNote = (values: { note: string }) => {
    const vprop = propertyCategory.propertyItems[0].name;
    saveNote(otype, oname, vprop, values.note);
  };

  return (
    <Form
      ref={editorRef}
      onFinish={updateNote}
    >
      <Form.Item
        name="note"
        initialValue={textareaValue}
        style={{ marginBottom: 0 }}
      >
        <Input.TextArea
          autoSize={{ minRows: 5 }}
          value={textareaValue}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
            onChangeTextArea(e)
          }
        />
      </Form.Item>
      <Form.Item
        style={{ marginBottom: 0, marginRight: '10px', textAlign: 'right' }}
      >
        <Space>
          {editable && (
            <>
              <Button
                size="small"
                onClick={() => cancel()}
              >
                取消
              </Button>
              <Button
                size="small"
                htmlType="submit"
              >
                保存
              </Button>
            </>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
}
