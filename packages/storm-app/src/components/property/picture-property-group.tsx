/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import PicturePropertyCategory from '@waterdesk/data/property/picture-property-category';
import { Carousel, Empty, Image } from 'antd';
import { ReactNode, useEffect, useState } from 'react';
import { BASE_URL } from 'src/config';
import styled from 'styled-components';

interface Props {
  deviceId: string | undefined;
  propertyCategory: PicturePropertyCategory;
  getPictureList: (deviceId: string) => Promise<string[]>;
}

const CarouselWrapper = styled(Carousel)`
  &.imagesList {
    text-align: center;
  }
`;

export default function PicturePropertyGroup(props: Props) {
  const { propertyCategory, deviceId, getPictureList } = props;
  const [propertyDom, setPropertyDom] = useState<ReactNode[]>([]);
  useEffect(() => {
    if (!deviceId) return;
    getPictureList(deviceId).then((pictureList) => {
      if (pictureList) {
        if (pictureList.length > 0) {
          setPropertyDom(
            pictureList.map((item) => (
              <Image
                key={item}
                height="100%"
                src={`${BASE_URL}/watergis/getOnamePicFile?otype_oname=${deviceId}&icon_name=${item}`}
              />
            )),
          );
        } else {
          setPropertyDom([
            <Empty
              key="empty"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无图片"
            />,
          ]);
        }
      }
    });
  }, [propertyCategory, deviceId]);
  return (
    <CarouselWrapper
      autoplay
      className="imagesList"
    >
      {propertyDom.map((item) => item)}
    </CarouselWrapper>
  );
}
