/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { IObjectItem } from '@waterdesk/data/object-item';
import { PropertyButtonEditor } from '@waterdesk/data/property/property-info';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { Col, Input, message, Tooltip } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';
import { ReactNode, useEffect, useState } from 'react';
import { PropertyRowWrapper } from './style';

export interface PropertyItemValue {
  selectedObject: IObjectItem;
  indicatorType?: string;
  indicatorName?: string;
  vprop: string;
  key: string;
  label: string;
  value: string | number | undefined;
  comparable?: boolean;
  compareValue?: (string | number | undefined)[];
  unitFormat?: UnitFormat;
  buttons: PropertyButtonEditor[] | undefined;
}

interface Props {
  itemValue?: PropertyItemValue;
  label: string;
  value?: string | number;
  comparable?: boolean;
  compareValue?: (string | number | undefined)[];
  editable?: 'number' | boolean;
  buttons?: PropertyButtonEditor[];
  unitFormat?: UnitFormat;
  editProperty?: (
    value: string | number | undefined | null,
  ) => Promise<boolean> | undefined;
  getButtonByType?: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode;
}

const Editor = (props: {
  editorType: 'number' | boolean;
  defaultValue: string | number | undefined;
  unitSymbol: string | undefined;
  messageApi: MessageInstance;
  editProperty?: (
    value: string | number | undefined | null,
  ) => Promise<boolean> | undefined;
}) => {
  const { editorType, defaultValue, unitSymbol, messageApi, editProperty } =
    props;
  const { token } = useToken();
  const [value, setVlaue] = useState<string>();
  const [oldValue, setOldVlaue] = useState<string>();
  useEffect(() => {
    setVlaue(defaultValue?.toString());
    setOldVlaue(defaultValue?.toString());
  }, [defaultValue]);
  const saveProperty = async () => {
    if (oldValue !== value && editProperty) {
      const editResult = await editProperty(value);
      if (editResult) {
        messageApi.success('修改成功!');
        setOldVlaue(value);
      } else {
        setVlaue(oldValue);
        messageApi.error('修改失败!');
      }
    }
  };

  if (!editorType) return null;

  return (
    <Input
      type="number"
      variant="borderless"
      style={{
        borderBottom: `1px solid ${token.colorPrimary}`,
        borderRadius: 0,
      }}
      value={value}
      suffix={unitSymbol}
      onChange={(e) => setVlaue(e.target.value)}
      onBlur={saveProperty}
      onPressEnter={saveProperty}
    />
  );
};

export default function PropertyItem(props: Props) {
  const {
    itemValue,
    label,
    value,
    comparable,
    compareValue,
    editable,
    buttons,
    unitFormat,
    editProperty,
    getButtonByType,
  } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const getButton = (propertyButton: PropertyButtonEditor): ReactNode =>
    getButtonByType ? getButtonByType(propertyButton, itemValue) : null;

  return (
    <PropertyRowWrapper>
      <Col
        className="row-title"
        span={10}
      >
        <Tooltip title={label}>{label}</Tooltip>
      </Col>
      <Col
        span={14}
        className="row-content"
      >
        <div className="row-content-wrapper">
          {!editable ? (
            <div className="row-content-text">
              {value}
              {unitFormat?.unitSymbol}
            </div>
          ) : (
            <Editor
              editProperty={editProperty}
              editorType={editable}
              defaultValue={value}
              unitSymbol={unitFormat?.unitSymbol}
              messageApi={messageApi}
            />
          )}

          {comparable && compareValue && compareValue.length > 0
            ? compareValue?.map((item, index) => {
                const key = index;
                return (
                  <div
                    key={key}
                    className="row-content-text compare-value"
                  >
                    {item}
                    {unitFormat?.unitSymbol}
                  </div>
                );
              })
            : null}
        </div>
        {buttons ? (
          <div className="row-content-button-group">
            {buttons.map((item, index) => {
              const key = `${item.type}${index}`;
              return <div key={key}>{getButton(item)}</div>;
            })}
          </div>
        ) : null}
      </Col>
      {contextHolder}
    </PropertyRowWrapper>
  );
}
