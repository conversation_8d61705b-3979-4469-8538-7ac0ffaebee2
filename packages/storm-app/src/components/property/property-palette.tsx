/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AimOutlined,
  BoxPlotOutlined,
  BranchesOutlined,
  ControlOutlined,
  DeleteColumnOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { AppConfig } from '@waterdesk/data/app-config';
import Database from '@waterdesk/data/database';
import Device, { PropertyValue, StationDevice } from '@waterdesk/data/device';
import GisObject from '@waterdesk/data/gis-object';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import {
  FlowRelativeIndicatorValue,
  PressureRelativeIndicatorValue,
} from '@waterdesk/data/indicator';
import ModelObject from '@waterdesk/data/model-object';
import { IObjectItem } from '@waterdesk/data/object-item';
import BasicPropertyCategory from '@waterdesk/data/property/basic-property-category';
import CorrelationPropertyCategory from '@waterdesk/data/property/correlation-property-category';
import DmaRelatedDeviceCategory, {
  DmaRelatedDevice,
  DmaType,
} from '@waterdesk/data/property/dma-related-devices-category';
import GeneralPropertyCategory from '@waterdesk/data/property/general-property-category';
import GisPropertyCategory from '@waterdesk/data/property/gis-property-category';
import IndicatorPropertyCategory from '@waterdesk/data/property/indicator-property-category';
import PicturePropertyCategory from '@waterdesk/data/property/picture-property-category';
import {
  PropertyButtonEditor,
  PropertyCategory,
  PropertyInfo,
} from '@waterdesk/data/property/property-info';
import PumpStateCategory from '@waterdesk/data/property/pump-state-category';
import WarnPropertyCategory from '@waterdesk/data/property/warn-property-category';
import { GroupTimeData } from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { Button, Col, Collapse, Typography } from 'antd';
import { Card, Grid } from 'antd-mobile';
import { JSX, ReactNode, useEffect, useState } from 'react';
import BasicPropertyGroup from './basic-property-group';
import CorrelationPropertyGroup from './correlation-property-group';
import DmaRelatedDeviceGroup from './dma-related-device-group';
import GeneralPropertyGroup from './general-property-group';
import GisPropertyGroup from './gis-property-group';
import IndicatorPropertyGroup from './indicator-property-group';
import PicturePropertyGroup from './picture-property-group';
import { PropertyItemValue } from './property-item';
import PumpStateGroup from './pump-state-group';
import {
  CollapseHeaderWrapper,
  PropertyCollapseWrapper,
  PropertyRowWrapper,
} from './style';
import WarnPropertyGroup from './warn-property-group';

interface Props {
  selectedObject: IObjectItem;
  propValues: Map<string, PropertyValue | PropertyValue[]>;
  activePanels: string | string[] | undefined;
  date?: string;
  compare?: number;
  database: Database;
  appConfig: AppConfig;
  getButtonByType: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode;
  time: string;
  getPressureCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: PressureRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>;
  getFlowCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: FlowRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>;
  displayCorrelationDeviceChart: (
    correlationDevice:
      | PressureRelativeIndicatorValue
      | FlowRelativeIndicatorValue,
  ) => ReactNode;
  handleRelativeHighlight: (
    deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => void;
  handleDmaRelatedDeviceHighlight: (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => void;
  handleSelect: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  handleHover: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
  handleNavigate: (objectItem: IObjectItem) => void;
  handleChangeActivePanels: (panelKeys: string[]) => void;
  handleShowValveEditor: (otype: string, oname: string) => void;
  getPictureList: (deviceId: string) => Promise<string[]>;
  getDeviceList: (
    otype: string,
    oname: string,
    time: string,
    dmaType: DmaType | undefined,
  ) => Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }>;
  getWarnData: (
    otype: string,
    oname: string,
  ) => Promise<{
    realtimeWarnList: WarnInfoList;
    assessmentWarnList: WarnInfoList;
  }>;
  displayWarnDeviceChart: (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    startDate?: string,
    endDate?: string,
  ) => void;
  handleUpstream?: () => void;
  handleDownstream?: () => void;
  handlePumpStatus?: () => void;
  handleBurstPipe?: () => void;
  handleValveAnalyse?: () => void;
  saveProperty?: (params: {
    value: string | number | undefined | null;
    oname: string | undefined;
    otype: string | undefined;
    vprop: string;
  }) => Promise<boolean>;
  getGroupObjectsTimeValues?: (valueGroup: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  }) => Promise<Map<string, GroupTimeData>>;
}

function getCompareHeader(compare: number | undefined): ReactNode {
  if (compare && compare > 1) {
    const compareHeader: ReactNode[] = [
      <Typography.Text
        key="base"
        strong
        className="row-content-text"
      >
        方案
      </Typography.Text>,
    ];
    for (let i = 1; i < compare; i += 1) {
      compareHeader.push(
        <Typography.Text
          key={i}
          strong
          className="row-content-text compare-value"
          style={{ border: 0 }}
        >
          对比方案
        </Typography.Text>,
      );
    }
    return (
      <PropertyRowWrapper>
        <Col
          className="row-title"
          span={6}
        />
        <Col
          className="row-content"
          span={18}
          style={{ border: 0 }}
        >
          <div className="row-content-wrapper">
            {compareHeader.map((item) => item)}
          </div>
        </Col>
      </PropertyRowWrapper>
    );
  }
  return null;
}

function getGisPropertyCategory(selectedObject: GisObject): ReactNode {
  const attributes: Array<[string, any]> = [...selectedObject.attributes];
  attributes.unshift(['图层', selectedObject.layerName]);
  return <GisPropertyGroup attributes={attributes} />;
}

function getObjectPropertyCategory(
  category: PropertyCategory,
  selectedObject: IObjectItem,
  propValues: Map<string, PropertyValue | PropertyValue[]>,
  database: Database,
  appConfig: AppConfig,
  getButtonByType: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode,
  time: string,
  handleRelativeHighlight: (
    deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => void,
  handleDmaRelatedDeviceHighlight: (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => void,
  getPressureCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: PressureRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>,
  getFlowCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: FlowRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>,
  getWarnData: (
    otype: string,
    oname: string,
  ) => Promise<{
    realtimeWarnList: WarnInfoList;
    assessmentWarnList: WarnInfoList;
  }>,
  displayWarnDeviceChart: (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    startDate?: string,
    endDate?: string,
  ) => void,
  displayCorrelationDeviceChart: (
    correlationDevice:
      | PressureRelativeIndicatorValue
      | FlowRelativeIndicatorValue,
  ) => ReactNode,
  handleLocate: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void,
  handleHover: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void,
  getPictureList: (deviceId: string) => Promise<string[]>,
  getDeviceList: (
    otype: string,
    oname: string,
    time: string,
    dmaType: DmaType | undefined,
  ) => Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }>,
  saveProperty?: (params: {
    value: string | number | undefined | null;
    oname: string | undefined;
    otype: string | undefined;
    vprop: string;
  }) => Promise<boolean>,
  date?: string,
  getGroupObjectsTimeValues?: (valueGroup: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  }) => Promise<Map<string, GroupTimeData>>,
): ReactNode {
  if (category instanceof BasicPropertyCategory) {
    return (
      <BasicPropertyGroup
        propertyCategory={category}
        propertyValues={propValues}
        saveProperty={saveProperty}
      />
    );
  }
  if (category instanceof GeneralPropertyCategory) {
    return (
      <GeneralPropertyGroup
        selectedObject={selectedObject}
        propertyCategory={category}
        indicators={selectedObject.indicators}
        propertyValues={propValues}
        getButtonByType={getButtonByType}
        saveProperty={saveProperty}
      />
    );
  }
  if (category instanceof IndicatorPropertyCategory) {
    return (
      <IndicatorPropertyGroup
        selectedObject={selectedObject}
        propertyCategory={category}
        database={database}
        indicators={selectedObject.indicators}
        propertyValues={propValues}
        getButtonByType={getButtonByType}
      />
    );
  }
  if (category instanceof CorrelationPropertyCategory) {
    return (
      <CorrelationPropertyGroup
        propertyCategory={category}
        indicators={selectedObject.indicators}
        database={database}
        getPressureCorrelationDevices={getPressureCorrelationDevices}
        getFlowCorrelationDevices={getFlowCorrelationDevices}
        displayCorrelationDeviceChart={displayCorrelationDeviceChart}
        handleRelativeHighlight={handleRelativeHighlight}
        handleLocate={handleLocate}
        handleHover={handleHover}
        date={date}
        currentTime={time}
      />
    );
  }
  if (category instanceof WarnPropertyCategory) {
    return (
      <WarnPropertyGroup
        selectedObject={selectedObject}
        getWarnData={getWarnData}
        displayWarnDeviceChart={displayWarnDeviceChart}
        date={date}
      />
    );
  }
  if (category instanceof PicturePropertyCategory) {
    return (
      <PicturePropertyGroup
        propertyCategory={category}
        deviceId={selectedObject.id}
        getPictureList={getPictureList}
      />
    );
  }
  if (category instanceof PumpStateCategory) {
    return (
      <PumpStateGroup
        appConfig={appConfig}
        pumpList={(selectedObject as StationDevice).pumpList}
        getGroupObjectsTimeValues={getGroupObjectsTimeValues}
      />
    );
  }
  if (category instanceof DmaRelatedDeviceCategory) {
    return (
      <DmaRelatedDeviceGroup
        time={time}
        dmaType={category.dmaType}
        selectedObject={selectedObject}
        getDeviceList={getDeviceList}
        handleLocate={handleLocate}
        handleHighlight={handleDmaRelatedDeviceHighlight}
      />
    );
  }
  return category.title;
}

function getPropertyCategory(
  category: PropertyCategory,
  selectedObject: IObjectItem,
  propValues: Map<string, PropertyValue | PropertyValue[]>,
  database: Database,
  appConfig: AppConfig,
  getButtonByType: (
    propertyButtonEditor: PropertyButtonEditor,
    propertyItemValue: PropertyItemValue | undefined,
    compareDevice?: IObjectItem,
  ) => ReactNode,
  time: string,
  handleRelativeHighlight: (
    deviceId?: string,
    highlightObject?: HighlightObject[],
  ) => void,
  handleDmaRelatedDeviceHighlight: (
    highlightObject?: HighlightObject[],
    displayHighlight?: boolean,
  ) => void,
  getPressureCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: PressureRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>,
  getFlowCorrelationDevices: (
    otype: string,
    oname: string,
    date: string,
  ) => Promise<{
    relativeIndicators: FlowRelativeIndicatorValue[];
    relativeLineString: HighlightObject[];
  }>,
  getWarnList: (
    otype: string,
    oname: string,
  ) => Promise<{
    realtimeWarnList: WarnInfoList;
    assessmentWarnList: WarnInfoList;
  }>,
  displayWarnDeviceChart: (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    startDate?: string,
    endDate?: string,
  ) => void,
  displayCorrelationDeviceChart: (
    correlationDevice:
      | PressureRelativeIndicatorValue
      | FlowRelativeIndicatorValue,
  ) => ReactNode,
  handleLocate: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void,
  handleHover: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void,
  getPictureList: (deviceId: string) => Promise<string[]>,
  getDeviceList: (
    otype: string,
    oname: string,
    time: string,
    dmaType: DmaType | undefined,
  ) => Promise<{
    inDeviceList: DmaRelatedDevice[];
    outDeviceList: DmaRelatedDevice[];
  }>,
  saveProperty?: (params: {
    value: string | number | undefined | null;
    oname: string | undefined;
    otype: string | undefined;
    vprop: string;
  }) => Promise<boolean>,
  date?: string,
  getGroupObjectsTimeValues?: (valueGroup: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  }) => Promise<Map<string, GroupTimeData>>,
): ReactNode {
  if (selectedObject instanceof GisObject) {
    if (category instanceof GisPropertyCategory)
      return getGisPropertyCategory(selectedObject);
    if (selectedObject.refModelObject)
      return getObjectPropertyCategory(
        category,
        selectedObject.refModelObject,
        propValues,
        database,
        appConfig,
        getButtonByType,
        time,
        handleRelativeHighlight,
        handleDmaRelatedDeviceHighlight,
        getPressureCorrelationDevices,
        getFlowCorrelationDevices,
        getWarnList,
        displayWarnDeviceChart,
        displayCorrelationDeviceChart,
        handleLocate,
        handleHover,
        getPictureList,
        getDeviceList,
        saveProperty,
        date,
        getGroupObjectsTimeValues,
      );
  }
  return getObjectPropertyCategory(
    category,
    selectedObject,
    propValues,
    database,
    appConfig,
    getButtonByType,
    time,
    handleRelativeHighlight,
    handleDmaRelatedDeviceHighlight,
    getPressureCorrelationDevices,
    getFlowCorrelationDevices,
    getWarnList,
    displayWarnDeviceChart,
    displayCorrelationDeviceChart,
    handleLocate,
    handleHover,
    getPictureList,
    getDeviceList,
    saveProperty,
    date,
    getGroupObjectsTimeValues,
  );
}

export default function PropertyPalette(props: Props) {
  const {
    selectedObject,
    activePanels,
    propValues,
    date,
    database,
    appConfig,
    getButtonByType,
    time,
    compare,
    getPressureCorrelationDevices,
    getFlowCorrelationDevices,
    displayCorrelationDeviceChart,
    getWarnData,
    displayWarnDeviceChart,
    handleRelativeHighlight,
    handleDmaRelatedDeviceHighlight,
    handleSelect,
    handleHover,
    handleNavigate,
    handleChangeActivePanels,
    handleShowValveEditor,
    getPictureList,
    getDeviceList,
    handleUpstream,
    handleDownstream,
    handlePumpStatus,
    handleBurstPipe,
    handleValveAnalyse,
    saveProperty,
    getGroupObjectsTimeValues,
  } = props;

  const [propertyInfo, setPropertyInfo] = useState<PropertyInfo | undefined>();
  const [activeKey, setActiveKey] = useState<string[]>([]);
  const [panel, setPanel] = useState<ReactNode>(null);

  useEffect(() => {
    if (activePanels) {
      setActiveKey([...activePanels]);
    } else {
      const keys: Set<string> = new Set();
      database.propertyInfos.forEach((info) => {
        if (info.categories.length > 0) keys.add(info.categories[0].title);
      });
      keys.add('GIS属性');
      setActiveKey([...keys]);
      handleChangeActivePanels([...keys]);
    }
  }, []);

  const getPropertyCategoryContent = (
    propertyInfo: PropertyInfo | undefined,
  ): ReactNode =>
    propertyInfo?.categories.map((element) => (
      <Collapse.Panel
        header={<CollapseHeaderWrapper>{element.title}</CollapseHeaderWrapper>}
        className="collapse-custom-panel"
        key={element.title}
      >
        <Card>
          {getPropertyCategory(
            element,
            selectedObject,
            propValues,
            database,
            appConfig,
            getButtonByType,
            time,
            handleRelativeHighlight,
            handleDmaRelatedDeviceHighlight,
            getPressureCorrelationDevices,
            getFlowCorrelationDevices,
            getWarnData,
            displayWarnDeviceChart,
            displayCorrelationDeviceChart,
            handleSelect,
            handleHover,
            getPictureList,
            getDeviceList,
            saveProperty,
            date,
            getGroupObjectsTimeValues,
          )}
        </Card>
      </Collapse.Panel>
    ));

  useEffect(() => {
    if (
      selectedObject instanceof Device ||
      selectedObject instanceof ModelObject
    ) {
      const info = database.getPropertyInfo(selectedObject.otype);
      setPanel(getPropertyCategoryContent(info));
      setPropertyInfo(info);
    } else if (selectedObject instanceof GisObject) {
      const gisCategory: GisPropertyCategory = new GisPropertyCategory(
        'GIS属性',
      );
      const info = new PropertyInfo('GIS_OBJECT', 'GIS属性');
      info.addCategory(gisCategory);
      if (selectedObject.refModelObject) {
        const modelObjectInfo = database.getPropertyInfo(
          selectedObject.refModelObject.otype,
        );

        modelObjectInfo?.categories.forEach((catetory) => {
          info.addCategory(catetory);
        });
      }
      setPanel(getPropertyCategoryContent(info));
      setPropertyInfo(info);
    }
  }, [selectedObject, propValues]);

  const onChange = (key: string | string[]) => {
    setActiveKey([...key]);
    handleChangeActivePanels([...key]);
  };

  const handleClickLocate = () => {
    if (selectedObject) handleNavigate(selectedObject);
  };

  const getButtonIcon = (
    type: string,
  ): { icon: JSX.Element; title?: string } => {
    switch (type) {
      case 'operations':
        return { icon: <ToolOutlined />, title: '操作记录' };
      case 'upstream':
        return { icon: <BranchesOutlined />, title: '来源追踪' };
      case 'downstream':
        return { icon: <BranchesOutlined rotate={180} />, title: '去向追踪' };
      case 'pumpstatus':
        return { icon: <ControlOutlined />, title: '开泵状态' };
      case 'burstPipe':
        return { icon: <DeleteColumnOutlined />, title: '爆管冲洗' };
      case 'valveAnalyse':
        return { icon: <BoxPlotOutlined />, title: '关阀分析' };
      default:
        return { icon: <AimOutlined />, title: '地图定位' };
    }
  };

  const clickPropertyButton = (type: string) => {
    switch (type) {
      case 'operations':
        handleShowValveEditor(selectedObject.otype, selectedObject.oname);
        break;
      case 'upstream':
        handleUpstream?.();
        break;
      case 'downstream':
        handleDownstream?.();
        break;
      case 'pumpstatus':
        handlePumpStatus?.();
        break;
      case 'burstPipe':
        handleBurstPipe?.();
        break;
      case 'valveAnalyse':
        handleValveAnalyse?.();
        break;
      default:
        break;
    }
  };

  return (
    <>
      <Card>
        <Grid
          columns={3}
          gap={8}
        >
          <Button
            icon={<AimOutlined />}
            onClick={() => handleClickLocate()}
          >
            地图定位
          </Button>
          {propertyInfo?.buttons.map((element) => (
            <Button
              key={element.type}
              icon={getButtonIcon(element.type).icon}
              title={getButtonIcon(element.type).title}
              onClick={() => clickPropertyButton(element.type)}
            >
              {getButtonIcon(element.type).title ?? ''}
            </Button>
          ))}
        </Grid>
      </Card>
      {getCompareHeader(compare)}
      <PropertyCollapseWrapper
        expandIconPosition="end"
        style={{
          overflow: 'auto',
        }}
        bordered={false}
        activeKey={activeKey}
        onChange={onChange}
      >
        {panel}
      </PropertyCollapseWrapper>
    </>
  );
}
