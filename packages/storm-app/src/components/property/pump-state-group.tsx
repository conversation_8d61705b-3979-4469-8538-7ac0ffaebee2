/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AppConfig } from '@waterdesk/data/app-config';
import { PumpInfo } from '@waterdesk/data/device';
import { formatPumpData, PumpTimeData } from '@waterdesk/data/pump-status';
import { GroupTimeData, TimeData } from '@waterdesk/data/time-data';
import { useEffect, useState } from 'react';
import PumpStatusChart from 'src/components/charts/pump-status-chart';

interface Props {
  appConfig: AppConfig;
  pumpList: Array<PumpInfo>;
  getGroupObjectsTimeValues?: (valueGroup: {
    [key: string]: {
      otype: string;
      oname: string;
      vprop: string;
    };
  }) => Promise<Map<string, GroupTimeData>>;
}

export default function PumpStateGroup(props: Props) {
  const { appConfig, pumpList, getGroupObjectsTimeValues } = props;
  const [pumpTimeData, setPumpTimeData] = useState<PumpTimeData[]>([]);

  useEffect(() => {
    const valueGroup: {
      [key: string]: {
        otype: string;
        oname: string;
        vprop: string;
      };
    } = {};
    pumpList.forEach((pump) => {
      const key = pump.oname;
      if (pump.onOffIndicator) {
        valueGroup[key] = {
          otype: pump.onOffIndicator.otype,
          oname: pump.onOffIndicator.oname,
          vprop: 'SDVAL',
        };
      }
    });

    getGroupObjectsTimeValues?.(valueGroup).then((res) => {
      const items: PumpTimeData[] = [];
      pumpList.forEach((pump) => {
        const key = pump.oname;
        const timeData = res.get(key);
        let data: TimeData[] = timeData ? timeData.timeData : [];
        if (pump.variable && timeData) {
          data = timeData.timeData.map((item) => {
            const valueData = formatPumpData(
              item.value,
              pump.minFrequency,
              pump.maxFrequency,
            );
            return {
              time: item.time,
              value: valueData,
            };
          });
        }
        items.push({
          name: pump.title,
          variable: pump.variable,
          timeData: data,
        });
      });
      setPumpTimeData(items);
    });
  }, [pumpList]);

  return (
    <PumpStatusChart
      pumpTimeData={pumpTimeData}
      pumpStateColor={appConfig.pumpStateColor}
    />
  );
}
