/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Collapse, Row } from 'antd';
import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

export const PropertyCollapseWrapper = styled(Collapse)`
  .collapse-custom-panel {
    border: 0;
    > .ant-collapse-header {
      padding-bottom: 0;
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 8px 8px 0px 8px;
      overflow: hidden;
      border-radius: ${({ theme }) => theme.borderRadius};
      /* background-color: ${({ theme }) => theme.colorBgBase}; */
    }
  }
`;

export const PropertyRowWrapper = styled(Row)`
  /* border-bottom: 1px solid ${({ theme }) => theme.colorBorder}; */
  .row-title {
    ${EllipsisText}
    padding: 5px;
  }

  .row-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    /* border-left: 1px solid ${({ theme }) => theme.colorBorder}; */
    .row-content-wrapper {
      display: flex;
      flex: 1 0 auto;
      height: 100%;
    }
    .row-content-text {
      ${EllipsisText}
      flex: 1;
      padding: 5px 3px;
    }
    .row-content-button-group {
      display: flex;
      flex-wrap: nowrap;
    }
    .compare-value {
      flex: 1;
      border-left: 1px solid ${({ theme }) => theme.colorBorder};
    }
  }

  &:last-child {
    border-bottom: 0;
  }
`;

export const CollapseHeaderWrapper = styled.span`
  font-size: 12px;
`;
