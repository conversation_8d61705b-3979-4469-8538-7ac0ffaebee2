/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { dateSorter } from '@waterdesk/data/utils';
import { WarnDetail, WarnInfoItem, WarnInfoList } from '@waterdesk/data/warn';
import { Button, Dropdown, Table, Tabs, TabsProps } from 'antd';
import { ColumnsType } from 'antd/lib/table/interface';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import IconText from 'src/components/icon-font/icon-text';

interface Props {
  selectedObject: IObjectItem;
  date?: string;
  getWarnData: (
    otype: string,
    oname: string,
  ) => Promise<{
    realtimeWarnList: WarnInfoList;
    assessmentWarnList: WarnInfoList;
  }>;
  displayWarnDeviceChart: (
    indicatorType: string,
    indicatorName: string,
    vprop: string,
    chartStartDate?: string,
    chartEndDate?: string,
  ) => void;
}

export default function WarnPropertyGroup(props: Props) {
  const { selectedObject, getWarnData, displayWarnDeviceChart, date } = props;

  const [tabPanels, setTabPanels] = useState<TabsProps['items']>();

  const formatOperation = (warnDetail: WarnDetail[]) => {
    if (warnDetail.length === 1) {
      return (
        <Button
          style={{ padding: '0', height: '22px' }}
          type="link"
          onClick={() =>
            displayWarnDeviceChart(
              warnDetail[0].otype,
              warnDetail[0].oname,
              warnDetail[0].vprop,
              warnDetail[0].startTime,
              warnDetail[0].endTime,
            )
          }
          title="查看曲线"
          icon={<IconText text={'\ue629'} />}
        />
      );
    }
    if (warnDetail.length > 1) {
      const items = warnDetail.map((item) => ({
        key: item.id,
        label: (
          <Button
            style={{ padding: '0' }}
            type="link"
            onClick={() =>
              displayWarnDeviceChart(
                item.otype,
                item.oname,
                item.vprop,
                item.startTime,
                item.endTime,
              )
            }
          >
            {item.typeName}
          </Button>
        ),
      }));

      return (
        <Dropdown menu={{ items }}>
          <Button type="link">
            <IconText text={'\ue629'} />
          </Button>
        </Dropdown>
      );
    }
    return null;
  };

  const warnInfoListColumns: ColumnsType<WarnInfoItem> = [
    {
      title: '时间',
      dataIndex: 'startTime',
      key: 'startTime',
      ellipsis: true,
      defaultSortOrder: 'descend',
      sorter: (a, b) => dateSorter(a.startTime, b.startTime),
      render: (value, record) => (
        <span title={`${record.secondTypeName}: ${value}`}>
          {dayjs(value).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      render: (_value, record) => formatOperation(record.details),
      width: 60,
    },
  ];

  useEffect(() => {
    if (typeof date === 'undefined') return;
    getWarnData(selectedObject.otype, selectedObject.oname).then((results) => {
      setTabPanels([
        {
          key: 'realtimeWarn',
          label: '实时警告',
          children: (
            <Table
              pagination={false}
              size="small"
              rowKey="id"
              dataSource={results.realtimeWarnList}
              columns={warnInfoListColumns}
            />
          ),
        },
        {
          key: 'assessmentWarn',
          label: '评估警告',
          children: (
            <Table
              pagination={false}
              size="small"
              rowKey="id"
              dataSource={results.assessmentWarnList}
              columns={warnInfoListColumns}
            />
          ),
        },
      ]);
    });
  }, [selectedObject, date]);

  return <Tabs items={tabPanels} />;
}
