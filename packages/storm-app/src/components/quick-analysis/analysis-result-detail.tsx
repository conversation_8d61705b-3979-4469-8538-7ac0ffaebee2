/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CloseOutlined } from '@ant-design/icons';
import {
  Button,
  Empty,
  Flex,
  Modal,
  Popconfirm,
  Space,
  Table,
  TableProps,
} from 'antd';
import { SpanClose } from 'src/styles/common-style';
import { tablePaginationConfig } from '../common/pagination';

interface Props {
  open: boolean;
  title: string;
  columns: TableProps<any>['columns'];
  dataSource: TableProps<any>['dataSource'];
  onClose: () => void;
  onDownload?: () => void;
  pagination?: TableProps['pagination'];
}

const AnalysisResultDetail = (props: Props) => {
  const { open, title, columns, dataSource, onClose, onDownload, pagination } =
    props;
  return (
    <Modal
      closable={false}
      title={
        <div>
          <Flex
            justify="space-between"
            align="center"
          >
            <span>{title}</span>
            <Space>
              {onDownload ? (
                <Popconfirm
                  title="数据导出"
                  description="导出时可能需要等候，请确认是否导出?"
                  onConfirm={onDownload}
                  onCancel={(e) => e?.stopPropagation()}
                >
                  <Button
                    type="primary"
                    size="small"
                  >
                    下载
                  </Button>
                </Popconfirm>
              ) : null}
              <SpanClose onClick={onClose}>
                <CloseOutlined />
              </SpanClose>
            </Space>
          </Flex>
        </div>
      }
      width="90%"
      destroyOnHidden
      onCancel={onClose}
      open={open}
      maskClosable={false}
      centered
      footer={false}
    >
      {columns && columns.length > 0 ? (
        <Table
          size="small"
          columns={columns}
          scroll={{
            y: 'calc(100vh - 300px)',
          }}
          rowKey="id"
          dataSource={dataSource}
          pagination={pagination ?? { ...tablePaginationConfig }}
        />
      ) : (
        <Empty />
      )}
    </Modal>
  );
};

AnalysisResultDetail.displayName = 'AnalysisResultDetail';

export default AnalysisResultDetail;
