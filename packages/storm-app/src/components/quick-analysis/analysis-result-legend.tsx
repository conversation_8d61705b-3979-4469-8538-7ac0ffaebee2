/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LegendGroupData } from '@waterdesk/data/legend-data';
import { Col, Divider, Empty, Row, Switch } from 'antd';
import LegendItem from 'src/components/legend/legend-item';
import { LegendItemTitle } from 'src/components/legend/style';
import { AnalysisResultLegendWrapper } from './style';

interface Props {
  data: LegendGroupData[];
  dimension: boolean;
  handleSwitchDimensionVisible: (checked: boolean) => void;
  showSwitchNode: boolean;
  node: boolean;
  handleSwitchNodeVisible: (checked: boolean) => void;
}

const AnalysisResultLegend = (props: Props) => {
  const {
    data,
    dimension,
    showSwitchNode,
    node,
    handleSwitchNodeVisible,
    handleSwitchDimensionVisible,
  } = props;

  return (
    <AnalysisResultLegendWrapper>
      {showSwitchNode ? (
        <>
          <LegendItemTitle style={{ marginBottom: 0 }}>
            <Row>
              <Col span={12}>显示节点</Col>
              <Col span={12}>
                <Switch
                  checked={node}
                  onChange={(checked) => handleSwitchNodeVisible(checked)}
                />
              </Col>
            </Row>
          </LegendItemTitle>
          <Divider style={{ margin: '5px 0' }} />
        </>
      ) : null}
      <LegendItemTitle style={{ marginBottom: 0 }}>
        <Row>
          <Col span={12}>显示标注</Col>
          <Col span={12}>
            <Switch
              checked={dimension}
              onChange={(checked) => handleSwitchDimensionVisible(checked)}
            />
          </Col>
        </Row>
      </LegendItemTitle>
      <Divider style={{ margin: '5px 0' }} />
      <LegendItemTitle style={{ marginBottom: 0 }}>
        <Row>
          <Col span={12}>图例</Col>
        </Row>
      </LegendItemTitle>
      {data.length ? (
        data.map((legendDataItem) => (
          <Row key={legendDataItem.key}>
            <LegendItem groupData={legendDataItem} />
          </Row>
        ))
      ) : (
        <Empty />
      )}
    </AnalysisResultLegendWrapper>
  );
};

export default AnalysisResultLegend;
