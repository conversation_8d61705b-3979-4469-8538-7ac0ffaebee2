/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ExportOutlined, SendOutlined, TableOutlined } from '@ant-design/icons';
import Database from '@waterdesk/data/database';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import {
  AnalysisConfig,
  AnalysisObject,
  AnalysisResultData,
  AnalysisUiConfig,
  AnalysisUiConfigType,
  AnalysisUiDataItem,
  exportAnalysisData,
  exportMultipleAnalysisData,
  getAnalysisUiConfig,
  getCollapseItemKeysByOrder,
  getEnergyHeaderTitle,
  getPanelHeaderCountText,
  getPanelHeaderTitle,
  getPlantStationHeaderTitle,
  getPlantsStationsDataByType,
  getTableDataSource,
  PlantStationInfo,
  PlantStationType,
  QueryParams,
  SendWaterMeter,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Importance, MsgFrom } from '@waterdesk/data/sms';
import { getUnitFormat } from '@waterdesk/data/unit-system';
import { ExcelSheetData } from '@waterdesk/data/utils';
import {
  Button,
  Collapse,
  CollapseProps,
  Empty,
  Popconfirm,
  Select,
  Space,
  Table,
  TableColumnsType,
} from 'antd';
import { ColumnsType, TableProps } from 'antd/es/table';
import { Key, ReactNode, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import IconText from '../icon-font/icon-text';
import AnalysisResultDetail from './analysis-result-detail';
import PlantStationTable from './plant-station-table';
import SendSMSDrawer from './send-sms-drawer';

type CollapseActiveKey = string | number | (string | number)[];
type PanelKey = AnalysisUiConfigType;

export interface AnalysisResultProps {
  db: Database;
  analysisConfig: AnalysisConfig | undefined;
  analysisData: AnalysisResultData;
  activeThemes: {
    [key in AnalysisUiConfigType]?: string;
  };
  analysisThemesConfig: {
    [key in AnalysisUiConfigType]?: LegendGroupData[];
  };
  tableSelectedKeys: {
    [key in AnalysisUiConfigType]?: React.Key[];
  };
  setTableSelectedKeys: (keys: Key[], tableKey: AnalysisUiConfigType) => void;
  handleActiveOnChange: (panelKey: string, value?: string) => void;
  handleLocate: (otype: string, oname: string, shape: string) => void;
  handleHover: (hoverObject: AnalysisUiDataItem | undefined) => void;
  asyncExportData?: (
    key: keyof Pick<
      AnalysisUiConfig,
      'affectedAreaUser' | 'waterOutageAreaUser'
    >,
    title: string,
  ) => void;
  getWaterMeterList?: (
    params: QueryParams['params'],
  ) => Promise<{ [index: string]: WaterMeterInfo[] }>;
  onSendSMS?: (
    msgFrom: MsgFrom,
    importance: Importance,
    msgText: string,
    dataSource: WaterMeterInfo[],
  ) => Promise<void>;
  showPlantStation?: boolean;
  plantStationColumn?: TableProps<PlantStationInfo>['columns'];
  handleToggleValveStatus?: (
    otype: string,
    oname: string,
    shape: string,
  ) => void;
  openValveList?: AnalysisObject[];
}

export function getColumnsItem(
  otype: string,
  db: Database,
  vprops?: string[],
  handleLocate?: (otype: string, oname: string, shape: string) => void,
  handleHover?: (hoverObject: AnalysisUiDataItem | undefined) => void,
): ColumnsType<any> {
  const otypeProperty = db.getPropertyInfo(otype);
  if (!otypeProperty || !vprops) {
    return [];
  }
  return vprops.map((vprop) => {
    let title = otypeProperty.getPropertyTitle(vprop);
    if (!title)
      title =
        otypeProperty.getPropertyTitle(vprop.toLocaleUpperCase()) ?? vprop;
    let unitKey = otypeProperty.getPropertyUnit(vprop);
    if (!unitKey)
      unitKey = otypeProperty.getPropertyUnit(vprop.toLocaleUpperCase());
    const unit = unitKey ? getUnitFormat(unitKey) : undefined;
    const unitSymbol = unit?.unitSymbol ? `(${unit.unitSymbol})` : '';
    return {
      key: vprop,
      dataIndex: vprop,
      title: `${title}${unitSymbol}`,
      width: 100,
      ellipsis: true,
      render: (value, record) => {
        if (
          (vprop === 'ROAD_NAME' || vprop === 'TITLE' || vprop === 'NAME') &&
          record.shape
        ) {
          return (
            <Button
              type="link"
              style={{ margin: 0, height: '22px', paddingLeft: '0' }}
              onClick={() =>
                handleLocate?.(record.otype, record.oname, record.shape)
              }
              onMouseOver={() => handleHover?.(record)}
              onMouseOut={() => handleHover?.(undefined)}
            >
              {value || '-'}
            </Button>
          );
        }
        return value || '-';
      },
    };
  });
}

const AnalysisResult = (props: AnalysisResultProps) => {
  const {
    analysisConfig,
    analysisData,
    activeThemes,
    analysisThemesConfig,
    tableSelectedKeys,
    db,
    onSendSMS,
    getWaterMeterList,
    setTableSelectedKeys,
    handleActiveOnChange,
    handleLocate,
    handleHover,
    asyncExportData,
    showPlantStation,
    plantStationColumn,
    handleToggleValveStatus,
    openValveList,
  } = props;

  const [activeKeys, setActiveKeys] = useState<CollapseActiveKey>([]);
  const [openKey, setOpenKey] = useState<PanelKey | undefined>(undefined);
  const [plantStationKey, setPlantStationKey] = useState<
    PlantStationType | undefined
  >(undefined);
  const [openEnergy, setOpenEnergy] = useState<boolean>(false);
  const [openSendSMS, setOpenSendSMS] = useState<PanelKey | false>(false);
  const [sendDataSource, setSendDataSource] = useState<SendWaterMeter[]>([]);

  if (typeof analysisConfig === 'undefined')
    return <Empty description="获取分析结果配置失败" />;

  const analysisUiConfig = getAnalysisUiConfig(analysisConfig);
  const {
    waterOutageArea: waterOutageAreaData,
    affectedArea: affectedAreaData,
  } = analysisData;

  const openValveListKeys = openValveList?.map((m) => m.oname);

  const mergePlantsStationColumns: TableColumnsType<PlantStationInfo> =
    plantStationColumn ?? [
      {
        title: '',
        key: 'ptitle',
        dataIndex: 'ptitle',
        width: 30,
        onCell: (record) => ({
          rowSpan: record.rowSpan,
        }),
      },
      {
        title: '名称',
        key: 'title',
        dataIndex: 'title',
        width: 80,
      },
      {
        title: '方案前',
        key: 'oldValue',
        dataIndex: 'oldValue',
        width: 60,
      },
      {
        title: '方案后',
        key: 'newValue',
        dataIndex: 'newValue',
        width: 60,
      },
      {
        title: '变化',
        key: 'diffValue',
        dataIndex: 'diffValue',
        width: 50,
      },
    ];

  const getSmallTableColumns = (
    panelKey: PanelKey | undefined,
  ): TableProps<any>['columns'] => {
    if (typeof panelKey === 'undefined') return [];
    const config = analysisUiConfig[panelKey];
    if (config) {
      const columns = getColumnsItem(
        config.otype,
        db,
        config.category,
        handleLocate,
        handleHover,
      );
      switch (panelKey) {
        case 'valveList':
          return [
            {
              title: '定位',
              width: 50,
              dataIndex: 'fixedLocate',
              align: 'center',
              fixed: true,
              render: (_, record) => (
                <Button
                  title="查看"
                  type="link"
                  style={{ margin: 0, padding: 0, height: '22px' }}
                  onClick={() =>
                    handleLocate(record.otype, record.oname, record.shape)
                  }
                >
                  <IconText text="&#xe69d;" />
                </Button>
              ),
            },
            ...columns,
            {
              title: '无法关闭',
              width: 80,
              dataIndex: 'operate',
              align: 'center',
              fixed: true,
              hidden: !handleToggleValveStatus,
              render: (_, record) => {
                const status = openValveListKeys?.includes(record.oname);
                return (
                  <Button
                    title={status ? '取消设置' : '设置为无法关闭'}
                    type="link"
                    style={{ margin: 0, padding: 0, height: '22px' }}
                    onClick={() =>
                      handleToggleValveStatus?.(
                        record.otype,
                        record.oname,
                        record.shape,
                      )
                    }
                  >
                    {status ? '取消' : '设置'}
                  </Button>
                );
              },
            },
          ];
        case 'affectedArea':
        case 'affectedAreaUser':
        case 'affectedPipeline':
        case 'closedPipeline':
        case 'reversePipeline':
        case 'waterOutageArea':
        case 'waterOutageAreaUser':
        case 'waterReverseAffectedArea':
        case 'waterReverseAffectedAreaUser':
        default:
          return columns;
      }
    }
    return [];
  };

  const getBigTableColumns: <T>(
    panelKey: PanelKey | undefined,
  ) => TableProps<T>['columns'] = (panelKey) => {
    if (typeof panelKey === 'undefined') return [];
    const config = analysisUiConfig[panelKey];
    if (!config) return [];
    return getColumnsItem(config.otype, db, config.table, handleLocate);
  };

  const getTableRowSelection = (
    tableKey: AnalysisUiConfigType,
  ): TableProps<any>['rowSelection'] => ({
    columnWidth: 40,
    renderCell: (_, __, ___, originNode) => (
      <div style={{ textAlign: 'center' }}>{originNode}</div>
    ),
    onChange: (e: React.Key[]) => setTableSelectedKeys(e, tableKey),
    selectedRowKeys: tableSelectedKeys[tableKey],
  });

  const getTable = (panelKey: PanelKey) => (
    <Table
      rowSelection={getTableRowSelection(panelKey)}
      scroll={{
        y: 'calc(35vh - 100px)',
      }}
      virtual
      size="small"
      pagination={false}
      rowKey="oname"
      columns={getSmallTableColumns(panelKey)}
      dataSource={getTableDataSource(panelKey, analysisData)}
    />
  );

  const getDataByPressChange = (
    pressChange: 'UP' | 'DOWN',
    data: AnalysisUiDataItem[],
  ): AnalysisUiDataItem[] => {
    if (pressChange === 'UP') {
      return data.filter((f) => {
        if ('press_change' in f && typeof f.press_change === 'number')
          return f.press_change > 0;
        return true;
      });
    }
    return data.filter((f) => {
      if ('press_change' in f && typeof f.press_change === 'number')
        return f.press_change < 0;
      return true;
    });
  };

  const getSendDataSource = async (
    panelKey: PanelKey,
    pressChange?: 'UP' | 'DOWN',
  ): Promise<SendWaterMeter[]> => {
    const params: QueryParams['params'] = {};
    let key1: PanelKey = 'affectedAreaUser';
    let key2: PanelKey = 'affectedArea';
    if (panelKey === 'affectedArea' || panelKey === 'affectedAreaUser') {
      let filterData = [...affectedAreaData];
      if (pressChange) {
        filterData = getDataByPressChange(pressChange, filterData);
      }
      params.affectedArea = filterData.map((item) => item.oname).join(',');
    }
    if (panelKey === 'waterOutageArea' || panelKey === 'waterOutageAreaUser') {
      let filterData = [...waterOutageAreaData];
      if (pressChange) {
        filterData = getDataByPressChange(pressChange, filterData);
      }
      params.waterOutageArea = filterData.map((item) => item.oname).join(',');
      key1 = 'waterOutageAreaUser';
      key2 = 'waterOutageArea';
    }

    const data = await getWaterMeterList?.(params);

    const key1Data = pressChange
      ? getDataByPressChange(
          pressChange,
          getTableDataSource(key1, analysisData),
        )
      : getTableDataSource(key1, analysisData);
    const dataSource: SendWaterMeter[] = [
      {
        key: key1,
        label: getPanelHeaderTitle(key1, analysisConfig),
        data: key1Data.map((item) => ({
          key: uuidv4(),
          userName: (item.NAME ?? '') as string,
          address: (item.ADDRESS ?? '') as string,
          waterMeterCode: item.oname,
          phone: (item.PAY_PHONE ??
            item.OWNER_MOBILE_PHONE ??
            item.OWNER_CONTACT_PHONE ??
            item.SMS_PHONE ??
            '') as string,
          pressChange: item.press_change as number,
        })),
      },
      {
        key: key2,
        label: getPanelHeaderTitle(key2, analysisConfig),
        data: data?.[key2] ?? [],
      },
      {
        key: 'custom',
        label: '自定义用户',
        data: [],
      },
    ];
    setSendDataSource(dataSource);
    return dataSource;
  };

  const handleExportAreaData = async (
    panelKey: 'affectedArea' | 'waterOutageArea',
    type: 'smallTable' | 'bigTable' = 'smallTable',
  ) => {
    const data = await getSendDataSource(panelKey);
    const waterMeterData = data.find((f) => f.key === panelKey)?.data ?? [];
    const waterMeterColumns = [
      {
        dataIndex: 'waterMeterCode',
        title: '水表',
      },
      {
        dataIndex: 'userName',
        title: '用户',
      },
      {
        dataIndex: 'phone',
        title: '手机号',
      },
      {
        dataIndex: 'address',
        title: '地址',
      },
    ];

    exportMultipleAnalysisData(
      [
        {
          data: getTableDataSource(panelKey, analysisData),
          columns: (type === 'smallTable'
            ? getSmallTableColumns(panelKey)
            : getBigTableColumns(panelKey)) as {
            dataIndex: string;
            title: string;
          }[],
          fileName: getPanelHeaderTitle(panelKey, analysisConfig),
        },
        {
          data: waterMeterData as unknown as {
            [index: string]: string | number | null | undefined;
          }[],
          columns: waterMeterColumns,
          fileName: '用户',
        },
      ],
      getPanelHeaderTitle(panelKey, analysisConfig),
    );
  };

  const handleExportData = async (
    panelKey: PanelKey,
    type: 'smallTable' | 'bigTable' = 'smallTable',
  ) => {
    switch (panelKey) {
      case 'waterOutageAreaUser':
      case 'affectedAreaUser':
        // 如果是大表格数据的导出， 直接从前端导出
        if (type === 'bigTable') {
          exportAnalysisData(
            getTableDataSource(panelKey, analysisData),
            getBigTableColumns(panelKey) as {
              dataIndex: string;
              title: string;
            }[],
            getPanelHeaderTitle(panelKey, analysisConfig),
          );
        } else {
          asyncExportData?.(
            panelKey,
            getPanelHeaderTitle(panelKey, analysisConfig),
          );
        }
        break;
      case 'affectedArea':
      case 'waterOutageArea':
        handleExportAreaData(panelKey, type);
        break;
      case 'affectedPipeline':
      case 'closedPipeline':
      case 'reversePipeline':
      case 'valveList':
      case 'waterReverseAffectedArea':
      case 'waterReverseAffectedAreaUser':
        exportAnalysisData(
          getTableDataSource(panelKey, analysisData),
          (type === 'smallTable'
            ? getSmallTableColumns(panelKey)
            : getBigTableColumns(panelKey)) as {
            dataIndex: string;
            title: string;
          }[],
          getPanelHeaderTitle(panelKey, analysisConfig),
        );
        break;
      default:
        break;
    }
  };

  const getPlantsStationsData = (
    type: PlantStationType | undefined,
  ): PlantStationInfo[] =>
    getPlantsStationsDataByType(type, analysisData.plantStation);

  const getEnergyConsumptionData = (): PlantStationInfo[] =>
    analysisData.energyConsumption;

  const handleExportPlantsStations = (type: PlantStationType) => {
    const dataSource = getPlantsStationsData(
      type,
    ) as unknown as ExcelSheetData['dataSource'];
    const columns = mergePlantsStationColumns.map((m) => ({
      title: m.title as string,
      dataIndex: m.key as string,
    }));
    exportAnalysisData(dataSource, columns, getPlantStationHeaderTitle(type));
  };

  const handleExportEnergy = () => {
    const dataSource =
      getEnergyConsumptionData() as unknown as ExcelSheetData['dataSource'];
    const columns = mergePlantsStationColumns.map((m) => ({
      title: m.title as string,
      dataIndex: m.key as string,
    }));
    exportAnalysisData(dataSource, columns, getEnergyHeaderTitle());
  };

  const handleOnSendSMS = (panelKey: PanelKey) => {
    setOpenSendSMS(panelKey);
    getSendDataSource(panelKey);
  };

  const getSendSmsButton = (panelKey: PanelKey): ReactNode => {
    const list = [
      'affectedArea',
      'affectedAreaUser',
      'waterOutageArea',
      'waterOutageAreaUser',
    ];
    if (!list.includes(panelKey)) return null;
    return (
      <SendOutlined
        title="发送通知"
        onClick={(e) => {
          e?.stopPropagation();
          handleOnSendSMS(panelKey);
        }}
      />
    );
  };

  const getCollapseItems = (): CollapseProps['items'] => {
    if (typeof analysisUiConfig === 'undefined') return undefined;
    const sortCollapsePanelKeys = getCollapseItemKeysByOrder(analysisUiConfig);
    return sortCollapsePanelKeys.map((panelKey) => {
      const analysisThemeConfig = analysisThemesConfig[panelKey];

      const headerText = `${getPanelHeaderTitle(
        panelKey,
        analysisConfig,
      )}${getPanelHeaderCountText(panelKey, analysisData)}`;

      return {
        label: headerText,
        key: panelKey,
        extra: (
          <Space>
            {analysisThemeConfig?.length ? (
              <Select
                size="small"
                value={activeThemes[panelKey]}
                options={analysisThemeConfig}
                fieldNames={{ label: 'title', value: 'name' }}
                style={{ width: '120px' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onChange={(value) => handleActiveOnChange(panelKey, value)}
              />
            ) : null}
            {getSendSmsButton(panelKey)}
            <Popconfirm
              title="数据导出"
              description="导出时可能需要等候，请确认是否导出?"
              onConfirm={(e) => {
                e?.stopPropagation();
                handleExportData(panelKey);
              }}
              onCancel={(e) => e?.stopPropagation()}
            >
              <ExportOutlined
                title="数据导出"
                onClick={(e) => e.stopPropagation()}
              />
            </Popconfirm>
            {analysisUiConfig[panelKey]?.table ? (
              <TableOutlined
                title="数据表格"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpenKey(panelKey);
                }}
              />
            ) : null}
          </Space>
        ),
        children: getTable(panelKey),
      };
    });
  };

  const getPlantAndPumpStationItems = (): CollapseProps['items'] =>
    showPlantStation
      ? [
          {
            label: getPlantStationHeaderTitle('plants'),
            key: 'plants',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getPlantsStationsData('plants')}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
            extra: (
              <Space>
                <Popconfirm
                  title="数据导出"
                  description="导出时可能需要等候，请确认是否导出?"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    handleExportPlantsStations('plants');
                  }}
                  onCancel={(e) => e?.stopPropagation()}
                >
                  <ExportOutlined
                    title="数据导出"
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
                <TableOutlined
                  title="数据表格"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPlantStationKey('plants');
                  }}
                />
              </Space>
            ),
          },
          {
            label: getPlantStationHeaderTitle('pumpStations'),
            key: 'pumpStation',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getPlantsStationsData('pumpStations')}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
            extra: (
              <Space>
                <Popconfirm
                  title="数据导出"
                  description="导出时可能需要等候，请确认是否导出?"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    handleExportPlantsStations('pumpStations');
                  }}
                  onCancel={(e) => e?.stopPropagation()}
                >
                  <ExportOutlined
                    title="数据导出"
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
                <TableOutlined
                  title="数据表格"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPlantStationKey('pumpStations');
                  }}
                />
              </Space>
            ),
          },
        ]
      : undefined;

  const getEnergyConsumptionItems = (): CollapseProps['items'] =>
    analysisConfig.showEnergy
      ? [
          {
            label: getEnergyHeaderTitle(),
            key: 'energyConsumption',
            children: (
              <PlantStationTable
                columns={mergePlantsStationColumns}
                dataSource={getEnergyConsumptionData()}
                tableProps={{
                  scroll: {
                    y: 'calc(35vh - 100px)',
                  },
                }}
              />
            ),
            extra: (
              <Space>
                <Popconfirm
                  title="数据导出"
                  description="导出时可能需要等候，请确认是否导出?"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    handleExportEnergy();
                  }}
                  onCancel={(e) => e?.stopPropagation()}
                >
                  <ExportOutlined
                    title="数据导出"
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
                <TableOutlined
                  title="数据表格"
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenEnergy(true);
                  }}
                />
              </Space>
            ),
          },
        ]
      : undefined;
  return (
    <>
      <Collapse
        activeKey={activeKeys}
        bordered={false}
        onChange={(activeKeys: CollapseActiveKey) => setActiveKeys(activeKeys)}
        items={[
          ...(getCollapseItems() ?? []),
          ...(getPlantAndPumpStationItems() ?? []),
          ...(getEnergyConsumptionItems() ?? []),
        ]}
      />
      <AnalysisResultDetail
        open={typeof openKey === 'string'}
        title={
          typeof openKey === 'undefined'
            ? ''
            : getPanelHeaderTitle(openKey, analysisConfig)
        }
        onClose={() => setOpenKey(undefined)}
        columns={getBigTableColumns<any>(openKey)}
        dataSource={getTableDataSource(openKey, analysisData)}
        onDownload={() =>
          handleExportData(openKey as AnalysisUiConfigType, 'bigTable')
        }
      />
      <AnalysisResultDetail
        open={typeof plantStationKey === 'string'}
        title={
          typeof plantStationKey === 'undefined'
            ? ''
            : getPlantStationHeaderTitle(plantStationKey)
        }
        onClose={() => setPlantStationKey(undefined)}
        columns={mergePlantsStationColumns.map((m) => ({
          ...m,
          width: 'auto',
        }))}
        dataSource={getPlantsStationsData(plantStationKey)}
        onDownload={() =>
          handleExportPlantsStations(plantStationKey as PlantStationType)
        }
        pagination={false}
      />
      <AnalysisResultDetail
        open={openEnergy}
        title={getEnergyHeaderTitle()}
        onClose={() => setOpenEnergy(false)}
        columns={mergePlantsStationColumns.map((m) => ({
          ...m,
          width: 'auto',
        }))}
        dataSource={getEnergyConsumptionData()}
        onDownload={() => handleExportEnergy()}
        pagination={false}
      />
      <SendSMSDrawer
        open={openSendSMS !== false}
        onClose={() => setOpenSendSMS(false)}
        dataSource={sendDataSource}
        updateDataSource={setSendDataSource}
        onSendSMS={onSendSMS}
        onFilter={(pressChange) =>
          getSendDataSource(openSendSMS as PanelKey, pressChange)
        }
      />
    </>
  );
};

AnalysisResult.displayName = 'AnalysisResult';

export default AnalysisResult;
