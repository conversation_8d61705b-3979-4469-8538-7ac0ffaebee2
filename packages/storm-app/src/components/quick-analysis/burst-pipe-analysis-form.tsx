/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EnvironmentOutlined } from '@ant-design/icons';
import { getFlowVelocity } from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Button, Form, FormInstance, Input, InputNumber, Space } from 'antd';
import { useEffect } from 'react';

export interface BurstPipeAnalysisFormValues {
  flow: number;
  flowVelocity: number;
}

interface Props {
  form: FormInstance<BurstPipeAnalysisFormValues>;
  originValues?: {
    otypeTitle: string;
    diameter: number;
    oname: string;
    otype: string;
    shape?: string;
  };
  onFinish: (values: BurstPipeAnalysisFormValues) => void;
  onSchedule?: () => void;
  onLocate: (otype: string, oname: string, shape?: string) => void;
  onReset?: () => void;
}

const BurstPipeAnalysisForm = (props: Props) => {
  const { form, originValues, onFinish, onLocate, onSchedule, onReset } = props;
  const {
    diameter = 0,
    otypeTitle = '',
    otype = '',
    oname = '',
    shape = '',
  } = originValues ?? {};

  const flow = Form.useWatch('flow', form);

  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  useEffect(() => {
    // set flowVelocity when flow or diameter  is changed
    const flowVelocity = getFlowVelocity(flow, diameter);
    form.setFieldValue('flowVelocity', flowVelocity);
  }, [flow, diameter]);

  return (
    <Form
      onFinish={onFinish}
      form={form}
      className="selectObjectFrom"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
    >
      <Form.Item label="对象ID">
        <Space.Compact style={{ width: '100%' }}>
          <Input
            readOnly
            prefix={otypeTitle ?? <span />}
            value={oname}
          />
          <Button onClick={() => onLocate(otype, oname, shape)}>
            <EnvironmentOutlined />
          </Button>
        </Space.Compact>
      </Form.Item>
      <Form.Item
        label="冲洗流量"
        name="flow"
      >
        <InputNumber
          addonAfter="m³/h"
          min={0}
        />
      </Form.Item>
      <Form.Item
        label="冲洗流速"
        name="flowVelocity"
      >
        <InputNumber
          addonAfter="m/s"
          readOnly
        />
      </Form.Item>
      <Form.Item wrapperCol={{ span: 24 }}>
        <Space
          style={{
            width: '100%',
            justifyContent: 'center',
          }}
        >
          {onSchedule ? <Button onClick={onSchedule}>转到调度</Button> : null}
          <Button
            type="primary"
            htmlType="submit"
          >
            分析
          </Button>
          <Button
            htmlType="button"
            onClick={handleReset}
          >
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default BurstPipeAnalysisForm;
