/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InboxOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { Modal, Upload, UploadProps } from 'antd';
import { useEffect, useState } from 'react';
import { read, utils } from 'xlsx';

const { Dragger } = Upload;

const ImportUser = (props: {
  open: boolean;
  onCreate: (data: Array<string>) => void;
  onCancel: () => void;
}) => {
  const { open, onCreate, onCancel } = props;
  const { token } = useToken();
  const [importInfo, setImportInfo] = useState<{
    status: 'success' | 'fail' | undefined;
    data: Array<string>;
  }>({
    status: undefined,
    data: [],
  });

  const uploadProps: UploadProps = {
    accept: '.xlsx, .xls',
    maxCount: 1,
    showUploadList: true,
    beforeUpload: (file) => {
      try {
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            const data = e.target?.result;
            const workbook = read(data, { type: 'binary' });

            // 获取第一个工作表
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];

            // 将工作表转换为JSON对象
            const jsonData: Array<Array<string>> = utils.sheet_to_json(sheet, {
              header: 1,
            });
            const importNumber = jsonData
              .map((row) => row[0])
              .filter((number) => /^\d+$/.test(String(number)));
            setImportInfo({
              status: 'success',
              data: importNumber,
            });
          };
          reader.readAsBinaryString(file);
        }
      } catch {
        setImportInfo({
          status: 'fail',
          data: [],
        });
      }

      return false;
    },
  };

  useEffect(() => {
    if (open)
      setImportInfo({
        status: undefined,
        data: [],
      });
  }, [open]);

  return (
    <Modal
      open={open}
      title="导入用户"
      okText="确定"
      cancelText="取消"
      onCancel={onCancel}
      onOk={() => onCreate(importInfo.data)}
      destroyOnHidden
    >
      <Dragger {...uploadProps}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或者拖拽文件到区域进行上传</p>
        <p className="ant-upload-hint">仅支持上传excel(.xlsx、.xls)文件</p>
      </Dragger>
      <div
        style={{
          textAlign: 'right',
          color: token.colorTextSecondary,
        }}
      >
        {importInfo.status === 'success' ? (
          <h4>
            解析成功：<span>共{importInfo.data.length}条数据</span>
          </h4>
        ) : null}
        {importInfo.status === 'fail' ? (
          <h4>解析失败，请重新选择文件</h4>
        ) : null}
      </div>
    </Modal>
  );
};

export default ImportUser;
