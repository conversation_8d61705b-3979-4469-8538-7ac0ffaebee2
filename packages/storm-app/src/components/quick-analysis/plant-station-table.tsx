/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PlantStationInfo } from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Table, TableColumnsType } from 'antd';
import { TableProps } from 'antd/lib';

interface Props {
  columns: TableColumnsType<PlantStationInfo>;
  dataSource: PlantStationInfo[];
  tableProps?: TableProps<PlantStationInfo>;
}

const PlantStationTable = (props: Props) => {
  const { dataSource, columns, tableProps } = props;

  return (
    <Table<PlantStationInfo>
      {...tableProps}
      size="small"
      rowKey="id"
      columns={columns}
      dataSource={dataSource}
      pagination={false}
    />
  );
};

PlantStationTable.displayName = 'PlantStationTable';

export default PlantStationTable;
