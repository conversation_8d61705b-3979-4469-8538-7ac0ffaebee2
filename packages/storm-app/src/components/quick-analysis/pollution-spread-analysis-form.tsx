/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EnvironmentOutlined } from '@ant-design/icons';
import { IObjectItem } from '@waterdesk/data/object-item';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  Row,
  Space,
  TimePicker,
  Typography,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect } from 'react';

export interface PollutionSpreadAnalysisFormValues {
  targetId: string;
  pollutionStartTime: Dayjs;
  pollutionEndTime: Dayjs;
  simulationEndTime: Dayjs;
}

interface Props {
  canEdit?: boolean;
  targetObject?: IObjectItem;
  handleLocate?: (otype: string, oname: string, shape: string) => void;
  form?: FormInstance<PollutionSpreadAnalysisFormValues>;
  date: string;
}

const { Text } = Typography;
const format = 'HH:mm';

const PollutionSpreadAnalysisForm = (props: Props) => {
  const {
    canEdit,
    targetObject,
    handleLocate,
    form: externalForm,
    date,
  } = props;
  const [internalForm] = Form.useForm<PollutionSpreadAnalysisFormValues>();
  const form = externalForm || internalForm;

  useEffect(() => {
    const dateBase = dayjs(date);
    const now = dayjs();

    const combinedTime = dateBase
      .hour(now.hour())
      .minute(now.minute())
      .second(0)
      .millisecond(0);

    // 将分钟向下取整到最近的5分钟倍数
    const roundedMinutes = Math.floor(combinedTime.minute() / 5) * 5;
    const roundedNow = combinedTime.minute(roundedMinutes);

    form.setFieldsValue({
      pollutionStartTime: roundedNow,
      pollutionEndTime: roundedNow.clone().add(30, 'minutes'),
      simulationEndTime: roundedNow.clone().add(4, 'hours'),
    });
  }, [form, date]);

  return (
    <Form
      form={form}
      layout="vertical"
      style={{ padding: '16px' }}
    >
      <Form.Item
        label="对象ID"
        name="targetId"
        extra={
          targetObject ? null : (
            <Text type="secondary">请在地图上选择节点对象</Text>
          )
        }
      >
        <Space.Compact style={{ width: '100%' }}>
          <Input
            readOnly
            value={targetObject?.oname}
          />
          {handleLocate && (
            <Button
              onClick={() =>
                handleLocate(
                  targetObject?.otype || '',
                  targetObject?.oname || '',
                  targetObject?.shape || '',
                )
              }
              disabled={!canEdit}
              style={{ cursor: canEdit ? 'pointer' : 'default' }}
            >
              <EnvironmentOutlined />
            </Button>
          )}
        </Space.Compact>
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="污染开始时间"
            name="pollutionStartTime"
            rules={[
              {
                validator: (_, value) => {
                  const endTime = form.getFieldValue('pollutionEndTime');
                  if (value && endTime && value.isAfter(endTime)) {
                    return Promise.reject(
                      new Error('污染开始时间必须早于结束时间'),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <TimePicker
              format={format}
              style={{ width: '100%' }}
              minuteStep={5}
              showNow={false}
              onChange={() => {
                form.validateFields(['pollutionStartTime', 'pollutionEndTime']);
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="污染结束时间"
            name="pollutionEndTime"
            rules={[
              {
                validator: (_, value) => {
                  const startTime = form.getFieldValue('pollutionStartTime');
                  if (value && startTime && value.isBefore(startTime)) {
                    return Promise.reject(
                      new Error('污染结束时间必须晚于开始时间'),
                    );
                  }

                  // 检查模拟结束时间是否晚于污染结束时间
                  const simulationEndTime =
                    form.getFieldValue('simulationEndTime');
                  if (
                    value &&
                    simulationEndTime &&
                    simulationEndTime.isBefore(value)
                  ) {
                    return Promise.reject(
                      new Error('污染结束时间不能晚于模拟结束时间'),
                    );
                  }

                  return Promise.resolve();
                },
                validateTrigger: ['onChange', 'onBlur'],
              },
            ]}
          >
            <TimePicker
              format={format}
              style={{ width: '100%' }}
              minuteStep={5}
              showNow={false}
              onChange={() => {
                form.validateFields(['pollutionStartTime', 'pollutionEndTime']);
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="模拟结束时间"
        name="simulationEndTime"
        rules={[
          {
            validator: (_, value) => {
              const endTime = form.getFieldValue('pollutionEndTime');
              if (value && endTime && value.isBefore(endTime)) {
                return Promise.reject(
                  new Error('模拟结束时间必须晚于污染结束时间'),
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <TimePicker
          format={format}
          style={{ width: '100%' }}
          minuteStep={5}
          showNow={false}
        />
      </Form.Item>
    </Form>
  );
};

PollutionSpreadAnalysisForm.displayName = 'PollutionSpreadAnalysisForm';

export default PollutionSpreadAnalysisForm;
