/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { convertReviewStatusToSelectOptions } from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Button, Drawer, Form, Input, message, Select, Space } from 'antd';
import { useEffect, useState } from 'react';

interface SaveResultFormValues {
  oldName: string;
  name: string;
  review: number;
  note?: string;
}

interface Props {
  open: boolean;
  submit: (
    name: string,
    review: number,
    note?: string,
  ) => Promise<{ status: 'Success' | 'Fail' }>;
  onSubmitSuccess?: () => void;
  onClose: () => void;
  initialValues?: Partial<SaveResultFormValues>;
}

const options = convertReviewStatusToSelectOptions();

const SaveResultDrawer = (props: Props) => {
  const { open, initialValues, onSubmitSuccess, submit, onClose } = props;
  const [form] = Form.useForm<SaveResultFormValues>();

  const [loading, setLoading] = useState<boolean>(false);

  const [messageApi, contextHolder] = message.useMessage();

  const handleOnFinish = async (values: SaveResultFormValues) => {
    setLoading(true);
    const res = await submit(values.name, values.review, values.note);
    setLoading(false);
    if (res.status === 'Success') {
      messageApi.success('保存成功!');
      onSubmitSuccess?.();
      onClose();
    } else {
      messageApi.error('保存失败!');
    }
  };

  const status = initialValues ? 'edit' : 'create';

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues]);

  return (
    <>
      <Drawer
        title={status === 'edit' ? '方案编辑' : '方案保存'}
        open={open}
        onClose={onClose}
        placement="right"
        width={400}
        destroyOnHidden
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button
              onClick={() => form.submit()}
              type="primary"
              loading={loading}
            >
              提交
            </Button>
          </Space>
        }
      >
        <Form
          name="save-analysis-result-form"
          layout="vertical"
          form={form}
          onFinish={handleOnFinish}
        >
          {status === 'edit' ? (
            <Form.Item
              label="原方案名称"
              name="oldName"
            >
              <Input
                placeholder="请输入"
                disabled
              />
            </Form.Item>
          ) : null}
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入名称!' }]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item
            label="审核状态"
            name="review"
            rules={[{ required: true, message: '请选择审核状态!' }]}
          >
            <Select
              placeholder="请选择"
              options={options}
            />
          </Form.Item>
          <Form.Item
            label="备注"
            name="note"
          >
            <Input.TextArea
              rows={4}
              maxLength={2000}
              showCount
              placeholder="请输入"
            />
          </Form.Item>
        </Form>
      </Drawer>
      {contextHolder}
    </>
  );
};

SaveResultDrawer.displayName = 'SaveResultDrawer';

export default SaveResultDrawer;
