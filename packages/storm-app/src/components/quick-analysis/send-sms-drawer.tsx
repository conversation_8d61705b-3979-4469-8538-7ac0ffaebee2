/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ExclamationCircleFilled } from '@ant-design/icons';
import {
  SendWaterMeter,
  WaterMeterInfo,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import {
  Importance,
  importanceOptions,
  MsgFrom,
  NO_WATER_MSG_TEMPLATE,
} from '@waterdesk/data/sms';
import {
  Button,
  Col,
  Drawer,
  Form,
  Input,
  Modal,
  message,
  Row,
  Select,
  Space,
} from 'antd';
import lodash from 'lodash';
import SendSMSTableList from './send-sms-table-list';

interface Props {
  open: boolean;
  dataSource: SendWaterMeter[];
  onClose: () => void;
  updateDataSource: React.Dispatch<React.SetStateAction<SendWaterMeter[]>>;
  onSendSMS?: (
    msgFrom: MsgFrom,
    importance: Importance,
    msgText: string,
    dataSource: WaterMeterInfo[],
  ) => Promise<void>;
  onFilter: (pressChange?: 'UP' | 'DOWN') => void;
}

type FormValues = {
  importance: Importance;
  msgText: string;
};

export default function SendSMSDrawer(props: Props) {
  const { open, dataSource, onClose, onSendSMS, updateDataSource, onFilter } =
    props;
  const [form] = Form.useForm<FormValues>();
  const [messageApi, contextHolder] = message.useMessage();

  const handleOnClose = () => {
    onClose();
  };

  const handleOnOk = () => {
    form.validateFields().then(async (formValues) => {
      const { importance, msgText } = formValues;
      const userInfo = dataSource.map((item) => ({
        ...item,
        data: lodash.uniqBy(
          item.data.filter((f) => !f.hidden),
          'phone',
        ),
      }));

      const userList = userInfo.flatMap((item) => item.data);
      const validatePhone = userList.find((item) => !item.phone);
      if (validatePhone) {
        messageApi.warning('发送列表存在用户手机号为空，请删除');
        return;
      }
      if (userList.length <= 0) {
        messageApi.warning('发送列表为空');
        return;
      }
      Modal.confirm({
        title: '发送确认',
        icon: <ExclamationCircleFilled />,
        content: (
          <div>
            <p>即将给以下用户发送短信通知，请仔细检查并确认：</p>
            {userInfo.map((item) => (
              <p key={item.key}>
                {item.label}: {item.data.length} 位用户
              </p>
            ))}
          </div>
        ),
        onOk() {
          return onSendSMS?.(
            MsgFrom.WATER_SHUTOFF_MSG,
            importance,
            msgText,
            userList,
          );
        },
      });
    });
  };

  return (
    <Drawer
      width={600}
      title="短信通知"
      open={open}
      onClose={handleOnClose}
      extra={
        <Space>
          <Button onClick={handleOnClose}>取消</Button>
          <Button
            onClick={handleOnOk}
            type="primary"
          >
            发送
          </Button>
        </Space>
      }
    >
      <Form
        name="editForm"
        form={form}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="重要类型"
              name="importance"
              rules={[{ required: true, message: '请选择重要类型' }]}
              initialValue={Importance.HIGH}
            >
              <Select
                placeholder="请选择"
                options={importanceOptions}
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          label="信息内容"
          name="msgText"
          initialValue={NO_WATER_MSG_TEMPLATE}
          rules={[{ required: true, message: '请输入短信模板' }]}
          labelCol={{ span: 24 }}
        >
          <Input.TextArea
            placeholder="请输入"
            rows={6}
          />
        </Form.Item>
        <SendSMSTableList
          dataSource={dataSource}
          updateDataSource={updateDataSource}
          onFilter={onFilter}
        />
        {contextHolder}
      </Form>
    </Drawer>
  );
}
