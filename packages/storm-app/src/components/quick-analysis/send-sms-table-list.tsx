/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FunnelPlotOutlined } from '@ant-design/icons';
import {
  SendWaterMeter,
  SendWaterMeterDataItem,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import {
  ExcelSheetData,
  exportToExcelWithMultipleSheets,
} from '@waterdesk/data/utils';
import {
  Alert,
  Button,
  Checkbox,
  Dropdown,
  MenuProps,
  message,
  Popover,
  Select,
  SelectProps,
  Space,
  Table,
  Tabs,
  TabsProps,
} from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Key, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { getColumnSearchProps } from '../table/column';
import CollectionUserForm, { Values } from './add-user-form';
import ImportUser from './import-user';

interface Props {
  dataSource: SendWaterMeter[];
  updateDataSource: React.Dispatch<React.SetStateAction<SendWaterMeter[]>>;
  onFilter: (pressChange?: 'UP' | 'DOWN') => void;
}

function SendSMSTableList(props: Readonly<Props>) {
  const { dataSource, updateDataSource, onFilter } = props;

  const [messageApi, contextHolder] = message.useMessage();
  const [open, setOpen] = useState(false);
  const [importOpen, setImportOpen] = useState(false);
  const [activeKey, setActiveKey] = useState<string>('custom');
  const [filterKey, setFilterKey] = useState<any>('all');
  const [selectedRowKeys, setSelectedRowKeys] = useState<{
    [index: string]: React.Key[];
  }>({});
  const [columnsField, setColumnsField] = useState<Key[]>([
    'index',
    'waterMeterCode',
    'userName',
    'phone',
  ]);

  const haveEmptyPhone = useMemo(
    () =>
      dataSource
        .flatMap((item) => item.data)
        .find((f) => !f.hidden && !f.phone),
    [dataSource],
  );

  const onSelectChange = (newSelectedRowKeys: React.Key[], key: string) => {
    setSelectedRowKeys((state) => ({
      ...state,
      [key]: newSelectedRowKeys,
    }));
  };

  const onCreate = (values: Values) => {
    const { userName, phone } = values;
    const findPhone = dataSource
      .flatMap((item) => item.data)
      .findLast((f) => f.phone === phone);
    if (findPhone && !findPhone.hidden) {
      messageApi.warning('手机号已存在');
      return;
    }
    const newDataSource = dataSource.map((item) => {
      if (item.key === 'custom') {
        const data = findPhone
          ? [...item.data].map((item) => {
              if (item.key === findPhone.key) return { ...item, hidden: false };
              return item;
            })
          : [
              ...item.data,
              {
                userName,
                phone,
                key: uuidv4(),
              },
            ];
        return { ...item, data };
      }
      return item;
    });
    updateDataSource(newDataSource);
    setActiveKey('custom');
    setOpen(false);
  };
  const handleImport = (data: Array<string>) => {
    const formatNumbers = data.map((phone) => ({
      userName: '',
      phone,
      key: uuidv4(),
    }));
    const newDataSource = dataSource.map((item) => {
      if (item.key === 'custom') {
        const data = [...item.data, ...formatNumbers];
        return { ...item, data };
      }
      return item;
    });
    updateDataSource(newDataSource);
    setActiveKey('custom');
    setImportOpen(false);
    messageApi.success('导入成功!');
  };
  const handleDelete = () => {
    if (Object.values(selectedRowKeys).flat().length === 0) {
      messageApi.warning('请先在下面列表中选择要删除的行');
      return;
    }
    const newDataSource = dataSource.map((item) => ({
      ...item,
      data: item.data.map((i) => ({
        ...i,
        hidden: selectedRowKeys[item.key]?.includes(i.key) ? true : i.hidden,
      })),
    }));
    updateDataSource(newDataSource);
    setSelectedRowKeys({});
  };
  const handleReset = () => {
    const resetDataSource = dataSource.map((item) => ({
      ...item,
      data: item.data.map((i) => ({
        ...i,
        hidden: false,
      })),
    }));
    updateDataSource(resetDataSource);
    setSelectedRowKeys({});
  };
  const handleDeleteNoPhone = () => {
    const newDataSource = dataSource.map((item) => ({
      ...item,
      data: item.data.map((i) => ({
        ...i,
        hidden: i.phone ? i.hidden : true,
      })),
    }));
    updateDataSource(newDataSource);
  };

  const memoDataSource = useMemo(
    () =>
      dataSource.map((d) => ({
        ...d,
        data: d.data.filter((item) => !item.hidden),
      })),
    [dataSource],
  );

  const handleExport = () => {
    const sheets: Array<ExcelSheetData> = [];
    const columnsData = [
      {
        dataIndex: 'waterMeterCode',
        title: '水表',
      },
      {
        dataIndex: 'userName',
        title: '用户',
      },
      {
        dataIndex: 'phone',
        title: '手机号',
      },
      {
        dataIndex: 'address',
        title: '地址',
      },
    ];
    memoDataSource.forEach((item) => {
      sheets.push({
        dataSource: item.data as unknown as {
          [index: string]: string | number | null | undefined;
        }[],
        columns: columnsData,
        sheetName: item.label,
        placeholderWithSheet: '-',
      });
    });
    exportToExcelWithMultipleSheets(
      sheets,
      `用户表_${dayjs().format('YYYYMMDD_HHmmss')}`,
      '-',
    );
  };

  const onMenuClick: MenuProps['onClick'] = (e) => {
    switch (e.key) {
      case 'add':
        setOpen(true);
        break;
      case 'delete':
        handleDelete();
        break;
      case 'reset':
        handleReset();
        break;
      case 'import':
        setImportOpen(true);
        break;
      case 'export':
        handleExport();
        break;
      default:
        break;
    }
  };

  const onFilterMenuClick: SelectProps['onChange'] = (e) => {
    switch (e) {
      case 'pressChangeUp':
        onFilter('UP');
        setFilterKey('pressChangeUp');
        break;
      case 'pressChangeDown':
        onFilter('DOWN');
        setFilterKey('pressChangeDown');
        break;
      case 'all':
        onFilter();
        setFilterKey('all');
        break;
      default:
        break;
    }
  };

  const columns: ColumnType<SendWaterMeterDataItem>[] = [
    {
      title: '编号',
      dataIndex: 'index',
      width: 55,
      render: (_text: string, _record, index: number) => index + 1,
    },
    {
      title: '水表',
      dataIndex: 'waterMeterCode',
      width: 90,
      render: (text) => text || '-',
      sorter: (a, b) =>
        (a.waterMeterCode ?? '-').localeCompare(b.waterMeterCode ?? '-'),
      ...getColumnSearchProps('waterMeterCode'),
    },
    {
      title: '用户',
      dataIndex: 'userName',
      width: 120,
      ellipsis: true,
      render: (text) => text || '-',
      sorter: (a, b) => (a.userName ?? '-').localeCompare(b.userName ?? '-'),
      ...getColumnSearchProps('userName'),
    },
    {
      title: '地址',
      dataIndex: 'address',
      ellipsis: true,
      render: (text) => text || '-',
      ...getColumnSearchProps('address'),
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 100,
      render: (text) => text || '-',
      sorter: (a, b) => (a.phone ?? '-').localeCompare(b.phone ?? '-'),
      ...getColumnSearchProps('phone'),
    },
  ];

  const columnsOptions = columns.map((item) => ({
    label: item.title as string,
    value: item.dataIndex as string,
  }));

  const showColumns: typeof columns = useMemo(
    () =>
      columns.filter((column) =>
        columnsField.includes(column.dataIndex as string),
      ),
    [columns, columnsField],
  );

  const items: TabsProps['items'] = useMemo(
    () =>
      memoDataSource.map(({ key, data, label }) => ({
        key,
        label,
        children: (
          <Table
            rowKey="key"
            virtual
            dataSource={data}
            columns={showColumns}
            bordered
            size="small"
            scroll={{
              x: 550,
              y: 300,
            }}
            rowSelection={{
              selectedRowKeys: selectedRowKeys[key] ?? [],
              columnWidth: 30,
              onChange: (selectedRowKeys: React.Key[]) =>
                onSelectChange(selectedRowKeys, key),
            }}
            pagination={false}
          />
        ),
      })),
    [memoDataSource, selectedRowKeys, showColumns],
  );

  const count = useMemo(
    () => memoDataSource.reduce((p, c) => p + c.data.length, 0),
    [memoDataSource],
  );

  const menuItems = [
    {
      key: 'add',
      label: '增加自定义',
    },
    {
      key: 'import',
      label: '批量导入',
    },
    {
      key: 'export',
      label: '批量导出',
    },
    {
      key: 'delete',
      label: '批量删除',
    },
    {
      key: 'reset',
      label: '重置',
    },
  ];

  const filterMenuItems = [
    {
      value: 'all',
      label: '全部',
    },
    {
      value: 'pressChangeUp',
      label: '压力变化上升',
    },
    {
      value: 'pressChangeDown',
      label: '压力变化下降',
    },
  ];

  const content = (
    <Checkbox.Group
      options={columnsOptions}
      onChange={(v) => setColumnsField(v)}
      value={columnsField}
      style={{ display: 'flex', flexDirection: 'column' }}
    />
  );

  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <span>发送列表</span>
        <div>
          <Space>
            <span>共{count}条</span>
            {!memoDataSource.some((i) =>
              ['waterOutageAreaUser', 'waterOutageArea'].includes(i.key),
            ) && (
              <>
                筛选列表:
                <Select
                  value={filterKey}
                  options={filterMenuItems}
                  onChange={onFilterMenuClick}
                  style={{ width: '150px' }}
                />
              </>
            )}
            <Dropdown.Button menu={{ items: menuItems, onClick: onMenuClick }}>
              编辑列表
            </Dropdown.Button>
          </Space>
          <CollectionUserForm
            open={open}
            onCreate={onCreate}
            onCancel={() => setOpen(false)}
          />
          <ImportUser
            open={importOpen}
            onCreate={handleImport}
            onCancel={() => setImportOpen(false)}
          />
        </div>
      </div>
      {haveEmptyPhone ? (
        <Alert
          style={{ marginTop: '5px' }}
          message="检查到列表中存在没有手机号的用户,是否一键删除?"
          type="error"
          action={
            <Button
              type="primary"
              size="small"
              onClick={handleDeleteNoPhone}
            >
              是
            </Button>
          }
        />
      ) : null}
      <Tabs
        items={items}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: (
            <Popover
              placement="bottom"
              content={content}
              title="Title"
            >
              <Button type="link">
                <FunnelPlotOutlined />
              </Button>
            </Popover>
          ),
        }}
      />
      {contextHolder}
    </>
  );
}

export default SendSMSTableList;
