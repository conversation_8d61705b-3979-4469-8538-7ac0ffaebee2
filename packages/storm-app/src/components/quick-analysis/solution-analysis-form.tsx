/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { WarningOutlined } from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import {
  CompareSourceType,
  compareSourceOptions,
  validatorErrMsg1,
} from '@waterdesk/data/solution';
import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import {
  Collapse,
  CollapseProps,
  ConfigProvider,
  Form,
  FormInstance,
  FormItemProps,
  InputNumber,
  Radio,
  Select,
  SelectProps,
  Slider,
  SliderSingleProps,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { ReactNode, useEffect, useState } from 'react';
import DatePicker from '../common/date/date-picker';

export interface SolutionAnalysisFormValues {
  originId: string;
  compareSource: CompareSourceType;
  compareId: string;
  historyDate?: string;
  compareTime: number;
  nodeAbsPress: number;
  linkMinChange: number;
  linkMaxChange: number;
  linkMinChangeVelocity: number;
  linkMaxChangeVelocity: number;
  linkReverseMinVelocity: number;
  linkMinDiam: number;
}

export interface CompareTimeSetting {
  step?: number | null;
  min: number;
  max: number;
  marks: SliderSingleProps['marks'];
  status: boolean;
}

interface Props {
  form: FormInstance<SolutionAnalysisFormValues>;
  initialValues?: Partial<SolutionAnalysisFormValues>;
  solutionOptions: SelectProps['options'];
  compareTimeSetting: CompareTimeSetting;
  onFinish: (values: SolutionAnalysisFormValues) => void;
  onSearchSolution: (value: string) => void;
}

const SolutionAnalysisForm = (props: Props) => {
  const {
    form,
    initialValues,
    onFinish,
    onSearchSolution,
    solutionOptions,
    compareTimeSetting,
  } = props;

  const { token } = useToken();

  const compareSource = Form.useWatch('compareSource', form);
  const linkMaxChange = Form.useWatch('linkMaxChange', form);
  const linkMaxChangeVelocity = Form.useWatch('linkMaxChangeVelocity', form);

  const [help, setHelp] = useState<ReactNode | undefined>();
  const [validateStatus, setValidateStatus] =
    useState<FormItemProps['validateStatus']>();

  const disabledDate = (currentDate: Dayjs): boolean =>
    currentDate > dayjs().endOf('day');

  const toolTipFormatter = (value?: number): string => {
    if (typeof value === 'undefined') return '';
    return getDateTimeFromValue(value).format('HH:mm');
  };

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '高级设置',
      children: (
        <>
          <Form.Item
            label="压力变化阈值(m)"
            name="nodeAbsPress"
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <InputNumber
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            label="受影响管道流速变化阈值"
            name="linkMaxChangeVelocity"
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <InputNumber
              min={0.1}
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            name="linkMinChangeVelocity"
            noStyle
            hidden
          >
            <InputNumber
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            label="受影响管道流量变化阈值"
            name="linkMaxChange"
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <InputNumber
              min={0.1}
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            name="linkMinChange"
            noStyle
            hidden
          >
            <InputNumber
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            label="反向管道流速变化阈值"
            name="linkReverseMinVelocity"
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <InputNumber
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            label="管道管径限制"
            name="linkMinDiam"
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <InputNumber
              placeholder="请输入"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </>
      ),
      forceRender: true,
    },
  ];

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues]);

  useEffect(() => {
    form.setFieldValue('linkMinChange', -linkMaxChange);
  }, [linkMaxChange]);
  useEffect(() => {
    form.setFieldValue('linkMinChangeVelocity', -linkMaxChangeVelocity);
  }, [linkMaxChangeVelocity]);

  useEffect(() => {
    if (compareTimeSetting.status) {
      setHelp(undefined);
      setValidateStatus(undefined);
    } else {
      setHelp(
        <span>
          <WarningOutlined />
          选择的对比方案和原方案计算时间不存在重叠，无法对比
        </span>,
      );
      setValidateStatus('warning');
    }
  }, [compareTimeSetting]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Collapse: {
            contentPadding: 0,
            headerPadding: 0,
          },
          Slider: {
            dotBorderColor: token.colorPrimary,
            dotActiveBorderColor: token.colorPrimary,
            railBg: token.colorPrimary,
            railHoverBg: token.colorPrimary,
            handleActiveColor: token.colorPrimary,
            handleColor: token.colorPrimary,
            trackBg: token.colorPrimary,
            trackHoverBg: token.colorPrimary,
            railSize: 6,
          },
        },
      }}
    >
      <Form
        onFinish={onFinish}
        form={form}
        name="solution-analysis-form"
        layout="vertical"
        wrapperCol={{ span: 22 }}
        initialValues={initialValues}
      >
        <Form.Item
          label="当前方案"
          name="originId"
          rules={[
            {
              required: true,
              message: '请选择对比方案',
            },
          ]}
        >
          <Select
            placeholder="请输入方案名称搜索"
            showSearch
            defaultActiveFirstOption={false}
            filterOption={false}
            suffixIcon={null}
            notFoundContent={null}
            options={solutionOptions}
            onSearch={onSearchSolution}
            disabled={!!initialValues?.originId}
          />
        </Form.Item>
        <Form.Item
          name="compareSource"
          label="方案来源"
          rules={[
            {
              required: true,
              message: '请选择方案来源',
            },
          ]}
        >
          <Radio.Group
            disabled={
              !!(initialValues?.compareId || initialValues?.historyDate)
            }
            options={compareSourceOptions}
          />
        </Form.Item>
        {compareSource === CompareSourceType.SOLUTION ? (
          <Form.Item
            name="compareId"
            label="对比方案"
            help={help}
            validateStatus={validateStatus}
            rules={[
              {
                required: true,
                message: '请选择对比方案',
              },
              ({ getFieldValue }) => ({
                validator(_, value: string | string[]) {
                  const originId = getFieldValue('originId');
                  if (Array.isArray(value)) {
                    if (value.includes(originId)) {
                      return Promise.reject(new Error(validatorErrMsg1));
                    }
                  } else if (value === originId) {
                    return Promise.reject(new Error(validatorErrMsg1));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              placeholder="请输入方案名称搜索"
              showSearch
              defaultActiveFirstOption={false}
              filterOption={false}
              suffixIcon={null}
              notFoundContent={null}
              options={solutionOptions}
              onSearch={onSearchSolution}
              disabled={!!initialValues?.compareId}
            />
          </Form.Item>
        ) : (
          <Form.Item
            label="历史时间"
            name="historyDate"
            rules={[
              {
                required: true,
                message: '请选择历史时间',
              },
            ]}
          >
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY[年]MM[月]DD[日]"
              valueFormat="YYYY-MM-DD"
              disabled={!!initialValues?.historyDate}
            />
          </Form.Item>
        )}
        <Form.Item
          name="compareTime"
          label="对比时刻"
          rules={[
            {
              required: true,
              message: '请选择对比时刻',
            },
          ]}
        >
          <Slider
            style={{ margin: '0 10px' }}
            max={compareTimeSetting.max}
            min={compareTimeSetting.min}
            marks={compareTimeSetting.marks}
            step={compareTimeSetting.step}
            tooltip={{
              formatter: toolTipFormatter,
            }}
          />
        </Form.Item>
        <Collapse
          ghost
          items={items}
        />
      </Form>
    </ConfigProvider>
  );
};

export default SolutionAnalysisForm;
