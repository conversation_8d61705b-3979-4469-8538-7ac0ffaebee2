/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

export const ValveAnalysisFormWrapper = styled.ul`
  list-style: none;
  padding: 0;
  li {
    display: flex;
    justify-content: space-between;
    .object-name {
      margin: 0;
      flex: 1 1 auto;
      ${EllipsisText}
    }
  }
`;

export const AnalysisResultLegendWrapper = styled.div`
  position: absolute;
  right: -240px;
  bottom: 0;
  width: 240px;
  padding: 0 10px 10px;
  border-radius: 5px;
  background: ${({ theme }) => theme.colorBgElevated};
`;
