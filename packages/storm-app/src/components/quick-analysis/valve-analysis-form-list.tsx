/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ExclamationCircleOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { AnalysisObject } from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Empty, Space, Tooltip } from 'antd';
import LinkSpan from '../common/link-span';
import { ValveAnalysisFormWrapper } from './style';

interface Props {
  list: AnalysisObject[];
  canEdit?: boolean;
  handleLocate: (otype: string, oname: string, shape: string) => void;
  removeObjectItem: (removeKey: string) => void;
  type?: 'OPEN' | 'CLOSE';
}

const getTitle = (obj: AnalysisObject): string =>
  `${obj.gisOTypeTitle ?? obj.otypeTitle ?? obj.otype}${'   '}${
    obj.gisOName ?? obj.oname
  }`;

const ValveAnalysisFormList = (props: Props) => {
  const { list, canEdit = true, removeObjectItem, handleLocate, type } = props;
  const { token } = useToken();

  if (!list.length) return <Empty />;

  return (
    <ValveAnalysisFormWrapper>
      {list.map((item, index) => {
        const { gisOName, gisOType, otype, oname, shape, gisShape, key } = item;
        const title = getTitle(item);
        const foundDuplicateModel = list.find((f, i) => {
          if (index <= i) return false;
          return otype === f.otype && oname === f.oname;
        });
        return (
          <li
            key={key}
            style={{ display: 'flex' }}
          >
            <div style={{ flex: '1 1 auto', width: '100%', display: 'flex' }}>
              <div>{index + 1}:</div>
              <Tooltip title={title}>
                {gisShape || shape ? (
                  <LinkSpan
                    className="object-name"
                    onClick={() =>
                      handleLocate(
                        gisOType || otype,
                        gisOName || oname,
                        gisShape || shape,
                      )
                    }
                  >
                    {title}
                  </LinkSpan>
                ) : (
                  <span className="object-name">{title}</span>
                )}
              </Tooltip>
            </div>
            <Space>
              {foundDuplicateModel ? (
                <Tooltip
                  title={`请注意:选择的模型对象存在重复,模型对象在:${getTitle(
                    foundDuplicateModel,
                  )}中已经存在,重复选择不会影响分析结果,因此建议删除此项`}
                >
                  <ExclamationCircleOutlined
                    style={{ color: token.colorWarning, width: '20px' }}
                  />
                </Tooltip>
              ) : null}
              {typeof item.isModel === 'boolean' && item.isModel === false ? (
                <Tooltip title="请注意:此对象缺少对应模型信息, 可能会影响分析结果">
                  <ExclamationCircleOutlined
                    style={{ color: token.colorError, width: '20px' }}
                  />
                </Tooltip>
              ) : null}
              {canEdit ? (
                <Tooltip
                  title={
                    type && type === 'CLOSE'
                      ? '从需要封闭的对象里移除'
                      : '从无法关闭的阀门里移除'
                  }
                >
                  <MinusCircleOutlined
                    style={{ width: '20px' }}
                    onClick={() => removeObjectItem(key)}
                  />
                </Tooltip>
              ) : null}
            </Space>
          </li>
        );
      })}
    </ValveAnalysisFormWrapper>
  );
};

export default ValveAnalysisFormList;
