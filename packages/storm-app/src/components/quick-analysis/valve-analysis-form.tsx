/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AnalysisObjects,
  ValveStateType,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { Button, Collapse, CollapseProps, Tooltip } from 'antd';
import IconText from '../icon-font/icon-text';
import ValveAnalysisFormList from './valve-analysis-form-list';

export interface ValveAnalysisFormValues {
  type: ValveStateType;
}

interface Props {
  canEdit?: boolean;
  analysisObjects: AnalysisObjects;
  handleLocate: (otype: string, oname: string, shape: string) => void;
  removeObjectItem: (removeKey: string, type: ValveStateType) => void;
  type: ValveStateType;
  setType: React.Dispatch<React.SetStateAction<ValveStateType>>;
}

const ValveAnalysisForm = (props: Props) => {
  const {
    canEdit,
    analysisObjects,
    type,
    setType,
    handleLocate,
    removeObjectItem,
  } = props;

  const handleIconClick = (e: React.MouseEvent, value: ValveStateType) => {
    e.stopPropagation();
    setType(value);
  };

  const items: CollapseProps['items'] = [
    {
      key: 'closeList',
      label: `需要封闭的对象(${analysisObjects.closeList.length})`,
      children: (
        <ValveAnalysisFormList
          list={analysisObjects.closeList}
          canEdit={canEdit}
          handleLocate={handleLocate}
          removeObjectItem={(key) => removeObjectItem(key, 'CLOSE')}
          type="CLOSE"
        />
      ),
      extra: (
        <Tooltip title="添加到需要封闭的对象">
          <Button
            type="text"
            onClick={(e) => handleIconClick(e, 'CLOSE')}
          >
            <IconText
              style={{
                fontSize: '1.5em',
                marginTop: '-0.5em',
                color: type === 'CLOSE' ? '#3d76fb' : '#c0c0c0',
              }}
              text="&#xe6dd;"
            />
          </Button>
        </Tooltip>
      ),
    },
    {
      key: 'openList',
      label: `无法关闭的阀门(${analysisObjects.openList.length})`,
      children: (
        <ValveAnalysisFormList
          list={analysisObjects.openList}
          canEdit={canEdit}
          handleLocate={handleLocate}
          removeObjectItem={(key) => removeObjectItem(key, 'OPEN')}
          type="OPEN"
        />
      ),
      extra: (
        <Tooltip
          title="添加到无法关闭的阀门"
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <Button
            type="text"
            onClick={(e) => handleIconClick(e, 'OPEN')}
          >
            <IconText
              style={{
                fontSize: '1.5em',
                color: type === 'OPEN' ? '#3d76fb' : '#c0c0c0',
                marginTop: '-0.5em',
              }}
              text="&#xe6dd;"
            />
          </Button>
        </Tooltip>
      ),
    },
  ];

  return (
    <Collapse
      defaultActiveKey={['closeList']}
      ghost
      items={items}
    />
  );
};

ValveAnalysisForm.displayName = 'ValveAnalysisForm';

export default ValveAnalysisForm;
