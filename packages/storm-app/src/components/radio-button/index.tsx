/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Grid } from 'antd-mobile';
import AppButton from '../button';
import { ItemWrapper, RadioButtonWrapper } from './style';

interface Props {
  dataSource: any[];
  value: any;
}
export default function RadioButton(props: Props) {
  const { dataSource, value } = props;

  return (
    <RadioButtonWrapper
      columns={3}
      gap={16}
    >
      {dataSource.map((item) => (
        <Grid.Item key={item.id}>
          <AppButton
            className="button"
            color="primary"
            active={value === item.id}
            ghost
            onClick={() => item.onClick(item.id)}
          >
            <ItemWrapper isActive={value === item.id}>{item.title}</ItemWrapper>
          </AppButton>
        </Grid.Item>
      ))}
    </RadioButtonWrapper>
  );
}
