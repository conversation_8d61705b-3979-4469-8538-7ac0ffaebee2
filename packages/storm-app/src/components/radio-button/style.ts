/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Grid } from 'antd-mobile';
import styled from 'styled-components';

export const RadioButtonWrapper = styled(Grid)`
  padding: 10px;
  .button {
    width: 100%;
  }
`;

export const ItemWrapper = styled.div<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 12px;
  color: ${({ theme, isActive }) =>
    isActive ? theme.colorPrimary : theme.colorText};
  span {
    flex: 1;
  }
`;
