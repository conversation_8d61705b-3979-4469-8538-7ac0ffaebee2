/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TimeStepType } from '@waterdesk/data/object-chart';
import { Selector } from 'antd-mobile';
import { useMergedState } from 'rc-util';

interface Porps {
  options: {
    label: string;
    value: TimeStepType;
  }[];
  value?: TimeStepType;
  onChange?: (value: TimeStepType) => void;
}
export default function TimeStepSelector(props: Porps) {
  const { options, value, onChange } = props;
  const [timeStep, setTimeStep] = useMergedState<TimeStepType>(
    TimeStepType.fiveMinutes,
    {
      value,
    },
  );

  const onStepChange = (values: TimeStepType[]) => {
    setTimeStep(values[0]);
    onChange?.(values[0]);
  };
  return (
    <Selector
      columns={3}
      options={options}
      value={[timeStep]}
      onChange={onStepChange}
    />
  );
}
