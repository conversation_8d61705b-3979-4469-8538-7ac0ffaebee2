/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CloseCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  ChartCompareType,
  getChainBaseDateRange,
  getChartCompareTypeName,
  getCustomDateRange,
  getQoQDateRange,
  getYoYDateRange,
} from '@waterdesk/data/object-chart';
import { DatePicker, message, Radio, RadioChangeEvent, Space } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { useMergedState } from 'rc-util';
import { useEffect, useMemo } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { CustomCompareTypeValue } from '../charts/object-chart/object-chart-content-new';

interface Props {
  compareDateRange: RangePickerProps['value'];
  value?: CustomCompareTypeValue;
  onChange?: (v: CustomCompareTypeValue) => void;
}

const defaultCompareDateRange: [Dayjs, Dayjs] = [
  dayjs(dayjs().format('YYYY-MM-DD 00:00:00')),
  dayjs(dayjs().format('YYYY-MM-DD 23:59:59')),
];

function mergeDate(date1: Dayjs, date2: Dayjs): Dayjs {
  const formattedDate1 = date1.format('YYYY-MM-DD');
  const formattedDate2 = date2.format('HH:mm:ss');
  return dayjs(`${formattedDate1} ${formattedDate2}`);
}

function getFormatDateRange(
  dates: RangePickerProps['value'],
  compareDateRange: [Dayjs, Dayjs],
  timeDuration: number,
): RangePickerProps['value'] {
  const startDate = dates?.[0];
  if (startDate) {
    const formatStartDate = mergeDate(startDate, compareDateRange[0]);
    const formatEndDate = mergeDate(
      startDate.add(timeDuration, 'day'),
      compareDateRange[1],
    );
    return [formatStartDate, formatEndDate];
  }
  return dates;
}

const CustomCompareType = (props: Props) => {
  const { value, compareDateRange, onChange } = props;

  const [messageApi, messageHolder] = message.useMessage();

  // use defaultCompareDateRange instead of compareDateRange when compareDateRange is invalid date
  const memoCompareDateRange: [Dayjs, Dayjs] = useMemo(() => {
    if (compareDateRange?.[0] && compareDateRange?.[1])
      return compareDateRange as [Dayjs, Dayjs];
    return defaultCompareDateRange;
  }, [compareDateRange]);

  const timeDuration = useMemo(
    () => memoCompareDateRange[1].diff(memoCompareDateRange[0], 'day'),
    [memoCompareDateRange],
  );

  const [customCompareTypeValue, setCustomCompareTypeValue] =
    useMergedState<CustomCompareTypeValue>(
      {
        type: ChartCompareType.chainBase,
        dateRanges: [],
      },
      {
        value,
        onChange,
      },
    );

  const handleRadioOnChange = (e: RadioChangeEvent) => {
    const type = e.target.value as ChartCompareType;
    const dateRanges: CustomCompareTypeValue['dateRanges'] = [];
    setCustomCompareTypeValue({
      type,
      dateRanges,
    });
  };

  const handleAdd = () => {
    if (customCompareTypeValue.dateRanges.length >= 5) {
      messageApi.warning('自定义对比分析最多只能添加5个日期');
      return;
    }
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: Array.isArray(s.dateRanges)
        ? [...s.dateRanges, null]
        : [null],
    }));
  };

  const handleDelete = (index: number) => {
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: s.dateRanges.filter((_, i) => i !== index),
    }));
  };

  const handleOnChange = (dates: RangePickerProps['value'], index: number) => {
    setCustomCompareTypeValue((s) => {
      const dateRanges = [...s.dateRanges];
      dateRanges[index] = getFormatDateRange(
        dates,
        memoCompareDateRange,
        timeDuration,
      );
      return {
        ...s,
        dateRanges,
      };
    });
  };

  // refresh customCompareTypeValue.dateRanges when memoCompareDateRange is changed
  useEffect(() => {
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: s.dateRanges.map((dateRange) =>
        getFormatDateRange(dateRange, memoCompareDateRange, timeDuration),
      ),
    }));
  }, [memoCompareDateRange]);

  useEffect(() => {
    if (customCompareTypeValue.type === ChartCompareType.yearOnYear) {
      setCustomCompareTypeValue((s) => ({
        ...s,
        dateRanges: [getYoYDateRange(memoCompareDateRange)],
      }));
    }
    if (customCompareTypeValue.type === ChartCompareType.ringComparison) {
      setCustomCompareTypeValue((s) => ({
        ...s,
        dateRanges: [getQoQDateRange(memoCompareDateRange)],
      }));
    }

    if (customCompareTypeValue.type === ChartCompareType.custom) {
      setCustomCompareTypeValue((s) => ({
        ...s,
        dateRanges: getCustomDateRange(memoCompareDateRange),
      }));
    }
    if (customCompareTypeValue.type === ChartCompareType.chainBase) {
      setCustomCompareTypeValue((s) => ({
        ...s,
        dateRanges: getChainBaseDateRange(memoCompareDateRange),
      }));
    }
  }, [customCompareTypeValue.type, memoCompareDateRange]);

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap' }}>
      <Radio.Group
        onChange={handleRadioOnChange}
        value={customCompareTypeValue.type}
      >
        <Radio
          key={ChartCompareType.chainBase}
          value={ChartCompareType.chainBase}
        >
          {getChartCompareTypeName(ChartCompareType.chainBase)}
        </Radio>
        <Radio
          key={ChartCompareType.yearOnYear}
          value={ChartCompareType.yearOnYear}
        >
          {getChartCompareTypeName(ChartCompareType.yearOnYear)}
        </Radio>
        <Radio
          key={ChartCompareType.ringComparison}
          value={ChartCompareType.ringComparison}
        >
          {getChartCompareTypeName(ChartCompareType.ringComparison)}
        </Radio>

        <Radio
          key={ChartCompareType.custom}
          value={ChartCompareType.custom}
        >
          {getChartCompareTypeName(ChartCompareType.custom)}
        </Radio>
      </Radio.Group>
      {customCompareTypeValue.dateRanges?.length &&
      customCompareTypeValue.type !== ChartCompareType.chainBase ? (
        <Space wrap>
          {customCompareTypeValue.dateRanges.map((dateRange, i) => (
            <div key={uuidv4()}>
              <Space.Compact>
                <DatePicker
                  style={{ width: '110px' }}
                  placeholder="开始时间"
                  value={dateRange?.[0]}
                  onChange={(date) =>
                    handleOnChange([date, dateRange?.[1] ?? dayjs()], i)
                  }
                  allowClear={{
                    clearIcon: false,
                  }}
                  disabled={
                    customCompareTypeValue.type !== ChartCompareType.custom
                  }
                />
                <DatePicker
                  style={{ width: '110px' }}
                  placeholder="结束时间"
                  value={dateRange?.[1]}
                  disabled
                />
              </Space.Compact>
              {customCompareTypeValue.type === ChartCompareType.custom ? (
                <CloseCircleOutlined onClick={() => handleDelete(i)} />
              ) : null}
            </div>
          ))}
        </Space>
      ) : null}
      {customCompareTypeValue.type === ChartCompareType.custom ? (
        <PlusCircleOutlined
          style={{ marginLeft: '10px' }}
          onClick={handleAdd}
        />
      ) : null}
      {messageHolder}
    </div>
  );
};

export default CustomCompareType;
