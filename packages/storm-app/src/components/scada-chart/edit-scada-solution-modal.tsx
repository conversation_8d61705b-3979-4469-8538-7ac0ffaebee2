/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  convertSchemeTypeDataToOptions,
  SchemeData,
  SchemeType,
  SchemeTypeData,
} from '@waterdesk/data/scheme-config';
import { Button, Drawer, Form, Input, Select, Switch } from 'antd';
import { useEffect, useMemo } from 'react';

export interface EditSchemeInfo {
  id: string;
  schemeName: string;
  isFixedTime: boolean;
  share: boolean;
  note: string;
  schemeType: string;
}

interface Props {
  open: boolean;
  onClose: () => void;
  onOk: (editInfo: EditSchemeInfo) => void;
  favoriteInfo?: SchemeData;
  schemeType: SchemeTypeData;
}

export default function EditScadaSolutionModal(props: Props) {
  const { open, onClose, onOk, favoriteInfo, schemeType } = props;
  const [form] = Form.useForm();

  const schemeTypeOptions = useMemo(
    () => convertSchemeTypeDataToOptions(schemeType) ?? [],
    [schemeType],
  );

  const handleOk = () => {
    form.validateFields().then((values) => {
      onOk({
        id: favoriteInfo?.id ?? '',
        schemeName: values.schemeName,
        isFixedTime: values.isFixedTime,
        share: values.share,
        note: values.note,
        schemeType: values.schemeType,
      });
    });
  };

  useEffect(() => {
    if (favoriteInfo) {
      const { schemeName, remark, isFixedTime, schemeShare, schemeType } =
        favoriteInfo;
      form.setFieldsValue({
        schemeName,
        isFixedTime,
        share: schemeShare,
        note: remark,
        schemeType,
      });
    }
  }, [favoriteInfo]);

  return (
    <Drawer
      title="编辑"
      width={500}
      open={open}
      onClose={onClose}
      extra={
        <Button
          type="primary"
          onClick={handleOk}
        >
          保存
        </Button>
      }
    >
      <Form
        name="edit-scada-solution-form"
        form={form}
      >
        <Form.Item
          name="schemeName"
          label="名称"
          rules={[
            {
              required: true,
              message: '名称不能为空',
            },
          ]}
        >
          <Input
            placeholder="请输入名称"
            maxLength={60}
          />
        </Form.Item>
        <Form.Item
          label="收藏类型"
          name="schemeType"
          initialValue={SchemeType.MONITORING_EXCEPTION}
        >
          <Select
            placeholder="请选择"
            options={schemeTypeOptions}
          />
        </Form.Item>
        <Form.Item
          name="isFixedTime"
          valuePropName="checked"
          label="固定曲线时间"
        >
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        <Form.Item
          name="share"
          valuePropName="checked"
          label="是否共享"
        >
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        <Form.Item
          label="备注"
          name="note"
        >
          <Input />
        </Form.Item>
      </Form>
    </Drawer>
  );
}
