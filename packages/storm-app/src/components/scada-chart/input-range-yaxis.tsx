/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Input, InputNumber, Space } from 'antd';
import { InputNumberProps } from 'antd/lib';
import useMergedState from 'rc-util/es/hooks/useMergedState';

export type RangeYAxisValue = {
  yMin?: number | null;
  yMax?: number | null;
};

interface Props {
  value?: RangeYAxisValue;
  onChange?: (value: RangeYAxisValue | undefined) => void;
}

const InputRangeYAxis = (props: Props) => {
  const { value, onChange } = props;

  const [rangeValue, setRangeValue] = useMergedState<
    RangeYAxisValue | undefined
  >(undefined, {
    value,
    onChange,
  });

  const handleChangeYMin: InputNumberProps['onChange'] = (value) => {
    setRangeValue((state) => ({
      ...state,
      yMin: value as number,
    }));
  };

  const handleChangeYMax: InputNumberProps['onChange'] = (value) => {
    setRangeValue((state) => ({
      ...state,
      yMax: value as number,
    }));
  };

  return (
    <>
      <span>Y轴范围：</span>
      <Space.Compact size="small">
        <InputNumber
          style={{ width: 80, textAlign: 'center' }}
          value={rangeValue?.yMin}
          size="small"
          placeholder="最小值"
          onChange={handleChangeYMin}
        />
        <Input
          style={{
            width: 20,
            borderLeft: 0,
            borderRight: 0,
            pointerEvents: 'none',
          }}
          size="small"
          placeholder="~"
          disabled
        />
        <InputNumber
          className="site-input-right"
          value={rangeValue?.yMax}
          style={{
            width: 80,
            textAlign: 'center',
          }}
          size="small"
          placeholder="最大值"
          onChange={handleChangeYMax}
        />
      </Space.Compact>
    </>
  );
};

export default InputRangeYAxis;
