/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getTimeRangeByType,
  getTimeStepName,
  TimeRangeType,
  TimeStepType,
} from '@waterdesk/data/object-chart';
import {
  Checkbox,
  Form,
  FormInstance,
  FormProps,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  SelectProps,
  Space,
} from 'antd';
import dayjs from 'dayjs';
import { ReactNode, useMemo } from 'react';
import { ObjectChartFormValues } from '../charts/object-chart/object-chart-content-new';
import CustomRangePicker from '../common/date/custom-range-picker';
import CustomCompareType from './custom-compare-type';

interface Props {
  form: FormInstance<ObjectChartFormValues & any>;
  /** 显示Y轴范围的显示/隐藏; */
  showYAxisRange?: boolean;
  /** 显示警告checkbox的显示/隐藏; */
  showWarnCheckbox?: boolean;
  /** 显示标注checkbox的显示/隐藏; */
  showMarkCheckbox?: boolean;
  /** 显示包络线checkbox的显示/隐藏; */
  showEnvelopLineCheckbox?: boolean;
  /** 显示警告线checkbox的显示/隐藏; */
  showWarnLineCheckbox?: boolean;
  /** 显示多日对比checkbox的显示/隐藏; */
  showChainBaseCheckbox?: boolean;
  showChainBaseRadio?: boolean;
  /** 时间范围的显示/隐藏; */
  showTimeRange?: boolean;
  /** 时间间隔的显示/隐藏 */
  showTimeStep?: boolean;
  /** 显示模拟值checkbox的显示/隐藏 */
  showModelCheckbox?: boolean;
  /** 指标选择器的options, 默认不显示 */
  indicatorOptions?: SelectProps['options'];
  /** 指标选择器的显示/隐藏 */
  showIndicatorSelector?: boolean;
  /** 指标选择器是否多选 */
  indicatorMode?: SelectProps['mode'];
  /** 显示多轴 */
  showMultiAxis?: boolean;
  timeRangeTypeOptions?: TimeRangeType[];
  timeStepTypeOptions?: TimeStepType[];
  extraFormItems?: ReactNode;

  onValuesChange?: (
    changedValues: Partial<ObjectChartFormValues>,
    values: ObjectChartFormValues,
  ) => void;
}

const defaultOptions = [
  TimeRangeType.oneDay,
  TimeRangeType.threeDays,
  TimeRangeType.sevenDays,
  TimeRangeType.oneMonth,
  TimeRangeType.oneYear,
];

const defaultTimeStepOptions = [
  TimeStepType.oneMinute,
  TimeStepType.fiveMinutes,
  TimeStepType.fifteenMinutes,
  TimeStepType.thirtyMinutes,
  TimeStepType.oneHour,
];

const deduplicateIndicator = (
  options: Props['indicatorOptions'],
  value: string | undefined | string[],
): Props['indicatorOptions'] =>
  options?.filter((o) => {
    if (Array.isArray(value)) {
      return !value.includes(o.value as string);
    }
    return value !== o.value;
  });

export default function ScadaChartForm(props: Readonly<Props>) {
  const {
    form,
    showYAxisRange,
    showTimeRange,
    showWarnCheckbox,
    showMarkCheckbox,
    showEnvelopLineCheckbox,
    showWarnLineCheckbox,
    showChainBaseCheckbox,
    showChainBaseRadio,
    showModelCheckbox,
    showTimeStep,
    showMultiAxis,
    showIndicatorSelector,
    indicatorOptions,
    indicatorMode,
    timeRangeTypeOptions,
    timeStepTypeOptions,
    extraFormItems,
    onValuesChange,
  } = props;

  const handleTimeRangeTypeChange = (changeValue: string) => {
    form.setFieldValue('timeRange', getTimeRangeByType(changeValue));
  };

  const timeRangeTypeRadios = (timeRangeTypeOptions ?? defaultOptions).map(
    (timeRangeType) => {
      switch (timeRangeType) {
        case TimeRangeType.oneDay:
          return <Radio.Button value={TimeRangeType.oneDay}>当日</Radio.Button>;
        case TimeRangeType.threeDays:
          return (
            <Radio.Button value={TimeRangeType.threeDays}>近三日</Radio.Button>
          );
        case TimeRangeType.sevenDays:
          return (
            <Radio.Button value={TimeRangeType.sevenDays}>近七日</Radio.Button>
          );
        case TimeRangeType.oneMonth:
          return (
            <Radio.Button value={TimeRangeType.oneMonth}>近一月</Radio.Button>
          );
        case TimeRangeType.threeMonths:
          return (
            <Radio.Button value={TimeRangeType.threeMonths}>
              近三月
            </Radio.Button>
          );
        case TimeRangeType.oneYear:
          return (
            <Radio.Button value={TimeRangeType.oneYear}>近一年</Radio.Button>
          );
        case TimeRangeType.customize:
          return (
            <Radio.Button value={TimeRangeType.customize}>自定义</Radio.Button>
          );
        default:
          return null;
      }
    },
  );

  const timeStepTypeRadios = (
    timeStepTypeOptions ?? defaultTimeStepOptions
  ).map((timeStepType) => ({
    label: getTimeStepName(timeStepType),
    value: timeStepType,
  }));

  const leftAxis = Form.useWatch('leftAxis', form);
  const rightAxis = Form.useWatch('rightAxis', form);
  const timeRange = Form.useWatch('timeRange', form);

  const deduplicateLeftOptions = useMemo(
    () => deduplicateIndicator(indicatorOptions, rightAxis),
    [indicatorOptions, rightAxis],
  );

  const deduplicateRightOptions = useMemo(
    () => deduplicateIndicator(indicatorOptions, leftAxis),
    [indicatorOptions, leftAxis],
  );

  const handleFormValuesChange: FormProps['onValuesChange'] = (
    changedValues,
    values,
  ) => {
    onValuesChange?.(changedValues, values);
  };

  return (
    <Form
      size="small"
      layout="inline"
      form={form}
      name="objectChartForm"
      onValuesChange={handleFormValuesChange}
    >
      <Row style={{ width: '100%', alignItems: 'center' }}>
        <Form.Item
          name="timeRange"
          label="时间"
          hidden={!showTimeRange}
        >
          <CustomRangePicker
            size="small"
            style={{ maxWidth: '300px' }}
            allowClear={false}
            allowEmpty={[false, false]}
            disabledDate={(date) =>
              date.isAfter(dayjs().add(1, 'day').startOf('day'))
            }
            format="YYYY-MM-DD HH:mm"
            showTime={{
              format: 'HH:mm',
              defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('59:59', 'HH:mm')],
            }}
            onChange={() => form.setFieldValue('timeRangeType', undefined)}
          />
        </Form.Item>

        <Form.Item
          name="timeRangeType"
          hidden={!showTimeRange}
        >
          <Radio.Group
            buttonStyle="solid"
            onChange={(e) => handleTimeRangeTypeChange(e.target.value)}
          >
            <Space wrap>{timeRangeTypeRadios}</Space>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Y轴范围"
          hidden={!showYAxisRange}
        >
          <Space.Compact size="small">
            <Form.Item
              noStyle
              name="yMin"
            >
              <InputNumber
                style={{ width: 80, textAlign: 'center' }}
                size="small"
                placeholder="最小值"
              />
            </Form.Item>
            <Input
              className="site-input-split"
              style={{
                width: 20,
                borderLeft: 0,
                borderRight: 0,
                pointerEvents: 'none',
              }}
              size="small"
              placeholder="~"
              disabled
            />
            <Form.Item
              noStyle
              name="yMax"
            >
              <InputNumber
                className="site-input-right"
                style={{
                  width: 80,
                  textAlign: 'center',
                }}
                size="small"
                placeholder="最大值"
              />
            </Form.Item>
          </Space.Compact>
        </Form.Item>
        <Form.Item
          label="时间间隔"
          name="timeStep"
          hidden={!showTimeStep}
        >
          <Select
            style={{ width: '90px' }}
            options={timeStepTypeRadios}
          />
        </Form.Item>
        <Form.Item
          name="compareType"
          hidden={!showChainBaseRadio}
        >
          <CustomCompareType compareDateRange={timeRange} />
        </Form.Item>
        <Form.Item
          name="indicators"
          label="属性"
          hidden={!showIndicatorSelector}
        >
          <Select
            mode={indicatorMode}
            placeholder="请选择"
            style={{ minWidth: '200px' }}
            options={indicatorOptions}
            maxTagCount={1}
            size="small"
          />
        </Form.Item>
        <Form.Item
          name="leftAxis"
          label="左轴"
          hidden={!showMultiAxis}
        >
          <Select
            placeholder="请选择"
            style={{ minWidth: '200px' }}
            options={deduplicateLeftOptions}
            size="small"
            mode="multiple"
            maxTagCount={4}
          />
        </Form.Item>
        <Form.Item
          name="rightAxis"
          label="右轴"
          hidden={!showMultiAxis}
        >
          <Select
            placeholder="请选择"
            style={{ minWidth: '200px' }}
            options={deduplicateRightOptions}
            size="small"
            mode="multiple"
            maxTagCount={4}
          />
        </Form.Item>
        <Form.Item
          noStyle
          name="warn"
          valuePropName="checked"
          hidden={!showWarnCheckbox}
        >
          <Checkbox>警告</Checkbox>
        </Form.Item>
        <Form.Item
          noStyle
          name="mark"
          valuePropName="checked"
          hidden={!showMarkCheckbox}
        >
          <Checkbox>标注</Checkbox>
        </Form.Item>
        <Form.Item
          noStyle
          name="warnLine"
          valuePropName="checked"
          hidden={!showWarnLineCheckbox}
        >
          <Checkbox>警告线</Checkbox>
        </Form.Item>
        <Form.Item
          noStyle
          name="envelopLine"
          valuePropName="checked"
          hidden={!showEnvelopLineCheckbox}
        >
          <Checkbox>包络线</Checkbox>
        </Form.Item>
        <Form.Item
          noStyle
          name="showModel"
          valuePropName="checked"
          hidden={!showModelCheckbox}
        >
          <Checkbox>模拟值</Checkbox>
        </Form.Item>
        <Form.Item
          noStyle
          name="chainBase"
          valuePropName="checked"
          hidden={!showChainBaseCheckbox}
        >
          <Checkbox>多日对比</Checkbox>
        </Form.Item>

        {extraFormItems}
      </Row>
    </Form>
  );
}
