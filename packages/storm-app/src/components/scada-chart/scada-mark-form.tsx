/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ScadaChartMarkFormValues } from '@waterdesk/data/chart-mark';
import { DatePicker, Form, FormInstance, Input, Select, Switch } from 'antd';

interface Props {
  form: FormInstance<ScadaChartMarkFormValues>;
  markObjects: {
    label: string;
    value: string;
  }[];
  schemeTypeOptions: {
    label: string;
    value: string;
  }[];
}

export default function ScadaChartMarkForm(props: Readonly<Props>) {
  const { form, markObjects, schemeTypeOptions } = props;

  const dateTimeType = Form.useWatch('dateTimeType', form);

  return (
    <Form
      name="scada-favorites-form"
      layout="vertical"
      form={form}
      autoComplete="off"
    >
      <Form.Item
        name="markId"
        hidden
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="markObjects"
        label="标注对象"
        initialValue={markObjects.map((item) => item.value)}
        rules={[{ required: true, message: '请选择标注对象!', type: 'array' }]}
      >
        <Select
          mode="multiple"
          options={markObjects}
        />
      </Form.Item>
      <Form.Item
        name="dateTimeType"
        valuePropName="checked"
        label="日期类型"
        initialValue
        rules={[
          {
            required: true,
            message: '日期类型不能为空!',
          },
        ]}
      >
        <Switch
          checkedChildren="单时刻"
          unCheckedChildren="时间范围"
        />
      </Form.Item>
      {dateTimeType ? (
        <Form.Item
          name="timePicker"
          label="时间选择"
          rules={[
            {
              required: true,
              message: '时间不能为空!',
            },
          ]}
        >
          <DatePicker
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            style={{ maxWidth: '220px' }}
            allowClear={false}
          />
        </Form.Item>
      ) : (
        <Form.Item
          name="timeRange"
          label="时间选择"
          rules={[
            {
              required: true,
              message: '时间不能为空!',
            },
          ]}
        >
          <DatePicker.RangePicker
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            style={{ maxWidth: '320px' }}
            allowClear={false}
            allowEmpty={[false, false]}
          />
        </Form.Item>
      )}
      <Form.Item
        label="标注类型"
        name="type"
        rules={[
          {
            required: true,
            message: '标注类型不能为空!',
          },
        ]}
      >
        <Select
          options={schemeTypeOptions}
          style={{ maxWidth: '220px' }}
          placeholder="请选择标注类型"
        />
      </Form.Item>
      <Form.Item
        label="描述"
        name="description"
      >
        <Input.TextArea
          placeholder="请输入描述"
          showCount
          rows={5}
        />
      </Form.Item>
    </Form>
  );
}
