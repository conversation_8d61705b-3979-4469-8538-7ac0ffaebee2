/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { makeObjectId } from '@waterdesk/data/object-item';
import { ChartProperties } from '@waterdesk/data/property/property-info';
import { Button, Col, Form, FormInstance, Row, Space, Tag } from 'antd';
import { Key } from 'react';
import { ObjectChartFormValues } from 'src/components/charts/object-chart/object-chart-content';
import { DatePickerType } from '../multi-date-picker';

interface Props {
  curDb: Database;
  form: FormInstance<any>;
  chartProperties?: Array<ChartProperties>;
  setCheckedKeys?: (checkedKeys: string[]) => void;
  saveAs?: (value: ObjectChartFormValues) => void;
}

export default function ScadaSelectedCollapse(props: Props) {
  const { chartProperties, curDb, setCheckedKeys, form, saveAs } = props;

  const multiDates = Form.useWatch('dateMultiSelect', form);

  const onCloseDate = (closeDate: string) => {
    const newDates = multiDates.dates.filter(
      (date: string) => date !== closeDate,
    );
    form.setFieldValue('dateMultiSelect', {
      dates: newDates,
      dateType: multiDates.dateType,
    });
  };

  const onClose = (key: Key) => {
    const newSelectedKeys: string[] = [];
    chartProperties?.forEach((item) => {
      if (item.key !== key) {
        newSelectedKeys.push(item.key as string);
      }
    });
    setCheckedKeys?.(newSelectedKeys);
  };
  const getSelectedMultiDates = () =>
    multiDates?.dates.map((date: string) => {
      if (date) {
        return (
          <Col key={date}>
            <Tag
              closable
              onClose={() => onCloseDate(date)}
            >
              {date}
            </Tag>
          </Col>
        );
      }
      return null;
    });
  const getSelectedTag = () =>
    chartProperties?.map(({ key, pname, ptype, title }) => {
      if (!pname || !ptype) return null;
      const deviceId = makeObjectId(ptype, pname);
      const deviceInfo = curDb.getDeviceById(deviceId);
      if (deviceInfo) {
        return (
          <Col key={key}>
            <Tag
              closable
              onClose={() => onClose(key)}
            >
              {deviceInfo.title + title}
            </Tag>
          </Col>
        );
      }
      return null;
    });

  const onCheckedClear = () => {
    setCheckedKeys?.([]);
    form.setFieldValue('dateMultiSelect', {
      dates: [],
      dateType: DatePickerType.DATE,
    });
  };

  return (
    <div>
      <Space>
        {multiDates?.dates && multiDates?.dates.length > 0 ? (
          <Row gutter={[8, 8]}>
            选择日期({multiDates?.dates.length}): {getSelectedMultiDates()}
          </Row>
        ) : null}
        {chartProperties ? (
          <Row gutter={[8, 8]}>
            选择对象({chartProperties.length}): {getSelectedTag()}
          </Row>
        ) : null}
        {setCheckedKeys ? (
          <Button
            type="link"
            onClick={onCheckedClear}
          >
            清空
          </Button>
        ) : null}
        {saveAs ? (
          <Button
            type="link"
            onClick={() => saveAs(form.getFieldsValue())}
          >
            添加到收藏夹
          </Button>
        ) : null}
      </Space>
    </div>
  );
}
