/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SchemeType } from '@waterdesk/data/scheme-config';
import { Form, FormInstance, Input, Select, Switch } from 'antd';

export interface ScadaFavoritesFormValues {
  title: string;
  note: string;
  schemeType: SchemeType;
  share: boolean;
  isFixedTime: boolean;
}

interface Props {
  form: FormInstance<ScadaFavoritesFormValues>;
  schemeTypeOptions?: {
    label: string;
    value: string;
  }[];
}

export default function ScadaFavoritesForm({ form, schemeTypeOptions }: Props) {
  return (
    <Form
      name="scada-favorites-form"
      layout="horizontal"
      form={form}
      autoComplete="off"
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 20 }}
      initialValues={{
        isFixedTime: true,
      }}
    >
      <Form.Item
        label="收藏夹名称"
        name="title"
        rules={[{ required: true, message: '不能为空' }]}
      >
        <Input maxLength={60} />
      </Form.Item>
      <Form.Item
        label="收藏类型"
        name="schemeType"
        initialValue={SchemeType.MONITORING_EXCEPTION}
      >
        <Select options={schemeTypeOptions} />
      </Form.Item>
      <Form.Item
        label="备注"
        name="note"
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="isFixedTime"
        valuePropName="checked"
        label="固定曲线时间"
      >
        <Switch
          checkedChildren="是"
          unCheckedChildren="否"
        />
      </Form.Item>
      <Form.Item
        name="share"
        valuePropName="checked"
        label="是否共享"
      >
        <Switch
          checkedChildren="是"
          unCheckedChildren="否"
        />
      </Form.Item>
    </Form>
  );
}
