/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  getSchemeTypeDataTitle,
  ScadaFavorite,
  SchemeData,
  SchemeTypeData,
} from '@waterdesk/data/scheme-config';
import { Descriptions, Popconfirm, Space, Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import { useState } from 'react';
import LinkSmallButton from '../common/link-button';
import LinkSpan from '../common/link-span';
import { AntdTableProps } from '../common/table';
import EditScadaSolutionModal, {
  EditSchemeInfo,
} from './edit-scada-solution-modal';

interface Props {
  tableProps: AntdTableProps<SchemeData, {}>;
  onWatch: (scadaSolution: ScadaFavorite, record: SchemeData) => void;
  onDelete: (id: string) => void;
  onEdit: (editInfo: EditSchemeInfo) => Promise<boolean>;
  schemeType: SchemeTypeData;
}

const getExpandedRowRender = (data: SchemeData) => (
  <Descriptions
    title={null}
    column={1}
  >
    <Descriptions.Item label="备注">{data.remark}</Descriptions.Item>
    <Descriptions.Item label="固定曲线时间">
      {data.isFixedTime ? '是' : '否'}
    </Descriptions.Item>
    <Descriptions.Item label="是否共享">
      {data.schemeShare ? '是' : '否'}
    </Descriptions.Item>
  </Descriptions>
);

export default function ScadaSolutionList(props: Props) {
  const { tableProps, onWatch, onDelete, schemeType, onEdit } = props;

  const [editRecord, setEditRecord] = useState<SchemeData | undefined>(
    undefined,
  );
  const [open, setOpen] = useState<boolean>(false);

  const columns: ColumnType<SchemeData>[] = [
    {
      title: '名称',
      key: 'schemeName',
      dataIndex: 'schemeName',
      ellipsis: true,
      render: (text, record) => (
        <LinkSpan
          title={text}
          onClick={() => onWatch(JSON.parse(record.configValue), record)}
        >
          {text}
        </LinkSpan>
      ),
    },
    {
      title: '类型',
      key: 'schemeType',
      dataIndex: 'schemeType',
      align: 'center',
      width: 85,
      ellipsis: true,
      render: (value) => getSchemeTypeDataTitle(schemeType, value),
    },
    {
      title: '操作',
      key: 'operation',
      dataIndex: 'operation',
      align: 'center',
      width: 60,
      render: (_, record) => (
        <Space>
          {record.createdByQueryUser ? (
            <LinkSmallButton
              type="link"
              title="编辑"
              onClick={() => {
                setOpen(true);
                setEditRecord(record);
              }}
            >
              <EditOutlined />
            </LinkSmallButton>
          ) : null}

          <Popconfirm
            title="提示"
            description="确定删除该收藏?"
            onConfirm={() => onDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <LinkSmallButton
              type="link"
              danger
              title="删除"
            >
              <DeleteOutlined />
            </LinkSmallButton>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleOnOk = async (editInfo: EditSchemeInfo) => {
    const res = await onEdit(editInfo);
    if (res) setOpen(false);
  };

  return (
    <>
      <Table
        {...tableProps}
        size="small"
        columns={columns}
        rowKey="id"
        expandable={{
          expandedRowRender: getExpandedRowRender,
          columnWidth: 48,
        }}
        pagination={false}
      />
      <EditScadaSolutionModal
        open={open}
        onClose={() => setOpen(false)}
        onOk={handleOnOk}
        favoriteInfo={editRecord}
        schemeType={schemeType}
      />
    </>
  );
}
