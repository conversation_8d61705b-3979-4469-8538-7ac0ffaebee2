/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  LineChartOutlined,
  OneToOneOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { Radio } from 'antd';
import useMergedState from 'rc-util/es/hooks/useMergedState';

/**
 * chart： 仅显示曲线
 *
 * table 仅显示表格
 *
 * both 都显示
 */
export enum ChartMode {
  CHART = 'chart',
  TABLE = 'table',
  BOTH = 'both',
}

interface Props {
  value: ChartMode;
  onChange?: (value: ChartMode) => void;
}

export const SwitchChartModeButton = (props: Props) => {
  const { value, onChange } = props;

  const [active, setActive] = useMergedState(ChartMode.CHART, {
    value,
    onChange,
  });

  const handleOnChange = (value: ChartMode) => {
    setActive(value);
  };

  return (
    <Radio.Group
      size="small"
      buttonStyle="solid"
      value={active}
      onChange={(e) => handleOnChange(e.target.value)}
    >
      <Radio.Button
        title="全部显示"
        value={ChartMode.BOTH}
      >
        <OneToOneOutlined title="全部显示" />
      </Radio.Button>
      <Radio.Button
        title="仅显示曲线"
        value={ChartMode.CHART}
      >
        <LineChartOutlined title="仅显示曲线" />
      </Radio.Button>
      <Radio.Button
        title="仅显示表格"
        value={ChartMode.TABLE}
      >
        <TableOutlined title="仅显示表格" />
      </Radio.Button>
    </Radio.Group>
  );
};
