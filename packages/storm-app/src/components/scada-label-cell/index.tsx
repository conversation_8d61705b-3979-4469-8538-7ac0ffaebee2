/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { PumpStateColor } from '@waterdesk/data/app-config';
import { PumpInfo } from '@waterdesk/data/device';
import { DeviceTimeData } from '@waterdesk/data/device-time-data';
import { formatPumpData } from '@waterdesk/data/pump-status';
import { Avatar, Typography } from 'antd';
import dayjs from 'dayjs';
import { CSSProperties, ReactNode, useEffect, useState } from 'react';
import { DefaultTheme } from 'styled-components';

const { Text } = Typography;
interface Props {
  data: DeviceTimeData | undefined;
  dataType: 'scadaData' | 'simulationData' | 'pumpData';
  currentTime: string | undefined;
  dataTime: string | undefined;
  pumpStateColor: PumpStateColor | undefined;
  pumpInfo?: PumpInfo;
  style?: React.CSSProperties;
  title?: string;
  oname?: string;
  defaultColor?: CSSProperties['color'];
}

export default function ScadaLabelCell(props: Readonly<Props>) {
  const {
    data,
    dataType,
    currentTime,
    dataTime,
    pumpStateColor,
    pumpInfo,
    style,
    title,
    oname,
    defaultColor = '#000',
  } = props;

  const { token } = useToken();

  const [tooltip, setTooltip] = useState<string | undefined>();
  const [content, setContent] = useState<ReactNode>();
  const [textColor, setTextColor] = useState<string>(defaultColor);

  function getPumpItem(
    pumpInfo: PumpInfo,
    value: string | number | undefined,
  ): ReactNode {
    let pumpColor: PumpStateColor = {
      variable: 'green',
      closed: 'red',
      fixed: 'green',
    };

    if (pumpStateColor) {
      pumpColor = pumpStateColor;
    }

    if (pumpInfo.variable) {
      const valueData = formatPumpData(
        value,
        pumpInfo.minFrequency,
        pumpInfo.maxFrequency,
      );
      // 此处是故意写死，只用于前端展示
      return (
        <Avatar
          size={16}
          style={{
            backgroundColor:
              valueData > 20 ? pumpColor.variable : pumpColor.closed,
          }}
          gap={2}
        >
          <span style={{ fontSize: 10 }}>
            {valueData > 20 ? Number(valueData).toFixed(0) : undefined}
          </span>
        </Avatar>
      );
    }
    return (
      <Avatar
        size={16}
        style={{
          backgroundColor: value ? pumpColor.fixed : pumpColor.closed,
        }}
      />
    );
  }

  const getTimeDiff = (
    currentTime: string | undefined,
    testTime: string | undefined,
  ): number | undefined => {
    if (
      currentTime === undefined ||
      currentTime === '' ||
      testTime === undefined ||
      testTime === ''
    )
      return undefined;
    return dayjs(currentTime).diff(testTime, 'minute');
  };

  const getTextColor = (
    timeDiff: number | undefined,
    token: DefaultTheme,
  ): string | undefined => {
    if (timeDiff === undefined) return defaultColor;
    if (timeDiff > 60) return token.colorErrorText;
    if (timeDiff > 5) return token.colorWarningText;
    return defaultColor;
  };

  const getTimeDiffText = (
    timeDiff: number | undefined,
    testTime: string | undefined,
  ): string | undefined => {
    if (timeDiff === undefined || testTime === undefined) return undefined;
    if (timeDiff > 60) return `${Math.floor(timeDiff / 60)}小时前, ${testTime}`;
    if (timeDiff > 5) return `${timeDiff}分钟前, ${testTime}`;
    return dayjs(testTime).format('YYYY-MM-DD HH:mm');
  };

  const getTooltipContent = (
    timeDiff: number | undefined,
    testTime: string | undefined,
    state: boolean,
  ): string | undefined => {
    const timeDiffText = getTimeDiffText(timeDiff, testTime);
    if (pumpInfo) {
      if (pumpInfo.variable) {
        const valueData = formatPumpData(
          data?.originalValue,
          pumpInfo.minFrequency,
          pumpInfo.maxFrequency,
        );
        if (valueData > 20)
          return `${pumpInfo.title}(变频): ${
            data?.value ?? valueData
          }\n${timeDiffText}`;
        return `${pumpInfo.title}(变频): 关\n${timeDiffText}`;
      }

      if (data?.originalValue)
        return `${pumpInfo.title}(工频): 开\n${timeDiffText}`;
      return `${pumpInfo.title}(工频): 关\n${timeDiffText}`;
    }

    return `${oname ?? '-'}\n${title ?? '-'}${!state ? '(坏)' : ''}${
      timeDiffText ? `\n${timeDiffText}` : ''
    }`;
  };

  useEffect(() => {
    const timeDiff = getTimeDiff(currentTime, dataTime);
    setTextColor(getTextColor(timeDiff, token) ?? defaultColor);
    setTooltip(
      getTooltipContent(timeDiff, dataTime, data?.latestDeviceState !== false),
    );

    switch (dataType) {
      case 'scadaData':
        if (data?.value !== undefined) setContent(`${data.value}`);
        else setContent('-');
        break;
      case 'pumpData':
        if (data?.value !== undefined && pumpInfo)
          setContent(getPumpItem(pumpInfo, data.originalValue));
        else setContent('-');
        break;
      default:
        if (data?.value !== undefined) setContent(`(${data.value})`);
        else setContent('');
        break;
    }
  }, [data, dataType, currentTime, dataTime, token]);

  return (
    <Text
      title={tooltip}
      style={
        data?.latestDeviceState === false
          ? {
              fontWeight: 600,
              color: textColor,
              ...style,
              backgroundColor: token.colorBgTextActive,
              padding: '0 4px',
            }
          : {
              fontWeight: 600,
              color: textColor,
              ...style,
            }
      }
    >
      {content}
    </Text>
  );
}
