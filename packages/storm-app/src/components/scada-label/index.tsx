/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PumpStateColor } from '@waterdesk/data/app-config';
import { PumpInfo } from '@waterdesk/data/device';
import {
  DeviceTimeData,
  ScadaModelTimeData,
} from '@waterdesk/data/device-time-data';
import { splitObjectId } from '@waterdesk/data/object-item';
import { CSSProperties, useCallback, useEffect, useState } from 'react';
import ScadaLabelCell from 'src/components/scada-label-cell';

interface Props {
  id: string;
  title?: string;
  dataType: 'scadaData' | 'simulationData' | 'pumpData';
  pumpInfo?: PumpInfo;
  pumpStateColor?: PumpStateColor;
  style?: React.CSSProperties;
  currentData?: ScadaModelTimeData;
  getScadaDataCell?: (id: string) => ScadaModelTimeData | undefined;
  currentTimeDataChanged?: string;
  defaultColor?: CSSProperties['color'];
}

export default function ScadaLabel(props: Readonly<Props>) {
  const {
    id,
    dataType,
    pumpInfo,
    pumpStateColor,
    style,
    title,
    currentData,
    defaultColor,
    getScadaDataCell,
    currentTimeDataChanged,
  } = props;
  const [data, setData] = useState<DeviceTimeData | undefined>();
  const [dataTime, setDataTime] = useState<string | undefined>();

  const updateScadaDataCell = useCallback(() => {
    const currentTimeData = getScadaDataCell?.(id);

    if (dataType === 'simulationData') {
      setData(currentTimeData?.simulationData);
      setDataTime(currentTimeData?.simulationData?.time);
    } else {
      setData(currentTimeData?.scadaData);
      setDataTime(currentTimeData?.scadaData?.time);
    }
  }, [id]);

  useEffect(() => {
    if (dataType === 'simulationData') {
      setData(currentData?.simulationData);
      setDataTime(currentData?.simulationData?.time);
    } else {
      setData(currentData?.scadaData);
      setDataTime(currentData?.scadaData?.time);
    }
  }, [currentData]);

  useEffect(() => {
    updateScadaDataCell();
  }, [currentTimeDataChanged]);

  return (
    <ScadaLabelCell
      defaultColor={defaultColor}
      data={data}
      pumpInfo={pumpInfo}
      dataType={dataType}
      currentTime={currentTimeDataChanged}
      dataTime={dataTime}
      pumpStateColor={pumpStateColor}
      style={style}
      title={title}
      oname={splitObjectId(id)[1]}
    />
  );
}
