/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import Database from '@waterdesk/data/database';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import {
  SearchElementInfo,
  SearchRoadInfo,
} from '@waterdesk/data/search-element-info';
import { InputRef, List, NavBar, SearchBar } from 'antd-mobile';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { SearchBoxWrapper } from './style';

export enum SearchBoxState {
  Empty,
  ShowResult,
  ShowHistory,
}

interface Props {
  visible: boolean;
  searchedElements: Array<SearchElementInfo | SearchRoadInfo>;
  database: Database;
  handleSearchObject: (value: string, saveHistory: boolean) => Promise<void>;
  handleClickSearchObject: (
    item: SearchElementInfo,
    searchKey?: string,
  ) => void;
  onBack: () => void;
}

export default function SearchBox(props: Props) {
  const {
    visible,
    searchedElements,
    database,
    onBack,
    handleSearchObject,
    handleClickSearchObject,
  } = props;

  const inputRef = useRef<InputRef>(null);
  const [searchState, setSearchState] = useState<SearchBoxState>(
    SearchBoxState.Empty,
  );
  const { token } = useToken();

  const onSearchObject = async (value: string, saveHistory: boolean) => {
    await handleSearchObject(value, saveHistory);
    if (value === '') {
      setSearchState(SearchBoxState.ShowHistory);
    } else {
      setSearchState(SearchBoxState.ShowResult);
    }
  };

  const onFocus = (value: string) => {
    const inputValue = value;
    inputRef.current?.focus();
    if (inputValue === '') {
      setSearchState(SearchBoxState.ShowHistory);
    } else {
      setSearchState(SearchBoxState.ShowResult);
    }
  };

  // const onBlur = () => {
  //   setSearchState(SearchBoxState.Empty);
  // };

  const getElementTitle = (item: SearchElementInfo): string => {
    let objectTitle: string = item.otype;
    let propertyTitle: string = item.vprop;
    const propertyInfo: PropertyInfo | undefined = database.propertyInfos.get(
      item.otype,
    );

    if (propertyInfo) {
      objectTitle = propertyInfo.title;
      const vpropTitle = propertyInfo.getPropertyTitle(item.vprop);
      if (vpropTitle === undefined)
        propertyTitle = item.vprop === 'ONAME' ? 'ID' : item.vprop;
      else propertyTitle = vpropTitle;
    }

    if (item.title === item.value)
      return `${objectTitle} ${item.title} (${propertyTitle})`;
    return `${objectTitle} ${item.title} (${propertyTitle}:${item.value})`;
  };

  const getDisplayTitle = (item: SearchElementInfo | SearchRoadInfo): string =>
    getElementTitle(item as SearchElementInfo);

  const onClickSearchItem = (item: SearchElementInfo | SearchRoadInfo) => {
    setSearchState(SearchBoxState.Empty);
    handleClickSearchObject(
      item as SearchElementInfo,
      inputRef.current?.nativeElement?.value,
    );
    onBack();
  };

  useEffect(() => {
    if (visible) {
      onFocus('1');
    } else {
      // onBlur();
    }
  }, [visible]);

  return (
    <SearchBoxWrapper>
      <div className="content">
        <NavBar
          onBack={onBack}
          style={{
            margin: '8px 0',
            backgroundColor: token.colorBgContainer,
            '--border-bottom': `1px solid ${token.colorBorder}`,
          }}
        >
          <SearchBar
            ref={inputRef}
            placeholder="查找对象"
            onChange={debounce((value) => onSearchObject(value, false), 200)}
            onSearch={(value) => onSearchObject(value, true)}
            onFocus={(e) => onFocus(e.target.value)}
            // onBlur={onBlur}
            style={{ '--height': '40px' }}
          />
        </NavBar>
        {searchState === SearchBoxState.ShowResult && (
          <List className="search-result">
            {searchedElements.map(
              (item: SearchElementInfo | SearchRoadInfo, index: number) => {
                const key = item.title + index;
                return (
                  <List.Item
                    key={key}
                    onClick={() => {
                      onClickSearchItem(item);
                    }}
                  >
                    {getDisplayTitle(item)}
                  </List.Item>
                );
              },
            )}
          </List>
        )}
      </div>
    </SearchBoxWrapper>
  );
}
