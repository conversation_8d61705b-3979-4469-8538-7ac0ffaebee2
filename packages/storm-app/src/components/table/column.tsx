/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { FilterFilled, SearchOutlined } from '@ant-design/icons';
import { Button, Input, InputNumber, Space } from 'antd';
import { ColumnType } from 'antd/es/table';
import {
  FilterConfirmProps,
  FilterDropdownProps,
} from 'antd/lib/table/interface';

export const getColumnSearchProps = <T extends {}>(
  dataIndex: keyof T,
  customFilter?: (text: any, record: T) => string,
): Pick<ColumnType<T>, 'filterDropdown' | 'filterIcon' | 'onFilter'> => ({
  filterDropdown: (filterDropdownProps: FilterDropdownProps) => {
    const { setSelectedKeys, selectedKeys, confirm, clearFilters } =
      filterDropdownProps;
    const handleSearch = (confirm: (param?: FilterConfirmProps) => void) => {
      confirm();
    };

    const handleReset = (
      clearFilters: () => void,
      confirm: (param?: FilterConfirmProps) => void,
    ) => {
      clearFilters();
      confirm();
    };
    return (
      <div
        role="none"
        style={{ padding: 8 }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          placeholder="搜索"
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => handleSearch(confirm)}
          allowClear
          style={{ marginBottom: 8 }}
        />
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            borderTop: '1px solid #ccc',
            padding: '7px 8px 0',
          }}
        >
          <Button
            onClick={() => clearFilters && handleReset(clearFilters, confirm)}
            size="small"
            type="link"
          >
            重置
          </Button>
          <Button
            type="primary"
            onClick={() => handleSearch(confirm)}
            size="small"
          >
            确定
          </Button>
        </div>
      </div>
    );
  },
  filterIcon: (filtered: boolean) => (
    <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value, record) =>
    !!(customFilter?.(record[dataIndex], record) ?? record[dataIndex])
      ?.toString()
      .toLowerCase()
      .includes((value as string).toLowerCase()),
});

export const getColumnFilterRangeProps = <T extends {}>(
  dataIndex: keyof T,
  customFilter?: (text: any, record: T) => number,
): Pick<ColumnType<T>, 'filterDropdown' | 'filterIcon' | 'onFilter'> => ({
  filterDropdown: (filterDropdownProps: FilterDropdownProps) => {
    const { setSelectedKeys, selectedKeys, confirm, clearFilters } =
      filterDropdownProps;
    const handleSearch = (confirm: (param?: FilterConfirmProps) => void) => {
      confirm();
    };

    const handleReset = (
      clearFilters: () => void,
      confirm: (param?: FilterConfirmProps) => void,
    ) => {
      clearFilters();
      confirm();
    };
    const [min, max] = selectedKeys[0]?.toString()?.split('~') ?? [];

    return (
      <div
        role="none"
        style={{ padding: 8 }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <div style={{ display: 'flex', flexWrap: 'nowrap', marginBottom: 8 }}>
          <Space.Compact size="small">
            <InputNumber
              placeholder="最小值"
              value={min}
              onChange={(value) => setSelectedKeys([[value, max].join('~')])}
              onPressEnter={() => handleSearch(confirm)}
              style={{ flex: '1 1 auto' }}
            />
            <Input
              style={{
                width: 20,
                borderLeft: 0,
                borderRight: 0,
                pointerEvents: 'none',
              }}
              placeholder="~"
              disabled
            />
            <InputNumber
              placeholder="最大值"
              value={max}
              onChange={(value) => setSelectedKeys([[min, value].join('~')])}
              onPressEnter={() => handleSearch(confirm)}
              style={{ flex: '1 1 auto' }}
            />
          </Space.Compact>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            borderTop: '1px solid #ccc',
            padding: '7px 8px 0',
          }}
        >
          <Button
            onClick={() => clearFilters && handleReset(clearFilters, confirm)}
            size="small"
            type="link"
          >
            重置
          </Button>
          <Button
            type="primary"
            onClick={() => handleSearch(confirm)}
            size="small"
          >
            确定
          </Button>
        </div>
      </div>
    );
  },
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value, record) => {
    const [min, max] = value.toString().split('~');
    const formatMin = min === '' ? undefined : Number(min);
    const formatMax = max === '' ? undefined : Number(max);
    const formatValue = Number(
      customFilter?.(record[dataIndex], record) ?? record[dataIndex],
    );
    if (typeof formatMin === 'undefined' && typeof formatMax === 'number') {
      return formatValue <= formatMax;
    }

    if (typeof formatMax === 'undefined' && typeof formatMin === 'number') {
      return formatValue >= formatMin;
    }

    if (typeof formatMin === 'undefined' && typeof formatMax === 'undefined')
      return true;

    return (
      (formatValue >= formatMin! && formatValue <= formatMax!) ||
      (formatValue >= formatMax! && formatValue <= formatMin!)
    );
  },
});
