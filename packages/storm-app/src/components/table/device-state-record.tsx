/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeviceStateData } from '@waterdesk/data/device-time-data';
import { dateSorter } from '@waterdesk/data/utils';
import { Table, TableColumnsType, TableProps } from 'antd';
import dayjs from 'dayjs';
import LinkSpan from '../common/link-span';
import { getColumnSearchProps } from './column';

type DeviceStateRecordData = DeviceStateData;

interface Props {
  tableProps: TableProps<DeviceStateRecordData>;
  onLocate?: (record: DeviceStateRecordData) => void;
}

const DeviceStateRecord = (props: Props) => {
  const { tableProps, onLocate } = props;
  const columns: TableColumnsType<DeviceStateRecordData> = [
    {
      title: '序号',
      render: (_, __, index) => `${index + 1}`,
      width: 60,
    },
    {
      title: '类型',
      key: 'typeTitle',
      dataIndex: 'typeTitle',
      width: 80,
      ...getColumnSearchProps('typeTitle'),
    },
    {
      title: '名称',
      key: 'title',
      dataIndex: 'title',
      ellipsis: true,
      render: (_, record) =>
        record.shape ? (
          <LinkSpan
            key={record.id}
            onClick={() => onLocate?.(record)}
          >
            {record.title}
          </LinkSpan>
        ) : (
          record.title
        ),
      ...getColumnSearchProps('title'),
    },
    {
      title: 'ONAME',
      key: 'oname',
      dataIndex: 'oname',
      ellipsis: true,
      width: 120,
      ...getColumnSearchProps('oname'),
    },
    {
      title: '变更后状态',
      key: 'state',
      dataIndex: 'state',
      width: 120,
      render: (state) => (state === false ? '坏' : '好'),
      filters: [
        {
          text: '坏',
          value: false,
        },
        {
          text: '好',
          value: true,
        },
      ],
      onFilter: (value, record) => {
        if (value === true) return record.state !== false;
        return record.state === value;
      },
    },
    {
      title: '修改时间',
      key: 'otime',
      dataIndex: 'otime',
      ellipsis: true,
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.otime, b.otime),
      defaultSortOrder: 'descend',
    },
    {
      title: '入库时间',
      key: 'stime',
      dataIndex: 'stime',
      ellipsis: true,
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.stime, b.stime),
    },
    {
      title: '来源',
      key: 'source',
      dataIndex: 'source',
      ellipsis: true,
      width: 150,
      ...getColumnSearchProps('source'),
    },
    {
      title: '修改人',
      key: 'creator',
      dataIndex: 'creator',
      ellipsis: true,
      width: 80,
      ...getColumnSearchProps('creator'),
    },
    {
      title: '备注',
      key: 'note',
      dataIndex: 'note',
      ellipsis: true,
      ...getColumnSearchProps('note'),
    },
  ];
  return (
    <Table<DeviceStateRecordData>
      size="small"
      rowKey="key"
      columns={columns}
      {...tableProps}
    />
  );
};

DeviceStateRecord.displayName = 'DeviceStateRecord';

export default DeviceStateRecord;
