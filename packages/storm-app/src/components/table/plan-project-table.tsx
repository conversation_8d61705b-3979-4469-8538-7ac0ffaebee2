/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ProColumns } from '@ant-design/pro-components';
import { renderRowNumber } from '@waterdesk/data/form';
import {
  getPlanProjectTypeTitle,
  PlanProjectList,
  PlanProjectType,
} from '@waterdesk/data/plan-project';
import { dateSorter } from '@waterdesk/data/utils';
import { Table, TableColumnsType, TableProps } from 'antd';
import dayjs from 'dayjs';

export const generatePlanProjectColumns = (
  current: number,
  pageSize: number,
  planProjectType?: PlanProjectType,
  extraColumns?: TableColumnsType<PlanProjectList>,
): TableColumnsType<PlanProjectList> => [
  {
    title: '',
    dataIndex: 'index',
    key: 'index',
    width: 40,
    render: renderRowNumber(current, pageSize),
  },
  {
    title: '工程类型',
    dataIndex: 'projectType',
    key: 'projectType',
    ellipsis: true,
    render: (value) => getPlanProjectTypeTitle(planProjectType ?? [], value),
    width: 80,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 140,
    ellipsis: true,
    render: (value) =>
      value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    sorter: (a, b) => dateSorter(a.startTime, b.startTime),
  },
  {
    title: '工程名称',
    dataIndex: 'projectName',
    key: 'projectName',
    ellipsis: true,
    width: 200,
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 100,
    render: (text) => text ?? '-',
  },
  {
    title: '创建人',
    dataIndex: 'userName',
    key: 'userName',
    width: 80,
    render: (text) => text ?? '-',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 140,
    ellipsis: true,
    render: (value) =>
      value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    sorter: (a, b) => dateSorter(a.createTime, b.createTime),
    defaultSortOrder: 'descend',
  },
  ...(extraColumns ?? []),
];

export const generatePlanProjectProTableColumns = (
  current: number,
  pageSize: number,
  planProjectType?: PlanProjectType,
  extraColumns?: ProColumns<PlanProjectList>[],
): ProColumns<PlanProjectList>[] => [
  {
    title: '',
    dataIndex: 'index',
    key: 'index',
    width: 40,
    render: renderRowNumber(current, pageSize),
  },
  {
    title: '工程类型',
    dataIndex: 'projectType',
    key: 'projectType',
    ellipsis: true,
    renderText: (value) =>
      getPlanProjectTypeTitle(planProjectType ?? [], value),
    width: 80,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 140,
    ellipsis: true,
    renderText: (value) =>
      value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    sorter: (a, b) => dateSorter(a.startTime, b.startTime),
  },
  {
    title: '工程名称',
    dataIndex: 'projectName',
    key: 'projectName',
    ellipsis: true,
    width: 200,
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 100,
    render: (text) => text ?? '-',
  },
  {
    title: '创建人',
    dataIndex: 'userName',
    key: 'userName',
    width: 80,
    render: (text) => text ?? '-',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 140,
    ellipsis: true,
    renderText: (value) =>
      value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
    sorter: (a, b) => dateSorter(a.createTime, b.createTime),
    defaultSortOrder: 'descend',
  },
  ...(extraColumns ?? []),
];

interface PlanProjectTableProps {
  planProjectType?: PlanProjectType;
  current: number;
  pageSize: number;
  tableProps?: TableProps<PlanProjectList>;
  extraColumns?: TableColumnsType<PlanProjectList>;
  style?: React.CSSProperties;
}

const PlanProjectTable: React.FC<PlanProjectTableProps> = ({
  planProjectType,
  current,
  pageSize,
  tableProps,
  extraColumns,
  style,
}) => {
  const columns: TableColumnsType<PlanProjectList> = generatePlanProjectColumns(
    current,
    pageSize,
    planProjectType,
    extraColumns,
  );

  return (
    <Table<PlanProjectList>
      {...tableProps}
      columns={columns}
      rowKey="projectId"
      size="small"
      style={style}
    />
  );
};

export default PlanProjectTable;
