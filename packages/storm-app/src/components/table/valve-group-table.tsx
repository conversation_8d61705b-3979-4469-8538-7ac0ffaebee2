/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  BulbOutlined,
  EditOutlined,
  FundProjectionScreenOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { dateSorter } from '@waterdesk/data/utils';
import {
  containsShapeInValveGroup,
  displayDuration,
  formatSdval,
  getValveOperationGroupTitle,
  getValveOperationValueData,
  ValveOperationGroup,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import {
  Button,
  Space,
  Table,
  TableColumnsType,
  TableProps,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import LinkSmallButton from '../common/link-button';
import LinkSpan from '../common/link-span';
import { TableType } from '../common/tables';
import HSTable from '../common/tables/hs-table';
import { getColumnFilterRangeProps, getColumnSearchProps } from './column';

const { Text } = Typography;

interface ValveTableProps {
  valveOperationGroups: ValveOperationGroup[];
  loading?: boolean;
  style?: React.CSSProperties;
  tableProps?: TableProps<ValveOperationGroup>;
  onameButton?: React.FC;
  onHighlightGroup?: (group: ValveOperationGroup) => void;
  onValveAnalysis?: (group: ValveOperationGroup) => void;
  onViewImpactDetails?: (group: ValveOperationGroup) => void;
  setSelectedRow?: (record: ValveOperationValue) => void;
  onLocate?: (locateObj: {
    otype: string;
    oname: string;
    shape: string;
  }) => void;
  onHover?: (
    locateObj:
      | {
          otype: string;
          oname: string;
          shape: string;
        }
      | undefined,
  ) => void;
  extraColumns?: TableColumnsType<ValveOperationGroup>;
  setScrollY?: boolean;
  height?: number;
}

const ValveGroupTable: React.FC<ValveTableProps> = ({
  valveOperationGroups,
  loading,
  tableProps,
  extraColumns,
  style,
  setSelectedRow,
  onHighlightGroup,
  onLocate,
  onHover,
  onValveAnalysis,
  onViewImpactDetails,
  setScrollY = true,
  height = 300,
}) => {
  const handleHighlightGroup = (group: ValveOperationGroup) => {
    onHighlightGroup?.(group);
  };

  const handleToValveAnalysis = (group: ValveOperationGroup) => {
    onValveAnalysis?.(group);
  };

  const getStatusText = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'finished':
        return '结束';
      case 'pending':
        return '进行中';
      default:
        return status;
    }
  };

  const columns: TableColumnsType<ValveOperationGroup> = [
    {
      key: 'index',
      dataIndex: 'index',
      width: 35,
      ellipsis: true,
      render: (_, __, index) => `${index + 1}`,
    },
    {
      title: '名称',
      key: 'title',
      dataIndex: 'title',
      width: 150,
      ellipsis: true,
      render: (_, record) => getValveOperationGroupTitle(record),
      ...getColumnSearchProps('title'),
    },
    {
      title: '阀门操作',
      key: 'valve_operation',
      ellipsis: true,
      width: 150,
      render: (_, record: ValveOperationGroup) => (
        <>
          {onHighlightGroup && (
            <Tooltip
              title={
                containsShapeInValveGroup(record)
                  ? '高亮相关阀门'
                  : '阀门不在模型中'
              }
            >
              <Button
                size="small"
                type={containsShapeInValveGroup(record) ? 'link' : 'text'}
                icon={<BulbOutlined />}
                onClick={() => handleHighlightGroup(record)}
              />
            </Tooltip>
          )}
          <Text>
            {`${record.valveCount}个阀门 ${record.operationCount}次操作  `}
          </Text>
        </>
      ),
      sorter: (a, b) => a.operationCount - b.operationCount,
    },
    {
      title: '开始时间',
      key: 'startTime',
      dataIndex: 'startTime',
      ellipsis: true,
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.startTime, b.startTime),
      defaultSortOrder: 'descend',
    },
    {
      title: '结束时间',
      key: 'endTime',
      dataIndex: 'endTime',
      ellipsis: true,
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.endTime, b.endTime),
    },
    {
      title: '总时长',
      key: 'duration',
      dataIndex: 'duration',
      width: 100,
      ellipsis: true,
      render: (duration: number) => displayDuration(duration),
      sorter: (a, b) => a.duration - b.duration,
      ...getColumnFilterRangeProps('duration'),
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      ellipsis: true,
      width: 90,
      render: (status: string) => {
        if (status.toLocaleLowerCase() === 'finished')
          return <Text type="success">结束</Text>;
        return <Text type="warning">进行中</Text>;
      },
      sorter: (a, b) => a.status.localeCompare(b.status),
      ...getColumnSearchProps('status', getStatusText),
    },
    {
      title: '所属部门',
      key: 'department',
      dataIndex: 'department',
      ellipsis: true,
      width: 120,
      sorter: (a, b) => a.department.localeCompare(b.department),
      ...getColumnSearchProps('department'),
    },
    ...(!onValveAnalysis
      ? []
      : [
          {
            title: '分析',
            key: 'analysis',
            width: 50,
            render: (_: string, record: ValveOperationGroup) => (
              <LinkSmallButton
                icon={<FundProjectionScreenOutlined />}
                tooltipTitle="影响范围快速分析"
                onClick={() => handleToValveAnalysis(record)}
              />
            ),
          },
        ]),
    ...(!onViewImpactDetails
      ? [
          {
            title: '影响范围',
            key: 'impact',
            width: 120,
            dataIndex: 'note',
            ellipsis: true,
            ...getColumnSearchProps('note'),
          },
        ]
      : [
          {
            title: '影响范围',
            key: 'impact',
            width: 120,
            dataIndex: 'note',
            ellipsis: true,
            render: (text: string, record: ValveOperationGroup) => (
              <>
                {text && text.trim() !== '' ? (
                  <LinkSmallButton
                    icon={<UnorderedListOutlined />}
                    tooltipTitle="查看影响详情"
                    onClick={() => onViewImpactDetails?.(record)}
                  />
                ) : null}
                <Tooltip title={text}>{text}</Tooltip>
              </>
            ),
            ...getColumnSearchProps('note'),
          },
        ]),
    ...(extraColumns ?? []),
  ];

  const handleSelectedRow = (record: ValveOperationValue) => {
    setSelectedRow?.(record);
  };

  const handleLocateValve = (record: ValveOperationValue) => {
    const { oname, otype, shape } = record;
    onLocate?.({
      otype,
      oname,
      shape,
    });
  };

  const expandedRowRender = (record: ValveOperationGroup) => {
    const detailsColumns: TableColumnsType<ValveOperationValue> = [
      {
        key: 'index',
        ellipsis: true,
        render: (_, __, index) => `${index + 1}`,
        width: 35,
      },
      {
        title: '时间',
        key: 'otime',
        dataIndex: 'otime',
        width: 120,
        render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
        sorter: (a, b) => dateSorter(a.otime, b.otime),
        defaultSortOrder: 'descend',
      },
      {
        title: '阀门标识码',
        key: 'oname',
        dataIndex: 'oname',
        width: 120,
        ellipsis: true,
        render: (value, record) =>
          record.shape ? (
            <LinkSpan
              onClick={() => handleLocateValve(record)}
              onMouseOver={() => onHover?.(record)}
              onMouseOut={() => onHover?.(undefined)}
            >
              {value}
            </LinkSpan>
          ) : (
            value
          ),
        ...getColumnSearchProps('oname'),
      },
      {
        title: '变更后状态',
        key: 'sdval',
        dataIndex: 'sdval',
        width: 120,
        ellipsis: true,
        render: (value) => formatSdval(value),
        ...getColumnSearchProps('sdval'),
      },
      {
        title: '口径(mm)',
        key: 'diameter',
        dataIndex: 'diameter',
        width: 120,
        ellipsis: true,
        sorter: (a, b) => Number(a.diameter) - Number(b.diameter),
        ...getColumnSearchProps('diameter'),
      },
      {
        title: '来源',
        key: 'source',
        dataIndex: 'source',
        width: 80,
        ellipsis: true,
        ...getColumnSearchProps('source'),
      },
      {
        title: '备注',
        key: 'note',
        dataIndex: 'note',
        ellipsis: true,
        width: 120,
        ...getColumnSearchProps('note'),
      },
      {
        title: '所属部门',
        key: 'department',
        dataIndex: 'department',
        ellipsis: true,
        width: 120,
        ...getColumnSearchProps('department'),
      },
      {
        title: '操作人员',
        key: 'createUser',
        dataIndex: 'createUser',
        ellipsis: true,
        width: 100,
        ...getColumnSearchProps('createUser'),
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'type',
        ellipsis: true,
        width: 70,
        ...getColumnSearchProps('type'),
      },
      {
        title: '在模型中',
        key: 'isModel',
        dataIndex: 'isModel',
        ellipsis: true,
        width: 100,
        render: (value: boolean) => (value ? '是' : '否'),
        filters: [
          { text: '是', value: true },
          { text: '否', value: false },
        ],
        onFilter: (value, record) => record.isModel === value,
      },
      {
        title: '创建时间',
        key: 'createTime',
        dataIndex: 'createTime',
        ellipsis: true,
        width: 150,
        render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
        sorter: (a, b) => dateSorter(a.createTime, b.createTime),
      },
      {
        title: '操作',
        key: 'option',
        width: 80,
        render: (_, record) => (
          <Space>
            {setSelectedRow && (
              <LinkSmallButton
                key="editable"
                tooltipTitle="查看阀门状态变更记录"
                onClick={() => handleSelectedRow(record)}
              >
                <EditOutlined />
              </LinkSmallButton>
            )}
          </Space>
        ),
      },
    ];

    return (
      <Table
        size="small"
        virtual
        scroll={{
          x: 800,
          y: 1000,
        }}
        columns={detailsColumns}
        dataSource={getValveOperationValueData(record.details)}
        rowKey="index"
        pagination={false}
      />
    );
  };

  if (setScrollY) {
    return (
      <HSTable
        {...tableProps}
        tableType={TableType.BOTTOM_INFINITE_TABLE}
        tableHeight={height}
        dataSource={valveOperationGroups}
        columns={columns}
        loading={loading}
        rowKey="groupID"
        style={style}
        expandable={{ expandedRowRender, columnWidth: 48 }}
        isColumnTitleDraggable
      />
    );
  }

  return (
    <HSTable
      {...tableProps}
      tableType={TableType.DRAWER_FIXED_TABLE}
      tableHeight={height}
      dataSource={valveOperationGroups}
      columns={columns}
      loading={loading}
      rowKey="groupID"
      style={style}
      expandable={{ expandedRowRender, columnWidth: 48 }}
    />
  );
};

export default ValveGroupTable;
