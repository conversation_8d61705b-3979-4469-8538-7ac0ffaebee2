/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { dateSorter } from '@waterdesk/data/utils';
import {
  formatSdval,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { Col, DatePicker, Row, Table, TableColumnsType } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { uniq } from 'lodash';
import { useMemo } from 'react';
import { getColumnSearchProps } from 'src/components/table/column';
import { rangePresets } from 'src/utils/tool';

const { RangePicker } = DatePicker;

interface Props {
  dateRanges?: RangePickerProps['value'];
  dateChange?: (dates: RangePickerProps['value']) => void;
  valveOperationList: ValveOperationValue[];
  loading?: boolean;
  setSelectedRow?: (selectedRows: ValveOperationValue[]) => void;
  setScrollY?: boolean;
}

export const ValveList = (props: Props) => {
  const {
    loading,
    valveOperationList,
    dateChange,
    dateRanges = [dayjs(), dayjs()],
    setSelectedRow,
    setScrollY = true,
  } = props;

  const handleDateChange = (dates: RangePickerProps['value']) => {
    if (dateChange && dates) dateChange(dates);
  };

  const valveCount = useMemo(
    () => uniq(valveOperationList.map((item) => item.oname)).length,
    [valveOperationList],
  );

  const columns: TableColumnsType<ValveOperationValue> = [
    {
      title: '序号',
      render: (_, __, index) => `${index + 1}`,
      width: 60,
    },
    {
      title: '时间',
      key: 'otime',
      dataIndex: 'otime',
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.otime, b.otime),
      defaultSortOrder: 'descend',
    },
    {
      title: '阀门标识码',
      key: 'oname',
      dataIndex: 'oname',
      ellipsis: true,
      width: 150,
      ...getColumnSearchProps('oname'),
    },
    {
      title: '变更后状态',
      key: 'sdval',
      dataIndex: 'sdval',
      ellipsis: true,
      width: 150,
      render: (value) => formatSdval(value),
      ...getColumnSearchProps('sdval'),
    },
    {
      title: '备注',
      key: 'note',
      dataIndex: 'note',
      ellipsis: true,
      width: 150,
      ...getColumnSearchProps('note'),
    },
    {
      title: '所属部门',
      key: 'department',
      dataIndex: 'department',
      ellipsis: true,
      width: 150,
      ...getColumnSearchProps('department'),
    },
    {
      title: '操作人员',
      key: 'createUser',
      dataIndex: 'createUser',
      ellipsis: true,
      width: 150,
      ...getColumnSearchProps('createUser'),
    },
    {
      title: '在模型中',
      key: 'isModel',
      dataIndex: 'isModel',
      ellipsis: true,
      width: 110,
      render: (value: boolean) => (value ? '是' : '否'),
      filters: [
        { text: '是', value: true },
        { text: '否', value: false },
      ],
      onFilter: (value, record) => record.isModel === value,
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime',
      width: 150,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.otime, b.otime),
    },
  ];
  return (
    <>
      <Row
        style={{ width: '100%' }}
        align="middle"
        justify="space-between"
      >
        <Col>
          <Row
            gutter={10}
            align="middle"
          >
            <Col>
              时间:
              <RangePicker
                allowClear={false}
                value={dateRanges}
                bordered={false}
                presets={rangePresets}
                onChange={handleDateChange}
              />
            </Col>
          </Row>
        </Col>
        <Col>
          <span>阀门数量:{valveCount}个,</span>
          <span>操作记录:{valveOperationList.length}条</span>
        </Col>
      </Row>
      <Table<ValveOperationValue>
        size="small"
        rowKey="id"
        dataSource={valveOperationList}
        columns={columns}
        loading={loading}
        pagination={false}
        rowSelection={{
          columnWidth: 50,
          onSelect: (
            _selectedRow: ValveOperationValue,
            _selected: boolean,
            selectedRows: ValveOperationValue[],
          ) => setSelectedRow?.(selectedRows),
          onSelectAll: (
            _selected: boolean,
            selectedRows: ValveOperationValue[],
          ) => setSelectedRow?.(selectedRows),
          getCheckboxProps: (record: ValveOperationValue) => ({
            disabled: record.eventId !== '',
          }),
        }}
        virtual
        scroll={{
          y: setScrollY ? 208 : undefined,
        }}
      />
    </>
  );
};
