/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { renderRowNumber } from '@waterdesk/data/form';
import { dateSorter } from '@waterdesk/data/utils';
import {
  formatSdval,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { Table, TableColumnsType, TableProps } from 'antd';
import dayjs from 'dayjs';
import { getColumnSearchProps } from './column';

interface ValveTableProps {
  current: number;
  pageSize: number;
  style?: React.CSSProperties;
  tableProps?: TableProps<ValveOperationValue>;
  extraColumns?: TableColumnsType<ValveOperationValue>;
  onameButton?: React.FC;
}

const ValveTable: React.FC<ValveTableProps> = ({
  current,
  pageSize,
  tableProps,
  extraColumns,
  style,
  onameButton,
}) => {
  const columns: TableColumnsType<ValveOperationValue> = [
    {
      title: '序号',
      width: 50,
      render: renderRowNumber(current, pageSize),
    },
    {
      title: '时间',
      key: 'otime',
      dataIndex: 'otime',
      width: 160,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.otime, b.otime),
      defaultSortOrder: 'descend',
    },
    {
      title: '阀门标识码',
      key: 'oname',
      dataIndex: 'oname',
      ellipsis: true,
      render: (value, record) =>
        record.shape && onameButton ? onameButton : value,
      ...getColumnSearchProps('oname'),
    },
    {
      title: '变更后状态',
      key: 'sdval',
      dataIndex: 'sdval',
      ellipsis: true,
      render: (value) => formatSdval(value),
      ...getColumnSearchProps('sdval'),
    },
    {
      title: '口径(mm)',
      key: 'diameter',
      dataIndex: 'diameter',
      ellipsis: true,
      sorter: (a, b) => Number(a.diameter) - Number(b.diameter),
      ...getColumnSearchProps('diameter'),
    },
    {
      title: '来源',
      key: 'source',
      dataIndex: 'source',
      ellipsis: true,
      ...getColumnSearchProps('source'),
    },
    {
      title: '备注',
      key: 'note',
      dataIndex: 'note',
      ellipsis: true,
      ...getColumnSearchProps('note'),
    },
    {
      title: '所属部门',
      key: 'department',
      dataIndex: 'department',
      ellipsis: true,
      ...getColumnSearchProps('department'),
    },
    {
      title: '操作人员',
      key: 'createUser',
      dataIndex: 'createUser',
      ellipsis: true,
      ...getColumnSearchProps('createUser'),
    },
    {
      title: '类型',
      key: 'type',
      dataIndex: 'type',
      ellipsis: true,
      ...getColumnSearchProps('type'),
    },
    {
      title: '在模型中',
      key: 'isModel',
      dataIndex: 'isModel',
      ellipsis: true,
      width: 110,
      render: (value: boolean) => (value ? '是' : '否'),
      filters: [
        { text: '是', value: true },
        { text: '否', value: false },
      ],
      onFilter: (value, record) => record.isModel === value,
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime',
      width: 160,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dateSorter(a.otime, b.otime),
    },
    ...(extraColumns ?? []),
  ];

  return (
    <Table<ValveOperationValue>
      {...tableProps}
      rowKey="id"
      style={style}
      size="small"
      virtual
      columns={columns}
    />
  );
};

export default ValveTable;
