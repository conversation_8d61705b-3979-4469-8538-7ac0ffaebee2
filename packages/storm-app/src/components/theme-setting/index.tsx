/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
/* eslint-disable no-eval */
import { ThemeItem, ThemeSection } from '@waterdesk/data/scene';
import { DefaultOptionType } from '@waterdesk/data/types';
import { Divider, Empty } from 'antd';
import { Card, Grid, Space } from 'antd-mobile';
import { Key } from 'react';
import AppButton from '../button';
import RadioButton from '../radio-button';
import { SceneSettingWrapper } from './style';

interface Props {
  settingsList: ThemeSection[];
  onLayerChange: (layerNames: string[], checked: boolean, type: string) => void;
  onThemeChange: (
    value: Key,
    option: DefaultOptionType<string>,
    type: string,
  ) => void;
}

const ThemeSetting = (props: Props) => {
  const { settingsList, onLayerChange, onThemeChange } = props;
  const { token } = useToken();
  const generateExtraSelect = (
    type: string,
    options: ThemeItem[],
    activeTheme: ThemeItem | undefined,
  ) => {
    if (!options.length) return null;
    return (
      <RadioButton
        dataSource={options.map((item) => ({
          id: item.name,
          title: item.title,
          onClick: (value: string) => {
            const option = {
              value,
              label: options.find((item) => item.name === value)?.title,
            };
            onThemeChange(value, option as DefaultOptionType<string>, type);
          },
        }))}
        value={activeTheme?.name}
      />
    );
  };

  return (
    <SceneSettingWrapper>
      <div>
        {settingsList.length ? (
          <div>
            <Divider
              style={{
                backgroundColor: token.colorBgContainer,
              }}
            >
              主题管理
            </Divider>
            <Space
              wrap
              direction="vertical"
              style={{ width: '100%' }}
            >
              {settingsList.map((settingsItem: ThemeSection) =>
                settingsItem.themeItems.length > 0 ? (
                  <Card
                    key={settingsItem.type}
                    title={settingsItem.title}
                    headerStyle={{
                      fontWeight: 'normal',
                      border: 0,
                      paddingBottom: 0,
                      color: token.colorTextSecondary,
                      fontSize: '12px',
                    }}
                    bodyStyle={{ padding: 0 }}
                  >
                    {generateExtraSelect(
                      settingsItem.type,
                      settingsItem.themeItems,
                      settingsItem.currentThemeItem,
                    )}
                  </Card>
                ) : null,
              )}
            </Space>

            <Divider>图层管理</Divider>
            <Space
              wrap
              direction="vertical"
              style={{ width: '100%' }}
            >
              {settingsList.map((settingsItem: ThemeSection) =>
                settingsItem.layerStates.length > 0 ? (
                  <Card
                    key={settingsItem.type}
                    title={settingsItem.title}
                    headerStyle={{
                      fontWeight: 'normal',
                      border: 0,
                      paddingBottom: 0,
                      color: token.colorTextSecondary,
                      fontSize: '12px',
                    }}
                    // extra={<RightOutline />}
                  >
                    <Grid
                      columns={3}
                      gap={16}
                    >
                      {settingsItem.layerStates.map((layerState) => (
                        <Grid.Item key={layerState.name}>
                          <AppButton
                            className="layerButton"
                            active={layerState.visible}
                            ghost
                            onClick={() =>
                              onLayerChange(
                                [layerState.name],
                                !layerState.visible,
                                settingsItem.type,
                              )
                            }
                          >
                            <span
                              style={{
                                color: layerState.visible
                                  ? token.colorPrimary
                                  : token.colorText,
                              }}
                            >
                              {layerState.icon ? (
                                <span className="icon">
                                  {eval(`'${layerState.icon}'`)}
                                </span>
                              ) : null}
                              {layerState.title}
                            </span>
                          </AppButton>
                        </Grid.Item>
                      ))}
                    </Grid>
                  </Card>
                ) : null,
              )}
            </Space>
          </div>
        ) : (
          <Empty style={{ margin: '50% 0' }} />
        )}
      </div>
    </SceneSettingWrapper>
  );
};

ThemeSetting.displayName = 'ThemeSetting';
export default ThemeSetting;
