/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const SceneSettingWrapper = styled.div`
  width: 100%;
  background-color: ${({ theme }) => theme.colorBgContainer};
  padding: 10px;

  .layerButton {
    font-size: 12px;
    width: 100%;
    .icon {
      font-family: iconfont;
    }
  }
`;
