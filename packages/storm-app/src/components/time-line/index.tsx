/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from '@waterdesk/data/event-scheduling/related-info';
import {
  getDateTimeFromValue,
  getValueFromDateTime,
} from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import localCN from 'dayjs/locale/zh-cn';
import { memo, ReactNode, useCallback, useEffect, useState } from 'react';
import SecondTimer from './second-timer';
import { TimelineWrapper } from './style';
import TimelineDatePicker from './time-line-datepicker';
import TimeSlider from './time-slider';

const defaultMarks = {
  240: getDateTimeFromValue(240).format('HH:mm'),
  480: getDateTimeFromValue(480).format('HH:mm'),
  720: getDateTimeFromValue(720).format('HH:mm'),
  960: getDateTimeFromValue(960).format('HH:mm'),
  1200: getDateTimeFromValue(1200).format('HH:mm'),
};

interface Props {
  timeValue: number;
  dateValue: Dayjs;
  startTime?: Dayjs;
  endTime?: Dayjs;
  updateDateValue: (value: Dayjs) => void;
  updateTimeValue: (value: number) => void;
  getAllList?: (date: Dayjs) => void;
  warnInfo?: Map<string, WarnInfoList>;
  eventInfo?: Map<string, EventSchedulingBasicInfo[]>;
  currentWarnInfo?: WarnInfoList;
  currentEventInfo?: EventSchedulingBasicInfo[];
  currentEventRelatedInfo?: EventSchedulingRelatedInfo[];
  showDate?: boolean;
  showRealtimeMark?: boolean;
  autoPlay: boolean;
  updateAutoPlay?: (autoPlay: boolean) => void;
}

const Timeline = memo((props: Props) => {
  const {
    autoPlay,
    showDate,
    timeValue,
    dateValue,
    startTime,
    endTime,
    warnInfo,
    eventInfo,
    currentWarnInfo,
    currentEventInfo,
    currentEventRelatedInfo,
    showRealtimeMark,
    updateDateValue,
    updateTimeValue,
    getAllList,
    updateAutoPlay,
  } = props;

  const { token } = useToken();

  // 最新时间
  const [latestTime, setLatestTime] = useState<Dayjs>(dayjs());
  const [sliderValue, setSliderValue] = useState<number>(timeValue);

  const handleDatePickerDateChange = useCallback(
    (date: Dayjs | null) => {
      if (date !== null) {
        updateDateValue(date);
        if (dayjs().isSame(date, 'day')) {
          updateTimeValue(getValueFromDateTime(dayjs()));
          setLatestTime(dayjs());
          updateAutoPlay?.(true);
        } else {
          updateTimeValue(0);
          updateAutoPlay?.(false);
        }
      }
    },
    [updateDateValue, updateTimeValue, updateAutoPlay],
  );

  const handleDatePickerTimeChange = (date: Dayjs | null) => {
    if (date !== null) {
      updateTimeValue(getValueFromDateTime(date));
      // 通过时间选择器手动设置timelineTime时,关闭自动播放
      updateAutoPlay?.(false);
    }
  };

  const handleOnChange = (value: number) => {
    setSliderValue(value);
  };

  const handleSecondTimerFinish = () => {
    const nowDate: Dayjs = dayjs();
    setLatestTime(nowDate);
    if (autoPlay) {
      const timeValue = getValueFromDateTime(nowDate);
      updateDateValue(nowDate);
      updateTimeValue(timeValue);
    }
  };

  const getMarkRealTime = () => {
    if (typeof showRealtimeMark === 'boolean' && showRealtimeMark === false)
      return {};
    if (latestTime && dayjs().isSame(dateValue, 'day')) {
      return {
        [getValueFromDateTime(latestTime)]: {
          style: {
            color: token.colorErrorActive,
            top: '-42px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              最新:{latestTime.format('HH:mm:')}
              <SecondTimer
                style={{ fontSize: '1.2em' }}
                onFinish={handleSecondTimerFinish}
              />
            </strong>
          ),
        },
      };
    }
    return {};
  };

  const markRealTime = getMarkRealTime();

  const handleAfterChange = (value: number) => {
    updateTimeValue(value);
    updateAutoPlay?.(
      value === getValueFromDateTime(dayjs()) &&
        dayjs().isSame(dateValue, 'day'),
    );
  };

  const timePickContent: ReactNode = (
    <div className="time-line-counttime">
      <DatePicker
        style={{ width: '48px', padding: '1px 5px' }}
        picker="time"
        format="HH:mm"
        variant="borderless"
        suffixIcon={null}
        allowClear={{
          clearIcon: null,
        }}
        inputReadOnly
        value={getDateTimeFromValue(sliderValue)}
        onChange={handleDatePickerTimeChange}
      />
    </div>
  );

  const getDatePickContent = (): ReactNode => {
    if (typeof showDate === 'boolean' && showDate === false)
      return <div className="time-line-week">{timePickContent}</div>;
    return (
      <div className="time-line-datepicker">
        <TimelineDatePicker
          value={dateValue}
          getAllList={getAllList}
          warnInfo={warnInfo}
          eventInfo={eventInfo}
          onChange={handleDatePickerDateChange}
        />
        <div className="time-line-week">
          <span>{dayjs(dateValue).locale(localCN).format('dddd')}</span>
          <div className="time-line-counttime">
            <DatePicker
              style={{ width: '48px', padding: '3px' }}
              picker="time"
              format="HH:mm"
              variant="borderless"
              suffixIcon={null}
              allowClear={{
                clearIcon: null,
              }}
              inputReadOnly
              value={getDateTimeFromValue(sliderValue)}
              onChange={handleDatePickerTimeChange}
            />
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    setSliderValue(timeValue);
  }, [timeValue]);

  useEffect(() => {
    if (
      timeValue === getValueFromDateTime(dayjs()) &&
      dayjs().isSame(dateValue, 'day')
    ) {
      if (!autoPlay) {
        updateAutoPlay?.(true);
      }
    }
  }, [timeValue, dateValue]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = dayjs();

        if (now.isAfter(dayjs(timeValue), 'second')) {
          setLatestTime(now);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [timeValue]);

  const getSliderMark = () => {
    if (startTime && endTime) {
      const startTimeMark = getValueFromDateTime(startTime);
      const endTimeMark = getValueFromDateTime(endTime);

      return {
        ...defaultMarks,
        [startTimeMark]: {
          style: {
            color: '#3589ff',
            top: '-40px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              {getDateTimeFromValue(startTimeMark).format('HH:mm')}
            </strong>
          ),
        },
        [endTimeMark]: {
          style: {
            color: '#3589ff',
            top: '-40px',
            width: '100px',
            display: 'inline-block',
          },
          label: (
            <strong style={{ width: 'inline-block' }}>
              {getDateTimeFromValue(endTimeMark).format('HH:mm')}
            </strong>
          ),
        },
      };
    }
    return defaultMarks;
  };

  return (
    <TimelineWrapper>
      <div className="time-line-timeslider">
        <TimeSlider
          date={startTime ?? dateValue}
          latestTime={latestTime}
          marks={{ ...getSliderMark(), ...markRealTime }}
          value={sliderValue}
          minSlider={startTime ? getValueFromDateTime(startTime) : undefined}
          maxSlider={endTime ? getValueFromDateTime(endTime) : undefined}
          onChange={handleOnChange}
          onAfterChange={handleAfterChange}
          warnList={currentWarnInfo ?? []}
          eventList={currentEventInfo ?? []}
          eventRelatedList={currentEventRelatedInfo ?? []}
        />
      </div>
      {getDatePickContent()}
    </TimelineWrapper>
  );
});

Timeline.displayName = 'Timeline';

export default Timeline;
