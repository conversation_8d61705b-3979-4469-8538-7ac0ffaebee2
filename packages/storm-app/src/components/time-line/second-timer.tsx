/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

interface Props {
  style?: React.CSSProperties;
  onFinish?: () => void;
}

const SecondTimer: React.FC<Props> = ({ onFinish, style }) => {
  const [second, setSecond] = useState(dayjs().format('ss'));
  const timerRef = useRef<number | undefined>(undefined);
  const onFinishRef = useRef(onFinish);

  const clearTimer = () => {
    if (timerRef.current !== undefined) {
      clearTimeout(timerRef.current);
      timerRef.current = undefined;
    }
  };

  const initTimer = () => {
    clearTimer();

    const update = () => {
      const now = dayjs();
      const nextSecond = now.second().toString().padStart(2, '0');

      if (nextSecond === '00') {
        onFinishRef.current?.();
      }

      setSecond(nextSecond);

      const delay = 1000 - now.millisecond();
      timerRef.current = window.setTimeout(() => {
        requestAnimationFrame(update);
      }, delay);
    };

    requestAnimationFrame(update);
  };

  useEffect(() => {
    onFinishRef.current = onFinish;
  }, [onFinish]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        initTimer();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    initTimer();

    return () => {
      clearTimer();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return <span style={style}>{second}</span>;
};

export default SecondTimer;
