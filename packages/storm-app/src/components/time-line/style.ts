/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DatePicker } from 'antd';
import { Dayjs } from 'dayjs';
import styled from 'styled-components';

export const TimelineWrapper = styled.div`
  min-height: 70px;
  height: 100%;
  min-width: 400px;
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid ${({ theme }) => theme.colorBorder};
  background-color: ${({ theme }) => theme.colorBgElevated};
  .time-line-datepicker,
  .time-line-counttime {
    flex: 0 0 auto;
  }
  .time-line-datepicker {
    position: relative;
    .time-line-week {
      display: flex;
      align-items: baseline;
      position: absolute;
      left: 28px;
      top: 20px;
    }
  }
  .time-line-timeslider {
    position: relative;
    flex: 1 0 auto;
    height: 16px;
    .ant-slider-with-marks {
      margin: 0;
    }

    .ant-slider-track,
    .ant-slider-rail {
      background-color: ${({ theme }) => theme.colorPrimary};
      height: 8px;
    }
    .ant-slider:hover {
      .ant-slider-track,
      .ant-slider-rail {
        background-color: ${({ theme }) => theme.colorPrimary};
      }
    }
    .ant-slider-step {
      height: 8px;
    }
    .ant-slider-dot {
      width: 12px;
      height: 12px;
      z-index: 9;
      display: none;
    }
    .ant-slider-dot,
    .ant-slider-dot-active {
      border-color: ${({ theme }) => theme.colorPrimary};
    }
    .ant-slider-mark {
      top: 16px;
    }
    .ant-slider-mark-text {
      color: inherit;
    }
    .ant-slider-handle {
      z-index: 10;
      width: 14px;
      height: 14px;
      &:hover::before,
      &:hover::before,
      &:focus::before {
        width: 22px;
        height: 22px;
      }
      &::before {
        width: 18px;
        height: 18px;
      }
      &::after {
        width: 14px;
        height: 14px;
      }
    }
  }
  .time-slider-mark-item {
    height: 8px;
    background-color: ${({ theme }) => theme.colorError};
    position: absolute;
    top: 4px;
    z-index: 7;
    cursor: pointer;
  }
  .mark-item-spot {
    top: 2px;
    width: 12px;
    height: 12px;
    border-radius: 12px;
  }
`;

export const DatePickerWrapper = styled(DatePicker<Dayjs>)<{
  isCurrentDay: boolean;
  isCurrentYear: boolean;
}>`
  width: 110px;
  padding: 0;
  margin-left: 19px;
  margin-bottom: 15px;
  padding-left: ${(props) => (props.isCurrentYear ? '9px' : '0px')};
  .ant-picker-input > input {
    font-size: ${(props) => (props.isCurrentYear ? '18px' : '14px')};
    font-weight: ${(props) => (props.isCurrentYear ? 'bold' : 'normal')};
    color: ${(props) =>
      props.isCurrentDay ? props.theme.colorErrorActive : ''};
  }
`;
