/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getValueFromDateTime } from '@waterdesk/data/time-data';
import { Tooltip } from 'antd';
import classnames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { CSSProperties, Key, memo } from 'react';
import { minuteCount } from './time-slider';

export const MARK_SPOT = 'SPOT';
export const MARK_LINE = 'LINE';
export type MarkType = typeof MARK_SPOT | typeof MARK_LINE; // 支持点和线
export interface MarkItem {
  key: Key;
  type?: string;
  startTime: string;
  endTime?: string;
  color?: string;
}
interface Props {
  date: Dayjs;
  marks?: MarkItem[];
  style?: CSSProperties;
  onClick?: (markItem: MarkItem) => void;
}

const getMarkInfo = (
  date: Dayjs,
  startTime: string,
  endTime?: string,
): {
  markType: MarkType;
  width: string | undefined;
  left: string;
  time: string;
} => {
  const startValue = date.isSame(dayjs(startTime), 'day')
    ? getValueFromDateTime(dayjs(startTime))
    : 0;
  let endValue = minuteCount;
  if (typeof endTime === 'undefined') {
    endValue = startValue;
  } else if (date.isSame(dayjs(endTime), 'day')) {
    endValue = getValueFromDateTime(dayjs(endTime));
  }
  let markType: MarkType;
  let width: string | undefined;
  let left: string | undefined;
  let time: string;
  if (startValue === endValue) {
    markType = MARK_SPOT;
    left = `${(startValue / minuteCount) * 100}%`;
    time = dayjs(startTime).format('HH:mm');
  } else {
    markType = MARK_LINE;
    width = `${((endValue - startValue) / minuteCount) * 100}%`;
    time = `${dayjs(startTime).format('HH:mm')}-${dayjs(endTime).format(
      'HH:mm',
    )}`;
  }
  left = `${(startValue / minuteCount) * 100}%`;
  return { markType, left, width, time };
};

const TimeSliderMark = memo((props: Props) => {
  const { marks, date, style, onClick } = props;

  return marks?.length ? (
    <>
      {marks.map((config: MarkItem) => {
        const { startTime, endTime, color, type } = config;
        if (startTime === '' || endTime === '') return null;
        const { width, left, markType, time } = getMarkInfo(
          date,
          startTime,
          endTime,
        );
        return (
          <Tooltip
            key={config.key}
            title={type ? `${time} ${type}` : null}
          >
            <div
              className={classnames('time-slider-mark-item', {
                'mark-item-spot': markType === 'SPOT',
                'mark-item-line': markType === 'LINE',
              })}
              style={{ width, left, backgroundColor: color, ...style }}
              aria-hidden="true"
              onClick={() => onClick?.(config)}
            />
          </Tooltip>
        );
      })}
    </>
  ) : null;
});

TimeSliderMark.displayName = 'TimeSliderMark';

export default TimeSliderMark;
