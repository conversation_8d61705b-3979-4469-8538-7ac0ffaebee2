/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { EventSchedulingRelatedInfo } from '@waterdesk/data/event-scheduling/related-info';
import {
  getDateTimeFromValue,
  getValueFromDateTime,
} from '@waterdesk/data/time-data';
import { WarnInfoList } from '@waterdesk/data/warn';
import { Slider } from 'antd';
import { SliderMarks } from 'antd/lib/slider';
import dayjs, { Dayjs } from 'dayjs';
import { memo, useMemo } from 'react';
import TimeSliderMark, { MarkItem } from './time-slider-mark';

// total number of minutes per day
export const minuteCount: number = 1440;

interface Props {
  date: Dayjs;
  value: number;
  latestTime: dayjs.Dayjs;
  marks?: SliderMarks;
  onChange?: (value: number) => void;
  onAfterChange?: (value: number) => void;
  warnList: WarnInfoList;
  eventList: EventSchedulingBasicInfo[];
  eventRelatedList: EventSchedulingRelatedInfo[];
  minSlider?: number;
  maxSlider?: number;
}

const toolTipFormatter = (value?: number): string => {
  if (typeof value === 'undefined') return '';
  return getDateTimeFromValue(value).format('HH:mm');
};

function getValue(
  value: number,
  min: number | undefined,
  max: number | undefined,
) {
  if (min && value < min) return min;
  if (max && value > max) return max;
  return value;
}

const TimeSlider = memo((props: Props) => {
  const {
    date,
    value,
    latestTime,
    onChange,
    onAfterChange,
    marks,
    warnList,
    eventList,
    eventRelatedList,
    minSlider,
    maxSlider,
  } = props;

  const { token } = useToken();

  const handleOnChange = (v: number) => {
    onChange?.(getValue(v, minSlider, maxSlider));
  };

  const handleAfterChange = (v: number) => {
    onAfterChange?.(getValue(v, minSlider, maxSlider));
  };

  const timeSliderMarks = useMemo(
    (): MarkItem[] =>
      warnList.map(
        (item): MarkItem => ({
          key: item.id,
          type: item.secondTypeName,
          startTime: item.startTime,
          endTime: item.endTime,
        }),
      ),
    [warnList],
  );

  const timeSliderEventMarks = useMemo(
    (): MarkItem[] =>
      eventList.map(
        (item): MarkItem => ({
          key: item.eventId,
          type: `调度事件: ${item.eventTitle}`,
          startTime: item.eventStartTime,
          color: token.purple5,
        }),
      ),
    [eventList],
  );

  const timeSliderEventRelatedMarks = useMemo(
    (): MarkItem[] =>
      eventRelatedList.map(
        (item): MarkItem => ({
          key: item.relatedId,
          type: `${item.eventName}: ${item.relatedDescription}`,
          startTime: item.relatedTime ?? '',
          color: '#A97CDC',
        }),
      ),
    [eventRelatedList],
  );

  const futureTimeMarks = useMemo((): MarkItem[] => {
    if (date.isSame(dayjs(), 'day') && value !== minuteCount) {
      return [
        {
          key: 'futureMark',
          startTime: dayjs(latestTime).format('YYYY-MM-DD HH:mm:ss'),
          endTime: dayjs().add(+1, 'day').format('YYYY-MM-DD 00:00:00'),
          color: '#d9d9d9',
        },
      ];
    }
    return [];
  }, [date, latestTime]);

  const getSolutionMinTimeSliderMark = (): MarkItem[] => {
    if (typeof minSlider !== 'undefined') {
      const startTime = getDateTimeFromValue(minSlider, date).format(
        'YYYY-MM-DD 00:00:00',
      );
      const endTime = getDateTimeFromValue(minSlider, date).format(
        'YYYY-MM-DD HH:mm:00',
      );

      if (startTime === endTime) return [];

      return [
        {
          key: 'minSlider',
          type: undefined,
          color: '#d9d9d9',
          startTime,
          endTime,
        },
      ];
    }
    return [];
  };

  const getSolutionMaxTimeSliderMark = (): MarkItem[] => {
    if (typeof maxSlider !== 'undefined') {
      const startTime = getDateTimeFromValue(maxSlider, date).format(
        'YYYY-MM-DD HH:mm:00',
      );
      const endTime = getDateTimeFromValue(maxSlider, date).format(
        'YYYY-MM-DD 23:59:00',
      );
      if (startTime === endTime) return [];
      return [
        {
          key: 'maxSlider',
          type: undefined,
          color: '#d9d9d9',
          startTime,
          endTime,
        },
      ];
    }
    return [];
  };

  return (
    <>
      <Slider
        marks={marks}
        max={minuteCount}
        value={value}
        tooltip={{
          formatter: toolTipFormatter,
        }}
        onChange={handleOnChange}
        onChangeComplete={handleAfterChange}
        style={{ height: '16px' }}
      />
      {typeof minSlider !== 'undefined' && typeof maxSlider !== 'undefined' ? (
        <>
          <TimeSliderMark
            date={date}
            marks={getSolutionMinTimeSliderMark()}
          />
          <TimeSliderMark
            date={date}
            marks={getSolutionMaxTimeSliderMark()}
          />
        </>
      ) : null}
      <TimeSliderMark
        onClick={(markItem) =>
          handleOnChange(getValueFromDateTime(dayjs(markItem.startTime)))
        }
        date={date}
        marks={timeSliderMarks}
      />
      <TimeSliderMark
        onClick={(markItem) =>
          handleOnChange(getValueFromDateTime(dayjs(markItem.startTime)))
        }
        date={date}
        marks={timeSliderEventRelatedMarks}
      />
      <TimeSliderMark
        onClick={(markItem) =>
          handleOnChange(getValueFromDateTime(dayjs(markItem.startTime)))
        }
        date={date}
        marks={timeSliderEventMarks}
      />
      <TimeSliderMark
        style={{ pointerEvents: 'none', zIndex: 6 }}
        date={date}
        marks={futureTimeMarks}
      />
    </>
  );
});

TimeSlider.displayName = 'TimeSlider';

export default TimeSlider;
