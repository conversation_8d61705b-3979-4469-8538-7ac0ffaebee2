/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Calendar } from 'antd-mobile';
import dayjs, { Dayjs } from 'dayjs';
import { useMergedState } from 'rc-util';

interface Props {
  value?: [Dayjs, Dayjs];
  defaultValue?: [Dayjs, Dayjs];
  onChange?: (dates: [Dayjs, Dayjs]) => void;
}

export default function TimePickerRange(props: Props) {
  const { value, defaultValue, onChange } = props;

  const [dayjsDates, setDayjsDates] = useMergedState<[Dayjs, Dayjs]>(
    [dayjs(), dayjs()],
    {
      value,
    },
  );

  const onCalendarChange = (date: [Date, Date] | null) => {
    if (date) {
      setDayjsDates([dayjs(date[0]), dayjs(date[1])]);
      onChange?.([dayjs(date[0]), dayjs(date[1])]);
    }
  };

  return (
    <Calendar
      selectionMode="range"
      value={[dayjsDates[0].toDate(), dayjsDates[1].toDate()]}
      onChange={onCalendarChange}
      defaultValue={
        defaultValue
          ? [defaultValue[0].toDate(), defaultValue[1].toDate()]
          : null
      }
    />
  );
}
