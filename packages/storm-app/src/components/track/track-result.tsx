/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SettingOutlined } from '@ant-design/icons';
import {
  DownTrackWaterMeterData,
  HighlightObject,
  TrackDownDma,
  TrackDownLink,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { HIGHLIGHT_CUSTOM } from '@waterdesk/data/style-config';
import {
  ObjectFormItem,
  ResultData,
  TRACK_CUSTOM,
  TRACK_DOWN,
  TRACK_POLLUTION,
  TRACK_UP,
  TrackConfig,
  TrackType,
} from '@waterdesk/data/track-data';
import { formatNumber } from '@waterdesk/data/utils';
import { But<PERSON>, Collapse, Select, Space, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import TrackResultDetail from 'src/components/network-track/track-result-detail';
import { getColumnSearchProps } from 'src/components/table/column';

interface Props {
  dimension: boolean;
  type: TrackType;
  resultData: ResultData | undefined;
  positionToMapView: (
    highlightObject: HighlightObject | ObjectFormItem,
  ) => void;
  handleHover: (
    highlightObject: HighlightObject | ObjectFormItem | undefined,
  ) => void;
  highlight: (
    highlightObject: {
      upWaterMeter?: HighlightObject[];
      downWaterMeter?: HighlightObject[];
      downDma?: HighlightObject[];
      upDma?: HighlightObject[];
      downLink?: HighlightObject[];
      upLink?: HighlightObject[];
      scada?: HighlightObject[];
      sourceLink?: HighlightObject[];
    },
    themeConfig?: {
      upWaterMeter?: LegendGroupData;
      downWaterMeter?: LegendGroupData;
      downDma?: LegendGroupData;
      upDma?: LegendGroupData;
      downLink?: LegendGroupData;
      upLink?: LegendGroupData;
      scada?: LegendGroupData;
      sourceLink?: LegendGroupData;
    },
  ) => void;
  trackResultConfig: (type: TrackType) => TrackConfig | undefined;
  getDmaSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme: LegendGroupData;
      }
    | undefined;
  getLinkSwitchData: (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
    themeMap: Map<string, LegendGroupData[]>,
  ) =>
    | {
        highlightData: HighlightObject[] | TrackDownDma[] | TrackDownLink[];
        highlightTheme: LegendGroupData;
      }
    | undefined;
}

function getLinkSumLength(impactedLinks: TrackDownLink[]) {
  const length = impactedLinks.reduce(
    (accumulator, obj) => accumulator + (obj.length || 0),
    0,
  );
  if (length > 10000) {
    return `${formatNumber(length / 1000, 2)} km`;
  }
  return `${formatNumber(length, 2)} m`;
}

function getResultCollapseConfig(type: TrackType) {
  switch (type) {
    case TRACK_DOWN:
      return new Map([
        ['downArea', '下游小区'],
        ['downWaterMeter', '下游用户'],
        ['downLink', '下游管道'],
        ['downScada', '下游监测点'],
      ]);
    case TRACK_UP:
      return new Map([
        ['upLink', '上游管线'],
        ['upScada', '上游监测点'],
      ]);
    case TRACK_POLLUTION:
      return new Map([
        ['pollutedSourceLink', '污染源'],
        ['upArea', '污染小区'],
        ['upLink', '污染管道'],
        ['upWaterMeter', '污染用户'],
        ['downArea', '未污染小区'],
        ['downLink', '未污染管道'],
        ['downWaterMeter', '未污染用户'],
      ]);
    case TRACK_CUSTOM:
      return new Map([
        ['downArea', '下游小区'],
        ['downWaterMeter', '下游用户'],
        ['downLink', '下游管道'],
        ['downScada', '下游监测'],
        ['upArea', '上游小区'],
        ['upWaterMeter', '上游用户'],
        ['upLink', '上游管道'],
        ['upScada', '上游监测'],
      ]);
    default:
      return new Map([
        ['upArea', '上游小区'],
        ['upLink', '上游管道'],
        ['upScada', '监测'],
      ]);
  }
}

export default function TrackResult(props: Props) {
  const {
    dimension,
    type,
    resultData,
    positionToMapView,
    handleHover,
    highlight,
    trackResultConfig,
    getDmaSwitchData,
    getLinkSwitchData,
  } = props;

  const [activeThemes, setActiveThemes] = useState<{ [key: string]: string }>(
    {},
  );
  const [upDmaDatas, setUpDmaDatas] = useState<TrackDownDma[]>([]);
  const [downDmaDatas, setDownDmaDatas] = useState<TrackDownDma[]>([]);
  const [result, setResult] = useState<{
    defaultHighlightData: HighlightObject[];
    upScadaDatas: HighlightObject[];
    upLinkDatasMap: Map<string, TrackDownLink>;
    downScadaDatas: HighlightObject[];
    downLinkDatasMap: Map<string, TrackDownLink>;
    downLinkDatas: TrackDownLink[];
    upLinkDatas: TrackDownLink[];
    downWaterMeterDatas: DownTrackWaterMeterData[];
    upWaterMeterDatas: DownTrackWaterMeterData[];
  }>({
    defaultHighlightData: [],
    upScadaDatas: [],
    upLinkDatasMap: new Map(),
    downScadaDatas: [],
    downLinkDatasMap: new Map(),
    downLinkDatas: [],
    upLinkDatas: [],
    downWaterMeterDatas: [],
    upWaterMeterDatas: [],
  });
  const [upLinkDatas, setUpLinkDatas] = useState<TrackDownLink[]>([]);
  const [selectedUpLinkRowKeys, setSelectedUpLinkRowKeys] = useState<
    React.Key[]
  >([]);
  const [downLinkDatas, setDownLinkDatas] = useState<TrackDownLink[]>([]);
  const [selectedDownLinkRowKeys, setSelectedDownLinkRowKeys] = useState<
    React.Key[]
  >([]);

  const [sourceLinkDatas, setSourceLinkDatas] = useState<TrackDownLink[]>([]);
  const [selectedSourceLinkRowKeys, setSelectedSourceLinkRowKeys] = useState<
    React.Key[]
  >([]);

  const [activeCollapseKey, setActiveCollapseKey] = useState<string | string[]>(
    [],
  );
  const [collapseConfig, setCollapseConfig] = useState<Map<string, string>>(
    new Map(),
  );

  const [columnsMap, setColumnsMap] = useState<Map<string, ColumnsType<any>>>(
    new Map(),
  );
  const [tableColumnsMap, setTableColumnsMap] = useState<
    Map<string, ColumnsType<any>>
  >(new Map());

  const [themeMap, setThemeMap] = useState<Map<string, LegendGroupData[]>>(
    new Map(),
  );

  const [trackResultDetail, setTrackResultDetail] = useState<{
    columns: ColumnsType<any>;
    title: string;
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[];
  }>({
    columns: [],
    title: '',
    dataSource: [],
  });
  const [trackDetailOpen, setTrackDetailOpen] = useState<boolean>(false);

  const getColumns = (columnsData: ColumnsType<any>) =>
    columnsData.map((item) => {
      if (
        item.key === 'TITLE' ||
        item.key === 'ROAD_NAME' ||
        item.key === 'NAME'
      ) {
        return {
          ...item,
          render: (
            value: string | number,
            record: HighlightObject | ObjectFormItem,
          ) => (
            <Button
              type="link"
              onClick={() => positionToMapView(record)}
              onMouseOver={() => handleHover(record)}
              onMouseOut={() => handleHover(undefined)}
            >
              {value}
            </Button>
          ),
          ...getColumnSearchProps<TrackDownDma>('TITLE'),
        };
      }
      return item;
    });

  const switchTrackTheme = (
    collapse: string,
    value: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
  ) => {
    setActiveThemes((state) => ({
      ...state,
      [collapse]: value,
    }));
    const data = getDmaSwitchData(collapse, value, dataSource, themeMap);
    if (data) {
      if (collapse === 'downArea') {
        highlight(
          { downDma: data.highlightData },
          { downDma: data.highlightTheme },
        );
      }
      if (collapse === 'upArea') {
        highlight(
          { upDma: data.highlightData },
          { upDma: data.highlightTheme },
        );
      }
      if (collapse === 'upWaterMeter') {
        highlight(
          { upWaterMeter: data.highlightData },
          { upWaterMeter: data.highlightTheme },
        );
      }
    }
  };

  const switchLinkTrackTheme = (
    collapse: string,
    value: string,
    dataSource: TrackDownLink[],
  ) => {
    setActiveThemes((state) => ({
      ...state,
      [collapse]: value,
    }));
    const data = getLinkSwitchData(collapse, value, dataSource, themeMap);
    if (data) {
      if (collapse === 'upLink') {
        highlight(
          { upLink: data.highlightData },
          { upLink: data.highlightTheme },
        );
      }
      if (collapse === 'downLink') {
        highlight(
          { downLink: data.highlightData },
          { downLink: data.highlightTheme },
        );
      }
      if (collapse === 'pollutedSourceLink') {
        highlight(
          { sourceLink: data.highlightData },
          { sourceLink: data.highlightTheme },
        );
      }
    }
  };

  const getTable = (
    collapse: string,
    title: string,
    dataSource: TrackDownDma[] | TrackDownLink[] | HighlightObject[],
  ) => {
    const columns = getColumns(tableColumnsMap.get(collapse) ?? []);
    setTrackResultDetail({
      columns,
      title,
      dataSource,
    });
    setTrackDetailOpen(true);
  };

  const trackDetailClose = () => {
    setTrackDetailOpen(false);
  };

  useEffect(() => {
    const collapse = getResultCollapseConfig(type);
    setCollapseConfig(collapse);
    const trackConfig = trackResultConfig(type);

    if (trackConfig) {
      setColumnsMap(trackConfig.panelColumnsMap);
      setTableColumnsMap(trackConfig.tableColumnsMap);
      setThemeMap(trackConfig.themes);
    }
  }, [type]);

  useEffect(() => {
    if (!resultData) return;
    const {
      upDmaDatas,
      upLinkDatasMap,
      upScadaDatas,
      defaultHighlightDatas,
      downDmaDatas,
      downLinkDatasMap,
      downScadaDatas,
      downWaterMeterDatas,
      upWaterMeterDatas,
      linkSourceDatasMap,
    } = resultData;

    const defaultHighlightData = defaultHighlightDatas ?? [];
    const activeCollapseName: string[] = [];
    if (upDmaDatas && upDmaDatas.length > 0) {
      activeCollapseName.push('effectDma');
    }
    setActiveCollapseKey(activeCollapseName);
    setUpDmaDatas(upDmaDatas ?? []);
    setDownDmaDatas(downDmaDatas ?? []);
    const impactedLinksObjectsMapKeys: React.Key[] = [];
    const links: TrackDownLink[] = [];
    const upImpactedLinksAllObjects: TrackDownLink[] = [];
    (upLinkDatasMap ?? new Map()).forEach((item, id) => {
      impactedLinksObjectsMapKeys.push(id);
      links.push(item);
      upImpactedLinksAllObjects.push(...item.highlightObjects);
    });
    setUpLinkDatas(links);
    setSelectedUpLinkRowKeys(impactedLinksObjectsMapKeys);

    const downImpactedLinksObjectsMapKeys: React.Key[] = [];
    const downImpactedLinks: TrackDownLink[] = [];
    const downImpactedLinksAllObjects: TrackDownLink[] = [];
    (downLinkDatasMap ?? new Map()).forEach((item, id) => {
      downImpactedLinksObjectsMapKeys.push(id);
      downImpactedLinks.push(item);
      downImpactedLinksAllObjects.push(...item.highlightObjects);
    });
    setDownLinkDatas(downImpactedLinks);
    setSelectedDownLinkRowKeys(downImpactedLinksObjectsMapKeys);

    const linkSourceLinksObjectsMapKeys: React.Key[] = [];
    const linkSourceLinks: TrackDownLink[] = [];
    const linkSourceLinksAllObjects: TrackDownLink[] = [];
    (linkSourceDatasMap ?? new Map()).forEach((item, id) => {
      linkSourceLinksObjectsMapKeys.push(id);
      linkSourceLinks.push(item);
      linkSourceLinksAllObjects.push(...item.highlightObjects);
    });
    setSourceLinkDatas(linkSourceLinks);
    setSelectedSourceLinkRowKeys(linkSourceLinksObjectsMapKeys);

    setResult({
      defaultHighlightData,
      upScadaDatas: upScadaDatas ?? [],
      upLinkDatasMap: upLinkDatasMap ?? new Map(),
      downScadaDatas: downScadaDatas ?? [],
      downLinkDatasMap: downLinkDatasMap ?? new Map(),
      downLinkDatas: downImpactedLinksAllObjects,
      upLinkDatas: upImpactedLinksAllObjects,
      downWaterMeterDatas: downWaterMeterDatas ?? [],
      upWaterMeterDatas: upWaterMeterDatas ?? [],
    });
  }, [resultData]);

  useEffect(() => {
    if (!resultData) return;
    const {
      upDmaDatas,
      upLinkDatas,
      downDmaDatas,
      downLinkDatas,
      downWaterMeterDatas,
      upWaterMeterDatas,
      linkSourceDatas,
    } = resultData;

    const highlightDownWaterMeterData = downWaterMeterDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
      highlightTextBgColor: '#ffffff',
    }));
    const highlightUpWaterMeterData = upWaterMeterDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    const highlightDownDmaData = downDmaDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    const highlightUpDmaData = upDmaDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    const highlightDownLinkData = downLinkDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    const highlightUpLinkData = upLinkDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    const highlightLinkSourceData = linkSourceDatas?.map((item) => ({
      ...item,
      highlightShowMark: dimension,
    }));
    highlight({
      downWaterMeter: highlightDownWaterMeterData ?? [],
      upWaterMeter: highlightUpWaterMeterData ?? [],
      downDma: highlightDownDmaData ?? [],
      upDma: highlightUpDmaData ?? [],
      downLink: highlightDownLinkData ?? [],
      upLink: highlightUpLinkData ?? [],
      sourceLink: highlightLinkSourceData ?? [],
    });
  }, [resultData, dimension]);

  useEffect(() => {
    if (!resultData) return;
    const collapse = getResultCollapseConfig(type);

    const downWaterMeterDatas = getDmaSwitchData(
      'downWaterMeter',
      activeThemes.downWaterMeter ?? themeMap?.get('downWaterMeter')?.[0]?.name,
      resultData?.downWaterMeterDatas ?? [],
      themeMap,
    );

    const downDmaData = getDmaSwitchData(
      'downArea',
      activeThemes.downArea ?? themeMap?.get('downArea')?.[0]?.name,
      downDmaDatas,
      themeMap,
    );

    const upWaterMeterDatas = getDmaSwitchData(
      'upWaterMeter',
      activeThemes.upWaterMeter ?? themeMap?.get('upWaterMeter')?.[0]?.name,
      resultData?.upWaterMeterDatas ?? [],
      themeMap,
    );

    const upDmaData = getDmaSwitchData(
      'upArea',
      activeThemes.upArea ?? themeMap?.get('upArea')?.[0]?.name,
      upDmaDatas,
      themeMap,
    );

    const downLinkData = getLinkSwitchData(
      'downLink',
      activeThemes.downLink ?? themeMap?.get('downLink')?.[0]?.name,
      downLinkDatas,
      themeMap,
    );

    const upLinkData = getLinkSwitchData(
      'upLink',
      activeThemes.upLink ?? themeMap?.get('upLink')?.[0]?.name,
      upLinkDatas,
      themeMap,
    );

    const pollutedSourceLinkData = getLinkSwitchData(
      'pollutedSourceLink',
      activeThemes.pollutedSourceLink ??
        themeMap?.get('pollutedSourceLink')?.[0]?.name,
      sourceLinkDatas,
      themeMap,
    );

    const highlightData: { [key: string]: HighlightObject[] | undefined } = {
      waterMeter: [],
      downDma: [],
      upDma: [],
      downLink: [],
      upLink: [],
      sourceLink: [],
    };
    const highlightDataThemeConfig: {
      [key: string]: LegendGroupData | undefined;
    } = {};

    if (collapse.has('downWaterMeter')) {
      highlightData.downWaterMeter = downWaterMeterDatas?.highlightData ?? [];
      highlightDataThemeConfig.downWaterMeter =
        downWaterMeterDatas?.highlightTheme;
    }
    if (collapse.has('upWaterMeter')) {
      highlightData.upWaterMeter = upWaterMeterDatas?.highlightData ?? [];
      highlightDataThemeConfig.upWaterMeter = upWaterMeterDatas?.highlightTheme;
    }
    if (collapse.has('downArea')) {
      highlightData.downDma = downDmaData?.highlightData ?? [];
      highlightDataThemeConfig.downDma = downDmaData?.highlightTheme;
    }
    if (collapse.has('upArea')) {
      highlightData.upDma = upDmaData?.highlightData ?? [];
      highlightDataThemeConfig.upDma = upDmaData?.highlightTheme;
    }
    if (collapse.has('downLink')) {
      highlightData.downLink = downLinkData?.highlightData ?? [];
      highlightDataThemeConfig.downLink = downLinkData?.highlightTheme;
    }
    if (collapse.has('upLink')) {
      highlightData.upLink = upLinkData?.highlightData ?? [];
      highlightDataThemeConfig.upLink = upLinkData?.highlightTheme;
    }
    if (collapse.has('pollutedSourceLink')) {
      highlightData.sourceLink = pollutedSourceLinkData?.highlightData ?? [];
      highlightDataThemeConfig.sourceLink =
        pollutedSourceLinkData?.highlightTheme;
    }

    highlight(highlightData, highlightDataThemeConfig);
  }, [themeMap, result, dimension, activeThemes]);

  const onChangeLink = (type: string, e: React.Key[]) => {
    let impactedLinkKeys = selectedUpLinkRowKeys;
    let downImpactedLinkKeys = selectedDownLinkRowKeys;

    if (type === 'upLink') {
      impactedLinkKeys = e;
      const highlightObjects: HighlightObject[] = [];
      highlightObjects.push(...downDmaDatas, ...upDmaDatas);
      impactedLinkKeys.forEach((id) => {
        const roadObject = result.upLinkDatasMap.get(id as string);
        if (roadObject) {
          roadObject.highlightObjects.forEach((item) => {
            highlightObjects.push({
              ...item,
              highlightType: HIGHLIGHT_CUSTOM,
              highlightShowMark: dimension,
            });
          });
        }
      });
      highlight(
        {
          upLink: highlightObjects,
        },
        {},
      );
    } else if (type === 'downLink') {
      downImpactedLinkKeys = e;
      const highlightObjects: HighlightObject[] = [];
      downImpactedLinkKeys.forEach((id) => {
        const roadObject = result.downLinkDatasMap.get(id as string);
        if (roadObject) {
          roadObject.highlightObjects.forEach((item) => {
            highlightObjects.push({
              ...item,
              highlightType: HIGHLIGHT_CUSTOM,
              highlightShowMark: dimension,
            });
          });
        }
      });
      highlight(
        {
          downLink: highlightObjects,
        },
        {},
      );
    }
  };

  const onSelectRow = (e: React.Key[]) => {
    setSelectedUpLinkRowKeys(e);
    onChangeLink('upLink', e);
  };

  const onSelectDownLinkRow = (e: React.Key[]) => {
    setSelectedDownLinkRowKeys(e);
    onChangeLink('downLink', e);
  };

  const onChangeCollapse = (e: string | string[]) => {
    setActiveCollapseKey(e);
  };

  return (
    <>
      <Collapse
        activeKey={activeCollapseKey}
        bordered={false}
        className="collapse"
        onChange={(e: string | string[]) => onChangeCollapse(e)}
        style={{ overflow: 'auto' }}
      >
        {collapseConfig.has('upWaterMeter') && (
          <Collapse.Panel
            header={`${collapseConfig.get('upWaterMeter')} (${
              result.upWaterMeterDatas?.length
            })`}
            key="upWaterMeter"
            extra={
              <SettingOutlined
                onClick={() =>
                  getTable(
                    'upWaterMeter',
                    `用户表 (${result.upWaterMeterDatas?.length})`,
                    result.upWaterMeterDatas,
                  )
                }
              />
            }
          >
            <Table
              scroll={{
                y: 'calc(35vh - 100px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={[...getColumns(columnsMap.get('downWaterMeter') ?? [])]}
              dataSource={result.upWaterMeterDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('downWaterMeter') && (
          <Collapse.Panel
            header={`${collapseConfig.get('downWaterMeter')} (${
              result.downWaterMeterDatas?.length
            })`}
            key="downWaterMeter"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('downWaterMeter')?.[0]?.name}
                  options={themeMap.get('downWaterMeter')}
                  fieldNames={{ label: 'title', value: 'name' }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchTrackTheme(
                      'downWaterMeter',
                      value,
                      result.downWaterMeterDatas,
                    );
                  }}
                />
                <SettingOutlined
                  onClick={() =>
                    getTable(
                      'downWaterMeter',
                      `用户表 (${result.downWaterMeterDatas?.length})`,
                      result.downWaterMeterDatas,
                    )
                  }
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(35vh - 100px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={[...getColumns(columnsMap.get('downWaterMeter') ?? [])]}
              dataSource={result.downWaterMeterDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('upArea') && (
          <Collapse.Panel
            header={`${collapseConfig.get('upArea')}(${upDmaDatas.length})`}
            key="upDma"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('upArea')?.[0]?.name}
                  options={themeMap.get('upArea')}
                  fieldNames={{ label: 'title', value: 'name' }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchTrackTheme('upArea', value, upDmaDatas);
                  }}
                />
                <SettingOutlined
                  onClick={() =>
                    getTable(
                      'upDma',
                      `${collapseConfig.get('upArea')} (${upDmaDatas.length})`,
                      upDmaDatas,
                    )
                  }
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(50vh - 250px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={getColumns(columnsMap.get('upArea') ?? [])}
              dataSource={upDmaDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('downArea') && (
          <Collapse.Panel
            header={`${collapseConfig.get('downArea')} (${
              downDmaDatas.length
            })`}
            key="downDma"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('downArea')?.[0]?.name}
                  options={themeMap.get('downArea')}
                  fieldNames={{ label: 'title', value: 'name' }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchTrackTheme('downArea', value, downDmaDatas);
                  }}
                />
                <SettingOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    getTable(
                      'downArea',
                      `${collapseConfig.get('downArea')} (${
                        downDmaDatas.length
                      })`,
                      downDmaDatas,
                    );
                  }}
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(50vh - 250px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={getColumns(columnsMap.get('downArea') ?? [])}
              dataSource={downDmaDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('pollutedSourceLink') && (
          <Collapse.Panel
            header={`${collapseConfig.get(
              'pollutedSourceLink',
            )} (总长: ${getLinkSumLength(sourceLinkDatas)})`}
            key="pollutedSourceLink"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('pollutedSourceLink')?.[0]?.name}
                  fieldNames={{ label: 'title', value: 'name' }}
                  options={themeMap.get('pollutedSourceLink')}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchLinkTrackTheme(
                      'pollutedSourceLink',
                      value,
                      sourceLinkDatas,
                    );
                  }}
                />
                <SettingOutlined
                  onClick={() =>
                    getTable(
                      'pollutedSourceLink',
                      `${collapseConfig.get(
                        'pollutedSourceLink',
                      )} (总长: ${getLinkSumLength(sourceLinkDatas)})`,
                      sourceLinkDatas,
                    )
                  }
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(50vh - 250px)',
              }}
              size="small"
              rowSelection={{
                onChange: (e: React.Key[]) => onSelectRow(e),
                selectedRowKeys: selectedSourceLinkRowKeys,
              }}
              pagination={false}
              rowKey="id"
              columns={getColumns(columnsMap.get('pollutedSourceLink') ?? [])}
              dataSource={sourceLinkDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('upLink') && (
          <Collapse.Panel
            header={`${collapseConfig.get('upLink')} (总长: ${getLinkSumLength(
              upLinkDatas,
            )})`}
            key="upLink"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('upLink')?.[0]?.name}
                  fieldNames={{ label: 'title', value: 'name' }}
                  options={themeMap.get('upLink')}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchLinkTrackTheme('upLink', value, upLinkDatas);
                  }}
                />
                <SettingOutlined
                  onClick={() =>
                    getTable(
                      'upLink',
                      `${collapseConfig.get('upLink')} (${collapseConfig.get(
                        'upLink',
                      )} : ${getLinkSumLength(upLinkDatas)})`,
                      upLinkDatas,
                    )
                  }
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(50vh - 250px)',
              }}
              size="small"
              rowSelection={{
                onChange: (e: React.Key[]) => onSelectRow(e),
                selectedRowKeys: selectedUpLinkRowKeys,
              }}
              pagination={false}
              rowKey="id"
              columns={getColumns(columnsMap.get('upLink') ?? [])}
              dataSource={upLinkDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('downLink') && (
          <Collapse.Panel
            header={`${collapseConfig.get(
              'downLink',
            )} (总长: ${getLinkSumLength(downLinkDatas)})`}
            key="downLink"
            extra={
              <Space>
                <Select
                  defaultValue={themeMap.get('downLink')?.[0]?.name}
                  fieldNames={{ label: 'title', value: 'name' }}
                  options={themeMap.get('downLink')}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(value) => {
                    switchLinkTrackTheme('downLink', value, downLinkDatas);
                  }}
                />
                <SettingOutlined
                  onClick={() =>
                    getTable(
                      'downLink',
                      `${collapseConfig.get(
                        'downLink',
                      )} (总长: ${getLinkSumLength(downLinkDatas)})`,
                      downLinkDatas,
                    )
                  }
                />
              </Space>
            }
          >
            <Table
              scroll={{
                y: 'calc(50vh - 250px)',
              }}
              size="small"
              rowSelection={{
                onChange: (e: React.Key[]) => onSelectDownLinkRow(e),
                selectedRowKeys: selectedDownLinkRowKeys,
              }}
              pagination={false}
              rowKey="id"
              columns={getColumns(columnsMap.get('downLink') ?? [])}
              dataSource={downLinkDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('downScada') && (
          <Collapse.Panel
            header={`${collapseConfig.get('downScada')}  (${
              result.downScadaDatas?.length
            })`}
            key="downScada"
          >
            <Table
              scroll={{
                y: 'calc(35vh - 100px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={[
                {
                  key: 'title',
                  dataIndex: 'title',
                  title: 'ID',
                  ellipsis: true,
                  width: 100,
                  render: (value, record) => (
                    <Button
                      type="link"
                      onClick={() => positionToMapView(record)}
                      onMouseOver={() => handleHover(record)}
                      onMouseOut={() => handleHover(undefined)}
                    >
                      {value}
                    </Button>
                  ),
                },
                {
                  key: 'scadaValue',
                  dataIndex: 'scadaValue',
                  title: '监测值',
                  width: 100,
                },
              ]}
              dataSource={result.downScadaDatas}
            />
          </Collapse.Panel>
        )}
        {collapseConfig.has('upScada') && (
          <Collapse.Panel
            header={`${collapseConfig.get('upScada')}  (${
              result.upScadaDatas?.length
            })`}
            key="upScada"
          >
            <Table
              scroll={{
                y: 'calc(35vh - 100px)',
              }}
              size="small"
              pagination={false}
              rowKey="oname"
              columns={[
                {
                  key: 'title',
                  dataIndex: 'title',
                  title: 'ID',
                  ellipsis: true,
                  width: 100,
                  render: (value, record) => (
                    <Button
                      type="link"
                      onClick={() => positionToMapView(record)}
                      onMouseOver={() => handleHover(record)}
                      onMouseOut={() => handleHover(undefined)}
                    >
                      {value}
                    </Button>
                  ),
                },
                {
                  key: 'scadaValue',
                  dataIndex: 'scadaValue',
                  title: '监测值',
                  width: 100,
                },
              ]}
              dataSource={result.upScadaDatas}
            />
          </Collapse.Panel>
        )}
      </Collapse>
      <TrackResultDetail
        open={trackDetailOpen}
        columns={trackResultDetail.columns}
        dataSource={trackResultDetail.dataSource}
        title={trackResultDetail.title}
        close={trackDetailClose}
      />
    </>
  );
}
