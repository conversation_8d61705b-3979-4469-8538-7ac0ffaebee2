/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { TRACK_UP, TrackType } from '@waterdesk/data/track-data';
import { formatNumber } from '@waterdesk/data/utils';
import { Button, InputNumber, Space } from 'antd';
import { Card, Form } from 'antd-mobile';
import { useCallback, useEffect } from 'react';

interface FormItem {
  direction: string;
  minFlow: number;
  minDiameter: number;
}

interface Props {
  selectObjects: IObjectItem | undefined;
  getStreamTrack: (
    oname: string,
    otype: string,
    minDiameter: number,
    minFLow: number,
  ) => Promise<void>;

  getRecommendValues: (
    otype: string,
    oname: string,
    trackType: TrackType,
  ) => Promise<{ flow: number; diameter: number }>;
}

export default function TrackStream(props: Props) {
  const { selectObjects, getStreamTrack, getRecommendValues } = props;
  const [modalForm] = Form.useForm<FormItem>();

  const getTrackObject = useCallback((): string[] | undefined => {
    if (selectObjects) {
      return [selectObjects.otype, selectObjects.oname];
    }
    return undefined;
  }, [selectObjects]);

  const onNetworkTrack = async (params: FormItem) => {
    const { minFlow, minDiameter } = params;
    const objects: string[] | undefined = getTrackObject();
    if (!objects) {
      return;
    }
    const [otype, oname] = objects;
    let minFlowNumber = 0;
    let minDiameterNumber = 0;
    if (minFlow !== undefined) {
      minFlowNumber = minFlow;
    }
    if (minDiameter !== undefined) {
      minDiameterNumber = minDiameter;
    }
    await getStreamTrack(oname, otype, minDiameterNumber, minFlowNumber);
  };

  const fetchRecommendValues = async (otype: string, oname: string) => {
    const res = await getRecommendValues(otype, oname, TRACK_UP);
    if (res) {
      modalForm.setFieldsValue({
        minFlow: formatNumber(res.flow, 1),
        minDiameter: res.diameter,
      });
    }
  };

  useEffect(() => {
    if (selectObjects) {
      fetchRecommendValues(selectObjects.otype, selectObjects.oname);
    }
  }, [selectObjects]);

  const onSubmit = () => {
    const params = modalForm.getFieldsValue();
    if (params) {
      onNetworkTrack(params);
    }
  };

  const onReset = () => {
    modalForm.resetFields();
  };

  return (
    <Card>
      <Form
        preserve={false}
        form={modalForm}
        onFinish={onSubmit}
      >
        <Form.Item
          name="minFlow"
          label="流量最小值(m³/h)"
          initialValue={0}
        >
          <InputNumber
            className="formInput"
            min={0}
            step={100}
            variant="borderless"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="minDiameter"
          label="管径最小值(mm)"
          initialValue={0}
        >
          <InputNumber
            className="formInput"
            min={0}
            step={100}
            variant="borderless"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item>
          <Space
            direction="vertical"
            style={{ width: '100%' }}
          >
            <Button
              type="primary"
              htmlType="submit"
              block
            >
              追踪
            </Button>
            <Button
              className="resetBtn"
              htmlType="button"
              onClick={() => onReset()}
              block
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
