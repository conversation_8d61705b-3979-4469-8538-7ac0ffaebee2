/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { dateSorter } from '@waterdesk/data/utils';
import {
  formatSdval,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { Form, Table, Tooltip } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { EditableCell } from 'src/components/observation-scada/editable-cell';
import { getColumnSearchProps } from 'src/components/table/column';

export interface EditValveOperationValue extends ValveOperationValue {
  canEdit?: boolean;
}

type Columns = ColumnType<EditValveOperationValue> & {
  editable?: boolean;
  isSmartValve?: boolean;
};

interface Props {
  valveOperationDetails: ValveOperationValue[];
  loading?: boolean;
  isSmartValve?: boolean;
}

const ValveEditor = (props: Props) => {
  const { valveOperationDetails, loading, isSmartValve } = props;
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState<React.Key | undefined>(
    undefined,
  );
  const [editData, setEditData] = useState<EditValveOperationValue[]>([]);

  const isEditing = (record: EditValveOperationValue) =>
    record.id === editingKey;

  const handleOnValuesChange = (changedValues: any) => {
    if (changedValues.sdval === 'SMART_VALVE') {
      form.setFieldValue('note', 'smart_valve迭代生成');
    }
  };

  const columns = useMemo(
    (): Columns[] => [
      {
        title: '时间',
        key: 'otime',
        dataIndex: 'otime',
        width: 150,
        render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
        sorter: (a, b) => dateSorter(a.otime, b.otime),
        defaultSortOrder: 'descend',
        editable: true,
      },
      {
        title: '变更后状态',
        key: 'sdval',
        dataIndex: 'sdval',
        ellipsis: true,
        editable: true,
        width: editData.length > 0 ? 200 : 120,
        render: (value) => formatSdval(value),
        ...getColumnSearchProps('sdval'),
      },
      {
        title: '口径(mm)',
        key: 'diameter',
        dataIndex: 'diameter',
        ellipsis: true,
        width: 100,
        ...getColumnSearchProps('diameter'),
      },
      {
        title: '来源',
        key: 'source',
        dataIndex: 'source',
        ellipsis: true,
        width: 120,
        ...getColumnSearchProps('source'),
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'type',
        ellipsis: true,
        width: 100,
        ...getColumnSearchProps('type'),
      },
      {
        title: '在模型中',
        key: 'isModel',
        dataIndex: 'isModel',
        ellipsis: true,
        width: 110,
        render: (value: boolean) => (value ? '是' : '否'),
        filters: [
          { text: '是', value: true },
          { text: '否', value: false },
        ],
        onFilter: (value, record) => record.isModel === value,
      },
      {
        title: '创建时间',
        key: 'stime',
        dataIndex: 'stime',
        ellipsis: true,
        width: 150,
        render: (value: string) =>
          value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
        sorter: (a, b) => dateSorter(a.stime, b.stime),
      },
      {
        title: '备注',
        key: 'note',
        dataIndex: 'note',
        ellipsis: true,
        editable: true,
        width: 180,
        render: (value: string) => <Tooltip title={value}>{value}</Tooltip>,
        ...getColumnSearchProps('note'),
      },
      {
        title: '所属部门',
        key: 'department',
        dataIndex: 'department',
        ellipsis: true,
        width: 130,
        ...getColumnSearchProps('department'),
      },
      {
        title: '操作人员',
        key: 'createUser',
        dataIndex: 'createUser',
        ellipsis: true,
        width: 100,
        ...getColumnSearchProps('createUser'),
      },
    ],
    [editData],
  );

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: EditValveOperationValue) =>
        ({
          record,
          dataIndex: col.dataIndex,
          editing: isEditing(record),
          isSmartValve: isEditing(record) && isSmartValve,
          editable: col.editable?.toString(),
          title: col.title,
          dataSource: valveOperationDetails,
        }) as React.HTMLAttributes<HTMLElement>,
    };
  });

  useEffect(() => {
    setEditingKey('');
    setEditData([]);
  }, [valveOperationDetails]);

  return (
    <Form
      onValuesChange={handleOnValuesChange}
      form={form}
      component={false}
    >
      <Table
        style={{
          height: 'calc(100vh - 85px)',
        }}
        loading={loading}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        size="small"
        rowKey="id"
        dataSource={[...editData, ...valveOperationDetails]}
        columns={mergedColumns}
        pagination={false}
        scroll={{
          y: 'calc(100vh - 85px)',
        }}
      />
    </Form>
  );
};

ValveEditor.displayName = 'ValveEditor';

export default ValveEditor;
