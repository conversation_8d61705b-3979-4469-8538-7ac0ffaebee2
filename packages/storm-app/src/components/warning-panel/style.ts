/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/
import { Badge } from 'antd';
import { EllipsisText } from 'src/styles/common-style';
import styled from 'styled-components';

export const WarningFilterTabWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0px 20px 10px;
`;

export const WarningLevelTab = styled.div`
  display: flex;
  margin-bottom: 10px;
`;

export const BadgeText = styled(Badge)<{ $isActive: boolean }>`
  color: ${({ theme, $isActive: isActive }) =>
    isActive ? theme.colorPrimaryText : theme.colorTextBase};
`;

export const ProcessMenuItem = styled.div<{
  isActive?: boolean;
  isHalfActive?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 35px;
  padding: 0 60px 0 20px;
  cursor: pointer;
  background: ${({ theme, isActive, isHalfActive }) => {
    if (isActive) return theme['blue-2'];
    if (isHalfActive) return theme['blue-1'];
    return theme.colorBgBase;
  }};
`;

export const FirstProcessMenuItem = styled(ProcessMenuItem)`
  border-top: 1px solid ${({ theme }) => theme.colorBorder};
`;

export const LastProcessMenuItem = styled(ProcessMenuItem)`
  border-top: 1px solid
    ${({ theme, isHalfActive }) =>
      !isHalfActive ? 'transparent' : theme.colorBorder};
  border-bottom: 1px solid
    ${({ theme, isHalfActive }) =>
      isHalfActive ? 'transparent' : theme.colorBorder};
`;

export const ProcessCount = styled.span`
  color: ${({ theme }) => theme.colorPrimaryTextActive};
`;

export const ProcessSubMenuList = styled.div`
  overflow-y: scroll;
  height: 140px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-left: 20px;
  border: 1px solid ${({ theme }) => theme.colorBorder};
  border-left: none;
  border-right: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

export const ProcessSubMenuItem = styled.div<{ isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: ${({ theme, isActive }) =>
    isActive ? theme['blue-2'] : theme.colorBgBase};
  height: 28px;
  padding: 0 60px 0 20px;
  cursor: pointer;
`;

export const WarningListWrapper = styled.div`
  .ant-list-header {
    padding: 5px 0px;
  }
`;

export const WarningListHeader = styled.div`
  margin: 0 10px;
`;

export const ListScroll = styled.div`
  height: calc(100vh - 440px);
  overflow-y: scroll;
`;

export const WarningListTitleWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 5px;

  .message-title {
    flex: 0 1 auto;
    ${EllipsisText}
  }
`;

export const WarningListItemName = styled.span`
  margin-right: 3px;
`;

export const WarningListItemTime = styled.span`
  color: ${({ theme }) => theme.colorTextDescription};
  font-size: 13px;
`;

export const WarningListContent = styled.div`
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin: 0 2px;
`;

export const WarningDescription = styled.span<{ showSpecialWarn?: boolean }>`
  color: ${({ showSpecialWarn, theme }) =>
    showSpecialWarn ? '#d32029' : theme.colorTextBase};
  font-weight: ${({ showSpecialWarn }) =>
    showSpecialWarn ? 'bold' : 'normal'};
`;
