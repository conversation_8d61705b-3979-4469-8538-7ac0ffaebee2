/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EditOutlined } from '@ant-design/icons';
import { SecondFilterItems } from '@waterdesk/data/scenes/warning-scene';
import { WarnInfoList } from '@waterdesk/data/warn';
import { warningLevels } from '@waterdesk/data/warn/warn-count';
import { Button, Radio, RadioProps, Space, Tooltip } from 'antd';
import { FC } from 'react';
import { BadgeText, WarningFilterTabWrapper, WarningLevelTab } from './style';

interface WarningFilterTabProps {
  selectedLevel: number | string | undefined;
  selectedCategory: string;
  unprocessedWarningList: Record<string, WarnInfoList>;
  warningCategories: SecondFilterItems[];
  onLevelChange: (selectedLevel: number) => void;
  onCategoryChange: (selectedCategory: string) => void;
  onEditCustomWarning: () => void;
}

const WarningFilterTab: FC<WarningFilterTabProps> = ({
  selectedLevel,
  selectedCategory,
  unprocessedWarningList,
  warningCategories,
  onLevelChange,
  onCategoryChange,
  onEditCustomWarning,
}) => {
  const handleLevelChange: RadioProps['onChange'] = (e) => {
    onLevelChange(e.target.value);
  };

  const handleCategoryChange: RadioProps['onChange'] = (e) => {
    onCategoryChange(e.target.value);
  };

  return (
    <WarningFilterTabWrapper>
      <WarningLevelTab>
        <Radio.Group
          value={selectedLevel}
          onChange={handleLevelChange}
        >
          {warningLevels.map((level) => (
            <Radio.Button
              key={level.value}
              value={level.value}
            >
              <BadgeText
                count={unprocessedWarningList[level.value]?.length ?? 0}
                offset={[0, -10]}
                $isActive={selectedLevel === level.value}
              >
                {level.label}
              </BadgeText>
            </Radio.Button>
          ))}
        </Radio.Group>
      </WarningLevelTab>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Radio.Group
          value={selectedCategory}
          onChange={handleCategoryChange}
          size="small"
        >
          <Space wrap>
            {warningCategories
              .filter((i) => !i.hidden)
              .map((category) => (
                <Radio.Button
                  key={category.value}
                  value={category.value}
                >
                  {category.label}
                </Radio.Button>
              ))}
          </Space>
        </Radio.Group>
        <Button
          size="small"
          style={{ marginRight: -10 }}
          onClick={onEditCustomWarning}
        >
          <Tooltip title="编辑自定义观测点">
            <EditOutlined />
          </Tooltip>
        </Button>
      </div>
    </WarningFilterTabWrapper>
  );
};

export default WarningFilterTab;
