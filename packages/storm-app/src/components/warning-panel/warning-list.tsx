/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  MinusCircleOutlined,
  ProfileOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { useToken } from '@waterdesk/core/theme';
import { WarnNoticeInfo } from '@waterdesk/data/notice-data';
import {
  getWarnConfirmStatusName,
  onSuccessCallback,
  WarnConfirmStatus,
  WarnInfoItem,
} from '@waterdesk/data/warn';
import {
  getWarningLevelColor,
  getWarningLevelName,
} from '@waterdesk/data/warn/warn-level';
import { Button, Checkbox, List, Tag, TagProps } from 'antd';
import dayjs from 'dayjs';
import VirtualList from 'rc-virtual-list';
import React, { FC, ReactNode, useEffect, useState } from 'react';
import { BlinkPoint } from 'src/styles/common-style';
import SearchTitle, { ButtonConfig } from '../common/table/table-title';
import IconText from '../icon-font/icon-text';
import {
  WarningDescription,
  WarningListContent,
  WarningListItemName,
  WarningListItemTime,
  WarningListTitleWrapper,
  WarningListWrapper,
} from './style';

interface WarningListProps {
  list: WarnNoticeInfo[];
  secondType?: string;
  selectedLevel?: string | number;
  onClick: (warnInfo: WarnInfoItem) => void;
  onWarnConfirm: (warnInfo: WarnInfoItem, callback: () => void) => void;
  onTrackDown: (details: WarnInfoItem) => void;
  onConfirm?: (
    warnInfo: WarnInfoItem | WarnInfoItem[] | undefined,
    onConfirmSuccess?: onSuccessCallback | undefined,
  ) => void;
  onReload?: () => void;
  virtual?: boolean;
  scrollHeight?: number;
  showSpecialWarn?: boolean;
}

const WarningList: FC<WarningListProps> = ({
  list,
  virtual,
  secondType,
  selectedLevel,
  scrollHeight,
  onClick,
  onWarnConfirm,
  onTrackDown,
  onConfirm,
  onReload,
  showSpecialWarn,
}) => {
  const [openCheck, setOpenCheck] = useState(false);
  const [checkedList, setCheckedList] = useState<WarnNoticeInfo[]>([]);

  const { token } = useToken();
  const getTitleContent = (warnInfo: WarnInfoItem) => {
    const { rank, details, secondTypeName: type } = warnInfo;
    const isShape = details?.some((item) => item.shape);
    return isShape ? (
      <WarningListItemName>
        <Button
          className="message-title"
          style={{ padding: 0, height: '22px', fontSize: 13 }}
          type="link"
          onClick={() => onClick(warnInfo)}
        >
          {getWarningLevelName(rank, true)}&nbsp;
          <span style={{ fontSize: 12 }}>{type}</span>
        </Button>
      </WarningListItemName>
    ) : (
      <WarningListItemName>
        {getWarningLevelName(rank, true)}&nbsp;
        <span style={{ fontSize: 12 }}>{type}</span>
      </WarningListItemName>
    );
  };

  const getTagIcon = (confirmStatus: WarnConfirmStatus): ReactNode => {
    switch (confirmStatus) {
      case WarnConfirmStatus.SHELVE:
        return <ClockCircleOutlined />;
      case WarnConfirmStatus.NOT_WARN:
        return <MinusCircleOutlined />;
      case WarnConfirmStatus.CONFIRM:
        return <CheckCircleOutlined />;
      case WarnConfirmStatus.NOT_CONFIRM:
        return <SyncOutlined spin />;
      default:
        return null;
    }
  };

  const getTagColor = (confirmStatus: WarnConfirmStatus): TagProps['color'] => {
    switch (confirmStatus) {
      case WarnConfirmStatus.CONFIRM:
        return 'success';
      case WarnConfirmStatus.NOT_CONFIRM:
        return 'processing';
      case WarnConfirmStatus.NOT_WARN:
      case WarnConfirmStatus.SHELVE:
      default:
        return 'default';
    }
  };

  const handleCheckedList = (item: WarnNoticeInfo) => {
    const index = checkedList.findIndex((i) => i.id === item.id);
    if (index > -1) {
      const newList = [...checkedList];
      newList.splice(index, 1);
      setCheckedList(newList);
    } else {
      setCheckedList([...checkedList, item]);
    }
  };

  const getItemBackground = (item: WarnInfoItem): string => {
    if (item.groupIndex === undefined || item.groupIndex === 0)
      return 'transparent';

    if (item.groupIndex % 2 === 0)
      return token.colorFillTertiary ?? 'transparent';
    return token.colorFillQuaternary ?? 'transparent';
  };

  const buttons: ButtonConfig[] = onConfirm
    ? [
        {
          label: '取消批量处理',
          callback: () => {
            setOpenCheck(false);
            setCheckedList([]);
          },
          size: 'small',
          hidden: !openCheck,
          icon: <CloseOutlined />,
          tooltip: true,
        },
        {
          label: '开启批量处理',
          callback: () => {
            setOpenCheck(true);
            setCheckedList([]);
          },
          size: 'small',
          hidden: openCheck,
          icon: <ProfileOutlined />,
          tooltip: true,
        },
        {
          label: '批量处理',
          callback: () =>
            onConfirm?.(checkedList, () => {
              onReload?.();
              setCheckedList([]);
              setOpenCheck(false);
            }),
          size: 'small',
          disabled: checkedList.length === 0,
          icon: <IconText text="&#xe6db;" />,
          hidden: !openCheck,
          tooltip: true,
        },
      ]
    : [];

  const renderListItem = (item: WarnInfoItem): ReactNode => (
    <List.Item
      style={{
        display: 'block',
        padding: '4px 0',
        background: getItemBackground(item),
      }}
    >
      <List.Item.Meta
        title={
          <WarningListTitleWrapper>
            <div>
              {openCheck && (
                <Checkbox
                  checked={checkedList.some((i) => i.id === item.id)}
                  onClick={() => handleCheckedList(item)}
                  disabled={item.eventId !== ''}
                />
              )}
              <BlinkPoint
                color={getWarningLevelColor(item.rank)}
                style={{ marginRight: 3 }}
              />
              {getTitleContent(item)}
              <WarningListItemTime>
                {dayjs(item.startTime).fromNow()}
                {`(${dayjs(item.startTime).format('MM-DD HH:mm')})`}
              </WarningListItemTime>
            </div>
            <div>
              <Tag
                style={{ cursor: 'pointer' }}
                onClick={() =>
                  onWarnConfirm(item, () => {
                    setCheckedList([]);
                    setOpenCheck(false);
                  })
                }
                icon={getTagIcon(item.confirmStatus)}
                color={getTagColor(item.confirmStatus)}
              >
                {getWarnConfirmStatusName(item.confirmStatus)}
              </Tag>
              {item.secondType === 'FLOW_DIRECTION_ABNORMAL' ? (
                <Button
                  title="去向追踪"
                  style={{ padding: '0', height: '22px' }}
                  type="link"
                  key="editable"
                  onClick={() => {
                    onTrackDown(item);
                  }}
                >
                  <IconText text="&#xe653;" />
                </Button>
              ) : null}
            </div>
          </WarningListTitleWrapper>
        }
      />
      <WarningListContent
        title={item.description + (item.endStatus === 0 ? '[持续中]' : '')}
      >
        <WarningDescription
          showSpecialWarn={showSpecialWarn && item.rank === 0}
        >
          {item.description}
        </WarningDescription>
        {item.endStatus === 0 ? '[持续中]' : ''}
      </WarningListContent>
    </List.Item>
  );

  useEffect(() => {
    setOpenCheck(false);
    setCheckedList([]);
  }, [selectedLevel]);

  return (
    <WarningListWrapper>
      {secondType && secondType !== '' && (
        <SearchTitle
          title={secondType ?? ''}
          style={{ marginLeft: 10, marginBottom: 0, marginTop: 3 }}
          buttons={buttons}
        />
      )}
      {virtual ? (
        <List size="small">
          <VirtualList
            data={list}
            height={scrollHeight}
            itemHeight={80}
            itemKey="id"
          >
            {(item) => renderListItem(item)}
          </VirtualList>
        </List>
      ) : (
        <List
          rowKey="id"
          size="small"
          dataSource={list}
          renderItem={(item) => renderListItem(item)}
        />
      )}
    </WarningListWrapper>
  );
};

export default React.memo(WarningList);
