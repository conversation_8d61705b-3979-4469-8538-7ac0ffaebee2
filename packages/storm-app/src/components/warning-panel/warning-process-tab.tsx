/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  TabItem,
  WARNING_PROCESSED,
  WARNING_UNPROCESSED,
} from '@waterdesk/data/warn/warn-count';
import { FC } from 'react';
import {
  FirstProcessMenuItem,
  LastProcessMenuItem,
  ProcessCount,
  ProcessSubMenuItem,
  ProcessSubMenuList,
} from './style';

interface WarningProcessTabProps {
  activeTab: string | undefined;
  activeSubTab: string | undefined;
  tabs: TabItem[];
  unprocessedCount: number;
  processedCount: number;
  onTabChange: (tabName: string) => void;
  onSubTabChange: (tabName: string) => void;
}

const WarningProcessTab: FC<WarningProcessTabProps> = ({
  activeTab,
  activeSubTab,
  tabs,
  unprocessedCount,
  processedCount,
  onTabChange,
  onSubTabChange,
}) => {
  const handleSubTabClick = (tabName: string) => {
    onSubTabChange(tabName);
  };

  const handleTabClick = (tabName: string) => {
    onTabChange(tabName);
    onSubTabChange(tabName);
  };

  const renderSubMenu = () => (
    <ProcessSubMenuList>
      {tabs.map((tab) => (
        <ProcessSubMenuItem
          key={tab.name}
          isActive={activeSubTab === tab.name}
          onClick={() => handleSubTabClick(tab.name)}
        >
          <span>{tab.name}</span>
          <ProcessCount>{tab.count}</ProcessCount>
        </ProcessSubMenuItem>
      ))}
    </ProcessSubMenuList>
  );

  return (
    <>
      <FirstProcessMenuItem
        onClick={() => handleTabClick(WARNING_UNPROCESSED)}
        isActive={activeSubTab === WARNING_UNPROCESSED}
        isHalfActive={activeTab === WARNING_UNPROCESSED}
      >
        <span>{WARNING_UNPROCESSED}</span>
        <ProcessCount>{unprocessedCount}</ProcessCount>
      </FirstProcessMenuItem>
      {activeTab === WARNING_UNPROCESSED && renderSubMenu()}
      <LastProcessMenuItem
        onClick={() => handleTabClick(WARNING_PROCESSED)}
        isActive={activeSubTab === WARNING_PROCESSED}
        isHalfActive={activeTab === WARNING_PROCESSED}
      >
        <span>{WARNING_PROCESSED}</span>
        <ProcessCount>{processedCount}</ProcessCount>
      </LastProcessMenuItem>
      {activeTab === WARNING_PROCESSED && renderSubMenu()}
    </>
  );
};

export default WarningProcessTab;
