/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

interface MyWindow extends Window {
  HSConfig: {
    HSWEBSERVER: string;
    APPNAME: string;
    SYSTEM_CONFIG: {
      systemName: string;
      systemSubTitle: string;
      systemSubTitleColor?: string;
      showWatermark: boolean;
      watermarkImage: string;
      logoImage: string;
      loginLogoImage: string;
      loginBackgroundImage: string;
      appLoginBackgroundImage: string;
    };
    weComAgentId: number | string;
    mobile?: boolean;
    appDeviceDetect?: string;
    wxworkAuthUrl?: string;
    loginValidation?: boolean;
    ssoLoginUrl?: string;
  };
}
declare const window: MyWindow;

const saveURL = (path: string): string => {
  const url = localStorage.getItem('requestUrl');
  if (!url) {
    localStorage.setItem('requestUrl', path);
  }
  return url || path;
};
const devBaseURL = 'http://192.168.123.43:81/newDrainGm/';
const proBaseURL = window?.HSConfig?.HSWEBSERVER.replace(
  '{ORIGIN}',
  window.location.origin,
);
const devIconfontURL = './iconfont/iconfont.js';
const iconfontURL = './iconfont/iconfont.js';

export const ICONFONT_URL =
  process.env.NODE_ENV === 'development' ? devIconfontURL : iconfontURL;
export const BASE_URL: string =
  process.env.NODE_ENV === 'development' ? saveURL(devBaseURL) : proBaseURL;

export const TIMEOUT = 120000;

export const APP_NAME = window?.HSConfig?.APPNAME;

export const SYSTEM_CONFIG = window?.HSConfig?.SYSTEM_CONFIG;

export const SYSTEM_ICON_BASE_PATH = `${BASE_URL}portal/getImage?fileName=`;

export const WeComAgentId = window?.HSConfig?.weComAgentId;

export const appDeviceDetect = window?.HSConfig?.appDeviceDetect;
export const wxworkAuthUrl = window?.HSConfig?.wxworkAuthUrl;

export const MOBILE = window?.HSConfig?.mobile;

export const LOGIN_VALIDATION = window?.HSConfig?.loginValidation;

export const SSO_LOGIN_URL = window?.HSConfig?.ssoLoginUrl;
