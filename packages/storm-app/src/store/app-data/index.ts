/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { Coordinate } from 'ol/coordinate';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { reduxLegendSaga } from './saga';
import { AppData } from './types';

export const initialState: AppData = {
  geoLocation: [0, 0],
  requestUrl: '',
};

export const slice = createSlice({
  name: 'appData',
  initialState,
  reducers: {
    updateGeoLocation(
      state,
      action: PayloadAction<{ geoLocation: Coordinate }>,
    ) {
      const { geoLocation } = action.payload;
      state.geoLocation = geoLocation;
    },
    updateRequestUrl(state, action: PayloadAction<{ requestUrl: string }>) {
      const { requestUrl } = action.payload;
      state.requestUrl = requestUrl;
    },
    geoLocationToCenter() {},
  },
});

export const { actions: appDataActions } = slice;

export const useAppDataSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxLegendSaga });
};
