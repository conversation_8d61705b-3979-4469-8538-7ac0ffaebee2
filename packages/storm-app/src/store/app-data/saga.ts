/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SagaReturnType, select, takeLatest } from 'redux-saga/effects';
import { hostApp } from 'src/app/host-app';
import { appDataActions } from '.';
import { selectGeoLocation } from './selectors';

function* geoLocationToCenter() {
  const mapView = hostApp().getMainMapView();
  if (mapView) {
    const geoLocation: SagaReturnType<typeof selectGeoLocation> =
      yield select(selectGeoLocation);
    mapView.navigateToPoint(geoLocation);
  }
}

export function* reduxLegendSaga() {
  yield takeLatest(appDataActions.geoLocationToCenter, geoLocationToCenter);
}
