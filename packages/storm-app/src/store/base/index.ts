/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { Theme } from '@waterdesk/core/theme';
import { AlertDefinition, AlertInstanceList } from '@waterdesk/data/alert';
import { AppMode } from '@waterdesk/data/app-config';
import { MapViewName } from '@waterdesk/data/const/map';
import { MenuInfoItem } from '@waterdesk/data/menu-data';
import { IObjectItem, makeId } from '@waterdesk/data/object-item';
import { ScadaDataConfig } from '@waterdesk/data/otype-vprop-config-data';
import {
  MouseMode,
  RIGHT_PANEL_PROPERTY,
  RightPanelItem,
} from '@waterdesk/data/ui-types';
import {
  ValveOperationGroup,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { WarnInfoItem, WarnInfoList } from '@waterdesk/data/warn';
import { WarnSettingList } from '@waterdesk/data/warn-setting';
import { requestApi } from '@waterdesk/request/request';
import dayjs from 'dayjs';
import { uniq } from 'lodash';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { baseSaga } from './saga';
import {
  BaseState,
  BottomHandleType,
  BottomTabKey,
  PropertyChartActionArgs,
  PropertyDmaCustomerAction,
} from './types';

export const initialState: BaseState = {
  loading: false,
  dataInitialComplete: false,
  siteName: '慧水科技WaterDesk-Live',
  viewId: undefined,
  mouseMode: 'SELECT',
  deviceUpdateDate: undefined,
  activeRightPanel: undefined,
  activeBottomTab: undefined,
  bottomOpenTabs: [],
  bottomOpen: false,
  activeChartWarnInfo: undefined,
  chartStartDate: undefined,
  chartEndDate: undefined,
  chartDateUpdateTime: undefined,
  noticeWarnList: [],
  noticeValveList: [],
  noticeValveGroupList: [],
  warnTypeList: {},
  alertDefinitionList: [],
  currentAlertList: [],
  hideFeatureTooltipImportant: undefined,
  currentTimeDataChanged: undefined,
  scadaDataConfig: undefined,
  appMode: AppMode.WEB,
  scheduleShift: undefined,
  valveGroupList: [],
  valveGroupDates: [
    dayjs().add(-1, 'd').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ],
  valveGroupLoading: false,
  menuList: [],
  layoutMainLoading: true,
};

export const slice = createSlice({
  name: 'base',
  initialState,
  reducers: {
    updateViewId(state, action: PayloadAction<{ viewId: string | undefined }>) {
      const { viewId } = action.payload;
      requestApi.viewId = viewId;
      state.viewId = viewId;
    },
    updateMouseMode(state, action: PayloadAction<{ mouseMode: MouseMode }>) {
      const { mouseMode } = action.payload;
      state.mouseMode = mouseMode;
    },
    initializeOnLineDataBase(
      _state,
      _actions: PayloadAction<{
        /** date: YYYY-MM-DD */
        theme: Theme;
        sectionId: MapViewName;
        hasContextMenu: boolean;
      }>,
    ) {},

    updateDeviceUpdateDate(state, action: PayloadAction<{ date: string }>) {
      const { date } = action.payload;
      state.deviceUpdateDate = date;
    },
    updateActiveRightPanel(
      state,
      action: PayloadAction<{ activePanel: RightPanelItem | undefined }>,
    ) {
      const { activePanel } = action.payload;
      if (state.activeRightPanel === activePanel) {
        state.activeRightPanel = undefined;
      } else {
        state.activeRightPanel = activePanel;
      }
    },
    trySwitchToPropertyPalette(state) {
      if (
        state.activeRightPanel !== undefined &&
        state.activeRightPanel !== RIGHT_PANEL_PROPERTY
      ) {
        state.activeRightPanel = RIGHT_PANEL_PROPERTY;
      }
    },
    updateBottomTab(
      state,
      action: PayloadAction<{
        activeKey?: BottomTabKey;
        type: BottomHandleType;
      }>,
    ) {
      const { activeKey, type } = action.payload;
      const newBottomOpenTabs = state.bottomOpenTabs.filter(
        (tabKey) => tabKey !== activeKey,
      );
      switch (type) {
        case 'ADD':
          if (typeof activeKey === 'undefined') break;
          state.bottomOpenTabs = uniq([...state.bottomOpenTabs, activeKey]);
          state.activeBottomTab = activeKey;
          state.bottomOpen = true;
          break;
        case 'DELETE':
          state.bottomOpenTabs = newBottomOpenTabs;
          // set default activeKey
          state.activeBottomTab =
            newBottomOpenTabs[newBottomOpenTabs.length - 1];
          // should close？
          state.bottomOpen = newBottomOpenTabs.length > 0;
          break;
        case 'CLEAN':
          state.bottomOpenTabs = [];
          state.activeBottomTab = undefined;
          state.bottomOpen = false;
          break;
        case 'TOGGLE':
          state.activeBottomTab = activeKey;
          break;
        case 'CLOSE':
          state.bottomOpen = false;
          break;
        case 'OPEN':
          if (typeof activeKey === 'undefined') break;
          state.bottomOpenTabs = uniq([...state.bottomOpenTabs, activeKey]);
          state.bottomOpen = true;
          break;
        default:
          break;
      }
    },
    updateChartDate(
      state,
      action: PayloadAction<{
        startDate: string | undefined;
        endDate: string | undefined;
      }>,
    ) {
      const { startDate, endDate } = action.payload;
      state.chartStartDate = startDate;
      state.chartEndDate = endDate;
      state.chartDateUpdateTime = dayjs().valueOf();
    },
    updateActiveChartWarnInfo(
      state,
      action: PayloadAction<WarnInfoItem | undefined>,
    ) {
      state.activeChartWarnInfo = action.payload;
    },
    resetState(state) {
      return {
        ...initialState,
        warnTypeList: state.warnTypeList,
        alertDefinitionList: state.alertDefinitionList,
        currentAlertList: state.currentAlertList,
        dataInitialComplete: state.dataInitialComplete,
        menuList: state.menuList,
        layoutMainLoading: state.layoutMainLoading,
      };
    },
    resetStateSaga() {},
    signOutResetSaga() {},
    propertyTrackAction(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
    ) {},
    propertyChartAction(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{
        arg: PropertyChartActionArgs;
        compareDevice?: IObjectItem;
      }>,
    ) {},
    propertyDmaCustomerAction(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{ arg: PropertyDmaCustomerAction }>,
    ) {},
    updateNoticeWarnList(state, action: PayloadAction<{ list: WarnInfoList }>) {
      const { list } = action.payload;
      state.noticeWarnList = list;
    },
    updateWarnTypeList(
      state,
      action: PayloadAction<{ list: WarnSettingList[] }>,
    ) {
      const { list } = action.payload;
      const listMap: { [key: string]: WarnSettingList } = {};
      list.forEach((item) => {
        const id = makeId(item.type as string, item.projectWarningId as string);
        listMap[id] = item;
      });
      state.warnTypeList = listMap;
    },
    updateNoticeValveList(
      state,
      action: PayloadAction<{ list: ValveOperationValue[] }>,
    ) {
      const { list } = action.payload;
      state.noticeValveList = list;
    },
    updateNoticeValveGroupList(
      state,
      action: PayloadAction<{ list: ValveOperationGroup[] }>,
    ) {
      const { list } = action.payload;
      state.noticeValveGroupList = list;
    },
    resetNoticeWarnList(state) {
      state.noticeWarnList = [];
    },
    updateStartLoading(state) {
      state.loading = true;
      state.dataInitialComplete = false;
    },
    updateEndLoading(state) {
      state.loading = false;
      state.dataInitialComplete = true;
    },
    updateDataInitialComplete(state) {
      state.dataInitialComplete = true;
    },
    updateHideFeatureTooltipImportant(
      state,
      action: PayloadAction<{ visible?: boolean }>,
    ) {
      const { visible } = action.payload;
      if (typeof visible === 'boolean') {
        state.hideFeatureTooltipImportant = false;
      } else {
        state.hideFeatureTooltipImportant = undefined;
      }
    },
    initializeDeviceAndIndicatorSaga() {},
    updateCurrentTimeData(
      state,
      action: PayloadAction<{
        updateTime: string;
      }>,
    ) {
      const { updateTime } = action.payload;
      state.currentTimeDataChanged = updateTime;
    },
    updateLastQueryNoticeWarnTime(
      state,
      action: PayloadAction<{
        time: string;
      }>,
    ) {
      const { time } = action.payload;
      state.lastQueryNoticeWarnTime = time;
    },
    updateLastQueryNoticeValveTime(
      state,
      action: PayloadAction<{
        time: string;
      }>,
    ) {
      const { time } = action.payload;
      state.lastQueryNoticeValveTime = time;
    },
    updateLastQueryNoticeValveGroupTime(
      state,
      action: PayloadAction<{
        time: string;
      }>,
    ) {
      const { time } = action.payload;
      state.lastQueryNoticeValveGroupTime = time;
    },
    updateScadaDataConfig(
      state,
      action: PayloadAction<{
        config: ScadaDataConfig | undefined;
      }>,
    ) {
      const { config } = action.payload;
      state.scadaDataConfig = config;
    },
    updateAppMode(
      state,
      action: PayloadAction<{
        appMode: AppMode;
      }>,
    ) {
      const { appMode } = action.payload;
      state.appMode = appMode;
    },
    updateScheduleShift() {},
    updateValveGroupList(
      state,
      action: PayloadAction<{
        list: ValveOperationGroup[];
      }>,
    ) {
      const { list } = action.payload;
      state.valveGroupList = list;
    },
    updateValveGroupDates(
      state,
      action: PayloadAction<{
        dates: [string, string];
      }>,
    ) {
      const { dates } = action.payload;
      state.valveGroupDates = dates;
    },
    updateValveGroupLoading(
      state,
      action: PayloadAction<{
        loading: boolean;
      }>,
    ) {
      const { loading } = action.payload;
      state.valveGroupLoading = loading;
    },
    updateMenuList(
      state,
      action: PayloadAction<{
        list: MenuInfoItem[];
      }>,
    ) {
      const { list } = action.payload;
      state.menuList = list;
    },
    updateLayoutMainLoading(
      state,
      action: PayloadAction<{
        loading: boolean;
      }>,
    ) {
      const { loading } = action.payload;
      state.layoutMainLoading = loading;
    },
    initializeLayoutMainSaga() {},
    updateAlertDefinitionList(
      state,
      action: PayloadAction<{
        list: AlertDefinition[];
      }>,
    ) {
      const { list } = action.payload;
      state.alertDefinitionList = list;
    },
    updateCurrentAlertList(
      state,
      action: PayloadAction<{
        list: AlertInstanceList;
      }>,
    ) {
      const { list } = action.payload;
      state.currentAlertList = list;
    },
  },
});

export const { actions: baseActions } = slice;

export const useBaseSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: baseSaga });
  return { actions: slice.actions };
};
