/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import {
  convertLegacyProjectionData,
  registerProjections,
} from '@waterdesk/core/map';
import { Theme } from '@waterdesk/core/theme';
import { MapViewName } from '@waterdesk/data/const/map';
import Database from '@waterdesk/data/database';
import {
  DeviceCollection,
  PlantsAndPumpStationsCollection,
} from '@waterdesk/data/device';
import {
  IndicatorObjectCollection,
  IndicatorTypeCollection,
} from '@waterdesk/data/indicator';
import { LayerDataCollection } from '@waterdesk/data/layer-data';
import { getMenuList } from '@waterdesk/data/menu-data';
import { IObjectItem } from '@waterdesk/data/object-item';
import { PropertyInfo } from '@waterdesk/data/property/property-info';
import {
  getDefaultSceneId,
  getInvisibleLayerNames,
  getScene,
  refreshLayerData,
  SCENE_ID_TYPE_ONLINE,
  Scene,
  SceneIdType,
} from '@waterdesk/data/scene';
import { TRACK_UP } from '@waterdesk/data/track-data';
import {
  MOUSE_EDIT_ADDJUNCTION,
  MOUSE_EDIT_ADDPIPE,
  MOUSE_EDIT_DELETEPIPE,
  MOUSE_MEASURE_LINESTRING,
  MOUSE_MEASURE_POLYGON,
  MouseMode,
} from '@waterdesk/data/ui-types';
import { registerSystemUnits } from '@waterdesk/data/unit-system';
import {
  createView,
  getPlantAndPumpStationList,
  getPropDefines,
  getScadaNameList,
  getScadaQuotaList,
  getThemeSectionList,
} from '@waterdesk/request/create-map-data';
import {
  getAlertDefinitionList,
  getCurrentAlertList,
} from '@waterdesk/request/get-alert';
import { getLoginUserInfo } from '@waterdesk/request/get-user-info';
import { queryWarningDefineList } from '@waterdesk/request/get-warn-setting';
import { setPermissionList } from '@waterdesk/request/login';
import { getUserConfigValues } from '@waterdesk/request/user-config/get-user-config-values';
import dayjs from 'dayjs';
import { SagaIterator } from 'redux-saga';
import {
  all,
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { curDb, hostApp } from 'src/app/host-app';
import { APP_NAME } from 'src/config';
import { setThemeTokenToLocal, setThemeToLocal } from 'src/utils/tool';
import { dispatchLogActions } from '../dispatch-log';
import { dmaCustomerActions } from '../dma-customer';
import { highlightActions } from '../highlight';
import { leftWrapperActions } from '../left-wrapper';
import { MENU_TOOLS_SCENE } from '../left-wrapper/types';
import { scenesActions } from '../scenes';
import { selectCurrentSceneId, selectScenes } from '../scenes/selectors';
import { selectionActions } from '../selection';
import { themeActions } from '../theme';
import { selectTheme } from '../theme/selector';
import { timelineActions } from '../time-line';
import { selectTimelineDate } from '../time-line/selectors';
import { userConfigActions } from '../user-config';
import { baseActions } from '.';
import {
  BottomTabKey,
  PropertyChartActionArgs,
  PropertyDmaCustomerAction,
} from './types';

function* initializeMapViewSaga() {
  try {
    const mapView = hostApp().getMainMapView();
    const projection = hostApp().appConfig.mapProjection;
    const { mapZoomFactor } = hostApp().appConfig;
    if (mapView) {
      const scenes: SagaReturnType<typeof selectScenes> =
        yield select(selectScenes);
      const currentSceneId: SagaReturnType<typeof selectCurrentSceneId> =
        yield select(selectCurrentSceneId);
      const defaultTheme: SagaReturnType<typeof selectTheme> =
        yield select(selectTheme);
      const currentScene = getScene(scenes, currentSceneId);
      const invisibleLayers = getInvisibleLayerNames(currentScene);

      mapView.initialize(
        hostApp().viewId,
        mapView.curDb.viewExtent,
        invisibleLayers,
        defaultTheme,
        projection,
        mapZoomFactor,
      );
    }
  } catch (err) {
    console.log(err);
  }
}

function* initializeUserConfig() {
  const userConfigValues: SagaReturnType<typeof getUserConfigValues> =
    yield call(getUserConfigValues, 'userConfig', APP_NAME);

  if (userConfigValues.status === 'Success') {
    const defaultTheme: Theme | undefined = userConfigValues.themeName as Theme;
    if (defaultTheme) {
      setThemeToLocal(defaultTheme);
      yield put(
        themeActions.updateTheme({
          theme: defaultTheme,
        }),
      );
    }

    const { searchHistory } = userConfigValues;
    if (searchHistory) {
      yield put(
        userConfigActions.updateSearchHistory({
          searchHistory,
        }),
      );
    }
    const {
      labelTitle,
      labelIndicator,
      labelSimulation,
      labelMode,
      appLabelTitle,
      appLabelIndicator,
      appLabelSimulation,
      appLabelMode,
      chartWarnMark,
      chartShowMaxLimitation,
      chartShowMaxMinRange,
    } = userConfigValues;
    yield put(
      userConfigActions.updateLabelTitle({
        labelTitle: !!labelTitle,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateLabelIndicator({
        labelIndicator: !!labelIndicator,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateLabelSimulation({
        labelSimulation: !!labelSimulation,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateLabelMode({
        labelMode,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateAppLabelTitle({
        appLabelTitle: !!appLabelTitle,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateAppLabelIndicator({
        appLabelIndicator: !!appLabelIndicator,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateAppLabelSimulation({
        appLabelSimulation: !!appLabelSimulation,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateAppLabelMode({
        appLabelMode,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateChartWarnMark({
        chartWarnMark: !!chartWarnMark,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateMaxLimitation({
        chartShowMaxLimitation: !!chartShowMaxLimitation,
        initialize: true,
      }),
    );
    yield put(
      userConfigActions.updateShowRange({
        chartShowMaxMinRange: !!chartShowMaxMinRange,
        initialize: true,
      }),
    );
  }
}

function initializeAppConfig(appConfig: any) {
  hostApp().initializeAppConfig(appConfig);
}

function initializeSystemUnits(unitJson?: {}) {
  registerSystemUnits(unitJson);
}

function initializeProj4() {
  const projections = hostApp().appConfig.proj4Register;
  if (Array.isArray(projections)) {
    const configs = convertLegacyProjectionData(projections);
    registerProjections(configs);
  }
}

function* initializeDeviceAndIndicator(
  db: Database,
): SagaIterator<DeviceCollection | undefined> {
  const timelineDate: SagaReturnType<typeof selectTimelineDate> =
    yield select(selectTimelineDate);

  const [
    deviceResponse,
    plantsAndPumpStationsResponse,
    indicatorResponse,
    propertyResponse,
  ]: [
    SagaReturnType<typeof getScadaNameList>,
    SagaReturnType<typeof getPlantAndPumpStationList>,
    SagaReturnType<typeof getScadaQuotaList>,
    SagaReturnType<typeof getPropDefines>,
  ] = yield all([
    call(getScadaNameList, timelineDate),
    call(getPlantAndPumpStationList, timelineDate),
    call(getScadaQuotaList, timelineDate),
    call(getPropDefines, SCENE_ID_TYPE_ONLINE),
  ]);

  if (deviceResponse.status === 'Fail') {
    console.error(deviceResponse.errorMessage);
    return;
  }

  if (plantsAndPumpStationsResponse.status === 'Fail') {
    console.error(plantsAndPumpStationsResponse.errorMessage);
    return;
  }

  if (indicatorResponse.status === 'Fail') {
    console.error(indicatorResponse.errorMessage);
    return;
  }

  if (propertyResponse.status === 'Fail') {
    console.error(propertyResponse.errorMessage);
    return;
  }

  const devices: DeviceCollection = deviceResponse.devices as DeviceCollection;
  const indicatorObjects: IndicatorObjectCollection =
    deviceResponse.indicators as IndicatorObjectCollection;

  devices.initializeOverlayIndicators(
    indicatorResponse.deviceIndicators as {},
    indicatorResponse.indicatorTypes as IndicatorTypeCollection,
    indicatorObjects,
  );

  const plantsAndPumpStations =
    plantsAndPumpStationsResponse.plantsAndPumpStations as PlantsAndPumpStationsCollection;
  devices.initializePlantsAndPumpStations(plantsAndPumpStations);

  if (db.icons.size > 0) {
    devices.initializeIcons(db.icons);
  }

  db.initializeDevice(devices, propertyResponse.propertyInfos);
  db.initializeIndicator(indicatorObjects);
  // Todo: appConfig unitJson从getPropDefines接口分离
  db.initializePropertyInfos(
    propertyResponse.propertyInfos as Map<string, PropertyInfo>,
  );
  db.initializeFieldEnumMap(propertyResponse.fieldEnumMap);

  initializeAppConfig(propertyResponse.appConfig);

  initializeSystemUnits(propertyResponse.unitJson);

  // eslint-disable-next-line consistent-return
  return devices;
}

function* initializeScenes(
  sceneIdType: SceneIdType,
): SagaIterator<Array<Scene> | undefined> {
  const themeResponse: SagaReturnType<typeof getThemeSectionList> = yield call(
    getThemeSectionList,
    sceneIdType,
  );

  if (themeResponse.status === 'Fail') {
    console.error(themeResponse.errorMessage);
    return;
  }

  if (themeResponse.scenes) {
    const defaultSceneId = getDefaultSceneId(themeResponse.scenes);
    yield put(
      scenesActions.updateCurrentSceneId({
        sceneId: defaultSceneId,
      }),
    );
    yield put(
      leftWrapperActions.leftWrapperContainerTypeChanged({
        containerType: MENU_TOOLS_SCENE,
      }),
    );

    // eslint-disable-next-line consistent-return
    return themeResponse.scenes;
  }
}

function* initializeOnLineDataBase(
  actions: PayloadAction<{
    theme: Theme;
    sectionId: MapViewName;
  }>,
) {
  try {
    const { theme, sectionId } = actions.payload;
    const timelineDate: SagaReturnType<typeof selectTimelineDate> =
      yield select(selectTimelineDate);
    yield put(baseActions.updateStartLoading());

    const [viewResponse, scenes]: [
      SagaReturnType<typeof createView>,
      SagaReturnType<typeof initializeScenes>,
    ] = yield all([
      call(createView, theme),
      call(initializeScenes, SCENE_ID_TYPE_ONLINE),
      call(initializeUserConfig),
    ]);

    if (viewResponse.status === 'Fail') {
      console.error(viewResponse.errorMessage);
      return;
    }
    yield put(
      baseActions.updateViewId({
        viewId: viewResponse.viewId ?? '',
      }),
    );

    curDb().initializeDevice(
      curDb(undefined, false).getDeviceCollection(),
      undefined,
    );

    curDb().initializeIndicator(curDb(undefined, false).getIndicators());

    curDb().initializePropertyInfos(curDb(undefined, false).propertyInfos);

    curDb().updateFieldEnumMap(curDb(undefined, false).fieldEnumMap);

    const devices = curDb().getDeviceCollection();

    if (!devices) return;

    const icons = viewResponse.icons as Map<string, string>;
    devices.initializeIcons(icons);

    curDb().initializeIcon(icons);

    const layerData: LayerDataCollection =
      viewResponse.layerData as LayerDataCollection;
    layerData.initializeDevices(devices);
    const mapView = hostApp().getMapView(sectionId);
    if (!mapView) {
      return;
    }
    mapView.initializeData(layerData, viewResponse.extent ?? []);

    hostApp().viewId = viewResponse.viewId ?? '';

    initializeProj4();
    yield initializeMapViewSaga();

    if (scenes) {
      yield put(
        scenesActions.initializeScenes({
          scenes: refreshLayerData(scenes, layerData),
        }),
      );
    }

    yield put(
      baseActions.updateDeviceUpdateDate({
        date: timelineDate,
      }),
    );
    yield put(baseActions.updateEndLoading());
  } catch (err) {
    console.log(err);
  }
}

function* resetStateSaga() {
  yield put(baseActions.resetState());
  yield put(scenesActions.resetState());
  yield put(highlightActions.resetState());
  yield put(leftWrapperActions.resetState());
  yield put(timelineActions.resetState());
}

function* signOutResetSaga() {
  yield resetStateSaga();
  yield put(baseActions.resetNoticeWarnList());
}

function* propertyTrackSaga() {
  yield put(
    leftWrapperActions.leftWrapperChanged({
      open: true,
    }),
  );
  yield put(
    leftWrapperActions.leftWrapperContainerTypeChanged({
      containerType: TRACK_UP,
    }),
  );
}

function* propertyChartObjectChangedSaga(
  actions: PayloadAction<{
    arg: PropertyChartActionArgs;
    compareDevice?: IObjectItem;
  }>,
) {
  const { arg, compareDevice } = actions.payload;
  if (arg) {
    const { pinnedItem } = arg;
    const objects: IObjectItem[] = [];
    const selectionCollection = hostApp().getMainMapView()?.selectionCollection;
    objects.push(
      pinnedItem ?? (selectionCollection?.firstSelectedObject as IObjectItem),
    );
    if (compareDevice) {
      objects.push(compareDevice);
    }

    hostApp().getMainMapView()?.setChartObjects(objects);
    yield put(
      selectionActions.chartObjectChanged({
        items: objects.map((item) => ({
          objectId: item.id,
        })),
        indicatorType: arg.indicatorType,
        indicatorName: arg.indicatorName,
        vprop: arg.vprop,
        forecast: arg.forecast,
      }),
    );
  }

  yield put(
    baseActions.updateBottomTab({
      activeKey: BottomTabKey.CHARTS,
      type: 'ADD',
    }),
  );

  if (arg?.chartEndDate && arg?.chartStartDate) {
    yield put(
      baseActions.updateChartDate({
        startDate: dayjs(arg.chartStartDate).format('YYYY-MM-DD'),
        endDate: dayjs(arg.chartEndDate).format('YYYY-MM-DD'),
      }),
    );
  }
}

function* propertyDmaCustomerActionSaga(
  actions: PayloadAction<{
    arg: PropertyDmaCustomerAction;
  }>,
) {
  const { arg } = actions.payload;

  yield put(
    dmaCustomerActions.dmaCustomerVisible({
      open: arg.open,
    }),
  );
  yield put(
    dmaCustomerActions.dmaCustomerChanged({
      otype: arg.otype,
      oname: arg.oname,
    }),
  );
}

function updateMouseModeSaga(action: PayloadAction<{ mouseMode: MouseMode }>) {
  const { mouseMode } = action.payload;
  const mapContainer = hostApp().getMainMapView()?.map?.getViewport();
  if (!mapContainer) return;
  switch (mouseMode) {
    case MOUSE_MEASURE_LINESTRING:
    case MOUSE_MEASURE_POLYGON:
      mapContainer.className = 'ol-viewport cursorCell';
      break;
    case MOUSE_EDIT_ADDJUNCTION:
    case MOUSE_EDIT_ADDPIPE:
    case MOUSE_EDIT_DELETEPIPE:
      mapContainer.className = 'ol-viewport cursorCrosshair';
      break;
    default:
      mapContainer.className = 'ol-viewport';
      break;
  }
}

function* initializeDeviceAndIndicatorSeparate() {
  yield put(baseActions.updateStartLoading());
  yield initializeDeviceAndIndicator(curDb(undefined, false));
  yield put(baseActions.updateEndLoading());
}

function* initializeLayoutMain() {
  try {
    const [
      loginUserInfoResponse,
      warnTypeListResponse,
      alertDefinitionResponse,
      currentAlertResponse,
    ]: [
      SagaReturnType<typeof getLoginUserInfo>,
      SagaReturnType<typeof queryWarningDefineList>,
      SagaReturnType<typeof getAlertDefinitionList>,
      SagaReturnType<typeof getCurrentAlertList>,
      SagaReturnType<typeof initializeDeviceAndIndicatorSeparate>,
    ] = yield all([
      call(getLoginUserInfo, { appId: APP_NAME }),
      call(queryWarningDefineList),
      call(getAlertDefinitionList),
      call(getCurrentAlertList),
      call(initializeDeviceAndIndicatorSeparate),
    ]);

    const { globalConfig, permissionList, applicationId, userInfo } =
      loginUserInfoResponse;

    // request failed, still initial globalConfig
    yield put(
      userConfigActions.updateGlobalConfig({
        globalConfig,
      }),
    );

    setThemeTokenToLocal(globalConfig?.systemThemeConfig);

    yield put(
      userConfigActions.updateUserPermission({
        userPermission: permissionList,
      }),
    );
    // setting menu
    yield put(
      baseActions.updateMenuList({
        list: getMenuList(permissionList ?? []),
      }),
    );

    // setting permission
    setPermissionList(permissionList ?? []);

    if (applicationId) localStorage.setItem('application', applicationId);
    if (userInfo) localStorage.setItem('userInfo', JSON.stringify(userInfo));

    // setting warning Type
    yield put(
      baseActions.updateWarnTypeList({
        list: warnTypeListResponse.list ?? [],
      }),
    );

    yield put(
      baseActions.updateAlertDefinitionList({
        list: alertDefinitionResponse.list ?? [],
      }),
    );

    yield put(
      baseActions.updateCurrentAlertList({
        list: currentAlertResponse.list ?? [],
      }),
    );

    yield put(
      baseActions.updateLayoutMainLoading({
        loading: false,
      }),
    );

    yield put(dispatchLogActions.fetchPlantsAndPumpStations());
  } catch (err) {
    console.log('initializeLayoutMain error', err);
  }
}

export function* baseSaga() {
  yield takeLatest(
    baseActions.initializeOnLineDataBase,
    initializeOnLineDataBase,
  );
  yield takeLatest(baseActions.resetStateSaga, resetStateSaga);
  yield takeLatest(baseActions.signOutResetSaga, signOutResetSaga);
  yield takeLatest(baseActions.propertyTrackAction, propertyTrackSaga);
  yield takeLatest(
    baseActions.propertyChartAction,
    propertyChartObjectChangedSaga,
  );
  yield takeLatest(
    baseActions.propertyDmaCustomerAction,
    propertyDmaCustomerActionSaga,
  );
  yield takeLatest(baseActions.updateMouseMode, updateMouseModeSaga);
  yield takeLatest(
    baseActions.initializeDeviceAndIndicatorSaga,
    initializeDeviceAndIndicatorSeparate,
  );
  yield takeLatest(baseActions.initializeLayoutMainSaga, initializeLayoutMain);
}
