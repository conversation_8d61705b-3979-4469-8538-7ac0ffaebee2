/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => state?.base ?? initialState;

export const selectBase = createSelector([selectSlice], (state) => state);

export const selectLoading = createSelector(
  [selectSlice],
  (state) => state.loading,
);

export const selectDataInitialComplete = createSelector(
  [selectSlice],
  (state) => state.dataInitialComplete,
);

export const selectSiteName = createSelector(
  [selectSlice],
  (state) => state.siteName,
);

export const selectViewId = createSelector(
  [selectSlice],
  (state) => state.viewId,
);

export const selectMouseMode = createSelector(
  [selectSlice],
  (state) => state.mouseMode,
);

export const selectDeviceUpdateDate = createSelector(
  [selectSlice],
  (state) => state.deviceUpdateDate,
);

export const selectActiveRightPanel = createSelector(
  [selectSlice],
  (state) => state.activeRightPanel,
);

export const selectBottomOpenTabs = createSelector(
  [selectSlice],
  (state) => state.bottomOpenTabs,
);

export const selectActiveBottomTab = createSelector(
  [selectSlice],
  (state) => state.activeBottomTab,
);

export const selectBottomOpen = createSelector(
  [selectSlice],
  (state) => state.bottomOpen,
);

export const selectChartStartDate = createSelector(
  [selectSlice],
  (state) => state.chartStartDate,
);

export const selectChartEndDate = createSelector(
  [selectSlice],
  (state) => state.chartEndDate,
);

export const selectChartDateUpdateTime = createSelector(
  [selectSlice],
  (state) => state.chartDateUpdateTime,
);

export const selectNoticeWarnList = createSelector(
  [selectSlice],
  (state) => state.noticeWarnList,
);

export const selectNoticeValveList = createSelector(
  [selectSlice],
  (state) => state.noticeValveList,
);

export const selectNoticeValveGroupList = createSelector(
  [selectSlice],
  (state) => state.noticeValveGroupList,
);

export const selectWarnTypeList = createSelector(
  [selectSlice],
  (state) => state.warnTypeList,
);

export const selectHideFeatureTooltipImportant = createSelector(
  [selectSlice],
  (state) => state.hideFeatureTooltipImportant,
);

export const selectCurrentTimeDataChanged = createSelector(
  [selectSlice],
  (state) => state.currentTimeDataChanged,
);

export const selectLastQueryNoticeWarnTime = createSelector(
  [selectSlice],
  (state) => state.lastQueryNoticeWarnTime,
);

export const selectLastQueryNoticeValveTime = createSelector(
  [selectSlice],
  (state) => state.lastQueryNoticeValveTime,
);

export const selectLastQueryNoticeValveGroupTime = createSelector(
  [selectSlice],
  (state) => state.lastQueryNoticeValveGroupTime,
);

export const selectScadaDataConfig = createSelector(
  [selectSlice],
  (state) => state.scadaDataConfig,
);

export const selectScheduleShift = createSelector(
  [selectSlice],
  (state) => state.scheduleShift,
);

export const selectAppMode = createSelector(
  [selectSlice],
  (state) => state.appMode,
);

export const selectValveGroupList = createSelector(
  [selectSlice],
  (state) => state.valveGroupList,
);

export const selectValveGroupDates = createSelector(
  [selectSlice],
  (state) => state.valveGroupDates,
);

export const selectValveGroupLoading = createSelector(
  [selectSlice],
  (state) => state.valveGroupLoading,
);

export const selectMenuList = createSelector(
  [selectSlice],
  (state) => state.menuList,
);

export const selectLayoutMainLoading = createSelector(
  [selectSlice],
  (state) => state.layoutMainLoading,
);

export const selectAlertDefinitionList = createSelector(
  [selectSlice],
  (state) => state.alertDefinitionList,
);

export const selectCurrentAlertList = createSelector(
  [selectSlice],
  (state) => state.currentAlertList,
);

export const selectActiveChartWarnInfo = createSelector(
  [selectSlice],
  (state) => state.activeChartWarnInfo,
);
