/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AlertDefinition, AlertInstanceList } from '@waterdesk/data/alert';
import { AppMode } from '@waterdesk/data/app-config';
import { HandoverInfoList } from '@waterdesk/data/dispatch-log/handover';
import { MenuInfoItem } from '@waterdesk/data/menu-data';
import { IObjectItem } from '@waterdesk/data/object-item';
import { ScadaDataConfig } from '@waterdesk/data/otype-vprop-config-data';
import { MouseMode, RightPanelItem } from '@waterdesk/data/ui-types';
import {
  ValveOperationGroup,
  ValveOperationValue,
} from '@waterdesk/data/valve-manager/valve-manager-data';
import { WarnInfoItem, WarnInfoList } from '@waterdesk/data/warn';
import { WarnSettingList } from '@waterdesk/data/warn-setting';

export enum BottomTabKey {
  CHARTS = 'CHARTS',
  PUMP_STATUS = 'PUMP_STATUS',
  VALVE_MANAGEMENT = 'VALVE_MANAGEMENT',
  VALVE_GROUP = 'VALVE_GROUP',
  CLOSED_VALVES = 'CLOSED_VALVES',
  WARN = 'WARN',
  ISSUE_REPORT = 'ISSUE_REPORT',
  DEVICE_STATE_RECORDS = 'DEVICE_STATE_RECORDS',
  DEVICE_BAD_RECORDS = 'DEVICE_BAD_RECORDS',
  WORK_ORDER = 'WORK_ORDER',
  WATER_OUTAGE_INFO = 'WATER_OUTAGE_INFO',
  QUICK_SOLUTION_LIST = 'QUICK_SOLUTION_LIST',
  WORK_ORDER_REPAIR_DETAIL = 'WORK_ORDER_REPAIR_DETAIL',
  WATER_QUALITY = 'WATER_QUALITY',
  ALERT = 'ALERT',
}
/**
 * DELETE: 关闭指定的tab页
 *
 * ADD: 打开一个指定的tab页，并且打开底部抽屉框，并且将tabs的activeKey切换为当前tab
 *
 * TOGGLE: 将tabs的activeKey切换为当前tab
 *
 * CLEAN: 关闭所有的tab页，并且关闭底部抽屉框
 *
 * CLOSE: 隐藏抽屉框，tab页的状态不变
 *
 * OPEN: 打开一个指定的tab页，并且打开底部抽屉框，但是tabs的activeKey不会切换
 */
export type BottomHandleType =
  | 'DELETE'
  | 'ADD'
  | 'CLEAN'
  | 'TOGGLE'
  | 'CLOSE'
  | 'OPEN';

export interface BaseState {
  loading: boolean;
  dataInitialComplete: boolean;
  siteName: string;
  viewId: string | undefined;
  mouseMode: MouseMode;
  /** string as YYYY-MM-DD，设备更新时间初始必须为undefined */
  deviceUpdateDate: string | undefined;
  activeRightPanel: RightPanelItem | undefined;
  /** 底部抽屉开关 active:activeBottomTab  open: bottomOpen;  */
  bottomOpen: boolean;
  activeBottomTab: BottomTabKey | undefined;
  bottomOpenTabs: BottomTabKey[];
  /** 关阀事件列表 */
  valveGroupList: ValveOperationGroup[];
  /** 关阀事件查询日期 */
  valveGroupDates: [string, string];
  /** 曲线框的时间选择框 string as YYYY-MM-DD */
  valveGroupLoading: boolean;
  activeChartWarnInfo?: WarnInfoItem;
  chartStartDate?: string;
  chartEndDate?: string;
  chartDateUpdateTime?: number;
  noticeWarnList: WarnInfoList;
  warnTypeList: { [key: string]: WarnSettingList };
  alertDefinitionList: AlertDefinition[];
  currentAlertList: AlertInstanceList;
  lastQueryNoticeWarnTime?: string;
  noticeValveList: ValveOperationValue[];
  lastQueryNoticeValveTime?: string;
  noticeValveGroupList: ValveOperationGroup[];
  lastQueryNoticeValveGroupTime?: string;
  /** 隐藏feature tooltip, 如果为boolean类型，则优先级最高;(见:state.useConfig.labelMode) */
  hideFeatureTooltipImportant: boolean | undefined;
  /** curDb.currentDeviceTimeData是否更新 string as YYYY-MM-DD HH:mm:ss */
  currentTimeDataChanged: string | undefined;
  scadaDataConfig: ScadaDataConfig | undefined;
  appMode: AppMode;
  scheduleShift: HandoverInfoList | undefined;
  menuList: MenuInfoItem[];
  layoutMainLoading: boolean;
}

export type PropertyActionType =
  | 'propertyTrackAction'
  | 'propertyChartAction'
  | 'propertyDmaCustomerAction';

export interface PropertyChartActionArgs {
  indicatorType: string | undefined;
  indicatorName: string | undefined;
  vprop: string;
  forecast?: boolean;
  pinnedItem?: IObjectItem;
  chartStartDate?: string;
  chartEndDate?: string;
}

export interface PropertyDmaCustomerAction {
  open: boolean;
  otype: string;
  oname: string;
}
