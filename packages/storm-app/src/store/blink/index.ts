/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { WarnInfoList } from '@waterdesk/data/warn';
import { useInjectReducer } from 'src/utils/redux-injectors';
import { BlinkState } from './types';

export const initialState: BlinkState = {
  realtimeWarn: [],
  hoverBlinkObjectId: undefined,
};

export const slice = createSlice({
  name: 'blink',
  initialState,
  reducers: {
    updateRealtimeWarn(state, action: PayloadAction<{ list: WarnInfoList }>) {
      const { list } = action.payload;
      state.realtimeWarn = list;
    },
    updateHoverBlinkObject(state, action: PayloadAction<{ id?: string }>) {
      const { id } = action.payload;
      state.hoverBlinkObjectId = id;
    },
  },
});

export const { actions: blinkActions } = slice;

export const useBlinkSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
};
