/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { DispatchCommandConfiguration } from '@waterdesk/data/dispatch-command/create-command';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import dispatchLogSaga from './saga';
import { DispatchLogState } from './types';

export const initialState: DispatchLogState = {
  plantsAndPumpStations: {
    waterPlants: [],
    pumpStations: [],
    valveList: [],
  },
  needUpdate: false,
};

export const slice = createSlice({
  name: 'dispatchLog',
  initialState,
  reducers: {
    setNeedUpdate: (state, action: PayloadAction<{ needUpdate: boolean }>) => {
      const { needUpdate } = action.payload;
      state.needUpdate = needUpdate;
    },
    fetchPlantsAndPumpStations: () => {},
    updatePlantsAndPumpStations: (
      state,
      action: PayloadAction<{
        plantsAndPumpStations: DispatchCommandConfiguration;
      }>,
    ) => {
      const { plantsAndPumpStations } = action.payload;
      state.plantsAndPumpStations = plantsAndPumpStations;
    },
  },
});

export const { actions: dispatchLogActions } = slice;

export const useDispatchLogSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: dispatchLogSaga });
  return { actions: slice.actions };
};
