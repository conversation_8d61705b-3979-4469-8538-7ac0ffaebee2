/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getCurrentDispatchCommandConfiguration } from '@waterdesk/request/get-command';
import dayjs from 'dayjs';
import { call, put, SagaReturnType, takeLatest } from 'redux-saga/effects';
import { dispatchLogActions } from '.';

function* queryCurrentPlantsAndPumpStations() {
  const res: SagaReturnType<typeof getCurrentDispatchCommandConfiguration> =
    yield call(
      getCurrentDispatchCommandConfiguration,
      dayjs().format('YYYY-MM-DD HH:mm:00'),
    );

  if (res.status === 'Success') {
    yield put(
      dispatchLogActions.updatePlantsAndPumpStations({
        plantsAndPumpStations: res.values,
      }),
    );
  }
}

export default function* dispatchLogSaga() {
  yield takeLatest(
    dispatchLogActions.fetchPlantsAndPumpStations,
    queryCurrentPlantsAndPumpStations,
  );
}
