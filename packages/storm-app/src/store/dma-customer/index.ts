/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { useInjectReducer } from 'redux-injectors';
import { DmaCustomerState } from './types';

export const initialState: DmaCustomerState = {
  open: false,
  otype: '',
  oname: '',
};

export const slice = createSlice({
  name: 'dmaCustomer',
  initialState,
  reducers: {
    dmaCustomerVisible(
      state,
      action: PayloadAction<{
        open: boolean;
      }>,
    ) {
      const { open } = action.payload;
      return {
        ...state,
        open,
      };
    },
    dmaCustomerChanged(
      state,
      action: PayloadAction<{
        otype: string;
        oname: string;
      }>,
    ) {
      const { otype, oname } = action.payload;
      return {
        ...state,
        otype,
        oname,
      };
    },
  },
});

export const { actions: dmaCustomerActions } = slice;

export const useDmaCustomerSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  return { actions: slice.actions };
};
