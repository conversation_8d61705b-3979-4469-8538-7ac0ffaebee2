/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import dayjs from 'dayjs';
import { useInjectReducer } from 'src/utils/redux-injectors';
import {
  AddJunctionType,
  AddPipeType,
  DeletePipe,
  EditSelectionState,
} from './types';

export const initialState: EditSelectionState = {
  addJunction: undefined,
  addPipe: undefined,
  deletePipe: undefined,
  editObject: undefined,
  modifiedTime: undefined,
};

export const slice = createSlice({
  name: 'editSelection',
  initialState,
  reducers: {
    selectionChanged(
      state,
      action: PayloadAction<{
        addJunction?: AddJunctionType[];
        addPipe?: AddPipeType[];
      }>,
    ) {
      const { addJunction, addPipe } = action.payload;
      state.addJunction = addJunction;
      state.addPipe = addPipe;
    },
    deletePipeChanged(
      state,
      action: PayloadAction<{
        deletePipe?: DeletePipe[];
      }>,
    ) {
      const { deletePipe } = action.payload;
      state.deletePipe = deletePipe;
    },

    modifiedChanged(state) {
      state.modifiedTime = dayjs().valueOf();
    },

    cleanModifiedTime(state) {
      state.modifiedTime = undefined;
    },

    clearEdit(state) {
      state.addJunction = undefined;
      state.addPipe = undefined;
    },
  },
});

export const { actions: editSelectionActions } = slice;

export const useEditSelectionSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
};
