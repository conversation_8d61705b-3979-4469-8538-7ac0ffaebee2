/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

export interface AddJunctionType {
  type: 'ADDJUNCTION' | 'BREAKPIPE';
  junction: {
    shape: string;
    otype?: string;
    oname?: string;
  };
  pipe?: {
    otype: string;
    oname: string;
  };
}
export interface AddPipeType {
  startJunction: { otype: string; oname: string };
  endJunction: { otype: string; oname: string };
  shape: string;
  diameter: number;
}

export interface DeletePipe {
  oname: string;
  otype: string;
}
export interface EditSelectionState {
  addJunction: AddJunctionType[] | undefined;
  addPipe: AddPipeType[] | undefined;
  deletePipe: DeletePipe[] | undefined;
  editObject: (AddJunctionType | AddPipeType)[] | undefined;
  modifiedTime: number | undefined;
}
