/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import {
  HighlightDataItem,
  HighlightObject,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { hostApp } from 'src/app/host-app';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { reduxHighlightSaga } from './saga';
import { HighlightState } from './types';

export const initialState: HighlightState = {
  highlightObjects: undefined,
  hoverObject: undefined,
  themeConfig: undefined,
  echartsLines: undefined,
};

export const slice = createSlice({
  name: 'highlight',
  initialState,
  reducers: {
    updateHighlight(
      state,
      action: PayloadAction<{
        highlight: {
          [key: string]: HighlightDataItem[];
        };
        highlightMapName?: string;
      }>,
    ) {
      const { highlight, highlightMapName = MAP_VIEW_NAME_ONLINE } =
        action.payload;
      const oldHighlightDataByMap =
        state.highlightObjects?.[highlightMapName] ?? {};
      const generateHighlightData: {
        [key: string]: Array<HighlightObject>;
      } = {};
      Object.keys(highlight).forEach((highlightKey) => {
        generateHighlightData[highlightKey] = highlight[highlightKey].map(
          (item) => {
            const shapeType = getShapeType(item.shape);
            return {
              ...item,
              id: item.id ?? makeObjectId(item.otype, item.oname),
              shapeType: item.shapeType ?? shapeType,
            };
          },
        );
      });
      state.highlightObjects = {
        ...state.highlightObjects,
        [highlightMapName]: {
          ...oldHighlightDataByMap,
          ...generateHighlightData,
        },
      };
    },
    updateEchartsLines(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
    ) {},

    clearEchartsLines(state) {
      state.echartsLines = {};
    },
    /**
     *
     * @deprecated 计划删除，高亮themeConfig和高亮数据放在一起
     */
    updateThemeConfig(
      state,
      action: PayloadAction<{
        themeConfig: { [key: string]: LegendGroupData | undefined } | undefined;
      }>,
    ) {
      const { themeConfig } = action.payload;

      if (typeof themeConfig === 'undefined') {
        state.themeConfig = undefined;
      } else {
        state.themeConfig = { ...state.themeConfig, ...themeConfig };
      }
    },
    /**
     *
     * @deprecated 计划删除，不需要了
     */
    displayHighlight(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{
        displayLabel?: boolean;
      }>,
    ) {},
    clearHighlight(
      state,
      action: PayloadAction<{ highlightLayerName?: string | string[] }>,
    ) {
      const mapViews = hostApp().getMapViews();
      if (action.payload) {
        const { highlightLayerName } = action.payload;
        mapViews?.forEach((mapView) => {
          if (mapView) {
            if (
              typeof highlightLayerName === 'string' ||
              typeof highlightLayerName === 'undefined'
            ) {
              mapView.clearHighlight(highlightLayerName);
            } else {
              highlightLayerName?.forEach((item) => {
                mapView.clearHighlight(item);
              });
            }
          }
        });
      }

      state.highlightObjects = undefined;
    },

    clearThemeConfig(
      state,
      action: PayloadAction<{ highlightLayerName?: string | string[] }>,
    ) {
      const { highlightLayerName } = action.payload;
      let newThemeConfig:
        | { [key: string]: LegendGroupData | undefined }
        | undefined = { ...state.themeConfig };
      if (newThemeConfig) {
        if (typeof highlightLayerName === 'string') {
          delete newThemeConfig[highlightLayerName];
        } else {
          newThemeConfig = undefined;
        }
      }
      state.themeConfig = newThemeConfig;
    },
    resetHighlight() {},
    resetState() {
      return initialState;
    },
    updateHoverObject(
      state,
      action: PayloadAction<{
        hoverObject?: { oname: string; otype: string; shape: string };
      }>,
    ) {
      const { hoverObject } = action.payload;
      state.hoverObject = hoverObject;
    },
  },
});

export const { actions: highlightActions } = slice;

export const useHighlightSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxHighlightSaga });
};
