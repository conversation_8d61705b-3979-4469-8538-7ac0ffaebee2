/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { HighlightObject } from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';
import ModelObject from '@waterdesk/data/model-object';
import { getColorAndValueByThemeConfig } from '@waterdesk/data/track-data';
import { resetHighlight } from '@waterdesk/request/track/get-track-data';
import { call, SagaReturnType, select, takeLatest } from 'redux-saga/effects';
import { curDb, hostApp } from 'src/app/host-app';
import { selectViewId } from '../base/selectors';
import { highlightActions } from '.';
import selectHighlight, { selectHighlightThemeConfig } from './selectors';

function setHighlightTheme(
  highlightObjects: { [key: string]: HighlightObject[] },
  themes:
    | {
        [key: string]: LegendGroupData | undefined;
      }
    | undefined,
): { [key: string]: HighlightObject[] } {
  if (themes) {
    const newHighlightObject: { [key: string]: HighlightObject[] } = {};
    Object.keys(highlightObjects).forEach((key) => {
      const themeConfigs = themes[key];
      newHighlightObject[key] = highlightObjects[key];
      if (themeConfigs) {
        newHighlightObject[key] = highlightObjects[key]?.map((item) => {
          const tempHighlightData = { ...item };
          const value: number | undefined = item[
            themeConfigs.name as keyof HighlightObject
          ] as number | undefined;
          const colorAndValue = getColorAndValueByThemeConfig(
            value,
            themeConfigs,
          );
          tempHighlightData.highlightColor = colorAndValue.color;
          if (tempHighlightData.highlightShowMark) {
            tempHighlightData.highlightText = colorAndValue.valueAndUnit;
          }
          return tempHighlightData;
        });
      }
    });
    return newHighlightObject;
  }
  return highlightObjects;
}

function* displayHighlightSaga() {
  try {
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      for (let i = 0; mapViews.length > i; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          const highlightObjects: SagaReturnType<typeof selectHighlight> =
            yield select(selectHighlight);
          const highlightData = highlightObjects?.[mapView.mapViewName];
          if (highlightData) {
            const themes: SagaReturnType<typeof selectHighlightThemeConfig> =
              yield select(selectHighlightThemeConfig);
            const data = setHighlightTheme(highlightData, themes);
            mapView.displayHighlight(data);
          }
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

function* resetHighlightSaga() {
  try {
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      for (let i = 0; mapViews.length > i; i += 1) {
        const mapView = mapViews[i];
        const viewId: SagaReturnType<typeof selectViewId> =
          yield select(selectViewId);
        if (mapView && viewId) {
          const result: SagaReturnType<typeof resetHighlight> = yield call(
            resetHighlight,
            mapView.dateTime?.format('YYYY-MM-DD HH:mm:ss'),
            mapView.getViewId(),
          );
          if (result.status === 'Success') {
            mapView.redraw();
          }
          mapView.trackHighlightObject = undefined;
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

function* displayEchartsLinesSaga() {
  try {
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      for (let i = 0; mapViews.length > i; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          const themes: SagaReturnType<typeof selectHighlightThemeConfig> =
            yield select(selectHighlightThemeConfig);
          mapView.displayEchartsLines(themes);
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

function clearEchartsLinesSaga() {
  try {
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      for (let i = 0; mapViews.length > i; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          mapView.clearEchartsLines();
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

function displayHoverObject(
  action: PayloadAction<{
    hoverObject?: { oname: string; otype: string; shape: string };
  }>,
) {
  try {
    const { hoverObject } = action.payload;
    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      mapViews.forEach((mapView) => {
        if (!hoverObject) {
          mapView?.clearHoverHighlight();
          return;
        }
        const device =
          curDb().getDevice(hoverObject.otype, hoverObject.oname) ??
          new ModelObject(
            hoverObject.otype,
            hoverObject.oname,
            hoverObject.shape,
            curDb(),
          );
        if (mapView && device) {
          mapView.displayHoverHighlight(device);
        }
      });
    }
  } catch (err) {
    console.log(err);
  }
}

export function* reduxHighlightSaga() {
  yield takeLatest(highlightActions.updateHighlight, displayHighlightSaga);
  yield takeLatest(
    highlightActions.updateEchartsLines,
    displayEchartsLinesSaga,
  );
  yield takeLatest(highlightActions.updateThemeConfig, displayHighlightSaga);
  yield takeLatest(highlightActions.resetHighlight, resetHighlightSaga);
  yield takeLatest(highlightActions.clearEchartsLines, clearEchartsLinesSaga);
  yield takeLatest(highlightActions.updateHoverObject, displayHoverObject);
}
