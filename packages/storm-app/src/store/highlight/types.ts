/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  HighlightObjectItem,
  HighlightObjects,
} from '@waterdesk/data/highlight-object';
import { LegendGroupData } from '@waterdesk/data/legend-data';

/* --- STATE --- */
export interface HighlightState {
  highlightObjects: HighlightObjects | undefined;
  themeConfig: { [key: string]: LegendGroupData | undefined } | undefined;
  hoverObject: { oname: string; otype: string; shape: string } | undefined;
  echartsLines: HighlightObjectItem | undefined;
}
