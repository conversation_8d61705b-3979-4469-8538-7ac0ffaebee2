/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { SidebarMenuType } from '@waterdesk/data/sidebar-menu-data';
import { useInjectReducer } from 'src/utils/redux-injectors';
import {
  ContainerType,
  LeftWrapperState,
  ModalName,
  sidebarMenuToModalName,
} from './types';

export const initialState: LeftWrapperState = {
  open: false,
  containerType: undefined,
  modals: Object.values(ModalName).reduce(
    (prev, currKey) => ({ ...prev, [currKey as ModalName]: false }),
    {},
  ) as Record<ModalName, boolean>,
  workOrderRemindCount: 0,
  refreshWorkOrderCount: undefined,
};

export const slice = createSlice({
  name: 'leftWrapper',
  initialState,
  reducers: {
    leftWrapperChanged(
      state,
      action: PayloadAction<{
        open: boolean;
      }>,
    ) {
      const { open } = action.payload;
      state.open = open;
    },
    leftWrapperContainerTypeChanged(
      state,
      action: PayloadAction<{
        containerType: ContainerType | undefined;
      }>,
    ) {
      const { containerType } = action.payload;
      state.containerType = containerType;
    },
    openModal(state, action: PayloadAction<{ modal: SidebarMenuType }>) {
      const { modal } = action.payload;
      const modalName = sidebarMenuToModalName[modal];
      if (modalName && state.modals) {
        state.modals[modalName] = true;
      }
    },
    closeModal(state, action: PayloadAction<{ modal: SidebarMenuType }>) {
      const { modal } = action.payload;
      const modalName = sidebarMenuToModalName[modal];
      if (modalName && state.modals) {
        state.modals[modalName] = false;
      }
    },
    resetState() {
      return initialState;
    },
    updateWorkOrderRemindCount(
      state,
      action: PayloadAction<{ count: number }>,
    ) {
      state.workOrderRemindCount = action.payload.count;
    },
    updateRefreshWorkOrderCount(
      state,
      action: PayloadAction<{ refresh: (() => void) | undefined }>,
    ) {
      state.refreshWorkOrderCount = action.payload.refresh;
    },
  },
});

export const { actions: leftWrapperActions } = slice;

export const useLeftWrapperSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
};
