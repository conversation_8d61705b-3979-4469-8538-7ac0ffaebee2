/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => {
  if (state?.leftWrapper) return state.leftWrapper;
  return initialState;
};

export const selectBase = createSelector([selectSlice], (state) => state);

export const selectLeftWrapper = createSelector(
  [selectSlice],
  (state) => state.open,
);

export const selectLeftContainerTypeWrapper = createSelector(
  [selectSlice],
  (state) => state.containerType,
);

export const selectModalsOpen = createSelector(
  [selectSlice],
  (state) => state.modals,
);

export const selectWorkOrderRemindCount = createSelector(
  [selectSlice],
  (state) => state.workOrderRemindCount,
);

export const selectRefreshWorkOrderCount = createSelector(
  [selectSlice],
  (state) => state.refreshWorkOrderCount,
);
