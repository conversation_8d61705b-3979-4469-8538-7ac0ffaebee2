/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SidebarMenuType } from '@waterdesk/data/sidebar-menu-data';
import { TrackType } from '@waterdesk/data/track-data';

/**
 * scene: 场景;
 *
 *
 * valveAnalysis: 关阀分析
 *
 * burstPipeFlushing: 爆管分析
 *
 * schedulingAnalysis: 调度
 *
 * demandStatistics: 水量统计
 *
 * TrackType 上下游追踪
 */
export type ContainerType =
  | 'scene'
  | TrackType
  | SidebarMenuType.VALVE_ANALYSIS
  | SidebarMenuType.BURST_PIPE_FLUSHING
  | SidebarMenuType.SCHEDULING_ANALYSIS
  | SidebarMenuType.WATER_VOLUME_STATISTICS
  | SidebarMenuType.SOLUTION_ANALYSIS
  | SidebarMenuType.BATCH_QUERY
  | SidebarMenuType.ENERGY_ANALYSIS;

/** 场景 */
export const MENU_TOOLS_SCENE: ContainerType = 'scene';
/** 关阀分析 */
export const MENU_TOOLS_VALVE: ContainerType = SidebarMenuType.VALVE_ANALYSIS;
/** 爆管分析 */
export const MENU_TOOLS_BURST_PIPE: ContainerType =
  SidebarMenuType.BURST_PIPE_FLUSHING;
/** 调度 */
export const MENU_TOOLS_SCHEDULE: ContainerType =
  SidebarMenuType.SCHEDULING_ANALYSIS;
/** 水量统计 */
export const MENU_TOOLS_DEMAND_STATISTICS: ContainerType =
  SidebarMenuType.WATER_VOLUME_STATISTICS;

export enum ModalName {
  SOLUTION_LIST = 'solutionListModalOpen',
  CREATE_SOLUTION = 'createSolutionModalOpen',
  SIMULATION_LOG = 'simulationLogModalOpen',
  COMPARE_SOLUTION = 'compareSolutionModalOpen',
  DEVICE_EVALUATION = 'deviceEvaluationModalOpen',
  DOWNLOAD_SCADA_DATA = 'downloadScadaDataModalOpen',
  DOWNLOAD_WATERAGE = 'downloadWaterAgeDataModalOpen',
  SMART_VALVE_MANAGEMENT = 'smartValveManagementModalOpen',
  SOLUTION_SIMULATION = 'simulationSolutionModalOpen',
}

export interface LeftWrapperState {
  open: boolean;
  containerType: ContainerType | undefined;
  modals: Record<ModalName, boolean>;
  workOrderRemindCount: number;
  refreshWorkOrderCount: (() => void) | undefined;
}

export const sidebarMenuToModalName: {
  [key in SidebarMenuType]?: ModalName;
} = {
  [SidebarMenuType.SOLUTION_CREATE]: ModalName.CREATE_SOLUTION,
  [SidebarMenuType.SOLUTION_LIST]: ModalName.SOLUTION_LIST,
  [SidebarMenuType.SOLUTION_COMPARE]: ModalName.COMPARE_SOLUTION,
  [SidebarMenuType.DEVICE_EVALUATION]: ModalName.DEVICE_EVALUATION,
  [SidebarMenuType.SIMULATION_LOG]: ModalName.SIMULATION_LOG,
  [SidebarMenuType.DOWNLOAD_SCADA_DATA]: ModalName.DOWNLOAD_SCADA_DATA,
  [SidebarMenuType.DOWNLOAD_WATERAGE]: ModalName.DOWNLOAD_WATERAGE,
  [SidebarMenuType.SMART_VALVE_MANAGEMENT]: ModalName.SMART_VALVE_MANAGEMENT,
  [SidebarMenuType.SOLUTION_SIMULATION]: ModalName.SOLUTION_SIMULATION,
};
