/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

/* eslint-disable @typescript-eslint/no-unused-vars */
import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { reduxLegendSaga } from './saga';
import { LegendState } from './types';

export const initialState: LegendState = {
  code: 0,
};

export const slice = createSlice({
  name: 'legend',
  initialState,
  reducers: {
    legendChanged(
      state,
      action: PayloadAction<{
        code: number;
      }>,
    ) {
      const { code } = action.payload;
      return {
        ...state,
        code,
      };
    },
    triggerLegendChanged(
      _state,
      _action: PayloadAction<{
        legendData:
          | {
              legendName: string;
              legendId: string | number;
              visible: boolean;
            }
          | undefined;
      }>,
    ) {},
  },
});

export const { actions: legendActions } = slice;

export const useLegendSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxLegendSaga });
};
