/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import {
  updateCurrentDeviceColorData,
  updateCurrentLegendData,
} from '@waterdesk/data/legend-data';
import { setLegendVisible } from '@waterdesk/request/set-legend-visible';
import dayjs from 'dayjs';
import { all, call, put, SagaReturnType, takeLatest } from 'redux-saga/effects';
import { hostApp } from 'src/app/host-app';
import { legendActions } from '.';

function* triggerLegendChangedSaga(
  action: PayloadAction<{
    legendData:
      | {
          legendName: string;
          legendId: string | number;
          visible: boolean;
        }
      | undefined;
  }>,
) {
  const { legendData } = action.payload;
  if (legendData) {
    const { legendName, legendId, visible } = legendData;

    const mapViews = hostApp().getMapViews();
    if (mapViews) {
      const setLegendRequest = [];
      for (let i = 0; mapViews.length > i; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          setLegendRequest.push(
            call(
              setLegendVisible,
              dayjs().format('YYYY-MM-DD HH:mm:ss'),
              legendName,
              legendId,
              visible,
              mapView.getViewId(),
            ),
          );
        }
      }
      const result: SagaReturnType<typeof setLegendVisible>[] =
        yield all(setLegendRequest);

      if (result[0].status === 'Success') {
        if (result[0].deviceColorData) {
          updateCurrentDeviceColorData(result[0].deviceColorData);
        }
        if (result[0].legendData) {
          updateCurrentLegendData(result[0].legendData);

          yield put(
            legendActions.legendChanged({
              code: dayjs().valueOf(),
            }),
          );
        }
        for (let i = 0; mapViews.length > i; i += 1) {
          const mapView = mapViews[i];
          if (mapView) {
            mapView?.redraw();
          }
        }
      }
    }
  }
}

export function* reduxLegendSaga() {
  yield takeLatest(
    legendActions.triggerLegendChanged,
    triggerLegendChangedSaga,
  );
}
