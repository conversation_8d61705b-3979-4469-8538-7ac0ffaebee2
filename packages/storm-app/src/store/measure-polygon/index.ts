/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { useInjectReducer } from 'redux-injectors';
import { MeasurePolygonState } from './types';

export const initialState: MeasurePolygonState = {
  polygon: [],
};

export const slice = createSlice({
  name: 'measurePolygon',
  initialState,
  reducers: {
    clearPolygon(state) {
      return { ...state, polygon: [] };
    },

    updatePolygon(
      state,
      action: PayloadAction<{
        polygon: number[][];
      }>,
    ) {
      const { polygon } = action.payload;
      return {
        ...state,
        polygon,
      };
    },
  },
});

export const { actions: measurePolygonActions } = slice;

export const useMeasurePolygonSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  return { actions: slice.actions };
};
