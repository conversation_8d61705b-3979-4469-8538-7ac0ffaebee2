/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ValveAnalysisConfig } from '@waterdesk/data/app-config';
import {
  AnalysisObject,
  AnalysisObjects,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { MessageInstance } from 'antd/es/message/interface';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { reduxQuickAnalysisSaga } from './saga';
import { QuickAnalysisState, ValveAnalysisSourceType } from './types';

export const initialState: QuickAnalysisState = {
  valveAnalysisObjects: {
    closeList: [],
    openList: [],
  },
  valveAnalysisTaskId: undefined,
  valveSelectable: true,
  valveAnalysisTime: undefined,
  valveGroupId: undefined,
  originalValveAnalysisResult: undefined,
  valveAnalysisSource: undefined,
  quickSolutionTitle: undefined,
  quickSolutionRefresh: undefined,
  pendingSolutionCount: 0,
  refreshPendingSolutionCount: undefined,
};

export const slice = createSlice({
  name: 'quickAnalysis',
  initialState,
  reducers: {
    updateValveAnalysisObjects(
      state,
      action: PayloadAction<{ objects: AnalysisObjects }>,
    ) {
      state.valveAnalysisObjects = action.payload.objects;
    },
    updateValveAnalysisTime(
      state,
      action: PayloadAction<{ time: string | undefined }>,
    ) {
      state.valveAnalysisTime = action.payload.time;
    },
    updateValveAnalysisTaskId(
      state,
      action: PayloadAction<{ taskId: string | undefined }>,
    ) {
      state.valveAnalysisTaskId = action.payload.taskId;
    },
    updateValveSelectable(
      state,
      action: PayloadAction<{ selectable: boolean }>,
    ) {
      state.valveSelectable = action.payload.selectable;
    },
    updateValveGroupId(
      state,
      action: PayloadAction<{ id: string | undefined }>,
    ) {
      state.valveGroupId = action.payload.id;
    },
    updateValveAnalysisSource(
      state,
      action: PayloadAction<{
        source: ValveAnalysisSourceType | undefined;
      }>,
    ) {
      state.valveAnalysisSource = action.payload.source;
    },
    updateQuickSolutionTitle(
      state,
      action: PayloadAction<{
        title: string | undefined;
      }>,
    ) {
      state.quickSolutionTitle = action.payload.title;
    },
    updateOriginalValveAnalysisResult(
      state,
      action: PayloadAction<{ result: { [index: string]: any } | undefined }>,
    ) {
      state.originalValveAnalysisResult = action.payload.result;
    },
    openValveGroupAnalysis(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _actions: PayloadAction<{
        groupId: string;
        closeList: AnalysisObject[];
        time: string;
        analysisConfig: ValveAnalysisConfig | undefined;
      }>,
    ) {},
    closeValveAnalysisModal() {},
    openValveImpact(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{ impact: { [index: string]: any } }>,
    ) {},
    openQuickValveResult(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{
        result: { [index: string]: any };
        params: AnalysisObjects;
        title: string;
        time: string;
      }>,
    ) {},
    fetchValveAnalysis(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _actions: PayloadAction<{
        analysisConfig: ValveAnalysisConfig | undefined;
        messageApi?: MessageInstance;
        analysisTime: string;
      }>,
    ) {},
    updateQuickSolutionRefresh(
      state,
      action: PayloadAction<{ refresh: (() => void) | undefined }>,
    ) {
      state.quickSolutionRefresh = action.payload.refresh;
    },
    updatePendingSolutionCount(
      state,
      action: PayloadAction<{ count: number }>,
    ) {
      state.pendingSolutionCount = action.payload.count;
    },
    updateRefreshPendingSolution(
      state,
      action: PayloadAction<{ refresh: (() => void) | undefined }>,
    ) {
      state.refreshPendingSolutionCount = action.payload.refresh;
    },
  },
});

export const { actions: quickAnalysisActions } = slice;

export const useQuickAnalysisSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxQuickAnalysisSaga });
};
