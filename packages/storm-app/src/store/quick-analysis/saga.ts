/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { ValveAnalysisConfig } from '@waterdesk/data/app-config';
import { makeObjectId } from '@waterdesk/data/object-item';
import {
  AnalysisObject,
  AnalysisObjects,
} from '@waterdesk/data/quick-analysis/quick-analysis-data';
import { getAnalysisData } from '@waterdesk/request/get-valve-analysis-data';
import { MessageInstance } from 'antd/es/message/interface';
import {
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { hostApp } from 'src/app/host-app';
import { baseActions } from '../base';
import { leftWrapperActions } from '../left-wrapper';
import { MENU_TOOLS_VALVE } from '../left-wrapper/types';
import { quickAnalysisActions } from '.';
import { selectValveAnalysisObjects } from './selectors';

function* valveAnalysis(
  actions: PayloadAction<{
    analysisConfig: ValveAnalysisConfig | undefined;
    messageApi?: MessageInstance;
    analysisTime: string;
  }>,
) {
  const { analysisConfig, messageApi, analysisTime } = actions.payload;
  const analysisObjects: SagaReturnType<typeof selectValveAnalysisObjects> =
    yield select(selectValveAnalysisObjects);
  if (!analysisConfig) return;
  const count =
    analysisObjects.openList.length + analysisObjects.closeList.length;
  if (count < 1) {
    messageApi?.warning('请至少选择一个对象再分析!');
    return;
  }
  yield put(
    quickAnalysisActions.updateValveSelectable({
      selectable: false,
    }),
  );

  const res: SagaReturnType<typeof getAnalysisData> = yield call(
    getAnalysisData,
    analysisObjects,
    analysisTime,
  );
  if (res.status === 'Success' && res.taskId) {
    yield put(
      quickAnalysisActions.updateValveAnalysisTaskId({
        taskId: res.taskId,
      }),
    );
  }
}

function* openValveGroupAnalysisSaga(
  actions: PayloadAction<{
    groupId: string;
    closeList: AnalysisObject[];
    time: string;
    analysisConfig: ValveAnalysisConfig | undefined;
  }>,
) {
  const { groupId, closeList, time, analysisConfig } = actions.payload;

  yield put(
    quickAnalysisActions.updateValveGroupId({
      id: groupId,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisSource({
      source: 'valveGroup',
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisObjects({
      objects: {
        closeList,
        openList: [],
      },
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisTime({
      time,
    }),
  );
  yield put(
    quickAnalysisActions.updateOriginalValveAnalysisResult({
      result: undefined,
    }),
  );

  // hide Feature Tooltip
  yield put(
    baseActions.updateHideFeatureTooltipImportant({
      visible: false,
    }),
  );
  hostApp().redrawMapView();

  // open valve analysis drawer
  yield put(
    leftWrapperActions.leftWrapperContainerTypeChanged({
      containerType: MENU_TOOLS_VALVE,
    }),
  );
  yield put(
    leftWrapperActions.leftWrapperChanged({
      open: true,
    }),
  );

  yield put(
    quickAnalysisActions.fetchValveAnalysis({
      analysisConfig,
      analysisTime: time,
    }),
  );
}

function* openValveImpactSaga(
  actions: PayloadAction<{
    impact: { [index: string]: any };
  }>,
) {
  const { impact } = actions.payload;
  yield put(
    quickAnalysisActions.updateOriginalValveAnalysisResult({
      result: impact,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisTime({
      time: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveGroupId({
      id: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisSource({
      source: 'valveGroup',
    }),
  );

  // hide Feature Tooltip
  yield put(
    baseActions.updateHideFeatureTooltipImportant({
      visible: false,
    }),
  );
  hostApp().redrawMapView();

  // open left wrapper drawer
  yield put(
    leftWrapperActions.leftWrapperContainerTypeChanged({
      containerType: MENU_TOOLS_VALVE,
    }),
  );
  yield put(
    leftWrapperActions.leftWrapperChanged({
      open: true,
    }),
  );
}
function* openQuickValveResultSaga(
  actions: PayloadAction<{
    result: { [index: string]: any };
    title: string;
    params: AnalysisObjects;
    time: string;
  }>,
) {
  const { result, title, params, time } = actions.payload;
  yield put(
    quickAnalysisActions.updateValveSelectable({
      selectable: false,
    }),
  );
  yield put(
    quickAnalysisActions.updateOriginalValveAnalysisResult({
      result,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisTime({
      time,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisSource({
      source: 'quickSolution',
    }),
  );
  yield put(
    quickAnalysisActions.updateQuickSolutionTitle({
      title,
    }),
  );
  const objects: AnalysisObjects = {
    closeList: [],
    openList: [],
  };
  Object.entries(params).forEach((f) => {
    const [key, list] = f as ['closeList' | 'openList', AnalysisObject[]];
    objects[key] = list.map((m) => ({
      ...m,
      key: m.key ?? makeObjectId(m.otype, m.oname),
    }));
  });
  yield put(
    quickAnalysisActions.updateValveAnalysisObjects({
      objects,
    }),
  );

  // hide Feature Tooltip
  yield put(
    baseActions.updateHideFeatureTooltipImportant({
      visible: false,
    }),
  );
  hostApp().redrawMapView();

  // open left wrapper drawer
  yield put(
    leftWrapperActions.leftWrapperContainerTypeChanged({
      containerType: MENU_TOOLS_VALVE,
    }),
  );
  yield put(
    leftWrapperActions.leftWrapperChanged({
      open: true,
    }),
  );
}

function* closeValveAnalysisSaga() {
  yield put(
    quickAnalysisActions.updateOriginalValveAnalysisResult({
      result: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisTime({
      time: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisSource({
      source: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveGroupId({
      id: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateQuickSolutionTitle({
      title: undefined,
    }),
  );
  yield put(
    quickAnalysisActions.updateValveAnalysisObjects({
      objects: {
        openList: [],
        closeList: [],
      },
    }),
  );
}

export function* reduxQuickAnalysisSaga() {
  yield takeLatest(quickAnalysisActions.fetchValveAnalysis, valveAnalysis);
  yield takeLatest(quickAnalysisActions.openValveImpact, openValveImpactSaga);
  yield takeLatest(
    quickAnalysisActions.openQuickValveResult,
    openQuickValveResultSaga,
  );
  yield takeLatest(
    quickAnalysisActions.closeValveAnalysisModal,
    closeValveAnalysisSaga,
  );
  yield takeLatest(
    quickAnalysisActions.openValveGroupAnalysis,
    openValveGroupAnalysisSaga,
  );
}
