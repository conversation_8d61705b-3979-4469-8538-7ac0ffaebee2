/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => {
  if (state?.quickAnalysis) return state.quickAnalysis;
  return initialState;
};

export const selectValveAnalysisObjects = createSelector(
  [selectSlice],
  (state) => state.valveAnalysisObjects,
);

export const selectValveAnalysisTime = createSelector(
  [selectSlice],
  (state) => state.valveAnalysisTime,
);

export const selectValveAnalysisTaskId = createSelector(
  [selectSlice],
  (state) => state.valveAnalysisTaskId,
);

export const selectValveSelectable = createSelector(
  [selectSlice],
  (state) => state.valveSelectable,
);

export const selectValveGroupId = createSelector(
  [selectSlice],
  (state) => state.valveGroupId,
);

export const selectValveAnalysisSource = createSelector(
  [selectSlice],
  (state) => state.valveAnalysisSource,
);

export const selectOriginalValveAnalysisResult = createSelector(
  [selectSlice],
  (state) => state.originalValveAnalysisResult,
);

export const selectQuickSolutionTitle = createSelector(
  [selectSlice],
  (state) => state.quickSolutionTitle,
);

export const selectQuickSolutionRefresh = createSelector(
  [selectSlice],
  (state) => state.quickSolutionRefresh,
);

export const selectPendingSolutionCount = createSelector(
  [selectSlice],
  (state) => state.pendingSolutionCount,
);

export const selectRefreshPendingSolutionCount = createSelector(
  [selectSlice],
  (state) => state.refreshPendingSolutionCount,
);
