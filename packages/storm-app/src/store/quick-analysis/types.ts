/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { AnalysisObjects } from '@waterdesk/data/quick-analysis/quick-analysis-data';

/**
 * valveGroup 管阀维修
 * quickSolution 快速方案
 */
export type ValveAnalysisSourceType = 'valveGroup' | 'quickSolution';

export interface QuickAnalysisState {
  /** 阀门分析 - 阀门列表 */
  valveAnalysisObjects: AnalysisObjects;
  valveAnalysisTaskId: string | undefined;
  valveSelectable: boolean;
  valveAnalysisSource: ValveAnalysisSourceType | undefined;
  valveGroupId: string | undefined;
  originalValveAnalysisResult: { [index: string]: any } | undefined;
  /** 阀门分析时间， 为空是默认取时间轴时间  string as YYYY-MM-DD HH:mm:ss */
  valveAnalysisTime: string | undefined;
  quickSolutionTitle: string | undefined;
  /** 快速方案列表刷新方法 */
  quickSolutionRefresh: (() => void) | undefined;
  /** 未审核快速方案数量 */
  pendingSolutionCount: number;
  /** 未审核快速方案数量刷新 */
  refreshPendingSolutionCount: (() => void) | undefined;
}
