/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ThemeState } from '@waterdesk/core/store';
import { BaseState } from 'src/store/base/types';
import { AppData } from './app-data/types';
import { BlinkState } from './blink/types';
import { DispatchLogState } from './dispatch-log/types';
import { DmaCustomerState } from './dma-customer/types';
import { EditSelectionState } from './edit-selection/types';
import { HighlightState } from './highlight/types';
import { LeftWrapperState } from './left-wrapper/types';
import { LegendState } from './legend/types';
import { MeasurePolygonState } from './measure-polygon/types';
import { QuickAnalysisState } from './quick-analysis/types';
import { ScenesState } from './scenes/types';
import { ScheduleLogState } from './schedule-log/types';
import { SchedulingState } from './scheduling/types';
import { SelectionState } from './selection/types';
import { ShiftScheduleState } from './shift-schedule/types';
import { SolutionState } from './solution/types';
import { TimelineState } from './time-line/types';
import { UserConfigState } from './user-config/types';
import { ValveEditorState } from './valve-editor/types';

// [IMPORT NEW CONTAINER STATE ABOVE] < Needed for generating containers seamlessly

/*
  Because the redux-injectors injects your reducers asynchronously somewhere in your code
  You have to declare them here manually
  Properties are optional because they are injected when the components are mounted sometime in your application's life.
  So, not available always
*/
export interface RootState {
  base?: BaseState;
  selection?: SelectionState;
  editSelection?: EditSelectionState;
  legend?: LegendState;
  timeline?: TimelineState;
  scenes?: ScenesState;
  theme?: ThemeState;
  leftWrapper?: LeftWrapperState;
  highlight?: HighlightState;
  dmaCustomer?: DmaCustomerState;
  valveEditor?: ValveEditorState;
  schedulingFormData?: SchedulingState;
  userConfig?: UserConfigState;
  blink?: BlinkState;
  measurePolygon?: MeasurePolygonState;
  dispatchLog?: DispatchLogState;
  appData?: AppData;
  solution?: SolutionState;
  quickAnalysis?: QuickAnalysisState;
  shiftSchedule?: ShiftScheduleState;
  scheduleLog?: ScheduleLogState;
  // [INSERT NEW REDUCER KEY ABOVE] < Needed for generating containers seamlessly
}
