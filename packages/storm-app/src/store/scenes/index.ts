/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { MapViewName } from '@waterdesk/data/const/map';
import { LayerState, Scene, SceneType } from '@waterdesk/data/scene';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { reduxScenesSaga } from './saga';
import { ScenesState } from './types';

export const initialState: ScenesState = {
  scenes: [],
  currentSceneId: undefined,
  beforeSceneId: undefined,
  currentUpdatedThemeItem: undefined,
  currentUpdatedLayerStates: undefined,
};

export const slice = createSlice({
  name: 'scenes',
  initialState,
  reducers: {
    initializeScenes(state, action: PayloadAction<{ scenes: Scene[] }>) {
      const { scenes } = action.payload;
      state.scenes = scenes;
    },
    updateCurrentSceneId(
      state,
      action: PayloadAction<{ sceneId: SceneType | undefined }>,
    ) {
      const { sceneId } = action.payload;
      state.beforeSceneId = state.currentSceneId;
      state.currentSceneId = sceneId;
    },
    updateSceneCurrentTheme(
      state,
      action: PayloadAction<{
        themeSectionType: string;
        currentTheme: { label: string; value: string };
      }>,
    ) {
      const { themeSectionType, currentTheme } = action.payload;
      const currentSceneIndex = state.scenes.findIndex(
        (scene) => scene.id === state.currentSceneId,
      );
      const currentScene = state.scenes[currentSceneIndex];
      const themeSectionsIndex = currentScene.themeSections.findIndex(
        (themeSection) => themeSection.type === themeSectionType,
      );
      const themeItem = {
        name: currentTheme.value,
        title: currentTheme.label,
      };

      state.currentUpdatedThemeItem = themeItem;
      currentScene.themeSections[themeSectionsIndex].currentThemeItem =
        themeItem;
    },
    updateSceneLayer(
      state,
      action: PayloadAction<{
        themeSectionType: string;
        layerNames: string[];
        layerVisible: boolean;
      }>,
    ) {
      const { themeSectionType, layerNames, layerVisible } = action.payload;
      const currentSceneIndex = state.scenes.findIndex(
        (scene) => scene.id === state.currentSceneId,
      );
      const currentScene = state.scenes[currentSceneIndex];
      const themeSectionsIndex = currentScene.themeSections.findIndex(
        (themeSection) => themeSection.type === themeSectionType,
      );
      const currentLayerStates =
        currentScene.themeSections[themeSectionsIndex].layerStates;
      const currentUpdatedLayerStates: LayerState[] = [];
      currentScene.themeSections[themeSectionsIndex].layerStates =
        currentLayerStates.map((layerState) => {
          if (layerNames.includes(layerState.name)) {
            const currentUpdateLayerState = {
              ...layerState,
              visible: layerVisible,
            };
            currentUpdatedLayerStates.push(currentUpdateLayerState);
            return currentUpdateLayerState;
          }
          return layerState;
        });
      state.currentUpdatedLayerStates = currentUpdatedLayerStates;
    },
    updateLayerVisible() {},
    switchViewTheme(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _state,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      _action: PayloadAction<{ mapNames?: MapViewName[] }>,
    ) {},
    resetState() {
      return initialState;
    },
  },
});

export const { actions: scenesActions } = slice;

export const useScenesSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxScenesSaga });
};
