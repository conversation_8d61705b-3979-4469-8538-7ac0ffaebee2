/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { MapViewName } from '@waterdesk/data/const/map';
import {
  updateCurrentDeviceColorData,
  updateCurrentLegendData,
} from '@waterdesk/data/legend-data';
import {
  getAllLayerNames,
  getInvisibleLayerNames,
  getScene,
  getThemeNames,
} from '@waterdesk/data/scene';
import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { setDevOTypeVisible } from '@waterdesk/request/layer/set-dev-otype-visible';
import { setLayerVisible } from '@waterdesk/request/layer/set-layer-visible';
import { switchViewTheme } from '@waterdesk/request/switch-view-theme';
import dayjs from 'dayjs';
import {
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import MapView from 'src/app/core/map-view/map-view';
import { hostApp } from 'src/app/host-app';
import { legendActions } from '../legend';
import { selectTimelineDate, selectTimelineTime } from '../time-line/selectors';
import { scenesActions } from '.';
import {
  selectBeforeSceneId,
  selectCurrentSceneId,
  selectScenes,
  selectUpdatedLayerStates,
} from './selectors';

function* updateLayerVisible(mapView: MapView) {
  if (typeof mapView === 'undefined') return;
  const currentUpdatedLayerStates: SagaReturnType<
    typeof selectUpdatedLayerStates
  > = yield select(selectUpdatedLayerStates);
  const scenes: SagaReturnType<typeof selectScenes> =
    yield select(selectScenes);
  const currentSceneId: SagaReturnType<typeof selectCurrentSceneId> =
    yield select(selectCurrentSceneId);
  const currentScene = getScene(scenes, currentSceneId);
  const invisibleLayers = getInvisibleLayerNames(currentScene);
  const currentSceneAllLayers = getAllLayerNames(currentScene);
  if (
    typeof currentUpdatedLayerStates !== 'undefined' &&
    currentUpdatedLayerStates.length
  ) {
    const { visible } = currentUpdatedLayerStates[0];
    const layerNetworkNames = currentUpdatedLayerStates
      .filter((layerState) => layerState.type === 'LayerNetwork')
      .map((layerState) => layerState.name);
    const layerFeatureNames = currentUpdatedLayerStates
      .filter((layerState) => layerState.type === 'LayerFeature')
      .map((layerState) => layerState.name);
    let result:
      | SagaReturnType<typeof setDevOTypeVisible | typeof setLayerVisible>
      | undefined;

    if (layerNetworkNames.length) {
      const layerNetworkResult: SagaReturnType<typeof setLayerVisible> =
        yield call(setLayerVisible, {
          time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          otypeName: layerNetworkNames,
          visible,
          viewId: mapView.getViewId(),
        });
      if (layerNetworkResult.status === 'Success') result = layerNetworkResult;
    }

    if (layerFeatureNames.length) {
      const layerFeatureResult: SagaReturnType<typeof setDevOTypeVisible> =
        yield call(setDevOTypeVisible, {
          time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          otypeName: layerFeatureNames,
          visible,
        });
      if (layerFeatureResult.status === 'Success') result = layerFeatureResult;
    }
    mapView?.updateLayersVisible(
      new Set([...invisibleLayers]),
      currentSceneAllLayers,
    );
    mapView.redraw();

    if (typeof result !== 'undefined' && result.legendData) {
      updateCurrentLegendData(result.legendData);
      yield put(
        legendActions.legendChanged({
          code: dayjs().valueOf(),
        }),
      );
    }
  }
}

function* updateLayerVisibleSaga() {
  try {
    const mapViews = hostApp().getMapViews();

    if (mapViews) {
      for (let i = 0; i < mapViews.length; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          yield updateLayerVisible(mapView);
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

function* updateMap(mapView: MapView) {
  const scenes: SagaReturnType<typeof selectScenes> =
    yield select(selectScenes);
  const currentSceneId: SagaReturnType<typeof selectCurrentSceneId> =
    yield select(selectCurrentSceneId);
  const timelineDate: SagaReturnType<typeof selectTimelineDate> =
    yield select(selectTimelineDate);
  const timelineTime: SagaReturnType<typeof selectTimelineTime> =
    yield select(selectTimelineTime);
  const beforeSceneId: SagaReturnType<typeof selectBeforeSceneId> =
    yield select(selectBeforeSceneId);

  const currentTime =
    mapView.dateTime || getDateTimeFromValue(timelineTime, dayjs(timelineDate));
  const currentScene = getScene(scenes, currentSceneId);
  const themeNames = getThemeNames(currentScene);
  const invisibleLayers = getInvisibleLayerNames(currentScene);
  const beforeScene = getScene(scenes, beforeSceneId);
  const beforeSceneAllLayers = getAllLayerNames(beforeScene);
  const currentSceneAllLayers = getAllLayerNames(currentScene);
  const differLayers = beforeSceneAllLayers.filter(
    (item) => !currentSceneAllLayers.includes(item),
  );

  const result: SagaReturnType<typeof switchViewTheme> = yield call(
    switchViewTheme,
    currentTime.format('YYYY-MM-DD HH:mm:ss'),
    themeNames,
    [...differLayers, ...invisibleLayers],
    mapView.getViewId(),
  );
  if (result.status === 'Success') {
    if (result.deviceColorData) {
      updateCurrentDeviceColorData(result.deviceColorData);
    }
    const layers: Set<string> = new Set([...differLayers, ...invisibleLayers]);
    mapView.updateDeviceFeatureRatio(
      result.deviceTypeRatioData,
      result.deviceThemeData,
    );
    mapView.updateLayersVisible(layers, currentSceneAllLayers);
    mapView.redraw();
    if (result.legendData) {
      updateCurrentLegendData(result.legendData);
      yield put(
        legendActions.legendChanged({
          code: dayjs().valueOf(),
        }),
      );
    }
  }
}

function* switchViewThemeSaga(
  action: PayloadAction<{ mapNames?: MapViewName[] }>,
) {
  try {
    const { mapNames } = action.payload;
    const mapViews = hostApp().getMapViews(mapNames);

    if (mapViews) {
      for (let i = 0; i < mapViews.length; i += 1) {
        const mapView = mapViews[i];
        if (mapView) {
          yield updateMap(mapView);
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

export function* reduxScenesSaga() {
  yield takeLatest(scenesActions.updateLayerVisible, updateLayerVisibleSaga);
  yield takeLatest(scenesActions.switchViewTheme, switchViewThemeSaga);
}
