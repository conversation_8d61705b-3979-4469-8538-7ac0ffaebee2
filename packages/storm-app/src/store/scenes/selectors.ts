/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => state?.scenes || initialState;

export const selectCurrentSceneId = createSelector(
  [selectSlice],
  (state) => state.currentSceneId,
);

export const selectBeforeSceneId = createSelector(
  [selectSlice],
  (state) => state.beforeSceneId,
);

export const selectScenes = createSelector(
  [selectSlice],
  (state) => state.scenes,
);

export const selectUpdatedThemeItem = createSelector(
  [selectSlice],
  (state) => state.currentUpdatedThemeItem,
);

export const selectUpdatedLayerStates = createSelector(
  [selectSlice],
  (state) => state.currentUpdatedLayerStates,
);

export const selectCurrentSceneTitle = createSelector(
  [selectSlice],
  (state) => {
    const { currentSceneId } = state;
    const sceneInfo = state.scenes.find((item) => item.id === currentSceneId);
    return sceneInfo?.title;
  },
);
