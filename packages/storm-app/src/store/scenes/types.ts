/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { LayerState, Scene, SceneType, ThemeItem } from '@waterdesk/data/scene';

/* --- STATE --- */
export interface ScenesState {
  /** 场景数据源 */
  scenes: Scene[];
  /** 当前选中场景id */
  currentSceneId: SceneType | undefined;
  /** 上次选中场景id */
  beforeSceneId: SceneType | undefined;
  /** 当前主题修改记录 */
  currentUpdatedThemeItem: ThemeItem | undefined;
  /** 当前图层修改记录 */
  currentUpdatedLayerStates: LayerState[] | undefined;
}
