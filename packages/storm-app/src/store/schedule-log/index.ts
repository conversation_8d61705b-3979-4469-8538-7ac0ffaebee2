/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import {
  defaultDateRange,
  ScheduleLog,
  ScheduleLogDateRange,
  ScheduleShift,
} from '@waterdesk/data/schedule-log/schedule-log';
import dayjs from 'dayjs';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import scheduleLogSaga from './saga';
import { ScheduleLogAction, ScheduleLogState } from './types';

export const initialState: ScheduleLogState = {
  loading: false,
  actionType: 'click',
  selectedDate: dayjs().format('YYYY-MM-DD'),
  scheduleLog: [],
  selectedScheduleLog: {},
  currentScheduleLog: {},
  fetchDateRange: defaultDateRange,
  eventTypeCheckedKeys: [],
  minAndMaxDateScope: undefined,
};

export const slice = createSlice({
  name: 'scheduleLog',
  initialState,
  reducers: {
    /** 初始化日志 */
    initialScheduleLog: () => {},
    /** 点击日志 */
    updateScheduleLogByClick: () => {},
    /** 滚动上拉日志 */
    updateScheduleLogByScrollUp: () => {},
    /** 滚动下拉日志 */
    updateScheduleLogByScrollDown: () => {},
    /** 更新当前日志 */
    updateCurrentScheduleLog: () => {},
    /** 设置加载状态 */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    /** 设置行为类型 */
    setActionType: (state, action: PayloadAction<ScheduleLogAction>) => {
      state.actionType = action.payload;
    },
    /** 设置选中日期 */
    setSelectedDate: (
      state,
      action: PayloadAction<{ date: string; actions: ScheduleLogAction }>,
    ) => {
      state.selectedDate = action.payload.date;
      state.actionType = action.payload.actions;
    },
    /** 设置当前日志 */
    setCurrentScheduleLog: (state, action: PayloadAction<ScheduleShift>) => {
      state.currentScheduleLog = action.payload;
    },
    /** 设置选中日期范围 */
    setFetchDateRange: (state, action: PayloadAction<ScheduleLogDateRange>) => {
      state.fetchDateRange = action.payload;
    },
    /** 设置日志 */
    setScheduleLog: (state, action: PayloadAction<ScheduleLog[]>) => {
      state.scheduleLog = action.payload;
      state.fetchDateRange = [
        dayjs(state.scheduleLog[0]?.date).format('YYYY-MM-DD'),
        dayjs(state.scheduleLog[state.scheduleLog.length - 1]?.date).format(
          'YYYY-MM-DD',
        ),
      ];
    },
    /** 设置选中日志 */
    setSelectedScheduleLog: (state, action: PayloadAction<ScheduleShift>) => {
      state.selectedScheduleLog = action.payload;
    },
    /** 设置事件类型勾选 */
    setEventTypeCheckedKeys: (state, action: PayloadAction<string[]>) => {
      state.eventTypeCheckedKeys = action.payload;
    },
    /** 设置最小和最大日期范围 */
    setMinAndMaxDateScope: (
      state,
      action: PayloadAction<{ minShiftDate?: string; maxShiftDate?: string }>,
    ) => {
      const { minShiftDate, maxShiftDate } = action.payload;
      if (!minShiftDate || !maxShiftDate) return;
      state.minAndMaxDateScope = [minShiftDate, maxShiftDate];
    },
  },
});

export const { actions: scheduleLogActions } = slice;

export const useScheduleLogSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: scheduleLogSaga });
  return { actions: slice.actions };
};
