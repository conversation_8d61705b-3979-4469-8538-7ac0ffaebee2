/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  defaultDateRange,
  formatDate,
  getScheduleLogByDate,
  newAggregateScheduleDataToLog,
  ScheduleLogDateRange,
} from '@waterdesk/data/schedule-log/schedule-log';
import {
  DefaultListAPIResponse,
  isDefaultListAPIResponse,
} from '@waterdesk/request/api/api-response';
import { getEventSchedulingBasicInfo } from '@waterdesk/request/event-scheduling/get-event-info';
import {
  getMinAndMaxDateScope,
  getScheduleInfoList,
  getShiftInfoList,
} from '@waterdesk/request/get-shift-schedule-data';
import {
  GetWeatherAndTemperatureResponse,
  getWeatherAndTemperature,
} from '@waterdesk/request/get-weather';
import { getShiftHandoverInfo } from '@waterdesk/request/schedule-log/get-shift-handover';
import dayjs from 'dayjs';
import {
  all,
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { selectGlobalConfig } from '../user-config/selector';
import { UserConfigState } from '../user-config/types';
import { scheduleLogActions } from '.';
import {
  selectDate,
  selectEventTypeCheckedKeys,
  selectFetchDateRange,
  selectScheduleLog,
} from './selector';
import { ScheduleLogState } from './types';

function* fetchScheduleLogData(
  fetchDateRange: ScheduleLogDateRange,
  eventType?: string | string[],
) {
  const fetchResponse: {
    eventResponse: SagaReturnType<typeof getEventSchedulingBasicInfo>;
    handoverResponse: SagaReturnType<typeof getShiftHandoverInfo>;
    weatherResponse: SagaReturnType<typeof getWeatherAndTemperature>;
    shiftResponse: SagaReturnType<typeof getShiftInfoList>;
    scheduleResponse: SagaReturnType<typeof getScheduleInfoList>;
    minAndMaxDateScopeResponse: SagaReturnType<typeof getMinAndMaxDateScope>;
  } = yield all({
    eventResponse: call(getEventSchedulingBasicInfo, {
      eventStartTime: fetchDateRange[0],
      eventEndTime: fetchDateRange[1],
      eventType,
    }),
    handoverResponse: call(getShiftHandoverInfo, {
      startTime: fetchDateRange[0],
      endTime: fetchDateRange[1],
    }),
    weatherResponse: call(
      getWeatherAndTemperature,
      fetchDateRange[0],
      fetchDateRange[1],
    ),
    shiftResponse: call(getShiftInfoList, {
      startDate: fetchDateRange[0],
      endDate: fetchDateRange[1],
    }),
    scheduleResponse: call(getScheduleInfoList, {
      startDate: fetchDateRange[0],
      endDate: fetchDateRange[1],
    }),
    minAndMaxDateScopeResponse: call(getMinAndMaxDateScope, {}),
  });

  const handleSuccessResponse = <T>(
    response: DefaultListAPIResponse<T> | GetWeatherAndTemperatureResponse,
  ) => {
    if (response.status === 'Success') {
      if (isDefaultListAPIResponse<T>(response)) {
        return response.list;
      }
      return response.data;
    }

    return undefined;
  };

  if (fetchResponse.minAndMaxDateScopeResponse.status === 'Success') {
    if (fetchResponse.minAndMaxDateScopeResponse.values) {
      yield put(
        scheduleLogActions.setMinAndMaxDateScope(
          fetchResponse.minAndMaxDateScopeResponse.values,
        ),
      );
    }
  }

  return {
    events: handleSuccessResponse(fetchResponse.eventResponse),
    handovers: handleSuccessResponse(fetchResponse.handoverResponse),
    weathers: handleSuccessResponse(fetchResponse.weatherResponse),
    shifts: handleSuccessResponse(fetchResponse.shiftResponse),
    schedules: handleSuccessResponse(fetchResponse.scheduleResponse),
  };
}

function* initialScheduleLog() {
  try {
    yield put(scheduleLogActions.setLoading(true));

    const fetchDateRange: ScheduleLogDateRange = defaultDateRange;
    const globalConfig: UserConfigState['globalConfig'] =
      yield select(selectGlobalConfig);
    const eventTypeCheckedKeys: ScheduleLogState['eventTypeCheckedKeys'] =
      yield select(selectEventTypeCheckedKeys);

    const { events, handovers, weathers, shifts, schedules } = yield call(
      fetchScheduleLogData,
      fetchDateRange,
      eventTypeCheckedKeys,
    );

    const scheduleLog = newAggregateScheduleDataToLog(
      events,
      shifts,
      schedules,
      handovers,
      weathers,
      fetchDateRange,
      globalConfig?.scheduleConfig?.shifts ?? [],
    );
    yield put(scheduleLogActions.setScheduleLog(scheduleLog));

    const selectedScheduleLog = getScheduleLogByDate(
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
      scheduleLog,
    );
    yield put(
      scheduleLogActions.setSelectedScheduleLog(selectedScheduleLog ?? {}),
    );
    yield put(scheduleLogActions.setLoading(false));
  } catch (error) {
    console.error('Error in initialScheduleLog:', error);
  } finally {
    yield put(scheduleLogActions.setLoading(false));
  }
}

function* updateScheduleLogByClick() {
  try {
    yield put(scheduleLogActions.setLoading(true));

    const selectedDate: ScheduleLogState['selectedDate'] =
      yield select(selectDate);
    const globalConfig: UserConfigState['globalConfig'] =
      yield select(selectGlobalConfig);
    const eventTypeCheckedKeys: ScheduleLogState['eventTypeCheckedKeys'] =
      yield select(selectEventTypeCheckedKeys);

    const date = dayjs(selectedDate);
    const startTime = date.subtract(7, 'days').format('YYYY-MM-DD');
    const endTime = date.add(6, 'days').format('YYYY-MM-DD');

    const { events, handovers, weathers, shifts, schedules } = yield call(
      fetchScheduleLogData,
      [startTime, endTime],
      eventTypeCheckedKeys,
    );

    const scheduleLog = newAggregateScheduleDataToLog(
      events,
      shifts,
      schedules,
      handovers,
      weathers,
      [startTime, endTime],
      globalConfig?.scheduleConfig?.shifts ?? [],
    );

    yield put(scheduleLogActions.setScheduleLog(scheduleLog));

    let selectedScheduleLog = scheduleLog?.find((i) => i?.date === selectedDate)
      ?.shifts?.[0];

    if (formatDate(dayjs(selectedDate)) === formatDate(dayjs())) {
      selectedScheduleLog = getScheduleLogByDate(
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
        scheduleLog,
      );
    }

    yield put(
      scheduleLogActions.setSelectedScheduleLog(selectedScheduleLog ?? {}),
    );
    yield put(scheduleLogActions.setLoading(false));
  } catch (error) {
    console.error('Error in updateScheduleLogByClick:', error);
  } finally {
    yield put(scheduleLogActions.setLoading(false));
  }
}

function* updateScheduleLogByScrollUp() {
  try {
    yield put(scheduleLogActions.setLoading(true));
    const fetchDateRange: ScheduleLogState['fetchDateRange'] =
      yield select(selectFetchDateRange);
    const globalConfig: UserConfigState['globalConfig'] =
      yield select(selectGlobalConfig);
    const eventTypeCheckedKeys: ScheduleLogState['eventTypeCheckedKeys'] =
      yield select(selectEventTypeCheckedKeys);

    const previousWeekDateRange: ScheduleLogState['fetchDateRange'] = [
      dayjs(fetchDateRange[0]).subtract(7, 'days').format('YYYY-MM-DD'),
      dayjs(fetchDateRange[0]).subtract(1, 'days').format('YYYY-MM-DD'),
    ];

    const { events, handovers, weathers, shifts, schedules } = yield call(
      fetchScheduleLogData,
      previousWeekDateRange,
      eventTypeCheckedKeys,
    );

    const newScheduleLog = newAggregateScheduleDataToLog(
      events,
      shifts,
      schedules,
      handovers,
      weathers,
      previousWeekDateRange,
      globalConfig?.scheduleConfig?.shifts ?? [],
    );

    const scheduleLog: ScheduleLogState['scheduleLog'] =
      yield select(selectScheduleLog);

    const value = [...newScheduleLog, ...scheduleLog];

    while (value.length > 21) {
      value.pop();
    }

    yield put(scheduleLogActions.setScheduleLog(value));
    yield put(
      scheduleLogActions.setFetchDateRange([
        formatDate(value[0].date),
        formatDate(value[value.length - 1].date),
      ]),
    );
    yield put(scheduleLogActions.setLoading(false));
  } catch (error) {
    console.error('Error in updateScheduleLogByScrollUp:', error);
  } finally {
    yield put(scheduleLogActions.setLoading(false));
  }
}

function* updateScheduleLogByScrollDown() {
  try {
    yield put(scheduleLogActions.setLoading(true));
    const fetchDateRange: ScheduleLogState['fetchDateRange'] =
      yield select(selectFetchDateRange);
    const globalConfig: UserConfigState['globalConfig'] =
      yield select(selectGlobalConfig);
    const eventTypeCheckedKeys: ScheduleLogState['eventTypeCheckedKeys'] =
      yield select(selectEventTypeCheckedKeys);

    const nextWeekDateRange: ScheduleLogState['fetchDateRange'] = [
      dayjs(fetchDateRange[1]).add(1, 'days').format('YYYY-MM-DD'),
      dayjs(fetchDateRange[1]).add(7, 'days').format('YYYY-MM-DD'),
    ];

    const { events, handovers, weathers, shifts, schedules } = yield call(
      fetchScheduleLogData,
      nextWeekDateRange,
      eventTypeCheckedKeys,
    );

    const newScheduleLog = newAggregateScheduleDataToLog(
      events,
      shifts,
      schedules,
      handovers,
      weathers,
      nextWeekDateRange,
      globalConfig?.scheduleConfig?.shifts ?? [],
    );

    const scheduleLog: ScheduleLogState['scheduleLog'] =
      yield select(selectScheduleLog);

    const value = [...scheduleLog, ...newScheduleLog];

    while (value.length > 21) {
      value.shift();
    }

    yield put(scheduleLogActions.setScheduleLog(value));
    yield put(
      scheduleLogActions.setFetchDateRange([
        formatDate(value[0].date),
        formatDate(value[value.length - 1].date),
      ]),
    );
    yield put(scheduleLogActions.setLoading(false));
  } catch (error) {
    console.error('Error in updateScheduleLogByScrollDown:', error);
  } finally {
    yield put(scheduleLogActions.setLoading(false));
  }
}

function* updateCurrentScheduleLog() {
  try {
    yield put(scheduleLogActions.setLoading(true));

    const globalConfig: UserConfigState['globalConfig'] =
      yield select(selectGlobalConfig);

    const dateRange: ScheduleLogState['fetchDateRange'] = [
      dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      dayjs().add(1, 'day').format('YYYY-MM-DD'),
    ];

    const { events, handovers, weathers, shifts, schedules } = yield call(
      fetchScheduleLogData,
      dateRange,
    );

    const newScheduleLog = newAggregateScheduleDataToLog(
      events,
      shifts,
      schedules,
      handovers,
      weathers,
      dateRange,
      globalConfig?.scheduleConfig?.shifts ?? [],
    );

    const currentScheduleLog = getScheduleLogByDate(
      dayjs()
        .subtract(
          globalConfig?.scheduleConfig.handoverTimeBufferMinutes ?? 0,
          'minutes',
        )
        .format('YYYY-MM-DD HH:mm:ss'),
      newScheduleLog ?? [],
    );

    yield put(
      scheduleLogActions.setCurrentScheduleLog(currentScheduleLog ?? {}),
    );
  } catch (error) {
    console.error('Error in updateCurrentScheduleLog:', error);
  } finally {
    yield put(scheduleLogActions.setLoading(false));
  }
}

export default function* scheduleLogSaga() {
  yield takeLatest(
    scheduleLogActions.initialScheduleLog.type,
    initialScheduleLog,
  );
  yield takeLatest(
    scheduleLogActions.updateScheduleLogByClick.type,
    updateScheduleLogByClick,
  );
  yield takeLatest(
    scheduleLogActions.updateScheduleLogByScrollUp.type,
    updateScheduleLogByScrollUp,
  );
  yield takeLatest(
    scheduleLogActions.updateScheduleLogByScrollDown.type,
    updateScheduleLogByScrollDown,
  );
  yield takeLatest(
    scheduleLogActions.updateCurrentScheduleLog.type,
    updateCurrentScheduleLog,
  );
}
