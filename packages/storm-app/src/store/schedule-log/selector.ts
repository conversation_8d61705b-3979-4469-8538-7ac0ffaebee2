/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => {
  if (state?.dispatchLog) return state.scheduleLog;
  return initialState;
};

/** 选择是否正在加载 */
export const selectLoading = createSelector(
  [selectSlice],
  (state) => state?.loading,
);

/** 选择行为类型 */
export const selectActionType = createSelector(
  [selectSlice],
  (state) => state?.actionType,
);

/** 选择选中日期 */
export const selectDate = createSelector(
  [selectSlice],
  (state) => state?.selectedDate,
);

/** 选择日志请求时间范围 */
export const selectFetchDateRange = createSelector(
  [selectSlice],
  (state) => state?.fetchDateRange,
);

/** 选择日志 */
export const selectScheduleLog = createSelector(
  [selectSlice],
  (state) => state?.scheduleLog,
);

/** 选择选中的日志 */
export const selectSelectedScheduleLog = createSelector(
  [selectSlice],
  (state) => state?.selectedScheduleLog,
);

/** 选择当前日志 */
export const selectCurrentScheduleLog = createSelector(
  [selectSlice],
  (state) => state?.currentScheduleLog,
);

/** 选择事件类型勾选 */
export const selectEventTypeCheckedKeys = createSelector(
  [selectSlice],
  (state) => state?.eventTypeCheckedKeys,
);

/** 选择时间可选范围 */
export const selectMinAndMaxDateScope = createSelector(
  [selectSlice],
  (state) => state?.minAndMaxDateScope,
);
