/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ScheduleLog,
  ScheduleLogDateRange,
  ScheduleShift,
} from '@waterdesk/data/schedule-log/schedule-log';

export type ScheduleLogAction = 'click' | 'scroll'; // 调度日志行为类型

export type ScheduleLogState = {
  loading: boolean; // 是否正在加载
  actionType: ScheduleLogAction; // 当前行为属于点击事件还是滚动事件
  selectedDate: string; // 左侧日历选中日期
  scheduleLog: ScheduleLog[]; // 调度日志数据
  selectedScheduleLog: ScheduleShift; // 选中的调度日志
  currentScheduleLog: ScheduleShift | undefined; // 当前调度日志
  fetchDateRange: ScheduleLogDateRange; // 右侧时间线请求日期范围
  eventTypeCheckedKeys: string[]; // 事件类型勾选
  minAndMaxDateScope?: ScheduleLogDateRange; // 时间可选范围
};
