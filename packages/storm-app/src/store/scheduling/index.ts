/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { ObjectFormItem } from '@waterdesk/data/track-data';
import { useInjectReducer } from 'src/utils/redux-injectors';

import { SchedulingState } from './types';

export const initialState: SchedulingState = {
  valveFormData: undefined,
  burstFlushingFormData: undefined,
};

export const slice = createSlice({
  name: 'schedulingFormData',
  initialState,
  reducers: {
    valveFormDataChanged(
      state,
      action: PayloadAction<{
        valveFormData: ObjectFormItem[] | undefined;
      }>,
    ) {
      const { valveFormData } = action.payload;
      state.valveFormData = valveFormData;
    },
    burstFlushingFormDataChanged(
      state,
      action: PayloadAction<{
        burstFlushingFormData: ObjectFormItem | undefined;
      }>,
    ) {
      const { burstFlushingFormData } = action.payload;
      state.burstFlushingFormData = burstFlushingFormData;
    },
  },
});

export const { actions: schedulingActions } = slice;

export const useSchedulingSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
};
