/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { Item } from '@waterdesk/data/selection-data';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { reduxSelectionSaga } from './saga';
import { SelectionState } from './types';

export const initialState: SelectionState = {
  items: [],
  indicatorType: undefined,
  indicatorName: undefined,
  vprop: undefined,
  chartCode: undefined,
  forecast: false,
};

export function makeChartCode(
  items: Item[] | Item,
  indicatorType: string | undefined,
  indicatorName: string | undefined,
  vprop: string | undefined,
  forecast?: boolean,
): string {
  const itemsString = Array.isArray(items)
    ? items.map((item) => JSON.stringify(item)).join(',')
    : JSON.stringify(items);

  return `${itemsString}#${indicatorType}#${indicatorName}#${vprop}#${forecast}`;
}

export const slice = createSlice({
  name: 'selection',
  initialState,
  reducers: {
    selectionChanged(
      state,
      action: PayloadAction<{
        items: Array<Item>;
      }>,
    ) {
      const { items } = action.payload;
      const chartCode = makeChartCode(
        items,
        state.indicatorType,
        state.indicatorName,
        state.vprop,
        state.forecast,
      );
      state.items = items;
      state.chartCode = chartCode;
    },
    propertyChanged(
      state,
      action: PayloadAction<{
        indicatorType?: string;
        indicatorName?: string;
        vprop?: string;
        forecast?: boolean;
      }>,
    ) {
      const {
        indicatorType,
        indicatorName,
        vprop,
        forecast = false,
      } = action.payload;
      const chartCode = makeChartCode(
        state.items,
        indicatorType,
        indicatorName,
        vprop,
        forecast,
      );

      return {
        ...state,
        indicatorType,
        indicatorName,
        vprop,
        chartCode,
        forecast,
      };
    },
    selectionAndPropertyChanged(
      state,
      action: PayloadAction<{
        items: Array<Item>;
        indicatorType?: string;
        indicatorName?: string;
        vprop?: string;
        forecast?: boolean;
      }>,
    ) {
      const {
        items,
        indicatorType,
        indicatorName,
        vprop,
        forecast = false,
      } = action.payload;
      const chartCode = makeChartCode(
        items,
        indicatorType,
        indicatorName,
        vprop,
        forecast,
      );

      return {
        ...state,
        items,
        indicatorType,
        indicatorName,
        vprop,
        chartCode,
        forecast,
      };
    },
    chartObjectChanged(
      state,
      action: PayloadAction<{
        items: Array<Item>;
        indicatorType?: string;
        indicatorName?: string;
        vprop?: string;
        forecast?: boolean;
      }>,
    ) {
      const {
        items,
        indicatorType,
        indicatorName,
        vprop,
        forecast = false,
      } = action.payload;
      const chartCode = makeChartCode(
        items,
        indicatorType,
        indicatorName,
        vprop,
        forecast,
      );

      return {
        ...state,
        indicatorType,
        indicatorName,
        vprop,
        chartCode,
        items,
        forecast,
      };
    },
    clearSelection(state) {
      state.items = [];
      state.chartCode = undefined;
      state.indicatorType = undefined;
      state.indicatorName = undefined;
      state.vprop = undefined;
      state.forecast = false;
    },
  },
});

export const { actions: selectionActions } = slice;

export const useSelectionSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxSelectionSaga });
};
