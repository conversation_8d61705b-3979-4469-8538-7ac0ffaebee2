/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { getShapeType, makeObjectId } from '@waterdesk/data/object-item';
import { Item } from '@waterdesk/data/selection-data';
import { getModelObjectByIndicators } from '@waterdesk/request/find-any-element';
import { call, put, SagaReturnType, takeLatest } from 'redux-saga/effects';
import { curDb, hostApp } from 'src/app/host-app';
import { highlightActions } from '../highlight';
import { selectionActions } from '.';

function* clearHighlightIndicatorRefModel() {
  yield put(
    highlightActions.clearHighlight({
      highlightLayerName: 'refModel',
    }),
  );
}

function* highlightIndicatorRefModel(ids: string[]) {
  const res: SagaReturnType<typeof getModelObjectByIndicators> = yield call(
    getModelObjectByIndicators,
    ids,
  );

  if (res.status === 'Success') {
    yield put(
      highlightActions.updateHighlight({
        highlight: {
          refModel: Object.values(res.data ?? {}).map((m) => {
            const shapeType = getShapeType(m.shape);
            return {
              ...m,
              highlightType: 'refModel',
              highlightIcon:
                shapeType === 'POINT'
                  ? curDb().layerCollection.getLayer(m.otype)?.icon
                  : undefined,
            };
          }),
        },
        highlightMapName: hostApp().getMainMapView()?.mapViewName,
      }),
    );
  }
}

function* selectionAndPropertyChangedSaga(
  actions: PayloadAction<{
    items: Array<Item>;
    indicatorType?: string;
    indicatorName?: string;
    vprop?: string;
    forecast?: boolean;
  }>,
) {
  if (!hostApp().appConfig.enableHighlightRefModel) return;
  const { indicatorType, indicatorName } = actions.payload;
  if (indicatorType && indicatorName) {
    yield highlightIndicatorRefModel([
      makeObjectId(indicatorType, indicatorName),
    ]);
  }
}

function* selectionChangedSaga(
  actions: PayloadAction<{
    items: Array<Item>;
  }>,
) {
  if (!hostApp().appConfig.enableHighlightRefModel) return;
  const { items } = actions.payload;
  if (items.length > 0) {
    const selectedObjects =
      hostApp().getMainMapView()?.selectionCollection?.selectedObjects;
    if (selectedObjects?.length) {
      const ids: string[] = [];
      selectedObjects?.forEach((item) => {
        item.indicators.forEach((f) => {
          ids.push(makeObjectId(f.otype, f.oname));
        });
      });
      if (ids.length > 0) yield highlightIndicatorRefModel(ids);
    }
  }
}

export function* reduxSelectionSaga() {
  yield takeLatest(
    selectionActions.selectionAndPropertyChanged,
    selectionAndPropertyChangedSaga,
  );
  yield takeLatest(selectionActions.selectionChanged, selectionChangedSaga);
  yield takeLatest(
    selectionActions.clearSelection,
    clearHighlightIndicatorRefModel,
  );
}
