/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { MONTH_FORMAT } from '@waterdesk/data/shift-schedule';
import dayjs from 'dayjs';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';

import shiftScheduleSaga from './saga';
import { ShiftScheduleState } from './types';

export const initialState: ShiftScheduleState = {
  shiftInfo: [], // 班次信息
  scheduleInfo: [], // 排班信息
  loading: false, // 加载状态
  selectedMonth: dayjs().format(MONTH_FORMAT), // 选中月份
  filterUserList: [], // 用户列表
  shiftScheduleInfoList: [], // 综合信息
  minAndMaxDateScope: undefined,
};

export const slice = createSlice({
  name: 'shiftSchedule',
  initialState,
  reducers: {
    // 初始化班次信息
    initialShiftSchedule: () => {},
    updateShiftSchedule: () => {},
    // 设置加载状态
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    // 设置选中月份
    setSelectedMonth: (state, action) => {
      state.selectedMonth = action.payload;
    },
    // 设置班次信息
    setShiftInfo: (state, action) => {
      state.shiftInfo = action.payload;
    },
    // 设置排班信息
    setScheduleInfo: (state, action) => {
      state.scheduleInfo = action.payload;
    },
    // 设置用户列表
    setFilterUserList: (state, action) => {
      state.filterUserList = action.payload;
    },
    // 设置综合信息
    setShiftScheduleInfoList: (state, action) => {
      state.shiftScheduleInfoList = action.payload;
    },
    /** 设置最小和最大日期范围 */
    setMinAndMaxDateScope: (
      state,
      action: PayloadAction<{ minShiftDate?: string; maxShiftDate?: string }>,
    ) => {
      const { minShiftDate, maxShiftDate } = action.payload;
      if (!minShiftDate || !maxShiftDate) return;
      state.minAndMaxDateScope = [minShiftDate, maxShiftDate];
    },
  },
});

export const { actions: shiftScheduleActions } = slice;

export const useShiftScheduleSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: shiftScheduleSaga });
  return { actions: slice.actions };
};
