/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DAY_FORMAT_WITH_FINIAL,
  DAY_FORMAT_WITH_ZERO,
  getShiftScheduleInfo,
} from '@waterdesk/data/shift-schedule';
import {
  getMinAndMaxDateScope,
  getScheduleInfoList,
  getShiftInfoList,
} from '@waterdesk/request/get-shift-schedule-data';
import { getUserList } from '@waterdesk/request/user';
import dayjs from 'dayjs';
import {
  all,
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { selectGlobalConfig } from '../user-config/selector';
import { UserConfigState } from '../user-config/types';
import { shiftScheduleActions } from '.';
import { selectFilterUserList, selectSelectedMonth } from './selector';
import { ShiftScheduleState } from './types';

function* initialShiftScheduleInfo() {
  try {
    yield put(shiftScheduleActions.setLoading(true));
    const selectedMonth: ShiftScheduleState['selectedMonth'] =
      yield select(selectSelectedMonth);
    const params = {
      startDate: dayjs(selectedMonth)
        .startOf('month')
        .format(DAY_FORMAT_WITH_ZERO),
      endDate: dayjs(selectedMonth)
        .endOf('month')
        .format(DAY_FORMAT_WITH_FINIAL),
    };

    const [
      globalConfigRes,
      userListRes,
      shiftInfoListRes,
      scheduleInfoListRes,
      minAndMaxDateScopeRes,
    ]: [
      UserConfigState['globalConfig'],
      SagaReturnType<typeof getUserList>,
      SagaReturnType<typeof getShiftInfoList>,
      SagaReturnType<typeof getScheduleInfoList>,
      SagaReturnType<typeof getMinAndMaxDateScope>,
    ] = yield all([
      select(selectGlobalConfig),
      call(getUserList),
      call(getShiftInfoList, params),
      call(getScheduleInfoList, params),
      call(getMinAndMaxDateScope, {}),
    ]);

    const { department, shifts } = globalConfigRes?.scheduleConfig ?? {};
    const filteredUserList = userListRes?.list?.filter((user) =>
      department?.includes(user.departmentName),
    );
    yield put(shiftScheduleActions.setFilterUserList(filteredUserList));
    yield put(shiftScheduleActions.setShiftInfo(shiftInfoListRes?.list ?? []));
    yield put(
      shiftScheduleActions.setScheduleInfo(scheduleInfoListRes?.list ?? []),
    );
    yield put(
      shiftScheduleActions.setMinAndMaxDateScope(
        minAndMaxDateScopeRes.values ?? {},
      ),
    );
    const shiftScheduleInfo = getShiftScheduleInfo(
      filteredUserList,
      shiftInfoListRes?.list ?? [],
      scheduleInfoListRes?.list ?? [],
      shifts,
    );

    yield put(shiftScheduleActions.setShiftScheduleInfoList(shiftScheduleInfo));
  } catch {
    console.error('初始化班次信息失败');
  } finally {
    yield put(shiftScheduleActions.setLoading(false));
  }
}

function* updateShiftScheduleInfo() {
  try {
    yield put(shiftScheduleActions.setLoading(true));
    const selectedMonth: ShiftScheduleState['selectedMonth'] =
      yield select(selectSelectedMonth);
    const filteredUserList: ShiftScheduleState['filterUserList'] =
      yield select(selectFilterUserList);
    const params = {
      startDate: dayjs(selectedMonth)
        .startOf('month')
        .format(DAY_FORMAT_WITH_ZERO),
      endDate: dayjs(selectedMonth)
        .endOf('month')
        .format(DAY_FORMAT_WITH_FINIAL),
    };
    const [globalConfigRes, shiftInfoListRes, scheduleInfoListRes]: [
      UserConfigState['globalConfig'],
      SagaReturnType<typeof getShiftInfoList>,
      SagaReturnType<typeof getScheduleInfoList>,
    ] = yield all([
      select(selectGlobalConfig),
      call(getShiftInfoList, params),
      call(getScheduleInfoList, params),
    ]);
    const { shifts } = globalConfigRes?.scheduleConfig ?? {};

    const shiftScheduleInfo = getShiftScheduleInfo(
      filteredUserList ?? [],
      shiftInfoListRes?.list ?? [],
      scheduleInfoListRes?.list ?? [],
      shifts,
    );

    yield put(shiftScheduleActions.setShiftScheduleInfoList(shiftScheduleInfo));
    yield put(shiftScheduleActions.setShiftInfo(shiftInfoListRes?.list ?? []));
    yield put(
      shiftScheduleActions.setScheduleInfo(scheduleInfoListRes?.list ?? []),
    );
  } catch (error) {
    console.error('更新班次信息失败', error);
  } finally {
    yield put(shiftScheduleActions.setLoading(false));
  }
}

export default function* shiftScheduleSaga() {
  yield takeLatest(
    shiftScheduleActions.initialShiftSchedule.type,
    initialShiftScheduleInfo,
  );
  yield takeLatest(
    shiftScheduleActions.setSelectedMonth.type,
    updateShiftScheduleInfo,
  );
}
