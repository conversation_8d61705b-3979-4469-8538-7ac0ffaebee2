/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => {
  if (state?.shiftSchedule) return state.shiftSchedule;
  return initialState;
};

// 选择加载状态
export const selectLoading = createSelector(
  [selectSlice],
  (state) => state.loading,
);

// 选择班次信息
export const selectShiftInfo = createSelector(
  [selectSlice],
  (state) => state.shiftInfo,
);

// 选择排班信息
export const selectScheduleInfo = createSelector(
  [selectSlice],
  (state) => state.scheduleInfo,
);

// 选择用户列表
export const selectFilterUserList = createSelector(
  [selectSlice],
  (state) => state.filterUserList,
);

// 选择综合信息
export const selectShiftScheduleInfoList = createSelector(
  [selectSlice],
  (state) => state.shiftScheduleInfoList,
);

// 选择选中月份
export const selectSelectedMonth = createSelector(
  [selectSlice],
  (state) => state.selectedMonth,
);

/** 选择时间可选范围 */
export const selectMinAndMaxDateScope = createSelector(
  [selectSlice],
  (state) => state?.minAndMaxDateScope,
);
