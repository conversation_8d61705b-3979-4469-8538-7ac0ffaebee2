/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ScheduleLogDateRange } from '@waterdesk/data/schedule-log/schedule-log';
import {
  ScheduleInfo,
  ShiftInfo,
  ShiftScheduleInfo,
} from '@waterdesk/data/shift-schedule';
import { UserInfo } from '@waterdesk/data/system-user';

export type ShiftScheduleState = {
  /** 班次信息 */
  shiftInfo?: ShiftInfo[];
  /** 排班信息 */
  scheduleInfo?: ScheduleInfo[];
  /** 加载状态 */
  loading?: boolean;
  /** 选中月份 */
  selectedMonth?: string;
  /** 用户列表 */
  filterUserList?: UserInfo[];
  /** 综合信息 */
  shiftScheduleInfoList?: ShiftScheduleInfo[];
  minAndMaxDateScope?: ScheduleLogDateRange; // 时间可选范围
};
