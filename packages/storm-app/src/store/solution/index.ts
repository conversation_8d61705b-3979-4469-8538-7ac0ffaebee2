/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CompareInfo } from '@waterdesk/data/solution';
import { useInjectReducer } from 'redux-injectors';
import { SolutionState } from './types';

export const initialState: SolutionState = {
  compareList: [],
};

export const slice = createSlice({
  name: 'solution',
  initialState,
  reducers: {
    updateSolutionCompare(
      state,
      action: PayloadAction<{ list: Array<CompareInfo> }>,
    ) {
      const { list } = action.payload;
      state.compareList = list;
    },
  },
});

export const { actions: solutionActions } = slice;

export const useSolutionSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
};
