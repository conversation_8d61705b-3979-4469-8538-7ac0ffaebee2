/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createThemeSlice, type ThemeState } from '@waterdesk/core/store';
import { useInjectReducer } from 'src/utils/redux-injectors';
import { getThemeFromLocal } from 'src/utils/tool';

export const initialState: ThemeState = {
  seedToken: undefined,
  componentsConfig: undefined,
  darkModeSeedToken: undefined,
  darkModeComponentsConfig: undefined,
  theme: getThemeFromLocal(),
};

export const themeSlice = createThemeSlice(initialState);

export const { actions: themeActions } = themeSlice;

export const useThemeSlice = () => {
  useInjectReducer({ key: themeSlice.name, reducer: themeSlice.reducer });
};
