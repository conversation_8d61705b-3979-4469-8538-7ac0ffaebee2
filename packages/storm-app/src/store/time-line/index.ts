/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import {
  getDateTimeFromValue,
  getValueFromDateTime,
} from '@waterdesk/data/time-data';
import {
  formatDevicesWarns,
  WarnDetail,
  WarnInfoList,
} from '@waterdesk/data/warn';
import { requestApi } from '@waterdesk/request/request';
import dayjs from 'dayjs';
import { hostApp } from 'src/app/host-app';
import { useInjectReducer, useInjectSaga } from 'src/utils/redux-injectors';
import { timelineSaga } from './saga';
import { TimelineState } from './types';

export const initialState: TimelineState = {
  timelineDate: dayjs().format('YYYY-MM-DD'),
  timelineTime: getValueFromDateTime(dayjs()),
  tempTime: undefined,
  timelineWarn: [],
  timelineEvent: [],
  devicesWarns: {},
  autoPlay: true,
};

export const slice = createSlice({
  name: 'timeline',
  initialState,
  reducers: {
    updateAutoPlay(state, action: PayloadAction<{ autoPlay: boolean }>) {
      const { autoPlay } = action.payload;
      state.autoPlay = autoPlay;
    },
    updateTimelineDate(state, action: PayloadAction<{ timelineDate: string }>) {
      const { timelineDate } = action.payload;
      const mapViews = hostApp().getMapViews();
      mapViews?.forEach((mapView) => {
        if (mapView.mapViewName === MAP_VIEW_NAME_ONLINE) {
          mapView.setTimelineDate(dayjs(timelineDate));
        }
      });
      requestApi.time = getDateTimeFromValue(
        state.timelineTime,
        timelineDate,
      ).format('YYYY-MM-DD HH:mm:ss');
      state.timelineDate = timelineDate;
    },
    updateTimelineTime(state, action: PayloadAction<{ timelineTime: number }>) {
      const { timelineTime } = action.payload;
      const mapViews = hostApp().getMapViews();
      mapViews?.forEach((mapView) => {
        mapView.setTimelineDateTime(timelineTime);
      });
      requestApi.time = getDateTimeFromValue(
        timelineTime,
        state.timelineDate,
      ).format('YYYY-MM-DD HH:mm:ss');
      state.timelineTime = timelineTime;
    },
    updateTimelineWarn(state, action: PayloadAction<{ list: WarnInfoList }>) {
      const { list } = action.payload;
      state.timelineWarn = list;
    },
    updateTimelineEvent(
      state,
      action: PayloadAction<{ list: EventSchedulingBasicInfo[] }>,
    ) {
      const { list } = action.payload;
      state.timelineEvent = list;
    },
    updateDevicesWarns(
      state,
      action: PayloadAction<{ devicesWarnsList: WarnInfoList }>,
    ) {
      const { devicesWarnsList } = action.payload;
      const devicesWarns = formatDevicesWarns(
        state.devicesWarns,
        devicesWarnsList.reduce(
          (acc: WarnDetail[], obj) => acc.concat(obj.details),
          [],
        ),
      );
      state.devicesWarns = devicesWarns;
    },
    updateTempTime(
      state,
      action: PayloadAction<{ tempTime: string | undefined }>,
    ) {
      const { tempTime } = action.payload;
      const mapViews = hostApp().getMapViews();
      mapViews?.forEach((mapView) => {
        mapView.setTempTime(tempTime ? dayjs(tempTime) : undefined);
      });
      state.tempTime = tempTime ? dayjs(tempTime) : undefined;
    },
    setViewThemeTime() {},
    resetState() {
      return initialState;
    },
  },
});

export const { actions: timelineActions } = slice;

export const useTimelineSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: timelineSaga });
  return { actions: slice.actions };
};
