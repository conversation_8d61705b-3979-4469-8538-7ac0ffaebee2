/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  updateCurrentDeviceColorData,
  updateCurrentLegendData,
} from '@waterdesk/data/legend-data';
import { setViewThemeTime } from '@waterdesk/request/set-view-theme-time';
import dayjs from 'dayjs';
import {
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { hostApp } from 'src/app/host-app';
import { legendActions } from '../legend';
import { timelineActions } from '.';
import { selectTimelineDate, selectTimelineTime } from './selectors';

function* setViewThemeTimeSaga() {
  try {
    const mapViews = hostApp().getMapViews();
    if (!mapViews) return;
    const timelineDate: ReturnType<typeof selectTimelineDate> =
      yield select(selectTimelineDate);
    const timelineTime: ReturnType<typeof selectTimelineTime> =
      yield select(selectTimelineTime);
    const time: string = dayjs(timelineDate)
      .set('minute', timelineTime)
      .format('YYYY-MM-DD HH:mm:ss');
    const fcExt = dayjs().isBefore(time, 'minute') ? '_FC1' : undefined;

    for (let i = 0; mapViews.length > i; i += 1) {
      const mapView = mapViews[i];
      let currentTime;
      if (mapView) {
        currentTime = mapView.dateTime?.format('YYYY-MM-DD HH:mm:ss');
      }
      const result: SagaReturnType<typeof setViewThemeTime> = yield call(
        setViewThemeTime,
        {
          time: currentTime || time,
          fc_ext: fcExt,
          view_id: mapView.getViewId(),
        },
      );
      if (result.status === 'Success') {
        if (result.deviceColorData) {
          updateCurrentDeviceColorData(result.deviceColorData);
        }
        mapView.redraw();
        if (result.legendData) {
          updateCurrentLegendData(result.legendData);
          yield put(
            legendActions.legendChanged({
              code: dayjs().valueOf(),
            }),
          );
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

export function* timelineSaga() {
  yield takeLatest(timelineActions.setViewThemeTime, setViewThemeTimeSaga);
}
