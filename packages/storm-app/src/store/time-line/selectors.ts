/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';

import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => state?.timeline || initialState;

export const selectTimelineDate = createSelector(
  [selectSlice],
  (state) => state.timelineDate,
);

export const selectTimelineTime = createSelector(
  [selectSlice],
  (state) => state.timelineTime,
);

export const selectTimelineDateTime = createSelector([selectSlice], (state) =>
  getDateTimeFromValue(state.timelineTime, state.timelineDate).format(
    'YYYY-MM-DD HH:mm:ss',
  ),
);

export const selectTimelineWarn = createSelector(
  [selectSlice],
  (state) => state.timelineWarn,
);

export const selectDevicesWarns = createSelector(
  [selectSlice],
  (state) => state.devicesWarns,
);

export const selectTempDateTime = createSelector(
  [selectSlice],
  (state) =>
    state.tempTime ??
    getDateTimeFromValue(state.timelineTime, state.timelineDate),
);

export const selectAutoPlay = createSelector(
  [selectSlice],
  (state) => state.autoPlay,
);
