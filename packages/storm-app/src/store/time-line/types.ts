/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import { DeviceWarningType, WarnInfoList } from '@waterdesk/data/warn';
import { Dayjs } from 'dayjs';

/* --- STATE --- */
export interface TimelineState {
  /** timelineDate as YYYY-MM-DD */
  timelineDate: string;
  /** timelineTime is mark timeSlider value,turn into time as HH:mm */
  timelineTime: number;
  /** Whether the timeline plays automatically or not */
  autoPlay: boolean;

  tempTime: Dayjs | undefined;
  /** 与时间轴date相关的警告信息  */
  timelineWarn: WarnInfoList;
  timelineEvent: EventSchedulingBasicInfo[];
  devicesWarns: DeviceWarningType;
}
