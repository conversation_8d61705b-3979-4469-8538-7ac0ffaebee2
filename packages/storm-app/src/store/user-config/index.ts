/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@waterdesk/core/store';
import { MenuInfo } from '@waterdesk/data/menu-data';
import { useInjectSaga } from 'redux-injectors';
import { useInjectReducer } from 'src/utils/redux-injectors';
import { reduxUserConfigSaga } from './saga';
import { GlobalConfig, LabelMode, UserConfigState } from './types';

export const initialState: UserConfigState = {
  searchHistory: [],
  labelTitle: true,
  labelIndicator: true,
  labelSimulation: false,
  labelMode: undefined,
  appLabelTitle: true,
  appLabelIndicator: true,
  appLabelSimulation: false,
  appLabelMode: undefined,
  chartWarnMark: true,
  chartShowMaxLimitation: true,
  chartShowMaxMinRange: true,
  showHelp: true,
  globalConfig: undefined,
};

export const slice = createSlice({
  name: 'userConfig',
  initialState,
  reducers: {
    updateSearchHistory(
      state,
      action: PayloadAction<{ searchHistory: string[] }>,
    ) {
      const { searchHistory } = action.payload;
      state.searchHistory = searchHistory;
    },
    updateLabelTitle(
      state,
      action: PayloadAction<{ labelTitle: boolean; initialize?: boolean }>,
    ) {
      const { labelTitle } = action.payload;
      state.labelTitle = labelTitle;
    },
    updateLabelIndicator(
      state,
      action: PayloadAction<{ labelIndicator: boolean; initialize?: boolean }>,
    ) {
      const { labelIndicator } = action.payload;
      state.labelIndicator = labelIndicator;
    },
    updateLabelSimulation(
      state,
      action: PayloadAction<{ labelSimulation: boolean; initialize?: boolean }>,
    ) {
      const { labelSimulation } = action.payload;
      state.labelSimulation = labelSimulation;
    },
    updateLabelMode(
      state,
      action: PayloadAction<{ labelMode: LabelMode; initialize?: boolean }>,
    ) {
      const { labelMode } = action.payload;
      state.labelMode = labelMode;
    },
    updateAppLabelTitle(
      state,
      action: PayloadAction<{ appLabelTitle: boolean; initialize?: boolean }>,
    ) {
      const { appLabelTitle } = action.payload;
      state.appLabelTitle = appLabelTitle;
    },
    updateAppLabelIndicator(
      state,
      action: PayloadAction<{
        appLabelIndicator: boolean;
        initialize?: boolean;
      }>,
    ) {
      const { appLabelIndicator } = action.payload;
      state.appLabelIndicator = appLabelIndicator;
    },
    updateAppLabelSimulation(
      state,
      action: PayloadAction<{
        appLabelSimulation: boolean;
        initialize?: boolean;
      }>,
    ) {
      const { appLabelSimulation } = action.payload;
      state.appLabelSimulation = appLabelSimulation;
    },
    updateAppLabelMode(
      state,
      action: PayloadAction<{ appLabelMode: LabelMode; initialize?: boolean }>,
    ) {
      const { appLabelMode } = action.payload;
      state.appLabelMode = appLabelMode;
    },

    updateChartWarnMark(
      state,
      action: PayloadAction<{ chartWarnMark: boolean; initialize?: boolean }>,
    ) {
      const { chartWarnMark } = action.payload;
      state.chartWarnMark = chartWarnMark;
    },
    updateMaxLimitation(
      state,
      action: PayloadAction<{
        chartShowMaxLimitation: boolean;
        initialize?: boolean;
      }>,
    ) {
      const { chartShowMaxLimitation } = action.payload;
      state.chartShowMaxLimitation = chartShowMaxLimitation;
    },
    updateShowRange(
      state,
      action: PayloadAction<{
        chartShowMaxMinRange: boolean;
        initialize?: boolean;
      }>,
    ) {
      const { chartShowMaxMinRange } = action.payload;
      state.chartShowMaxMinRange = chartShowMaxMinRange;
    },
    updateGlobalConfig(
      state,
      action: PayloadAction<{ globalConfig: GlobalConfig }>,
    ) {
      const { globalConfig } = action.payload;
      return {
        ...state,
        globalConfig: {
          noticeQueryDuration: globalConfig.noticeQueryDuration ?? 48,
          systemThemeConfig: globalConfig.systemThemeConfig,
          indicatorSelector: globalConfig.indicatorSelector ?? [],
          systemVersionInfo: globalConfig.systemVersionInfo ?? {},
          scheduleConfig: globalConfig.scheduleConfig ?? [],
          chartConfig: globalConfig.chartConfig ?? {},
          commandConfig: globalConfig.commandConfig ?? {},
          dispatchEventConfig: globalConfig.dispatchEventConfig ?? {},
          planProjectConfig: globalConfig.planProjectConfig ?? {},
          warnConfig: globalConfig.warnConfig ?? {},
          warnGroupDisplayConfig: globalConfig.warnGroupDisplayConfig ?? {},
          schemeConfig: globalConfig.schemeConfig ?? {},
          relatedEvent: globalConfig.relatedEvent ?? false,
          displayScadaTooltip: globalConfig.displayScadaTooltip ?? false,
          leftSiderWidth: globalConfig.leftSiderWidth ?? 0,
          envelopMinMaxField: globalConfig.envelopMinMaxField,
          bubbleBoxConfig: globalConfig.bubbleBoxConfig ?? {},
          scadaDashboard: globalConfig.scadaDashboard ?? {},
          energyDashboard: globalConfig.energyDashboard ?? {},
          operationConfig: globalConfig.operationConfig ?? {},
          realtimeTimelineSetting: globalConfig.realtimeTimelineSetting ?? 0,
        },
      };
    },
    updateUserPermission(
      state,
      action: PayloadAction<{ userPermission?: MenuInfo[] }>,
    ) {
      const { userPermission } = action.payload;
      state.userPermission = userPermission ?? [];
    },
    updateShowHelp(state, action: PayloadAction<{ visible: boolean }>) {
      const { visible } = action.payload;
      state.showHelp = visible;
    },
  },
});

export const { actions: userConfigActions } = slice;

export const useUserConfigSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  useInjectSaga({ key: slice.name, saga: reduxUserConfigSaga });
};
