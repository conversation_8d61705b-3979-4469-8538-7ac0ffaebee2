/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PayloadAction } from '@reduxjs/toolkit';
import {
  saveAppOverlayLabelIndicator,
  saveAppOverlayLabelMode,
  saveAppOverlayLabelSimulation,
  saveAppOverlayLabelTitle,
  saveChartShowWarnMark,
  saveMaxLimitation,
  saveOverlayLabelIndicator,
  saveOverlayLabelMode,
  saveOverlayLabelSimulation,
  saveOverlayLabelTitle,
  saveShowRange,
} from '@waterdesk/request/user-config/set-user-config-value';
import { takeLatest } from 'redux-saga/effects';
import { APP_NAME } from 'src/config';
import { userConfigActions } from '.';
import { LabelMode } from './types';

function* saveLabelTitle(
  action: PayloadAction<{ labelTitle: boolean; initialize?: boolean }>,
) {
  const { labelTitle, initialize } = action.payload;
  if (initialize) return;

  yield saveOverlayLabelTitle(labelTitle, APP_NAME);
}

function* saveLabelIndicator(
  action: PayloadAction<{ labelIndicator: boolean; initialize?: boolean }>,
) {
  const { labelIndicator, initialize } = action.payload;
  if (initialize) return;

  yield saveOverlayLabelIndicator(labelIndicator, APP_NAME);
}

function* saveLabelSimulation(
  action: PayloadAction<{ labelSimulation: boolean; initialize?: boolean }>,
) {
  const { labelSimulation, initialize } = action.payload;
  if (initialize) return;

  yield saveOverlayLabelSimulation(labelSimulation, APP_NAME);
}

function* saveLabelMode(
  action: PayloadAction<{ labelMode: LabelMode; initialize?: boolean }>,
) {
  const { labelMode, initialize } = action.payload;
  if (initialize) return;

  yield saveOverlayLabelMode(labelMode, APP_NAME);
}

function* saveAppLabelTitle(
  action: PayloadAction<{ appLabelTitle: boolean; initialize?: boolean }>,
) {
  const { appLabelTitle, initialize } = action.payload;
  if (initialize) return;

  yield saveAppOverlayLabelTitle(appLabelTitle, APP_NAME);
}

function* saveAppLabelIndicator(
  action: PayloadAction<{ appLabelIndicator: boolean; initialize?: boolean }>,
) {
  const { appLabelIndicator, initialize } = action.payload;
  if (initialize) return;

  yield saveAppOverlayLabelIndicator(appLabelIndicator, APP_NAME);
}

function* saveAppLabelSimulation(
  action: PayloadAction<{ appLabelSimulation: boolean; initialize?: boolean }>,
) {
  const { appLabelSimulation, initialize } = action.payload;
  if (initialize) return;

  yield saveAppOverlayLabelSimulation(appLabelSimulation, APP_NAME);
}

function* saveAppLabelMode(
  action: PayloadAction<{ appLabelMode: LabelMode; initialize?: boolean }>,
) {
  const { appLabelMode, initialize } = action.payload;
  if (initialize) return;

  yield saveAppOverlayLabelMode(appLabelMode, APP_NAME);
}

function* saveUpdateChartWarnMark(
  action: PayloadAction<{ chartWarnMark: boolean; initialize?: boolean }>,
) {
  const { chartWarnMark, initialize } = action.payload;
  if (initialize) return;

  yield saveChartShowWarnMark(chartWarnMark, APP_NAME);
}

function* saveUpdateMaxLimitation(
  action: PayloadAction<{
    chartShowMaxLimitation: boolean;
    initialize?: boolean;
  }>,
) {
  const { chartShowMaxLimitation, initialize } = action.payload;
  if (initialize) return;

  yield saveMaxLimitation(chartShowMaxLimitation, APP_NAME);
}

function* saveUpdateShowRange(
  action: PayloadAction<{
    chartShowMaxMinRange: boolean;
    initialize?: boolean;
  }>,
) {
  const { chartShowMaxMinRange, initialize } = action.payload;
  if (initialize) return;

  yield saveShowRange(chartShowMaxMinRange, APP_NAME);
}

export function* reduxUserConfigSaga() {
  yield takeLatest(userConfigActions.updateLabelTitle, saveLabelTitle);
  yield takeLatest(userConfigActions.updateLabelIndicator, saveLabelIndicator);
  yield takeLatest(
    userConfigActions.updateLabelSimulation,
    saveLabelSimulation,
  );
  yield takeLatest(userConfigActions.updateLabelMode, saveLabelMode);

  yield takeLatest(userConfigActions.updateAppLabelTitle, saveAppLabelTitle);
  yield takeLatest(
    userConfigActions.updateAppLabelIndicator,
    saveAppLabelIndicator,
  );
  yield takeLatest(
    userConfigActions.updateAppLabelSimulation,
    saveAppLabelSimulation,
  );
  yield takeLatest(userConfigActions.updateAppLabelMode, saveAppLabelMode);

  yield takeLatest(
    userConfigActions.updateChartWarnMark,
    saveUpdateChartWarnMark,
  );
  yield takeLatest(
    userConfigActions.updateMaxLimitation,
    saveUpdateMaxLimitation,
  );
  yield takeLatest(userConfigActions.updateShowRange, saveUpdateShowRange);
}
