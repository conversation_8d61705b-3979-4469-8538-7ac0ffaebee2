/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../root-state';
import { initialState } from '.';

const selectSlice = (state: RootState) => state?.userConfig ?? initialState;

export const selectSearchHistory = createSelector(
  [selectSlice],
  (state) => state.searchHistory,
);

export const selectLabelTitle = createSelector(
  [selectSlice],
  (state) => state.labelTitle,
);

export const selectLabelIndicator = createSelector(
  [selectSlice],
  (state) => state.labelIndicator,
);

export const selectLabelSimulation = createSelector(
  [selectSlice],
  (state) => state.labelSimulation,
);

export const selectLabelMode = createSelector(
  [selectSlice],
  (state) => state.labelMode,
);

export const selectAppLabelTitle = createSelector(
  [selectSlice],
  (state) => state.appLabelTitle,
);

export const selectAppLabelIndicator = createSelector(
  [selectSlice],
  (state) => state.appLabelIndicator,
);

export const selectAppLabelSimulation = createSelector(
  [selectSlice],
  (state) => state.appLabelSimulation,
);

export const selectAppLabelMode = createSelector(
  [selectSlice],
  (state) => state.appLabelMode,
);

export const selectChartWarnMark = createSelector(
  [selectSlice],
  (state) => state.chartWarnMark,
);

export const selectMaxLimitation = createSelector(
  [selectSlice],
  (state) => state.chartShowMaxLimitation,
);

export const selectShowRange = createSelector(
  [selectSlice],
  (state) => state.chartShowMaxMinRange,
);

export const selectGlobalConfig = createSelector(
  [selectSlice],
  (state) => state.globalConfig,
);

export const selectUserPermission = createSelector(
  [selectSlice],
  (state) => state.userPermission,
);

export const selectShowHelp = createSelector(
  [selectSlice],
  (state) => state.showHelp,
);
