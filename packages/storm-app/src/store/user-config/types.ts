/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ComponentsConfig, DefaultSeedToken } from '@waterdesk/core/theme';
import { DispatchEventType } from '@waterdesk/data/dispatch-data';
import { Shift } from '@waterdesk/data/dispatch-log/shift';
import { LevelType } from '@waterdesk/data/event-scheduling/basic-info';
import { MenuInfo } from '@waterdesk/data/menu-data';
import { EnergyDashboardConfig } from '@waterdesk/data/mini-dashboard/energy-data';
import { PlanProjectType, ProjectType } from '@waterdesk/data/plan-project';
import { ScadaDashboardConfig } from '@waterdesk/data/scada-dashboard-data';
import { SchemeTypeData } from '@waterdesk/data/scheme-config';
import {
  WarnGroupDisplayConfig,
  WarnPrimaryType,
  WarnTypeData,
} from '@waterdesk/data/warn';

export type LabelMode = undefined | string;

/** 首页气泡框配置项 */
interface BubbleBoxConfig {
  // 显示样式: 默认样式 | 仅显示数值
  displayStyle?: 'default' | 'onlyNumber';
  // 气泡框宽度：填入数字，不填则自动计算
  width?: number;
  // 是否展示水厂水泵
  showPump?: boolean;
  // 监测量配置
  indicatorConfig?: {
    // 监测量名称：必填项，不填则本条配置无效
    otype: string;
    // 数字颜色: 填入颜色值 (例如 #FFFFFF)，不填则默认为主题色
    color?: string;
    // 顺序值: 用于排序，数值越大越优先，不填则默认为0
    order?: number;
  }[];
  maxVisibleIndicators?: number;
  disableCollisionDetect?: boolean;
}

export interface EditableScheduleScope {
  pastMonth: boolean;
  currentMonth: boolean;
  futureMonth: boolean;
}

export interface OperationConfig {
  operationTypeList: Record<string, string>;
  hideInModelService: string[];
}

type UserOperationConfig = {
  searchHistory: string[];
  /** 监测展示: 是否显示名称 */
  labelTitle: boolean;
  /** 监测展示: 是否显示监测指标 */
  labelIndicator: boolean;
  /** 监测展示: 是否显示计算值 */
  labelSimulation: boolean;
  /** 监测气泡显示模式 */
  labelMode: LabelMode;
  /** app监测展示: 是否显示名称 */
  appLabelTitle: boolean;
  /** app监测展示: 是否显示监测指标 */
  appLabelIndicator: boolean;
  /** app监测展示: 是否显示计算值 */
  appLabelSimulation: boolean;
  /** app监测气泡显示模式 */
  appLabelMode: LabelMode;
  /** 曲线框 - 显示警告的checked状态 */
  chartWarnMark: boolean;
  /** 曲线框警告 - 显示警告线的checked状态 */
  chartShowMaxLimitation: boolean;
  /** 曲线框警告 - 显示包络线的checked状态 */
  chartShowMaxMinRange: boolean;
  /** 是否显示用户帮助 */
  showHelp: boolean;
};

export interface PlantProjectionOptions {
  label: string;
  value: string;
  isDefault?: boolean;
  dependentOptions?: string[];
}
/** 计划工程表单配置选项 */
export interface PlantProjectionConfig {
  type?: string; // 组件类型
  label?: string; // 展示名称
  name?: string; // 字段值
  colSpan?: number; // 栅格占位
  rules?: any[]; // 校验规则
  options?: PlantProjectionOptions[]; // 下拉选项
  dependsOn?: string; // 依赖字段
  dependencies?: string[]; // 依赖字段值
  filePath?: string; // 文件根目录
}

export type GlobalConfig = {
  /** 通知警告查询时长 default: 48 hours */
  noticeQueryDuration: number;
  /** 系统主题相关配置, main: 默认主题; solution: 方案主题 */
  systemThemeConfig?: {
    main?: {
      token: DefaultSeedToken;
      components: ComponentsConfig;
      darkToken?: DefaultSeedToken;
      darkComponents?: ComponentsConfig;
    };
    solution?: {
      token: DefaultSeedToken;
      components: ComponentsConfig;
      darkToken?: DefaultSeedToken;
      darkComponents?: ComponentsConfig;
    };
  };
  /** 指标选择器配置 */
  indicatorSelector?: {
    label: string;
    value: string;
  }[];
  /** 排班表部门配置 */
  scheduleConfig: {
    /** 班次信息 */
    shifts: Shift[];
    /** 客户排班的时间偏移量，以分钟为单位 */
    shiftOffsetMinutes: number;
    /** 参与排班部门 */
    department: string[];
    /** 交接班可顺延时间，以分钟为单位 */
    handoverTimeBufferMinutes: number;
    /* 仅当班人可进行日志操作 */
    operateOnlyOnDuty: boolean;
    /** 排班页面允许编辑范围 */
    editableScheduleScope: EditableScheduleScope;
  };
  /** 系统版本信息 */
  systemVersionInfo: {
    systemName?: string;
    version?: string;
  };
  chartConfig: {
    online: {
      showModel: boolean;
      showAsLineType: boolean;
    };
    analysis: {
      showModel: boolean;
      showAsLineType: boolean;
    };
  };
  /** 调度指令配置 */
  commandConfig: {
    /** 指令超时阈值 */
    timeoutThreshold: number;
    finalDepartmentId: string;
    /** 播放接收指令音频配置 */
    playReceiveCommandAudio: {
      isPlay: boolean;
      loop: boolean;
    };
    /** 播放回复指令音频配置 */
    playReplyCommandAudio: {
      isPlay: boolean;
    };
  };
  /** 调度事件配置 */
  dispatchEventConfig: {
    dispatchEventType: DispatchEventType;
    openDefault: boolean;
    // 默认等级
    dispatchLevelType: LevelType[];
  };
  /** 计划工程配置 */
  planProjectConfig: {
    planProjectType: PlanProjectType;
    planProjectFormConfig: {
      type: ProjectType;
      config: PlantProjectionConfig[];
    }[];
    planProjectConnectModule: {
      type: ProjectType;
      module: string[];
      format: string;
    }[];
  };
  /** 警告配置 */
  warnConfig: {
    warnType: WarnTypeData;
    warnClassId: WarnTypeData;
    nonAssessmentWarnTypes: WarnPrimaryType[];
    warnSource: WarnTypeData;
  };
  /** 警告分组显示规则设置 */
  warnGroupDisplayConfig: WarnGroupDisplayConfig;
  /** 曲线标注配置 */
  schemeConfig: {
    schemeType: SchemeTypeData;
  };
  relatedEvent: boolean;
  /** 是否展示SCADA tooltip控制配置 */
  displayScadaTooltip: boolean;
  /** 左边侧边栏宽度, default: 0 */
  leftSiderWidth: number;
  /** 包络线字段配置 */
  envelopMinMaxField: {
    minField: string;
    maxField: string;
  };
  /** 气泡框配置 */
  bubbleBoxConfig: BubbleBoxConfig;
  scadaDashboard: ScadaDashboardConfig;
  energyDashboard: EnergyDashboardConfig;
  operationConfig: OperationConfig;
  realtimeTimelineSetting: number;
};

export interface UserConfigState extends UserOperationConfig {
  globalConfig?: GlobalConfig;
  userPermission?: MenuInfo[];
}
