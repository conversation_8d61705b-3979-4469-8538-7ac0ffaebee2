/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AntdThemeProvider as CoreAntdThemeProvider,
  StyledThemeProvider as CoreStyledThemeProvider,
} from '@waterdesk/core/theme';
import { useSelector } from 'react-redux';
import { useThemeSlice } from 'src/store/theme';
import {
  selectComponentsConfig,
  selectDarkModeComponentsConfig,
  selectDarkModeSeedToken,
  selectSeedToken,
  selectTheme,
} from 'src/store/theme/selector';

type WithChildren<T = {}> = T & { children?: React.ReactNode };

type Props = WithChildren<{}>;

/**
 * Storm App 的 AntdThemeProvider 适配器
 *
 * 这个组件桥接了 storm-app 的主题 store 和 core 包的 AntdThemeProvider
 */
export const AntdThemeProvider = (props: Props) => {
  useThemeSlice();
  const themeType = useSelector(selectTheme);
  const systemSeedToken = useSelector(selectSeedToken);
  const componentsConfig = useSelector(selectComponentsConfig);
  const darkModeSystemSeedToken = useSelector(selectDarkModeSeedToken);
  const darkModeComponentsConfig = useSelector(selectDarkModeComponentsConfig);

  const { children } = props;

  return (
    <CoreAntdThemeProvider
      theme={themeType}
      systemSeedToken={systemSeedToken}
      componentsConfig={componentsConfig}
      darkModeSeedToken={darkModeSystemSeedToken}
      darkModeComponentsConfig={darkModeComponentsConfig}
      setDataAttribute={true} // storm-app 需要设置 DOM 属性
    >
      {children}
    </CoreAntdThemeProvider>
  );
};

/**
 * Storm App 的 StyledThemeProvider 适配器
 *
 * 直接使用 core 包的 StyledThemeProvider
 */
export const StyledThemeProvider = CoreStyledThemeProvider;
