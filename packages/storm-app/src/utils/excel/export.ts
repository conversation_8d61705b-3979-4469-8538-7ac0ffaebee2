/*
  (C) Copyright 2023-2024 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { utils, write, writeFile, writeFileXLSX } from 'xlsx';

export type IgnoreConfig = {
  /** 需要忽略的 columns title */
  titles?: string[];
  /** 需要忽略的 columns dataIndex */
  dataIndexes?: string[];
};

type FormatTableDataForExcelProps = {
  /** 表格数据 */
  data: Record<string, any>[];
  /** 表格列配置 */
  columns: any[];
  /** 忽略配置 */
  ignoreConfig?: IgnoreConfig;
};

/**
 *  格式化表格数据为导出 Excel 所需的数据格式
 */
export const formatTableDataForExcel = ({
  data,
  columns,
  ignoreConfig,
}: FormatTableDataForExcelProps): Record<string, any>[] => {
  const { titles = [], dataIndexes = [] } = ignoreConfig ?? {};

  return data.map((row) => {
    const exportItem: Record<string, any> = {};

    columns?.forEach(({ title, dataIndex, renderText, render }) => {
      if (titles.includes(title) || dataIndexes.includes(dataIndex)) {
        return;
      }

      const value = row[dataIndex];
      let renderTextResult: string | undefined;

      if (renderText) {
        renderTextResult = renderText(value, row);
      } else if (render) {
        renderTextResult =
          typeof render(value, row) === 'string' ? render(value, row) : value;
      }

      exportItem[title] = renderTextResult ?? value;
    });

    return exportItem;
  });
};

export function downloadExcel(
  data: Record<string, any>[],
  filename: string,
  excelFormat: 'xlsx' | 'csv' = 'xlsx',
) {
  const ws = utils.json_to_sheet(data);
  const wb = utils.book_new();

  utils.book_append_sheet(wb, ws, 'Sheet1');

  if (excelFormat === 'csv') {
    write(wb, { bookType: 'csv', type: 'buffer' });
    writeFile(wb, `${filename}.csv`);
  } else {
    writeFileXLSX(wb, `${filename}.xlsx`);
  }
}
