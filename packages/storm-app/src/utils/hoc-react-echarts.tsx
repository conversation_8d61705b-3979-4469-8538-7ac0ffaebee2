/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Theme, ThemeToken, useToken } from '@waterdesk/core/theme';
import React from 'react';
import { useSelector } from 'react-redux';
import { useThemeSlice } from 'src/store/theme';
import { selectTheme } from 'src/store/theme/selector';

const HocReactEcharts = <P extends {}, R = unknown>(
  WrappedComponent: React.ComponentType<
    P & { theme?: Theme; themeToken?: ThemeToken }
  >,
) => {
  function ForwardedComponent(
    props: React.PropsWithoutRef<P>,
    ref: React.Ref<R>,
  ) {
    useThemeSlice();
    const theme = useSelector(selectTheme);
    const { token } = useToken();
    return (
      <WrappedComponent
        {...(props as P)}
        theme={theme}
        ref={ref}
        themeToken={token}
      />
    );
  }

  return React.forwardRef<R, P>(ForwardedComponent);
};

export default HocReactEcharts;
