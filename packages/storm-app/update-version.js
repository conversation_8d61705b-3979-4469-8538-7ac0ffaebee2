import fs, { readFileSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取当前项目的 package.json 中的版本号
const packageJsonPath = path.resolve(__dirname, 'package.json');
const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

const version = packageJson.version || '1.0.0';

// 获取当前时间
const buildTime = new Date().toISOString();

// 构建版本信息对象
const versionInfo = {
  version,
  buildTime,
};

// 将版本信息写入到 public/version.json 文件中
const versionFilePath = path.resolve(__dirname, 'public', 'version.json');
fs.writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2));

console.log('Version info updated:', versionInfo);
