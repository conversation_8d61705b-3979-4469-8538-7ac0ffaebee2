import react from '@vitejs/plugin-react';
import { execSync } from 'child_process';
import dayjs from 'dayjs';
import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';

// 获取版本信息（与 craco 配置保持一致）
let version = '1.0.0';
try {
  // 项目根目录路径（包含 .git 的目录）
  const projectRoot = resolve(__dirname, '../..');

  // 执行 git 命令获取信息
  const gitDate = execSync('git log -1 --format=%ct', {
    cwd: projectRoot,
    encoding: 'utf8',
  }).trim();
  const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', {
    cwd: projectRoot,
    encoding: 'utf8',
  }).trim();
  const gitShort = execSync('git rev-parse --short HEAD', {
    cwd: projectRoot,
    encoding: 'utf8',
  }).trim();

  // 转换时间戳为日期格式
  const date = new Date(parseInt(gitDate, 10) * 1000);
  version = `${dayjs(date).format('YYYYMMDD')}.${gitBranch}.${gitShort}`;
} catch (error) {
  console.warn('无法获取 Git 信息，使用默认版本号', error);
}

export default defineConfig(({ mode }: { mode: string }) => {
  // 正确加载环境变量
  const env = loadEnv(mode, process.cwd(), '');
  return {
    // 支持环境变量配置 base 路径，默认使用相对路径
    base: process.env.VITE_BASE_URL || './',
    plugins: [
      react({
        babel: {
          plugins: [
            [
              'babel-plugin-styled-components',
              {
                displayName: true,
                fileName: false,
              },
            ],
          ],
        },
      }),
    ],

    // 路径别名配置
    resolve: {
      alias: {
        '@waterdesk/data': resolve(__dirname, '../data/src'),
        '@waterdesk/request': resolve(__dirname, '../request/src'),
        '@waterdesk/core': resolve(__dirname, '../core/src'),
        src: resolve(__dirname, './src'),
        // 解决 ol-contextmenu 模块导入问题
        'ol/control/Control': resolve(
          __dirname,
          '../../node_modules/ol/control/Control.js',
        ),
        'ol/MapBrowserEvent': resolve(
          __dirname,
          '../../node_modules/ol/MapBrowserEvent.js',
        ),
      },
      extensions: ['.js', '.ts', '.tsx', '.jsx', '.json'],
    },

    // 环境变量
    define: {
      'process.env.VERSION': JSON.stringify(version),
      'process.env.UPDATEDATE': JSON.stringify(dayjs().format('YYYY-MM-DD')),
      // 解决 Node.js 环境变量在浏览器中的问题
      global: 'globalThis',
      'process.env.NODE_ENV': JSON.stringify(mode),
      // 正确传递环境变量给浏览器
      'import.meta.env.VITE_ENV': JSON.stringify(env.VITE_ENV || mode),
      'import.meta.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL),
      'import.meta.env.VITE_APP_NAME': JSON.stringify(env.VITE_APP_NAME),
    },

    // 服务器配置
    server: {
      host: '0.0.0.0', // 允许通过IP地址访问
      port: 3000,
      open: true,
      historyApiFallback: true,
    },

    // 构建配置
    build: {
      outDir: 'build',
      sourcemap: mode === 'development',
      // 处理静态资源
      assetsDir: 'static',
      // 分块策略优化
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          // 手动分块减少单个文件大小
          manualChunks: {
            vendor: ['react', 'react-dom'],
            antd: ['antd', '@ant-design/icons'],
            charts: ['echarts'],
            utils: ['lodash', 'dayjs'],
            openlayers: ['ol', 'proj4'],
          },

          // 处理静态资源文件名
          assetFileNames: (assetInfo: any) => {
            const info = assetInfo.name!.split('.');
            const ext = info[info.length - 1];
            if (
              /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name!)
            ) {
              return `static/media/[name].[hash][extname]`;
            }
            if (
              /\.(png|jpe?g|gif|svg|webp|ico)(\?.*)?$/i.test(assetInfo.name!)
            ) {
              return `static/media/[name].[hash][extname]`;
            }
            if (/\.(woff2?|ttf|eot|otf)(\?.*)?$/i.test(assetInfo.name!)) {
              return `static/fonts/[name].[hash][extname]`;
            }
            if (ext === 'css') {
              return `static/css/[name].[hash][extname]`;
            }
            return `static/[ext]/[name].[hash][extname]`;
          },
          chunkFileNames: 'static/js/[name].[hash].js',
          entryFileNames: 'static/js/[name].[hash].js',
        },
      },
    },

    // 优化依赖
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'antd',
        '@ant-design/icons',
        'lodash',
        'dayjs',
        'axios',
        'ol',
        'proj4',
        'echarts',
      ],
    },

    // CSS 配置
    css: {
      modules: {
        localsConvention: 'camelCase' as const,
      },
    },

    // 处理不同文件类型
    assetsInclude: ['**/*.docx'],
  };
});
