import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts'],
    css: true,
  },
  resolve: {
    alias: {
      '@waterdesk/data': resolve(__dirname, '../data/src'),
      '@waterdesk/request': resolve(__dirname, '../request/src'),
      src: resolve(__dirname, './src'),
    },
  },
});
