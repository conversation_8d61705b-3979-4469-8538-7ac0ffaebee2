window.VERSION = '20250613.main.5e1237f';

window.HSConfig = {
  HSWEBSERVER: '{ORIGIN}/newDrainGm/',
  /** 调度系统 SUPPLY_SCHEDULING / 模型系统 SUPPLY_MODEL  */
  APPNAME: 'SUPPLY_SCHEDULING',
  SYSTEM_CONFIG: {
    systemName: '在线系统',
    systemSubTitle: 'WaterDesk-Live 在线系统',
    /** 如果不配置，则根据主题自动调整 */
    systemSubTitleColor: '#fff',
    showWatermark: true,
    watermarkImage: 'system-watermark',
    /** 支持深色(light)/浅色模式(dark)， 默认使用light模式
     *  自动根据系统个人设置的主题改变,通过后缀区分,格式为：SYSTEM_CONFIG.logoImage-模式
     *  例如配置为 logoImage: 'system-logo'时：
     *  上传图标，需要上传2张图片：浅色模式：system-logo-light, 深色模式：system-logo-dark
     */
    logoImage: 'system-logo',
    /** 同 watermarkImage */
    loginLogoImage: 'login-logo',
    loginBackgroundImage: 'login-background',
    appLoginBackgroundImage: 'app-login-background',
  },
  weComAgentId: 1000003,
  ssoLoginUrl: '{ORIGIN}/ssoLogin',
  loginValidation: false,
  // CASLogin: true,
  // mobile: true,
  /** app设备判断: 企业微信 wxwork / 微信 micromessenger  */
  // appDeviceDetect: 'wxwork',
  // wxworkAuthUrl:'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwac9c2859c1c0cea1&redirect_uri=http%3A%2F%2Finfo-water.com%3A81%2FcallbackWeixinLoin&response_type=code&scope=snsapi_base&state=STATE&agentid=1000003#wechat_redirect',
};
