/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { StrictMode } from 'react';
import { RouterProvider } from 'react-router';
import routes from './app/router';
import { GlobalStyles } from './styles/global-styles';
import {
  AntdThemeProvider,
  StyledThemeProvider,
} from './styles/theme-provider-adapter';

function App() {
  return (
    <StrictMode>
      <AntdThemeProvider>
        <StyledThemeProvider>
          <GlobalStyles />
          <RouterProvider router={routes} />
        </StyledThemeProvider>
      </AntdThemeProvider>
    </StrictMode>
  );
}

export default App;
