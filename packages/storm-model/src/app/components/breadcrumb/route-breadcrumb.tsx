/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { HomeOutlined } from '@ant-design/icons';
import type { BreadcrumbProps } from 'antd';
import { Breadcrumb } from 'antd';
import { useLocation } from 'react-router';
import { useNavigation } from '@/app/hooks/use-navigation';
import { getRouteHierarchy } from '@/config/route-config';

export const RouteBreadcrumb: React.FC = () => {
  const location = useLocation();
  const { goHome, navigate } = useNavigation();

  // 首页不显示面包屑
  if (location.pathname === '/home' || location.pathname === '/') {
    return null;
  }

  // 从路由配置中获取层级结构
  const routeHierarchy = getRouteHierarchy(location.pathname);

  const breadcrumbItems: BreadcrumbProps['items'] = [
    {
      title: (
        <span
          onClick={goHome}
          style={{ cursor: 'pointer' }}
        >
          <HomeOutlined style={{ marginRight: 4 }} />
          主页
        </span>
      ),
    },
  ];

  // 根据路由层级生成面包屑项
  routeHierarchy.forEach((routeConfig, index) => {
    const isLast = index === routeHierarchy.length - 1;

    // 处理动态路由标签
    let label = routeConfig.label;
    if (routeConfig.getDynamicLabel && routeConfig.path.includes(':')) {
      const pathParts = location.pathname.split('/');
      const routeParts = routeConfig.path.split('/');
      const params: Record<string, string> = {};

      routeParts.forEach((part, i) => {
        if (part.startsWith(':')) {
          const paramName = part.slice(1);
          params[paramName] = pathParts[i] || '';
        }
      });

      label = routeConfig.getDynamicLabel(params);
    }

    breadcrumbItems.push({
      title: isLast ? (
        <span>{label}</span>
      ) : (
        <span
          onClick={() =>
            navigate(
              routeConfig.path.includes(':')
                ? location.pathname
                : routeConfig.path,
            )
          }
          style={{ cursor: 'pointer', color: '#1890ff' }}
        >
          {label}
        </span>
      ),
    });
  });

  // 如果只有主页，不显示面包屑
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <Breadcrumb
      items={breadcrumbItems}
      style={{ marginBottom: 16 }}
    />
  );
};
