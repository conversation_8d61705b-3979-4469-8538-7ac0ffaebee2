/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CounterUI } from '@/components/counter/counter-ui';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  decrement,
  increment,
  incrementByAmount,
  reset,
  setLoading,
} from '@/store/slices/counter-slice';

export const Counter: React.FC = () => {
  const { value, loading } = useAppSelector((state) => state.counter);
  const dispatch = useAppDispatch();

  const handleIncrement = () => {
    dispatch(increment());
  };

  const handleDecrement = () => {
    dispatch(decrement());
  };

  const handleReset = () => {
    dispatch(reset());
  };

  const handleIncrementByAmount = (amount: number) => {
    dispatch(incrementByAmount(amount));
  };

  const handleAsyncIncrement = () => {
    dispatch(setLoading(true));
    setTimeout(() => {
      dispatch(incrementByAmount(5));
      dispatch(setLoading(false));
    }, 1000);
  };

  return (
    <CounterUI
      value={value}
      loading={loading}
      onIncrement={handleIncrement}
      onDecrement={handleDecrement}
      onReset={handleReset}
      onIncrementByAmount={handleIncrementByAmount}
      onAsyncIncrement={handleAsyncIncrement}
    />
  );
};
