/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Timeline } from '@waterdesk/core/components/web';
import { Dayjs } from 'dayjs';

const TimeLineContainer = () => {
  return (
    <Timeline
      timeValue={0}
      dateValue={new Dayjs()}
      updateDateValue={(value: Dayjs): void => {
        throw new Error('Function not implemented.');
      }}
      updateTimeValue={(value: number): void => {
        throw new Error('Function not implemented.');
      }}
      autoPlay={false}
    />
  );
};

export default TimeLineContainer;
