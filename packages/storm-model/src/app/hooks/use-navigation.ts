/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useLocation, useNavigate } from 'react-router';

export const useNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // const goTo = (path: RoutePath) => {
  //   navigate(path);
  // };

  // const goHome = () => goTo(ROUTE_PATHS.HOME);
  // const goToCounter = () => goTo(ROUTE_PATHS.COUNTER);
  // const goToAbout = () => goTo(ROUTE_PATHS.ABOUT);
  // const goToTheme = () => goTo(ROUTE_PATHS.THEME);

  const isCurrentPath = (path: string) => location.pathname === path;

  return {
    navigate,
    location,
    isCurrentPath,
    currentPath: location.pathname,
  };
};
