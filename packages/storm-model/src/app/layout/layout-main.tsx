/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  HeaderActions,
  HeaderLogo,
  HeaderMenu,
  HelpDropdown,
  LayoutContent,
  LayoutHeader,
} from '@waterdesk/core/components/web';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useOutlet, useParams } from 'react-router';
import { getSystemIconBasePath, SYSTEM_CONFIG } from 'src/config';
import { useThemeSlice } from 'src/store/theme';
import { selectTheme } from 'src/store/theme/selector';

const LayoutMain = () => {
  useThemeSlice();

  const theme = useSelector(selectTheme);

  const { hiddenLayout } = useParams();
  const outlet = useOutlet();

  const menuItems = [
    {
      key: '1',
      label: '首页',
    },
    {
      key: '2',
      label: '监控',
    },
    {
      key: '3',
      label: '警告',
    },
  ];

  const helpItems = [
    {
      key: 'manual',
      label: '使用手册',
    },
    {
      key: 'updateLog',
      label: '更新日志',
    },
    {
      key: 'about',
      label: '版本信息',
    },
  ];

  const headerActionsContent = useMemo(
    () => (
      <>
        <HelpDropdown items={helpItems} />
      </>
    ),
    [helpItems],
  );

  return (
    <>
      <LayoutHeader
        hidden={!!hiddenLayout}
        logo={
          <HeaderLogo
            src={`${getSystemIconBasePath()}${SYSTEM_CONFIG.logoImage}-${theme}`}
            alt={SYSTEM_CONFIG.systemName}
            height={'45px'}
          />
        }
        menu={<HeaderMenu items={menuItems} />}
        actions={<HeaderActions size={1}>{headerActionsContent}</HeaderActions>}
      />
      <LayoutContent hiddenHeader={!!hiddenLayout}>{outlet}</LayoutContent>
    </>
  );
};

LayoutMain.displayName = 'LayoutMain';

export default LayoutMain;
