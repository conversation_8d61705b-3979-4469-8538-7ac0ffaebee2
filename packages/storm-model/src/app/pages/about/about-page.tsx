/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { InfoCircleOutlined } from '@ant-design/icons';
import { Card, Descriptions, Divider, Space, Tag, Typography } from 'antd';

const { Title, Paragraph } = Typography;

export const AboutPage: React.FC = () => {
  return (
    <div>
      <Typography>
        <Title level={2}>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          关于 Storm Model
        </Title>
        <Paragraph>
          Storm Model
          是一个现代化的前端开发脚手架，集成了最新的技术栈和最佳实践。
        </Paragraph>
      </Typography>

      <Divider />

      <Card title="技术栈">
        <Space
          size={[0, 8]}
          wrap
        >
          <Tag color="blue">React 19</Tag>
          <Tag color="green">TypeScript</Tag>
          <Tag color="purple">Redux Toolkit</Tag>
          <Tag color="orange">Ant Design 5</Tag>
          <Tag color="red">Vite</Tag>
          <Tag color="cyan">React Router</Tag>
          <Tag color="magenta">Styled Components</Tag>
          <Tag color="volcano">ESLint</Tag>
          <Tag color="gold">Prettier</Tag>
          <Tag color="lime">Storybook</Tag>
        </Space>
      </Card>

      <Divider />

      <Card title="项目信息">
        <Descriptions
          column={1}
          bordered
        >
          <Descriptions.Item label="项目名称">Storm Model</Descriptions.Item>
          <Descriptions.Item label="版本">0.0.0</Descriptions.Item>
          <Descriptions.Item label="开发者">
            上海慧水科技有限公司
          </Descriptions.Item>
          <Descriptions.Item label="技术架构">
            React + TypeScript + Redux Toolkit
          </Descriptions.Item>
          <Descriptions.Item label="UI 框架">Ant Design 5</Descriptions.Item>
          <Descriptions.Item label="构建工具">Vite</Descriptions.Item>
          <Descriptions.Item label="代码规范">
            ESLint + Prettier
          </Descriptions.Item>
          <Descriptions.Item label="组件测试">Storybook</Descriptions.Item>
        </Descriptions>
      </Card>

      <Divider />

      <Card title="开发规范">
        <Space
          direction="vertical"
          style={{ width: '100%' }}
        >
          <Paragraph>
            <strong>组件开发：</strong>使用函数式组件 + TypeScript + Hooks
          </Paragraph>
          <Paragraph>
            <strong>状态管理：</strong>Redux Toolkit + 动态注入 reducer
          </Paragraph>
          <Paragraph>
            <strong>样式管理：</strong>Ant Design + Styled Components
          </Paragraph>
          <Paragraph>
            <strong>路由管理：</strong>React Router 6 + 嵌套路由
          </Paragraph>
          <Paragraph>
            <strong>代码质量：</strong>ESLint + Prettier + TypeScript 严格模式
          </Paragraph>
        </Space>
      </Card>
    </div>
  );
};
