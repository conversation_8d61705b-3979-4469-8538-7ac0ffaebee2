/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  AlertOutlined,
  DeploymentUnitOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  MailOutlined,
  NotificationOutlined,
  ScheduleOutlined,
  SettingOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Card,
  Col,
  Descriptions,
  Input,
  Row,
  Select,
  Space,
  Switch,
  Tag,
  Typography,
} from 'antd';
import { useState } from 'react';
import styled from 'styled-components';
import { useNavigation } from '@/app/hooks/use-navigation';
import {
  configCategories,
  configItems,
  configStatuses,
} from '@/data/config-data';
import type { ConfigCategory, ConfigStatus } from '@/types/config-types';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

const PageContainer = styled.div`
  padding: 24px;
`;

const HeaderSection = styled.div`
  margin-bottom: 24px;
`;

const FilterSection = styled.div`
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
`;

const ConfigCard = styled(Card)<{ $category: string }>`
  height: 100%;
  border-left: 4px solid ${(props) => getCategoryColor(props.$category)};
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 20px;
  }
`;

const ConfigHeader = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
`;

const ConfigTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ConfigMeta = styled.div`
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  flex-shrink: 0;
`;

// 图标映射
const iconMap = {
  ScheduleOutlined: ScheduleOutlined,
  AlertOutlined: AlertOutlined,
  SettingOutlined: SettingOutlined,
  NotificationOutlined: NotificationOutlined,
  DeploymentUnitOutlined: DeploymentUnitOutlined,
  ExclamationCircleOutlined: ExclamationCircleOutlined,
  ToolOutlined: ToolOutlined,
  MailOutlined: MailOutlined,
};

// 获取分类颜色
function getCategoryColor(category: string): string {
  const categoryInfo = configCategories.find((cat) => cat.key === category);
  return categoryInfo?.color || '#1890ff';
}

// 获取图标组件
function getIconComponent(iconName: string) {
  const IconComponent = iconMap[iconName as keyof typeof iconMap];
  return IconComponent ? <IconComponent /> : <SettingOutlined />;
}

export const ConfigListPage: React.FC = () => {
  const { navigate } = useNavigation();
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<
    ConfigCategory | 'all'
  >('all');
  const [selectedStatus, setSelectedStatus] = useState<ConfigStatus | 'all'>(
    'all',
  );

  // 过滤配置项
  const filteredConfigs = configItems.filter((item) => {
    const matchSearch =
      !searchText ||
      item.name.toLowerCase().includes(searchText.toLowerCase()) ||
      item.description.toLowerCase().includes(searchText.toLowerCase()) ||
      item.tags?.some((tag) =>
        tag.toLowerCase().includes(searchText.toLowerCase()),
      );

    const matchCategory =
      selectedCategory === 'all' || item.category === selectedCategory;
    const matchStatus =
      selectedStatus === 'all' || item.status === selectedStatus;

    return matchSearch && matchCategory && matchStatus;
  });

  // 处理配置编辑
  const handleEditConfig = (configId: string) => {
    navigate(`/management/config/${configId}`);
  };

  // 处理状态切换
  const handleStatusToggle = (configId: string, enabled: boolean) => {
    console.log(
      `切换配置 ${configId} 状态为:`,
      enabled ? 'enabled' : 'disabled',
    );
    // TODO: 实际的状态更新逻辑
  };

  // 获取状态信息
  const getStatusInfo = (status: ConfigStatus) => {
    return configStatuses.find((s) => s.key === status) || configStatuses[0];
  };

  return (
    <PageContainer>
      <HeaderSection>
        <Title level={2}>配置管理中心</Title>
        <Paragraph type="secondary">
          管理系统的各项配置，支持可视化编辑和状态控制
        </Paragraph>
      </HeaderSection>

      <FilterSection>
        <Row
          gutter={16}
          align="middle"
        >
          <Col
            xs={24}
            sm={8}
            md={6}
          >
            <Search
              placeholder="搜索配置名称、描述或标签"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col
            xs={12}
            sm={6}
            md={4}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="选择分类"
              value={selectedCategory}
              onChange={setSelectedCategory}
            >
              <Select.Option value="all">全部分类</Select.Option>
              {configCategories.map((category) => (
                <Select.Option
                  key={category.key}
                  value={category.key}
                >
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col
            xs={12}
            sm={6}
            md={4}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="选择状态"
              value={selectedStatus}
              onChange={setSelectedStatus}
            >
              <Select.Option value="all">全部状态</Select.Option>
              {configStatuses.map((status) => (
                <Select.Option
                  key={status.key}
                  value={status.key}
                >
                  {status.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col
            xs={24}
            sm={4}
            md={4}
          >
            <Text type="secondary">共 {filteredConfigs.length} 个配置</Text>
          </Col>
        </Row>
      </FilterSection>

      <Row gutter={[16, 16]}>
        {filteredConfigs.map((config) => {
          const statusInfo = getStatusInfo(config.status);
          const categoryInfo = configCategories.find(
            (cat) => cat.key === config.category,
          );

          return (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={config.id}
            >
              <ConfigCard $category={config.category}>
                <ConfigHeader>
                  <ConfigTitle>
                    {getIconComponent(config.icon || 'SettingOutlined')}
                    <div>
                      <Title
                        level={5}
                        style={{ margin: 0 }}
                      >
                        {config.name}
                      </Title>
                      <Text
                        type="secondary"
                        style={{ fontSize: 12 }}
                      >
                        v{config.version}
                      </Text>
                    </div>
                  </ConfigTitle>
                  <ActionButtons>
                    <Switch
                      size="small"
                      checked={config.status === 'enabled'}
                      onChange={(checked) =>
                        handleStatusToggle(config.id, checked)
                      }
                    />
                    <Button
                      type="primary"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditConfig(config.id)}
                    >
                      编辑
                    </Button>
                  </ActionButtons>
                </ConfigHeader>

                <Paragraph
                  style={{
                    margin: '8px 0 16px 0',
                    color: '#666',
                    fontSize: 13,
                    lineHeight: 1.5,
                  }}
                >
                  {config.description}
                </Paragraph>

                <Space
                  wrap
                  style={{ marginBottom: 16 }}
                >
                  <Badge
                    status={
                      statusInfo.color as
                        | 'success'
                        | 'processing'
                        | 'default'
                        | 'error'
                        | 'warning'
                    }
                    text={statusInfo.name}
                  />
                  <Tag color={categoryInfo?.color}>{categoryInfo?.name}</Tag>
                  {config.tags?.map((tag) => (
                    <Tag
                      key={tag}
                      style={{ fontSize: 11 }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </Space>

                <ConfigMeta>
                  <Descriptions
                    size="small"
                    column={1}
                  >
                    <Descriptions.Item label="最后更新">
                      {new Date(config.lastModified).toLocaleDateString(
                        'zh-CN',
                      )}
                    </Descriptions.Item>
                  </Descriptions>
                </ConfigMeta>
              </ConfigCard>
            </Col>
          );
        })}
      </Row>

      {filteredConfigs.length === 0 && (
        <Card style={{ textAlign: 'center', marginTop: 32 }}>
          <Text type="secondary">没有找到匹配的配置项</Text>
        </Card>
      )}
    </PageContainer>
  );
};
