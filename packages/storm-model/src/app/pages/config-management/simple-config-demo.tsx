/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Button, Result } from 'antd';
import React from 'react';
import { useParams } from 'react-router';
import styled from 'styled-components';
import { useNavigation } from '@/app/hooks/use-navigation';
import { SimpleConfigEditor } from '@/components/config-visualizer/simple-config-editor';
import { getConfigById } from '@/data/config-data';

const ConfigPageContainer = styled.div`
  height: calc(
    100vh - 64px - 48px - 48px - 32px
  ); /* header(64) + content margin(24*2) + content padding(24*2) + breadcrumb(约32) */
  overflow: auto;
  margin: 0;
  padding: 0;
`;

export const SimpleConfigDemo: React.FC = () => {
  const { configId } = useParams<{ configId: string }>();
  const { navigate } = useNavigation();

  // 如果没有配置ID，跳转到配置列表
  if (!configId) {
    return (
      <Result
        status="404"
        title="配置ID未指定"
        subTitle="请选择要编辑的配置项"
        extra={
          <Button
            type="primary"
            onClick={() => navigate('/management/config')}
          >
            返回配置列表
          </Button>
        }
      />
    );
  }

  // 获取配置项
  const configItem = getConfigById(configId);

  if (!configItem) {
    return (
      <Result
        status="404"
        title="配置不存在"
        subTitle={`找不到ID为 "${configId}" 的配置项`}
        extra={
          <Button
            type="primary"
            onClick={() => navigate('/management/config')}
          >
            返回配置列表
          </Button>
        }
      />
    );
  }

  const handleConfigChange = (data: unknown) => {
    console.log(`配置 ${configId} 变化:`, data);
    // TODO: 实际的配置保存逻辑
  };

  const handleSave = (data: unknown) => {
    console.log(`保存配置 ${configId}:`, data);
    // TODO: 实际的保存逻辑，如发送 API 请求
    // 可以显示保存成功的提示
  };

  const handleReset = () => {
    console.log(`重置配置 ${configId}`);
    // TODO: 实际的重置逻辑
    // 可以显示重置成功的提示
  };

  return (
    <ConfigPageContainer>
      <SimpleConfigEditor
        componentConfig={{
          schema: configItem.schema,
          uiSchema: configItem.uiSchema,
          defaultData: configItem.defaultData,
        }}
        initialData={configItem.currentData}
        onChange={handleConfigChange}
        onSave={handleSave}
        onReset={handleReset}
        title={`${configItem.name} - 配置编辑器`}
      />
    </ConfigPageContainer>
  );
};
