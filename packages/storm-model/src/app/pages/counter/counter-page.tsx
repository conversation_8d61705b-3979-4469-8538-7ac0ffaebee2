/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CalculatorOutlined } from '@ant-design/icons';
import { Card, Divider, Typography } from 'antd';
import { Counter } from '@/app/components/counter';

const { Title, Paragraph } = Typography;

export const CounterPage: React.FC = () => {
  return (
    <div>
      <Typography>
        <Title level={2}>
          <CalculatorOutlined style={{ marginRight: 8 }} />
          Redux 计数器演示
        </Title>
        <Paragraph>
          这个页面展示了如何在 React 组件中使用 Redux Toolkit 进行状态管理。
          计数器的状态完全由 Redux store 管理，演示了同步和异步操作。
        </Paragraph>
      </Typography>

      <Divider />

      <Card
        title="计数器功能"
        style={{ maxWidth: 600 }}
      >
        <Counter />
      </Card>

      <Divider />

      <Card title="技术要点">
        <ul>
          <li>使用 Redux Toolkit 的 createSlice 创建 reducer</li>
          <li>使用 useAppSelector 和 useAppDispatch hooks</li>
          <li>支持同步和异步状态更新</li>
          <li>完整的 TypeScript 类型支持</li>
          <li>遵循 Redux 最佳实践</li>
        </ul>
      </Card>
    </div>
  );
};
