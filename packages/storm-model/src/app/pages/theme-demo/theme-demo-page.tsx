/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BgColorsOutlined, MoonOutlined, SunOutlined } from '@ant-design/icons';
import {
  Alert,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Row,
  Space,
  Tag,
  Typography,
  theme,
} from 'antd';
import React from 'react';
import { useAppSelector } from '@/store/hooks';

const { Title, Paragraph, Text } = Typography;

export const ThemeDemoPage: React.FC = () => {
  const { mode, themes } = useAppSelector((state) => state.theme);
  const { token } = theme.useToken();

  // 获取当前主题模式的配置
  const currentThemeConfig = themes[mode];
  const otherMode = mode === 'light' ? 'dark' : 'light';
  const otherThemeConfig = themes[otherMode];

  return (
    <div>
      <Typography>
        <Title level={1}>
          <BgColorsOutlined style={{ marginRight: 8 }} />
          主题系统演示
        </Title>
        <Paragraph>
          展示 Storm Model 的主题系统功能，包括亮色暗色模式切换和独立配置。
        </Paragraph>
      </Typography>

      <Alert
        message="主题配置说明"
        description="点击右上角的主题设置按钮可以进行主题配置。亮色和暗色模式支持独立的主色调和圆角设置。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={[16, 16]}>
        {/* 当前主题状态 */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title={
              <Space>
                {mode === 'light' ? <SunOutlined /> : <MoonOutlined />}
                当前主题状态
              </Space>
            }
            extra={
              <Tag color={mode === 'dark' ? 'purple' : 'orange'}>
                {mode === 'dark' ? '暗色模式' : '亮色模式'}
              </Tag>
            }
          >
            <Descriptions
              column={1}
              size="small"
            >
              <Descriptions.Item label="模式">
                {mode === 'dark' ? '暗色模式' : '亮色模式'}
              </Descriptions.Item>
              <Descriptions.Item label="主色调">
                <Space>
                  <Tag
                    color={currentThemeConfig.primaryColor}
                    style={{
                      backgroundColor: currentThemeConfig.primaryColor,
                      border: 'none',
                      color: 'white',
                    }}
                  >
                    {currentThemeConfig.primaryColor}
                  </Tag>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="圆角大小">
                {currentThemeConfig.borderRadius}px
              </Descriptions.Item>
              <Descriptions.Item label="背景色">
                <Tag
                  style={{
                    backgroundColor: token.colorBgContainer,
                    border: `1px solid ${token.colorBorder}`,
                  }}
                >
                  {token.colorBgContainer}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="文字颜色">
                <Tag
                  style={{
                    backgroundColor: token.colorTextBase,
                    color: 'white',
                    border: 'none',
                  }}
                >
                  {token.colorTextBase}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 另一模式预览 */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title={
              <Space>
                {otherMode === 'light' ? <SunOutlined /> : <MoonOutlined />}
                {otherMode === 'dark' ? '暗色模式' : '亮色模式'}预览
              </Space>
            }
            extra={
              <Tag color={otherMode === 'dark' ? 'purple' : 'orange'}>
                {otherMode === 'dark' ? '暗色' : '亮色'}
              </Tag>
            }
          >
            <Descriptions
              column={1}
              size="small"
            >
              <Descriptions.Item label="模式">
                {otherMode === 'dark' ? '暗色模式' : '亮色模式'}
              </Descriptions.Item>
              <Descriptions.Item label="主色调">
                <Tag
                  color={otherThemeConfig.primaryColor}
                  style={{
                    backgroundColor: otherThemeConfig.primaryColor,
                    border: 'none',
                    color: 'white',
                  }}
                >
                  {otherThemeConfig.primaryColor}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="圆角大小">
                {otherThemeConfig.borderRadius}px
              </Descriptions.Item>
            </Descriptions>
            <Divider />
            <Text type="secondary">
              切换到 {otherMode === 'dark' ? '暗色' : '亮色'} 模式查看完整效果
            </Text>
          </Card>
        </Col>
      </Row>

      <Divider />

      {/* 组件样式演示 */}
      <Card title="组件样式演示">
        <Row gutter={[16, 16]}>
          <Col
            xs={24}
            sm={8}
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <Title level={4}>按钮组件</Title>
              <Space wrap>
                <Button type="primary">主要按钮</Button>
                <Button>默认按钮</Button>
                <Button type="dashed">虚线按钮</Button>
                <Button type="text">文字按钮</Button>
                <Button danger>危险按钮</Button>
              </Space>
            </Space>
          </Col>

          <Col
            xs={24}
            sm={8}
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <Title level={4}>标签组件</Title>
              <Space wrap>
                <Tag color="blue">蓝色标签</Tag>
                <Tag color="green">绿色标签</Tag>
                <Tag color="red">红色标签</Tag>
                <Tag color="orange">橙色标签</Tag>
                <Tag color={currentThemeConfig.primaryColor}>主题色标签</Tag>
              </Space>
            </Space>
          </Col>

          <Col
            xs={24}
            sm={8}
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <Title level={4}>卡片组件</Title>
              <Card
                size="small"
                title="小卡片"
              >
                <p>这是一个小尺寸的卡片组件</p>
              </Card>
            </Space>
          </Col>
        </Row>
      </Card>

      <Divider />

      <Alert
        message="主题系统特性"
        description={
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>支持亮色和暗色两种主题模式</li>
            <li>每种模式支持独立的主色调配置</li>
            <li>每种模式支持独立的圆角大小配置</li>
            <li>主题设置自动保存到本地存储</li>
            <li>页面刷新后自动恢复用户配置</li>
            <li>所有 Ant Design 组件自动适配主题</li>
          </ul>
        }
        type="success"
        showIcon
      />
    </div>
  );
};
