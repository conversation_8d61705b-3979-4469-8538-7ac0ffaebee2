/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { RJSFSchema, UiSchema } from '@rjsf/utils';
import {
  Card,
  Descriptions,
  InputNumber,
  List,
  Select,
  Space,
  Switch,
  Tag,
  Typography,
} from 'antd';
import React from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;

const PreviewContainer = styled.div`
  .ant-descriptions-item-label {
    font-weight: 500;
    color: #262626;
    min-width: 120px;
  }

  .ant-card {
    margin-bottom: 16px;
    border-radius: 6px;
  }

  .field-group {
    background: #fafafa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
  }

  .field-group-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 12px;
  }
`;

export interface ConfigFormPreviewProps<T = unknown> {
  /** 配置数据 */
  data: T;
  /** JSON Schema 定义 */
  schema?: RJSFSchema;
  /** UI Schema 定义 */
  uiSchema?: UiSchema;
  /** 预览标题 */
  title?: string;
}

export const ConfigFormPreview = <T = unknown>({
  data,
  schema,
  uiSchema = {},
  title = '配置预览',
}: ConfigFormPreviewProps<T>) => {
  // 获取字段标题（支持中英文）
  const getFieldTitle = (key: string, fieldSchema?: RJSFSchema): string => {
    // 优先使用 schema 中的 title
    if (fieldSchema?.title) {
      return fieldSchema.title;
    }

    // 然后尝试从 uiSchema 中获取
    const uiField = uiSchema[key];
    if (uiField && typeof uiField === 'object' && 'ui:title' in uiField) {
      return (uiField as Record<string, unknown>)['ui:title'] as string;
    }

    // 最后使用字段名
    return key;
  };

  // 获取字段描述
  const getFieldDescription = (
    key: string,
    fieldSchema?: RJSFSchema,
  ): string | undefined => {
    if (fieldSchema?.description) {
      return fieldSchema.description;
    }

    const uiField = uiSchema[key];
    if (uiField && typeof uiField === 'object' && 'ui:description' in uiField) {
      return (uiField as Record<string, unknown>)['ui:description'] as string;
    }

    return undefined;
  };

  // 根据schema类型渲染字段值
  const renderFieldValue = (
    value: unknown,
    key: string,
    fieldSchema?: RJSFSchema,
  ): React.ReactNode => {
    if (value === null || value === undefined) {
      return <Text type="secondary">未设置</Text>;
    }

    const fieldType = fieldSchema?.type;
    const fieldEnum = fieldSchema?.enum;

    // 布尔类型
    if (fieldType === 'boolean' || typeof value === 'boolean') {
      return (
        <Switch
          checked={!!value}
          disabled
          size="small"
        />
      );
    }

    // 数字类型
    if (
      fieldType === 'number' ||
      fieldType === 'integer' ||
      typeof value === 'number'
    ) {
      return (
        <InputNumber
          value={value as number}
          disabled
          size="small"
          style={{ width: 'auto' }}
        />
      );
    }

    // 枚举类型（下拉选择）
    if (fieldEnum && Array.isArray(fieldEnum)) {
      const enumValue = fieldEnum.find((item) => item === value);
      return (
        <Select
          value={enumValue}
          disabled
          size="small"
          style={{ minWidth: 120 }}
          options={fieldEnum.map((item) => ({
            label: String(item),
            value: item,
          }))}
        />
      );
    }

    // 字符串类型
    if (fieldType === 'string' || typeof value === 'string') {
      const stringValue = value as string;

      // 颜色值
      if (stringValue.startsWith('#') && stringValue.length === 7) {
        return (
          <Space>
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: stringValue,
                border: '1px solid #d9d9d9',
                borderRadius: 2,
              }}
            />
            <Text code>{stringValue}</Text>
          </Space>
        );
      }

      // 状态类型
      if (
        key.toLowerCase().includes('status') ||
        key.toLowerCase().includes('state')
      ) {
        const statusColors: Record<string, string> = {
          enabled: 'success',
          disabled: 'default',
          draft: 'warning',
          active: 'processing',
          inactive: 'default',
          success: 'success',
          error: 'error',
          warning: 'warning',
          on: 'success',
          off: 'default',
        };
        return (
          <Tag color={statusColors[stringValue.toLowerCase()] || 'default'}>
            {stringValue}
          </Tag>
        );
      }

      // 时间类型
      if (
        key.toLowerCase().includes('time') ||
        key.toLowerCase().includes('date')
      ) {
        try {
          const date = new Date(stringValue);
          if (!Number.isNaN(date.getTime())) {
            return (
              <Space
                direction="vertical"
                size={0}
              >
                <Text>{date.toLocaleDateString('zh-CN')}</Text>
                <Text
                  type="secondary"
                  style={{ fontSize: 12 }}
                >
                  {date.toLocaleTimeString('zh-CN')}
                </Text>
              </Space>
            );
          }
        } catch {
          // 忽略解析错误
        }
      }

      return <Text>{stringValue}</Text>;
    }

    // 数组类型
    if (fieldType === 'array' || Array.isArray(value)) {
      const arrayValue = value as unknown[];

      if (arrayValue.length === 0) {
        return <Text type="secondary">空列表</Text>;
      }

      // 简单值数组用标签显示
      if (
        arrayValue.every((item) =>
          ['string', 'number', 'boolean'].includes(typeof item),
        )
      ) {
        return (
          <Space wrap>
            {arrayValue.map((item, index) => (
              <Tag key={index}>{String(item)}</Tag>
            ))}
          </Space>
        );
      }

      // 对象数组用列表显示
      return (
        <List
          size="small"
          dataSource={arrayValue}
          renderItem={(item, index) => (
            <List.Item style={{ padding: '8px 0' }}>
              <div style={{ width: '100%' }}>
                <Text
                  strong
                  style={{ marginBottom: 4, display: 'block' }}
                >
                  项目 {index + 1}
                </Text>
                {renderFieldValue(
                  item,
                  `${key}[${index}]`,
                  fieldSchema?.items as RJSFSchema,
                )}
              </div>
            </List.Item>
          )}
        />
      );
    }

    // 对象类型
    if (
      fieldType === 'object' ||
      (typeof value === 'object' && value !== null)
    ) {
      return renderObject(value as Record<string, unknown>, fieldSchema);
    }

    return <Text>{String(value)}</Text>;
  };

  // 渲染对象
  const renderObject = (
    obj: Record<string, unknown>,
    objectSchema?: RJSFSchema,
  ): React.ReactNode => {
    const entries = Object.entries(obj);

    if (entries.length === 0) {
      return <Text type="secondary">无数据</Text>;
    }

    const properties = objectSchema?.properties || {};

    return (
      <Descriptions
        column={1}
        size="small"
        bordered
      >
        {entries.map(([key, value]) => {
          const fieldSchema = properties[key] as RJSFSchema;
          const fieldTitle = getFieldTitle(key, fieldSchema);
          const fieldDescription = getFieldDescription(key, fieldSchema);

          return (
            <Descriptions.Item
              key={key}
              label={
                <div>
                  <div>{fieldTitle}</div>
                  {fieldDescription && (
                    <Text
                      type="secondary"
                      style={{ fontSize: 12, fontWeight: 'normal' }}
                    >
                      {fieldDescription}
                    </Text>
                  )}
                </div>
              }
            >
              {renderFieldValue(value, key, fieldSchema)}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    );
  };

  // 渲染顶层数据
  const renderTopLevel = (): React.ReactNode => {
    if (!data || typeof data !== 'object') {
      return <Text type="secondary">无效的配置数据</Text>;
    }

    const dataObj = data as Record<string, unknown>;
    const properties = schema?.properties || {};

    // 如果有schema定义，按照schema的结构来组织显示
    if (schema && Object.keys(properties).length > 0) {
      const entries = Object.entries(dataObj);
      const groupedFields: Record<string, Array<[string, unknown]>> = {};
      const ungroupedFields: Array<[string, unknown]> = [];

      // 根据schema分组字段
      entries.forEach(([key, value]) => {
        const fieldSchema = properties[key] as RJSFSchema;

        // 检查是否是对象类型（作为组）
        if (fieldSchema?.type === 'object') {
          groupedFields[key] = [[key, value]];
        } else {
          ungroupedFields.push([key, value]);
        }
      });

      return (
        <div>
          {ungroupedFields.length > 0 && (
            <Card
              size="small"
              title="基本配置"
              style={{ marginBottom: 16 }}
            >
              <Descriptions
                column={1}
                size="small"
                bordered
              >
                {ungroupedFields.map(([key, value]) => {
                  const fieldSchema = properties[key] as RJSFSchema;
                  const fieldTitle = getFieldTitle(key, fieldSchema);
                  const fieldDescription = getFieldDescription(
                    key,
                    fieldSchema,
                  );

                  return (
                    <Descriptions.Item
                      key={key}
                      label={
                        <div>
                          <div>{fieldTitle}</div>
                          {fieldDescription && (
                            <Text
                              type="secondary"
                              style={{ fontSize: 12, fontWeight: 'normal' }}
                            >
                              {fieldDescription}
                            </Text>
                          )}
                        </div>
                      }
                    >
                      {renderFieldValue(value, key, fieldSchema)}
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </Card>
          )}

          {Object.entries(groupedFields).map(([groupKey, fields]) => {
            const fieldSchema = properties[groupKey] as RJSFSchema;
            const groupTitle = getFieldTitle(groupKey, fieldSchema);

            return (
              <Card
                key={groupKey}
                size="small"
                title={groupTitle}
                style={{ marginBottom: 16 }}
              >
                {fields.map(([key, value]) => (
                  <div key={key}>
                    {renderFieldValue(value, key, fieldSchema)}
                  </div>
                ))}
              </Card>
            );
          })}
        </div>
      );
    }

    // 没有schema时，直接渲染对象
    return renderObject(dataObj);
  };

  return (
    <PreviewContainer>
      <Title
        level={5}
        style={{ marginBottom: 16 }}
      >
        {title}
      </Title>
      {renderTopLevel()}
    </PreviewContainer>
  );
};
