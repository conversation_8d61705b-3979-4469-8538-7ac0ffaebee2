/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Alert, Badge, Card, Descriptions, List, Tag, Typography } from 'antd';
import React from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;

// 调度事件配置类型
interface DispatchEventType {
  type: string;
  title: string;
  children: {
    type: string;
    title: string;
  }[];
}

interface LevelType {
  type: number;
  title: string;
  isDefault?: boolean;
  color?: string;
}

interface RelatedTypeConfig {
  isShow?: boolean;
}

export interface DispatchEventConfig {
  enabled: boolean;
  basicConfig: {
    defaultEventStatusType: 'DONE' | 'DOING' | 'PLANNING';
    openDefault: boolean;
    allowUploadImage: boolean;
  };
  eventTypeConfig: {
    dispatchEventType: DispatchEventType[];
  };
  levelConfig: {
    dispatchLevelType: LevelType[];
  };
  relatedConfig: {
    supportedRelatedTypes: {
      valve?: RelatedTypeConfig;
      pipe?: RelatedTypeConfig;
      device?: RelatedTypeConfig;
    };
  };
}

const PreviewContainer = styled.div`
  .ant-card {
    margin-bottom: 16px;
    border-radius: 6px;
  }

  .ant-descriptions-item-label {
    font-weight: 500;
    color: #262626;
  }

  .config-disabled {
    opacity: 0.6;
    filter: grayscale(50%);
  }
`;

const StatusBadge = styled(Badge)`
  .ant-badge-status-dot {
    width: 8px;
    height: 8px;
  }
`;

const ConfigHeader = styled.div<{ $enabled: boolean }>`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: ${(props) => (props.$enabled ? '#f6ffed' : '#fff2f0')};
  border: 1px solid ${(props) => (props.$enabled ? '#b7eb8f' : '#ffccc7')};
  border-radius: 6px;
`;

const StatusIndicator = styled.div<{ $enabled: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${(props) => (props.$enabled ? '#52c41a' : '#ff4d4f')};
`;

interface DispatchEventPreviewProps {
  config: DispatchEventConfig;
  title?: string;
}

export const DispatchEventPreview: React.FC<DispatchEventPreviewProps> = ({
  config,
  title = '调度事件配置预览',
}) => {
  const getStatusText = (status: string) => {
    switch (status) {
      case 'DONE':
        return '已完成';
      case 'DOING':
        return '进行中';
      case 'PLANNING':
        return '计划中';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DONE':
        return 'success';
      case 'DOING':
        return 'processing';
      case 'PLANNING':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getRelatedTypeText = (key: string) => {
    switch (key) {
      case 'valve':
        return '阀门关联';
      case 'pipe':
        return '管道关联';
      case 'device':
        return '设备关联';
      default:
        return key;
    }
  };

  const containerClass = config.enabled ? '' : 'config-disabled';

  return (
    <PreviewContainer className={containerClass}>
      <Title
        level={5}
        style={{ marginBottom: 16 }}
      >
        {title}
      </Title>

      {/* 配置状态 */}
      <ConfigHeader $enabled={config.enabled}>
        <StatusIndicator $enabled={config.enabled} />
        <div>
          <Text
            strong
            style={{ fontSize: 14 }}
          >
            配置状态：{config.enabled ? '已启用' : '已禁用'}
          </Text>
          <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
            {config.enabled
              ? '调度事件配置功能已启用，将使用下方的自定义配置'
              : '调度事件配置功能已禁用，将使用系统默认配置'}
          </div>
        </div>
      </ConfigHeader>

      {!config.enabled && (
        <Alert
          message="配置已禁用"
          description="当前配置处于禁用状态，系统将使用默认配置。如需启用，请在左侧勾选启用调度事件配置选项。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 基础配置 */}
      <Card
        title="基础配置"
        size="small"
      >
        <Descriptions
          column={1}
          size="small"
        >
          <Descriptions.Item label="默认事件状态">
            <StatusBadge
              status={getStatusColor(config.basicConfig.defaultEventStatusType)}
              text={getStatusText(config.basicConfig.defaultEventStatusType)}
            />
          </Descriptions.Item>
          <Descriptions.Item label="启用默认预填值">
            <Tag color={config.basicConfig.openDefault ? 'green' : 'red'}>
              {config.basicConfig.openDefault ? '已启用' : '未启用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="允许上传附件">
            <Tag color={config.basicConfig.allowUploadImage ? 'green' : 'red'}>
              {config.basicConfig.allowUploadImage ? '允许' : '不允许'}
            </Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 事件类型配置 */}
      <Card
        title="事件类型配置"
        size="small"
      >
        <List
          size="small"
          dataSource={config.eventTypeConfig.dispatchEventType}
          renderItem={(item) => (
            <List.Item>
              <div style={{ width: '100%' }}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>{item.title}</Text>
                  <Text
                    type="secondary"
                    style={{ marginLeft: 8 }}
                  >
                    ({item.type})
                  </Text>
                </div>
                <div>
                  {item.children.map((child) => (
                    <Tag
                      key={child.type}
                      style={{ marginBottom: 4 }}
                    >
                      {child.title}
                    </Tag>
                  ))}
                </div>
              </div>
            </List.Item>
          )}
        />
      </Card>

      {/* 事件等级配置 */}
      <Card
        title="事件等级配置"
        size="small"
      >
        <List
          size="small"
          dataSource={config.levelConfig.dispatchLevelType}
          renderItem={(item) => (
            <List.Item>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Badge
                  color={item.color || '#d9d9d9'}
                  text={`等级 ${item.type}`}
                />
                <Text strong>{item.title}</Text>
                {item.isDefault && <Tag color="blue">默认</Tag>}
              </div>
            </List.Item>
          )}
        />
      </Card>

      {/* 关联类型配置 */}
      <Card
        title="关联类型配置"
        size="small"
      >
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          {Object.entries(config.relatedConfig.supportedRelatedTypes).map(
            ([key, value]) => (
              <Tag
                key={key}
                color={value?.isShow ? 'green' : 'red'}
              >
                {getRelatedTypeText(key)}: {value?.isShow ? '显示' : '隐藏'}
              </Tag>
            ),
          )}
        </div>
      </Card>

      {/* 配置摘要 */}
      <Card
        title="配置摘要"
        size="small"
      >
        <Descriptions
          column={2}
          size="small"
        >
          <Descriptions.Item label="配置状态">
            <Tag color={config.enabled ? 'green' : 'red'}>
              {config.enabled ? '启用' : '禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="事件类型数量">
            <Badge
              count={config.eventTypeConfig.dispatchEventType.length}
              showZero
            />
          </Descriptions.Item>
          <Descriptions.Item label="子事件类型总数">
            <Badge
              count={config.eventTypeConfig.dispatchEventType.reduce(
                (total, item) => total + item.children.length,
                0,
              )}
              showZero
            />
          </Descriptions.Item>
          <Descriptions.Item label="事件等级数量">
            <Badge
              count={config.levelConfig.dispatchLevelType.length}
              showZero
            />
          </Descriptions.Item>
          <Descriptions.Item label="可关联类型数量">
            <Badge
              count={
                Object.values(
                  config.relatedConfig.supportedRelatedTypes,
                ).filter((item) => item?.isShow).length
              }
              showZero
            />
          </Descriptions.Item>
          <Descriptions.Item label="默认等级">
            {config.levelConfig.dispatchLevelType.find((item) => item.isDefault)
              ?.title || '未设置'}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </PreviewContainer>
  );
};
