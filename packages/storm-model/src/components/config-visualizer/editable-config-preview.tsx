/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import type { RJSFSchema, UiSchema } from '@rjsf/utils';
import {
  Button,
  Card,
  ColorPicker,
  DatePicker,
  Descriptions,
  Input,
  InputNumber,
  Select,
  Space,
  Switch,
  TimePicker,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useCallback } from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;
const { TextArea } = Input;

const EditableContainer = styled.div`
  .ant-descriptions-item-label {
    min-width: 120px;
    vertical-align: top;
    padding-top: 8px;
  }

  .ant-descriptions-item-content {
    padding-top: 4px;
  }

  .array-item {
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .array-item-content {
    flex: 1;
    margin-right: 8px;
  }
`;

export interface EditableConfigPreviewProps<T = unknown> {
  /** 配置数据 */
  data: T;
  /** JSON Schema 定义 */
  schema?: RJSFSchema;
  /** UI Schema 定义 */
  uiSchema?: UiSchema;
  /** 预览标题 */
  title?: string;
  /** 数据变化回调 */
  onChange?: (data: T) => void;
}

export const EditableConfigPreview = <T = unknown>({
  data,
  schema,
  uiSchema = {},
  title = '配置编辑',
  onChange,
}: EditableConfigPreviewProps<T>) => {
  // 获取字段标题（支持中英文）
  const getFieldTitle = (
    key: string,
    fieldSchema?: RJSFSchema,
  ): React.ReactNode => {
    let chineseTitle = '';

    // 首先尝试从 schema 获取中文标题
    if (fieldSchema?.title) {
      chineseTitle = fieldSchema.title;
    } else {
      // 然后尝试从 uiSchema 获取中文标题
      const uiField = uiSchema[key];
      if (uiField && typeof uiField === 'object' && 'ui:title' in uiField) {
        chineseTitle = (uiField as Record<string, unknown>)[
          'ui:title'
        ] as string;
      }
    }

    // 如果没有中文标题，使用字段名作为标题
    if (!chineseTitle) {
      chineseTitle = key;
    }

    // 返回中英文对照的标题
    return (
      <div>
        <div>{chineseTitle}</div>
        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{key}</div>
      </div>
    );
  };

  // 获取字段描述
  const getFieldDescription = (
    key: string,
    fieldSchema?: RJSFSchema,
  ): string | undefined => {
    if (fieldSchema?.description) {
      return fieldSchema.description;
    }

    const uiField = uiSchema[key];
    if (uiField && typeof uiField === 'object' && 'ui:description' in uiField) {
      return (uiField as Record<string, unknown>)['ui:description'] as string;
    }

    return undefined;
  };

  // 更新字段值
  const updateFieldValue = useCallback(
    (path: string[], value: unknown) => {
      const newData = JSON.parse(JSON.stringify(data));

      // 根据路径更新嵌套对象
      let current = newData;
      for (let i = 0; i < path.length - 1; i++) {
        if (current[path[i]] === undefined) {
          current[path[i]] = {};
        }
        current = current[path[i]];
      }
      current[path[path.length - 1]] = value;

      onChange?.(newData as T);
    },
    [data, onChange],
  );

  // 渲染可编辑字段
  const renderEditableField = (
    value: unknown,
    key: string,
    fieldSchema?: RJSFSchema,
    path: string[] = [key],
  ): React.ReactNode => {
    const fieldType = fieldSchema?.type;
    const fieldEnum = fieldSchema?.enum;
    const uiWidget =
      uiSchema[key] && typeof uiSchema[key] === 'object'
        ? ((uiSchema[key] as Record<string, unknown>)['ui:widget'] as string)
        : undefined;

    // 布尔类型
    if (fieldType === 'boolean' || typeof value === 'boolean') {
      return (
        <Switch
          checked={!!value}
          onChange={(checked) => updateFieldValue(path, checked)}
          size="small"
        />
      );
    }

    // 数字类型
    if (
      fieldType === 'number' ||
      fieldType === 'integer' ||
      typeof value === 'number'
    ) {
      return (
        <InputNumber
          value={value as number}
          onChange={(val) => updateFieldValue(path, val)}
          min={fieldSchema?.minimum}
          max={fieldSchema?.maximum}
          step={fieldType === 'integer' ? 1 : 0.1}
          size="small"
          style={{ width: '100%', maxWidth: 200 }}
        />
      );
    }

    // 枚举类型（下拉选择）
    if (fieldEnum && Array.isArray(fieldEnum)) {
      return (
        <Select
          value={value}
          onChange={(val) => updateFieldValue(path, val)}
          size="small"
          style={{ width: '100%', maxWidth: 200 }}
          options={fieldEnum.map((item) => ({
            label: String(item),
            value: item,
          }))}
        />
      );
    }

    // 颜色选择器
    if (
      uiWidget === 'color' ||
      (typeof value === 'string' &&
        (value as string).startsWith('#') &&
        (value as string).length === 7)
    ) {
      return (
        <Space>
          <ColorPicker
            value={value as string}
            onChange={(color) => updateFieldValue(path, color.toHexString())}
            size="small"
          />
          <Input
            value={value as string}
            onChange={(e) => updateFieldValue(path, e.target.value)}
            size="small"
            style={{ width: 100 }}
            placeholder="#000000"
          />
        </Space>
      );
    }

    // 日期时间类型
    if (key.toLowerCase().includes('date')) {
      return (
        <DatePicker
          value={value ? dayjs(value as string) : undefined}
          onChange={(date) => updateFieldValue(path, date?.toISOString())}
          size="small"
          style={{ width: '100%', maxWidth: 200 }}
        />
      );
    }

    if (
      key.toLowerCase().includes('time') &&
      !key.toLowerCase().includes('date')
    ) {
      return (
        <TimePicker
          value={value ? dayjs(value as string, 'HH:mm:ss') : undefined}
          onChange={(time) => updateFieldValue(path, time?.format('HH:mm:ss'))}
          size="small"
          style={{ width: '100%', maxWidth: 200 }}
        />
      );
    }

    // 字符串类型
    if (fieldType === 'string' || typeof value === 'string') {
      // 多行文本
      if (uiWidget === 'textarea' || (value as string).includes('\n')) {
        return (
          <TextArea
            value={value as string}
            onChange={(e) => updateFieldValue(path, e.target.value)}
            size="small"
            rows={3}
            style={{ width: '100%' }}
          />
        );
      }

      return (
        <Input
          value={value as string}
          onChange={(e) => updateFieldValue(path, e.target.value)}
          size="small"
          style={{ width: '100%', maxWidth: 300 }}
        />
      );
    }

    // 数组类型
    if (fieldType === 'array' || Array.isArray(value)) {
      return renderEditableArray(value as unknown[], key, fieldSchema, path);
    }

    // 对象类型
    if (
      fieldType === 'object' ||
      (typeof value === 'object' && value !== null)
    ) {
      return renderEditableObject(
        value as Record<string, unknown>,
        fieldSchema,
        path,
      );
    }

    return <Text type="secondary">不支持的类型</Text>;
  };

  // 渲染可编辑数组
  const renderEditableArray = (
    arr: unknown[],
    key: string,
    fieldSchema?: RJSFSchema,
    path: string[] = [key],
  ): React.ReactNode => {
    const itemSchema = fieldSchema?.items as RJSFSchema;

    const addItem = () => {
      const newItem =
        itemSchema?.type === 'string'
          ? ''
          : itemSchema?.type === 'number'
            ? 0
            : itemSchema?.type === 'boolean'
              ? false
              : {};
      const newArray = [...arr, newItem];
      updateFieldValue(path, newArray);
    };

    const removeItem = (index: number) => {
      const newArray = arr.filter((_, i) => i !== index);
      updateFieldValue(path, newArray);
    };

    return (
      <div>
        {arr.map((item, index) => (
          <div
            key={index}
            className="array-item"
          >
            <div className="array-item-content">
              {renderEditableField(item, `${key}[${index}]`, itemSchema, [
                ...path,
                index.toString(),
              ])}
            </div>
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => removeItem(index)}
            />
          </div>
        ))}
        <Button
          type="dashed"
          size="small"
          icon={<PlusOutlined />}
          onClick={addItem}
          style={{ width: '100%' }}
        >
          添加项目
        </Button>
      </div>
    );
  };

  // 渲染可编辑对象
  const renderEditableObject = (
    obj: Record<string, unknown>,
    objectSchema?: RJSFSchema,
    path: string[] = [],
  ): React.ReactNode => {
    const properties = objectSchema?.properties || {};
    const entries = Object.entries(obj);

    if (entries.length === 0) {
      return <Text type="secondary">无数据</Text>;
    }

    return (
      <Descriptions
        column={1}
        size="small"
        bordered
      >
        {entries.map(([key, value]) => {
          const fieldSchema = properties[key] as RJSFSchema;
          const fieldTitle = getFieldTitle(key, fieldSchema);
          const fieldDescription = getFieldDescription(key, fieldSchema);
          const fieldPath = path.length > 0 ? [...path, key] : [key];

          return (
            <Descriptions.Item
              key={key}
              label={
                <div style={{ color: '#000000' }}>
                  {fieldTitle}
                  {fieldDescription && (
                    <Text
                      type="secondary"
                      style={{ fontSize: 12 }}
                    >
                      {fieldDescription}
                    </Text>
                  )}
                </div>
              }
            >
              {renderEditableField(value, key, fieldSchema, fieldPath)}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    );
  };

  // 渲染顶层数据
  const renderTopLevel = (): React.ReactNode => {
    if (!data || typeof data !== 'object') {
      return <Text type="secondary">无效的配置数据</Text>;
    }

    const dataObj = data as Record<string, unknown>;
    const properties = schema?.properties || {};

    // 如果有schema定义，按照schema的结构来组织显示
    if (schema && Object.keys(properties).length > 0) {
      const entries = Object.entries(dataObj);
      const groupedFields: Record<string, Array<[string, unknown]>> = {};
      const ungroupedFields: Array<[string, unknown]> = [];

      // 根据schema分组字段
      entries.forEach(([key, value]) => {
        const fieldSchema = properties[key] as RJSFSchema;

        if (fieldSchema?.type === 'object') {
          groupedFields[key] = [[key, value]];
        } else {
          ungroupedFields.push([key, value]);
        }
      });

      return (
        <div>
          {/* 显示非对象字段 */}
          {ungroupedFields.length > 0 && (
            <Card
              size="small"
              title="基本配置"
              style={{ marginBottom: 16 }}
            >
              <Descriptions
                column={1}
                size="small"
                bordered
              >
                {ungroupedFields.map(([key, value]) => {
                  const fieldSchema = properties[key] as RJSFSchema;
                  const fieldTitle = getFieldTitle(key, fieldSchema);
                  const fieldDescription = getFieldDescription(
                    key,
                    fieldSchema,
                  );

                  return (
                    <Descriptions.Item
                      key={key}
                      label={
                        <div style={{ color: '#000000' }}>
                          {fieldTitle}
                          {fieldDescription && (
                            <Text
                              type="secondary"
                              style={{ fontSize: 12 }}
                            >
                              {fieldDescription}
                            </Text>
                          )}
                        </div>
                      }
                    >
                      {renderEditableField(value, key, fieldSchema)}
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </Card>
          )}

          {/* 显示对象字段组 */}
          {Object.entries(groupedFields).map(([groupKey, fields]) => {
            const fieldSchema = properties[groupKey] as RJSFSchema;
            const groupTitle = getFieldTitle(groupKey, fieldSchema);

            return (
              <Card
                key={groupKey}
                size="small"
                title={groupTitle}
                style={{ marginBottom: 16 }}
              >
                {fields.map(([key, value]) => (
                  <div key={key}>
                    {renderEditableField(value, key, fieldSchema, [key])}
                  </div>
                ))}
              </Card>
            );
          })}
        </div>
      );
    }

    // 没有schema时，直接渲染对象
    return renderEditableObject(dataObj);
  };

  return (
    <EditableContainer>
      <Title
        level={5}
        style={{ marginBottom: 16 }}
      >
        {title}
      </Title>
      {renderTopLevel()}
    </EditableContainer>
  );
};
