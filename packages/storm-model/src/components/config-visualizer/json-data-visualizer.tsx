/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  Badge,
  Card,
  Descriptions,
  Divider,
  List,
  Space,
  Switch,
  Table,
  Tag,
  Typography,
} from 'antd';
import React from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;

const VisualizerContainer = styled.div`
  .ant-descriptions-item-label {
    font-weight: 500;
    color: #262626;
    min-width: 120px;
  }

  .ant-card {
    margin-bottom: 16px;
    border-radius: 6px;
  }

  .json-array-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #e9ecef;
  }
`;

export interface JsonDataVisualizerProps {
  data: unknown;
  title?: string;
}

export const JsonDataVisualizer: React.FC<JsonDataVisualizerProps> = ({
  data,
  title = '数据预览',
}) => {
  // 渲染基本值
  const renderValue = (value: unknown, key?: string): React.ReactNode => {
    if (value === null || value === undefined) {
      return <Text type="secondary">null</Text>;
    }

    if (typeof value === 'boolean') {
      return (
        <Switch
          checked={value}
          disabled
          size="small"
        />
      );
    }

    if (typeof value === 'number') {
      return (
        <Badge
          count={value}
          overflowCount={999999}
          color="blue"
        />
      );
    }

    if (typeof value === 'string') {
      // 特殊处理一些常见的字符串类型
      if (value.startsWith('#') && value.length === 7) {
        // 颜色值
        return (
          <Space>
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: value,
                border: '1px solid #d9d9d9',
                borderRadius: 2,
              }}
            />
            <Text code>{value}</Text>
          </Space>
        );
      }

      if (
        key &&
        (key.toLowerCase().includes('time') ||
          key.toLowerCase().includes('date'))
      ) {
        // 时间字符串
        try {
          const date = new Date(value);
          if (!Number.isNaN(date.getTime())) {
            return (
              <Space
                direction="vertical"
                size={0}
              >
                <Text>{date.toLocaleDateString('zh-CN')}</Text>
                <Text
                  type="secondary"
                  style={{ fontSize: 12 }}
                >
                  {date.toLocaleTimeString('zh-CN')}
                </Text>
              </Space>
            );
          }
        } catch {
          // 忽略解析错误
        }
      }

      if (key?.toLowerCase().includes('status')) {
        // 状态字符串
        const statusColors: Record<string, string> = {
          enabled: 'success',
          disabled: 'default',
          draft: 'warning',
          active: 'processing',
          inactive: 'default',
          success: 'success',
          error: 'error',
          warning: 'warning',
        };
        return (
          <Tag color={statusColors[value.toLowerCase()] || 'default'}>
            {value}
          </Tag>
        );
      }

      return <Text>{value}</Text>;
    }

    if (Array.isArray(value)) {
      return renderArray(value);
    }

    if (typeof value === 'object') {
      return renderObject(value as Record<string, unknown>);
    }

    return <Text>{String(value)}</Text>;
  };

  // 渲染数组
  const renderArray = (arr: unknown[]): React.ReactNode => {
    if (arr.length === 0) {
      return <Text type="secondary">空数组</Text>;
    }

    // 如果是简单值数组，用 Tags 显示
    if (
      arr.every((item) => typeof item === 'string' || typeof item === 'number')
    ) {
      return (
        <Space wrap>
          {arr.map((item, index) => (
            <Tag key={index}>{String(item)}</Tag>
          ))}
        </Space>
      );
    }

    // 如果是对象数组，尝试用 Table 显示
    if (arr.every((item) => typeof item === 'object' && item !== null)) {
      const objArray = arr as Record<string, unknown>[];

      // 获取所有可能的列
      const allKeys = new Set<string>();
      objArray.forEach((obj) => {
        Object.keys(obj).forEach((k) => allKeys.add(k));
      });

      if (allKeys.size <= 5 && objArray.length <= 10) {
        // 适合用表格显示
        const columns = Array.from(allKeys).map((k) => ({
          title: k,
          dataIndex: k,
          key: k,
          render: (value: unknown) => renderValue(value, k),
        }));

        return (
          <Table
            dataSource={objArray.map((item, index) => ({
              ...item,
              key: index,
            }))}
            columns={columns}
            pagination={false}
            size="small"
            scroll={{ x: 'max-content' }}
          />
        );
      }
    }

    // 否则用 List 显示
    return (
      <List
        size="small"
        dataSource={arr}
        renderItem={(item, index) => (
          <List.Item style={{ padding: '8px 0' }}>
            <div
              className="json-array-item"
              style={{ width: '100%' }}
            >
              <Text
                strong
                style={{ marginBottom: 8, display: 'block' }}
              >
                项目 {index + 1}
              </Text>
              {renderValue(item)}
            </div>
          </List.Item>
        )}
      />
    );
  };

  // 渲染对象
  const renderObject = (obj: Record<string, unknown>): React.ReactNode => {
    const entries = Object.entries(obj);

    if (entries.length === 0) {
      return <Text type="secondary">空对象</Text>;
    }

    // 如果属性不多，用 Descriptions 显示
    if (entries.length <= 8) {
      return (
        <Descriptions
          column={1}
          size="small"
          bordered
        >
          {entries.map(([key, value]) => (
            <Descriptions.Item
              key={key}
              label={key}
            >
              {renderValue(value, key)}
            </Descriptions.Item>
          ))}
        </Descriptions>
      );
    }

    // 属性较多时，分组显示
    return (
      <div>
        {entries.map(([key, value]) => (
          <div
            key={key}
            style={{ marginBottom: 16 }}
          >
            <Title
              level={5}
              style={{ margin: '0 0 8px 0' }}
            >
              {key}
            </Title>
            {renderValue(value, key)}
            <Divider style={{ margin: '12px 0' }} />
          </div>
        ))}
      </div>
    );
  };

  return (
    <VisualizerContainer>
      <Title
        level={5}
        style={{ marginBottom: 16 }}
      >
        {title}
      </Title>
      <Card size="small">{renderValue(data)}</Card>
    </VisualizerContainer>
  );
};
