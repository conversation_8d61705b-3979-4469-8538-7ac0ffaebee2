/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { ReloadOutlined, SaveOutlined } from '@ant-design/icons';
import type { RJSFSchema, UiSchema } from '@rjsf/utils';
import { Alert, Button, Col, Input, Row, Space, Typography } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';
import { EditableConfigPreview } from './editable-config-preview';

const { Title } = Typography;
const { TextArea } = Input;

// 组件配置 - 改为泛型
interface ComponentConfig<T = unknown> {
  schema?: RJSFSchema;
  uiSchema?: UiSchema;
  defaultData?: T;
}

const EditorContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 24px;
`;

const ContentRow = styled(Row)`
  flex: 1;
  height: 0; /* 强制使用flex布局 */
`;

const PanelColumn = styled(Col)`
  height: 100%;
  display: flex;
  flex-direction: column;

  &:first-child {
    padding-right: 12px;
  }

  &:last-child {
    padding-left: 12px;
  }
`;

const PanelCard = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
`;

const PanelHeader = styled.div`
  padding: 20px 24px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const PanelContent = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const ScrollableContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  /* 确保滚动区域有正确的高度 */
  min-height: 0;
`;

const JsonEditor = styled(TextArea)`
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  border: none;
  resize: none;

  &:focus {
    box-shadow: none;
  }
`;

const ErrorAlert = styled(Alert)`
  margin: 16px 24px;
  flex-shrink: 0;
`;

export interface SimpleConfigEditorProps<T = unknown> {
  /** 组件配置 */
  componentConfig: ComponentConfig<T>;
  /** 初始配置数据 */
  initialData?: T;
  /** 配置变化回调 */
  onChange?: (data: T) => void;
  /** 保存回调 */
  onSave?: (data: T) => void;
  /** 重置回调 */
  onReset?: () => void;
  /** 标题 */
  title?: string;
}

export const SimpleConfigEditor = <T = unknown>({
  componentConfig,
  initialData,
  onChange,
  onSave,
  onReset,
  title = '配置编辑器',
}: SimpleConfigEditorProps<T>) => {
  const [configData, setConfigData] = useState<T>(
    initialData || componentConfig.defaultData || ({} as T),
  );
  const [jsonText, setJsonText] = useState(() =>
    JSON.stringify(
      initialData || componentConfig.defaultData || ({} as T),
      null,
      2,
    ),
  );
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // 保存原始数据以便重置
  const originalData = useMemo(
    () => initialData || componentConfig.defaultData || ({} as T),
    [componentConfig.defaultData, initialData],
  );

  // 验证并更新JSON
  const handleJsonChange = useCallback(
    (value: string) => {
      setJsonText(value);

      try {
        const parsed = JSON.parse(value) as T;
        setConfigData(parsed);
        setJsonError(null);
        setHasChanges(true);
        onChange?.(parsed);
      } catch (error) {
        setJsonError(error instanceof Error ? error.message : '无效的JSON格式');
      }
    },
    [onChange],
  );

  // 可视化编辑变化
  const handleVisualChange = useCallback(
    (newData: T) => {
      setConfigData(newData);
      setJsonText(JSON.stringify(newData, null, 2));
      setJsonError(null);
      setHasChanges(true);
      onChange?.(newData);
    },
    [onChange],
  );

  // 保存配置
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(configData);
      setHasChanges(false);
    }
  }, [onSave, configData]);

  // 重置配置
  const handleReset = useCallback(() => {
    setConfigData(originalData);
    setJsonText(JSON.stringify(originalData, null, 2));
    setJsonError(null);
    setHasChanges(false);
    onChange?.(originalData);
    if (onReset) {
      onReset();
    }
  }, [originalData, onChange, onReset]);

  return (
    <EditorContainer>
      <ContentRow gutter={0}>
        <PanelColumn span={12}>
          <PanelCard>
            <PanelHeader>
              <div>
                <Title
                  level={4}
                  style={{ margin: 0, marginBottom: '4px' }}
                >
                  {title}
                </Title>
              </div>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                  disabled={!hasChanges}
                  size="small"
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                  disabled={!hasChanges || !!jsonError}
                  size="small"
                >
                  保存
                </Button>
              </Space>
            </PanelHeader>
            <PanelContent>
              <div
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <ScrollableContent>
                  {componentConfig.schema ? (
                    <EditableConfigPreview
                      data={configData}
                      schema={componentConfig.schema}
                      uiSchema={componentConfig.uiSchema}
                      title=""
                      onChange={handleVisualChange}
                    />
                  ) : (
                    <Alert
                      type="warning"
                      message="未提供配置结构"
                      description="请在componentConfig中提供schema配置"
                    />
                  )}
                </ScrollableContent>
              </div>
            </PanelContent>
          </PanelCard>
        </PanelColumn>

        <PanelColumn span={12}>
          <PanelCard>
            <PanelHeader>
              <div>
                <Title
                  level={4}
                  style={{ margin: 0, marginBottom: '4px' }}
                >
                  JSON 编辑器
                </Title>
              </div>
            </PanelHeader>
            <PanelContent>
              <div
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {jsonError && (
                  <ErrorAlert
                    type="error"
                    message="JSON格式错误"
                    description={jsonError}
                    showIcon
                  />
                )}
                <ScrollableContent>
                  <JsonEditor
                    value={jsonText}
                    onChange={(e) => handleJsonChange(e.target.value)}
                    placeholder="请输入有效的JSON配置"
                    style={{ height: '100%', minHeight: 400 }}
                  />
                </ScrollableContent>
              </div>
            </PanelContent>
          </PanelCard>
        </PanelColumn>
      </ContentRow>
    </EditorContainer>
  );
};
