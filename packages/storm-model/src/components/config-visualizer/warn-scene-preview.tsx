/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  ClockCircleOutlined,
  FilterOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Alert, Select, Space, Switch, TimePicker, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;

const PreviewContainer = styled.div`
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  min-height: 400px;
`;

const FilterSection = styled.div`
  margin-bottom: 24px;

  .filter-item {
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
  }
`;

const TimeSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
`;

const StatusBar = styled.div`
  padding: 12px 16px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

// 告警场景配置类型
interface SecondFilterItem {
  label: string;
  key: string;
  type: 'select' | 'range' | 'input';
  options?: string[];
  min?: number;
  max?: number;
  unit?: string;
}

interface WarnConfig {
  enableTime: boolean;
  startTime: string;
  endTime: string;
}

interface WarningSceneConfig {
  secondFilterItems: SecondFilterItem[];
  warnConfig: WarnConfig;
}

export interface WarnScenePreviewProps {
  /** 告警场景配置 */
  config: WarningSceneConfig;
  /** 标题 */
  title?: string;
  /** 是否显示状态栏 */
  showStatus?: boolean;
}

export const WarnScenePreview: React.FC<WarnScenePreviewProps> = ({
  config,
  title = '告警场景预览',
  showStatus = true,
}) => {
  const { secondFilterItems = [], warnConfig } = config;

  // 渲染筛选项
  const renderFilterItem = (item: SecondFilterItem) => {
    switch (item.type) {
      case 'select':
        return (
          <Select
            placeholder={`请选择${item.label}`}
            style={{ width: '100%' }}
            options={item.options?.map((opt) => ({ label: opt, value: opt }))}
          />
        );

      case 'range':
        return (
          <div>
            <Space>
              <Text>
                范围：{item.min} - {item.max}
              </Text>
              {item.unit && <Text type="secondary">({item.unit})</Text>}
            </Space>
            <div style={{ marginTop: 8 }}>
              <input
                type="range"
                min={item.min}
                max={item.max}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );

      case 'input':
      default:
        return (
          <input
            type="text"
            placeholder={`请输入${item.label}`}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
            }}
          />
        );
    }
  };

  return (
    <PreviewContainer>
      <Title level={4}>
        <WarningOutlined style={{ color: '#ff7875', marginRight: 8 }} />
        {title}
      </Title>

      {/* 筛选条件区域 */}
      <FilterSection>
        <Title level={5}>
          <FilterOutlined style={{ marginRight: 8 }} />
          筛选条件
        </Title>

        {secondFilterItems.length > 0 ? (
          secondFilterItems.map((item, index) => (
            <div
              key={item.key || index}
              className="filter-item"
            >
              <Text
                strong
                style={{ display: 'block', marginBottom: 8 }}
              >
                {item.label}
              </Text>
              {renderFilterItem(item)}
            </div>
          ))
        ) : (
          <Alert
            message="暂无筛选条件配置"
            type="info"
            showIcon
          />
        )}
      </FilterSection>

      {/* 时间配置区域 */}
      <TimeSection>
        <Title level={5}>
          <ClockCircleOutlined style={{ marginRight: 8 }} />
          时间设置
        </Title>

        <Space
          direction="vertical"
          style={{ width: '100%' }}
        >
          <div>
            <Switch
              checked={warnConfig?.enableTime}
              disabled
            />
            <Text style={{ marginLeft: 8 }}>启用时间限制</Text>
          </div>

          {warnConfig?.enableTime && (
            <Space>
              <Text>告警时间：</Text>
              <TimePicker
                value={
                  warnConfig.startTime
                    ? dayjs(warnConfig.startTime, 'HH:mm')
                    : undefined
                }
                format="HH:mm"
                disabled
              />
              <Text>-</Text>
              <TimePicker
                value={
                  warnConfig.endTime
                    ? dayjs(warnConfig.endTime, 'HH:mm')
                    : undefined
                }
                format="HH:mm"
                disabled
              />
            </Space>
          )}
        </Space>
      </TimeSection>

      {/* 状态栏 */}
      {showStatus && (
        <StatusBar>
          <WarningOutlined style={{ color: '#fa8c16' }} />
          <Text>
            配置已生效 -{secondFilterItems.length} 个筛选项, 时间限制:{' '}
            {warnConfig?.enableTime ? '已启用' : '未启用'}
          </Text>
        </StatusBar>
      )}
    </PreviewContainer>
  );
};
