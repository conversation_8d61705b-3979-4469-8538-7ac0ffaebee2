/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Button, Space, Spin, Statistic } from 'antd';
import styled from 'styled-components';

const CounterContainer = styled.div`
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid rgba(0, 0, 0, 0.06);
`;

const StyledStatistic = styled(Statistic)`
  text-align: center;
  margin-bottom: 24px;

  .ant-statistic-title {
    font-size: 18px;
    font-weight: 600;
    color: #595959;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    font-size: 48px;
    font-weight: 700;
    color: #1890ff;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
`;

const ButtonSpace = styled(Space)`
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;

  .ant-btn {
    border-radius: 8px;
    font-weight: 500;
    min-width: 100px;
    height: 40px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &.ant-btn-dangerous {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
        color: white;
      }
    }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid rgba(0, 0, 0, 0.06);

  .ant-spin-dot-item {
    background-color: #1890ff;
  }
`;

export interface CounterUIProps {
  value: number;
  loading?: boolean;
  onIncrement: () => void;
  onDecrement: () => void;
  onReset: () => void;
  onIncrementByAmount: (amount: number) => void;
  onAsyncIncrement: () => void;
}

export const CounterUI: React.FC<CounterUIProps> = ({
  value,
  loading = false,
  onIncrement,
  onDecrement,
  onReset,
  onIncrementByAmount,
  onAsyncIncrement,
}) => {
  if (loading) {
    return (
      <LoadingContainer>
        <Spin size="large" />
      </LoadingContainer>
    );
  }

  return (
    <CounterContainer>
      <StyledStatistic
        title="计数器值"
        value={value}
      />
      <ButtonSpace>
        <Button onClick={onIncrement}>增加</Button>
        <Button onClick={onDecrement}>减少</Button>
        <Button onClick={() => onIncrementByAmount(10)}>+10</Button>
        <Button onClick={onAsyncIncrement}>异步 +5</Button>
        <Button
          onClick={onReset}
          danger
        >
          重置
        </Button>
      </ButtonSpace>
    </CounterContainer>
  );
};
