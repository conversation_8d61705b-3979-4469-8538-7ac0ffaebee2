/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import React from 'react';
import styled from 'styled-components';

export interface HeaderHelpProps {
  /** 帮助文档链接 */
  helpUrl?: string;
  /** 帮助点击回调 */
  onHelpClick?: () => void;
  /** 主题模式 */
  theme?: 'light' | 'dark';
}

const HelpButton = styled(Button)<{ $theme: 'light' | 'dark' }>`
  color: ${({ $theme }) => ($theme === 'dark' ? '#ffffff' : '#000000')};
`;

export const HeaderHelp: React.FC<HeaderHelpProps> = ({
  helpUrl,
  onHelpClick,
  theme = 'light',
}) => {
  const handleHelpClick = () => {
    if (onHelpClick) {
      onHelpClick();
    } else if (helpUrl) {
      window.open(helpUrl, '_blank');
    }
  };

  return (
    <Tooltip title="帮助文档">
      <HelpButton
        type="text"
        icon={<QuestionCircleOutlined />}
        size="large"
        onClick={handleHelpClick}
        $theme={theme}
      />
    </Tooltip>
  );
};
