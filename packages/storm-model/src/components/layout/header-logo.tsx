/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React from 'react';
import styled from 'styled-components';

export interface HeaderLogoProps {
  /** Logo 图片地址 */
  src?: string;
  /** Logo 文字 */
  text?: string;
  /** Logo 点击回调 */
  onClick?: () => void;
}

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  margin-right: 24px;
  min-width: fit-content;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;

  @media (max-width: 768px) {
    margin-right: 16px;
  }
`;

const LogoImage = styled.img`
  height: 32px;
  width: auto;
  margin-right: 8px;

  @media (max-width: 768px) {
    height: 28px;
  }
`;

const LogoText = styled.div`
  font-weight: bold;
  font-size: 18px;

  @media (max-width: 768px) {
    font-size: 16px;
  }
`;

export const HeaderLogo: React.FC<HeaderLogoProps> = ({
  src,
  text,
  onClick,
}) => {
  if (!src && !text) return null;

  return (
    <LogoContainer onClick={onClick}>
      {src && (
        <LogoImage
          src={src}
          alt="Logo"
        />
      )}
      {!src && text && <LogoText>{text}</LogoText>}
    </LogoContainer>
  );
};
