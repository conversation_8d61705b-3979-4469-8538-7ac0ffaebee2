/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Menu } from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';

export interface MenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  disabled?: boolean;
  children?: MenuItem[];
}

export interface HeaderMenuProps {
  /** 菜单项 */
  items: MenuItem[];
  /** 当前选中的菜单项 */
  selectedKeys?: string[];
  /** 菜单点击回调 */
  onMenuClick?: (key: string) => void;
  /** 最大显示菜单数量，超过此数量会使用更多按钮 */
  maxVisibleMenus?: number;
  /** 主题模式 */
  theme?: 'light' | 'dark';
  /** 屏幕宽度 */
  screenWidth: number;
}

const MenuContainer = styled.div<{
  $collapsed: boolean;
  $theme: 'light' | 'dark';
}>`
  flex: 1;
  min-width: 0;
  display: ${(props) => (props.$collapsed ? 'none' : 'flex')};
  align-items: center;

  @media (max-width: 1024px) {
    display: ${(props) => (props.$collapsed ? 'none' : 'flex')};
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const MobileMenuContainer = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    margin-right: auto;
  }
`;

const CollapseButton = styled(Button)`
  @media (min-width: 769px) {
    display: none;
  }
`;

export const HeaderMenu: React.FC<HeaderMenuProps> = ({
  items,
  selectedKeys,
  onMenuClick,
  maxVisibleMenus = 6,
  theme = 'light',
  screenWidth,
}) => {
  const [collapsed, setCollapsed] = useState(false);

  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    onMenuClick?.(key);
  };

  // 根据屏幕宽度和最大显示数量构建菜单项
  const getMenuItems = () => {
    const isMobile = screenWidth <= 768;
    const isTablet = screenWidth <= 1024;

    let visibleCount = maxVisibleMenus;

    if (isMobile) {
      // 移动端全部折叠到下拉菜单
      return {
        desktopMenuItems: [],
        mobileMenuItems: items,
      };
    } else if (isTablet) {
      // 平板端减少显示数量
      visibleCount = Math.min(maxVisibleMenus - 2, 4);
    }

    if (items.length <= visibleCount) {
      // 菜单项不多，直接显示
      return {
        desktopMenuItems: items,
        mobileMenuItems: items,
      };
    }

    // 菜单项过多，需要"更多"子菜单
    const visibleMenus = items.slice(0, visibleCount - 1); // 为"更多"留出位置
    const moreMenus = items.slice(visibleCount - 1);

    const desktopMenuItems = [
      ...visibleMenus,
      {
        key: 'more',
        icon: <MoreOutlined />,
        label: '更多',
        children: moreMenus,
      },
    ];

    return {
      desktopMenuItems,
      mobileMenuItems: items,
    };
  };

  const { desktopMenuItems, mobileMenuItems } = getMenuItems();

  // 移动端菜单配置
  const mobileMenuProps: MenuProps = {
    items: mobileMenuItems,
    onClick: handleMenuClick,
    selectedKeys,
  };

  // 平板端折叠按钮
  const renderTabletCollapseButton = () => {
    if (screenWidth <= 768 || screenWidth > 1024) return null;

    return (
      <Button
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={() => setCollapsed(!collapsed)}
      />
    );
  };

  return (
    <>
      {/* 菜单部分 - 桌面端 */}
      <MenuContainer
        $collapsed={collapsed}
        $theme={theme}
      >
        <Menu
          theme={theme === 'dark' ? 'dark' : 'light'}
          mode="horizontal"
          selectedKeys={selectedKeys}
          items={desktopMenuItems}
          onClick={handleMenuClick}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            flex: 1,
            minWidth: 0,
          }}
        />
      </MenuContainer>

      {/* 菜单部分 - 移动端 */}
      <MobileMenuContainer>
        <Dropdown
          menu={mobileMenuProps}
          placement="bottomLeft"
        >
          <CollapseButton
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
          />
        </Dropdown>
      </MobileMenuContainer>

      {/* 平板端折叠按钮 */}
      {renderTabletCollapseButton()}
    </>
  );
};
