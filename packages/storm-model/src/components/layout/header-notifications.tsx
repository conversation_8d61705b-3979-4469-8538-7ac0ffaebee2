/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BellOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Badge, Button, Dropdown } from 'antd';
import React from 'react';
import styled from 'styled-components';

export interface NotificationItem {
  id: string;
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  type?: 'info' | 'warning' | 'error' | 'success';
}

export interface HeaderNotificationsProps {
  /** 通知列表 */
  items: NotificationItem[];
  /** 未读通知数量 */
  unreadCount?: number;
  /** 通知点击回调 */
  onNotificationClick?: (notification: NotificationItem) => void;
  /** 查看全部通知回调 */
  onViewAllNotifications?: () => void;
  /** 主题模式 */
  theme?: 'light' | 'dark';
}

const NotificationButton = styled(Button)<{ $theme: 'light' | 'dark' }>`
  color: ${({ $theme }) => ($theme === 'dark' ? '#ffffff' : '#000000')};
`;

export const HeaderNotifications: React.FC<HeaderNotificationsProps> = ({
  items,
  unreadCount,
  onNotificationClick,
  onViewAllNotifications,
  theme = 'light',
}) => {
  const notificationItems: MenuProps['items'] = [
    ...items.slice(0, 5).map((notification) => ({
      key: notification.id,
      label: (
        <div
          style={{
            width: 280,
            padding: '8px 0',
            borderBottom: '1px solid #f0f0f0',
          }}
          onClick={() => onNotificationClick?.(notification)}
        >
          <div style={{ fontWeight: notification.read ? 'normal' : 'bold' }}>
            {notification.title}
          </div>
          <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
            {notification.timestamp}
          </div>
        </div>
      ),
    })),
    {
      type: 'divider',
    },
    {
      key: 'view-all',
      label: (
        <div
          style={{ textAlign: 'center', padding: '8px 0' }}
          onClick={onViewAllNotifications}
        >
          查看全部通知
        </div>
      ),
    },
  ];

  return (
    <Dropdown
      menu={{ items: notificationItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Badge
        count={unreadCount}
        size="small"
      >
        <NotificationButton
          type="text"
          icon={<BellOutlined />}
          size="large"
          $theme={theme}
        />
      </Badge>
    </Dropdown>
  );
};
