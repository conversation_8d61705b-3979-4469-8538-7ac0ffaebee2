/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { UserOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Avatar, Dropdown, Space } from 'antd';
import React from 'react';

export interface MenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  disabled?: boolean;
  children?: MenuItem[];
}

export interface UserInfo {
  name: string;
  avatar?: string;
  email?: string;
  role?: string;
}

export interface HeaderUserProps {
  /** 用户信息 */
  userInfo: UserInfo;
  /** 用户菜单项 */
  menuItems?: MenuProps['items'];
  /** 用户菜单点击回调 */
  onUserMenuClick?: (key: string) => void;
}

export const HeaderUser: React.FC<HeaderUserProps> = ({
  userInfo,
  menuItems,
  onUserMenuClick,
}) => {
  const userMenuProps: MenuProps = {
    items: menuItems || [
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人资料',
      },
      {
        type: 'divider',
      },
      {
        key: 'logout',
        label: '退出登录',
      },
    ],
    onClick: ({ key }) => onUserMenuClick?.(key),
  };

  return (
    <Dropdown
      menu={userMenuProps}
      placement="bottomRight"
      trigger={['click']}
    >
      <Space style={{ cursor: 'pointer' }}>
        <Avatar
          src={userInfo.avatar}
          icon={!userInfo.avatar && <UserOutlined />}
          size="default"
        />
        <span style={{ fontSize: '14px' }}>{userInfo.name}</span>
      </Space>
    </Dropdown>
  );
};
