/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { requestApi } from '@waterdesk/request/request';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { reportWebVitals } from '@waterdesk/core/utils';
import { injectAuthorizationPlugin } from '@waterdesk/request/request-plugins';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from 'src/store/configure-store';
import App from './app';
import { BASE_URL } from './config';

dayjs.locale('zh-cn');

const root = createRoot(document.getElementById('root')!);
requestApi.initialize({
  baseUrl: BASE_URL,
  requestInterceptors: [
    { name: 'injectAuthorizationPlugin', handler: injectAuthorizationPlugin },
  ],
});

root.render(
  <Provider store={store}>
    <StrictMode>
      <App />
    </StrictMode>
  </Provider>,
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
