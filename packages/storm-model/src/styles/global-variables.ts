/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React from 'react';
import { css } from 'styled-components';
import { useAppSelector } from '@/store/hooks';
import type { ThemeConfig } from '@/store/slices/theme-slice';

/**
 * 主题钩子 - 获取当前主题配置
 */
export const useTheme = () => {
  const { mode, themes } = useAppSelector((state) => state.theme);
  return themes[mode];
};

/**
 * 主题配置类型 - 用于 styled-components 的 props
 */
export interface ThemeProps {
  theme: ThemeConfig;
}

/**
 * 样式工厂函数 - 根据主题配置生成样式
 */
export const createStyledTheme = (themeConfig: ThemeConfig) => ({
  // 颜色配置
  colors: {
    primary: themeConfig.primaryColor,
    success: themeConfig.successColor,
    warning: themeConfig.warningColor,
    error: themeConfig.errorColor,
    info: themeConfig.infoColor,
    background: themeConfig.backgroundColor,
    surface: themeConfig.surfaceColor,
    text: themeConfig.textColor,
    textSecondary: themeConfig.textSecondaryColor,
    border: themeConfig.borderColor,
    shadow: themeConfig.shadowColor,
  },

  // 边框配置
  border: {
    radius: `${themeConfig.borderRadius}px`,
    width: `${themeConfig.borderWidth}px`,
    style: themeConfig.borderStyle,
    color: themeConfig.borderColor,
    full: `${themeConfig.borderWidth}px ${themeConfig.borderStyle} ${themeConfig.borderColor}`,
  },

  // 字体配置
  font: {
    size: `${themeConfig.fontSize}px`,
    sizeSmall: `${themeConfig.fontSizeSmall}px`,
    sizeLarge: `${themeConfig.fontSizeLarge}px`,
  },

  // 间距配置
  spacing: {
    base: `${themeConfig.spacing}px`,
    small: `${themeConfig.spacingSmall}px`,
    large: `${themeConfig.spacingLarge}px`,
  },

  // 组件高度配置
  height: {
    component: `${themeConfig.componentHeight}px`,
    componentSmall: `${themeConfig.componentHeightSmall}px`,
    componentLarge: `${themeConfig.componentHeightLarge}px`,
  },

  // 阴影配置
  shadow: {
    blur: `${themeConfig.shadowBlur}px`,
    spread: `${themeConfig.shadowSpread}px`,
    offset: `${themeConfig.shadowOffset}px`,
    color: themeConfig.shadowColor,
    box: `0 ${themeConfig.shadowOffset}px ${themeConfig.shadowBlur}px ${themeConfig.shadowSpread}px ${themeConfig.shadowColor}`,
  },

  // 透明度配置
  opacity: {
    default: themeConfig.opacity,
    disabled: themeConfig.opacityDisabled,
    hover: themeConfig.opacityHover,
  },
});

/**
 * 高阶组件样式生成器
 * 为 styled-components 提供主题感知的样式生成函数
 */
export const withTheme = <T extends ThemeProps>(
  stylesFn: (props: T) => ReturnType<typeof css>,
) => {
  return (props: T) => stylesFn(props);
};

/**
 * 常用样式混入 - 基于主题配置
 */
export const getCommonStyles = (themeConfig: ThemeConfig) => {
  const styledTheme = createStyledTheme(themeConfig);

  return {
    // 页面容器样式
    pageContainer: css`
      padding: ${styledTheme.spacing.large};
      background: ${styledTheme.colors.background};
      min-height: 100vh;
      color: ${styledTheme.colors.text};
      transition: all 0.3s ease;
    `,

    // 页面头部样式
    pageHeader: css`
      margin-bottom: ${styledTheme.spacing.large};

      .ant-typography {
        margin-bottom: ${styledTheme.spacing.base} !important;
        color: ${styledTheme.colors.text};
      }
    `,

    // 内容区域样式
    contentArea: css`
      .ant-card {
        box-shadow: ${styledTheme.shadow.box};
        border-radius: ${styledTheme.border.radius};
        background: ${styledTheme.colors.surface};
        border: ${styledTheme.border.full};

        .ant-card-head-title {
          color: ${styledTheme.colors.text};
          font-weight: 600;
        }
      }
    `,

    // 表单样式
    formStyles: css`
      .ant-form-item {
        margin-bottom: ${styledTheme.spacing.base};

        .ant-form-item-label {
          color: ${styledTheme.colors.text};
          font-weight: 500;
        }

        .ant-form-item-required {
          &::before {
            color: ${styledTheme.colors.error};
          }
        }
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        border-radius: ${styledTheme.border.radius};
        border: ${styledTheme.border.full};
        font-size: ${styledTheme.font.size};
      }
    `,

    // 按钮组样式
    actionButtons: css`
      display: flex;
      gap: ${styledTheme.spacing.small};
      justify-content: flex-end;
      margin-top: ${styledTheme.spacing.large};
      padding-top: ${styledTheme.spacing.base};
      border-top: ${styledTheme.border.full};
    `,

    // 边框样式
    border: css`
      border: ${styledTheme.border.full};
      border-radius: ${styledTheme.border.radius};
    `,

    // 阴影样式
    shadow: css`
      box-shadow: ${styledTheme.shadow.box};
    `,

    // 悬停效果
    hover: css`
      transition: opacity 0.3s ease;
      &:hover {
        opacity: ${styledTheme.opacity.hover};
      }
    `,

    // 禁用状态
    disabled: css`
      opacity: ${styledTheme.opacity.disabled};
      pointer-events: none;
    `,

    // 文本样式
    text: {
      primary: css`
        color: ${styledTheme.colors.text};
        font-size: ${styledTheme.font.size};
      `,
      secondary: css`
        color: ${styledTheme.colors.textSecondary};
        font-size: ${styledTheme.font.size};
      `,
      small: css`
        color: ${styledTheme.colors.text};
        font-size: ${styledTheme.font.sizeSmall};
      `,
      large: css`
        color: ${styledTheme.colors.text};
        font-size: ${styledTheme.font.sizeLarge};
      `,
    },
  };
};

/**
 * 主题感知的 styled-components 创建器
 * 使用方式：
 * const MyComponent = createThemedComponent('div')`
 *   background: ${({ theme }) => theme.colors.surface};
 *   color: ${({ theme }) => theme.colors.text};
 * `;
 */
export const createThemedComponent = <
  T extends keyof React.JSX.IntrinsicElements,
>(
  element: T,
) => {
  return (strings: TemplateStringsArray, ...values: Array<string | number>) => {
    // 这里需要结合实际的 styled-components 使用，暂时返回样式函数
    return (props: ThemeProps) => {
      const styledTheme = createStyledTheme(props.theme);
      // 实际使用中，这里会通过 styled-components 的机制处理
      return { styledTheme, element, strings, values };
    };
  };
};

export type StyledTheme = ReturnType<typeof createStyledTheme>;
