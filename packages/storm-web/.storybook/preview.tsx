import type { Preview } from '@storybook/react-vite';
import { type ReactElement } from 'react';
import { Provider } from 'react-redux';
import store from '../src/app/store/configure-store';
import { GlobalStyle } from '../src/styles/global-style';
import {
  AntdThemeProvider,
  StyledThemeProvider,
} from '../src/styles/theme/theme-provider-adapter';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },

    a11y: {
      // 'todo' - show a11y violations in the test UI only
      // 'error' - fail CI on a11y violations
      // 'off' - skip a11y checks entirely
      test: 'todo',
    },
  },

  decorators: [
    (Story): ReactElement => (
      <Provider store={store}>
        <AntdThemeProvider>
          <StyledThemeProvider>
            <>
              <GlobalStyle />
              <Story />
            </>
          </StyledThemeProvider>
        </AntdThemeProvider>
      </Provider>
    ),
  ],
};

export default preview;
