/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getFlowUnitFormat,
  getHeadUnitFormat,
} from '@waterdesk/data/unit-system';
import { Modal, Spin } from 'antd';
import { useSelector } from 'react-redux';
import { useSchedulingPlansData } from 'src/app/hooks/use-scheduling-plans-data';
import { hostApp } from 'src/app/host-app';
import { selectTimelineDate } from 'src/app/store/time-line/selectors';
import SchedulingPlans from 'src/components/scheduling-navigation/scheduling-plans';
import SchedulingPlansDashboard from 'src/components/scheduling-navigation/scheduling-plans-dashboard';

interface Props {
  open: boolean;
  onClose: () => void;
}

export default function SchedulingPlansModalContainerLinGang(props: Props) {
  const { open, onClose } = props;

  const timelineDate = useSelector(selectTimelineDate);

  const {
    plants,
    pumpStations,
    date,
    recommendedValues,
    forecastRealtimeFlow,
    loading,
    getPatternValues,
    getPlantDailySumFlows,
    getModalTitle,
  } = useSchedulingPlansData(open, timelineDate);

  return (
    <Modal
      title={getModalTitle()}
      centered
      destroyOnHidden
      open={open}
      width="70%"
      onCancel={onClose}
      footer={null}
      styles={{ body: { padding: '8px' } }}
    >
      <Spin
        spinning={loading}
        tip="加载调度数据中..."
      >
        <SchedulingPlans
          date={date}
          plants={plants}
          pumpStations={pumpStations}
          forecastFlow={forecastRealtimeFlow}
          recommendedValues={recommendedValues}
          flowUnitFormat={getFlowUnitFormat()}
          headUnitFormat={getHeadUnitFormat()}
          pumpStateColor={hostApp().appConfig.pumpStateColor}
          showPumpStationAndPressure={true}
          getPatternValues={getPatternValues}
          getPlantDailySumFlows={getPlantDailySumFlows}
          customTableComponent={SchedulingPlansDashboard}
        />
      </Spin>
    </Modal>
  );
}

SchedulingPlansModalContainerLinGang.displayName =
  'SchedulingPlansModalContainerLinGang';
