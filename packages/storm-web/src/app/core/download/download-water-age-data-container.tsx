/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { TimeData } from '@waterdesk/data/time-data';
import { getUnitValue } from '@waterdesk/data/unit-system';
import {
  ExcelSheetData,
  exportToExcel,
  formatNumber,
  getMinMax,
} from '@waterdesk/data/utils';
import { getGroupObjectsTimeValues } from '@waterdesk/request/get-group-time-values';
import { Button, DatePicker, Form, FormInstance, Modal } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useRef } from 'react';
import { curDb } from 'src/app/host-app';

interface FormDataType {
  month: Dayjs;
}
interface Props {
  visible: boolean;
  close: () => void;
}

function generateData(data: TimeData[], unitName: string | undefined) {
  const dataSource: ExcelSheetData['dataSource'] = [];
  const values: number[] = [];
  data.forEach((item) => {
    const value = unitName ? getUnitValue(unitName, item.value) : item.value;
    if (typeof value === 'number') {
      values.push(value);
    }
    dataSource.push({
      time: dayjs(item.time).format('YYYY-MM-DD'),
      value,
    });
  });
  if (values.length === 0) {
    dataSource.push({ time: '最大值', value: 'N/A' });
    dataSource.push({ time: '最小值', value: 'N/A' });
    dataSource.push({ time: '平均值', value: 'N/A' });
  } else {
    const { min, max } = getMinMax(values);
    dataSource.push({
      time: '最大值',
      value: max,
    });
    dataSource.push({
      time: '最小值',
      value: min,
    });
    dataSource.push({
      time: '平均值',
      value: formatNumber(
        values.reduce((acc, val) => acc + val, 0) / values.length,
        2,
      ),
    });
  }
  return dataSource;
}

export default function DownloadWaterAgeDataContainer(props: Props) {
  const { visible, close } = props;
  const OTYPE = 'MODEL_SUMMARY';
  const ONAME = 'AGE_SUMMARY_DAY';
  const VPROP = 'AGE_SUMMARY_DAY';
  const unitSymbol = curDb().getUnitFormat(OTYPE, VPROP)?.unitSymbol;
  const columnsData = [
    {
      dataIndex: 'time',
      title: '日期',
    },
    {
      dataIndex: 'value',
      title: unitSymbol ? `平均水龄(${unitSymbol})` : '平均水龄',
    },
  ];
  const modalForm = useRef<FormInstance<FormDataType>>(null);

  const downloadWaterAgeData = () => {
    if (modalForm.current) {
      modalForm.current
        .validateFields()
        .then((formData: FormDataType) => {
          getGroupObjectsTimeValues(
            formData.month.startOf('M').format('YYYY-MM-DD'),
            formData.month.endOf('M').format('YYYY-MM-DD'),
            OTYPE,
            ONAME,
            {
              [VPROP]: {
                vprop: VPROP,
              },
            },
          ).then((res) => {
            if (res.status === 'Success') {
              const waterAgeData = res.values?.get(VPROP);
              if (waterAgeData) {
                const unitName = curDb()
                  .getPropertyInfo(OTYPE)
                  ?.getPropertyUnit(VPROP);
                exportToExcel(
                  generateData(waterAgeData.timeData, unitName),
                  columnsData,
                  `${formData.month.format('YYYY-MM')}月水龄数据`,
                );
              }
            } else {
              console.log(res.errorMessage);
            }
          });
        })
        .catch((errorInfo) => {
          console.error('Validation failed:', errorInfo);
        });
    }
  };

  return (
    <Modal
      className="modalTitle downloadBox"
      title="水龄数据下载"
      open={visible}
      destroyOnHidden
      onCancel={() => {
        close();
      }}
      footer={[
        <Button
          key="downloadWaterAgeData"
          onClick={() => downloadWaterAgeData()}
        >
          下载
        </Button>,
        <Button onClick={close}>取消</Button>,
      ]}
    >
      <Form
        preserve={false}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 14 }}
        ref={modalForm}
      >
        <Form.Item
          name="month"
          label="选择月份"
          rules={[{ required: true, message: '请选择月份!' }]}
          initialValue={dayjs()}
        >
          <DatePicker picker="month" />
        </Form.Item>
      </Form>
    </Modal>
  );
}
