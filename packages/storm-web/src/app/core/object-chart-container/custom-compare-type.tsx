/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { CloseCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { ChartCompareType } from '@waterdesk/data/object-chart';
import { DatePicker, message, Space } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { useMergedState } from 'rc-util';
import { useEffect, useMemo } from 'react';
import { CustomCompareTypeValue } from 'src/components/charts/object-chart/object-chart-content-new';

interface Props {
  compareDateRange: RangePickerProps['value'];
  value?: CustomCompareTypeValue;
  onChange?: (v: CustomCompareTypeValue) => void;
}

const defaultCompareDateRange: [string, string] = [
  dayjs().format('YYYY-MM-DD 00:00:00'),
  dayjs().format('YYYY-MM-DD 23:59:59'),
];

function mergeDate(date1: Dayjs, date2: Dayjs): Dayjs {
  const formattedDate1 = date1.format('YYYY-MM-DD');
  const formattedDate2 = date2.format('HH:mm:ss');
  return dayjs(`${formattedDate1} ${formattedDate2}`);
}

function getFormatDateRange(
  dates: RangePickerProps['value'],
  compareDateRange: [Dayjs, Dayjs],
): RangePickerProps['value'] {
  const startDate = dates?.[0];
  if (startDate) {
    const formatStartDate = mergeDate(startDate, compareDateRange[0]);
    const formatEndDate = mergeDate(
      startDate.add(1, 'day'),
      compareDateRange[1],
    );
    return [formatStartDate, formatEndDate];
  }
  return dates;
}

const CustomCompareType = (props: Props) => {
  const { value, compareDateRange, onChange } = props;

  const [messageApi, messageHolder] = message.useMessage();

  const MAX_CUSTOM_DATE_RANGE_DAYS = 6;
  // use defaultCompareDateRange instead of compareDateRange when compareDateRange is invalid date
  const memoCompareDateRange: [string, string] = useMemo(() => {
    if (compareDateRange?.[0] && compareDateRange?.[1])
      return [
        compareDateRange[0].format('YYYY-MM-DD HH:mm:ss'),
        compareDateRange[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    return defaultCompareDateRange;
  }, [compareDateRange]);

  const timeDuration = useMemo(
    () =>
      dayjs(memoCompareDateRange[1]).diff(
        dayjs(memoCompareDateRange[0]),
        'day',
      ),
    [memoCompareDateRange],
  );

  const [customCompareTypeValue, setCustomCompareTypeValue] =
    useMergedState<CustomCompareTypeValue>(
      {
        type: ChartCompareType.custom,
        dateRanges: [],
      },
      {
        value,
        onChange,
      },
    );

  const handleAdd = () => {
    if (customCompareTypeValue.dateRanges.length >= 7) {
      messageApi.warning('自定义对比分析最多只能添加7个日期');
      return;
    }
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: Array.isArray(s.dateRanges)
        ? [...s.dateRanges, null]
        : [null],
    }));
  };

  const handleDelete = (index: number) => {
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: s.dateRanges.filter((_, i) => i !== index),
    }));
  };

  const handleOnChange = (dates: RangePickerProps['value'], index: number) => {
    setCustomCompareTypeValue((s) => {
      const dateRanges = [...s.dateRanges];
      dateRanges[index] = getFormatDateRange(dates, [
        dayjs(memoCompareDateRange[0]),
        dayjs(memoCompareDateRange[1]),
      ]);
      return {
        ...s,
        dateRanges,
      };
    });
  };

  const getDateRange = (
    dateRange: [Dayjs, Dayjs],
    cycle: number = 1,
  ): [Dayjs, Dayjs] => {
    const startTime = dateRange[0].add(cycle, 'd');
    const endTime = dateRange[1].add(cycle, 'd');
    return [startTime, endTime];
  };

  const getCustomDateRange = (
    dateRange: [Dayjs, Dayjs],
  ): Array<[Dayjs, Dayjs]> => {
    const diffDays = dateRange[1].diff(dateRange[0], 'd');
    const days =
      diffDays <= MAX_CUSTOM_DATE_RANGE_DAYS
        ? diffDays
        : MAX_CUSTOM_DATE_RANGE_DAYS;
    const range: Array<[Dayjs, Dayjs]> = [];
    for (let i = 0; i <= days; i += 1) {
      range.push(getDateRange(dateRange, i));
    }
    return range;
  };

  // refresh customCompareTypeValue.dateRanges when memoCompareDateRange is changed
  useEffect(() => {
    setCustomCompareTypeValue((s) => ({
      ...s,
      dateRanges: s.dateRanges.map((dateRange) =>
        getFormatDateRange(dateRange, [
          dayjs(memoCompareDateRange[0]),
          dayjs(memoCompareDateRange[1]),
        ]),
      ),
    }));
  }, [memoCompareDateRange]);

  useEffect(() => {
    if (customCompareTypeValue.type === ChartCompareType.custom) {
      setCustomCompareTypeValue((s) => ({
        ...s,
        dateRanges: getCustomDateRange([
          dayjs(memoCompareDateRange[0]),
          dayjs(memoCompareDateRange[1]),
        ]),
      }));
    }
  }, [customCompareTypeValue.type, memoCompareDateRange.toString()]);

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap' }}>
      {customCompareTypeValue.dateRanges?.length &&
      customCompareTypeValue.type !== ChartCompareType.chainBase ? (
        <Space wrap>
          {customCompareTypeValue.dateRanges.map((dateRange, i) => (
            <div key={i}>
              <Space.Compact>
                <DatePicker
                  style={{ width: '110px' }}
                  placeholder="开始时间"
                  value={dateRange?.[0]}
                  onChange={(date) => handleOnChange([date, dayjs()], i)}
                  allowClear={{
                    clearIcon: false,
                  }}
                  disabled={
                    customCompareTypeValue.type !== ChartCompareType.custom
                  }
                />
              </Space.Compact>
              {customCompareTypeValue.type === ChartCompareType.custom ? (
                <CloseCircleOutlined onClick={() => handleDelete(i)} />
              ) : null}
            </div>
          ))}
        </Space>
      ) : null}
      {customCompareTypeValue.type === ChartCompareType.custom ? (
        <PlusCircleOutlined
          style={{ marginLeft: '10px' }}
          onClick={handleAdd}
        />
      ) : null}
      {messageHolder}
    </div>
  );
};

export default CustomCompareType;
