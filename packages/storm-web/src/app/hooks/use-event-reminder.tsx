/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  EventStatusType,
  getDispatchEventTypeTitle,
  getDispatchTypeTitle,
} from '@waterdesk/data/dispatch-data';
import { EventSchedulingBasicInfo } from '@waterdesk/data/event-scheduling/basic-info';
import {
  getEventSchedulingBasicInfo,
  updateEventSchedulingBasicInfo,
} from '@waterdesk/request/event-scheduling/get-event-info';
import { Button, Modal } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  dispatchLogActions,
  useDispatchLogSlice,
} from 'src/app/store/dispatch-log';
import { scheduleLogActions } from 'src/app/store/schedule-log';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';

/** 事件提醒的时间容差窗口（分钟） */
const REMINDER_TIME_TOLERANCE_MINUTES = 2;

interface UseEventReminderProps {
  messageApi: MessageInstance;
  onEventDetails: (eventId: string) => void;
}

interface EventReminderState {
  eventId: string;
  eventData: EventSchedulingBasicInfo;
  pendingReminderTimes: Set<string>; // 存储待提醒的时间点 (YYYY-MM-DD HH:mm 格式)
}

export const useEventReminder = ({
  messageApi,
  onEventDetails,
}: UseEventReminderProps) => {
  useDispatchLogSlice();

  const dispatch = useDispatch();
  const globalConfig = useSelector(selectGlobalConfig);
  const [modal, contextHolder] = Modal.useModal();

  const [eventReminders, setEventReminders] = useState<
    Map<string, EventReminderState>
  >(new Map());

  const { eventTypeData, reminderConfig, continueAfterStarted } =
    useMemo(() => {
      const dispatchEventConfig = globalConfig?.dispatchEventConfig;
      const eventReminderConfig = dispatchEventConfig?.eventReminderConfig;

      return {
        eventTypeData: dispatchEventConfig?.dispatchEventType ?? [],
        reminderConfig: eventReminderConfig?.reminders ?? [0],
        continueAfterStarted:
          eventReminderConfig?.continueAfterStarted ?? false,
      };
    }, [globalConfig]);

  // 初始化事件提醒状态
  const initializeEventReminders = useCallback(
    (events: EventSchedulingBasicInfo[]) => {
      const now = dayjs();
      const newEventReminders = new Map<string, EventReminderState>();

      events.forEach((event) => {
        const eventStartTime = dayjs(event.eventStartTime);
        const pendingReminderTimes = new Set<string>();

        // 计算所有需要提醒的绝对时间点
        reminderConfig.forEach((offset) => {
          const reminderTime = eventStartTime.add(offset, 'minutes');
          const diffMinutes = now.diff(reminderTime, 'minutes');

          // 如果提醒时间还没到（未来）或者刚过去不久（容差窗口内），则加入待提醒列表
          if (diffMinutes < REMINDER_TIME_TOLERANCE_MINUTES) {
            pendingReminderTimes.add(reminderTime.format('YYYY-MM-DD HH:mm'));
          }
        });

        if (pendingReminderTimes.size > 0) {
          newEventReminders.set(event.eventId, {
            eventId: event.eventId,
            eventData: event,
            pendingReminderTimes,
          });
        }
      });

      setEventReminders(newEventReminders);
    },
    [reminderConfig],
  );

  // 移除已提醒的时间点
  const removeReminderTime = useCallback(
    (eventId: string, reminderTime: string) => {
      setEventReminders((prev) => {
        const newMap = new Map(prev);
        const eventReminder = newMap.get(eventId);

        if (eventReminder) {
          eventReminder.pendingReminderTimes.delete(reminderTime);

          // 如果没有待提醒的时间点了，删除整个事件记录
          if (eventReminder.pendingReminderTimes.size === 0) {
            newMap.delete(eventId);
          } else {
            newMap.set(eventId, eventReminder);
          }
        }

        return newMap;
      });
    },
    [],
  );

  // 获取提醒消息
  const getReminderMessage = useCallback(
    (offset: number, isEventStarted: boolean) => {
      // 生成基础消息
      const getBaseMessage = () => {
        if (offset < 0) return `事件还有${Math.abs(offset)}分钟开始`;
        if (offset === 0) return '事件到了开始时间';
        return `事件已开始${offset}分钟`;
      };

      const baseMessage = getBaseMessage();

      // 如果事件已开始，只返回基础消息
      if (isEventStarted) return baseMessage;

      // 如果事件未开始，添加询问
      return `计划中的${baseMessage}, 是否把事件状态更新为进行中?`;
    },
    [],
  );

  // 处理事件状态更新
  const handleEventStatusUpdate = useCallback(
    async (
      item: EventSchedulingBasicInfo,
      modalInstance: ReturnType<typeof Modal.info>,
    ) => {
      try {
        const res = await updateEventSchedulingBasicInfo({
          ...item,
          eventStatus: EventStatusType.DOING,
        });

        if (res.status === 'Success') {
          messageApi.success('事件状态更新成功');
          modalInstance.destroy();
          dispatch(dispatchLogActions.setNeedUpdate({ needUpdate: true }));
          dispatch(scheduleLogActions.fetchScheduleDataByDate());

          // 更新事件状态后，根据 continueAfterStarted 配置决定是否继续提醒
          if (!continueAfterStarted) {
            // 如果不继续提醒，删除该事件的所有提醒记录
            setEventReminders((prev) => {
              const newMap = new Map(prev);
              newMap.delete(item.eventId);
              return newMap;
            });
          } else {
            // 如果继续提醒，更新事件数据状态
            setEventReminders((prev) => {
              const newMap = new Map(prev);
              const eventReminder = newMap.get(item.eventId);
              if (eventReminder) {
                eventReminder.eventData = {
                  ...item,
                  eventStatus: EventStatusType.DOING,
                };
                newMap.set(item.eventId, eventReminder);
              }
              return newMap;
            });
          }
        } else {
          messageApi.error('事件状态更新失败');
        }
      } catch (error) {
        messageApi.error('事件状态更新失败');
        console.error('更新事件状态失败:', error);
      }
    },
    [messageApi, dispatch, continueAfterStarted],
  );

  // 处理查看事件详情
  const handleEventDetails = useCallback(
    (eventId: string, modalInstance: any) => {
      modalInstance.destroy();
      onEventDetails(eventId);
    },
    [onEventDetails],
  );

  // 渲染事件信息内容
  const eventInfoContent = useCallback(
    ({
      item,
      message,
    }: {
      item: EventSchedulingBasicInfo;
      message: string;
    }) => (
      <>
        <div>{message}</div>
        <div>事件名称: {item.eventTitle}</div>
        <div>开始时间: {item.eventStartTime}</div>
        <div>大类: {getDispatchTypeTitle(eventTypeData, item.eventType)}</div>
        <div>
          小类:{' '}
          {getDispatchEventTypeTitle(
            eventTypeData,
            item.eventType,
            item.eventSubType,
          )}
        </div>
        <p />
      </>
    ),
    [eventTypeData],
  );

  // 显示提醒模态框
  const showReminderModal = useCallback(
    (
      eventData: EventSchedulingBasicInfo,
      offset: number,
      reminderTime: string,
    ) => {
      const isEventInPlanning =
        eventData.eventStatus === EventStatusType.PLANNING;

      const modalInstance = modal.info({
        title: '事件开始提醒',
        content: eventInfoContent({
          item: eventData,
          message: getReminderMessage(offset, !isEventInPlanning),
        }),
        footer: (
          <div style={{ textAlign: 'center' }}>
            {isEventInPlanning ? (
              <>
                <Button
                  key="submit"
                  type="primary"
                  style={{ marginRight: '10px' }}
                  onClick={() =>
                    handleEventStatusUpdate(eventData, modalInstance)
                  }
                >
                  是
                </Button>
                <Button
                  key="back"
                  onClick={() => modalInstance.destroy()}
                >
                  否
                </Button>
                <Button
                  key="info"
                  style={{ position: 'absolute', right: '10px' }}
                  onClick={() =>
                    handleEventDetails(eventData.eventId, modalInstance)
                  }
                >
                  查看详情
                </Button>
              </>
            ) : (
              <>
                <Button
                  key="back"
                  onClick={() => modalInstance.destroy()}
                >
                  关闭
                </Button>
                <Button
                  key="info"
                  type="primary"
                  style={{ marginLeft: '10px' }}
                  onClick={() =>
                    handleEventDetails(eventData.eventId, modalInstance)
                  }
                >
                  查看详情
                </Button>
              </>
            )}
          </div>
        ),
        centered: true,
      });

      // 显示提醒后，移除该时间点
      removeReminderTime(eventData.eventId, reminderTime);
    },
    [
      modal,
      eventInfoContent,
      getReminderMessage,
      handleEventStatusUpdate,
      handleEventDetails,
      removeReminderTime,
    ],
  );

  // 查询最新事件数据并检查提醒
  const queryAndCheckReminders = useCallback(async () => {
    const now = dayjs();

    // 计算查询范围：取所有offset的最大绝对值，前后都用这个范围，加上5分钟冗余
    const maxAbsOffset = Math.max(
      ...reminderConfig.map((offset) => Math.abs(offset)),
    );
    const startTime = now.add(-(maxAbsOffset + 5), 'minutes');
    const endTime = now.add(maxAbsOffset + 5, 'minutes');

    // 根据 continueAfterStarted 配置决定查询哪些状态的事件
    const eventStatuses = continueAfterStarted
      ? [EventStatusType.PLANNING, EventStatusType.DOING]
      : [EventStatusType.PLANNING];

    // 查询事件
    const res = await getEventSchedulingBasicInfo({
      eventStatus: eventStatuses,
      eventStartTime: startTime.format('YYYY-MM-DD HH:mm:00'),
      eventEndTime: endTime.format('YYYY-MM-DD HH:mm:59'),
    });

    if (res.list.length > 0) {
      // 合并现有的事件提醒状态和新查询的事件
      setEventReminders((prevEventReminders) => {
        const newEventReminders = new Map<string, EventReminderState>();

        res.list.forEach((event) => {
          const eventStartTime = dayjs(event.eventStartTime);
          const existingEventReminder = prevEventReminders.get(event.eventId);

          let pendingReminderTimes: Set<string>;

          if (existingEventReminder) {
            // 如果事件已存在，保留现有的 pendingReminderTimes，但更新事件数据
            pendingReminderTimes = existingEventReminder.pendingReminderTimes;
          } else {
            // 如果是新事件，重新计算 pendingReminderTimes
            pendingReminderTimes = new Set<string>();
            reminderConfig.forEach((offset) => {
              const reminderTime = eventStartTime.add(offset, 'minutes');
              const diffMinutes = now.diff(reminderTime, 'minutes');

              // 如果提醒时间还没到（未来）或者刚过去不久（容差窗口内），则加入待提醒列表
              if (diffMinutes < REMINDER_TIME_TOLERANCE_MINUTES) {
                pendingReminderTimes.add(
                  reminderTime.format('YYYY-MM-DD HH:mm'),
                );
              }
            });
          }

          // 检查是否有需要立即触发的提醒
          const currentTimeStr = now.format('YYYY-MM-DD HH:mm');
          pendingReminderTimes.forEach((reminderTimeStr) => {
            // 只有当前时间的分钟与提醒时间完全一致才触发
            if (currentTimeStr === reminderTimeStr) {
              // 计算当前时间相对于事件开始时间的实际偏移量
              // 使用分钟级别的时间来避免秒数精度问题
              const currentTimeMinute = now.startOf('minute');
              const eventStartTimeMinute = eventStartTime.startOf('minute');
              const actualOffset = currentTimeMinute.diff(
                eventStartTimeMinute,
                'minutes',
              );
              showReminderModal(event, actualOffset, reminderTimeStr);
            }
          });

          if (pendingReminderTimes.size > 0) {
            newEventReminders.set(event.eventId, {
              eventId: event.eventId,
              eventData: event,
              pendingReminderTimes,
            });
          }
        });

        return newEventReminders;
      });
    } else {
      // 如果没有事件，清空状态
      setEventReminders(new Map());
    }
  }, [
    reminderConfig,
    initializeEventReminders,
    showReminderModal,
    continueAfterStarted,
  ]);

  return {
    contextHolder,
    queryAndCheckReminders,
    showReminderModal,
    reminderConfig,
    eventReminders: Array.from(eventReminders.values()),
  };
};
