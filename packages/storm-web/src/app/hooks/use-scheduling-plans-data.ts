/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  PlantSimulationInfo,
  PumpStationSimulationInfo,
} from '@waterdesk/data/scheduling-data';
import { TimeData } from '@waterdesk/data/time-data';
import {
  getForecastDayFlow,
  getForecastRealtimeFlow,
} from '@waterdesk/request/get-group-time-values';
import { getPatternTimeValues } from '@waterdesk/request/get-pattern-time-values';
import { getPlantsDailyFlows } from '@waterdesk/request/get-plants-daily-flows';
import { getSchedulingData } from '@waterdesk/request/get-scheduling-data';
import { getSchedulingRecommendedValues } from '@waterdesk/request/get-smart-scheduling';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { RecommendedValues } from 'src/components/scheduling-navigation/scheduling-plans';

export interface UseSchedulingPlansDataReturn {
  plants: PlantSimulationInfo[];
  pumpStations: PumpStationSimulationInfo[];
  date: string;
  recommendedValues: RecommendedValues[];
  forecastRealtimeFlow: TimeData[];
  forecastDayFlow: number | undefined;
  loading: boolean;
  getPatternValues: (
    date: string,
    patterns: string[],
  ) => Promise<Map<string, TimeData[]>>;
  getPlantDailySumFlows: (
    date: string,
    plantIds: string[],
  ) => Promise<Map<string, number>>;
  getModalTitle: () => string;
}

export function useSchedulingPlansData(
  open: boolean,
  timelineDate: string,
): UseSchedulingPlansDataReturn {
  const [plants, setPlants] = useState<PlantSimulationInfo[]>([]);
  const [pumpStations, setPumpStations] = useState<PumpStationSimulationInfo[]>(
    [],
  );
  const [date, setDate] = useState<string>('');
  const [recommendedValues, setRecommendedValues] = useState<
    RecommendedValues[]
  >([]);
  const [forecastRealtimeFlow, setForecastRealtimeFlow] = useState<TimeData[]>(
    [],
  );
  const [forecastDayFlow, setForecastDayFlow] = useState<number | undefined>();
  const [loading, setLoading] = useState<boolean>(false);

  const updateSchedulingPlantsAndPumpStations = async (date: string) => {
    try {
      const res = await getSchedulingData(
        dayjs(date).format('YYYY-MM-DD 00:00:00'),
      );
      if (res.status === 'Success' && res.schedulingData) {
        setPlants([...res.schedulingData.plants.values()]);
        setPumpStations([...res.schedulingData.pumpStations.values()]);
      }
    } catch (error) {
      console.error(
        'Failed to update scheduling plants and pump stations:',
        error,
      );
    }
  };

  const getPatternValues = async (
    date: string,
    patterns: string[],
  ): Promise<Map<string, TimeData[]>> => {
    const res = await getPatternTimeValues(date, patterns);
    if (res.status === 'Success' && res.values !== undefined) return res.values;
    return new Map<string, TimeData[]>();
  };

  const getPlantDailySumFlows = async (
    date: string,
    plantIds: string[],
  ): Promise<Map<string, number>> => {
    if (plantIds.length === 0) return new Map<string, number>();
    const res = await getPlantsDailyFlows(date, plantIds);
    if (res.status === 'Success' && res.values !== undefined) return res.values;
    return new Map<string, number>();
  };

  const updateForecastRealtimeFlow = async (date: string) => {
    try {
      const res = await getForecastRealtimeFlow(date);
      setForecastRealtimeFlow(res);
    } catch (error) {
      console.error('Failed to update forecast realtime flow:', error);
      setForecastRealtimeFlow([]);
    }
  };

  const updateForecastDayFlow = async (date: string) => {
    try {
      const res = await getForecastDayFlow(date);
      setForecastDayFlow(res);
    } catch (error) {
      console.error('Failed to update forecast day flow:', error);
      setForecastDayFlow(undefined);
    }
  };

  const updateRecommendedValues = async (date: string) => {
    try {
      const res = await getSchedulingRecommendedValues(date);
      if (res.values) {
        const values: RecommendedValues[] = [];
        res.values.forEach((item: TimeData) => {
          values.push({
            date: dayjs(item.time).format('YYYY-MM-DD'),
            value: item.value,
          });
        });
        setRecommendedValues(values);
      } else {
        setRecommendedValues([]);
      }
    } catch (error) {
      console.error('Failed to update recommended values:', error);
      setRecommendedValues([]);
    }
  };

  const getModalTitle = (): string => {
    if (forecastDayFlow !== undefined)
      return `调度预案 - 预测水量: ${(forecastDayFlow / 10000).toFixed(
        1,
      )} 万吨`;
    return '调度预案';
  };

  useEffect(() => {
    if (open) {
      setLoading(true);
      const currentDate = dayjs(timelineDate).format('YYYY-MM-DD');
      setDate(currentDate);

      Promise.all([
        updateForecastDayFlow(currentDate),
        updateRecommendedValues(currentDate),
        updateForecastRealtimeFlow(currentDate),
        updateSchedulingPlantsAndPumpStations(timelineDate),
      ]).finally(() => {
        setLoading(false);
      });
    }
  }, [open, timelineDate]);

  return {
    plants,
    pumpStations,
    date,
    recommendedValues,
    forecastRealtimeFlow,
    forecastDayFlow,
    loading,
    getPatternValues,
    getPlantDailySumFlows,
    getModalTitle,
  };
}
