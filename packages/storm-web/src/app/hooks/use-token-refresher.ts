/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';

interface TokenData {
  token: string;
  expiresAt: string | number; // ISO 8601 format date string
}

interface RefreshResponse {
  token: string;
  expires: string | number;
}
const TOKEN_REFRESH_THRESHOLD_MINUTES = 10;

const useTokenRefresher = (
  key: string,
  refreshEndpoint: string,
  params: Record<string, string>,
  callBack?: () => void,
) => {
  const state: {
    callback: (() => void) | undefined;
    intervalId: NodeJS.Timeout | null;
  } = {
    callback: callBack,
    intervalId: null,
  };
  // Function to check and refresh expiring tokens
  const checkAndRefreshTokens = async () => {
    try {
      const now = dayjs();
      const defaultTokenData: TokenData = { token: '', expiresAt: 0 };
      let tokenData: TokenData;
      try {
        const parsedData = JSON.parse(localStorage.getItem(key) || '{}');
        if (
          parsedData &&
          typeof parsedData.token === 'string' &&
          parsedData.expiresAt
        ) {
          tokenData = parsedData;
        } else {
          tokenData = defaultTokenData;
        }
      } catch {
        tokenData = defaultTokenData;
      }
      const expiresAt = dayjs(tokenData.expiresAt);
      const timeUntilExpiry = expiresAt.diff(now, 'minute');

      // If token expires within the threshold or is already expired
      if (timeUntilExpiry <= TOKEN_REFRESH_THRESHOLD_MINUTES) {
        try {
          const urlencoded = new URLSearchParams();
          Object.keys(params).forEach((item) => {
            urlencoded.append(item, params[item]);
          });
          const res = await fetch(refreshEndpoint, {
            method: 'POST',
            body: urlencoded,
          });
          if (!res.ok) {
            throw new Error(`HTTP error! Status: ${res.status}`);
          }
          const response: RefreshResponse = await res.json();

          if (response?.token && response?.expires) {
            // Update the token in localStorage
            tokenData = {
              token: response.token,
              expiresAt:
                typeof response.expires === 'string'
                  ? Date.parse(response.expires)
                  : response.expires,
            };
            localStorage.setItem(key, JSON.stringify(tokenData));
            state.callback?.();
            console.log(`Token ${key} refreshed successfully`);
          }
        } catch (error) {
          console.error(`Failed to refresh token ${key}:`, error);
          // Remove the expired/invalid token from localStorage
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('Error processing tokens:', error);
    }
  };
  checkAndRefreshTokens();
  // Then run every minute to check tokens
  state.intervalId = setInterval(checkAndRefreshTokens, 60000);

  return {
    updateCallback: (newCallback: () => void) => {
      state.callback = newCallback;
    },
    stop: () => {
      if (state.intervalId) {
        clearInterval(state.intervalId);
        state.intervalId = null;
      }
    },
  };
};

export default useTokenRefresher;
