/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useSelector } from 'react-redux';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';
import { CommandTemplateManager } from 'src/components/command-template-editor/command-template-manager';
import { PageWrapper } from 'src/styles/common-style';
import styled from 'styled-components';

const ContentContainer = styled.div`
  min-height: calc(100vh - 120px);
`;

const CommandTemplatePageContainer = () => {
  const globalConfig = useSelector(selectGlobalConfig);
  const { commandTemplateConfig } = globalConfig ?? {};

  return (
    <PageWrapper>
      <ContentContainer>
        <CommandTemplateManager
          config={commandTemplateConfig}
          readonly={false}
        />
      </ContentContainer>
    </PageWrapper>
  );
};

CommandTemplatePageContainer.displayName = 'CommandTemplatePageContainer';

export default CommandTemplatePageContainer;
