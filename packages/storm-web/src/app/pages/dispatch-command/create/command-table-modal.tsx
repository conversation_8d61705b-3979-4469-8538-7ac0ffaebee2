/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import Database from '@waterdesk/data/database';
import { DepartmentList } from '@waterdesk/data/department';
import { CreateCommandList } from '@waterdesk/data/dispatch-command/create-command';
import { UserList } from '@waterdesk/data/system-user';
import { Modal } from 'antd';
import { FC, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import useResponsiveModalSize from 'src/app/hooks/use-responsive-modal-size';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';
import { CommandTable } from 'src/components/dispatch-command/command-table';

export interface CommandTableModalProps {
  open: boolean;
  onClose: () => void;
  dataSource: CreateCommandList[];
  curDb: Database;
  handleCreate: () => void;
  resetAll: () => void;
  getCommandDeviceTitle: (receiveOrganization: string) => string;
  departmentList: DepartmentList;
  userList: UserList;
  loading: boolean;
}

export const CommandTableModal: FC<CommandTableModalProps> = ({
  open,
  onClose,
  dataSource,
  curDb,
  handleCreate,
  resetAll,
  getCommandDeviceTitle,
  departmentList,
  userList,
  loading,
}) => {
  const globalConfig = useSelector(selectGlobalConfig);

  const { modalWidth } = useResponsiveModalSize();

  const template = globalConfig?.commandTemplateConfig;

  const [data, setData] = useState<CreateCommandList[]>(dataSource);

  const handleResetAll = () => {
    setData([]);
    resetAll();
  };

  useEffect(() => {
    setData(dataSource);
  }, [open]);

  return (
    <Modal
      open={open}
      onCancel={onClose}
      width={modalWidth}
      destroyOnHidden
      onOk={handleCreate}
      title="发送调度指令"
      footer={null}
    >
      <CommandTable
        dataSource={data}
        curDb={curDb}
        handleCreate={handleCreate}
        resetAll={handleResetAll}
        setDataSource={setData}
        getCommandDeviceTitle={getCommandDeviceTitle}
        departmentList={departmentList}
        userList={userList}
        isModal
        loading={loading}
        template={template}
      />
    </Modal>
  );
};
