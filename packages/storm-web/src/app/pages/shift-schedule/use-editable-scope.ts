/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import dayjs from 'dayjs';
import { useMemo } from 'react';
import { EditableScheduleScope } from 'src/app/store/user-config/types';

interface UseEditableScopeProps {
  selectedMonth?: string;
  editableScheduleScope?: EditableScheduleScope;
}

interface UseEditableScopeResult {
  /** 当前选中月份是否可编辑 */
  isCurrentMonthEditable: boolean;
  /** 获取指定日期是否可编辑 */
  isDateEditable: (date: string) => boolean;
  /** 获取月份类型 */
  getMonthType: (date: string) => 'past' | 'current' | 'future';
}

/**
 * 处理排班编辑权限的自定义 hook
 */
export const useEditableScope = ({
  selectedMonth,
  editableScheduleScope,
}: UseEditableScopeProps): UseEditableScopeResult => {
  // 获取月份类型
  const getMonthType = (date: string): 'past' | 'current' | 'future' => {
    const isPastMonth = dayjs(date).isBefore(dayjs().startOf('month'), 'month');
    const isCurrentMonth = dayjs(date).isSame(dayjs(), 'month');
    const isFutureMonth = dayjs(date).isAfter(dayjs().endOf('month'), 'month');

    if (isPastMonth) return 'past';
    if (isCurrentMonth) return 'current';
    if (isFutureMonth) return 'future';
    return 'current'; // 默认当前月份
  };

  // 检查指定日期是否可编辑
  const isDateEditable = (date: string): boolean => {
    const monthType = getMonthType(date);

    switch (monthType) {
      case 'past':
        return editableScheduleScope?.pastMonth ?? false;
      case 'current':
        return editableScheduleScope?.currentMonth ?? false;
      case 'future':
        return editableScheduleScope?.futureMonth ?? false;
      default:
        return false;
    }
  };

  // 检查当前选中月份是否可编辑
  const isCurrentMonthEditable = useMemo(() => {
    if (!selectedMonth) return false;
    return isDateEditable(selectedMonth);
  }, [selectedMonth, editableScheduleScope]);

  return {
    isCurrentMonthEditable,
    isDateEditable,
    getMonthType,
  };
};
