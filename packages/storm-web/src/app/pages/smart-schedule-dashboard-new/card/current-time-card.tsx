/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { SecondTimer } from '@waterdesk/core/components/web';
import { Card } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import { useRefresh } from '../contexts/refresh-context';
import { CardText } from '../style';

const CurrentTimeCard: React.FC = () => {
  const {
    minuteRefresh,
    triggerMinuteRefresh,
    resetMinuteRefresh,
    dayRefresh,
    triggerDayRefresh,
    resetDayRefresh,
  } = useRefresh();
  const [latestTime, setLatestTime] = useState<Dayjs>(dayjs());

  const handleSecondTimerFinish = useCallback(() => {
    setLatestTime(dayjs());
    resetMinuteRefresh();
  }, [resetMinuteRefresh]);

  const handleDayChange = useCallback(() => {
    resetDayRefresh();
  }, [resetDayRefresh]);

  useEffect(() => {
    if (minuteRefresh) {
      handleSecondTimerFinish();
    }
  }, [minuteRefresh, handleSecondTimerFinish]);

  useEffect(() => {
    if (dayRefresh) {
      handleDayChange();
    }
  }, [dayRefresh, handleDayChange]);

  return (
    <Card
      size="small"
      title="当前时间"
    >
      <CardText>
        {latestTime.format('HH:mm:')}
        <SecondTimer
          onFinish={triggerMinuteRefresh}
          onDayChange={triggerDayRefresh}
        />
      </CardText>
    </Card>
  );
};

export default CurrentTimeCard;
