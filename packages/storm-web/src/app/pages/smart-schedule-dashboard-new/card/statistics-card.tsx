/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Card, ConfigProvider } from 'antd';
import { EllipsisTextDiv } from 'src/styles/common-style';

interface StatisticsCardProps {
  firstText: string;
  secondText: string;
  style?: React.CSSProperties;
  bodyPadding?: number;
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({
  firstText,
  secondText,
  style,
  bodyPadding = 4,
}) => (
  <ConfigProvider
    theme={{
      components: {
        Card: {
          bodyPaddingSM: bodyPadding,
        },
      },
    }}
  >
    <Card
      size="small"
      style={{ ...style, textAlign: 'center' }}
    >
      <EllipsisTextDiv title={firstText}>{firstText}</EllipsisTextDiv>
      <EllipsisTextDiv title={secondText}>{secondText}</EllipsisTextDiv>
    </Card>
  </ConfigProvider>
);

export default StatisticsCard;
