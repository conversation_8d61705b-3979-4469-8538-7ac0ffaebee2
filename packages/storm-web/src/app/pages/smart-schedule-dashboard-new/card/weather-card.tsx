/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Card } from 'antd';
import React from 'react';
import WeatherContainer from 'src/app/core/containers/weather-container';

const WeatherCard: React.FC = () => (
  <Card
    size="small"
    style={{ height: '94px' }}
  >
    <WeatherContainer style={{ border: 'none' }} />
  </Card>
);

export default WeatherCard;
