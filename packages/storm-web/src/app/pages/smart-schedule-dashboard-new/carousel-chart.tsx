/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { IObjectItem } from '@waterdesk/data/object-item';
import { RunObservationMainItem } from '@waterdesk/data/smart-scheduling-data-new';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { useThemeSlice } from 'src/app/store/theme';
import { selectTheme } from 'src/app/store/theme/selector';
import TimeDataChartNew from 'src/components/charts/time-data-chart/index-new';
import useScadaChartData, {
  AxisObject,
} from '../scada-dashboard/use-scada-chart-data';
import { useRefresh } from './contexts/refresh-context';

const CarouselChart: React.FC<{
  mainObservation: RunObservationMainItem;
}> = ({ mainObservation }) => {
  useThemeSlice();

  const theme = useSelector(selectTheme);

  const { ptype, pname, indicators } = mainObservation;

  const { minuteRefresh } = useRefresh();

  const options = useMemo(() => {
    const device = curDb().getDevice(ptype, pname);

    const axisObjects: AxisObject[] = [
      {
        selectedObject:
          device ??
          ({
            otype: ptype,
            oname: pname,
          } as IObjectItem),
        indicators: indicators.map((m, index) => ({
          otype: m.otype,
          oname: m.oname,
          vprop: m.vprop,
          formData: {
            timeRange: [dayjs(), dayjs()] as [Dayjs, Dayjs],
          },
          direction: index === 0 ? 'left' : 'right',
        })),
      },
    ];

    return {
      axisObjects,
      db: curDb(),
    };
  }, [mainObservation]);

  const { axisData, refresh: refreshChartData } = useScadaChartData(options);

  const darkMode = useMemo(() => theme === 'dark', [theme]);

  useEffect(() => {
    if (minuteRefresh) {
      refreshChartData();
    }
  }, [minuteRefresh, refreshChartData]);

  return (
    <TimeDataChartNew
      startTime={dayjs().format('YYYY-MM-DD 00:00:00')}
      endTime={dayjs().format('YYYY-MM-DD 23:59:59')}
      xAxisType="timely"
      height="220px"
      darkMode={darkMode}
      axisData={axisData}
      schemeType={[]}
      defaultShowAsLineType
      splitNumber={6}
      showToolbox={['myChartMode', 'dataView']}
    />
  );
};

export default CarouselChart;
