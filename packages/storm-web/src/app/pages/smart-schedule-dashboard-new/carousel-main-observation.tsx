/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeId } from '@waterdesk/data/object-item';
import { RunObservationConfigItem } from '@waterdesk/data/smart-scheduling-data-new';
import { Carousel, Flex, Space } from 'antd';
import { useMemo } from 'react';
import CarouselChart from './carousel-chart';
import CarouselObservationScada from './carousel-observation-scada';

interface Props {
  mainObservation: RunObservationConfigItem['mainObservation'];
}

const CarouselMainObservation: React.FC<Props> = ({ mainObservation }) => {
  const groupedObservation = useMemo(() => {
    const result = [];
    for (let i = 0; i < mainObservation.length; i += 2) {
      result.push(mainObservation.slice(i, i + 2));
    }
    return result;
  }, [mainObservation]);

  return (
    <Carousel autoplay autoplaySpeed={8888}>
      {groupedObservation.map((group) => {
        const groupKey = group
          .map((item) => makeId(item.ptype, item.pname))
          .join('-');
        return (
          <Space
            key={groupKey}
            direction="vertical"
            style={{ width: '100%', overflow: 'hidden' }}
          >
            {group.map((item) => (
              <Flex key={makeId(item.ptype, item.pname)}>
                <Flex flex={1} align="center" justify="center">
                  <CarouselObservationScada mainObservation={item} />
                </Flex>
                <Flex flex={2} style={{ overflow: 'hidden' }}>
                  <CarouselChart mainObservation={item} />
                </Flex>
              </Flex>
            ))}
          </Space>
        );
      })}
    </Carousel>
  );
};

export default CarouselMainObservation;
