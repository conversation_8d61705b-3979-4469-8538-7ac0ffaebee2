/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeId } from '@waterdesk/data/object-item';
import { RunObservationMainItem } from '@waterdesk/data/smart-scheduling-data-new';
import { Flex, Typography } from 'antd';
import { useMemo } from 'react';
import { curDb } from 'src/app/host-app';
import CarouselScadaCard from './carousel-scada-card';

const { Text } = Typography;

const CarouselObservationScada: React.FC<{
  mainObservation: RunObservationMainItem;
}> = ({ mainObservation }) => {
  const { ptype, pname, indicators } = mainObservation;

  const mainObservationTitle = useMemo(
    () => curDb().getDevice(ptype, pname)?.title ?? pname,
    [ptype, pname],
  );

  return (
    <Flex
      style={{ width: '100%', overflow: 'hidden' }}
      vertical
      align="center"
      gap={6}
    >
      <Text strong>{mainObservationTitle}</Text>
      <Flex
        gap={8}
        flex="1"
        style={{ width: '100%' }}
        align="center"
        justify="center"
      >
        {indicators?.map(({ otype, oname, vprop }) => (
          <CarouselScadaCard
            key={makeId(otype, oname, vprop)}
            otype={otype}
            oname={oname}
            vprop={vprop}
          />
        ))}
      </Flex>
    </Flex>
  );
};

export default CarouselObservationScada;
