/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { makeId } from '@waterdesk/data/object-item';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { curDb } from 'src/app/host-app';
import StatisticsCard from './card/statistics-card';
import { useRefresh } from './contexts/refresh-context';
import usePropValue from './hooks/use-prop-value';

const CarouselScadaCard: React.FC<{
  otype: string;
  oname: string;
  vprop: string;
}> = ({ otype, oname, vprop }) => {
  const { minuteRefresh } = useRefresh();

  const propertyTitle = useMemo(
    () => curDb().getPropertyInfo(otype)?.title ?? otype,
    [otype],
  );

  const unitFormat = useMemo(
    () => curDb().getUnitFormat(otype, vprop),
    [otype, vprop],
  );

  const propsInfo = useMemo(
    () => [{ otype, oname, vprop }],
    [otype, oname, vprop],
  );

  const { data: propValues, refresh: refreshPropValueData } = usePropValue({
    props: propsInfo,
  });

  const originValue = useMemo(
    () => propValues?.get(makeId(otype, oname, vprop)),
    [propValues],
  );

  const text = useMemo(
    () =>
      `当前${propertyTitle}${unitFormat?.getValueWithSymbol(originValue?.value ?? '')}`,
    [propertyTitle, unitFormat, originValue],
  );

  useEffect(() => {
    if (minuteRefresh) {
      refreshPropValueData();
    }
  }, [minuteRefresh, refreshPropValueData]);

  return (
    <StatisticsCard
      style={{ minWidth: '120px', width: '50%' }}
      firstText={text}
      secondText={
        originValue?.otime ? dayjs(originValue.otime).format('HH:mm') : '--:--'
      }
    />
  );
};

export default CarouselScadaCard;
