/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import React, {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';

interface RefreshContextType {
  // 按分钟变化触发刷新
  minuteRefresh: boolean;
  triggerMinuteRefresh: () => void;
  resetMinuteRefresh: () => void;

  // 按天变化触发刷新
  dayRefresh: boolean;
  triggerDayRefresh: () => void;
  resetDayRefresh: () => void;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

export const useRefresh = () => {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
};

export const RefreshProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [minuteRefresh, setMinuteRefresh] = useState(false);
  const [dayRefresh, setDayRefresh] = useState(false);

  const triggerMinuteRefresh = useCallback(() => {
    setMinuteRefresh(true);
  }, []);

  const resetMinuteRefresh = useCallback(() => {
    setMinuteRefresh(false);
  }, []);

  const triggerDayRefresh = useCallback(() => {
    setDayRefresh(true);
  }, []);

  const resetDayRefresh = useCallback(() => {
    setDayRefresh(false);
  }, []);

  const contextValue = useMemo(
    () => ({
      minuteRefresh,
      triggerMinuteRefresh,
      resetMinuteRefresh,
      dayRefresh,
      triggerDayRefresh,
      resetDayRefresh,
    }),
    [
      minuteRefresh,
      triggerMinuteRefresh,
      resetMinuteRefresh,
      dayRefresh,
      triggerDayRefresh,
      resetDayRefresh,
    ],
  );

  return (
    <RefreshContext.Provider value={contextValue}>
      {children}
    </RefreshContext.Provider>
  );
};
