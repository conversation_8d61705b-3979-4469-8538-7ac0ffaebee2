/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { getCommandContentRenderText } from '@waterdesk/data/dispatch-command/command-template';
import { getDispatchCommandList } from '@waterdesk/request/get-dispatch-command';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { curDb } from 'src/app/host-app';
import { useBaseSlice } from 'src/app/store/base';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';

export const useCommandData = () => {
  useBaseSlice();

  const globalConfig = useSelector(selectGlobalConfig);

  const template = globalConfig?.commandTemplateConfig;

  const { data, refresh } = useRequest(() =>
    getDispatchCommandList({
      current: 1,
      pageSize: 10,
    }),
  );

  const convertList = useMemo(
    () =>
      data?.list?.map((m) => ({
        ...m,
        contentRender: getCommandContentRenderText(m, curDb(), template),
      })),
    [data, template],
  );

  return {
    list: convertList ?? [],
    refresh,
  };
};
