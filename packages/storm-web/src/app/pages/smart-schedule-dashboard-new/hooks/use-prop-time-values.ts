/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { GroupTimeData, ValueGroupParams } from '@waterdesk/data/time-data';
import { getGroupObjectsTimeValues } from '@waterdesk/request/get-group-time-values';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';

export interface PropInfo {
  otype: string;
  oname: string;
  vprop: string;
}

const usePropTimeValues = (options: { props: PropInfo[] }) => {
  const { props } = options;

  const fetchScadaValueData = async (
    props: PropInfo[],
  ): Promise<Map<string, GroupTimeData>> => {
    const valueGroupParams: ValueGroupParams = {};
    props.forEach((param) => {
      const id = param.vprop;
      valueGroupParams[id] = {
        otype: param.otype,
        oname: param.oname,
        vprop: param.vprop,
      };
    });

    const res = await getGroupObjectsTimeValues(
      dayjs().format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
      '',
      '',
      valueGroupParams,
    );
    if (res.status === 'Success' && res.values) {
      return res.values;
    }
    return new Map();
  };

  const { data, loading, refresh } = useRequest(
    () => fetchScadaValueData(props),
    {
      refreshDeps: [props],
    },
  );

  return {
    data,
    loading,
    refresh,
  };
};

export default usePropTimeValues;
