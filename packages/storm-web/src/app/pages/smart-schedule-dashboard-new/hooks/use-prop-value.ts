/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PropertyValue } from '@waterdesk/data/device';
import { makeId } from '@waterdesk/data/object-item';
import { ValueGroupParams } from '@waterdesk/data/time-data';
import { getGroupPropValues } from '@waterdesk/request/get-group-prop-values';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';

export interface PropInfo {
  // 自定义key
  id?: string;
  otype: string;
  oname: string;
  vprop: string;
}

const usePropValue = (options: { props: PropInfo[] }) => {
  const { props } = options;

  const fetchScadaValueData = async (
    props: PropInfo[],
  ): Promise<Map<string, PropertyValue>> => {
    const valueGroupParams: ValueGroupParams = {};
    props.forEach((param) => {
      const id = param.id ?? makeId(param.otype, param.oname, param.vprop);
      valueGroupParams[id] = {
        otype: param.otype,
        oname: param.oname,
        vprop: param.vprop,
      };
    });
    const res = await getGroupPropValues(
      '',
      '',
      valueGroupParams,
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
    );

    if (res.status === 'Success' && res.values) {
      return res.values;
    }
    return new Map();
  };

  const {
    data: scadaValues,
    loading,
    refresh,
  } = useRequest(() => fetchScadaValueData(props), {
    refreshDeps: [props],
  });

  return {
    data: scadaValues,
    loading,
    refresh,
  };
};

export default usePropValue;
