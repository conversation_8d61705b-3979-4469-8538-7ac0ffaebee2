/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getPredictionTimeRange,
  getRealTimeRange,
} from '@waterdesk/data/mini-dashboard/scheduling-dashboard-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { getAssignObjectTimeValuesByTime } from '@waterdesk/request/get-group-time-values';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { curDb } from 'src/app/host-app';

const useWaterPredictionData = ({
  otype,
  oname,
  vprop,
  predictionVprop,
}: {
  otype: string;
  oname: string;
  vprop: string;
  predictionVprop: string;
}) => {
  const getRealTimeData = async () => {
    const timeRange = getRealTimeRange(
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
      dayjs(),
    );
    if (timeRange) {
      const res = await getAssignObjectTimeValuesByTime(
        otype,
        oname,
        vprop,
        timeRange[0],
        timeRange[1],
      );
      return res.values?.get(vprop)?.timeData ?? [];
    }
    return [];
  };
  const getPredictionData = async () => {
    const timeRange = getPredictionTimeRange(
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
      dayjs(),
    );
    if (timeRange) {
      const res = await getAssignObjectTimeValuesByTime(
        otype,
        oname,
        predictionVprop,
        timeRange[0],
        timeRange[1],
      );
      return res.values?.get(predictionVprop)?.timeData ?? [];
    }
    return [];
  };

  const { data, refresh, loading } = useRequest(
    () => Promise.all([getRealTimeData(), getPredictionData()]),
    {
      refreshDeps: [otype, oname, vprop, predictionVprop],
      ready: !!(otype && oname && vprop && predictionVprop),
    },
  );

  const unitFormat: UnitFormat | undefined = curDb().getUnitFormat(
    otype,
    vprop,
  );
  const propertyName =
    curDb().getPropertyTitleUnit(otype, vprop)[0] ?? 'TM_FLOW';

  return {
    predictData: data?.[1] ?? [],
    realTimeData: data?.[0] ?? [],
    loading,
    refresh,
    unitFormat,
    propertyName,
  };
};

export default useWaterPredictionData;
