/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { totalWaterSupply } from '@waterdesk/data/mini-dashboard/running-state-data';
import { makeId } from '@waterdesk/data/object-item';
import { getUnit } from '@waterdesk/data/scada-dashboard-data';
import { Options } from '@waterdesk/data/types';
import { Flex, Radio, Space } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import WaterPredictionChart from 'src/app/core/scheduling-dashboard-container/water-prediction-chart';
import { curDb } from 'src/app/host-app';
import { useUserConfigSlice } from 'src/app/store/user-config';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import StatisticsCard from './card/statistics-card';
import CarouselMainObservation from './carousel-main-observation';
import { useRefresh } from './contexts/refresh-context';
import usePropTimeValues from './hooks/use-prop-time-values';
import usePropValue from './hooks/use-prop-value';
import useWaterPredictionData from './hooks/use-water-prediction-data';
import { Divider6 } from './style';

const RunObservationDashboard: React.FC = () => {
  useUserConfigSlice();

  const { minuteRefresh, dayRefresh } = useRefresh();
  const [activeConfigKey, setActiveConfigKey] = useState<string>();
  const globalConfig = useSelector(selectGlobalConfig);
  const runObservationConfig =
    globalConfig?.smartSchedulingConfig?.runObservationConfig ?? [];

  const radioOptions: Options<string, string> = useMemo(
    () =>
      runObservationConfig.map((item) => ({
        value: makeId(item.otype, item.oname, item.vprop),
        label: item.title,
      })),
    [runObservationConfig],
  );

  const selectedConfig = useMemo(
    () =>
      runObservationConfig.find(
        (item) =>
          makeId(item.otype, item.oname, item.vprop) === activeConfigKey,
      ),
    [activeConfigKey, runObservationConfig],
  );

  const {
    otype = '',
    oname = '',
    vprop = '',
    predictionVprop = '',
    predictTotalFlowVprop = '',
    mainObservation = [],
  } = selectedConfig ?? {};

  const {
    realTimeData,
    predictData,
    unitFormat,
    propertyName,
    refresh: refreshWaterPredictionData,
  } = useWaterPredictionData({
    otype,
    oname,
    vprop,
    predictionVprop,
  });

  const tmFlowPropInfo = useMemo(
    () => [
      {
        otype,
        oname,
        vprop,
      },
      {
        otype,
        oname,
        vprop: predictionVprop,
      },
    ],
    [otype, oname, vprop, predictionVprop],
  );

  const totalFlowPropInfo = useMemo(
    () => [
      {
        otype,
        oname,
        vprop,
      },
      {
        otype,
        oname,
        vprop: predictTotalFlowVprop,
      },
    ],
    [otype, oname, predictTotalFlowVprop],
  );

  const { data: tmFlowData, refresh: refreshTMFlow } = usePropValue({
    props: tmFlowPropInfo,
  });
  const { data: totalTimeValuesData, refresh: refreshTotalTimeValues } =
    usePropTimeValues({
      props: totalFlowPropInfo,
    });

  const tmFlowUnit = useMemo(() => getUnit(curDb(), vprop), [vprop]);
  const totalFlowUnit = useMemo(
    () => getUnit(curDb(), predictTotalFlowVprop),
    [predictTotalFlowVprop],
  );

  const totalFlowValue = useMemo(() => {
    if (totalTimeValuesData) {
      const data = totalTimeValuesData?.get(vprop)?.timeData ?? [];
      if (data.length) {
        return totalWaterSupply(data, data[0].time, data[data.length - 1].time);
      }
    }
    return 0;
  }, [totalTimeValuesData]);

  useEffect(() => {
    if (radioOptions.length > 0) {
      setActiveConfigKey(radioOptions[0].value);
    }
  }, [radioOptions]);

  useEffect(() => {
    if (minuteRefresh) {
      refreshWaterPredictionData();
      refreshTMFlow();
    }
  }, [minuteRefresh, refreshWaterPredictionData, refreshTMFlow]);

  useEffect(() => {
    if (dayRefresh) {
      refreshTotalTimeValues();
    }
  }, [dayRefresh, refreshTotalTimeValues]);

  if (!selectedConfig) return null;

  return (
    <Flex vertical>
      <SpecificPrefixH2 style={{ height: '30px' }}>
        <div>运行与监测</div>
        {radioOptions.length > 1 && (
          <Radio.Group
            buttonStyle="solid"
            value={activeConfigKey}
            size="small"
            onChange={(e) => setActiveConfigKey(e.target.value)}
          >
            {radioOptions.map((item) => (
              <Radio.Button
                type="primary"
                key={item.value}
                value={item.value}
              >
                {item.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        )}
      </SpecificPrefixH2>
      <Flex>
        <Space
          direction="vertical"
          style={{ width: '80%', flex: 1, justifyContent: 'space-around' }}
        >
          <StatisticsCard
            firstText="当前供水流量"
            secondText={
              tmFlowUnit?.getValueWithSymbol(
                tmFlowData?.get(makeId(otype, oname, vprop))?.value ?? 0,
              ) ?? '--'
            }
          />
          <StatisticsCard
            firstText="下一时刻供水流量预测"
            secondText={
              tmFlowUnit?.getValueWithSymbol(
                tmFlowData?.get(makeId(otype, oname, predictionVprop))?.value ??
                  0,
              ) ?? '--'
            }
          />
          <StatisticsCard
            firstText="当日累计供水量"
            secondText={
              totalFlowUnit?.getValueWithSymbol(totalFlowValue ?? 0) ?? '--'
            }
          />
          <StatisticsCard
            firstText="当日预测供水量"
            secondText={
              totalFlowUnit?.getValueWithSymbol(
                totalTimeValuesData?.get(predictTotalFlowVprop)?.timeData[0]
                  ?.value ?? 0,
              ) ?? '--'
            }
          />
        </Space>
        <Flex flex={2}>
          <WaterPredictionChart
            style={{ width: '100%', height: '250px' }}
            dateTime={dayjs().format('YYYY-MM-DD HH:mm:ss')}
            data={realTimeData}
            predictionData={predictData}
            unitFormat={unitFormat}
            propertyName={propertyName}
          />
        </Flex>
      </Flex>
      <Divider6 />
      <CarouselMainObservation mainObservation={mainObservation} />
    </Flex>
  );
};

export default RunObservationDashboard;
