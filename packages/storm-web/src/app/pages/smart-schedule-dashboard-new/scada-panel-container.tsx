/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { BarChartOutlined } from '@ant-design/icons';
import { SCADA_SUMMARY_OBJECT } from '@waterdesk/data/const/system-const';
import Device, { PropertyValue } from '@waterdesk/data/device';
import { IObjectItem, makeId, makeObjectId } from '@waterdesk/data/object-item';
import { formatPumpData, PumpTimeData } from '@waterdesk/data/pump-status';
import {
  ScadaTableData,
  TableData,
  TableDataValueInfo,
} from '@waterdesk/data/scada-dashboard-data';
import { GroupTimeData, TimeData } from '@waterdesk/data/time-data';
import { WarnDetail } from '@waterdesk/data/warn';
import { getGroupPropValues } from '@waterdesk/request/get-group-prop-values';
import {
  getAllGroupObjectsTimeValues,
  getGroupTimeValues,
} from '@waterdesk/request/get-group-time-values';
import { useRequest } from 'ahooks';
import { Button, Card, Flex, Radio, Space, Table, TableProps } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { curDb, hostApp } from 'src/app/host-app';
import { useThemeSlice } from 'src/app/store/theme';
import { selectTheme } from 'src/app/store/theme/selector';
import { useUserConfigSlice } from 'src/app/store/user-config';
import { selectGlobalConfig } from 'src/app/store/user-config/selector';
import PumpStatusChart from 'src/components/charts/pump-status-chart';
import TimeDataChartNew from 'src/components/charts/time-data-chart/index-new';
import RenderCell from 'src/components/scada-dashboard/render-cell';
import RenderPumpCell from 'src/components/scada-dashboard/render-pump-cell';
import useScadaChartData from '../scada-dashboard/use-scada-chart-data';
import useScadaDashboardWarn from '../scada-dashboard/use-scada-dashboard-warn';
import ChartContainer from '../schedule-dashboard/chart-container';
import { useRefresh } from './contexts/refresh-context';

const getPumpTimeData = (
  pumpStatusData: Map<string, GroupTimeData>,
  id: string,
  scadaData: ScadaTableData,
  activeGroup: string,
): PumpTimeData[] => {
  const items: PumpTimeData[] = [];
  const pumpList = scadaData.pumpGroupMap.get(activeGroup)?.pumps.get(id) ?? [];
  pumpList.forEach((pump) => {
    pump.indicators.forEach((pumpIndicator) => {
      const key = pumpIndicator.oname;
      const timeData = pumpStatusData.get(key);
      let data: TimeData[] = timeData ? timeData.timeData : [];
      if (pump.variable && timeData) {
        data = timeData.timeData.map((item) => {
          const valueData = formatPumpData(
            item.value,
            pump.minFrequency,
            pump.maxFrequency,
          );
          return {
            time: item.time,
            value: valueData,
          };
        });
      }
      if (timeData) {
        items.push({
          name: pump.title,
          variable: pump.variable,
          timeData: data,
        });
      }
    });
  });
  return items;
};

const ScadaPanelContainer: React.FC = () => {
  const { minuteRefresh } = useRefresh();

  useUserConfigSlice();
  useThemeSlice();

  const theme = useSelector(selectTheme);
  const globalConfig = useSelector(selectGlobalConfig);
  const { scadaDashboard } = globalConfig ?? {};

  const dataSourceRef = useRef<Map<string, TableData[]>>(new Map());

  const [activeGroup, setActiveGroup] = useState<string>('');
  const [activeDeviceId, setActiveDeviceId] = useState<string | undefined>(
    undefined,
  );
  const [scadaData, setScadaData] = useState<ScadaTableData | undefined>(
    undefined,
  );
  const [pumpDeviceId, setPumpDeviceId] = useState<string | undefined>(
    undefined,
  );
  const [selectedTableCell, setSelectedTableCell] = useState<
    | {
        deviceOType: string;
        deviceOName: string;
        otype: string;
        oname: string;
        vprop: string;
        timeRange?: [Dayjs, Dayjs];
      }
    | undefined
  >();

  const darkMode = useMemo(() => theme === 'dark', [theme]);

  const groupListConfig = useMemo(
    () => scadaDashboard?.group ?? [],
    [scadaDashboard],
  );

  const activeDevices = useMemo(() => {
    const scadaDevices = [
      ...(scadaData?.groupMap.get(activeGroup)?.devices.values() ?? []),
    ].sort((a, b) => (a.index ?? 0) - (b.index ?? 0));
    const devices: Array<Device> = [];

    scadaDevices.forEach(({ otype, oname }) => {
      const device = curDb().getDevice(otype, oname);
      if (device) devices.push(device);
    });
    return devices;
  }, [activeGroup, scadaData]);

  const { indicators: indicatorsChart = [], pump: pumpChart } =
    scadaData?.groupMap.get(activeGroup)?.devices.get(activeDeviceId ?? '')
      ?.chart ?? {
      indicators: [
        {
          otype: 'SDVAL_FLOW_W',
          index: 1,
        },
        {
          otype: 'SDVAL_PRESS_W',
          index: 2,
        },
      ],
      pump: {
        show: true,
      },
    };

  const sortedIndicatorsChart = [...indicatorsChart]?.sort(
    (a, b) => a.index - b.index,
  );

  const fetchPumpStatusData = async (
    id: string | undefined,
  ): Promise<Map<string, GroupTimeData>> => {
    if (id && scadaData) {
      const pumpList =
        scadaData.pumpGroupMap.get(activeGroup)?.pumps.get(id) ?? [];
      const valueGroup: {
        [key: string]: {
          otype: string;
          oname: string;
          vprop: string;
        };
      } = {};
      pumpList.forEach((pump) => {
        if (pump.onOffIndicator) {
          valueGroup[pump.oname] = {
            otype: pump.onOffIndicator.otype,
            oname: pump.onOffIndicator.oname,
            vprop: 'SDVAL',
          };
        }
      });
      const values: Map<string, GroupTimeData>[] = [];
      if (Object.keys(valueGroup).length > 0) {
        const res = await getAllGroupObjectsTimeValues(
          dayjs().format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
          hostApp().getMapViews(),
          valueGroup,
        );
        res.forEach((result) => {
          if (result.status === 'Success' && result.values) {
            values.push(result.values);
          }
        });
      }
      return values[0];
    }
    return new Map();
  };

  const {
    data: activeDevicePumpStatusData,
    refresh: refreshActiveDevicePumpStatusData,
  } = useRequest(() => fetchPumpStatusData(activeDeviceId), {
    refreshDeps: [activeDeviceId],
  });

  const fetchScadaValue = async (valueGroupParams: {}): Promise<
    Map<string, PropertyValue>
  > => {
    const res = await getGroupPropValues(
      '',
      '',
      valueGroupParams,
      dayjs().format('YYYY-MM-DD HH:mm:ss'),
    );
    if (res.values && res.status === 'Success') {
      return res.values;
    }
    return new Map();
  };

  const {
    data: scadaDataValue,
    run,
    refresh: refreshScadaDataValue,
  } = useRequest(fetchScadaValue, {
    manual: true,
  });

  const pumpTimeData = useMemo(() => {
    if (activeDevicePumpStatusData && activeDeviceId && scadaData)
      return getPumpTimeData(
        activeDevicePumpStatusData,
        activeDeviceId,
        scadaData,
        activeGroup,
      );
    return [];
  }, [activeDevicePumpStatusData, activeDeviceId, scadaData, activeGroup]);

  const { data: activePumpStatusData, refresh: refreshActivePumpStatusData } =
    useRequest(() => fetchPumpStatusData(pumpDeviceId), {
      refreshDeps: [pumpDeviceId],
    });

  const pumpTimeData1 = useMemo(() => {
    if (activePumpStatusData && pumpDeviceId && scadaData)
      return getPumpTimeData(
        activePumpStatusData,
        pumpDeviceId,
        scadaData,
        activeGroup,
      );
    return [];
  }, [activePumpStatusData, pumpDeviceId, scadaData, activeGroup]);

  const { pumpStateColor } = hostApp().appConfig;

  const handleClickTableCell = (
    otype: string,
    oname: string,
    vprop: string,
    deviceOType: string,
    deviceOName: string,
  ) => {
    setSelectedTableCell({
      deviceOName,
      deviceOType,
      otype,
      oname,
      vprop,
    });
  };

  const getDataSource = (
    activeGroup: string,
    scadaData: ScadaTableData,
  ): TableData[] => {
    const data: TableData[] = [];
    const groupInfo = scadaData.groupMap.get(activeGroup);
    const indicatorMap =
      scadaData.indicatorGroupMap.get(activeGroup)?.indicators;
    const devicesMap = groupInfo?.devices;
    const groupColumn = groupInfo?.columns;
    const deviceArr = (devicesMap ? [...devicesMap.values()] : []).sort(
      (a, b) => (a.index ?? 0) - (b.index ?? 0),
    );
    deviceArr.forEach((device) => {
      const { title, otype, oname, indicators } = device;
      const id = makeId(otype, oname);
      const dataItem: TableData = {
        key: id,
        title,
        pumpStatus: {
          deviceOType: device.otype,
          deviceOName: device.oname,
          oname: '',
          otype: '',
          dataIndex: 'pumpStatus',
        },
      };
      if (groupColumn) {
        groupColumn.forEach((column) => {
          const foundIndicator = indicators.find(
            (indicator) => indicator.dataIndex === column.dataIndex,
          );
          if (foundIndicator) {
            dataItem[column.dataIndex] = {
              ...foundIndicator,
              deviceOName: device.oname,
              deviceOType: device.otype,
              children: foundIndicator.children?.map((item) => ({
                ...item,
                title:
                  item.title ??
                  indicatorMap?.get(makeObjectId(item.otype, item.oname))
                    ?.title,
              })),
            };
          }
        });
      }
      data.push(dataItem);
    });
    return data;
  };

  const dataSource = useMemo(() => {
    const data = dataSourceRef.current.get(activeGroup);
    if (data?.length) return data;
    if (!scadaData) return [];
    const d = getDataSource(activeGroup, scadaData);
    dataSourceRef.current.set(activeGroup, d);
    return d;
  }, [activeGroup, scadaData]);

  const indicatorMap = useMemo(() => {
    const newMap = new Map<string, string>();
    const indicatorMapArr = Array.from(
      scadaData?.indicatorGroupMap.values() ?? [],
    ).map((m) => m.indicators);
    indicatorMapArr.forEach((map) => {
      map.forEach((_, k) => {
        newMap.set(k, k);
      });
    });
    return newMap;
  }, [scadaData?.indicatorGroupMap]);

  const warnMap = useScadaDashboardWarn(Array.from(indicatorMap.keys()));

  const columns = useMemo(() => {
    const list: TableProps<TableData>['columns'] = [
      {
        title: '名称',
        dataIndex: 'title',
        render: (text) => <span style={{ fontWeight: 'bold' }}>{text}</span>,
      },
    ];
    const groupColumns = [
      ...(scadaData?.groupMap.get(activeGroup)?.columns?.values() ?? []),
    ].sort((a, b) => (a.index ?? 0) - (b.index ?? 0));

    if (groupColumns.length) {
      groupColumns.forEach((item) => {
        list.push({
          title: item.title,
          dataIndex: item.dataIndex,
          render: (data: TableDataValueInfo | undefined) => {
            let warnIds: string[] | undefined;
            if (data) {
              warnIds =
                data.children && data.children.length > 0
                  ? data.children.map((c) => makeId(c.otype, c.oname))
                  : [makeId(data.otype, data.oname)];
            }
            const warnList = (
              warnIds
                ?.map((id) => warnMap.get(id))
                .filter((f) => f && f.length > 0)
                .flat() as WarnDetail[] | undefined
            )?.map((m) => m.description);
            return (
              <RenderCell
                db={curDb()}
                data={data}
                warnList={warnList}
                valueInfo={scadaDataValue}
                handleClick={handleClickTableCell}
              />
            );
          },
        });
      });
    }
    list.push({
      title: '水泵运行',
      dataIndex: 'pumpStatus',
      width: 620,
      render: (data) => {
        const pumpList =
          scadaData?.pumpGroupMap
            .get(activeGroup)
            ?.pumps.get(makeObjectId(data.deviceOType, data.deviceOName)) ?? [];
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <RenderPumpCell
              style={{ width: '570px' }}
              valueInfo={scadaDataValue}
              pumpList={pumpList}
              pumpStateColor={pumpStateColor}
            />
            <Button
              style={{ width: '30px' }}
              type="link"
              size="small"
              title="泵站运行状态"
              onClick={() =>
                setPumpDeviceId(
                  makeObjectId(data.deviceOType, data.deviceOName),
                )
              }
            >
              <BarChartOutlined />
            </Button>
          </div>
        );
      },
    });
    return list;
  }, [
    scadaData?.groupMap,
    scadaData?.pumpGroupMap,
    activeGroup,
    scadaDataValue,
    warnMap,
    pumpStateColor,
  ]);

  const handleClickDefault = () => {
    setSelectedTableCell({
      deviceOType: SCADA_SUMMARY_OBJECT.otype,
      deviceOName: SCADA_SUMMARY_OBJECT.oname,
      otype: SCADA_SUMMARY_OBJECT.otype,
      oname: SCADA_SUMMARY_OBJECT.oname,
      vprop: 'CUMULATIVE_FLOW',
      timeRange: [dayjs().add(-6, 'd'), dayjs()],
    });
  };

  const customChartTitle = useMemo(() => {
    let title = '';
    if (pumpDeviceId) {
      const device = curDb().getDeviceById(pumpDeviceId);
      title = `${device?.title ?? ''} 泵站运行状态`;
    } else if (selectedTableCell) {
      const { otype, oname, vprop } = selectedTableCell;
      title =
        curDb().getIndicator(otype, oname)?.title ??
        curDb().getPropertyTitleUnit(otype, vprop)?.[0] ??
        oname;
    }
    return (
      <span>
        {title}
        <Button
          style={{ marginLeft: '20px' }}
          type="link"
          size="small"
          onClick={handleClickDefault}
        >
          查看默认
        </Button>
      </span>
    );
  }, [selectedTableCell, pumpDeviceId]);

  const getObjectTimeValues = async (
    object: IObjectItem,
    indicatorOType: string | undefined,
    indicatorOName: string | undefined,
    vprop: string,
    startDate: string,
    endDate: string,
    forecast?: boolean,
  ): Promise<Map<string, GroupTimeData>> => {
    const res = await getGroupTimeValues(
      undefined,
      object,
      indicatorOType,
      indicatorOName,
      vprop,
      startDate,
      endDate,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      'day',
      forecast,
    );

    if (res.status === 'Success' && res.values) {
      return res.values;
    }
    return new Map();
  };

  const customTimeRange = useMemo(
    (): [Dayjs, Dayjs] => selectedTableCell?.timeRange ?? [dayjs(), dayjs()],
    [selectedTableCell],
  );

  const customOptions = useMemo(() => {
    if (selectedTableCell) {
      const { deviceOName, deviceOType, oname, otype, vprop } =
        selectedTableCell;
      const device = curDb().getDevice(deviceOType, deviceOName);
      if (deviceOName && deviceOType) {
        return {
          axisObjects: [
            {
              selectedObject:
                device ??
                ({
                  otype: deviceOType,
                  oname: deviceOName,
                } as IObjectItem),
              indicators: [
                {
                  otype,
                  oname,
                  vprop,
                  formData: {
                    timeRange: customTimeRange,
                  },
                },
              ],
            },
          ],
          db: curDb(),
          getObjectTimeValues,
        };
      }
    }
    return { axisObjects: [], db: curDb(), getObjectTimeValues };
  }, [selectedTableCell, customTimeRange]);

  const {
    axisData: customAxisData,
    loading: customLoading,
    refresh: refreshCustomAxisData,
  } = useScadaChartData(customOptions);

  useEffect(() => {
    if (groupListConfig.length) {
      const scadaData = new ScadaTableData();
      scadaData.initialConfiguration(groupListConfig, curDb());
      setScadaData(scadaData);
      setActiveGroup(groupListConfig[0]?.groupId);
    }
  }, [groupListConfig]);

  useEffect(() => {
    if (scadaData && activeGroup) {
      const scadaTableParams = scadaData.getParamsValue(activeGroup);
      const param = { ...(scadaTableParams ?? {}) };
      run(param);
    }
  }, [activeGroup, scadaData, run]);

  // 处理按分钟刷新逻辑
  useEffect(() => {
    if (minuteRefresh) {
      refreshScadaDataValue();
      refreshActiveDevicePumpStatusData();
      refreshActivePumpStatusData();
      refreshCustomAxisData();
    }
  }, [
    minuteRefresh,
    refreshScadaDataValue,
    refreshActiveDevicePumpStatusData,
    refreshActivePumpStatusData,
    refreshCustomAxisData,
  ]);

  useEffect(() => {
    setActiveDeviceId(activeDevices[0]?.id ?? '');
    handleClickDefault();
  }, [activeDevices]);

  useEffect(() => {
    setPumpDeviceId(undefined);
  }, [selectedTableCell]);

  return (
    <Space
      direction="vertical"
      style={{ width: '100%' }}
    >
      <Flex justify="space-between">
        <Radio.Group
          buttonStyle="solid"
          value={activeGroup}
          onChange={(e) => setActiveGroup(e.target.value)}
        >
          {groupListConfig.map((item) => (
            <Radio.Button
              type="primary"
              key={item.groupId}
              value={item.groupId}
            >
              {item.groupName}
            </Radio.Button>
          ))}
        </Radio.Group>

        <Radio.Group
          buttonStyle="solid"
          value={activeDeviceId}
          onChange={(e) => setActiveDeviceId(e.target.value)}
        >
          {activeDevices.map((item) => (
            <Radio.Button
              type="primary"
              key={item.id}
              value={item.id}
            >
              {item.title}
            </Radio.Button>
          ))}
        </Radio.Group>
      </Flex>
      <ChartContainer
        finishStatus={false}
        activeDeviceId={activeDeviceId}
        indicators={sortedIndicatorsChart}
      />

      {pumpChart?.show ? (
        <Card
          size="small"
          title={pumpChart.title ?? '泵运行状态'}
        >
          <PumpStatusChart
            pumpTimeData={pumpTimeData}
            pumpStateColor={pumpStateColor}
            startDate={dayjs().format('YYYY-MM-DD')}
            days={1}
            minHeight="140px"
          />
        </Card>
      ) : null}

      <Table
        size="small"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
      <Card
        size="small"
        title={customChartTitle}
      >
        {pumpDeviceId ? (
          <PumpStatusChart
            pumpTimeData={pumpTimeData1}
            pumpStateColor={pumpStateColor}
            startDate={dayjs().format('YYYY-MM-DD')}
            days={1}
            minHeight="240px"
          />
        ) : (
          <TimeDataChartNew
            loading={customLoading}
            startTime={customTimeRange[0].format('YYYY-MM-DD 00:00:00')}
            endTime={customTimeRange[1].format('YYYY-MM-DD 23:59:59')}
            xAxisType="timely"
            height="240px"
            darkMode={darkMode}
            axisData={customAxisData}
            schemeType={[]}
            defaultShowAsLineType
            splitNumber={6}
          />
        )}
      </Card>
    </Space>
  );
};

export default ScadaPanelContainer;
