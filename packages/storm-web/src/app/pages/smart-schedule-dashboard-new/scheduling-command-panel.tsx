/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DispatchCommandList,
  DispatchCommandState,
  dispatchCommandStateColorMap,
  dispatchCommandStateNameMap,
  getCommandSourceTypeText,
  getTimelyReplyText,
} from '@waterdesk/data/dispatch-command/command-list';
import { Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';
import LinkSmallButton from 'src/components/common/link-button';
import DispatchCommandProgressTimeline from 'src/components/dispatch-command/dispatch-command-items';
import { SpecificPrefixH2 } from 'src/styles/common-style';
import useReceiveCommandModal from '../dispatch-command/list/use-receive-command';

interface SchedulingCommandPanelProps {
  data: DispatchCommandList[];
  refresh: () => void;
}

const SchedulingCommandPanel: React.FC<SchedulingCommandPanelProps> = ({
  data,
  refresh,
}) => {
  const { contextHolder: contentHolder, handleOpen } = useReceiveCommandModal({
    refresh,
  });

  const columns: ColumnsType<DispatchCommandList> = [
    {
      title: '指令内容',
      dataIndex: 'contentRender',
      key: 'contentRender',
      ellipsis: true,
    },
    {
      title: '指令来源',
      dataIndex: 'sourceType',
      key: 'sourceType',
      width: 100,
      render: (value) => getCommandSourceTypeText(value),
    },
    {
      title: '指令状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (text: DispatchCommandState) => (
        <Tag
          key={text}
          color={dispatchCommandStateColorMap[text]}
        >
          {dispatchCommandStateNameMap[text] ?? text}
        </Tag>
      ),
    },
    {
      title: '回复及时性',
      dataIndex: 'timelyReply',
      key: 'timelyReply',
      width: 100,
      render: (value) => getTimelyReplyText(value),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <LinkSmallButton
          size="small"
          type="link"
          onClick={() => handleOpen(record)}
        >
          详情
        </LinkSmallButton>
      ),
    },
  ];

  return (
    <Space direction="vertical">
      <SpecificPrefixH2>指令跟踪</SpecificPrefixH2>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={data}
        size="small"
        pagination={false}
        expandable={{
          expandedRowRender: DispatchCommandProgressTimeline,
        }}
      />
      {contentHolder}
    </Space>
  );
};

export default SchedulingCommandPanel;
