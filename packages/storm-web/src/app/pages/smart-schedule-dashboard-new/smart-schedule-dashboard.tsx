/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Col, Flex, Radio, Row, Space, Splitter } from 'antd';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { PageWrapper } from 'src/styles/common-style';
import CurrentTimeCard from './card/current-time-card';
import WeatherCard from './card/weather-card';
import { RefreshProvider, useRefresh } from './contexts/refresh-context';
import { useCommandData } from './hooks/use-command-data';
import RunObservationDashboard from './run-observation-dashboard';
import ScadaPanelContainer from './scada-panel-container';
import SchedulingCommandPanel from './scheduling-command-panel';
import StationScheduleInfoContainer from './station-schedule-info-container';
import StrategyList from './strategy-list';
import { Divider6 } from './style';

const DashboardContent: React.FC = () => {
  const { minuteRefresh } = useRefresh();
  const [activePanelKey, setActivePanelKey] = useState<'strategy' | 'command'>(
    'strategy',
  );

  const { list: commandList, refresh: refreshCommandList } = useCommandData();

  useEffect(() => {
    if (minuteRefresh) {
      refreshCommandList();
    }
  }, [minuteRefresh]);

  return (
    <Space
      direction="vertical"
      style={{ width: '100%', marginBottom: '15px' }}
    >
      <Flex
        gap={20}
        align="center"
      >
        <Row
          gutter={20}
          style={{ width: '100%' }}
        >
          {/* 当前时间卡片 */}
          <Col
            xs={24}
            sm={12}
            md={6}
          >
            <CurrentTimeCard />
          </Col>
        </Row>
        {/* 天气卡片 */}
        <WeatherCard />
      </Flex>
      <Radio.Group
        buttonStyle="solid"
        value={activePanelKey}
        onChange={(e) => setActivePanelKey(e.target.value)}
      >
        <Radio.Button
          type="primary"
          value="strategy"
        >
          智能策略
        </Radio.Button>
        <Radio.Button
          type="primary"
          value="command"
        >
          指令监测
        </Radio.Button>
      </Radio.Group>
      {activePanelKey === 'strategy' && <StationScheduleInfoContainer />}
      {activePanelKey === 'command' && (
        <>
          <Splitter style={{ boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
            <Splitter.Panel
              style={{ padding: '6px' }}
              defaultSize="50%"
              min="30%"
              max="60%"
            >
              <StrategyList />
              <Divider6 />
              <SchedulingCommandPanel
                data={commandList}
                refresh={refreshCommandList}
              />
            </Splitter.Panel>
            <Splitter.Panel style={{ padding: '6px' }}>
              <RunObservationDashboard />
            </Splitter.Panel>
          </Splitter>
          <ScadaPanelContainer />
        </>
      )}
    </Space>
  );
};

const SmartScheduleDashboard: React.FC = () => (
  <>
    <Helmet>
      <title>调度看板</title>
    </Helmet>
    <PageWrapper>
      <RefreshProvider>
        <DashboardContent />
      </RefreshProvider>
    </PageWrapper>
  </>
);

export default SmartScheduleDashboard;
