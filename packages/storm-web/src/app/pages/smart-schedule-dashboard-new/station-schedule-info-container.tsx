/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { useToken } from '@waterdesk/core/theme';
import { WDM_VALVES } from '@waterdesk/data/const/system-const';
import Database from '@waterdesk/data/database';
import { PropertyValue, StationDevice } from '@waterdesk/data/device';
import { makeId } from '@waterdesk/data/object-item';
import {
  getScheduleStragyData,
  parseScheduleSimulationOperation,
  ScadaDataSourceItem,
  ScheduleSimulationItem,
  StrategyItem,
} from '@waterdesk/data/smart-scheduling-data-new';
import { formatNumber } from '@waterdesk/data/utils';
import { Flex, Space, Timeline } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useScheduleWebSocketNew } from 'src/app/hooks/use-web-socket';
import { curDb, hostApp } from 'src/app/host-app';
import { useUserConfigSlice } from 'src/app/store/user-config';
import { ScheduleInfoCard } from 'src/components/smart-schedule';
import { useRefresh } from './contexts/refresh-context';
import usePropValue, { PropInfo } from './hooks/use-prop-value';

const getStationScadaDataSource = (
  db: Database,
  station: StationDevice,
  data: Map<string, PropertyValue> | undefined,
): ScadaDataSourceItem[] => {
  const scadaDataSource: ScadaDataSourceItem[] = [];
  const { otype, oname, indicators } = station;

  const press = indicators.find((f) => f.otype === 'SDVAL_PRESS_W');
  const flow = indicators.find((f) => f.otype === 'SDVAL_FLOW_W');

  if (press) {
    const pressData = data?.get(makeId(otype, oname, press.otype, press.oname));
    const unit = db.getUnitFormat(press.otype, 'SDVAL');
    if (pressData) {
      scadaDataSource.push({
        otype: press.otype,
        oname: press.oname,
        title: press.title,
        valueWithSymbol:
          unit?.getValueWithSymbol(pressData.value ?? 0) ??
          formatNumber(pressData.value, 2).toString(),
        otime: pressData.otime ?? '',
      });
    }
  }

  if (flow) {
    const flowData = data?.get(makeId(otype, oname, flow.otype, flow.oname));
    const unit = db.getUnitFormat(flow.otype, 'SDVAL');
    if (flowData) {
      scadaDataSource.push({
        otype: flow.otype,
        oname: flow.oname,
        title: flow.title,
        valueWithSymbol:
          unit?.getValueWithSymbol(flowData.value ?? 0) ??
          formatNumber(flowData.value, 2).toString(),
        otime: flowData.otime ?? '',
      });
    }
  }

  return scadaDataSource;
};

const StationScheduleInfoContainer = () => {
  useUserConfigSlice();
  const { minuteRefresh } = useRefresh();

  const { token } = useToken();
  const pumpStateColor = useMemo(() => hostApp().appConfig.pumpStateColor, []);

  const plants = useMemo(() => curDb().getAllPlants(), []);

  const [strategyData, setStrategyData] = useState<{
    stragiesMap: Map<string, StrategyItem[]>;
    allTimes: string[];
  }>({
    stragiesMap: new Map(),
    allTimes: [],
  });
  const [currentTime, setCurrentTime] = useState<string>('');

  const propValueParams = useMemo(() => {
    const data: PropInfo[] = [];
    plants.forEach((plant) => {
      // 使用find, 只看一个
      const press = plant.indicators.find((f) => f.otype === 'SDVAL_PRESS_W');
      const flow = plant.indicators.find((f) => f.otype === 'SDVAL_FLOW_W');
      if (press)
        data.push({
          otype: press.otype,
          oname: press.oname,
          vprop: 'SDVAL',
          id: makeId(plant.otype, plant.oname, press.otype, press.oname),
        });
      if (flow)
        data.push({
          otype: flow.otype,
          oname: flow.oname,
          vprop: 'SDVAL',
          id: makeId(plant.otype, plant.oname, flow.otype, flow.oname),
        });
      plant.pumpList.forEach((pump) => {
        if (pump.onOffIndicator) {
          data.push({
            otype: pump.onOffIndicator.otype,
            oname: pump.onOffIndicator.oname,
            vprop: 'SDVAL',
          });
        }
      });
    });
    return data;
  }, [plants]);

  const { data } = usePropValue({
    props: propValueParams,
  });

  const { connect: connectScheduleNew, disconnect: disconnectScheduleNew } =
    useScheduleWebSocketNew((msg) => {
      try {
        const data = msg?.strategies?.map(
          (item: any): ScheduleSimulationItem => ({
            otime: dayjs(item.otime).format('YYYY-MM-DD HH:mm:ss'),
            otype: item.otype,
            oname: item.oname,
            modelId: item.modelId,
            operations: parseScheduleSimulationOperation(
              item.otype,
              item.oname,
              item.operations,
            ),
            forecastFlow: item.forecastFlow,
            taskId: item.taskId,
            stime: item.stime,
          }),
        );
        const strategyData = getScheduleStragyData(data, curDb());
        setStrategyData(strategyData);
      } catch {
        setStrategyData({
          stragiesMap: new Map(),
          allTimes: [],
        });
      }
    });

  const timelineItems = useMemo(() => {
    return strategyData.allTimes.map((time: string) => {
      const isActive = time === currentTime;

      return {
        color: isActive ? token.colorPrimary : '#666',
        children: (
          <span
            style={{
              cursor: 'pointer',
              padding: '2px 4px',
              borderRadius: '4px',
              backgroundColor: isActive ? '#e6f7ff' : 'transparent',
              border: isActive ? '1px solid #1890ff' : '1px solid transparent',
            }}
            onClick={() => setCurrentTime(time)}
          >
            {dayjs(time).format('HH:mm')}
          </span>
        ),
      };
    });
  }, [currentTime, setStrategyData, token]);

  useEffect(() => {
    connectScheduleNew();

    return () => {
      disconnectScheduleNew();
    };
  }, []);

  useEffect(() => {
    if (strategyData.allTimes.length > 0) {
      const now = dayjs();

      // 找到距离当前时刻最近的且处于未来的时刻
      const futureTimesList = strategyData.allTimes.filter(
        (time) => dayjs(time).isAfter(now) || dayjs(time).isSame(now),
      );

      if (futureTimesList.length > 0) {
        // 在未来时刻中找到最近的一个
        const nearestFutureTime = futureTimesList.reduce((nearest, current) => {
          const nearestDiff = Math.abs(dayjs(nearest).diff(now));
          const currentDiff = Math.abs(dayjs(current).diff(now));
          return currentDiff < nearestDiff ? current : nearest;
        });
        setCurrentTime(nearestFutureTime);
      } else {
        // 如果没有未来时刻，选择最后一个时刻
        setCurrentTime(strategyData.allTimes[strategyData.allTimes.length - 1]);
      }
    }
  }, [strategyData.allTimes, minuteRefresh]);

  return (
    <Flex>
      <Timeline
        style={{
          width: '120px',
          alignItems: 'center',
          padding: '20px 2px',
          overflow: 'auto',
          scrollbarWidth: 'none',
          alignSelf: 'center',
        }}
        mode="left"
        items={timelineItems}
      />
      <Space
        style={{
          width: '100%',
          overflowX: 'auto',
          scrollbarWidth: 'none',
          justifyContent: 'space-around',
        }}
      >
        {plants.map((plant) => {
          const scadaDataSource = getStationScadaDataSource(
            curDb(),
            plant,
            data,
          );
          const strategies = strategyData.stragiesMap.get(
            makeId(plant.otype, plant.oname),
          );

          return (
            <ScheduleInfoCard
              key={plant.id}
              style={{
                width: 280,
              }}
              scadaDataSource={scadaDataSource}
              title={plant.shortTitle ?? plant.oname}
              pumpDataSource={{
                pumpList: plant.pumpList,
                valueInfo: data ?? new Map(),
                pumpStateColor,
              }}
              activeTime={currentTime}
              strategies={strategies ?? []}
            />
          );
        })}
        {/* 阀门 */}
        <ScheduleInfoCard
          style={{
            width: 280,
          }}
          title="阀门操作"
          pumpDataSource={{
            pumpList: [],
            valueInfo: new Map(),
            pumpStateColor,
          }}
          activeTime={currentTime}
          strategies={strategyData.stragiesMap.get(WDM_VALVES) ?? []}
        />
      </Space>
    </Flex>
  );
};

export default StationScheduleInfoContainer;
