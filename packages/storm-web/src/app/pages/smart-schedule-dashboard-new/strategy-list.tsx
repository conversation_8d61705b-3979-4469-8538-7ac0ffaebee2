/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { StrategyItemChangeValue } from '@waterdesk/data/smart-scheduling-data-new';
import { List, Switch } from 'antd';
import { useState } from 'react';
import { SpecificPrefixH2 } from 'src/styles/common-style';

const StrategyList = () => {
  const [enablePush, setEnablePush] = useState(true);

  return (
    <>
      <SpecificPrefixH2 style={{ height: '30px' }}>
        <span>最新智能策略</span>
        <>
          <Switch
            size="small"
            title={enablePush ? '关闭调度推送' : '打开调度推送'}
            checked={enablePush}
            onChange={(checked) => {
              setEnablePush(checked);
            }}
          />
        </>
      </SpecificPrefixH2>
      <List<StrategyItemChangeValue>
        size="small"
        style={{ height: '250px', overflow: 'auto' }}
        dataSource={[]}
        renderItem={(item) => (
          <List.Item key={item.contentText}>{item.contentText}</List.Item>
        )}
      />
    </>
  );
};

export default StrategyList;
