/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Divider } from 'antd';
import { EllipsisText } from 'src/styles/common-style';
import styled, { css } from 'styled-components';

export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const CardTextSize = css`
  font-size: 1.2rem;
`;

export const CardText = styled.div`
  font-weight: bold;
  ${CardTextSize}
  ${EllipsisText}
`;

export const Divider6 = styled(Divider)`
  margin: 6px 0;
`;
