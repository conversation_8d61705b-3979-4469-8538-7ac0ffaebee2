/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { MAP_VIEW_NAME_ONLINE } from '@waterdesk/data/const/map';
import {
  updateCurrentDeviceColorData,
  updateCurrentLegendData,
} from '@waterdesk/data/legend-data';
import { getDateTimeFromValue } from '@waterdesk/data/time-data';
import { requestApi } from '@waterdesk/request/request';
import { setViewThemeTime } from '@waterdesk/request/set-view-theme-time';
import dayjs from 'dayjs';
import {
  call,
  put,
  SagaReturnType,
  select,
  takeLatest,
} from 'redux-saga/effects';
import { hostApp } from 'src/app/host-app';
import { timelineActions } from '.';
import { legendActions } from '../legend';
import { selectTimelineDate, selectTimelineTime } from './selectors';

// 处理时间轴日期变化的业务逻辑
function* handleTimelineDateChangeSaga(action: any) {
  const { timelineDate } = action.payload;
  const timelineTime: ReturnType<typeof selectTimelineTime> =
    yield select(selectTimelineTime);

  // 更新地图视图
  const mapViews = hostApp().getMapViews();
  mapViews?.forEach((mapView) => {
    if (mapView.mapViewName === MAP_VIEW_NAME_ONLINE) {
      mapView.setTimelineDate(dayjs(timelineDate));
    }
  });

  // 更新请求 API 时间
  requestApi.time = getDateTimeFromValue(timelineTime, timelineDate).format(
    'YYYY-MM-DD HH:mm:ss',
  );
}

// 处理时间轴时间变化的业务逻辑
function* handleTimelineTimeChangeSaga(action: any) {
  const { timelineTime } = action.payload;
  const timelineDate: ReturnType<typeof selectTimelineDate> =
    yield select(selectTimelineDate);

  // 更新地图视图
  const mapViews = hostApp().getMapViews();
  mapViews?.forEach((mapView) => {
    mapView.setTimelineDateTime(timelineTime);
  });

  // 更新请求 API 时间
  requestApi.time = getDateTimeFromValue(timelineTime, timelineDate).format(
    'YYYY-MM-DD HH:mm:ss',
  );
}

// 处理临时时间变化的业务逻辑
function* handleTempTimeChangeSaga(action: any) {
  const { tempTime } = action.payload;

  // 更新地图视图
  const mapViews = hostApp().getMapViews();
  mapViews?.forEach((mapView) => {
    mapView.setTempTime(tempTime);
  });
}

function* setViewThemeTimeSaga(): SagaGenerator<void> {
  try {
    const mapViews = hostApp().getMapViews();
    if (!mapViews) return;

    const timelineDate: ReturnType<typeof selectTimelineDate> =
      yield select(selectTimelineDate);
    const timelineTime: ReturnType<typeof selectTimelineTime> =
      yield select(selectTimelineTime);
    const time: string = dayjs(timelineDate)
      .set('minute', timelineTime)
      .format('YYYY-MM-DD HH:mm:ss');
    const fcExt = dayjs().isBefore(time, 'minute') ? '_FC1' : undefined;

    for (let i = 0; mapViews.length > i; i += 1) {
      const mapView = mapViews[i];
      let currentTime;
      if (mapView) {
        currentTime = mapView.dateTime?.format('YYYY-MM-DD HH:mm:ss');
      }
      const result: SagaReturnType<typeof setViewThemeTime> = yield call(
        setViewThemeTime,
        {
          time: currentTime || time,
          fc_ext: fcExt,
          view_id: mapView.getViewId(),
        },
      );
      if (result.status === 'Success') {
        if (result.deviceColorData) {
          updateCurrentDeviceColorData(result.deviceColorData);
        }
        mapView.redraw();
        if (result.legendData) {
          updateCurrentLegendData(result.legendData);
          yield put(
            legendActions.legendChanged({
              code: dayjs().valueOf(),
            }),
          );
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

export function* timelineSaga() {
  yield takeLatest(timelineActions.setViewThemeTime, setViewThemeTimeSaga);
  yield takeLatest(
    timelineActions.updateTimelineDate,
    handleTimelineDateChangeSaga,
  );
  yield takeLatest(
    timelineActions.updateTimelineTime,
    handleTimelineTimeChangeSaga,
  );
  yield takeLatest(timelineActions.updateTempTime, handleTempTimeChangeSaga);
}
