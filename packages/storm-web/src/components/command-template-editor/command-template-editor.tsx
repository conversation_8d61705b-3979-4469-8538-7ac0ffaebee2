/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeleteOutlined,
  EditOutlined,
  HolderOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  DndContext,
  DragEndEvent,
  MeasuringStrategy,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  CommandTemplateConfig,
  getSystemVariableOptions,
  renderCommandTemplate,
  SystemVariableType,
  TemplateElement,
  TemplateElementType,
  TemplateRenderData,
  validateTemplate,
} from '@waterdesk/data/dispatch-command/command-template';
import {
  Al<PERSON>,
  Button,
  Card,
  Col,
  Form,
  Input,
  Modal,
  message,
  Popconfirm,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import React, { useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';

const { Title, Text } = Typography;
const { TextArea } = Input;

const EditorContainer = styled.div`
  display: flex;
  gap: 16px;
  min-height: 600px;
`;

const CanvasPanel = styled(Card)`
  flex: 1;
  .ant-card-body {
    padding: 16px;
  }
`;

const PreviewPanel = styled(Card)`
  width: 350px;
  .ant-card-body {
    padding: 16px;
  }
`;

const ElementTypeTag = styled.span<{ type: TemplateElementType }>`
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background: ${(props) => {
    switch (props.type) {
      case TemplateElementType.TEXT:
        return '#52c41a';
      case TemplateElementType.VARIABLE:
        return '#1890ff';
      case TemplateElementType.DESCRIPTION:
        return '#fa8c16';
      default:
        return '#8c8c8c';
    }
  }};
`;

const PreviewText = styled.div`
  padding: 12px;
  border: 1px solid ${(props) => props.theme.colorBorder};
  border-radius: 6px;
  background: ${(props) => props.theme.colorFillSecondary};
  font-family: monospace;
  white-space: pre-wrap;
  line-height: 1.6;
`;

const SortableElementItem = styled.div<{ isDragging?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid ${(props) => props.theme.colorBorder};
  border-radius: 6px;
  background: ${(props) =>
    props.isDragging
      ? props.theme.colorPrimaryBg
      : props.theme.colorBgContainer};
  margin-bottom: 8px;
  transition:
    border-color 0.2s,
    box-shadow 0.2s;
  touch-action: none;
  height: 40px;
  user-select: none;
  position: relative;
  z-index: ${(props) => (props.isDragging ? 1 : 0)};

  &:hover {
    border-color: ${(props) => props.theme.colorPrimary};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const DragHandle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: move;
  color: ${(props) => props.theme.colorTextTertiary};
  margin-right: 8px;

  &:hover {
    color: ${(props) => props.theme.colorPrimary};
  }

  &:active {
    cursor: grabbing;
  }
`;

interface CommandTemplateEditorProps {
  /** 当前模板配置 */
  template: CommandTemplateConfig;
  /** 保存模板回调 */
  onSave?: (template: CommandTemplateConfig) => void;
  /** 取消编辑回调 */
  onCancel?: () => void;
  /** 是否只读模式 */
  readonly?: boolean;
}

interface ElementFormData {
  type: TemplateElementType;
  content: string;
  variableType?: SystemVariableType;
}

// 可排序的元素组件
const SortableElement: React.FC<{
  element: TemplateElement;
  index: number;
  readonly: boolean;
  onEdit: (element: TemplateElement, index: number) => void;
  onDelete: (index: number) => void;
  renderElementType: (type: TemplateElementType) => React.ReactNode;
  renderElementContent: (element: TemplateElement) => string;
}> = ({
  element,
  index,
  readonly,
  onEdit,
  onDelete,
  renderElementType,
  renderElementContent,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: element.id,
    disabled: readonly,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <SortableElementItem
      ref={setNodeRef}
      style={style}
      isDragging={isDragging}
      {...attributes}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        {!readonly && (
          <DragHandle {...listeners}>
            <HolderOutlined />
          </DragHandle>
        )}
        <Text type="secondary">{index + 1}.</Text>
        {renderElementType(element.type)}
        <span>{renderElementContent(element)}</span>
      </div>
      {!readonly && (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(element, index);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除此元素吗？"
            onConfirm={(e) => {
              e?.stopPropagation();
              onDelete(index);
            }}
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                danger
                onClick={(e) => e.stopPropagation()}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )}
    </SortableElementItem>
  );
};

export const CommandTemplateEditor: React.FC<CommandTemplateEditorProps> = ({
  template,
  onSave,
  onCancel,
  readonly = false,
}) => {
  const [currentTemplate, setCurrentTemplate] =
    useState<CommandTemplateConfig>(template);
  const [editingElement, setEditingElement] = useState<{
    element: TemplateElement;
    index: number;
  } | null>(null);
  const [elementModalVisible, setElementModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [form] = Form.useForm<ElementFormData>();

  // 拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
        delay: 100,
        tolerance: 5,
      },
    }),
  );

  // 系统变量选项
  const systemVariableOptions = useMemo(() => getSystemVariableOptions(), []);

  // 模板验证结果
  const validationResult = useMemo(
    () => validateTemplate(currentTemplate),
    [currentTemplate],
  );

  // 预览数据
  const previewData: TemplateRenderData = useMemo(
    () => ({
      deviceTitle: '示例水厂',
      planTime: '2024-01-15 14:30',
      note: '这是一个示例备注',
      descriptionArr: ['水泵P001开启，频率调整为45±2Hz'],
    }),
    [],
  );

  // 渲染预览文本
  const previewText = useMemo(() => {
    try {
      return renderCommandTemplate(currentTemplate, previewData);
    } catch (error) {
      console.error('模板渲染错误:', error);
      return '模板渲染错误：请检查模板配置';
    }
  }, [currentTemplate, previewData]);

  // 排序后的元素列表
  const sortedElements = useMemo(
    () => [...currentTemplate.elements].sort((a, b) => a.order - b.order),
    [currentTemplate.elements],
  );

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) return;

      const oldIndex = sortedElements.findIndex(
        (item) => item.id === active.id,
      );
      const newIndex = sortedElements.findIndex((item) => item.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newElements = arrayMove(sortedElements, oldIndex, newIndex);

        // 重新分配order
        const updatedElements = newElements.map((item, index) => ({
          ...item,
          order: index + 1,
        }));

        setCurrentTemplate((prev) => ({
          ...prev,
          elements: updatedElements,
        }));
      }
    },
    [sortedElements],
  );

  // 添加新元素
  const handleAddElement = useCallback(() => {
    if (readonly) return;
    setEditingElement(null);
    form.resetFields();
    setElementModalVisible(true);
  }, [form, readonly]);

  // 编辑元素
  const handleEditElement = useCallback(
    (element: TemplateElement, index: number) => {
      if (readonly) return;
      setEditingElement({ element, index });
      form.setFieldsValue({
        type: element.type,
        content: element.content,
        variableType: element.variableType,
      });
      setElementModalVisible(true);
    },
    [form, readonly],
  );

  // 删除元素
  const handleDeleteElement = useCallback(
    (index: number) => {
      if (readonly) return;
      const elementToDelete = sortedElements[index];
      const newElements = currentTemplate.elements.filter(
        (el) => el.id !== elementToDelete.id,
      );
      setCurrentTemplate((prev) => ({
        ...prev,
        elements: newElements.map((item, i) => ({
          ...item,
          order: i + 1,
        })),
      }));
    },
    [sortedElements, currentTemplate.elements, readonly],
  );

  // 保存元素
  const handleSaveElement = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const newElement: TemplateElement = {
        id: editingElement?.element.id || `element-${Date.now()}`,
        type: values.type,
        content: values.content,
        variableType: values.variableType,
        order: editingElement?.index ?? currentTemplate.elements.length,
      };

      if (editingElement) {
        // 编辑现有元素
        const newElements = currentTemplate.elements.map((el) =>
          el.id === editingElement.element.id ? newElement : el,
        );
        setCurrentTemplate((prev) => ({
          ...prev,
          elements: newElements,
        }));
      } else {
        // 添加新元素
        setCurrentTemplate((prev) => ({
          ...prev,
          elements: [
            ...prev.elements,
            { ...newElement, order: prev.elements.length + 1 },
          ],
        }));
      }

      setElementModalVisible(false);
      setEditingElement(null);
      form.resetFields();
      message.success(editingElement ? '元素更新成功' : '元素添加成功');
    } catch (error) {
      console.error('保存元素失败:', error);
    }
  }, [form, editingElement, currentTemplate.elements]);

  // 保存模板
  const handleSaveTemplate = useCallback(() => {
    if (!validationResult.isValid) {
      message.error('模板配置有误，请检查后再保存');
      return;
    }

    const updatedTemplate: CommandTemplateConfig = {
      ...currentTemplate,
      updateTime: new Date().toISOString(),
    };

    onSave?.(updatedTemplate);
    message.success('模板保存成功');
  }, [currentTemplate, validationResult.isValid, onSave]);

  // 渲染元素类型标签
  const renderElementType = (type: TemplateElementType) => (
    <ElementTypeTag type={type}>
      {type === TemplateElementType.TEXT && '文字'}
      {type === TemplateElementType.VARIABLE && '变量'}
      {type === TemplateElementType.DESCRIPTION && '描述'}
    </ElementTypeTag>
  );

  // 渲染元素内容
  const renderElementContent = (element: TemplateElement) => {
    switch (element.type) {
      case TemplateElementType.TEXT:
        return element.content || '(空文字)';
      case TemplateElementType.VARIABLE: {
        const variable = systemVariableOptions.find(
          (v) => v.value === element.variableType,
        );
        return `{${variable?.label || '未知变量'}}`;
      }
      case TemplateElementType.DESCRIPTION:
        return '{指令描述}';
      default:
        return '未知元素';
    }
  };

  return (
    <div>
      {/* 模板基本信息 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="模板名称">
              <Input
                value={currentTemplate.name}
                onChange={(e) =>
                  setCurrentTemplate((prev) => ({
                    ...prev,
                    name: e.target.value,
                  }))
                }
                placeholder="模板名称"
                disabled={readonly}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="模板描述">
              <Input
                value={currentTemplate.description}
                onChange={(e) =>
                  setCurrentTemplate((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="模板描述"
                disabled={readonly}
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Space>
              {!readonly && (
                <Button
                  type="primary"
                  onClick={handleSaveTemplate}
                  disabled={!validationResult.isValid}
                >
                  保存
                </Button>
              )}
              {onCancel && <Button onClick={onCancel}>取消</Button>}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 验证错误提示 */}
      {!validationResult.isValid && (
        <Alert
          type="error"
          message="模板配置错误"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {validationResult.errors.map((error) => (
                <li key={`error-${error}`}>{error}</li>
              ))}
            </ul>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 编辑器主体 */}
      <EditorContainer>
        {/* 画布面板 */}
        <CanvasPanel
          title="模板设计"
          extra={
            !readonly && (
              <Button
                type="text"
                icon={<PlusOutlined />}
                size="small"
                onClick={handleAddElement}
              >
                添加
              </Button>
            )
          }
        >
          {sortedElements.length === 0 ? (
            <div
              style={{ textAlign: 'center', color: '#999', padding: '40px' }}
            >
              暂无元素，请添加模板元素
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              modifiers={[restrictToVerticalAxis]}
              onDragEnd={handleDragEnd}
              measuring={{
                droppable: {
                  strategy: MeasuringStrategy.Always,
                },
              }}
            >
              <SortableContext
                items={sortedElements.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                {sortedElements.map((element, index) => (
                  <SortableElement
                    key={element.id}
                    element={element}
                    index={index}
                    readonly={readonly}
                    onEdit={handleEditElement}
                    onDelete={handleDeleteElement}
                    renderElementType={renderElementType}
                    renderElementContent={renderElementContent}
                  />
                ))}
              </SortableContext>
            </DndContext>
          )}
        </CanvasPanel>

        {/* 预览面板 */}
        <PreviewPanel title="实时预览">
          <Space
            direction="vertical"
            style={{ width: '100%' }}
          >
            <Text type="secondary">基于示例数据的渲染效果：</Text>
            <PreviewText>{previewText}</PreviewText>

            <div>
              <Text strong>示例数据：</Text>
              <ul style={{ margin: '8px 0', paddingLeft: 20, fontSize: 12 }}>
                <li>接收站点: {previewData.deviceTitle}</li>
                <li>计划时间: {previewData.planTime}</li>
                <li>备注: {previewData.note}</li>
                <li>指令描述: {previewData.descriptionArr.join('; ')}</li>
              </ul>
            </div>
          </Space>
        </PreviewPanel>
      </EditorContainer>

      {/* 元素编辑弹窗 */}
      <Modal
        title={editingElement ? '编辑元素' : '添加元素'}
        open={elementModalVisible}
        onOk={handleSaveElement}
        onCancel={() => {
          setElementModalVisible(false);
          setEditingElement(null);
          form.resetFields();
        }}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ type: TemplateElementType.TEXT }}
        >
          <Form.Item
            name="type"
            label="元素类型"
            rules={[{ required: true, message: '请选择元素类型' }]}
          >
            <Select
              options={[
                { label: '固定文字', value: TemplateElementType.TEXT },
                { label: '系统变量', value: TemplateElementType.VARIABLE },
                { label: '指令描述', value: TemplateElementType.DESCRIPTION },
              ]}
              onChange={() =>
                form.setFieldsValue({ content: '', variableType: undefined })
              }
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prev, curr) => prev.type !== curr.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');

              if (type === TemplateElementType.TEXT) {
                return (
                  <Form.Item
                    name="content"
                    label="文字内容"
                    rules={[{ required: true, message: '请输入文字内容' }]}
                  >
                    <TextArea
                      placeholder="请输入要显示的文字内容"
                      rows={3}
                    />
                  </Form.Item>
                );
              }

              if (type === TemplateElementType.VARIABLE) {
                return (
                  <Form.Item
                    name="variableType"
                    label="变量类型"
                    rules={[{ required: true, message: '请选择变量类型' }]}
                  >
                    <Select
                      options={systemVariableOptions}
                      placeholder="请选择要插入的变量"
                    />
                  </Form.Item>
                );
              }

              if (type === TemplateElementType.DESCRIPTION) {
                return (
                  <Alert
                    type="info"
                    message="指令描述元素"
                    description="此元素会自动显示根据指令内容生成的描述信息，无需配置具体内容。"
                  />
                );
              }

              return null;
            }}
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览弹窗 */}
      <Modal
        title="模板预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button
            key="close"
            onClick={() => setPreviewModalVisible(false)}
          >
            关闭
          </Button>,
        ]}
        width={600}
      >
        <Space
          direction="vertical"
          style={{ width: '100%' }}
        >
          <Title level={5}>渲染结果：</Title>
          <PreviewText>{previewText}</PreviewText>

          <Title level={5}>模板元素：</Title>
          <div>
            {sortedElements.map((element, index) => (
              <div
                key={element.id}
                style={{ marginBottom: 8 }}
              >
                <Text code>{index + 1}.</Text> {renderElementType(element.type)}{' '}
                {renderElementContent(element)}
              </div>
            ))}
          </div>
        </Space>
      </Modal>
    </div>
  );
};
