/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  CommandTemplateConfig,
  CommandTemplateManagerConfig,
  getDefaultTemplateForDeviceAction,
  validateTemplate,
} from '@waterdesk/data/dispatch-command/command-template';
import {
  DeviceAction,
  deviceActionNameMap,
} from '@waterdesk/data/dispatch-command/create-command';
import {
  Button,
  Card,
  Modal,
  message,
  Popconfirm,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import styled from 'styled-components';
import HSSearchTitle from '../common/table/table-title';
import { CommandTemplateEditor } from './command-template-editor';

const { Text } = Typography;

const ManagerContainer = styled.div`
  padding: 24px;
`;

interface DeviceActionRow {
  action: DeviceAction;
  name: string;
  template?: CommandTemplateConfig;
  hasTemplate: boolean;
  isValid: boolean;
  lastModified?: string;
}

interface CommandTemplateManagerProps {
  /** 模板管理配置 */
  config?: CommandTemplateManagerConfig;
  /** 是否只读模式 */
  readonly?: boolean;
}

export const CommandTemplateManager: React.FC<CommandTemplateManagerProps> = ({
  config = {},
  readonly = false,
}) => {
  const [currentConfig, setCurrentConfig] =
    useState<CommandTemplateManagerConfig>(config);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [editingAction, setEditingAction] = useState<
    DeviceAction | undefined
  >();
  const [editingTemplate, setEditingTemplate] = useState<
    CommandTemplateConfig | undefined
  >();
  const [previewTemplate, setPreviewTemplate] = useState<
    CommandTemplateConfig | undefined
  >();

  const [messageApi, contextHolder] = message.useMessage();

  // 复制配置到剪贴板
  const handleCopyConfig = useCallback(() => {
    messageApi.success(
      '配置已复制，请前往 postgres.app_config_key__project.commandTemplateConfig 粘贴复制内容',
      5,
    );
  }, []);

  const copyText = useMemo(() => {
    const json = JSON.stringify(currentConfig, null, 2);
    return `"commandTemplateConfig": ${json},`;
  }, [currentConfig]);

  // 将指令类型转换为表格数据
  const tableData: DeviceActionRow[] = useMemo(
    () =>
      Object.values(DeviceAction).map((action) => {
        const template = currentConfig[action];
        const validation = template
          ? validateTemplate(template)
          : { isValid: true, errors: [] };

        return {
          action,
          name: deviceActionNameMap[action],
          template,
          hasTemplate: !!template,
          isValid: validation.isValid,
          lastModified: template?.updateTime || template?.createTime,
        };
      }),
    [currentConfig],
  );

  // 处理新增/编辑模板
  const handleEditTemplate = useCallback(
    (action: DeviceAction) => {
      if (readonly) return;

      const existingTemplate = currentConfig[action];
      setEditingTemplate(
        existingTemplate || getDefaultTemplateForDeviceAction(action),
      );
      setEditingAction(action);
      setEditModalVisible(true);
    },
    [currentConfig, readonly],
  );

  // 处理预览模板
  const handlePreviewTemplate = useCallback(
    (action: DeviceAction) => {
      const template =
        currentConfig[action] || getDefaultTemplateForDeviceAction(action);
      setPreviewTemplate(template);
      setPreviewModalVisible(true);
    },
    [currentConfig],
  );

  // 处理删除模板
  const handleDeleteTemplate = useCallback(
    (action: DeviceAction) => {
      if (readonly) return;

      const newConfig = { ...currentConfig };
      delete newConfig[action];

      setCurrentConfig(newConfig);
      message.success(
        `${deviceActionNameMap[action]}模板已删除，将使用默认模板`,
      );
    },
    [currentConfig, readonly],
  );

  // 处理恢复默认模板
  const handleResetToDefault = useCallback(
    (action: DeviceAction) => {
      if (readonly) return;

      const defaultTemplate = getDefaultTemplateForDeviceAction(action);
      const newConfig = {
        ...currentConfig,
        [action]: defaultTemplate,
      };

      setCurrentConfig(newConfig);
      message.success(`${deviceActionNameMap[action]}模板已恢复为默认配置`);
    },
    [currentConfig, readonly],
  );

  // 处理保存模板
  const handleSaveTemplate = useCallback(
    (template: CommandTemplateConfig) => {
      if (!editingAction) return;

      const validation = validateTemplate(template);
      if (!validation.isValid) {
        message.error('模板配置有误，请检查后再保存');
        return;
      }

      const updatedTemplate: CommandTemplateConfig = {
        ...template,
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };

      const newConfig: CommandTemplateManagerConfig = {
        ...currentConfig,
        [editingAction]: updatedTemplate,
      };

      setCurrentConfig(newConfig);
      setEditModalVisible(false);
      setEditingAction(undefined);

      const isNew = !currentConfig[editingAction];
      message.success(
        isNew
          ? `${deviceActionNameMap[editingAction]}模板创建成功`
          : `${deviceActionNameMap[editingAction]}模板更新成功`,
      );
    },
    [editingAction, currentConfig],
  );

  // 表格列定义
  const columns: ColumnsType<DeviceActionRow> = useMemo(
    () => [
      {
        title: '指令类型',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        render: (name: string) => <Text strong>{name}</Text>,
      },
      {
        title: '模板状态',
        key: 'status',
        width: 120,
        align: 'center',
        render: (_, record: DeviceActionRow) => (
          <Tag
            style={{
              background: record.hasTemplate ? '#f6ffed' : '#fff2e8',
              borderColor: record.hasTemplate ? '#b7eb8f' : '#ffbb96',
              color: record.hasTemplate ? '#389e0d' : '#d4380d',
            }}
          >
            {record.hasTemplate ? '已配置' : '使用默认'}
          </Tag>
        ),
      },
      {
        title: '最后修改时间',
        dataIndex: 'lastModified',
        key: 'lastModified',
        width: 160,
        render: (lastModified: string) =>
          lastModified ? dayjs(lastModified).format('YYYY-MM-DD HH:mm') : '-',
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
        render: (_, record: DeviceActionRow) => (
          <Space size="small">
            <Tooltip title="预览">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handlePreviewTemplate(record.action)}
              />
            </Tooltip>
            {!readonly && (
              <>
                <Tooltip title={record.hasTemplate ? '编辑' : '配置'}>
                  <Button
                    type="text"
                    size="small"
                    icon={
                      record.hasTemplate ? <EditOutlined /> : <PlusOutlined />
                    }
                    onClick={() => handleEditTemplate(record.action)}
                  />
                </Tooltip>
                {record.hasTemplate && (
                  <Popconfirm
                    title="确定删除此模板配置吗？"
                    description="删除后将使用系统默认模板"
                    onConfirm={() => handleDeleteTemplate(record.action)}
                  >
                    <Tooltip title="删除">
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        danger
                      />
                    </Tooltip>
                  </Popconfirm>
                )}
              </>
            )}
          </Space>
        ),
      },
    ],
    [
      readonly,
      handlePreviewTemplate,
      handleEditTemplate,
      handleResetToDefault,
      handleDeleteTemplate,
    ],
  );

  return (
    <ManagerContainer>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <HSSearchTitle title="调度指令模板管理" />
        <div style={{ flex: 1 }} />
        <CopyToClipboard
          text={copyText}
          onCopy={handleCopyConfig}
        >
          <Button
            type="primary"
            disabled={readonly}
          >
            复制配置
          </Button>
        </CopyToClipboard>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={tableData}
          rowKey="action"
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 编辑模板弹窗 */}
      <Modal
        title={`配置${editingAction ? deviceActionNameMap[editingAction] : ''}模板`}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingAction(undefined);
        }}
        footer={null}
        width="90%"
        style={{ maxWidth: 1400 }}
        destroyOnHidden
      >
        {editingAction && (
          <CommandTemplateEditor
            template={editingTemplate!}
            onSave={handleSaveTemplate}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingAction(undefined);
            }}
          />
        )}
      </Modal>

      {/* 预览模板弹窗 */}
      <Modal
        title="模板预览"
        open={previewModalVisible}
        onCancel={() => {
          setPreviewModalVisible(false);
          setPreviewTemplate(undefined);
        }}
        footer={[
          <Button
            key="close"
            onClick={() => {
              setPreviewModalVisible(false);
              setPreviewTemplate(undefined);
            }}
          >
            关闭
          </Button>,
        ]}
        width="90%"
        style={{ maxWidth: 1400 }}
        destroyOnHidden
      >
        {previewTemplate && (
          <CommandTemplateEditor
            template={previewTemplate}
            readonly
          />
        )}
      </Modal>
      {contextHolder}
    </ManagerContainer>
  );
};
