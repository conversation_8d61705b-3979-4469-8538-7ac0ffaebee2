# ShelveTimeInput 搁置时间输入组件

一个自定义的搁置时间输入组件，支持数值输入、单位选择和快捷选项。

## 功能特性

- 支持数值输入框和单位选择器
- 自动单位转换（分钟、小时、天）
- 快捷选项 Tag（首次聚焦时显示）
- 完全兼容 Antd Form
- 类型安全

## 使用方法

### 在表单中使用

```tsx
import { Form } from 'antd';
import ShelveTimeInput from '@/components/common/shelve-time-input';

const MyForm = () => {
  const [form] = Form.useForm();

  return (
    <Form form={form}>
      <Form.Item
        name="shelveTime"
        label="搁置时间"
        rules={[{ required: true, message: '搁置时间不能为空' }]}
      >
        <ShelveTimeInput
          placeholder="请输入搁置时间"
          showQuickOptions
          showQuickOptionsOnFocus
        />
      </Form.Item>
    </Form>
  );
};
```

### 独立使用

```tsx
import ShelveTimeInput from '@/components/common/shelve-time-input';

const MyComponent = () => {
  const [value, setValue] = useState<number | undefined>(undefined);

  return (
    <ShelveTimeInput
      value={value}
      onChange={setValue}
      placeholder="请输入搁置时间"
      showQuickOptions={true}
      showQuickOptionsOnFocus={true}
    />
  );
};
```

### 不同的快捷选项显示方式

```tsx
// 始终显示快捷选项
<ShelveTimeInput showQuickOptions={true} showQuickOptionsOnFocus={false} />

// 只在首次聚焦时显示快捷选项
<ShelveTimeInput showQuickOptions={false} showQuickOptionsOnFocus={true} />

// 始终显示快捷选项，且首次聚焦时也显示
<ShelveTimeInput showQuickOptions={true} showQuickOptionsOnFocus={true} />

// 不显示快捷选项
<ShelveTimeInput showQuickOptions={false} showQuickOptionsOnFocus={false} />
```

### 快捷选项显示逻辑说明

快捷选项的显示遵循以下规则：

```tsx
// 显示条件：(showQuickOptions || shelveTimeInputFocused) && !disabled

// 情况1：showQuickOptions=true, showQuickOptionsOnFocus=false
// 结果：始终显示快捷选项

// 情况2：showQuickOptions=false, showQuickOptionsOnFocus=true
// 结果：首次聚焦时显示快捷选项，之后一直保持显示

// 情况3：showQuickOptions=true, showQuickOptionsOnFocus=true
// 结果：始终显示快捷选项（因为 showQuickOptions=true）

// 情况4：showQuickOptions=false, showQuickOptionsOnFocus=false
// 结果：不显示快捷选项
```

**注意**：当 `showQuickOptionsOnFocus=true` 时，快捷选项会在输入框首次聚焦时显示，之后会一直保持显示状态，不会因为失焦而隐藏。

## API

### Props

| 参数                    | 说明                       | 类型                                   | 默认值     |
| ----------------------- | -------------------------- | -------------------------------------- | ---------- |
| value                   | 当前值（分钟）             | `number \| undefined`                  | -          |
| onChange                | 值变化时的回调             | `(value: number \| undefined) => void` | -          |
| placeholder             | 输入框占位符               | `string`                               | `'请输入'` |
| style                   | 自定义样式                 | `React.CSSProperties`                  | -          |
| disabled                | 是否禁用                   | `boolean`                              | `false`    |
| showQuickOptions        | 是否显示快捷选项           | `boolean`                              | `false`    |
| showQuickOptionsOnFocus | 首次聚焦时是否显示快捷选项 | `boolean`                              | `false`    |

### Ref 方法

| 方法  | 说明       | 类型         |
| ----- | ---------- | ------------ |
| focus | 聚焦输入框 | `() => void` |
| blur  | 失焦输入框 | `() => void` |

## 注意事项

1. 组件内部会自动处理单位转换，最终返回给表单的值始终为分钟
2. 快捷选项显示规则：`showQuickOptions || shelveTimeInputFocused`
   - `showQuickOptions`: 始终显示快捷选项
   - `showQuickOptionsOnFocus`: 首次聚焦时显示快捷选项
   - 两个条件满足任一即可显示快捷选项
3. 两个属性可以独立使用，也可以组合使用
4. 支持初始值自动解析，会根据分钟数自动选择合适的单位
