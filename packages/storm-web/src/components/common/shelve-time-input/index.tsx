/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getShelveTimeUnitName,
  minuteToShelveTime,
  ShelveTimeUnit,
  shelveTimeOptions,
  shelveTimeToMinute,
  shelveTimeUnitOptions,
} from '@waterdesk/data/warn';
import { InputNumber, InputRef, Select, Tag } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';

export interface ShelveTimeInputProps {
  value?: number;
  onChange?: (value: number | undefined) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  showQuickOptions?: boolean;
  showQuickOptionsOnFocus?: boolean;
}

export interface ShelveTimeInputRef {
  focus: () => void;
  blur: () => void;
}

const ShelveTimeInput = forwardRef<ShelveTimeInputRef, ShelveTimeInputProps>(
  (
    {
      value,
      onChange,
      placeholder = '请输入',
      style,
      disabled = false,
      showQuickOptions = false,
      showQuickOptionsOnFocus = false,
    },
    ref,
  ) => {
    const [shelveTimeValue, setShelveTimeValue] = useState<number | undefined>(
      undefined,
    );
    const [shelveTimeUnit, setShelveTimeUnit] = useState<ShelveTimeUnit>(
      ShelveTimeUnit.DAY,
    );
    const [shelveTimeInputFocused, setShelveTimeInputFocused] =
      useState<boolean>(false);
    const inputRef = React.useRef<InputRef['input']>(null);

    // 暴露 focus 和 blur 方法
    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
    }));

    // 初始化值
    useEffect(() => {
      if (value !== undefined) {
        const parsed = minuteToShelveTime(value);
        setShelveTimeValue(
          typeof parsed.value === 'number' ? parsed.value : undefined,
        );
        setShelveTimeUnit(parsed.unit);
      } else {
        setShelveTimeValue(undefined);
        setShelveTimeUnit(ShelveTimeUnit.DAY);
      }
      setShelveTimeInputFocused(false);
    }, [value]);

    const handleValueChange = (val: number | null) => {
      const newValue = typeof val === 'number' ? val : undefined;
      setShelveTimeValue(newValue);
      if (newValue !== undefined) {
        const minute = shelveTimeToMinute(newValue, shelveTimeUnit);
        onChange?.(minute);
      } else {
        onChange?.(undefined);
      }
    };

    const handleUnitChange = (unit: ShelveTimeUnit) => {
      setShelveTimeUnit(unit);
      if (shelveTimeValue !== undefined) {
        const minute = shelveTimeToMinute(shelveTimeValue, unit);
        onChange?.(minute);
      }
    };

    const handleTagClick = (opt: { label: string; value: number }) => {
      const parsed = minuteToShelveTime(opt.value);
      const newValue =
        typeof parsed.value === 'number' ? parsed.value : undefined;
      setShelveTimeValue(newValue);
      setShelveTimeUnit(parsed.unit);
      if (newValue !== undefined) {
        const minute = shelveTimeToMinute(newValue, parsed.unit);
        onChange?.(minute);
      } else {
        onChange?.(undefined);
      }
    };

    return (
      <div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InputNumber
            ref={inputRef}
            min={1}
            value={shelveTimeValue}
            onChange={handleValueChange}
            onFocus={() =>
              showQuickOptionsOnFocus && setShelveTimeInputFocused(true)
            }
            style={{ width: 120, ...style }}
            placeholder={placeholder}
            disabled={disabled}
          />
          <Select
            style={{ width: 80, marginLeft: 8 }}
            value={shelveTimeUnit}
            onChange={handleUnitChange}
            options={shelveTimeUnitOptions}
            disabled={disabled}
          />
        </div>

        {/* 快捷选项 tag - 根据配置显示 */}
        {(showQuickOptions || shelveTimeInputFocused) && !disabled && (
          <div style={{ marginTop: '8px' }}>
            {shelveTimeOptions.map((opt) => {
              const parsed = minuteToShelveTime(opt.value);
              const label = `${parsed.value}${getShelveTimeUnitName(parsed.unit)}`;
              return (
                <Tag
                  key={opt.value}
                  color="blue"
                  style={{
                    cursor: 'pointer',
                    marginRight: 8,
                    marginBottom: 4,
                  }}
                  onClick={() => handleTagClick(opt)}
                >
                  {label}
                </Tag>
              );
            })}
          </div>
        )}
      </div>
    );
  },
);

ShelveTimeInput.displayName = 'ShelveTimeInput';

export default ShelveTimeInput;
