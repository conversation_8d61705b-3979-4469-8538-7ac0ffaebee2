/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { DispatchCommandList } from '@waterdesk/data/dispatch-command/command-list';
import { ValveOperationValue } from '@waterdesk/data/dispatch-command/create-command';
import { EventRelatedType } from '@waterdesk/data/event-scheduling/related-info';
import { PlanProjectList } from '@waterdesk/data/plan-project';
import { ChartProperties } from '@waterdesk/data/property/property-info';
import { SolutionListItem } from '@waterdesk/data/solution';
import { ValveOperationGroup } from '@waterdesk/data/valve-manager/valve-manager-data';
import { WarnInfoItem } from '@waterdesk/data/warn';
import { WorkOrder } from '@waterdesk/data/work-order';
import { Descriptions, Divider } from 'antd';
import { FC, useMemo } from 'react';

export type SchedulingSourceDetail =
  | WarnInfoItem
  | DispatchCommandList
  | PlanProjectList
  | ValveOperationValue
  | ValveOperationGroup
  | ChartProperties
  | WorkOrder
  | SolutionListItem
  | undefined;

export type SourceConfig = {
  [key in EventRelatedType]: {
    label: string;
    name: keyof SchedulingSourceDetail;
    span?: number;
  }[];
};

export interface EventSchedulingSourceDetailProps {
  eventSourceType: EventRelatedType | undefined;
  sourceDetailInfo: SchedulingSourceDetail;
  sourceConfig: SourceConfig | undefined;
}

export const EventSchedulingSourceDetail: FC<
  EventSchedulingSourceDetailProps
> = ({ eventSourceType, sourceDetailInfo, sourceConfig }) => {
  const sourceDetailContent = useMemo(() => {
    if (eventSourceType && sourceConfig) {
      return sourceConfig[eventSourceType];
    }
    return undefined;
  }, [eventSourceType, sourceConfig]);

  const getSourceDetail = useMemo(() => {
    if (sourceDetailContent && sourceDetailInfo) {
      return sourceDetailContent.map((item) => {
        const data = sourceDetailInfo[item.name];
        return {
          key: item.name,
          label: item.label,
          span: item.span ?? 2,
          children: data,
        };
      });
    }
    return [];
  }, [sourceDetailInfo, sourceDetailContent]);
  return getSourceDetail.length > 0 ? (
    <>
      <Divider orientation="left">关键信息</Divider>
      <Descriptions
        size="small"
        items={getSourceDetail}
      />
    </>
  ) : null;
};
