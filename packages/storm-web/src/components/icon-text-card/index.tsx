/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Button } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { IconTextCardWrap } from './style';

// 定义类型
type IconTextCardMode = 'text' | 'button' | 'select';

interface Option {
  value: string | number;
  icon?: React.ReactNode;
  title: string;
  description: string;
}

interface BaseProps {
  mode?: IconTextCardMode;
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

interface TextProps extends BaseProps {
  mode?: 'text';
}

interface ButtonProps extends BaseProps {
  mode: 'button';
  onClick: () => void;
}

interface SelectProps extends BaseProps {
  mode: 'select';
  options: Option[];
  selectedValue?: string | number;
  onSelect: (value: string | number) => void;
}

export type IconTextCardProps = TextProps | ButtonProps | SelectProps;

const IconTextCard: React.FC<IconTextCardProps> = (props) => {
  const { mode = 'text', icon, title, description, className = '' } = props;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 处理点击事件
  const handleClick = () => {
    if (mode === 'button' && 'onClick' in props) {
      props.onClick();
    } else if (mode === 'select') {
      setIsOpen(!isOpen);
    }
  };

  // 处理选择选项
  const handleSelect = (value: string | number) => {
    if (mode === 'select') {
      (props as SelectProps).onSelect(value);
      setIsOpen(false);
    }
  };

  // 获取当前选中的选项（用于选择模式）
  const getSelectedOption = (): Option | null => {
    if (mode === 'select' && (props as SelectProps).selectedValue) {
      return (
        (props as SelectProps).options.find(
          (option) => option.value === (props as SelectProps).selectedValue,
        ) || null
      );
    }
    return null;
  };

  // 渲染内容
  const renderContent = () => {
    const selectedOption = mode === 'select' ? getSelectedOption() : null;

    return (
      <>
        <div className="icon-container">
          {selectedOption ? selectedOption.icon : icon}
        </div>
        <div className="text-container">
          <div className="title">
            {selectedOption ? selectedOption.title : title}
          </div>
          <div className="description">
            {selectedOption ? selectedOption.description : description}
          </div>
        </div>
        {mode === 'select' && (
          <div className="chevron">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              width="20"
              height="20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </>
    );
  };

  // 渲染下拉菜单
  const renderDropdown = () => {
    if (mode !== 'select' || !isOpen || !(props as SelectProps).options.length)
      return null;

    return (
      <div className="dropdown-menu">
        {(props as SelectProps).options.map((option) => (
          <div
            key={option.value}
            className={`dropdown-item ${(props as SelectProps).selectedValue === option.value ? 'selected' : ''}`}
            onClick={() => handleSelect(option.value)}
          >
            <div className="item-icon">{option.icon}</div>
            <div className="item-text">
              <div className="item-title">{option.title}</div>
              <div className="item-description">{option.description}</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // 根据模式渲染不同的根元素
  const rootClasses = `icon-text-card ${mode} ${isOpen ? 'open' : ''} ${className}`;

  if (mode === 'button') {
    return (
      <Button
        className={rootClasses}
        onClick={handleClick}
      >
        {renderContent()}
      </Button>
    );
  }

  return (
    <IconTextCardWrap
      className={rootClasses}
      onClick={handleClick}
      ref={dropdownRef}
    >
      {renderContent()}
      {renderDropdown()}
    </IconTextCardWrap>
  );
};

export default IconTextCard;
