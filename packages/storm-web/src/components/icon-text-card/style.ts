/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import styled from 'styled-components';

export const IconTextCardWrap = styled.div`
  position: relative;
  display: flex;
  border-radius: 5px;
  align-items: center;

  &:hover {
    background-color: #eff6ff96; // #eff6ff0d;
    border-radius: 5px;
  }
  .icon-text-card {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    background-color: #fff;
    width: 100%;
    position: relative;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .icon-text-card.text {
    cursor: default;
  }

  .icon-text-card.button {
    cursor: pointer;
    text-align: left;
  }

  .icon-text-card.button:hover {
    background-color: #f8fafc;
    border-color: #cbd5e1;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 2px 4px -1px rgba(0, 0, 0, 0.05);
  }

  .icon-text-card.select {
    cursor: pointer;
  }

  .icon-text-card.select:hover {
    border-color: #cbd5e1;
  }

  .icon-text-card.open {
    border-color: #94a3b8;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 2px 4px -1px rgba(0, 0, 0, 0.05);
  }

  .icon-container {
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 30px;
    border-radius: 10px;
    flex-shrink: 0;
  }

  .text-container {
    flex: 1;
    min-width: 0;
  }

  .title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .description {
    font-size: 14px;
    color: #64748b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .chevron {
    margin-left: 8px;
    color: #94a3b8;
    transition: transform 0.2s;
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 10;
    max-height: 300px;
    overflow-y: auto;
  }

  .dropdown-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s;
  }

  .dropdown-item:last-child {
    border-bottom: none;
  }

  .dropdown-item:hover {
    background-color: #f8fafc;
  }

  .dropdown-item.selected {
    background-color: #eff6ff;
  }

  .item-icon {
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f1f5f9;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .item-text {
    flex: 1;
    min-width: 0;
  }

  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #1e293b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-description {
    font-size: 13px;
    color: #64748b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;
