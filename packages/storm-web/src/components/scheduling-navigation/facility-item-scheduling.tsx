/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PumpStateColor } from '@waterdesk/data/app-config';
import { PumpTimeData } from '@waterdesk/data/pump-status';
import {
  PlantSimulationInfo,
  PumpStationSimulationInfo,
} from '@waterdesk/data/scheduling-data';
import { TimeData } from '@waterdesk/data/time-data';
import { UnitFormat } from '@waterdesk/data/unit-system';
import { Divider } from 'antd';
import { useEffect, useState } from 'react';
import PumpStatusChart from '../charts/pump-status-chart';
import SchedulingChart from './scheduling-chart';

// 定义设施类型的联合类型
type FacilityType = PlantSimulationInfo | PumpStationSimulationInfo;

interface Props {
  date: string;
  facility: FacilityType;
  pumpStateColor: PumpStateColor;
  showFacilityName?: boolean;
  flowUnitFormat?: UnitFormat;
  headUnitFormat?: UnitFormat;
  showPressure?: boolean;
  onLoadingChange?: (isLoading: boolean) => void;
  getPatternValues: (
    date: string,
    patterns: string[],
  ) => Promise<Map<string, TimeData[]>>;
}

export default function FacilityItemScheduling(props: Props) {
  const {
    date,
    facility,
    pumpStateColor,
    showFacilityName,
    flowUnitFormat,
    headUnitFormat,
    showPressure = false,
    onLoadingChange,
    getPatternValues,
  } = props;

  const [flowTimeData, setFlowTimeData] = useState<TimeData[]>([]);
  const [headTimeData, setHeadTimeData] = useState<TimeData[]>([]);
  const [pumpTimeData, setPumpTimeData] = useState<PumpTimeData[]>([]);
  const [pumpTimeDataMap, setPumpTimeDataMap] = useState<
    Map<string, PumpTimeData[]>
  >(new Map());

  const updateFlowPatternValuesChart = async () => {
    if (facility.flow.length === 0) return;
    const flowTimeData = await getPatternValues(date, [
      facility.flow[0].pattern,
    ]);
    const patternValues = flowTimeData.get(facility.flow[0].pattern);
    setFlowTimeData(patternValues ?? []);
  };

  const updateHeadPatternValuesChart = async () => {
    if (facility.head.length === 0) {
      setHeadTimeData([]);
      return;
    }
    const headTimeData = await getPatternValues(date, [
      facility.head[0].pattern,
    ]);
    const patternValues = headTimeData.get(facility.head[0].pattern);
    setHeadTimeData(patternValues ?? []);
  };

  const updatePumpPatternValuesChart = async () => {
    if (facility.pumps.length === 0) return;
    const pumpPatternValues = await getPatternValues(
      date,
      facility.pumps.map((item) => item.pumpPattern),
    );

    const pumpTimeData: PumpTimeData[] = [];
    pumpPatternValues.forEach((value, key) => {
      const variable = facility.pumps.find(
        (item) => item.pumpPattern === key,
      )?.variable;
      pumpTimeData.push({
        name: key,
        variable: variable ?? false,
        timeData: value,
      });
    });

    const pumpTimeDataMap: Map<string, PumpTimeData[]> = new Map();
    pumpTimeDataMap.set(facility.id, pumpTimeData);

    setPumpTimeData(pumpTimeData);
    setPumpTimeDataMap(pumpTimeDataMap);
  };

  useEffect(() => {
    const loadData = async () => {
      if (onLoadingChange) {
        onLoadingChange(true);
      }

      try {
        await Promise.all([
          updateFlowPatternValuesChart(),
          updateHeadPatternValuesChart(),
          updatePumpPatternValuesChart(),
        ]);
      } finally {
        if (onLoadingChange) {
          onLoadingChange(false);
        }
      }
    };

    loadData();
  }, [date, facility]);

  // 判断是水厂还是泵站
  const isPlant = (facility: FacilityType): facility is PlantSimulationInfo => {
    return 'flow' in facility && !('pumpStationId' in facility);
  };

  return (
    <>
      <SchedulingChart
        date={date}
        showPlantName={showFacilityName}
        flowData={flowTimeData}
        headData={showPressure ? headTimeData : []}
        flowUnitFormat={flowUnitFormat}
        headUnitFormat={headUnitFormat}
        pumpTimeData={pumpTimeDataMap}
        subtitle={isPlant(facility) ? '出厂水量' : '出站水量'}
      />
      {facility.pumps.length > 0 && pumpTimeData.length > 0 && (
        <>
          <Divider
            orientation="left"
            style={{ margin: '2px 0 2px 0' }}
          >
            水泵开关状态详情
          </Divider>
          <PumpStatusChart
            minHeight="80px"
            pumpTimeData={pumpTimeData}
            pumpStateColor={pumpStateColor}
          />
        </>
      )}
    </>
  );
}

FacilityItemScheduling.displayName = 'FacilityItemScheduling';
