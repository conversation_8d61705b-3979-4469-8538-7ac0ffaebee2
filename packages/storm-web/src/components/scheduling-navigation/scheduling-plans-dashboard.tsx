/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  PlantSimulationInfo,
  PumpStationSimulationInfo,
} from '@waterdesk/data/scheduling-data';
import { Card, Col, Row, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { RecommendedValues } from './scheduling-plans';

const DashboardContainer = styled.div`
  width: 100%;
  padding: 16px;
  background: ${({ theme }) => theme.colorBgContainer};
  border-radius: 8px;

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .ant-card-head {
      border-bottom: 1px solid ${({ theme }) => theme.colorBorderSecondary};
      padding: 12px 16px;
    }

    .ant-card-body {
      padding: 16px;
    }
  }
`;

const SectionTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colorText};
  margin: 0;
`;

const WaterSupplyCard = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #4285f4 0%, #1e5dca 100%);
  border-radius: 8px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 60px;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);

    &::before {
      opacity: 1;
    }
  }

  .supply-name {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
    flex: 1;
  }

  .supply-value {
    font-size: 22px;
    font-weight: 700;
    text-align: right;

    .unit {
      font-size: 12px;
      font-weight: 400;
      opacity: 0.8;
      margin-left: 4px;
    }
  }
`;

const PumpStationCard = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #4285f4 0%, #1e5dca 100%);
  border-radius: 8px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 60px;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);

    &::before {
      opacity: 1;
    }
  }

  .station-name {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
    flex: 1;
  }

  .station-value {
    font-size: 22px;
    font-weight: 700;
    text-align: right;

    .unit {
      font-size: 12px;
      font-weight: 400;
      opacity: 0.8;
      margin-left: 4px;
    }
  }
`;

const EvaluationCard = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #4285f4 0%, #1e5dca 100%);
  border-radius: 8px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 60px;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);

    &::before {
      opacity: 1;
    }
  }

  .evaluation-label {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
    flex: 1;
  }

  .evaluation-score {
    font-size: 24px;
    font-weight: 700;
    text-align: right;
    color: #ffffff;
  }
`;

interface WaterSupplyData {
  name: string;
  value: number;
  unit: string;
}

interface PumpStationData {
  name: string;
  value: number;
  unit: string;
}

interface EvaluationData {
  score: number;
  label: string;
}

interface PlantDayFlow {
  id: string;
  name: string;
  flow: number;
}

interface PlanItem {
  key: string;
  date: string;
  recommendedValue: number;
  plants: PlantDayFlow[];
}

export interface SchedulingPlansDashboardProps {
  plants: PlantSimulationInfo[];
  pumpStations: PumpStationSimulationInfo[];
  recommendedValues: RecommendedValues[];
  showPumpStationAndPressure?: boolean;
  onSelectionChange: (
    selectedRowKeys: React.Key[],
    selectedDate: string,
  ) => void;
  getPlantDailySumFlows: (
    date: string,
    plantIds: string[],
  ) => Promise<Map<string, number>>;
}

const SchedulingPlansDashboard: React.FC<SchedulingPlansDashboardProps> = ({
  plants,
  pumpStations,
  recommendedValues,
  showPumpStationAndPressure = false,
  onSelectionChange,
  getPlantDailySumFlows,
}) => {
  const [planItem, setPlanItem] = useState<PlanItem | null>(null);
  const [loading, setLoading] = useState(false);

  const allFacilities = showPumpStationAndPressure
    ? [...plants, ...pumpStations]
    : [...plants];

  const formatRecommendedValue = (value: number): number => {
    return Number((value * 100).toFixed(2));
  };

  const buildPlanItem = (
    recommendedDay: RecommendedValues,
    plantFlowMap: Map<string, number>,
  ): PlanItem => {
    const plants: PlantDayFlow[] = [];
    plantFlowMap.forEach((flow, plantId) => {
      plants.push({
        id: plantId,
        name: plantId,
        flow,
      });
    });

    return {
      key: recommendedDay.date,
      date: recommendedDay.date,
      recommendedValue: formatRecommendedValue(recommendedDay.value),
      plants,
    };
  };

  useEffect(() => {
    const updatePlanItem = async () => {
      if (allFacilities.length === 0 || recommendedValues.length === 0) return;

      setLoading(true);
      try {
        // 找到分数最高的推荐值
        const highestScoreRecommendedValue = recommendedValues.reduce(
          (prev, current) => (current.value > prev.value ? current : prev),
        );

        const facilityIds = allFacilities.map((item) => item.id);

        const plantFlowMap = await getPlantDailySumFlows(
          highestScoreRecommendedValue.date,
          facilityIds,
        );

        const item = buildPlanItem(highestScoreRecommendedValue, plantFlowMap);

        setPlanItem(item);

        // 自动选中分数最高的那条
        onSelectionChange([item.key], item.date);
      } catch (error) {
        console.error('Failed to update plan item:', error);
      } finally {
        setLoading(false);
      }
    };

    updatePlanItem();
  }, [plants, recommendedValues]);

  // 获取水厂数据
  const getWaterSupplyData = (): WaterSupplyData[] => {
    if (!planItem) return [];

    const waterSupplyData = planItem.plants
      .map((plant) => {
        // 在plants数组中查找对应的水厂
        const facility = plants.find((f) => f.id === plant.id);
        if (!facility) return null;
        return {
          name: facility.title,
          value: Number((plant.flow / 10000).toFixed(1)),
          unit: '万吨',
        };
      })
      .filter(Boolean) as WaterSupplyData[];

    return waterSupplyData;
  };

  // 获取泵站数据
  const getPumpStationData = (): PumpStationData[] => {
    if (!planItem || !showPumpStationAndPressure) return [];

    const pumpStationData = planItem.plants
      .map((plant) => {
        // 在pumpStations数组中查找对应的泵站
        const facility = pumpStations.find((f) => f.id === plant.id);
        if (!facility) return null;
        return {
          name: facility.title,
          value: Number((plant.flow / 10000).toFixed(1)),
          unit: '万吨',
        };
      })
      .filter(Boolean) as PumpStationData[];

    return pumpStationData;
  };

  // 获取评价数据
  const getEvaluationData = (): EvaluationData => {
    return {
      score: planItem?.recommendedValue || 0,
      label: '推荐度',
    };
  };

  const waterSupplyData = getWaterSupplyData();
  const pumpStationData = getPumpStationData();
  const evaluationData = getEvaluationData();

  return (
    <DashboardContainer>
      <Row
        gutter={[24, 16]}
        align="stretch"
      >
        {/* 左侧：日供水量预测 + 泵站供水预测 */}
        <Col span={12}>
          <Space
            direction="vertical"
            size={16}
            style={{ width: '100%' }}
          >
            {/* 日供水量预测 */}
            <Card
              title={<SectionTitle>日供水量预测</SectionTitle>}
              variant="borderless"
              loading={loading}
              styles={{ body: { padding: '16px' } }}
            >
              <Row gutter={[12, 12]}>
                {waterSupplyData.map((item, index) => (
                  <Col
                    span={12}
                    key={index}
                  >
                    <WaterSupplyCard>
                      <div className="supply-name">{item.name}</div>
                      <div className="supply-value">
                        {item.value} <span className="unit">{item.unit}</span>
                      </div>
                    </WaterSupplyCard>
                  </Col>
                ))}
              </Row>
            </Card>

            {/* 泵站供水预测（仅在显示泵站时显示） */}
            {showPumpStationAndPressure && pumpStationData.length > 0 && (
              <Card
                title={<SectionTitle>泵站供水预测</SectionTitle>}
                variant="borderless"
                loading={loading}
                styles={{ body: { padding: '16px' } }}
              >
                <Row gutter={[12, 12]}>
                  {pumpStationData.map((item, index) => (
                    <Col
                      span={12}
                      key={index}
                    >
                      <PumpStationCard>
                        <div className="station-name">{item.name}</div>
                        <div className="station-value">
                          {item.value} <span className="unit">{item.unit}</span>
                        </div>
                      </PumpStationCard>
                    </Col>
                  ))}
                </Row>
              </Card>
            )}
          </Space>
        </Col>

        {/* 右侧：方案评价 */}
        <Col span={12}>
          <Card
            title={<SectionTitle>方案评价</SectionTitle>}
            variant="borderless"
            loading={loading}
            styles={{ body: { padding: '16px' } }}
          >
            <EvaluationCard>
              <div className="evaluation-label">{evaluationData.label}</div>
              <div className="evaluation-score">{evaluationData.score}</div>
            </EvaluationCard>
          </Card>
        </Col>
      </Row>
    </DashboardContainer>
  );
};

export default SchedulingPlansDashboard;
