/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  PlantSimulationInfo,
  PumpStationSimulationInfo,
} from '@waterdesk/data/scheduling-data';
import { Table } from 'antd';
import type { ColumnGroupType, ColumnType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { RecommendedValues } from './scheduling-plans';

interface PlantDayFlow {
  id: string;
  name: string;
  flow: number;
}

interface PlanItem {
  key: string;
  date: string;
  recommendedValue: number;
  plants: PlantDayFlow[];
}

// 添加表格数据行的类型定义
interface TableDataRow extends PlanItem {
  [key: string]: string | number | PlantDayFlow[];
}

export interface SchedulingPlansTableProps {
  plants: PlantSimulationInfo[];
  pumpStations: PumpStationSimulationInfo[];
  recommendedValues: RecommendedValues[];
  showPumpStationAndPressure?: boolean;
  selectedRowKeys: React.Key[];
  onSelectionChange: (
    selectedRowKeys: React.Key[],
    selectedDate: string,
  ) => void;
  getPlantDailySumFlows: (
    date: string,
    plantIds: string[],
  ) => Promise<Map<string, number>>;
}

export default function SchedulingPlansTable(props: SchedulingPlansTableProps) {
  const {
    plants,
    pumpStations,
    recommendedValues,
    showPumpStationAndPressure = false,
    selectedRowKeys,
    onSelectionChange,
    getPlantDailySumFlows,
  } = props;

  const [columns, setColumns] = useState<
    (ColumnGroupType<TableDataRow> | ColumnType<TableDataRow>)[]
  >([]);
  const [transformedDataSource, setTransformedDataSource] = useState<
    TableDataRow[]
  >([]);

  const allFacilities = showPumpStationAndPressure
    ? [...plants, ...pumpStations]
    : [...plants];

  const formatRecommendedValue = (value: number): number => {
    return Number((value * 100).toFixed(2));
  };

  const buildPlanItem = (
    recommendedDay: RecommendedValues,
    plantFlowMap: Map<string, number>,
  ): PlanItem => {
    const plants: PlantDayFlow[] = [];
    plantFlowMap.forEach((flow, plantId) => {
      plants.push({
        id: plantId,
        name: plantId,
        flow,
      });
    });

    return {
      key: recommendedDay.date,
      date: recommendedDay.date,
      recommendedValue: formatRecommendedValue(recommendedDay.value),
      plants,
    };
  };

  const transformToTableData = (planItems: PlanItem[]): TableDataRow[] => {
    return planItems.map((item) => {
      const tableRow: TableDataRow = { ...item };

      item.plants.forEach((plant) => {
        tableRow[plant.id] = plant.id;
        tableRow[`${plant.id}_totalFlow`] = Number(
          (plant.flow / 10000).toFixed(1),
        );
      });

      return tableRow;
    });
  };

  const fetchPlanItems = async (): Promise<PlanItem[]> => {
    const facilityIds = allFacilities.map((item) => item.id);

    const plantFlowMaps = await Promise.all(
      recommendedValues.map((recommendedDay) =>
        getPlantDailySumFlows(recommendedDay.date, facilityIds),
      ),
    );

    const planItems = recommendedValues.map((recommendedDay, index) =>
      buildPlanItem(recommendedDay, plantFlowMaps[index]),
    );

    return planItems;
  };

  const updatePlanItemSource = async () => {
    if (allFacilities.length === 0) return;

    try {
      const planItems = await fetchPlanItems();
      const tableData = transformToTableData(planItems);
      setTransformedDataSource(tableData);
    } catch (error) {
      console.error('Failed to update plan item source:', error);
    }
  };

  useEffect(() => {
    updatePlanItemSource();
  }, [plants, recommendedValues]);

  useEffect(() => {
    const columns: (
      | ColumnGroupType<TableDataRow>
      | ColumnType<TableDataRow>
    )[] = [
      {
        title: '推荐度',
        dataIndex: 'recommendedValue',
        key: 'recommendedValue',
      },
    ];

    if (plants.length > 0) {
      columns.push({
        title: '水厂出厂水量(万吨)',
        key: 'plants',
        children: [
          ...plants.map((plant) => ({
            title: plant.title,
            dataIndex: `${plant.id}_totalFlow`,
            key: `${plant.id}_totalFlow`,
          })),
        ],
      });
    }

    // 添加泵站列（根据配置决定是否显示）
    if (showPumpStationAndPressure && pumpStations.length > 0) {
      columns.push({
        title: '泵站出站水量(万吨)',
        key: 'pumpStations',
        children: [
          ...pumpStations.map((pumpStation) => ({
            title: pumpStation.title,
            dataIndex: `${pumpStation.id}_totalFlow`,
            key: `${pumpStation.id}_totalFlow`,
          })),
        ],
      });
    }

    setColumns(columns);
  }, [plants, pumpStations, showPumpStationAndPressure]);

  return (
    <Table
      size="small"
      dataSource={transformedDataSource}
      columns={columns}
      bordered
      pagination={false}
      rowSelection={{
        type: 'radio',
        selectedRowKeys,
        onChange: (
          selectedRowKeys: React.Key[],
          selectedRows: TableDataRow[],
        ) => {
          const selectedDate =
            selectedRows.length > 0 ? selectedRows[0].date : '';
          onSelectionChange(selectedRowKeys, selectedDate);
        },
        getCheckboxProps: (record: TableDataRow) => ({
          name: record.date,
        }),
      }}
    />
  );
}
