# ScheduleInfoCard 智能调度信息卡片组件

## 概述

`ScheduleInfoCard` 是一个用于显示智能调度信息的卡片组件，支持展示标题、监测信息、泵状态和策略列表等功能。

## 功能特性

- ✅ 标题显示：支持文字溢出隐藏
- ✅ 监测信息：支持 0、1、2 个监测数据的不同布局
- ✅ 泵状态显示：集成现有 RenderPumpCell 组件
- ✅ 策略列表：支持活跃时间高亮显示
- ✅ 接受按钮：支持点击回调

## Props 接口

```typescript
interface ScheduleInfoCardProps {
  // 标题
  title: string;

  // 监测数据源，支持0、1、2个数据项
  scadDataSource?: [string, string] | [string] | [];

  // 泵数据源，用于显示泵状态
  pumpDataSource: {
    pumpList: PumpInfo[];
    valueInfo: Map<string, PropertyValue>;
    pumpStateColor: PumpStateColor;
  };

  // 当前活跃的时间点
  activeTime: string;

  // 策略列表
  strategies: StrategyItem[];

  // 接受按钮点击回调
  onAccept?: () => void;

  // 组件样式
  className?: string;
  style?: React.CSSProperties;
}
```

## 使用示例

### 基础用法

```tsx
import { ScheduleInfoCard } from '@/components/smart-schedule';

const mockStrategies = [
  { id: '09:00', time: '09:00', content: '开启3#泵，频率调至35Hz' },
  { id: '09:15', time: '09:15', content: '调整2#泵频率至40Hz，关闭1#泵' },
];

<ScheduleInfoCard
  title="上水厂智能调度"
  scadDataSource={['0.53MPa', '50000m³/h']}
  pumpDataSource={{
    pumpList: mockPumpList,
    valueInfo: mockValueInfo,
    pumpStateColor: mockPumpStateColor,
  }}
  activeTime="09:15"
  strategies={mockStrategies}
  onAccept={() => console.log('接受调度策略')}
/>;
```

### 单个监测数据

```tsx
<ScheduleInfoCard
  title="单监测数据示例"
  scadDataSource={['0.53MPa']}
  // ... 其他 props
/>
```

### 无监测数据

```tsx
<ScheduleInfoCard
  title="无监测数据示例"
  scadDataSource={[]}
  // ... 其他 props
/>
```

## 样式定制

组件内部使用 styled-components 进行样式管理，支持主题系统：

- `CardContent`: 卡片内容容器
- `MonitoringInfo`: 监测信息区域
- `PumpSection`: 泵状态区域
- `StrategyList`: 策略列表区域
- `StrategyItem`: 策略项样式（支持活跃状态）

## 依赖关系

- `@waterdesk/data/app-config`: PumpStateColor 类型
- `@waterdesk/data/device`: PumpInfo, PropertyValue 类型
- `antd`: Card, Button 组件
- `src/components/scada-dashboard/render-pump-cell`: 泵状态显示组件
- `src/styles/common-style`: EllipsisTextDiv 样式

## Storybook 测试

组件提供了完整的 Storybook 测试用例：

- Default: 基础示例
- SingleMonitoring: 单个监测数据
- NoMonitoring: 无监测数据
- DifferentActiveTime: 不同活跃时间
- LongTitle: 长标题溢出测试
- MultiplePumps: 多泵状态
- CustomStyle: 自定义样式

运行 Storybook 查看所有示例：

```bash
npm run storybook
```

## 注意事项

1. `pumpDataSource` 中的 `valueInfo` 需要正确设置泵的状态值
2. `activeTime` 必须与 `strategies` 中的某个 `id` 匹配才能正确高亮
3. 组件使用了主题系统，确保在正确的主题上下文中使用

## 故障排除

如果遇到 Vite 依赖优化错误，可以尝试：

```bash
# 清理缓存
rm -rf node_modules/.vite
rm -rf node_modules/.cache/storybook

# 重新启动 Storybook
npm run storybook
```
