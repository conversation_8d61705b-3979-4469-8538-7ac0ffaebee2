/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  getChangeTooltips,
  StrategyItemChangeType,
  StrategyItemChangeValue,
} from '@waterdesk/data/smart-scheduling-data-new';
import { Card, Tooltip } from 'antd';
import React, { useEffect, useRef } from 'react';
import { EllipsisTextDiv } from 'src/styles/common-style';
import RenderPumpCell from '../scada-dashboard/render-pump-cell';
import { getHighestPriorityColor } from './strategy-color-utils';
import {
  ButtonsContainer,
  CardContent,
  CircleButton,
  MonitoringInfo,
  MonitoringItem,
  PumpSection,
  StrategyContent,
  StrategyList,
  StrategyItem as StyledStrategyItem,
} from './style';
import { ScheduleInfoCardProps } from './types';

const ScheduleInfoCard: React.FC<ScheduleInfoCardProps> = ({
  title,
  scadaDataSource = [],
  pumpDataSource,
  activeTime,
  strategies,
  onAccept,
  onReject,
  className,
  style,
}) => {
  const strategyListRef = useRef<HTMLDivElement>(null);

  // 根据change type获取tooltip内容
  const getTooltipContent = (changes: StrategyItemChangeValue[]) => {
    if (!changes || changes.length === 0) return null;

    const tooltipItems = getChangeTooltips(changes);

    return (
      <div>
        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>策略详情:</div>
        {tooltipItems.map((item, index) => (
          <div
            key={index}
            style={{ margin: '2px 0', fontSize: '13px' }}
          >
            {item}
          </div>
        ))}
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
          点击拒绝按钮拒绝采纳该策略
        </div>
      </div>
    );
  };

  // 根据策略类型获取边框颜色
  const getCardBorderColor = () => {
    const activeStrategy = strategies.find(
      (strategy) => strategy.id === activeTime,
    );

    return getHighestPriorityColor(activeStrategy?.changes || []);
  };

  // 渲染监测信息
  const renderMonitoringInfo = () => {
    const data =
      scadaDataSource.length > 0
        ? scadaDataSource
        : [
            {
              valueWithSymbol: '--',
            },
            {
              valueWithSymbol: '--',
            },
          ];
    const itemWidth = data.length === 1 ? '100%' : '50%';

    return (
      <MonitoringInfo>
        {data.map((item, index) => (
          <Tooltip
            key={index}
            placement="bottom"
            title={
              item.title && (
                <>
                  <div>{item.title}</div>
                  {item.otime && (
                    <div
                      style={{
                        marginTop: '8px',
                        fontSize: '12px',
                      }}
                    >
                      {item.otime}
                    </div>
                  )}
                </>
              )
            }
          >
            <MonitoringItem
              key={index}
              index={index}
              width={itemWidth}
            >
              {item.valueWithSymbol}
            </MonitoringItem>
          </Tooltip>
        ))}
      </MonitoringInfo>
    );
  };

  // 获取所有策略列表（按时间排序）
  const getDisplayStrategies = () => {
    // 按时间排序，返回所有策略
    return [...strategies].sort(
      (a, b) => new Date(a.time).getTime() - new Date(b.time).getTime(),
    );
  };

  // 渲染策略列表
  const renderStrategies = () => {
    const displayStrategies = getDisplayStrategies();
    const cardBorderColor = getCardBorderColor();

    return (
      <StrategyList ref={strategyListRef}>
        {displayStrategies.map((strategy) => {
          const isActive = strategy.id === activeTime;
          const isNoOperation = strategy.changes.length === 0;
          const displayContent = isNoOperation
            ? '无需操作'
            : strategy.changes.map((item) => `${item.contentText}`).join(', ');

          const shouldShowTooltip = isActive && strategy.changes.length > 0;
          const strategyItem = (
            <StyledStrategyItem
              key={strategy.id}
              isActive={isActive}
              borderColor={cardBorderColor}
              data-strategy-id={strategy.id}
            >
              <StrategyContent isActive={isActive}>
                {isActive ? (
                  <div title={displayContent}>{displayContent}</div>
                ) : (
                  <EllipsisTextDiv title={displayContent}>
                    {displayContent}
                  </EllipsisTextDiv>
                )}
              </StrategyContent>
            </StyledStrategyItem>
          );

          return shouldShowTooltip ? (
            <Tooltip
              key={strategy.id}
              title={getTooltipContent(strategy.changes)}
              placement="right"
              styles={{
                root: {
                  maxWidth: '280px',
                },
              }}
            >
              {strategyItem}
            </Tooltip>
          ) : (
            strategyItem
          );
        })}
      </StrategyList>
    );
  };
  // 简单的滚动定位到活跃策略
  useEffect(() => {
    if (strategyListRef.current) {
      const activeElement = strategyListRef.current.querySelector(
        `[data-strategy-id="${activeTime}"]`,
      ) as HTMLElement;

      if (activeElement) {
        activeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }, [activeTime]);

  // 渲染按钮
  const renderButtons = () => {
    const activeStrategy = strategies.find(
      (strategy) => strategy.id === activeTime,
    );

    // 如果没找到当前活跃策略，不显示任何按钮
    if (!activeStrategy) {
      return <ButtonsContainer />;
    }

    // 检查是否有策略内容（用于显示拒绝按钮）
    const hasContent = activeStrategy.changes.length > 0;

    // 检查是否包含敏感操作 WDM_PUMPS（用于显示接受按钮）
    const hasSensitiveOperation =
      hasContent &&
      activeStrategy.changes.some(
        (operation) =>
          operation.changeType === StrategyItemChangeType.PUMP_CLOSE ||
          operation.changeType === StrategyItemChangeType.PUMP_OPEN,
      );

    // 如果既没有内容也没有敏感操作，不显示任何按钮
    if (!hasContent && !hasSensitiveOperation) {
      return <ButtonsContainer />;
    }

    return (
      <ButtonsContainer>
        {/* 接受按钮：只有敏感操作时才显示 */}
        {hasSensitiveOperation && (
          <CircleButton
            onClick={onAccept}
            style={{
              backgroundColor: '#52c41a',
            }}
          >
            接受
          </CircleButton>
        )}

        {/* 拒绝按钮：有策略信息就显示 */}
        {hasContent && (
          <CircleButton
            onClick={onReject}
            style={{
              backgroundColor: '#ff4d4f',
            }}
          >
            拒绝
          </CircleButton>
        )}
      </ButtonsContainer>
    );
  };

  return (
    <Card
      className={className}
      style={{
        ...style,
        border: `2px solid ${getCardBorderColor()}`,
        borderRadius: '12px',
        boxShadow: `0 4px 12px ${getCardBorderColor()}40, 0 2px 8px ${getCardBorderColor()}20`,
      }}
      styles={{
        body: {
          padding: '12px',
        },
      }}
    >
      <CardContent>
        {/* 标题 */}
        <EllipsisTextDiv
          style={{
            textAlign: 'center',
            fontSize: '16px',
            fontWeight: 'bold',
          }}
          title={title}
        >
          {title}
        </EllipsisTextDiv>

        {/* 监测信息 */}
        {renderMonitoringInfo()}

        {/* 泵状态信息 */}
        <PumpSection>
          <RenderPumpCell
            pumpList={pumpDataSource.pumpList}
            valueInfo={pumpDataSource.valueInfo}
            pumpStateColor={pumpDataSource.pumpStateColor}
            size="small"
            emptyText={''}
            justify="center"
          />
        </PumpSection>

        {/* 策略列表 */}
        {renderStrategies()}

        {/* 按钮区域 */}
        {renderButtons()}
      </CardContent>
    </Card>
  );
};

export default ScheduleInfoCard;
