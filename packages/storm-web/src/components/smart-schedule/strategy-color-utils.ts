/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import {
  StrategyItemChangeType,
  StrategyItemChangeValue,
} from '@waterdesk/data/smart-scheduling-data-new';

// 策略颜色配置
export const STRATEGY_COLORS = {
  PUMP_OPERATION: '#ff4d4f', // 红色 - 泵开关操作（最高优先级）
  VALVE_OPERATION: '#fa8c16', // 橙色 - 阀门开关操作
  PRESSURE_OPERATION: '#1890ff', // 蓝色 - 压力调整操作
  OTHER_OPERATION: '#52c41a', // 绿色 - 其他操作
  NO_OPERATION: '#d9d9d9', // 灰色 - 无操作（最低优先级）
} as const;

// 策略优先级定义（数值越高优先级越高）
export const STRATEGY_PRIORITY = {
  [StrategyItemChangeType.PUMP_CLOSE]: 4,
  [StrategyItemChangeType.PUMP_OPEN]: 4,
  [StrategyItemChangeType.VALVE_CLOSE]: 3,
  [StrategyItemChangeType.VALVE_OPEN]: 3,
  [StrategyItemChangeType.VALVE_OPENING]: 3,
  [StrategyItemChangeType.PRESSURE]: 2,
  [StrategyItemChangeType.PUMP_FREQUENCY]: 2,
  [StrategyItemChangeType.FLOW]: 1,
} as const;

/**
 * 根据策略变更类型获取对应颜色
 */
export const getStrategyTypeColor = (
  changeType: StrategyItemChangeType,
): string => {
  switch (changeType) {
    case StrategyItemChangeType.PUMP_CLOSE:
    case StrategyItemChangeType.PUMP_OPEN:
      return STRATEGY_COLORS.PUMP_OPERATION;
    case StrategyItemChangeType.VALVE_CLOSE:
    case StrategyItemChangeType.VALVE_OPEN:
    case StrategyItemChangeType.VALVE_OPENING:
      return STRATEGY_COLORS.VALVE_OPERATION;
    case StrategyItemChangeType.PRESSURE:
    case StrategyItemChangeType.PUMP_FREQUENCY:
      return STRATEGY_COLORS.PRESSURE_OPERATION;
    case StrategyItemChangeType.FLOW:
    default:
      return STRATEGY_COLORS.OTHER_OPERATION;
  }
};

/**
 * 根据策略变更获取最高优先级的颜色
 */
export const getHighestPriorityColor = (
  changes: StrategyItemChangeValue[],
): string => {
  if (!changes || changes.length === 0) {
    return STRATEGY_COLORS.NO_OPERATION;
  }

  // 找到优先级最高的变更类型
  const highestPriorityChange = changes.reduce((highest, current) => {
    const currentPriority = STRATEGY_PRIORITY[current.changeType] || 0;
    const highestPriority = STRATEGY_PRIORITY[highest.changeType] || 0;

    return currentPriority > highestPriority ? current : highest;
  });

  return getStrategyTypeColor(highestPriorityChange.changeType);
};

/**
 * 获取策略变更类型的中文描述
 */
export const getStrategyTypeDescription = (
  changeType: StrategyItemChangeType,
): string => {
  switch (changeType) {
    case StrategyItemChangeType.PUMP_CLOSE:
      return '泵关闭';
    case StrategyItemChangeType.PUMP_OPEN:
      return '泵开启';
    case StrategyItemChangeType.PUMP_FREQUENCY:
      return '泵频率';
    case StrategyItemChangeType.VALVE_CLOSE:
      return '阀门关闭';
    case StrategyItemChangeType.VALVE_OPEN:
      return '阀门开启';
    case StrategyItemChangeType.VALVE_OPENING:
      return '阀门开度';
    case StrategyItemChangeType.PRESSURE:
      return '压力调节';
    case StrategyItemChangeType.FLOW:
      return '流量调节';
    default:
      return '其他操作';
  }
};

/**
 * 获取策略变更类型的图标
 */
export const getStrategyTypeIcon = (
  changeType: StrategyItemChangeType,
): string => {
  switch (changeType) {
    case StrategyItemChangeType.PUMP_CLOSE:
      return '⏹️';
    case StrategyItemChangeType.PUMP_OPEN:
      return '▶️';
    case StrategyItemChangeType.PUMP_FREQUENCY:
      return '⚡';
    case StrategyItemChangeType.VALVE_CLOSE:
      return '🔴';
    case StrategyItemChangeType.VALVE_OPEN:
      return '🟢';
    case StrategyItemChangeType.VALVE_OPENING:
      return '🔧';
    case StrategyItemChangeType.PRESSURE:
      return '📊';
    case StrategyItemChangeType.FLOW:
      return '💧';
    default:
      return '🔧';
  }
};

/**
 * 统计策略变更类型数量
 */
export const getStrategyTypeCount = (
  changes: StrategyItemChangeValue[],
): Map<StrategyItemChangeType, number> => {
  const countMap = new Map<StrategyItemChangeType, number>();

  changes.forEach((change) => {
    const currentCount = countMap.get(change.changeType) || 0;
    countMap.set(change.changeType, currentCount + 1);
  });

  return countMap;
};
