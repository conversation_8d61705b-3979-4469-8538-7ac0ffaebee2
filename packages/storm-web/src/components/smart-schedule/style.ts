/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { EllipsisTextDiv } from 'src/styles/common-style';
import styled from 'styled-components';

export const CardContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const MonitoringInfo = styled.div`
  display: flex;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colorBorder};
`;

export const MonitoringItem = styled(EllipsisTextDiv)<{
  width: string;
  index: number;
}>`
  width: ${({ width }) => width};
  font-size: 14px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  border-left: ${({ index, theme }) =>
    index > 0 ? `1px solid ${theme.colorBorder}` : 'none'};
`;

export const PumpSection = styled.div`
  border-radius: 4px;
  height: 22px;
  text-align: center;
  overflow: hidden;
`;

export const StrategyList = styled.div`
  /* 固定高度，约显示3条策略（每条约60px + gap） */
  height: 222px;
  overflow: hidden;
`;

export const StrategyItem = styled.div<{
  isActive: boolean;
  borderColor?: string;
}>`
  height: ${({ isActive }) => (isActive ? '60px' : '40px')};
  position: relative;
  padding: 8px 12px;
  margin: 6px 0;
  background-color: ${({ theme, isActive, borderColor }) =>
    isActive
      ? `${borderColor}15` // 15% 透明度的边框色作为背景
      : theme.colorFillAlter};
  border-radius: 6px;
  border: 1px solid
    ${({ theme, isActive, borderColor }) =>
      isActive ? borderColor || theme.colorPrimary : theme.colorBorder};
  cursor: pointer;
  box-shadow: ${({ theme, isActive, borderColor }) =>
    isActive
      ? `0 4px 12px ${borderColor || theme.colorPrimary}30, 0 2px 8px ${borderColor || theme.colorPrimary}20`
      : `0 2px 8px ${theme.colorBorder}20`};
  opacity: ${({ isActive }) => (isActive ? 1 : 0.6)};
  box-sizing: border-box;

  /* 简单的过渡效果 */
  transition: all 0.4s ease-in-out;

  &:hover {
    opacity: 1;
    background-color: ${({ theme, isActive, borderColor }) =>
      isActive
        ? `${borderColor}25` // hover时使用25%透明度的边框色
        : theme.colorFillTertiary};
  }

  /* 根据激活状态设置overflow */
  overflow: ${({ isActive }) => (isActive ? 'hidden' : 'visible')};
`;

export const StrategyContent = styled.div<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  /* 激活状态的样式 */
  ${({ isActive }) =>
    isActive &&
    `
    font-weight: bold;
    font-size: 15px;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
  `}

  /* 非激活状态的样式 */
  ${({ isActive }) =>
    !isActive &&
    `
    font-size: 14px;
    white-space: nowrap;
    overflow: visible;
    text-overflow: initial;
  `}
`;

export const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 8px;
  height: 40px;
`;

export const CircleButton = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    opacity: 0.8;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
`;
