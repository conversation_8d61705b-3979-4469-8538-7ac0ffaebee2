/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { PumpStateColor } from '@waterdesk/data/app-config';
import { PropertyValue, PumpInfo } from '@waterdesk/data/device';
import {
  ScadaDataSourceItem,
  StrategyItem,
} from '@waterdesk/data/smart-scheduling-data-new';

// 组件主要属性接口
export interface ScheduleInfoCardProps {
  // 标题
  title: string;
  // 监测数据源，支持0、1、2个数据项
  scadaDataSource?: ScadaDataSourceItem[];
  // 泵数据源，用于显示泵状态
  pumpDataSource: {
    pumpList: PumpInfo[];
    valueInfo: Map<string, PropertyValue>;
    pumpStateColor: PumpStateColor;
  };
  // 当前活跃的时间点 (datetime string)
  activeTime: string;
  // 策略列表
  strategies: StrategyItem[];
  // 接受按钮点击回调
  onAccept?: () => void;
  // 拒绝按钮点击回调
  onReject?: () => void;
  // 组件样式
  className?: string;
  style?: React.CSSProperties;
}
