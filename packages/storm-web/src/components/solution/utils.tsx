/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Tag, Tooltip } from 'antd';
import React from 'react';
import styled from 'styled-components';

const EventTagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
`;

const MoreIndicator = styled.span`
  color: #666;
  font-size: 12px;
  margin-left: 2px;
`;

const TooltipContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 300px;
`;

const TooltipEventItem = styled.div`
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
`;

// 定义事件对象类型
interface EventInfo {
  id: string;
  title: string;
}

/**
 * 渲染事件信息为标签组件
 * @param eventData 事件数据，可以是逗号分隔的字符串或JSON字符串
 * @returns React 组件或字符串
 */
export const renderEventTitles = (eventData: string): React.ReactNode => {
  if (!eventData) return '-';

  let events: string[] = [];

  try {
    // 尝试解析JSON格式的数据
    const parsedData = JSON.parse(eventData) as EventInfo[];
    if (Array.isArray(parsedData)) {
      events = parsedData
        .map((event) => event.title?.trim())
        .filter((title) => title);
    } else {
      throw new Error('Not an array');
    }
  } catch {
    // 如果JSON解析失败，回退到原来的逗号分隔处理
    events = eventData
      .split(',')
      .map((event) => event.trim())
      .filter((event) => event);
  }

  if (events.length === 0) return '-';

  // 最多显示5个标签
  const maxDisplayCount = 5;
  const displayEvents = events.slice(0, maxDisplayCount);
  const hasMore = events.length > maxDisplayCount;

  const tooltipContent = hasMore ? (
    <TooltipContent>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
        全部事件（共{events.length}个）：
      </div>
      {events.map((event, index) => (
        <TooltipEventItem key={`tooltip-${event}-${index}`}>
          {index + 1}. {event}
        </TooltipEventItem>
      ))}
    </TooltipContent>
  ) : null;

  const content = (
    <EventTagsContainer>
      {displayEvents.map((event, index) => (
        <Tag
          key={`${event}-${index}`}
          color="processing"
        >
          {event}
        </Tag>
      ))}
      {hasMore && <MoreIndicator>...(共{events.length}个)</MoreIndicator>}
    </EventTagsContainer>
  );

  if (hasMore) {
    return (
      <Tooltip
        title={tooltipContent}
        placement="topLeft"
        styles={{
          root: {
            maxWidth: '400px',
          },
        }}
      >
        {content}
      </Tooltip>
    );
  }

  return content;
};
