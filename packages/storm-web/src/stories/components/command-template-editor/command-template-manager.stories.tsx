/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react-vite';
import {
  CommandTemplateManagerConfig,
  getDefaultTemplateForDeviceAction,
  SystemVariableType,
  TemplateElementType,
} from '@waterdesk/data/dispatch-command/command-template';
import { DeviceAction } from '@waterdesk/data/dispatch-command/create-command';
import { CommandTemplateManager } from '../../../components/command-template-editor/command-template-manager';

const meta: Meta<typeof CommandTemplateManager> = {
  title: 'Components/CommandTemplateManager',
  component: CommandTemplateManager,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof CommandTemplateManager>;

// 空配置 - 所有指令类型都使用默认模板
export const Empty: Story = {
  args: {
    config: {},
    readonly: false,
  },
};

// 部分配置 - 只有部分指令类型有自定义模板
export const PartiallyConfigured: Story = {
  args: {
    config: {
      [DeviceAction.WATER_PUMP]: {
        id: 'water-pump-template',
        name: '水泵操作模板',
        description: '专用于水泵开关操作的指令模板',
        elements: [
          {
            id: 'element-1',
            type: TemplateElementType.TEXT,
            content: '接到指挥中心调度指令，请',
            order: 1,
          },
          {
            id: 'element-2',
            type: TemplateElementType.VARIABLE,
            content: '',
            variableType: SystemVariableType.DEVICE_TITLE,
            order: 2,
          },
          {
            id: 'element-3',
            type: TemplateElementType.TEXT,
            content: '于',
            order: 3,
          },
          {
            id: 'element-4',
            type: TemplateElementType.VARIABLE,
            content: '',
            variableType: SystemVariableType.PLAN_TIME,
            order: 4,
          },
          {
            id: 'element-5',
            type: TemplateElementType.TEXT,
            content: '按以下要求执行：',
            order: 5,
          },
          {
            id: 'element-6',
            type: TemplateElementType.DESCRIPTION,
            content: '',
            order: 6,
          },
          {
            id: 'element-7',
            type: TemplateElementType.TEXT,
            content: '。请确认收到并按时执行。',
            order: 7,
          },
        ],
        createTime: '2024-01-15T10:00:00.000Z',
        updateTime: '2024-01-15T14:30:00.000Z',
      },
      [DeviceAction.FLOW_ADJUSTMENT]: {
        id: 'flow-adjustment-template',
        name: '流量调整模板',
        description: '专用于流量调整操作的指令模板',
        elements: [
          {
            id: 'element-1',
            type: TemplateElementType.TEXT,
            content: '根据调度要求，',
            order: 1,
          },
          {
            id: 'element-2',
            type: TemplateElementType.VARIABLE,
            content: '',
            variableType: SystemVariableType.DEVICE_TITLE,
            order: 2,
          },
          {
            id: 'element-3',
            type: TemplateElementType.TEXT,
            content: '需在',
            order: 3,
          },
          {
            id: 'element-4',
            type: TemplateElementType.VARIABLE,
            content: '',
            variableType: SystemVariableType.PLAN_TIME,
            order: 4,
          },
          {
            id: 'element-5',
            type: TemplateElementType.TEXT,
            content: '进行如下流量调整：',
            order: 5,
          },
          {
            id: 'element-6',
            type: TemplateElementType.DESCRIPTION,
            content: '',
            order: 6,
          },
        ],
        createTime: '2024-01-15T09:30:00.000Z',
      },
    },
    readonly: false,
  },
};

// 完全配置 - 所有指令类型都有自定义模板
export const FullyConfigured: Story = {
  args: {
    config: Object.values(DeviceAction).reduce(
      (acc, action) => ({
        ...acc,
        [action]: {
          ...getDefaultTemplateForDeviceAction(action),
          updateTime: '2024-01-15T12:00:00.000Z',
        },
      }),
      {},
    ) as CommandTemplateManagerConfig,
    readonly: false,
  },
};

// 只读模式
export const ReadOnly: Story = {
  args: {
    config: {
      [DeviceAction.WATER_PUMP]: getDefaultTemplateForDeviceAction(
        DeviceAction.WATER_PUMP,
      ),
      [DeviceAction.VALVE_CONTROL]: getDefaultTemplateForDeviceAction(
        DeviceAction.VALVE_CONTROL,
      ),
    },
    readonly: true,
  },
};
