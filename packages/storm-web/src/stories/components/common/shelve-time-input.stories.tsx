/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react-vite';
import { Button, Form } from 'antd';
import { useState } from 'react';
import ShelveTimeInput from 'src/components/common/shelve-time-input/index';

const meta: Meta<typeof ShelveTimeInput> = {
  title: 'components/common/shelve-time-input',
  component: ShelveTimeInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: 'number',
      description: '当前值（分钟）',
    },
    onChange: {
      action: 'changed',
      description: '值变化时的回调',
    },
    placeholder: {
      control: 'text',
      description: '输入框占位符',
    },
    disabled: {
      control: 'boolean',
      description: '是否禁用',
    },
    showQuickOptions: {
      control: 'boolean',
      description: '是否显示快捷选项',
    },
    showQuickOptionsOnFocus: {
      control: 'boolean',
      description: '首次聚焦时是否显示快捷选项',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础用法
export const Basic: Story = {
  args: {
    placeholder: '请输入搁置时间',
  },
};

// 始终显示快捷选项
export const AlwaysShowQuickOptions: Story = {
  args: {
    placeholder: '请输入搁置时间',
    showQuickOptions: true,
  },
};

// 首次聚焦时显示快捷选项
export const WithQuickOptionsOnFocus: Story = {
  args: {
    placeholder: '点击输入框聚焦查看快捷选项（首次聚焦后一直显示）',
    showQuickOptionsOnFocus: true,
  },
};

// 组合使用：始终显示且首次聚焦时也显示
export const CombinedQuickOptions: Story = {
  args: {
    placeholder: '请输入搁置时间',
    showQuickOptions: true,
    showQuickOptionsOnFocus: true,
  },
};

// 带初始值
export const WithInitialValue: Story = {
  args: {
    value: 1440, // 1天
    placeholder: '请输入搁置时间',
  },
};

// 禁用状态
export const Disabled: Story = {
  args: {
    value: 60,
    disabled: true,
    placeholder: '请输入搁置时间',
  },
};

// 在表单中使用
export const InForm: Story = {
  render: () => {
    const FormExample = () => {
      const [form] = Form.useForm();

      const onFinish = (values: any) => {
        console.log('Form values:', values);
      };

      return (
        <Form
          form={form}
          onFinish={onFinish}
          style={{ width: 400 }}
        >
          <Form.Item
            name="shelveTime"
            label="搁置时间"
            rules={[{ required: true, message: '搁置时间不能为空' }]}
          >
            <ShelveTimeInput
              placeholder="请输入搁置时间"
              showQuickOptionsOnFocus
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
            >
              提交
            </Button>
          </Form.Item>
        </Form>
      );
    };

    return <FormExample />;
  },
};

// 受控组件
export const Controlled: Story = {
  render: () => {
    const ControlledExample = () => {
      const [value, setValue] = useState<number | undefined>(undefined);

      const handleChange = (newValue: number | undefined) => {
        console.log('Value changed:', newValue);
        setValue(newValue);
      };

      return (
        <div style={{ width: 400 }}>
          <ShelveTimeInput
            value={value}
            onChange={handleChange}
            placeholder="请输入搁置时间"
            showQuickOptions
            showQuickOptionsOnFocus
          />
          <div style={{ marginTop: 16 }}>
            <p>当前值（分钟）: {value ?? '未设置'}</p>
            <p>当前值（小时）: {value ? Math.round(value / 60) : '未设置'}</p>
            <p>当前值（天）: {value ? Math.round(value / 1440) : '未设置'}</p>
          </div>
        </div>
      );
    };

    return <ControlledExample />;
  },
};

// 不同初始值展示
export const DifferentInitialValues: Story = {
  render: () => {
    const DifferentValuesExample = () => {
      const [values, setValues] = useState({
        minute: 30,
        hour: 120,
        day: 4320,
      });

      const handleMinuteChange = (value: number | undefined) => {
        setValues((prev) => ({ ...prev, minute: value ?? 0 }));
      };

      const handleHourChange = (value: number | undefined) => {
        setValues((prev) => ({ ...prev, hour: value ?? 0 }));
      };

      const handleDayChange = (value: number | undefined) => {
        setValues((prev) => ({ ...prev, day: value ?? 0 }));
      };

      return (
        <div style={{ width: 400 }}>
          <h4>30分钟</h4>
          <ShelveTimeInput
            value={values.minute}
            onChange={handleMinuteChange}
            showQuickOptions
            showQuickOptionsOnFocus
          />

          <h4>2小时</h4>
          <ShelveTimeInput
            value={values.hour}
            onChange={handleHourChange}
            showQuickOptions
            showQuickOptionsOnFocus
          />

          <h4>3天</h4>
          <ShelveTimeInput
            value={values.day}
            onChange={handleDayChange}
            showQuickOptions
            showQuickOptionsOnFocus
          />
        </div>
      );
    };

    return <DifferentValuesExample />;
  },
};
