/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import IconTextCard from 'src/components/icon-text-card';

// 定义元数据
const meta: Meta<typeof IconTextCard> = {
  title: 'Components/IconTextCard',
  component: IconTextCard,
  tags: ['autodocs'],
  argTypes: {
    mode: {
      control: 'radio',
      options: ['text', 'button', 'select'],
      description: '组件模式：文本、按钮或选择',
      defaultValue: 'text',
    },
    icon: {
      control: 'text',
      description: '左侧图标元素',
    },
    title: {
      control: 'text',
      description: '标题文本',
    },
    description: {
      control: 'text',
      description: '描述文本',
    },
    onClick: {
      action: 'clicked',
      description: '按钮模式点击事件',
      if: { arg: 'mode', eq: 'button' },
    },
    options: {
      control: 'text',
      description: '选择模式下的选项数组',
      if: { arg: 'mode', eq: 'select' },
    },
    selectedValue: {
      control: 'text',
      description: '选择模式下当前选中的值',
      if: { arg: 'mode', eq: 'select' },
    },
    onSelect: {
      action: 'selected',
      description: '选择模式下的选择事件',
      if: { arg: 'mode', eq: 'select' },
    },
    className: {
      control: 'text',
      description: '自定义类名',
    },
  },
};
export default meta;

// 基础模板
const Template: StoryObj<typeof IconTextCard> = {
  render: (args) => <IconTextCard {...args} />,
};

// 文本模式
export const TextMode = {
  ...Template,
  args: {
    mode: 'text',
    icon: '人',
    title: '个人资料',
    description: '查看和编辑您的个人信息',
  },
};

// 按钮模式
export const ButtonMode = {
  ...Template,
  args: {
    mode: 'button',
    icon: '设',
    title: '系统设置',
    description: '配置系统参数和选项',
    onClick: () => console.log('设置按钮被点击'),
  },
};

// 选择模式
const SelectModeTemplate = (args: any) => {
  const [selectedValue, setSelectedValue] = useState('light');

  const options = [
    {
      value: 'light',
      icon: '主',
      title: '亮色主题',
      description: '明亮舒适的界面',
    },
    {
      value: 'dark',
      icon: '主',
      title: '暗色主题',
      description: '适合夜间使用',
    },
    {
      value: 'system',
      icon: '',
      title: '系统默认',
      description: '跟随系统设置',
    },
    {
      value: 'private',
      icon: '',
      title: '隐私模式',
      description: '不保存任何历史记录',
    },
  ];

  return (
    <div className="space-y-4">
      <IconTextCard
        {...args}
        options={options}
        selectedValue={selectedValue}
        onSelect={setSelectedValue}
      />
      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
        <p className="text-gray-700">
          当前选择的主题:{' '}
          <span className="font-semibold text-blue-600">
            {options.find((o) => o.value === selectedValue)?.title}
          </span>
        </p>
        <p className="text-gray-600 mt-1">
          {options.find((o) => o.value === selectedValue)?.description}
        </p>
      </div>
    </div>
  );
};

export const SelectMode = {
  render: SelectModeTemplate,
  args: {
    mode: 'select',
    icon: '',
    title: '主题设置',
    description: '选择您的界面主题',
  },
};

// 自定义样式示例
export const CustomStyled = {
  ...Template,
  args: {
    mode: 'button',
    icon: '',
    title: '自定义样式',
    description: '添加自定义类名修改样式',
    className: 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white',
  },
};

// 无图标示例
export const WithoutIcon = {
  ...Template,
  args: {
    mode: 'text',
    title: '无图标示例',
    description: '这个卡片没有图标',
  },
};

// 长文本示例
export const LongText = {
  ...Template,
  args: {
    mode: 'text',
    icon: '',
    title: '这是一个非常长的标题文本，用于测试组件的文本溢出处理能力',
    description:
      '这是一个非常长的描述文本，用于测试组件的文本溢出处理能力。这个描述应该足够长以展示组件如何处理多行文本和溢出情况。',
  },
};

// 交互式控制面板
export const Playground = {
  render: (args: any) => {
    const [selectedValue, setSelectedValue] = useState<string | number>(
      'option1',
    );

    const options = [
      {
        value: 'option1',
        icon: '',
        title: '选项一',
        description: '第一个选项',
      },
      {
        value: 'option2',
        icon: '',
        title: '选项二',
        description: '第二个选项',
      },
      {
        value: 'option3',
        icon: '',
        title: '选项三',
        description: '第三个选项',
      },
    ];

    return (
      <div className="p-4">
        <IconTextCard
          {...args}
          options={args.mode === 'select' ? options : []}
          selectedValue={args.mode === 'select' ? selectedValue : ''}
          onSelect={args.mode === 'select' ? setSelectedValue : () => {}}
        />
      </div>
    );
  },
  args: {
    mode: 'text',
    icon: '',
    title: '交互式控制',
    description: '使用Storybook控制面板调整属性',
  },
  argTypes: {
    onSelect: {
      action: 'selected',
    },
    onClick: {
      action: 'clicked',
    },
  },
};
