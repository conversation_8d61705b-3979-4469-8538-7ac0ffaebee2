/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Meta, StoryFn } from '@storybook/react-vite';
import { getMockPatternValues } from '@waterdesk/data/mock/mock-pattern-values';
import { mockPlantList } from '@waterdesk/data/mock/mock-scheduling-data';
import { SchedulingData } from '@waterdesk/data/scheduling-data';
import { generateUnitFormat, UnitFormat } from '@waterdesk/data/unit-system';
import FacilityItemScheduling from 'src/components/scheduling-navigation/facility-item-scheduling';

export default {
  title: 'components/SchedulingNavigation/FacilityItemScheduling',
  component: FacilityItemScheduling,
} as Meta<typeof FacilityItemScheduling>;

const flowUnitCase = {
  unit_title: '流量',
  unit_type: 'D',
  value_add: 0,
  value_muli: 1,
  value_precision: 1,
  value_title: 'm³/h',
};

const headUnitCase = {
  unit_title: '水头',
  unit_type: 'D',
  value_add: 0,
  value_muli: 1,
  value_precision: 1,
  value_title: 'm',
};

const flowUnitFormat: UnitFormat | null = generateUnitFormat(
  'FLOW',
  flowUnitCase,
);

const headUnitFormat: UnitFormat | null = generateUnitFormat(
  'HEAD',
  headUnitCase,
);

const date = '2024-07-17';
const schedulingData = new SchedulingData(mockPlantList);

const Template: StoryFn<typeof FacilityItemScheduling> = (args) => (
  <div style={{ width: '800px', height: '600px' }}>
    <FacilityItemScheduling {...args} />
  </div>
);

export const Plant = Template.bind({});
Plant.args = {
  date,
  facility: [...schedulingData.plants.values()][0],
  pumpStateColor: {
    closed: '#808080',
    variable: '#009ece',
    fixed: '#00c542',
  },
  showFacilityName: true,
  flowUnitFormat: flowUnitFormat || undefined,
  headUnitFormat: headUnitFormat || undefined,
  showPressure: true,
  getPatternValues: getMockPatternValues,
};

export const PumpStation = Template.bind({});
PumpStation.args = {
  date,
  facility: [...schedulingData.pumpStations.values()][0],
  pumpStateColor: {
    closed: '#808080',
    variable: '#009ece',
    fixed: '#00c542',
  },
  showFacilityName: true,
  flowUnitFormat: flowUnitFormat || undefined,
  headUnitFormat: headUnitFormat || undefined,
  showPressure: true,
  getPatternValues: getMockPatternValues,
};
