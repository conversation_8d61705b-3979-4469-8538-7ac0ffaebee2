/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Meta, StoryFn } from '@storybook/react-vite';
import { getMockPlantDayFlowValues } from '@waterdesk/data/mock/mock-pattern-values';
import { mockPlantList } from '@waterdesk/data/mock/mock-scheduling-data';
import { SchedulingData } from '@waterdesk/data/scheduling-data';
import { useState } from 'react';
import SchedulingPlansDashboard, {
  SchedulingPlansDashboardProps,
} from 'src/components/scheduling-navigation/scheduling-plans-dashboard';

export default {
  title: 'components/SchedulingNavigation/SchedulingPlansDashboard',
  component: SchedulingPlansDashboard,
} as Meta<typeof SchedulingPlansDashboard>;

const mockRecommendedValues = [
  { date: '2024-07-16', value: 0.85 },
  { date: '2024-07-17', value: 0.78 },
  { date: '2024-07-18', value: 0.92 },
];

// 使用 SchedulingData 包装 mock 数据
const schedulingData = new SchedulingData(mockPlantList);

const mockGetPlantDailySumFlows = async (
  date: string,
  plantIds: string[],
): Promise<Map<string, number>> => {
  return getMockPlantDayFlowValues(date, plantIds);
};

const Template: StoryFn<SchedulingPlansDashboardProps> = (args) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([
    '2024-07-16',
  ]);

  const handleSelectionChange = (
    selectedRowKeys: React.Key[],
    selectedDate: string,
  ) => {
    setSelectedRowKeys(selectedRowKeys);
    console.log('Selected date:', selectedDate);
  };

  return (
    <div style={{ width: '100%', padding: '20px' }}>
      <SchedulingPlansDashboard
        {...args}
        onSelectionChange={handleSelectionChange}
      />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  plants: [...schedulingData.plants.values()],
  pumpStations: [],
  recommendedValues: mockRecommendedValues,
  showPumpStationAndPressure: false,
  getPlantDailySumFlows: mockGetPlantDailySumFlows,
};

export const WithPumpStations = Template.bind({});
WithPumpStations.args = {
  plants: [...schedulingData.plants.values()],
  pumpStations: [...schedulingData.pumpStations.values()],
  recommendedValues: mockRecommendedValues,
  showPumpStationAndPressure: true,
  getPlantDailySumFlows: mockGetPlantDailySumFlows,
};
