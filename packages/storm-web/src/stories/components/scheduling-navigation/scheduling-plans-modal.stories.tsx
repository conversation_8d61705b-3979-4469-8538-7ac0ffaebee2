/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { Button } from 'antd';
import { useState } from 'react';
import SchedulingPlansModalContainer from 'src/app/core/containers/scheduling-plans-modal-container';
import SchedulingPlansModalContainerLinGang from 'src/app/core/containers/scheduling-plans-modal-container-lingang';

const meta: Meta = {
  title: '调度预案/模态框',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          '演示调度预案模态框的基础版本和临港版本的区别。基础版本只显示水厂信息，临港版本显示泵站和压力信息。',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;

// 完整版本 Story
const FullVersionTemplate = () => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        type="primary"
        onClick={() => setOpen(true)}
      >
        打开调度预案（临港版 - 包含泵站和压力）
      </Button>
      <SchedulingPlansModalContainerLinGang
        open={open}
        onClose={() => setOpen(false)}
      />
    </>
  );
};

// 基础版本 Story
const BasicVersionTemplate = () => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        type="primary"
        onClick={() => setOpen(true)}
      >
        打开调度预案（基础版 - 仅水厂信息）
      </Button>
      <SchedulingPlansModalContainer
        open={open}
        onClose={() => setOpen(false)}
      />
    </>
  );
};

// 对比版本 Story
const ComparisonTemplate = () => {
  const [fullOpen, setFullOpen] = useState(false);
  const [basicOpen, setBasicOpen] = useState(false);

  return (
    <div style={{ display: 'flex', gap: '16px', flexDirection: 'column' }}>
      <div style={{ display: 'flex', gap: '16px' }}>
        <Button
          type="primary"
          onClick={() => setFullOpen(true)}
        >
          临港版本（包含泵站和压力）
        </Button>
        <Button onClick={() => setBasicOpen(true)}>
          基础版本（仅水厂信息）
        </Button>
      </div>

      <div style={{ color: '#666', fontSize: '14px' }}>
        <p>
          <strong>功能对比：</strong>
        </p>
        <ul>
          <li>
            <strong>临港版本：</strong>
            显示水厂和泵站的调度信息，包含压力数据
          </li>
          <li>
            <strong>基础版本：</strong>
            仅显示水厂的调度信息，不包含泵站和压力数据
          </li>
        </ul>
      </div>

      <SchedulingPlansModalContainerLinGang
        open={fullOpen}
        onClose={() => setFullOpen(false)}
      />

      <SchedulingPlansModalContainer
        open={basicOpen}
        onClose={() => setBasicOpen(false)}
      />
    </div>
  );
};

export const LinGangVersion: StoryObj = {
  name: '临港版本',
  render: FullVersionTemplate,
  parameters: {
    docs: {
      description: {
        story:
          '临港版本的调度预案模态框，显示水厂和泵站的调度信息，包含压力数据。适用于需要完整调度信息的场景。',
      },
    },
  },
};

export const BasicVersion: StoryObj = {
  name: '基础版本',
  render: BasicVersionTemplate,
  parameters: {
    docs: {
      description: {
        story:
          '基础版本的调度预案模态框，仅显示水厂的调度信息，不包含泵站和压力数据。适用于只需要水厂调度信息的场景。',
      },
    },
  },
};

export const Comparison: StoryObj = {
  name: '功能对比',
  render: ComparisonTemplate,
  parameters: {
    docs: {
      description: {
        story:
          '演示两个版本的功能差异。可以分别打开临港版本和基础版本，对比它们的显示内容。',
      },
    },
  },
};
