/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react-vite';
import {
  ScheduleInfo,
  ShiftScheduleInfo,
} from '@waterdesk/data/shift-schedule';
import { theme } from 'antd';
import dayjs from 'dayjs';
import { ThemeProvider } from 'styled-components';
import { RenderEditableDateCell } from '../../../components/shift-schedule';

const { useToken } = theme;

// 用户类型定义
interface User {
  id: string;
  name: string;
  department: string;
}

// 班次类型定义
interface Shift {
  key: string;
  name: string;
  startTime: string;
  endTime: string;
  date: string;
}

// Wrapper component to provide theme
const ThemedWrapper = ({ children }: { children: React.ReactNode }) => {
  const { token } = useToken();
  return (
    <ThemeProvider theme={token}>
      <div style={{ padding: '20px', background: token.colorBgContainer }}>
        {children}
      </div>
    </ThemeProvider>
  );
};

const meta: Meta<typeof RenderEditableDateCell> = {
  title: 'components/shift-schedule/editable-date-cell',
  component: RenderEditableDateCell,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          '支持编辑功能的排班表日历单元格组件，展示不同班次配置下的排班情况。用户可以通过点击编辑按钮弹出的面板来添加或删除排班。',
      },
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemedWrapper>
        <div
          style={{
            width: '300px',
            height: '200px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
          }}
        >
          <Story />
        </div>
      </ThemedWrapper>
    ),
  ],
  argTypes: {
    current: {
      description: '当前日期',
      control: false,
    },
    scheduleInfo: {
      description: '排班信息数据',
      control: false,
    },
    shiftScheduleInfo: {
      description: '用户班次信息',
      control: false,
    },
    shifts: {
      description: '班次配置',
      control: false,
    },
    selectedMonth: {
      description: '选中的月份',
      control: { type: 'text' },
    },
    editableScheduleScope: {
      description: '可编辑的排班范围',
      control: false,
    },
    onAddOrDeleteSchedule: {
      description: '添加或删除排班的回调函数',
      action: 'schedule-changed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 创建通用的用户列表
const createUsers = (): User[] => [
  { id: 'user1', name: '张三', department: '运营部' },
  { id: 'user2', name: '李四', department: '运营部' },
  { id: 'user3', name: '王五', department: '技术部' },
  { id: 'user4', name: '赵六', department: '技术部' },
  { id: 'user5', name: '钱七', department: '管理部' },
];

// 生成排班信息的工具函数
const generateScheduleInfo = (
  shifts: Shift[],
  users: User[],
  currentDate: string,
): { scheduleInfo: ScheduleInfo[]; shiftScheduleInfo: ShiftScheduleInfo[] } => {
  const scheduleInfo: ScheduleInfo[] = [];
  const shiftScheduleInfo: ShiftScheduleInfo[] = [];

  // 为每个班次和用户创建数据
  shifts.forEach((shift, shiftIndex) => {
    users.forEach((user) => {
      // 随机决定是否为这个用户在当前日期安排这个班次
      const isScheduled = Math.random() > 0.6; // 40% 概率安排

      if (isScheduled) {
        scheduleInfo.push({
          scheduleId: `${user.id}-${shift.name}-${currentDate}`,
          shiftId: `shift-${shiftIndex}`,
          shiftDate: currentDate,
          shiftName: shift.name,
          scheduleUser: user.name,
          scheduleUserId: user.id,
        });
      }

      // 为每个用户和班次创建 shiftScheduleInfo 条目
      shiftScheduleInfo.push({
        id: `${user.id}-${shiftIndex}`,
        scheduleUserId: user.id,
        scheduleUser: user.name,
        department: user.department,
        shiftName: shift.name,
        shiftIndex,
        scheduleInfo: scheduleInfo.filter(
          (s) => s.scheduleUserId === user.id && s.shiftName === shift.name,
        ),
      });
    });
  });

  return { scheduleInfo, shiftScheduleInfo };
};

// 一班次场景
const singleShiftConfig: Shift[] = [
  {
    key: 'shift-1',
    name: '全天班',
    startTime: '08:00',
    endTime: '18:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
];

const singleShiftGlobalConfig = {
  scheduleConfig: {
    shifts: singleShiftConfig,
    editableScheduleScope: {
      pastMonth: false,
      currentMonth: true,
      futureMonth: true,
    },
  },
};

const singleShiftData = generateScheduleInfo(
  singleShiftConfig,
  createUsers(),
  dayjs().format('YYYY-MM-DD'),
);

export const SingleShift: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: singleShiftData.scheduleInfo,
    shiftScheduleInfo: singleShiftData.shiftScheduleInfo,
    shifts: singleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      singleShiftGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('单班次排班变更:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**一天一班次场景**：只有一个全天班，适用于小规模团队或特殊工作模式。显示简洁的排班界面。',
      },
    },
  },
};

// 两班次场景
const doubleShiftConfig: Shift[] = [
  {
    key: 'shift-1',
    name: '早班',
    startTime: '08:00',
    endTime: '16:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
  {
    key: 'shift-2',
    name: '晚班',
    startTime: '16:00',
    endTime: '00:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
];

const doubleShiftGlobalConfig = {
  scheduleConfig: {
    shifts: doubleShiftConfig,
    editableScheduleScope: {
      pastMonth: false,
      currentMonth: true,
      futureMonth: true,
    },
  },
};

const doubleShiftData = generateScheduleInfo(
  doubleShiftConfig,
  createUsers(),
  dayjs().format('YYYY-MM-DD'),
);

export const DoubleShift: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: doubleShiftData.scheduleInfo,
    shiftScheduleInfo: doubleShiftData.shiftScheduleInfo,
    shifts: doubleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      doubleShiftGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('双班次排班变更:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**一天两班次场景**：早班和晚班，这是最常见的排班模式。支持不同班次的独立排班管理。',
      },
    },
  },
};

// 三班次场景
const tripleShiftConfig: Shift[] = [
  {
    key: 'shift-1',
    name: '早班',
    startTime: '08:00',
    endTime: '16:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
  {
    key: 'shift-2',
    name: '中班',
    startTime: '16:00',
    endTime: '00:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
  {
    key: 'shift-3',
    name: '夜班',
    startTime: '00:00',
    endTime: '08:00',
    date: dayjs().format('YYYY-MM-DD'),
  },
];

const tripleShiftGlobalConfig = {
  scheduleConfig: {
    shifts: tripleShiftConfig,
    editableScheduleScope: {
      pastMonth: false,
      currentMonth: true,
      futureMonth: true,
    },
  },
};

const tripleShiftData = generateScheduleInfo(
  tripleShiftConfig,
  createUsers(),
  dayjs().format('YYYY-MM-DD'),
);

export const TripleShift: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: tripleShiftData.scheduleInfo,
    shiftScheduleInfo: tripleShiftData.shiftScheduleInfo,
    shifts: tripleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      tripleShiftGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('三班次排班变更:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**一天三班次场景**：早班、中班、夜班，适用于24小时运营的业务。展示最复杂的排班管理界面。',
      },
    },
  },
};

// 空排班场景（用于测试编辑功能）
export const EmptySchedule: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: [],
    shiftScheduleInfo: tripleShiftData.shiftScheduleInfo.map((item) => ({
      ...item,
      scheduleInfo: [], // 清空所有排班
    })),
    shifts: tripleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      tripleShiftGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('空排班编辑:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**空排班场景**：没有任何排班安排的日期，可以测试从零开始添加排班的功能。',
      },
    },
  },
};

// 权限受限场景
const restrictedGlobalConfig = {
  scheduleConfig: {
    shifts: doubleShiftConfig,
    editableScheduleScope: {
      pastMonth: false,
      currentMonth: false, // 禁止编辑当前月份
      futureMonth: true,
    },
  },
};

export const RestrictedEdit: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: doubleShiftData.scheduleInfo,
    shiftScheduleInfo: doubleShiftData.shiftScheduleInfo,
    shifts: doubleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      restrictedGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('受限编辑尝试:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**权限受限场景**：当前月份不允许编辑，编辑按钮会被禁用或显示错误提示。用于测试权限控制功能。',
      },
    },
  },
};

// 满排班场景
const fullScheduleData = (() => {
  const scheduleInfo: ScheduleInfo[] = [];
  const shiftScheduleInfo: ShiftScheduleInfo[] = [];
  const currentDate = dayjs().format('YYYY-MM-DD');

  // 为所有用户安排所有班次
  tripleShiftConfig.forEach((shift, shiftIndex) => {
    createUsers().forEach((user) => {
      scheduleInfo.push({
        scheduleId: `${user.id}-${shift.name}-${currentDate}`,
        shiftId: `shift-${shiftIndex}`,
        shiftDate: currentDate,
        shiftName: shift.name,
        scheduleUser: user.name,
        scheduleUserId: user.id,
      });

      shiftScheduleInfo.push({
        id: `${user.id}-${shiftIndex}`,
        scheduleUserId: user.id,
        scheduleUser: user.name,
        department: user.department,
        shiftName: shift.name,
        shiftIndex,
        scheduleInfo: scheduleInfo.filter(
          (s) => s.scheduleUserId === user.id && s.shiftName === shift.name,
        ),
      });
    });
  });

  return { scheduleInfo, shiftScheduleInfo };
})();

export const FullSchedule: Story = {
  args: {
    current: dayjs(),
    scheduleInfo: fullScheduleData.scheduleInfo,
    shiftScheduleInfo: fullScheduleData.shiftScheduleInfo,
    shifts: tripleShiftConfig,
    selectedMonth: dayjs().format('YYYY-MM'),
    editableScheduleScope:
      tripleShiftGlobalConfig.scheduleConfig.editableScheduleScope,
    onAddOrDeleteSchedule: (
      isChecked: boolean,
      shiftName: string,
      userId: string,
      userName: string,
      date: string,
    ) => {
      console.log('满排班编辑:', {
        isChecked,
        shiftName,
        userId,
        userName,
        date,
      });
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          '**满排班场景**：所有用户都被安排到所有班次，展示高密度排班情况下的界面表现。',
      },
    },
  },
};
