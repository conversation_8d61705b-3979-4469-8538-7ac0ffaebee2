/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import type { Meta, StoryObj } from '@storybook/react';
import { PumpInfo } from '@waterdesk/data/device';
import {
  mockPumpList,
  mockPumpStateColor,
  mockPumpValveInfo,
} from '@waterdesk/data/mock/mock-pump-status';
import {
  ScadaDataSourceItem,
  StrategyItem,
  StrategyItemChangeType,
} from '@waterdesk/data/smart-scheduling-data-new';
import { Button, Space, Timeline } from 'antd';
import { useState } from 'react';
import { ScheduleInfoCard } from '../../../components/smart-schedule';

// 生成今天的datetime string
const getDateTimeString = (hour: number, minute: number = 0) => {
  const today = new Date();
  today.setHours(hour, minute, 0, 0);
  return today.toISOString();
};

// 生成scadDataSource测试数据
const createScadDataSource = (
  items: { title: string; value: string; unit?: string }[],
): ScadaDataSourceItem[] => {
  return items.map((item, index) => ({
    title: item.title,
    otype: 'MONITOR',
    oname: `MONITOR_${index + 1}`,
    valueWithSymbol: item.unit ? `${item.value}${item.unit}` : item.value,
    otime: new Date().toLocaleString('zh-CN'),
  }));
};

const mockStrategies: StrategyItem[] = [
  {
    id: getDateTimeString(8, 45),
    time: getDateTimeString(8, 45),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '阀门检查: 完成',
      },
    ],
  },
  {
    id: getDateTimeString(9, 0),
    time: getDateTimeString(9, 0),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '3#泵: 35Hz',
      },
    ],
  },
  {
    id: getDateTimeString(9, 15),
    time: getDateTimeString(9, 15),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '2#泵: 40Hz',
      },
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '1#泵: 关闭',
      },
    ],
  },
  {
    id: getDateTimeString(9, 30),
    time: getDateTimeString(9, 30),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '1#泵: 30Hz',
      },
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '4#泵: 25Hz',
      },
    ],
  },
  {
    id: getDateTimeString(9, 45),
    time: getDateTimeString(9, 45),
    changes: [], // 无需操作
  },
  {
    id: getDateTimeString(10, 0),
    time: getDateTimeString(10, 0),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '5#泵: 25Hz',
      },
    ],
  },
];

// 扩展的策略数据，用于测试滚动效果（增加更多数据项）
const extendedStrategies: StrategyItem[] = [
  {
    id: getDateTimeString(8, 0),
    time: getDateTimeString(8, 0),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '系统启动: 完成',
      },
    ],
  },
  {
    id: getDateTimeString(8, 15),
    time: getDateTimeString(8, 15),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '设备检查: 进行中',
      },
    ],
  },
  {
    id: getDateTimeString(8, 30),
    time: getDateTimeString(8, 30),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '预备泵: 预热',
      },
    ],
  },
  ...mockStrategies,
  {
    id: getDateTimeString(10, 15),
    time: getDateTimeString(10, 15),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '6#泵: 30Hz',
      },
    ],
  },
  {
    id: getDateTimeString(10, 30),
    time: getDateTimeString(10, 30),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '数据备份: 开始',
      },
    ],
  },
  {
    id: getDateTimeString(10, 45),
    time: getDateTimeString(10, 45),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '7#泵: 开启',
      },
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '8#泵: 待机',
      },
    ],
  },
  {
    id: getDateTimeString(11, 0),
    time: getDateTimeString(11, 0),
    changes: [],
  },
  {
    id: getDateTimeString(11, 15),
    time: getDateTimeString(11, 15),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '循环泵A: 启动',
      },
    ],
  },
  {
    id: getDateTimeString(11, 30),
    time: getDateTimeString(11, 30),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '流量调节: 开始',
      },
    ],
  },
  {
    id: getDateTimeString(11, 45),
    time: getDateTimeString(11, 45),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '高压泵: 45Hz',
      },
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '压力监测: 启动',
      },
    ],
  },
  {
    id: getDateTimeString(12, 0),
    time: getDateTimeString(12, 0),
    changes: [],
  },
  {
    id: getDateTimeString(12, 15),
    time: getDateTimeString(12, 15),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '备用泵B: 预备',
      },
    ],
  },
  {
    id: getDateTimeString(12, 30),
    time: getDateTimeString(12, 30),
    changes: [
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '循环泵B: 启动',
      },
      {
        changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
        contentText: '增压泵: 50Hz',
      },
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '系统检测: 执行',
      },
    ],
  },
  {
    id: getDateTimeString(12, 45),
    time: getDateTimeString(12, 45),
    changes: [
      {
        changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
        contentText: '数据同步: 完成',
      },
    ],
  },
  {
    id: getDateTimeString(13, 0),
    time: getDateTimeString(13, 0),
    changes: [],
  },
];

// 包含所有changeType类型的完整测试数据
const allChangeTypesStrategies: StrategyItem[] = [
  {
    id: getDateTimeString(9, 0),
    time: getDateTimeString(9, 0),
    changes: [
      {
        changeType: StrategyItemChangeType.PRESSURE,
        contentText: '1#给水泵频率调节: 35Hz -> 40Hz',
        originPumpValue: {
          otype: 'WDM_PUMPS',
          oname: '1#给水泵',
          preValue: 35,
          curValue: 40,
        },
        pumpInfo: {
          title: '1#给水泵',
          oname: '1#给水泵',
        } as PumpInfo,
      },
      {
        changeType: StrategyItemChangeType.FLOW,
        contentText: '流量调节: 500m³/h -> 600m³/h',
      },
    ],
  },
  {
    id: getDateTimeString(9, 15),
    time: getDateTimeString(9, 15),
    changes: [
      {
        changeType: StrategyItemChangeType.PUMP_OPEN,
        contentText: '2#给水泵开启: 0Hz -> 30Hz',
        originPumpValue: {
          otype: 'WDM_PUMPS',
          oname: '2#给水泵',
          preValue: 0,
          curValue: 30,
        },
        pumpInfo: {
          title: '2#给水泵',
          oname: '2#给水泵',
        } as PumpInfo,
      },
    ],
  },
  {
    id: getDateTimeString(9, 30),
    time: getDateTimeString(9, 30),
    changes: [
      {
        changeType: StrategyItemChangeType.PUMP_CLOSE,
        contentText: '3#给水泵关闭: 45Hz -> 0Hz',
        originPumpValue: {
          otype: 'WDM_PUMPS',
          oname: '3#给水泵',
          preValue: 45,
          curValue: 0,
        },
        pumpInfo: {
          title: '3#给水泵',
          oname: '3#给水泵',
        } as PumpInfo,
      },
    ],
  },
  {
    id: getDateTimeString(9, 45),
    time: getDateTimeString(9, 45),
    changes: [
      {
        changeType: StrategyItemChangeType.VALVE_OPEN,
        contentText: '阀门V001开启: 0% -> 100%',
        originPumpValue: {
          otype: 'WDM_VALVES',
          oname: 'V001',
          preValue: 0,
          curValue: 100,
        },
      },
      {
        changeType: StrategyItemChangeType.VALVE_CLOSE,
        contentText: '阀门V002关闭: 80% -> 0%',
        originPumpValue: {
          otype: 'WDM_VALVES',
          oname: 'V002',
          preValue: 80,
          curValue: 0,
        },
      },
    ],
  },
  {
    id: getDateTimeString(10, 0),
    time: getDateTimeString(10, 0),
    changes: [
      {
        changeType: StrategyItemChangeType.VALVE_OPENING,
        contentText: '阀门V003开度调节: 50% -> 75%',
        originPumpValue: {
          otype: 'WDM_VALVES',
          oname: 'V003',
          preValue: 50,
          curValue: 75,
        },
      },
    ],
  },
  {
    id: getDateTimeString(10, 15),
    time: getDateTimeString(10, 15),
    changes: [
      {
        changeType: StrategyItemChangeType.PUMP_FREQUENCY,
        contentText: '4#给水泵频率调节: 25Hz -> 35Hz',
        originPumpValue: {
          otype: 'WDM_PUMPS',
          oname: '4#给水泵',
          preValue: 25,
          curValue: 35,
        },
        pumpInfo: {
          title: '4#给水泵',
          oname: '4#给水泵',
        } as PumpInfo,
      },
    ],
  },
  {
    id: getDateTimeString(10, 30),
    time: getDateTimeString(10, 30),
    changes: [], // 无需操作
  },
];

const meta: Meta<typeof ScheduleInfoCard> = {
  title: 'Components/SmartSchedule/ScheduleInfoCard',
  component: ScheduleInfoCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          '智能调度信息卡片组件，用于显示调度标题、监测信息、泵状态和策略列表等信息',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: '卡片标题',
    },
    activeTime: {
      control: 'select',
      options: [
        getDateTimeString(9, 0),
        getDateTimeString(9, 15),
        getDateTimeString(9, 30),
        getDateTimeString(9, 45),
      ],
      description: '当前活跃的时间点',
    },
    onAccept: {
      action: 'clicked',
      description: '接受按钮点击事件',
    },
    onReject: {
      action: 'clicked',
      description: '拒绝按钮点击事件',
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  args: {
    title: '上水厂智能调度',
    scadaDataSource: createScadDataSource([
      { title: '出厂压力', value: '0.53', unit: 'MPa' },
      { title: '出厂流量', value: '50000', unit: 'm³/h' },
    ]),
    pumpDataSource: {
      pumpList: [mockPumpList[0]],
      valueInfo: mockPumpValveInfo,
      pumpStateColor: mockPumpStateColor,
    },
    activeTime: getDateTimeString(9, 15),
    strategies: mockStrategies,
    onAccept: () => console.log('接受按钮被点击'),
    onReject: () => console.log('拒绝按钮被点击'),
    style: {
      width: 280,
    },
  },
};

// 单个监测数据
export const SingleMonitoring: Story = {
  args: {
    ...Default.args,
    title: '单监测数据示例',
    scadaDataSource: createScadDataSource([
      { title: '出厂压力', value: '0.53', unit: 'MPa' },
    ]),
    activeTime: getDateTimeString(9, 30),
  },
};

// 无监测数据
export const NoMonitoring: Story = {
  args: {
    ...Default.args,
    title: '无监测数据示例',
    scadaDataSource: [],
  },
};

// 不同活跃时间
export const DifferentActiveTime: Story = {
  args: {
    ...Default.args,
    title: '活跃时间09:00',
    activeTime: getDateTimeString(9, 0),
  },
};

// 长标题溢出测试
export const LongTitle: Story = {
  args: {
    ...Default.args,
    title: '这是一个非常长的标题用来测试文字溢出隐藏效果的智能调度卡片组件',
    activeTime: getDateTimeString(9, 0),
  },
};

// 无泵状态
export const NoPumps: Story = {
  args: {
    ...Default.args,
    title: '无泵状态示例',
    pumpDataSource: {
      pumpList: [],
      valueInfo: mockPumpValveInfo,
      pumpStateColor: mockPumpStateColor,
    },
  },
};

// 多泵状态
export const MultiplePumps: Story = {
  args: {
    ...Default.args,
    title: '多泵状态示例',
    pumpDataSource: {
      pumpList: mockPumpList,
      valueInfo: mockPumpValveInfo,
      pumpStateColor: mockPumpStateColor,
    },
  },
};

// 无需操作状态（不显示任何按钮）
export const NoOperationNeeded: Story = {
  args: {
    ...Default.args,
    title: '无需操作状态 - 无按钮',
    activeTime: getDateTimeString(9, 45), // 这个时间点对应空的 content
  },
};

// 非敏感操作（只显示拒绝按钮）
export const NonSensitiveOperation: Story = {
  args: {
    ...Default.args,
    title: '非敏感操作 - 只有拒绝按钮',
    strategies: [
      {
        id: getDateTimeString(8, 45),
        time: getDateTimeString(8, 45),
        changes: [
          {
            changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
            contentText: '阀门检查: 完成',
          },
        ],
      },
    ],
    activeTime: getDateTimeString(8, 45),
  },
};

// 敏感操作（显示接受和拒绝按钮）
export const SensitiveOperation: Story = {
  args: {
    ...Default.args,
    title: '敏感操作 - 接受和拒绝按钮',
    activeTime: getDateTimeString(9, 0), // 这个时间点有 WDM_PUMPS 操作
  },
};

// 混合操作（有敏感和非敏感操作，显示两个按钮）
export const MixedOperation: Story = {
  args: {
    ...Default.args,
    title: '混合操作 - 接受和拒绝按钮',
    strategies: [
      {
        id: getDateTimeString(11, 0),
        time: getDateTimeString(11, 0),
        changes: [
          {
            changeType: 'PUMP_FREQUENCY' as StrategyItemChangeType,
            contentText: '1#泵: 30Hz',
          },
          {
            changeType: 'OTHER_OPERATION' as StrategyItemChangeType,
            contentText: '阀门1: 开启',
          },
        ],
      },
    ],
    activeTime: getDateTimeString(11, 0),
  },
};

// 交互式滚动测试（时间轴）
export const InteractiveTimelineTest = () => {
  const [activeTime, setActiveTime] = useState(getDateTimeString(9, 15));

  const timelineItems = extendedStrategies.map((strategy: StrategyItem) => {
    const timeDisplay = new Date(strategy.time).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const isActive = strategy.id === activeTime;
    const isNoOperation = !strategy.changes || strategy.changes.length === 0;
    const displayContent = isNoOperation
      ? '无需操作'
      : strategy.changes.map((change: any) => change.contentText).join(', ');

    return {
      color: isActive
        ? '#1890ff'
        : strategy.changes.some(
              (change: any) => change.changeType === 'PUMP_FREQUENCY',
            )
          ? '#52c41a'
          : '#666',
      children: (
        <div
          style={{
            cursor: 'pointer',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: isActive ? '#e6f7ff' : 'transparent',
            border: isActive ? '1px solid #1890ff' : '1px solid transparent',
          }}
          onClick={() => setActiveTime(strategy.id)}
        >
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
            {timeDisplay}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {displayContent}
          </div>
        </div>
      ),
    };
  });

  return (
    <div style={{ display: 'flex', gap: '20px', alignItems: 'flex-start' }}>
      <div style={{ flex: '0 0 300px' }}>
        <ScheduleInfoCard
          title="交互式滚动测试"
          scadaDataSource={createScadDataSource([
            { title: '出厂压力', value: '0.53', unit: 'MPa' },
            { title: '出厂流量', value: '50000', unit: 'm³/h' },
          ])}
          pumpDataSource={{
            pumpList: [mockPumpList[0]],
            valueInfo: mockPumpValveInfo,
            pumpStateColor: mockPumpStateColor,
          }}
          activeTime={activeTime}
          strategies={extendedStrategies}
          onAccept={() => console.log('接受按钮被点击')}
          onReject={() => console.log('拒绝按钮被点击')}
          style={{ width: 280 }}
        />
      </div>
      <div style={{ flex: '1', maxHeight: '500px', overflow: 'auto' }}>
        <h4>点击时间轴切换时刻，观察左侧卡片的滚动效果</h4>
        <Timeline
          mode="left"
          items={timelineItems}
        />
      </div>
    </div>
  );
};

// 交互式按钮测试
export const InteractiveButtonTest = () => {
  const [activeTime, setActiveTime] = useState(getDateTimeString(9, 15));

  const timeButtons = extendedStrategies.map((strategy: StrategyItem) => {
    const timeDisplay = new Date(strategy.time).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const isActive = strategy.id === activeTime;
    const hasPumps = strategy.changes.some(
      (change: any) => change.changeType === 'PUMP_FREQUENCY',
    );

    return (
      <Button
        key={strategy.id}
        type={isActive ? 'primary' : hasPumps ? 'default' : 'dashed'}
        size="small"
        onClick={() => setActiveTime(strategy.id)}
        style={{
          margin: '2px',
          backgroundColor: isActive
            ? '#1890ff'
            : hasPumps
              ? '#52c41a'
              : undefined,
          borderColor: isActive ? '#1890ff' : hasPumps ? '#52c41a' : undefined,
          color: isActive ? 'white' : hasPumps ? 'white' : undefined,
        }}
      >
        {timeDisplay}
      </Button>
    );
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <div>
        <h4>点击时间按钮切换时刻，观察卡片的滚动效果</h4>
        <div
          style={{
            padding: '10px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa',
          }}
        >
          <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
            <span style={{ color: '#1890ff' }}>■</span> 当前时刻
            <span style={{ color: '#52c41a', marginLeft: '12px' }}>■</span>{' '}
            泵操作
            <span style={{ color: '#666', marginLeft: '12px' }}>■</span>{' '}
            其他操作
          </div>
          <Space wrap>{timeButtons}</Space>
        </div>
      </div>
      <ScheduleInfoCard
        title="交互式按钮测试"
        scadaDataSource={createScadDataSource([
          { title: '出厂压力', value: '0.53', unit: 'MPa' },
          { title: '出厂流量', value: '50000', unit: 'm³/h' },
        ])}
        pumpDataSource={{
          pumpList: [mockPumpList[0]],
          valueInfo: mockPumpValveInfo,
          pumpStateColor: mockPumpStateColor,
        }}
        activeTime={activeTime}
        strategies={extendedStrategies}
        onAccept={() => console.log('接受按钮被点击')}
        onReject={() => console.log('拒绝按钮被点击')}
        style={{ width: 320, margin: '0 auto' }}
      />
    </div>
  );
};

// 自定义样式
export const CustomStyle: Story = {
  args: {
    ...Default.args,
    title: '自定义样式示例',
    activeTime: getDateTimeString(10, 0),
    style: {
      width: 280,
      border: '2px solid #1890ff',
      borderRadius: '8px',
    },
  },
};

// 所有changeType交互式测试
export const AllChangeTypesInteractiveTest = () => {
  const [activeTime, setActiveTime] = useState(getDateTimeString(9, 15));

  const getChangeTypeColor = (changes: any[]) => {
    if (!changes || changes.length === 0) return '#d9d9d9';

    const hasSensitive = changes.some(
      (change: any) =>
        change.changeType === StrategyItemChangeType.PUMP_CLOSE ||
        change.changeType === StrategyItemChangeType.PUMP_OPEN,
    );
    if (hasSensitive) return '#ff4d4f';

    const hasPressure = changes.some(
      (change: any) => change.changeType === StrategyItemChangeType.PRESSURE,
    );
    if (hasPressure) return '#1890ff';

    return '#52c41a';
  };

  const getChangeTypeDescription = (changes: any[]) => {
    if (!changes || changes.length === 0) return '无需操作';

    const types = changes.map((change: any) => {
      switch (change.changeType) {
        case StrategyItemChangeType.PRESSURE:
          return '压力调节';
        case StrategyItemChangeType.FLOW:
          return '流量调节';
        case StrategyItemChangeType.PUMP_CLOSE:
          return '泵关闭';
        case StrategyItemChangeType.PUMP_OPEN:
          return '泵开启';
        case StrategyItemChangeType.PUMP_FREQUENCY:
          return '泵频率';
        case StrategyItemChangeType.VALVE_CLOSE:
          return '阀门关闭';
        case StrategyItemChangeType.VALVE_OPEN:
          return '阀门开启';
        case StrategyItemChangeType.VALVE_OPENING:
          return '阀门开度';
        default:
          return '其他操作';
      }
    });

    return types.join(' + ');
  };

  const timeButtons = allChangeTypesStrategies.map((strategy: StrategyItem) => {
    const timeDisplay = new Date(strategy.time).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const isActive = strategy.id === activeTime;
    const changeColor = getChangeTypeColor(strategy.changes);
    const changeDesc = getChangeTypeDescription(strategy.changes);

    return (
      <div
        key={strategy.id}
        style={{ margin: '4px' }}
      >
        <Button
          type={isActive ? 'primary' : 'default'}
          size="small"
          onClick={() => setActiveTime(strategy.id)}
          style={{
            backgroundColor: isActive ? changeColor : undefined,
            borderColor: changeColor,
            color: isActive ? 'white' : changeColor,
            minWidth: '80px',
          }}
        >
          {timeDisplay}
        </Button>
        <div
          style={{
            fontSize: '10px',
            color: '#666',
            textAlign: 'center',
            marginTop: '2px',
            wordBreak: 'break-all',
          }}
        >
          {changeDesc}
        </div>
      </div>
    );
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <div>
        <h4>所有变更类型交互测试</h4>
        <p style={{ fontSize: '14px', color: '#666', marginBottom: '12px' }}>
          点击不同时间按钮切换策略，观察卡片边框颜色、按钮显示和Tooltip内容的变化
        </p>
        <div
          style={{
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa',
          }}
        >
          <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
            <span style={{ color: '#ff4d4f' }}>■</span> 敏感操作（泵开关）
            <span style={{ color: '#1890ff', marginLeft: '12px' }}>■</span>{' '}
            压力调节
            <span style={{ color: '#52c41a', marginLeft: '12px' }}>■</span>{' '}
            其他操作
            <span style={{ color: '#d9d9d9', marginLeft: '12px' }}>■</span>{' '}
            无操作
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {timeButtons}
          </div>
        </div>
      </div>
      <ScheduleInfoCard
        title="所有变更类型展示"
        scadaDataSource={createScadDataSource([
          { title: '系统压力', value: '0.65', unit: 'MPa' },
          { title: '总流量', value: '45000', unit: 'm³/h' },
        ])}
        pumpDataSource={{
          pumpList: mockPumpList.slice(0, 3),
          valueInfo: mockPumpValveInfo,
          pumpStateColor: mockPumpStateColor,
        }}
        activeTime={activeTime}
        strategies={allChangeTypesStrategies}
        onAccept={() => console.log('接受操作:', activeTime)}
        onReject={() => console.log('拒绝操作:', activeTime)}
        style={{ width: 320, margin: '0 auto' }}
      />
    </div>
  );
};
